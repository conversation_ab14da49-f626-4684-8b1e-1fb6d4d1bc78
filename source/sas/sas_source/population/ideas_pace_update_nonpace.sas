/***********************************************************************************************************************
 ** WARN: The macros in this file are not stateless. They make certain assumptions regarding the state of the program.**
 ***********************************************************************************************************************/


/*
 * Create accom_inventory(nonpace) from accom_inventory_pace.
 * This will create the non-pace inventory for a single accom-type, as the pace_inventory holds pace of a single accom-type.
 */
%macro create_accom_nonpace()/store;

    /* Only retain latest pace-point for an occupancy-date */
    proc sort data =&work_lib..accom_inventory_db;
            by occupancy_dt descending Snapshot_DTTM;
    run;

    data &work_lib..accom_nonpace;
        set &work_lib..accom_inventory_db;
        by occupancy_dt;
        if first.occupancy_dt then
            do;
                length property_id accom_type_id 6;
                length accom_capacity rooms_not_avail_maint rooms_not_avail_other 4;
                output &work_lib..accom_nonpace;
            end;
    run;

    proc append base=&work_lib..accom_nonpace_all data=&work_lib..accom_nonpace force;
    run;

    /* Create accom_inventory and retain needed attributes only.*/
    proc append base=rm_data.accom_inventory data=&work_lib..accom_nonpace(keep=property_id occupancy_dt accom_type_id 
            accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures no_shows 
            cancellations room_revenue total_revenue food_revenue);
    run;

%mend create_accom_nonpace;



/*
 * Create total_inventory(nonpace) from total_inventory_pace.
 */
%macro create_total_nonpace()/store;

    proc sort data =&work_lib..total_inventory_db;
            by occupancy_dt descending Snapshot_DTTM;
    run;

    data &work_lib..total_nonpace_all;
        set &work_lib..total_inventory_db;
        by occupancy_dt;
        if first.occupancy_dt then
            do;
                length property_id 6;
                length total_accom_capacity rooms_not_avail_maint rooms_not_avail_other 4;
                output &work_lib..total_nonpace_all;
            end;
    run;

    proc append base=rm_data.total_inventory data=&work_lib..total_nonpace_all(keep=property_id occupancy_dt total_accom_capacity rooms_not_avail_maint
            rooms_not_avail_other rooms_sold arrivals departures no_shows cancellations room_revenue total_revenue
            food_revenue);
    run;

%mend create_total_nonpace;



/*
 * Create mkt_accom_inventory(nonpace) from mkt_accom_pace.
 * This will create the non-pace inventory for a single mkt-seg, as the mkt_accom_pace holds pace of a single mkt-seg.
 */
%macro create_mkt_accom_nonpace(mkt_seg=)/store;

    proc sql;
        create table &work_lib..mkt_accom_inventory_db as
            select &Property_ID. as property_id, a.occupancy_dt, a.capture_dttm as Snapshot_DTTM ,
                    datepart(a.capture_dttm)-1 as Business_day_end_dt,
                    &mkt_seg. as mkt_seg_id length=8,
                    a.accom_type_id,
                    a.rooms_sold,
                    a.arrivals,
                    a.departures,
                    a.no_shows,
                    a.cancellations,
                    a.Room_revenue,
                    a.Food_Revenue,
                    a.Total_Revenue,
                    b.file_metadata_id,
                    month(a.occupancy_dt) as Month_ID,
                    year(a.occupancy_dt) - 2000 as Year_ID,
                    datetime() as Last_Updated_Dttm
                from &work_lib..mkt_accom_pace_all_exp as a
                    inner join &work_lib..file_metadata as b
                        on datepart(a.capture_dttm)=b.snapshot_dt;
    quit;


    proc sort data =&work_lib..mkt_accom_inventory_db;
            by accom_type_id occupancy_dt descending Snapshot_DTTM;
    run;

    data &work_lib..mkt_accom_nonpace;
        set &work_lib..mkt_accom_inventory_db;
        by accom_type_id occupancy_dt;
        if first.occupancy_dt then
            do;
                length property_id mkt_seg_id 6;
                output &work_lib..mkt_accom_nonpace;
            end;
    run;

    proc append base=&work_lib..mkt_accom_nonpace_all data=&work_lib..mkt_accom_nonpace force;
    run;

    proc append base=rm_data.mkt_accom_inventory data=&work_lib..mkt_accom_nonpace(keep=property_id mkt_seg_id
            accom_type_id occupancy_dt rooms_sold arrivals departures no_shows cancellations room_revenue rooms_sold
            total_revenue food_revenue);
    run;
%mend create_mkt_accom_nonpace;



/*
 * Create bde_accom_inventory and bde_mkt_accom_inventory from accom_inventory and mkt_accom_inventory resp.
 */
%macro create_bde_inventories()/store;

    proc append base=rm_data.bde_accom_inventory data=rm_data.accom_inventory force;
    run;

    proc append base=rm_data.bde_mkt_accom_inventory data=rm_data.mkt_accom_inventory force;
    run;

%mend create_bde_inventories;


/*
 * Replace old non-pace datasets with newly created ones.
 */
%macro move_nonpace_2_perm_lib()/store;

    proc Datasets library=rm_datap noprint nolist;
        delete &non_pace_dataset_list.;
        copy in=rm_data out=rm_datap move
            CLONE
            CONSTRAINT=YES
            INDEX=YES;
        select &non_pace_dataset_list.;
    run;

%mend move_nonpace_2_perm_lib;

/*
 * Copy old non-pace datasets(schema only) in temp directory to preserve dataset metadata such as indexes and constraints.
 */
%macro move_nonpace_2_temp_lib()/store;

    options obs=0;
    proc Datasets library=rm_data noprint nolist;
        copy in=rm_datap out=rm_data
            CLONE
            CONSTRAINT=YES
            INDEX=YES;
        select &non_pace_dataset_list.;
    run;
    options obs=max;
%mend move_nonpace_2_temp_lib;


/*
 * Upload non-pace activities to Pacman tables.
 */
%macro upload_nonpace_2_tenant()/store;

    %upload_total_nonpace_2_tenant();
    %upload_accom_nonpace_2_tenant();
    %upload_mkt_acc_nonpace_2_tenant();

%mend upload_nonpace_2_tenant;



%macro upload_accom_nonpace_2_tenant()/store;

    %local acc_activity;

    data &work_lib..accom_activity_db(keep=property_id occupancy_dt accom_type_id accom_capacity rooms_not_avail_maint
            rooms_not_avail_other rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue
            total_revenue file_metadata_id snapshot_dttm);
        set &work_lib..accom_nonpace_all;
    run;

    %put Uploading Accom_Activity to tenant;

    %ideas_trans_upload_tmp(upload_table=&work_lib..accom_activity_db,
        _name_in_db=acc_activity,
        like_table=tenant.accom_activity(drop=Accom_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=3&load_id.);
    %let acc_activity = ##&acc_activity;

    %if &syscc > 4 %then
        %do;
            %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
            %goto EXIT;
        %end;

    proc Sql;
        connect to odbc (&connect_str autocommit=yes bulkload=yes);
        execute ( delete from Accom_Activity ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        execute (insert into Accom_Activity (property_id, occupancy_dt, snapshot_dttm, Accom_Type_ID, accom_capacity,
                    rooms_sold, rooms_not_avail_maint, Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows,
                    room_revenue, food_revenue, total_revenue, file_metadata_id, CreateDate)
                select property_id, occupancy_dt, snapshot_dttm, Accom_Type_ID, accom_capacity, rooms_sold,
                    rooms_not_avail_maint, Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue,
                    food_revenue, total_revenue, file_metadata_id, getdate() from &acc_activity.
        ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        disconnect from odbc;
    quit;

%EXIT:
    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS update failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend upload_accom_nonpace_2_tenant;



%macro upload_total_nonpace_2_tenant()/store;

    %local total_activity;

    data &work_lib..total_activity_db(keep=property_id occupancy_dt total_accom_capacity rooms_not_avail_maint
            rooms_not_avail_other rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue
            total_revenue file_metadata_id snapshot_dttm);
        set &work_lib..total_nonpace_all;
    run;

    %put Uploading Total_Activity to tenant;

    %ideas_trans_upload_tmp(upload_table=&work_lib..total_activity_db,
        _name_in_db=total_activity,
        like_table=tenant.total_activity(drop=Total_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=3&load_id.);
    %let total_activity = ##&total_activity;

    %if &syscc > 4 %then
        %do;
            %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
            %goto EXIT;
        %end;

    proc Sql;
        connect to odbc (&connect_str autocommit=yes bulkload=yes);
        execute ( delete from Total_Activity ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        execute (insert into Total_Activity (property_id, occupancy_dt, snapshot_dttm, total_accom_capacity,
                    rooms_sold, rooms_not_avail_maint, Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows,
                    room_revenue, food_revenue, total_revenue, file_metadata_id, CreateDate)
                select property_id, occupancy_dt, snapshot_dttm, total_accom_capacity, rooms_sold, rooms_not_avail_maint,
                    Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue,
                    food_revenue, total_revenue, file_metadata_id, getdate() from &total_activity.
                ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        disconnect from odbc;
    quit;

%EXIT:
    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS update failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend upload_total_nonpace_2_tenant;



%macro upload_mkt_acc_nonpace_2_tenant()/store;

    %local mkt_acc_activity;

    data &work_lib..mkt_accom_activity_db(keep=property_id occupancy_dt mkt_seg_id accom_type_id mkt_accom_capacity
            rooms_sold arrivals departures no_shows cancellations room_revenue food_revenue
            total_revenue file_metadata_id snapshot_dttm);
        set &work_lib..mkt_accom_nonpace_all;
    run;

    %put Uploading mkt_accom_Activity to tenant;

    %ideas_trans_upload_tmp(upload_table=&work_lib..mkt_accom_activity_db,
        _name_in_db=mkt_acc_activity,
        like_table=tenant.mkt_accom_activity(drop=mkt_accom_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=3&load_id.);
    %let mkt_acc_activity = ##&mkt_acc_activity;

    %if &syscc > 4 %then
        %do;
            %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
            %goto EXIT;
        %end;

    proc Sql;
        connect to odbc (&connect_str autocommit=yes bulkload=yes);
        execute ( delete from mkt_accom_Activity ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        execute (insert into mkt_accom_Activity (property_id, occupancy_dt, snapshot_dttm, accom_Type_ID, mkt_seg_id,
                    rooms_sold, arrivals, departures, cancellations, no_shows,
                    room_revenue, food_revenue, total_revenue, file_metadata_id, CreateDate, Pseudo_Room_Revenue)
                select property_id, occupancy_dt, snapshot_dttm, accom_Type_ID, mkt_seg_id, rooms_sold,
                    arrivals, departures, cancellations, no_shows, room_revenue,
                    food_revenue, total_revenue, file_metadata_id, getdate(), 0 from &mkt_acc_activity.
        ) by odbc;

        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
            %do;
                %let error_codes = 921;
                %goto EXIT;
            %end;

        disconnect from odbc;
    quit;

%EXIT:
    %if &error_codes =921 %then
        %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS update failed', '', &request_Id., &err_ct.)
            %let etl_status = 1;
            %let GL_ABORT_FLAG=1;
        %end;

    %if &GL_ABORT_FLAG eq 1 or &syscc > 4 %then
        %do;
            %let etl_status = 1;
            %let return_code=200;
        %end;
    %else %let return_code=0;
%mend upload_mkt_acc_nonpace_2_tenant;