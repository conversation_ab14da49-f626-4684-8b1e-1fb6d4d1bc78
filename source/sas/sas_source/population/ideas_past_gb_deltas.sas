%macro ideas_past_gb_deltas/store;
 %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %let rm_data = rm_data;
    %put SNAP DATE IS &snap_date;

    data _null_;
        pastgbdt = intnx('day', "&snap_date", -3);
        call symputx('pastgbdate', pastgbdt);

    run;
    %put pastgbdate is &pastgbdate;

 proc Sql;
            create table &work_lib..group_block as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, b.group_code, a.blocks, a.accom_type_id
                    from tenant.group_block as a, tenant.group_master as b
                        where a.group_id = b.group_id and b.property_id=&property_id. and a.occupancy_dt>&pastgbdate
                            order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql noprint;
            create index gb_idx on &work_lib..group_block(group_code, accom_type_id, occupancy_dt );
        quit;

        %if %sysfunc(exist(rm_data.BDE_group_block))=0  %then %do;

            proc Sql;
                create table rm_data.BDE_group_block as
                    select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, b.group_code, a.blocks, a.accom_type_id
                        from tenant.group_block as a, tenant.group_master as b
                            where a.group_id = b.group_id and b.property_id=&property_id. and a.occupancy_dt>=&pastgbdate
                                order by b.group_code, a.accom_type_id, a.occupancy_dt;
            quit;

            proc Sql noprint;
                create index gb_idx on rm_data.BDE_group_block(group_code, accom_type_id, occupancy_dt );
            quit;

        %end;

        proc Sql;
            create table &work_lib..BDE_group_block as
                select *
                    from rm_data.BDE_group_block as a
         order by a.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql noprint;
            create index gb_idx on &work_lib..BDE_group_block(group_code, accom_type_id, occupancy_dt );
        quit;

        proc Sql;
            create table &work_lib..gb1 as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, a.group_code, a.blocks, b.accom_type_id
                    from &work_lib..gb as a, tenant.accom_type as b
                        where a.accom_type_cd = b.accom_type_code and b.property_id=&property_id.
                            order by a.group_code, b.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql;
            create table &work_lib..group_unique_extract_temp as
                select distinct a.group_code, b.accom_type_id, c.start_dt, c.end_date as end_dt from &work_lib..gb as a,
                    tenant.accom_type as b, &work_lib..gm as c
                where a.accom_type_cd = b.accom_type_code and b.property_id=&property_id. and a.group_code=c.group_code
                    order by a.group_code, b.accom_type_id;
        quit;

        data &work_lib..group_unique_extract (keep=group_code accom_type_id start_dt end_dt group_new );
            merge &work_lib..group_unique_extract_temp (in=groupblockin) &work_lib..group_master (in=groupmasterin);
            by group_code;

            if groupblockin=1 then
                output &work_lib..group_unique_extract;

        data &work_lib..group_exploded (keep=rate pickup original_blocks occupancy_dt blocks group_code accom_type_id group_new);
            retain group_code accom_type_id rate occupancy_dt blocks pickup original_blocks group_new;
            format occupancy_dt date9.;
            set &work_lib..group_unique_extract;
            explode_start_dt= max(start_dt,"&pastgbdate"-"&past_window_size");
            explode_end_dt= min(end_dt,"&pastgbdate"+"&future_window_size"+1);

            do date = explode_start_dt to explode_end_dt;
                rate=0;
                pickup=0;
                original_blocks=0;
                occupancy_dt=date;
                blocks=0;

                if date > &pastgbdate or group_new="Y" then
                    output &work_lib..group_exploded;
            end;
        run;

        proc Sort data= &work_lib..group_exploded;
            by group_code accom_type_id occupancy_dt;

        /*Sandy : What about in Progress groups as occupancy date ge gm start date will filter out in progress groups*/
        proc Sql;
            create table &work_lib..group_block_future_temp as
                select a.rate, a.pickup, a.original_blocks, a.occupancy_dt, a.group_id, b.group_code, a.blocks, a.accom_type_id, b.property_id,
                    b.start_dt ,b.end_dt, b.group_status_code as system_group_status_code, a.group_block_id
                from tenant.group_block as a, tenant.group_master as b
                    where a.group_id = b.group_id and b.property_id=&property_id. and
                        a.occupancy_dt > &pastgbdate and a.occupancy_dt >= b.start_dt
                    order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        proc Sql;
            create table &work_lib..group_block_future as
                select a.*,b.group_status_code as extract_group_status_code
                    from &work_lib..group_block_future_temp as a, &work_lib..group_master as b
                        where a.group_code = b.group_code and b.property_id=a.property_id
                            order by b.group_code, a.accom_type_id, a.occupancy_dt;
        quit;

        data &work_lib..group_exploded1 (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id system_group_status_code extract_group_status_code );
            merge &work_lib..group_exploded (in=tranin) &work_lib..group_block_future (in=futin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or futin=1) then do;
                output &work_lib..group_exploded1;
            end;

        data &work_lib..gb_extract_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id);
            merge &work_lib..group_exploded1 (in=tranin) &work_lib..gb1 (in=extin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or extin=1) then do;
                if tranin = 1 and occupancy_dt > &pastgbdate and
                    upcase(system_group_status_code) ne "CANCELLED" and upcase(extract_group_status_code) eq "CANCELLED" then do;
                    blocks=Pickup;
                end;
                else if tranin=1 and extin=0 and occupancy_dt > &pastgbdate then do;
                    blocks=0;
                    pickup=0;
                end;

                output &work_lib..gb_extract_transactions;
            end;

        proc Sql;
            create table &work_lib..gb_master_deletes as
                select c.rate, 0 as pickup, c.original_blocks, c.occupancy_dt, 0 as blocks, c.accom_type_id, b.group_id, b.group_code, c.group_block_id
                    from &work_lib..gm as a inner join tenant.group_master as b
                        on a.group_code = b.group_code , tenant.group_block as c
                    where ((b.group_id =c.group_id and b.property_id=&property_id. and c.occupancy_dt >= &pastgbdate ) and
                        (c.occupancy_dt < a.start_dt or c.occupancy_dt > a.end_date) )
                    order by b.group_code, c.accom_type_id, c.occupancy_dt;
        quit;

        data &work_lib..gb_extract_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id);
            merge &work_lib..gb_extract_transactions (in=tranin) &work_lib..gb_master_deletes (in=extin);
            by group_code accom_type_id occupancy_dt;

            if ( tranin=1 or extin=1) then do;
                output &work_lib..gb_extract_transactions;
            end;

        data &work_lib..gb_transactions (keep=rate pickup original_blocks occupancy_dt group_code blocks accom_type_id );
            set &work_lib..gb_extract_transactions;
        run;

        data &work_lib..group_block;
            set &work_lib..gb_transactions (rename=(
                rate = i_rate
                pickup=i_pickup
                original_blocks=i_original_blocks
                blocks = i_blocks  ));
            modify &work_lib..group_block key = gb_idx;

            if _iorc_=0 then do;
                if rate ne i_rate
                    or pickup ne i_pickup
                    or original_blocks ne i_original_blocks
                    or blocks ne i_blocks
                    then do;
                    rate = i_rate;
                    pickup=i_pickup;
                    original_blocks=i_original_blocks;
                    blocks = i_blocks;
                    replace &work_lib..group_block;
                end;
            end;
            else do;
                rate = i_rate;
                pickup=i_pickup;
                original_blocks=i_original_blocks;
                blocks = i_blocks;
                output &work_lib..group_block;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* Group Block Pace  */
        data &work_lib..BDE_group_block
            &work_lib..delta_group_block (keep = rate pickup original_blocks occupancy_dt group_code blocks
            accom_type_id snapshot_dttm file_metadata_id change_flag);
            set &work_lib..gb_transactions (rename=(
                rate = i_rate
                pickup=i_pickup
                original_blocks=i_original_blocks
                blocks = i_blocks  ));
            modify &work_lib..BDE_group_block key = gb_idx;

            if _iorc_=0 then do;
                if rate ne i_rate
                    or pickup ne i_pickup
                    or original_blocks ne i_original_blocks
                    or blocks ne i_blocks
                    then do;
                    rate = i_rate;
                    pickup=i_pickup;
                    original_blocks=i_original_blocks;
                    blocks = i_blocks;
                    snapshot_dttm = &snap_dttm;
                    file_metadata_id = &meta_id;
                    change_flag = 1;
                    output &work_lib..delta_group_block;
                end;
                else do;
                    rate = rate;
                    pickup=pickup;
                    original_blocks=original_blocks;
                    blocks = blocks;
                    snapshot_dttm = &snap_dttm;
                    file_metadata_id = &meta_id;
                    change_flag = 0;
                end;
            end;
            else do;
                rate = i_rate;
                pickup=i_pickup;
                original_blocks=i_original_blocks;
                blocks = i_blocks;
                snapshot_dttm = &snap_dttm;
                file_metadata_id = &meta_id;
                change_flag = 1;
                output &work_lib..delta_group_block;
            end;

            _iorc_=0;
            _error_=0;
        run;

        data &work_lib..pace_group_block;
            set &work_lib..delta_group_block;
            format Business_Day_End_Dt date9.;
            snapshot_dttm = &snap_dttm;

            if occupancy_dt le datepart(snapshot_dttm) - 1 then do;
                Business_Day_End_Dt = occupancy_dt;
            end;
            else do;
                Business_Day_End_Dt = datepart(snapshot_dttm) - 1;
            end;

            drop change_flag snapshot_dttm;
        run;

%mend ideas_past_gb_deltas;