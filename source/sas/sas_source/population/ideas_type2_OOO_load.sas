%macro ideas_type2_OOO_load(lib=in_xml)/store;
	%PUT NOTE:ENTERING &SYSMACRONAME WITH SYSCC=&SYSCC;
	libname di_macro ("C:\SAS\Catalogs\analytics" "C:\SAS\Catalogs\common" "C:\SAS\Catalogs\dataintg\rezview" 
	"C:\SAS\Catalogs\dataintg\hilstar" "C:\SAS\Catalogs\population" ) access=readonly;
	options mstored sasmstore=di_macro;
	Options nomacrogen NoSymbolgen nomlogic nomprint nomfile;

	/*Set default parameters for error logging*/
	%let error_codes = 0;
	%let etl_status = 0;
	%let err_str = .;
	%let maxerrors = -1;
	%let GL_ABORT_FLAG = 0;

	/*Setup working lib in case multiple sessions run at the same time*/
	%local work_lib work_dir;

	%ideas_util_tmp_lib(_lib=work_lib,_dir=work_dir);

    data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

	/*Read in Request Header from stored process*/
	data _null_;
		set  &lib..requestHeader;
		call symputx('property_Id',propertyId,'l');
		call symputx('requestId',requestId,'l');
		call symputx('tenant_server',tenant_server,'l');
		call symputx('server_instance',tenant_server_instance,'l');
		call symputx('tenant_pwd',tenant_password,'l');
		call symputx('tenant_user',tenant_user,'l');
		call symputx('tenant_db',tenant_database,'l');
		call symputx('port',tenant_db_port,'l');
		call symputx('saspath',dataset_path,'l');
		stop;
	run;

	%if &syscc > 4 %then %do;
		%ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1);
		%let etl_status = 1;
		%goto EXIT;
	%end;

	%let request_id = %str(&requestid.);
	%let sas_path = %str(&saspath.);

	/*Read in Operation Header from stored process*/
	data _null_;
		set  &lib..OOOPopulationRequest;
		call symputx('path',filePath,'l');
		stop;
	run;

	%if &syscc > 4 %then %do;
		%ideas_util_report_errors(910, 'Failed to read OOOPopulationRequest Header', '', &request_Id., 1);
		%let etl_status = 1;
		%goto EXIT;
	%end;

	/*read in datafile*/
	data &work_lib..All_ooo;
		attrib record_type format=$20.;
		infile "&path" delimiter='|' dsd truncover;

		/* Read in the "universal" fields first */
		input record_type $4. @;

		/* The record type determines the remaining field names.  Read accordingly.*/
		if record_type='_OO_' then do;
			input @1 record_type $                                                                                                            
				hotel :$                                                                                                                
				start_date : yymmdd10.                                                                                                              
				end_date : yymmdd10.                                                                                                              
				number_of_rooms;
			output &work_lib..All_ooo;
		end;

		drop record_type;
	run;

	%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

	/*get list of properties from inbound 000 file*/
	proc sql noprint;
		create table &work_lib..properties as select distinct hotel from &work_lib..all_ooo;
	quit;

	/*generate list of property ids*/
	proc sql noprint;
		create table &work_lib..properties_db as
			select a.*, b.property_id from &work_lib..properties as a, tenant.property as b
				where a.hotel = b.property_code;
	quit;

	/*generate list of accom classes to use to remove suite*/
	proc sql noprint;
		create table &work_lib..properties_rt as
			select b.hotel, a.property_id, a.accom_type_id, a.accom_type_capacity  from tenant.accom_type as a, 
			(select a.*, b.accom_class_id, b.accom_class_name from &work_lib..properties_db as a, tenant.accom_class as b
				where a.property_id = b.property_id
					and upcase(b.accom_class_name) ne 'SUITE') as b 
				where a.accom_class_id = b.accom_class_id and a.property_id = b.property_id;
	quit;

	/*get a list of 000 > 0 to distribute*/
	proc sql noprint;
		create table &work_lib..all_ooo2 as
			select a.*, b.property_id from &work_lib..all_ooo as a, &work_lib..properties_db as b where a.hotel = b.hotel
				and a.number_of_rooms > 0;
	quit;

	proc sql;
		select distinct property_id into: proplist separated by '/' from &work_lib..properties_rt;
	quit;

	%let j = 1;

	%do %while (%scan(&proplist, &j, %str(/)) ne );
		%let prop = %scan(&proplist, &j, %str(/));
		%put &prop.;

		proc sql noprint;
			create table rt&prop. as select * from &work_lib..properties_rt where property_id = &prop.;
		quit;

		proc sql noprint;
			select count(accom_type_id) into: rt_ct from rt&prop.;
		quit;

		proc sql noprint;
			create table ooo&prop. as select * from &work_lib..all_ooo2 where property_id = &prop. and number_of_rooms >0;
		quit;

		proc sql noprint;
			select min(start_date) into: sdate from ooo&prop.;
			select max(end_date) into: edate from ooo&prop.;
		quit;

		/*create dataset with available capacity by date*/
		data rt&prop._dt;
			set rt&prop.;
			occdate = &sdate. -1;
			format occdate date9.;

			do while (occdate <= &edate. -1);
				occdate +1;
				output rt&prop._dt;
			end;
		run;

		proc sort data = rt&prop._dt;
			by occdate accom_type_capacity;
		run;

		proc sql noprint;
			select count(*) into: ct_total from ooo&prop.;
		quit;

		data ooo&prop.;
			set ooo&prop.;
			row = _n_;
		run;

		data ooo;
			attrib occdate format = date9.;
			attrib property_id accom_type_id ooo format = best12.;
		run;

		data &work_lib..ooo&prop.;
			set ooo;
			where ooo ne .;
		run;

		%let i = 1;

		%do i = 1 %to &ct_total;
			%put &i;

			proc sql noprint;
				select start_date into: startdate from ooo&prop. where row = &i;
			quit;

			proc sql noprint;
				select number_of_rooms into: newbalance from ooo&prop. where row = &i;
			quit;

			proc sql noprint;
				create table working as select * from rt&prop._dt where occdate = &startdate and accom_type_capacity ne . order by 
				accom_type_capacity descending;
			quit;

			proc sql noprint;
				select count(accom_type_id) into: newRoomCount from rt&prop._dt where occdate = &startdate;
			quit;

			data working;
				set working;
				ooo = 0;
			run;
			
			proc sql;
					select sum(accom_type_capacity) into: tac from working;
			quit;

			%put HOTEL CAPACITY : &tac;
			%put TOTAL HOTEL OOO COUNT :  &newBalance ;
			%put TOTAL NO OF ROOM TYPES : &newRoomCount;

			
			
			%do %until (&newRoomCount eq 0 | &newBalance eq 0);

				data working;
					set working;
					wa = accom_type_capacity / &tac.;
				run;

				data working;
					set working end=last;
					oc = accom_type_capacity;
					retain newbalance oc sumdist remaining newRoomCount;

					if _n_ = 1 then do;
						newbalance = &newbalance;
						remaining = newbalance;
						sumdist = 0;
						newRoomCount = &newRoomCount;
					end;

					dist = round((newbalance * wa),1);

					if newbalance < dist then
						dist = newbalance;

					if newbalance > 0 and accom_type_capacity > 0 then do;
						if dist = 0 then
							dist = 1;

						if dist > remaining then
							dist = remaining;

						if accom_type_capacity > dist then do;
							ooo = dist + ooo;
							accom_type_capacity = accom_type_capacity - dist;
							sumdist = sumdist + dist;
						end;
						else do;
							ooo = ooo + accom_type_capacity;
							sumdist = sumdist + dist;
							accom_type_capacity = accom_type_capacity - accom_type_capacity;
						end;
					end;

					remaining = newbalance - sumdist;
					newRoomCount = newRoomCount -1;
				run;

				proc sql;
					select min(remaining) into: newbalance from working;
					select newRoomCount into: newRoomCount from working;
				quit;

				data working1;
					set working;
				run;

				data working;
					set working (drop = dist newbalance oc wa remaining sumdist newRoomCount);
				run;

			%end; /*end loop available balance*/

			data oo;
				set working;
				where ooo > 0;
				keep property_id accom_type_id occdate ooo;
			run;

			proc append base = &work_lib..ooo&prop. data = oo force;
			run;

			proc datasets library=work;
				delete working working1;
			quit;

		%end; /*end loop over dates for property*/

		/*update sql tables*/
		proc sort data=&work_lib..ooo&prop.;
			by property_id accom_type_id occdate;
		run;

		data &work_lib..ooo&prop.;
			set &work_lib..ooo&prop.;
			rename occdate = occupancy_dt;
			rename ooo = rooms_not_avail_maint;
		run;

		proc sql;
			create table temp as select distinct * from &work_lib..ooo&prop.;

		data ten_tmp.oo&prop.;
			set temp;
		run;

		data ten_tmp.ttl&prop.;
			set ooo&prop.;
			rename start_date = occupancy_dt;
			rename number_of_rooms = rooms_not_avail_maint;
			keep start_date number_of_rooms property_id;
			format start_date date9.;
		run;

		/*Set connection details to run the pass through query to update/insert the data into rdbms*/
		%local connect_Str;
		%let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)
		uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
		%let connect_str=complete="&connect_str";
		%put Connection Str= &connect_str.;

		proc sql;
			connect to odbc (&connect_str autocommit=no);
			execute(
				with etldata as (select property_id, occupancy_dt, accom_type_id, rooms_not_avail_maint from ##oo&prop.)

				merge accom_activity as t

			using etldata as s

				on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.accom_type_id = s.accom_type_id

				when matched and (t.rooms_not_avail_maint <> s.rooms_not_avail_maint) then

			update set 	t.rooms_not_avail_maint = s.rooms_not_avail_maint;
			) by odbc;
			execute(
				with etldata as (select property_id, occupancy_dt, accom_type_id, rooms_not_avail_maint from ##oo&prop.)

				merge pace_accom_activity as t

			using etldata as s

				on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.accom_type_id = s.accom_type_id

				when matched and (t.rooms_not_avail_maint <> s.rooms_not_avail_maint) then

			update set 	t.rooms_not_avail_maint = s.rooms_not_avail_maint;
			) by odbc;
			execute(
				with etldata as (select property_id, occupancy_dt, rooms_not_avail_maint from ##ttl&prop.)

				merge total_activity as t

			using etldata as s

				on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id

				when matched and (t.rooms_not_avail_maint <> s.rooms_not_avail_maint) then

			update set 	t.rooms_not_avail_maint = s.rooms_not_avail_maint;
			) by odbc;
			execute(
				with etldata as (select property_id, occupancy_dt, rooms_not_avail_maint from ##ttl&prop.)

				merge pace_total_activity as t

			using etldata as s

				on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id

				when matched and (t.rooms_not_avail_maint <> s.rooms_not_avail_maint) then

			update set 	t.rooms_not_avail_maint = s.rooms_not_avail_maint;
			) by odbc;
			execute (
				commit
				)by odbc;
		quit;

		%if &syscc > 4 %then %do;
			%ideas_util_report_errors(910, 'Failed to read request header', '', &request_Id., 1);
			%let ooo_status = 1;
			%goto EXIT;
		%end;

		/*update sas datasets*/
		libname rm_data "&sas_path.";

		data &work_lib..ooo&prop.;
			set &work_lib..ooo&prop.;
			rename rooms_not_avail_maint = i_rooms_not_avail_maint;
		run;

		data rm_data.accom_inventory;
			set &work_lib..ooo&prop.;
			modify  rm_data.accom_inventory key = acc_idx;

			if _iorc_=0 then do;
				if rooms_not_avail_maint ne i_rooms_not_avail_maint
					then do;
					rooms_not_avail_maint = i_rooms_not_avail_maint;
					replace rm_data.accom_inventory;
				end;
			end;
		run;

		data &work_lib..ttl_ooo&prop.;
			set ooo&prop.;
			rename start_date = occupancy_dt;
			rename number_of_rooms = i_rooms_not_avail_maint;
			keep start_date number_of_rooms property_id;
		run;

		proc sort data=&work_lib..ttl_ooo&prop.;
			by property_id occupancy_dt;
		run;

		data rm_data.total_inventory;
			set &work_lib..ttl_ooo&prop.;
			modify  rm_data.total_inventory key = ttl_idx;

			if _iorc_=0 then do;
				if rooms_not_avail_maint ne i_rooms_not_avail_maint
					then do;
					rooms_not_avail_maint = i_rooms_not_avail_maint;
					replace rm_data.total_inventory;
				end;
			end;
		run;

		%let j = %sysevalf(&j + 1);
	%end;

	%EXIT:

	%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then %do;
		%let GL_ABORT_FLAG =1;
		%let syscc = 5;
		%let etl_status = 1;
	%end;

proc sql noprint;
	select count(*) into: errcount from &work_lib..errors;
quit;

%if &errcount. > 0 %then %do;

	proc sql noprint;
		select distinct error_message into: err_str from &work_lib..errors;
	quit;
	
%end;

data _NULL_;
	FILE resXml;
	put '<?xml version="1.0" encoding="UTF-8"?>';
	put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/outoforderpopulation/response/v1">';
	put "<ResponseHeader><PropertyId>&property_Id</PropertyId>" @;
	put "<operationName>OOOPopulationResponse</operationName>" @;
	put "<requestId>&request_Id</requestId>";
	stop;
run;

%if &syscc = 5 %then %do;
	%ideas_util_report_errors(1235, 'SAS Runtime Exception Occurred while OO Population-Please refer to the logs for more information', '', &request_Id., 1);
%end;
	
	%let error_str= "SAS Runtime Exception Occurred while OO Populatio-Please refer to the logs for more information";
   


data _NULL_;
	FILE resXml MOD;
	set &work_lib..errors;

	if _N_ > 0 then do;
		            put "<Errors>";
		            put '<Errors id="' error_cd'">';
		            put "<ErrorMessage> " &error_str "</ErrorMessage>";
		            put "<ErrorData>" &error_str "</ErrorData>";
		            put "</Errors>";
        		    put "</Errors>";
        end;     
run;




data _NULL_;
	FILE resXml MOD;
	 put "</ResponseHeader>";
	put "<OOOPopulationResponse>";
	put "</OOOPopulationResponse></SASResponse>";
run;

%PUT NOTE:LEAVING &SYSMACRONAME WITH SYSCC=&SYSCC;
%mend ideas_type2_OOO_load;
