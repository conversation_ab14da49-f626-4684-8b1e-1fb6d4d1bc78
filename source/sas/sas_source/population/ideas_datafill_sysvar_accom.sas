%macro ideas_datafill_sysvar_accom (property_id, m_var, work_lib)/store;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    
proc sql;
	create table &work_lib..myaccoms as select distinct accom_type_cd from &work_lib..accom order by accom_type_cd;
	create table &work_lib..sysaccoms as select distinct accom_type_code as accom_type_cd from tenant.accom_type where property_id = &property_id. and status_id=1 order by accom_type_cd;
quit;
proc sort data=&work_lib..sysaccoms; 
by  accom_type_cd; 

data &work_lib..missaccoms;
	merge &work_lib..sysaccoms (in=a) &work_lib..myaccoms (in=b);
	by accom_type_cd;
	if a and not b then output &work_lib..missaccoms;
run;

proc sql;
	select accom_type_cd into: accom_list separated by '/' from &work_lib..missaccoms;
	create table &work_lib..myoccdates as select distinct occupancy_dt from &work_lib..OccDates;
quit;

data &work_lib..occdates_accom;
	attrib occupancy_dt format = date9.;
	attrib accom_type_cd format = $50.;
run;

%if %symexist(accom_list) %then %do;

	%let i = 1;
	%do %while (%scan(&accom_list, &i, '/') ne);
		%let prod = %scan(&accom_list, &i, '/');

		data &work_lib..data_&i; set &work_lib..myoccdates;
			accom_type_cd = "&prod.";
		run;
       
		proc append base=&work_lib..occdates_accom data=&work_lib..data_&i force;  quit;

	%let i = %sysevalf(&i + 1);
	%end;

	data &work_lib..occdates_accom; set &work_lib..occdates_accom;
		where occupancy_dt ne .;
			rooms_not_avail_maint = &m_var.;
			rooms_not_avail_other = &m_var.;
			rooms_sold = &m_var.;
			arrivals= &m_var.;
			departures = &m_var.;
			room_revenue = &m_var.;
			food_revenue = &m_var.;
			total_revenue = &m_var.;
			cancellations = &m_var.;
			no_shows = &m_var.;
			record_type_id = 4;
	run;

	proc sql;
		create table &work_lib..occdates_accom as
			select a.*, b.accom_type_capacity as accom_capacity from &work_lib..occdates_accom as a, tenant.accom_type as b where a.accom_type_cd = b.accom_type_code and b.property_id = &property_id. and b.status_id=1; 
	quit;

	proc append base=&work_lib..accom data=&work_lib..occdates_accom force; quit;

%end;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_datafill_sysvar_accom;


