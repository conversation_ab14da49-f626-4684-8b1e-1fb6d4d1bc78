%macro ideas_cow (work_lib, factor, property_id) / store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    /****************************************************************************************
    Cost of Walk

    inputs: 
        f_file = indicator to say if this is the first extract being processed or later extract
            1 = first extract 0 = later extract
        snapshot_dt = date for which historical data is determined
        working libname (example: work or work1)
        factor for calculation  
        property id 
        tenant database connectiond details
    assumptions:  
        active accom_types are identified with a status = 1
        user id to input cost of walk values = SSO User
        status = 0 success        status = 1 failure
    ****************************************************************************************/
    %global message mylib cow_status;
    %let cow_status = 0;
    
    /*generate new list of accoms that need to have cost of walk values assigned*/
    proc Sql;
        create table &work_lib..cow_accoms1 as select * from  tenant.accom_type 
            where status_id = 1 and property_id = &property_id 
                order by accom_type_id, property_id;
        create table &work_lib..costofwalk_default as select * from tenant.costofwalk_default 
            where property_id = &property_id 
                order by accom_type_id, property_id;
    quit;

    data &work_lib..cow_accoms;
        merge &work_lib..cow_accoms1 (in=a) &work_lib..costofwalk_default(in=b);
        by accom_type_id property_id;

        if a and not b;
    run;

    /*count number of accom types to calculate cow*/
    proc Sql noprint;
        select count(*) into: accom_ct from &work_lib..cow_accoms;
    quit;

    %if &accom_ct eq 0 %then %do;
        %let message = 'No accom_type to assign cost of walk';
        %goto EXIT;
    %end;

    /*get user id from database to assign to cow values*/
    proc Sql noprint;
        select distinct user_id into: user_id from tenant.users where User_Name = 'SSO User';
    quit;

    %if not (%symexist(user_id)) %then %do;
        %let etl_status = 1;
        %let message = 'Failed to assign user id';
        %goto EXIT;
    %end;

    /*generate revenue and solds for cow accom types*/
    proc Sql;
        create table &work_lib..cow1 as
            select weekday(a.occupancy_dt) as dow, a.accom_type_id, sum(a.rooms_sold) as rooms_sold, sum(a.room_revenue) as room_revenue
                from tenant.accom_activity as a , &work_lib..cow_accoms as b
                    where a.accom_type_id = b.accom_type_id and a.property_id = &property_id 
                        group by dow, a.accom_type_id
                            order by accom_type_id;
    quit;

    proc Sql;
                  create table &work_lib..cowH as
                      select distinct weekday(a.occupancy_dt) as dow, sum(a.rooms_sold) as rooms_sold, sum(a.room_revenue) as room_revenue
                          from tenant.total_activity as a 
                          where a.property_id = &property_id 
                                  group by dow
                                      order by dow;
        quit;
        
        
        data &work_lib..cow5;
        set &work_lib..cowH;    
	                cow = (&factor. * (room_revenue /rooms_sold));
    	run;
    
       data &work_lib..cow_accom_level;
        set &work_lib..cow1;
        if rooms_sold > 0 and room_revenue > 0 then
            cow = (&factor. * (room_revenue /rooms_sold));
    	run;

	/* check if accom level dow values are present if yes use them else use hotel level*/
	proc sql noprint;
		select count(*) into: cownos from &work_lib..cow_accom_level where cow GT 0;
	quit;
	
	%if %ideas_util_nobs(&work_lib..cow_accom_level) GT 0 AND &cownos GT 0 %then %do;
		proc sql noprint outobs=1;
			select cow into: cowdefault from &work_lib..cow_accom_level where cow GT 0;
		quit;
	%end;
	%else %do;
		/* Added for case where all revenue figures are 0.0 */
		proc sql noprint;
			select count(*) into: cowcnt from &work_lib..cow5 where cow GT 0;
		quit;
		%if &cowcnt GT 0 %then %do;
			proc sql noprint outobs=1;
				select cow into: cowdefault from &work_lib..cow5 where cow GT 0;
			quit;
		%end;
		%else %do;
			%let cowdefault = 1;
		%end;
	%end;
 
 proc Sql;
         create table &work_lib..cowTemp1 as 
             select dow, a.accom_type_id, cow
                 from &work_lib..cow_accoms as a , &work_lib..cow5 as b
                             order by dow,a.accom_type_id;
    quit;
 
 
 PROC SORT data=&work_lib..cow_accom_level; BY dow; RUN;
 PROC SORT data=&work_lib..cowTemp1; BY dow; RUN;
	


	data &work_lib..cowTemp1;
		update  &work_lib..cowTemp1 &work_lib..cow_accom_level;
	 	by dow Accom_Type_ID;
	 run;

 
 PROC SORT data=&work_lib..cowTemp1; BY accom_type_id; RUN;
 
    proc Transpose data = &work_lib..cowTemp1 out=&work_lib..cow3;
        by accom_type_id;
        var cow;
    run;

    /*rename and assign all necessary values for cost of walk insert into database*/
    data &work_lib..cow4;
        set &work_lib..cow3;

        if col1=. or col1 = 0.0 then
            col1=&cowdefault;

        if col2=. or col2 = 0.0 then
            col2=&cowdefault;

        if col3=. or col3 = 0.0 then
            col3=&cowdefault;

        if col4=. or col4 = 0.0 then
            col4=&cowdefault;

        if col5=. or col5 = 0.0 then
            col5=&cowdefault;

        if col6=. or col6 = 0.0 then
            col6=&cowdefault;

        if col7=. or col7 = 0.0 then
            col7=&cowdefault;
			
        rename col1 = Sunday_Value;
        rename col2 = Monday_Value;
        rename col3 = Tuesday_Value;
        rename col4 = Wednesday_Value;
        rename col5 = Thursday_Value;
        rename col6 = Friday_Value;
        rename col7 = Saturday_Value;
        property_id = &property_id;
        user_id = &user_id;
        createdate_dttm = datetime();
        drop _name_;
    run;
	
	data &work_lib..defaultcow (drop= col1 col2 col3 col4 col5 col6 col7 ismis);
		set &work_lib..cow3;
		%let dow1=,dow2=,dow3=,dow4=,dow5=,dow6=,dow7=,ismis=;

		if col1=. or col1 = 0.0 then do;
            dow1="Sun";
			ismis="T";
		end;
        if col2=. or col2 = 0.0 then do;
            dow2="Mon";
			ismis="T";
		end;

        if col3=. or col3 = 0.0 then do;
            dow3="Tue";
			ismis="T";
		end;

        if col4=. or col4 = 0.0 then do;
            dow4="Wed";
			ismis="T";
		end;

        if col5=. or col5 = 0.0 then do;
            dow5="Thu";
			ismis="T";
		end;

        if col6=. or col6 = 0.0 then do;
            dow6="Fri";
			ismis="T";
		end;

        if col7=. or col7 = 0.0 then do;
            dow7="Sat";
			ismis="T";
		end;
		if ismis ="T" then do;
		   output;
		end;
 run;
 
data &work_lib..defaultcow;
		update &work_lib..defaultcow(in=a) &work_lib..Cow_accoms(in=b);
	 	by Accom_Type_ID;
		if a and b;
run;
 
%EXIT:

    %if &etl_status eq 1 or &syscc > 4 %then %do;
        %let GL_ABORT_FLAG =1;
        %let etl_status = 1;
        %put 'Process Stopped';
    %end;

    %put &message;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_cow;
