%macro ideas_mspace_los/store;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    

	proc sql;
		select distinct mkt_seg_id into: list SEPARATED by '|' from tenant.mkt_seg where Property_ID EQ &property_id.;
	quit;

	%let ms_list = &list.;
	%let i = 1;

	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list, &i, |),'l');
		run;
        %let wklibdsn = &work_lib..Ma_los_&ms.;
        %let dsn = Ma_los_&ms.;
        %let libdsn = rm_part.ma_los_&ms.;
        %if %sysfunc(exist(&libdsn)) ne 1 %then %do;
            proc sql;
                create table rm_part.&dsn. like rm_part.ma_los_inv_pace_template;
            quit;
        %end;

		%let i = %sysevalf(&i. + 1);
	%end;

	%if %symexist(skipStayedRTPopulation) and %upcase(&skipStayedRTPopulation.)=TRUE %then %do;
	    %put Booked Data usage started - Stayed sas data only needed for groups;
	    %put &=ms_list;
	    %let group_ms_list=;
        proc sql noprint;
            select distinct mkt_seg_id into: group_ms_list SEPARATED by '|' from tenant.mkt_seg
                where mkt_seg_id in (
                        select distinct mkt_seg_id from tenant.mkt_seg_details msd where business_type_id = (select business_type_id from tenant.business_type where business_type_name='Group')
                        union
                        select distinct mkt_seg_id from tenant.mkt_seg_details_proposed msdp where business_type_id = (select business_type_id from tenant.business_type where business_type_name='Group')
                    );
        quit;
        %let ms_list = &group_ms_list.;
	%end;

	%let i=1;
	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list, &i, |),'l');
		run;
        %let wklibdsn = &work_lib..Ma_los_&ms.;
		%let dsn = Ma_los_&ms.;
		%let libdsn = rm_part.ma_los_&ms.;

        proc sql;
            create table &work_lib..ms_pace (drop= property_id mkt_seg_id) as select * from &work_lib..mkt_accom_los_inventory_pace
            where mkt_seg_id = &ms.;
        quit;


        proc copy in=rm_part out=&work_lib. memtype=data;
            select &dsn.;
        run;

        proc datasets library = &work_lib. nolist;
            change &dsn. = &dsn._fin;
        quit;

        proc append base=&wklibdsn._fin data=&work_lib..ms_pace force;
        run;

		proc copy in=&work_lib. out=rm_part memtype=data;
			select &dsn._fin;
		run;

		%let i = %sysevalf(&i. + 1);
	%end;

%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend;
