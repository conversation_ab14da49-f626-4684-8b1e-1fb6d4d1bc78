%macro ideas_ms_non_pace/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc Sql;
        select distinct mkt_seg_id into: newmslist SEPARATED by '|' from tenant.mkt_seg 
		 where mkt_seg_code in (select distinct mkt_seg_code from &work_lib..inbound_mktseg_cd)
			AND Property_ID EQ &property_id.;
	quit;

    %let i = 1;

    %do %while (%scan(&newmslist., &i., |) ne);

        data _null_;
            call symputx ('ms',%scan(&newmslist, &i, |),'l');
        run;

        %let dsn = Ma_&ms.;
        %let los_dsn = ma_los_&ms.;

        /*create an empty table for the new market segments in CDP run */
        %if %sysfunc(exist(rm_part.&dsn.)) eq 0 %then
            %do;

                proc Sql;
                    create table rm_part.&dsn. like rm_part.ma_inv_pace_template;
                quit;

            %end;

        /*create an empty table for the los data*/
        %if %sysfunc(exist(rm_part.&los_dsn.)) eq 0 %then
            %do;

                proc Sql;
                    create table rm_part.&los_dsn. like rm_part.ma_los_inv_pace_template;
                quit;

            %end;
      %let i = %sysevalf(&i. + 1);
    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_ms_non_pace;
