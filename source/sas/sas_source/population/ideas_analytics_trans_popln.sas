%macro ideas_analytics_trans_popln(lib=in_xml)/store;

   	%let error_codes = 0;
   	%let return_code=0;
   	%let etl_status = 0;
   	%let err_str = .;
   	%let etl_file_status = 0;

	%Put *********************************************************************;
	%Put ****************** Starting Analytics Trans Population ******************;
	%Put *********************************************************************;
	%Put;
    %let app_start_time=%sysfunc(datetime());
    %let startup_start_time=%sysfunc(datetime());
    /* check if data set is present, empty it, else create one */
    	data _null_;
    		set &lib..requestHeader;
    		call symputx('Operation',OperationName,'g');
    		call symputx('property_Id',propertyId,'g');
    		call symputx('requestId',requestId,'g');
    		call symputx('tenant_server',tenant_server,'g');
    		call symputx('server_instance',tenant_server_instance,'g');
    		call symputx('tenant_pwd',tenant_password,'g');
    		call symputx('tenant_user',tenant_user,'g');
    		call symputx('tenant_db',tenant_database,'g');
    		call symputx('port',tenant_db_port,'g');
    		call symputx('saspath',dataset_path,'g');
    		stop;
    	run;

	%if &work_library.=work %then
		%do;
			%let work_lib=work;
			%let work_path=%sysfunc(pathname(work));
		%end;
	%else
		%do;
			%let work_lib=&work_library.;
			%let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
			%let work_path_root=&work_path_drive./sas;

			%ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp;

			%ideas_util_create_dir(dir=&property_id.,base_path=&work_path_root.);
			%let work_path_root=&work_path_drive./sas/temp/&property_id.;

			%ideas_util_create_dir(dir=work,base_path=&work_path_root.);
			%let work_path=&work_path_drive./sas/temp/&property_id./work;
			libname &work_lib "&work_path.";

			proc Datasets library=&work_lib. kill memtype = data noprint nolist;
			run;

		%end;

	data &work_lib..errors;
		length err_ct error_cd 8 request_id error_params error_message $ 200;
		stop;
	run;

    libname sas_home "&saspath.";
	%if %sysfunc(libref(tenant)) ne 0 %then
		%do;
			%ideas_connect_tenant (&tenant_db., &tenant_server., &port., &server_instance., &tenant_user., &tenant_pwd.);

			%if &etl_status = 1 %then
				%goto EXIT;
		%end;

	%if %sysfunc(exist(sas_home.analytics_trans))  %then
	    %do;
            %ideas_UTIL_TRUNCATE_TABLE(sas_home.analytics_trans);
	    %end;
    %else %do;
            data sas_home.analytics_trans;

              length Reservation_Id $50 Individual_Status $2 Arrival_DT Booking_DT LOS 8
            	Cancellation_DT 8 Booked_Accom_Type_ID 8 Stayed_Accom_Type_ID Mkt_Seg_ID 8
            	Rate_Code $50 Num_Adults Num_Children 8 Rate_Value Room_Revenue Food_Beverage_Revenue Other_Revenue 8
            	Source_Booking Channel $50  Nationality $3
              ;
             	call missing(Reservation_ID,Individual_Status,Arrival_Dt,Booking_dt,LOS,Cancellation_DT,Booked_Accom_Type_ID,Stayed_Accom_Type_ID,
            		Mkt_Seg_ID,Rate_Code,Num_Adults,Num_children,Rate_Value,Room_Revenue,Food_Beverage_Revenue,Other_Revenue,
            		Source_booking,Channel,Nationality);
             	if _N_ = 0 then output;
             	stop;
            run;
        %end;
    	%let startup_end_time=%sysfunc(datetime());
    	%let startuprunTime=%sysfunc(round(&startup_end_time-&startup_start_time, .05));


    /* take data from the table and populate it in the database */
    proc sql;
        insert into sas_home.analytics_trans
            select Reservation_ID,Individual_Status,Arrival_Dt,Booking_dt,LOS,Cancellation_DT,Booked_Accom_Type_ID,Stayed_Accom_Type_ID,
                               		Mkt_Seg_ID,Rate_Code,Num_Adults,Num_children,Rate_Value,Room_Revenue,Food_Beverage_Revenue,Other_Revenue,
                               		Source_booking,Channel,Nationality
                from tenant.analytics_trans
                where (rate_code is not null and rate_code <> '');
    run;

    %EXIT:

	%let app_end_time=%sysfunc(datetime());
	%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));


    	proc Sql noprint;
    		select count(*) into: errcount from &work_lib..errors;
    	quit;

    	%if &errcount. > 0 %then
    		%do;

    			proc Sql noprint;
    				select distinct error_message into: err_str from &work_lib..errors;
    			quit;

    		%end;

    	data _null_;
    		FILE resXml;
    		put '<?xml version="1.0" encoding="UTF-8"?>';
    		put '<AnalyticTransSASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/analyticstranspopulation/response/v1" ';
    		put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
    		put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/analyticstranspopulation/response/v1/analytics_trans_population_response_data.xsd ">';
    		put "<ResponseHeader> <PropertyId>&property_Id.</PropertyId>" @;
    		put "<operationName>analyticsTransPopulation</operationName>" @;
    		put "<requestId>&requestId</requestId>";
    		stop;
    	run;

    	%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 %then
    		%do;
    			%let GL_ABORT_FLAG =1;

    			proc Sort data=&work_lib..errors;
    				by request_id error_cd;
    			run;

    			%if &syscc = 1012 %then
    				%do;
    					%let error_str= "SAS Runtime Exception Occurred-Please refer to the logs for more information";

    					data _null_;
    						FILE resXml MOD;
    						put "<Errors>";
    						put '<Errors id="1235">';
    						put "<ErrorMessage>" &error_str "</ErrorMessage>";
    						put "<ErrorData>Error Occurred while Processing</ErrorData>";
    						put "</Errors>";
    						put "</Errors>";
    					run;

    				%end;
    			%else
    				%do;

    					data _null_;
    						FILE resXml MOD;
    						put "<Errors>";
    						put '<Errors id="' error_cd '">';
    						put "<ErrorMessage>" "&error_str" "</ErrorMessage>";
    						put "<ErrorData>" error_params "</ErrorData>";
    						put "</Errors>";
    						put "</Errors>";
    					run;

    				%end;
    		%end;

    	data _null_;
    		FILE resXml MOD;
    		put "</ResponseHeader>";
    		put "<AnalyticTransPopulationResponse>";
       		put "</AnalyticTransPopulationResponse>";
    		put "</AnalyticTransSASResponse>";
    	run;

	%if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &etl_status = 1 %then
		%let Status_desc=Failed;
	%else
		%do;
			%let Status_desc=Successful;

			%if %upcase(&Sas_Debug.)=FALSE %then
				%do;

					proc printto log="&logfile" new;
					run;

				%end;
		%end;

    %put Finished creating analytics trans datasets;

    %PUT ---------------------------------------------------------------------;
    %PUT -------------------- Ending Analytics Trans Population ----------------------;
    %PUT ---------------------------------------------------------------------;
    %PUT;
	%Put -            Opera Population Status: &status_desc.;
	%Put -                          Operation: &Operation.;
	%Put -                      Sas Data Path: &saspath.;
	%Put -                  Work Library Path: &work_path.;
	%Put -                  Work Library Name: &work_lib;
	%Put;
	%Put -                    Database Server: &tenant_server.;
	%Put -                    Server Instance: &server_instance.;
	%Put -                      Database Name: &tenant_db.;
	%Put -                      Database User: &tenant_user.;
	%Put -                      Database Port: &port.;
	%Put -               Startup Process Time: &startuprunTime. seconds;
	%Put -                 Total Process Time: &runTime. seconds;
%mend;