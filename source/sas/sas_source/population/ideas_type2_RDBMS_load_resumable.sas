%macro ideas_type2_RDBMS_load_resumable(tenant_db, tenant_server, port, server_instance, tenant_user, tenant_pwd)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    /* upload the rm_data delta tables as temp tables in sql. In case the process failed/restarted, 
    we need to get the delta tables from the rm_data folder then assign the snapshot_dttm to the table and upload it as a temp to sql

    Prepare activity tables
    if the client parameter is set to missing (.) then we need to replace that value with the m_var value specified by the client Default -1*/
    %let rm_data = rm_data;
    %let accom_str = accom_capacity arrivals rooms_not_avail_maint rooms_not_avail_other rooms_sold departures room_revenue total_revenue food_revenue;
    %let mkt_str = arrivals departures rooms_sold room_revenue total_revenue food_revenue;
    %let total_str = total_accom_capacity rooms_not_avail_maint rooms_not_avail_other rooms_sold arrivals departures room_revenue total_revenue food_revenue;

		data &work_lib..temp_delta_accom_inventory;
			array miss &accom_str.;
			set &work_lib..delta_accom_inventory;
			where change_flag = 1;
			snapshot_dttm = &snap_dttm;
			Last_Updated_DTTM = datetime();
			createdate = datetime();
			drop change_flag;

			do over miss;
				if miss=. then
					miss=-1;
			end;

			if arrivals = -1 then
				no_shows = -1;

			if arrivals = -1 then
				cancellations = -1;

			if food_revenue = -1 or food_revenue = . then
				food_revenue = 0;
		run;

		data &work_lib..temp_delta_mkt_accom_inventory;
			array miss &mkt_str.;
			set &work_lib..delta_mkt_accom_inventory;
			where change_flag = 1;
			snapshot_dttm = &snap_dttm;
			Last_Updated_DTTM = datetime();
			createdate = datetime();
			drop change_flag;

			do over miss;
				if miss=. then
					miss=-1;
			end;

			if arrivals = -1 then
				no_shows = -1;

			if arrivals = -1 then
				cancellations = -1;

			if food_revenue = -1 or food_revenue = . then
				food_revenue = 0;
		run;
		
		data &work_lib..temp_delta_total_inventory;
			array miss &total_str.;
			set &work_lib..delta_total_inventory;
			where change_flag = 1;
			snapshot_dttm = &snap_dttm;
			Last_Updated_DTTM = datetime();
			createdate = datetime();
			drop change_flag;

			do over miss;
				if miss=. then
					miss=-1;
			end;

			if arrivals = -1 then
				no_shows = -1;

			if arrivals = -1 then
				cancellations = -1;

			if food_revenue = -1 or food_revenue = . then
				food_revenue = 0;
		run;
		
		data &work_lib..temp_pace_total_inventory;
			set &work_lib..delta_total_inventory;
			array miss &total_str.;
			snapshot_dttm = &snap_dttm;
			Last_Updated_DTTM = datetime();
			createdate = datetime();

			if occupancy_dt le datepart(snapshot_dttm) - 1 then do;
				Business_Day_End_Dt = occupancy_dt;
			end;
			else do;
				Business_Day_End_Dt = datepart(snapshot_dttm) - 1;
			end;

			Month_ID = month(occupancy_dt);
			Year_ID = year(occupancy_dt) - 2000;
			drop change_flag;

			do over miss;
				if miss=. then
					miss=-1;
			end;

			if arrivals = -1 then
				no_shows = -1;

			if arrivals = -1 then
				cancellations = -1;

			if food_revenue = -1 or food_revenue = . then
				food_revenue = 0;
		run;
		
		data &work_lib..temp_pace_accom_activity;
			array miss &accom_str.;
			set &work_lib..delta_accom_inventory;
			snapshot_dttm = &snap_dttm;
			Last_Updated_DTTM = datetime();
			createdate = datetime();

			if occupancy_dt le datepart(snapshot_dttm) - 1 then do;
				Business_Day_End_Dt = occupancy_dt;
			end;
			else do;
				Business_Day_End_Dt = datepart(snapshot_dttm) - 1;
			end;

			Month_ID = month(occupancy_dt);
			Year_ID = year(occupancy_dt) - 2000;

			do over miss;
				if miss=. then
					miss=-1;
			end;

			if arrivals = -1 then
				no_shows = -1;

			if arrivals = -1 then
				cancellations = -1;

			if food_revenue = -1 or food_revenue = . then
				food_revenue = 0;
			drop change_flag;
		run;

		proc Sql noprint;
			create table &work_lib..temp_pace_mkt_activity_temp as
				select occupancy_dt, mkt_seg_id, property_id, sum(arrivals) as Arrivals, sum(departures) as Departures,
					sum(no_shows) as no_shows, sum(cancellations) as cancellations, sum(room_revenue) as room_revenue,
					sum(rooms_sold) as rooms_sold, sum(total_revenue) as total_revenue, sum(food_revenue) as food_revenue, file_metadata_id
				from &work_lib..delta_mkt_accom_inventory
					group by occupancy_dt, mkt_seg_id, property_id, file_metadata_id;
		quit;

    /* prepare RDBMS pace tables */
    data _null_;
        set &work_lib..file_metadata;
        tmp_dt=put(snapshot_date,date9.)||':'||trim(left(snapshot_time));
        snap_dttm=input(tmp_dt,datetime20.);
        call symput('snapshot_date',snap_dttm);
        call symput('snap_date', compress(snapshot_date));
        call symput('snap_time', compress(snapshot_time));
        call symput('snap_dttm', compress(snap_dttm));
    run;

    

    data &work_lib..temp_pace_mkt_activity;
        array miss &mkt_str.;
        set &work_lib..temp_pace_mkt_activity_temp;
        snapshot_dttm = &snap_dttm;
        Last_Updated_DTTM = datetime();
        createdate = datetime();

        if occupancy_dt le datepart(snapshot_dttm) - 1 then do;
            Business_Day_End_Dt = occupancy_dt;
        end;
        else do;
            Business_Day_End_Dt = datepart(snapshot_dttm) - 1;
        end;

        Month_ID = month(occupancy_dt);
        Year_ID = year(occupancy_dt) - 2000;

        do over miss;
            if miss=. then
                miss=-1;
        end;

        if arrivals = -1 then
            no_shows = -1;

        if arrivals = -1 then
            cancellations = -1;

        if food_revenue = -1 or food_revenue = . then
            food_revenue = 0;
    run;

    

    /* remove leading 0 for mkt seg pace */
    proc Sql;
        create table &work_lib..tpace_m as
            select distinct *, (arrivals + departures + no_shows + cancellations+ room_revenue + 
                rooms_sold + total_revenue + food_revenue) as total from &work_lib..temp_pace_mkt_activity
            order by mkt_seg_id, occupancy_dt desc;;
    quit;

    data &work_lib..matrix_m;
        retain matrix_id 0;
        set &work_lib..tpace_m;
        by mkt_seg_id descending occupancy_dt;

        if first.mkt_seg_id then
            matrix_id+1;
    run;

    data &work_lib..final_m;
        retain isFound;
        set &work_lib..matrix_m;
        by matrix_id;

        if first.matrix_id then
            isFound=0;

        * reset for each group;
        if not isFound and total=0 then
            delete;

        * delete on data data condition;
        if total GT 0 then
            isFound=_n_;

        * stop delete on first occurrance of data condition;
        drop isfound;
    run;

    /* remove leading 0 for accom pace */
    proc Sql;
        create table &work_lib..tpace_a as
            select distinct *, (arrivals + departures + no_shows + cancellations+ room_revenue + 
                rooms_sold + total_revenue + food_revenue) as total from &work_lib..temp_pace_accom_activity
            order by accom_type_id, occupancy_dt desc;
    quit;

    data &work_lib..matrix_a;
        retain matrix_id 0;
        set &work_lib..tpace_a;
        by accom_type_id descending occupancy_dt;

        if first.accom_type_id then
            matrix_id+1;
    run;

    data &work_lib..final_a;
        retain isFound;
        set &work_lib..matrix_a;
        by matrix_id;

        if first.matrix_id then
            isFound=0;

        * reset for each group;
        if not isFound and total=0 then
            delete;

        * delete on data data condition;
        if total GT 0 then
            isFound=_n_;

        * stop delete on first occurrance of data condition;
        drop isfound;
    run;

    /*remove leading 0 for total pace*/
    proc Sql;
        create table &work_lib..tpace_t as
            select distinct *, (arrivals + departures + no_shows + cancellations+ room_revenue + 
                rooms_sold + total_revenue + food_revenue) as total from &work_lib..temp_pace_total_inventory
            order by occupancy_dt desc;
    quit;

    data &work_lib..matrix_t;
        retain matrix_id 0;
        set &work_lib..tpace_t;
        by descending occupancy_dt;

        if first.occupancy_dt then
            matrix_id+1;
    run;

    data &work_lib..final_t;
        retain isFound;
        set &work_lib..matrix_t;
        by descending occupancy_dt;

        if first.matrix_id then
            isFound=0;

        * reset for each group;
        if not isFound and total=0 then
            delete;

        * delete on data data condition;
        if total GT 0 then
            isFound=_n_;

        * stop delete on first occurrance of data condition;
        drop isfound;
    run;

    /*Set connection details to run the pass through query to update/insert the data into rdbms*/
    %local connect_Str;
    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_pwd%str(;)database=&tenant_db%str(;)port=&port;
    %let connect_str=complete="&connect_str";
    %local acc_activity mkt_acc_activity total_activity group_master;
    %local pace_acc_activity pace_mkt_activity pace_total_activity;

    %ideas_trans_upload_tmp(upload_table=&work_lib..temp_delta_accom_inventory,
        _name_in_db=acc_activity,
        like_table=tenant.accom_activity(drop=Accom_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=2&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..temp_delta_mkt_accom_inventory,
        _name_in_db=mkt_acc_activity,
        like_table=tenant.mkt_accom_activity(drop=Mkt_Accom_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=3&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..temp_delta_total_inventory,
        _name_in_db=total_activity,
        like_table=tenant.total_activity(drop=Total_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=4&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..final_a,
        _name_in_db=pace_acc_activity,
        like_table=tenant.pace_accom_activity(drop=PACE_Accom_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=5&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..final_m,
        _name_in_db=pace_mkt_activity,
        like_table=tenant.pace_mkt_activity(drop=PACE_Mkt_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=6&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..final_t,
        _name_in_db=pace_total_activity,
        like_table=tenant.pace_total_activity(drop=PACE_Total_Activity_ID),
        tmp_trans_lib=ten_tmp,cnt=7&load_id.);
    %ideas_trans_upload_tmp(upload_table=&work_lib..group_master(drop=group_new),
        _name_in_db=group_master,
        like_table=tenant.group_master,
        tmp_trans_lib=ten_tmp,cnt=8&load_id.);

    /*put the group block tables in directly since all the columns do not match*/
    data ten_tmp.group_blk&load_id.;
        set &work_lib..group_block;
    run;

    data ten_tmp.pace_group_blk&load_id.;
        set &work_lib..pace_group_block;
    run;

    data ten_tmp.group_blk_deletes&load_id.;
        set &work_lib..gb_master_deletes;
    run;

    %let acc_activity = ##&acc_activity;
    %let mkt_acc_activity = ##&mkt_acc_activity;
    %let total_activity = ##&total_activity;
    %let pace_acc_activity = ##&pace_acc_activity;
    %let pace_mkt_activity = ##&pace_mkt_activity;
    %let pace_total_activity = ##&pace_total_activity;
    %let group_mstr = ##&group_master;

    %if &syscc > 4 %then %do;
        %ideas_util_report_errors(920, 'Failed to insert temp tables into RDBMS', '', &request_Id.)
        %goto EXIT;
    %end;

    /* update/insert mkt_accom_activity, update/insert accom_activity, update/insert total_activity;
     *last_updated_dttm and createdate are generated from rdbms on the insert;
     *If the load type is BDE then update the pace database tables ;
     */
    proc Sql;
        connect to odbc (&connect_str autocommit=no);
        execute(
            with etldata as (select property_id, group_code, group_name, group_description, master_group_id, master_group_code,
            group_status_code, group_type_code, mkt_seg_id, start_Dt, end_dt, booking_dt, pickup_type_code, cancel_dt,
            booking_type, sales_person, cut_off_date, cut_off_days
        from &group_mstr.)

            merge group_master as t

        using etldata as s

            on t.property_id = s.property_id and t.group_code = s.group_code

            when matched and (t.Group_Name <> s.Group_Name
            or ( t.Group_Description is null and s.Group_Description is not null)
            OR (t.Group_Description is not null and s.Group_Description is null)
            or t.Group_Description <> s.Group_Description
            or t.Master_Group_ID <> s.Master_Group_ID
            or t.Master_Group_Code <> s.Master_Group_Code
            or t.group_status_code <> s.group_status_code
            or t.Group_Type_Code <> s.Group_Type_Code
            or t.Mkt_Seg_ID <> s.Mkt_Seg_ID
            or t.Start_DT <> s.Start_DT
            or t.End_DT <> s.End_DT
            or t.Booking_DT <> s.Booking_DT
            or ( t.Pickup_type_code is null and s.Pickup_type_code is not null)
            OR (t.Pickup_type_code is not null and s.Pickup_type_code is null)
            or t.Pickup_Type_Code <> s.Pickup_Type_Code
            or ( t.cancel_DT is null and s.cancel_DT is not null)
            OR (t.cancel_DT is not null and s.cancel_DT is null)
            or t.Cancel_DT <> s.Cancel_DT
            or ( t.sales_person is null and s.sales_person is not null)
            OR (t.sales_person is not null and s.sales_person is null)
            or t.Sales_Person <> s.Sales_Person
            or ( t.cut_off_date is null and s.cut_off_date is not null)
            OR (t.Cut_Off_date is not null and s.cut_off_date is null)
            OR t.Cut_Off_date <> s.Cut_Off_date
            or (t.Cut_Off_days is null and s.Cut_Off_days is not null)
            or (t.Cut_Off_days is not null and s.Cut_Off_days is null)
            or t.Cut_Off_days <> s.Cut_Off_days
            or ( t.booking_type is null and s.booking_type is not null)
            OR (t.booking_type is not null and s.booking_type is null)
            or t.Booking_type <> s.Booking_type) then

        update set t.Group_Name = s.Group_Name,
            t.Group_Description = s.Group_Description,
            t.Master_Group_ID = s.Master_Group_ID,
            t.Master_Group_Code = s.Master_Group_Code,
            t.group_status_code = s.group_status_code,
            t.Group_Type_Code = s.Group_Type_Code,
            t.Mkt_Seg_ID = s.Mkt_Seg_ID,
            t.Start_DT = s.Start_DT,
            t.End_DT = s.End_DT,
            t.Booking_DT = s.Booking_DT,
            t.Pickup_Type_Code = s.Pickup_Type_Code,
            t.Cancel_DT = s.Cancel_DT,
            t.Sales_Person = s.Sales_Person,
            t.Cut_Off_date = s.Cut_Off_date,
            t.Cut_Off_days = s.Cut_Off_days,
            t.Booking_type = s.Booking_type

            when not matched by target then
        insert (property_id, group_code, Group_Name, Group_Description, Master_Group_ID, master_group_code, group_status_code, Group_Type_Code, Mkt_Seg_ID, Start_DT, End_DT, 
            Booking_DT, Pickup_Type_Code, Cancel_DT, Sales_Person, Cut_Off_date, Cut_Off_days, Booking_type)
        values (s.property_id, s.group_code, s.Group_Name, s.Group_Description, s.Master_Group_ID, s.master_group_code, s.group_status_code, s.Group_Type_Code, s.Mkt_Seg_ID, s.Start_DT, s.End_DT, 
            s.Booking_DT, s.Pickup_Type_Code, s.Cancel_DT, s.Sales_Person, s.Cut_Off_date, s.Cut_Off_days, s.Booking_type);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute(
            with etldata as (select a.accom_type_id, a.blocks, b.group_id, a.occupancy_dt, a.original_blocks, a.pickup, a.rate
        from ##group_blk&load_id. as a, group_master as b where a.group_code = b.group_code) 

            merge group_block as t

        using etldata as s

            on t.occupancy_dt = s.occupancy_dt and t.group_id = s.group_id and t.accom_type_id = s.accom_type_id

            when matched and (t.blocks <> s.blocks
            or t.original_blocks <> s.original_blocks
            or t.pickup <> s.pickup
            or t.rate <> s.rate) then

        update set t.blocks = s.blocks,
            t.original_blocks = s.original_blocks,
            t.pickup = s.pickup,
            t.rate = s.rate

            when not matched by target then
        insert (accom_type_id, blocks, group_id, occupancy_dt, original_blocks, pickup, rate)
            values (s.accom_type_id, s.blocks, s.group_id, s.occupancy_dt, s.original_blocks, s.pickup, s.rate);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute( 
            delete from group_block 
                where group_block_id in (select group_block_id 
                    from ##group_blk_deletes&load_id.);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute(
            with etldata as (select property_id, occupancy_dt, accom_type_id, mkt_seg_id, rooms_sold, 
            arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, 
            total_revenue, file_metadata_id, last_updated_dttm, createdate, snapshot_dttm
        from &mkt_acc_activity.)

            merge mkt_accom_activity as t

        using etldata as s

            on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.accom_type_id = s.accom_type_id and t.mkt_seg_id = s.mkt_seg_id

            when matched and (t.rooms_sold <> s.rooms_sold
            or t.arrivals <> s.arrivals
            or t.departures <> s.departures
            or t.cancellations <> s.cancellations
            or t.no_shows <> s.no_shows
            or t.room_revenue <> s.room_revenue
            or t.food_revenue <> s.food_revenue
            or t.total_revenue <> s.total_revenue) then

        update set t.rooms_sold = s.rooms_sold,
            t.arrivals = s.arrivals,
            t.departures = s.departures,
            t.cancellations = s.cancellations,
            t.no_shows = s.no_shows,
            t.room_revenue = s.room_revenue,
            t.food_revenue = s.food_revenue,
            t.total_revenue = s.total_revenue,
            t.file_metadata_id = s.file_metadata_id,
            t.SnapShot_DTTM = s.SnapShot_DTTM,
            t.last_updated_dttm = s.last_updated_dttm

            when not matched by target then
        insert (occupancy_dt, property_id, accom_type_id, mkt_seg_id, rooms_sold, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, 
            total_revenue,file_metadata_id, snapshot_dttm)
        values (s.occupancy_dt, s.property_id, s.accom_type_id, s.mkt_seg_id, s.rooms_sold, s.arrivals, s.departures, s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, 
            s.total_revenue, s.file_metadata_id, s.snapshot_dttm);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute (
            with etldata as (select property_id, occupancy_dt, accom_type_id, accom_capacity, rooms_sold, 
            rooms_not_avail_maint, Rooms_Not_Avail_Other, arrivals, departures, cancellations,
            no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, last_updated_dttm, createdate, snapshot_dttm
        from &acc_activity.)

            merge accom_activity as t

        using etldata as s

            on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.accom_type_id = s.accom_type_id

            when matched and (t.accom_capacity <> s.accom_capacity
            or t.rooms_sold <> s.rooms_sold
            or t.rooms_not_avail_maint <> s.rooms_not_avail_maint
            or t.Rooms_Not_Avail_Other <> s.Rooms_Not_Avail_Other
            or t.arrivals <> s.arrivals
            or t.departures <> s.departures
            or t.cancellations <> s.cancellations
            or t.no_shows <> s.no_shows
            or t.room_revenue <> s.room_revenue
            or t.food_revenue <> s.food_revenue
            or t.total_revenue <> s.total_revenue) then

        update set t.accom_capacity = s.accom_capacity,
            t.rooms_sold = s.rooms_sold,
            t.rooms_not_avail_maint = s.rooms_not_avail_maint,
            t.Rooms_Not_Avail_Other = s.Rooms_Not_Avail_Other,
            t.arrivals = s.arrivals,
            t.departures = s.departures,
            t.cancellations = s.cancellations,
            t.no_shows = s.no_shows,
            t.room_revenue = s.room_revenue,
            t.food_revenue = s.food_revenue,
            t.total_revenue = s.total_revenue,
            t.file_metadata_id = s.file_metadata_id,
            t.SnapShot_DTTM = s.SnapShot_DTTM,
            t.last_updated_dttm = s.last_updated_dttm

            when not matched by target then
        insert (occupancy_dt, property_id, accom_type_id, accom_capacity, rooms_sold, rooms_not_avail_maint,
            Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, 
            total_revenue,file_metadata_id, snapshot_dttm)
        values (s.occupancy_dt, s.property_id, s.accom_type_id, s.accom_capacity, s.rooms_sold, s.rooms_not_avail_maint,
            s.Rooms_Not_Avail_Other, s.arrivals, s.departures, s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, 
            s.total_revenue, s.file_metadata_id, s.snapshot_dttm);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute (
            with etldata as (select property_id, occupancy_dt, Total_accom_capacity, rooms_sold, rooms_not_avail_maint, 
            Rooms_Not_Avail_Other, arrivals, departures, cancellations,no_shows, room_revenue, 
            food_revenue, total_revenue, file_metadata_id, last_updated_dttm, createdate, snapshot_dttm
        from &total_activity.)

            merge total_activity as t

        using etldata as s

            on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id

            when matched and (t.total_accom_capacity <> s.total_accom_capacity
            or t.rooms_sold <> s.rooms_sold
            or t.rooms_not_avail_maint <> s.rooms_not_avail_maint
            or t.Rooms_Not_Avail_Other <> s.Rooms_Not_Avail_Other
            or t.arrivals <> s.arrivals
            or t.departures <> s.departures
            or t.cancellations <> s.cancellations
            or t.no_shows <> s.no_shows
            or t.room_revenue <> s.room_revenue
            or t.food_revenue <> s.food_revenue
            or t.total_revenue <> s.total_revenue) then

        update set t.total_accom_capacity = s.total_accom_capacity,
            t.rooms_sold = s.rooms_sold,
            t.rooms_not_avail_maint = s.rooms_not_avail_maint,
            t.Rooms_Not_Avail_Other = s.Rooms_Not_Avail_Other,
            t.arrivals = s.arrivals,
            t.departures = s.departures,
            t.cancellations = s.cancellations,
            t.no_shows = s.no_shows,
            t.room_revenue = s.room_revenue,
            t.food_revenue = s.food_revenue,
            t.total_revenue = s.total_revenue,
            t.file_metadata_id = s.file_metadata_id,
            t.SnapShot_DTTM = s.SnapShot_DTTM,
            t.last_updated_dttm = s.last_updated_dttm

            when not matched by target then
        insert (occupancy_dt, property_id, total_accom_capacity, rooms_sold, rooms_not_avail_maint,
            Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, 
            total_revenue,file_metadata_id, snapshot_dttm)
        values (s.occupancy_dt, s.property_id, s.total_accom_capacity, s.rooms_sold, s.rooms_not_avail_maint,
            s.Rooms_Not_Avail_Other, s.arrivals, s.departures, s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, 
            s.total_revenue, s.file_metadata_id, s.snapshot_dttm);
        ) by odbc;
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        %if %upcase(&loadtype.)=BDE %then %do;
            execute(
                with etldata as (select a.accom_type_id, a.blocks, a.business_day_end_dt, b.group_id, a.occupancy_dt, a.original_blocks, a.pickup, a.rate
            from ##pace_group_blk&load_id. as a, group_master as b where a.group_code = b.group_code )

                merge pace_group_block as t

            using etldata as s

                on t.occupancy_dt = s.occupancy_dt and t.group_id = s.group_id and t.accom_type_id = s.accom_type_id and t.business_day_end_dt = s.business_day_end_dt

                when matched and (t.blocks <> s.blocks
                or t.original_blocks <> s.original_blocks
                or t.pickup <> s.pickup
                or t.rate <> s.rate) then

            update set t.blocks = s.blocks,
                t.original_blocks = s.original_blocks,
                t.pickup = s.pickup,
                t.rate = s.rate

                when not matched by target then
            insert (accom_type_id, business_day_end_dt, blocks, group_id, occupancy_dt, original_blocks, pickup, rate)
                values (s.accom_type_id, s.business_day_end_dt, s.blocks, s.group_id, s.occupancy_dt, s.original_blocks, s.pickup, s.rate);
            ) by odbc;
            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                %let error_codes = 921;
                %goto EXIT;
            %end;

            execute (
                with etldata as (select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Mkt_Seg_ID, rooms_sold, arrivals, departures,
                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id, last_updated_dttm
            from &pace_mkt_activity.)

                merge pace_mkt_activity as t

            using etldata as s

                on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.mkt_seg_id = s.mkt_seg_id and t.business_day_end_dt = s.business_day_end_dt

                when matched and (t.rooms_sold <> s.rooms_sold
                or t.arrivals <> s.arrivals
                or t.departures <> s.departures
                or t.cancellations <> s.cancellations
                or t.no_shows <> s.no_shows
                or t.room_revenue <> s.room_revenue
                or t.food_revenue <> s.food_revenue
                or t.total_revenue <> s.total_revenue) then

            update set t.rooms_sold = s.rooms_sold,
                t.arrivals = s.arrivals,
                t.departures = s.departures,
                t.cancellations = s.cancellations,
                t.no_shows = s.no_shows,
                t.room_revenue = s.room_revenue,
                t.food_revenue = s.food_revenue,
                t.total_revenue = s.total_revenue,
                t.file_metadata_id = s.file_metadata_id,
                t.SnapShot_DTTM = s.SnapShot_DTTM,
                t.last_updated_dttm = s.last_updated_dttm

                when not matched by target and (rooms_sold >0 OR arrivals >0 OR departures >0 OR cancellations >0
                OR no_shows >0 OR room_revenue !=0.0 OR food_revenue !=0.0 OR total_revenue !=0.0) then
            insert (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Mkt_Seg_ID, rooms_sold, arrivals, departures,
                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id)
            values (s.property_id, s.occupancy_dt, s.snapshot_dttm, s.business_day_end_dt, s.Mkt_Seg_ID, s.rooms_sold, s.arrivals, s.departures, 
                s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, s.total_revenue, s.file_metadata_id, s.month_id, s.year_id);
            ) by odbc;
            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                %let error_codes = 921;
                %goto EXIT;
            %end;

            execute (
                with etldata as (select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Accom_Type_ID, accom_capacity, rooms_not_avail_maint, Rooms_Not_Avail_Other, rooms_sold, arrivals, departures,
                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id, last_updated_dttm
            from &pace_acc_activity.)

                merge PACE_Accom_Activity as t

            using etldata as s

                on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.Accom_Type_ID = s.Accom_Type_ID and t.business_day_end_dt = s.business_day_end_dt

                when matched and (t.accom_capacity <> s.accom_capacity 
                or t.rooms_sold <> s.rooms_sold
                or t.arrivals <> s.arrivals
                or t.rooms_not_avail_maint <> s.rooms_not_avail_maint
                or t.Rooms_Not_Avail_Other <> s.Rooms_Not_Avail_Other
                or t.departures <> s.departures
                or t.cancellations <> s.cancellations
                or t.no_shows <> s.no_shows
                or t.room_revenue <> s.room_revenue
                or t.food_revenue <> s.food_revenue
                or t.total_revenue <> s.total_revenue) then

            update set t.accom_capacity = s.accom_capacity,
                t.rooms_sold = s.rooms_sold,
                t.arrivals = s.arrivals,
                t.rooms_not_avail_maint = s.rooms_not_avail_maint,
                t.Rooms_Not_Avail_Other = s.Rooms_Not_Avail_Other,
                t.departures = s.departures,
                t.cancellations = s.cancellations,
                t.no_shows = s.no_shows,
                t.room_revenue = s.room_revenue,
                t.food_revenue = s.food_revenue,
                t.total_revenue = s.total_revenue,
                t.file_metadata_id = s.file_metadata_id,
                t.SnapShot_DTTM = s.SnapShot_DTTM,
                t.last_updated_dttm = s.last_updated_dttm

                when not matched by target then
            insert (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, Accom_Type_ID, accom_capacity, rooms_sold, rooms_not_avail_maint,
                Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id)
            values (s.property_id, s.occupancy_dt, s.snapshot_dttm, s.business_day_end_dt, s.Accom_Type_ID, s.accom_capacity, s.rooms_sold, s.rooms_not_avail_maint,
                s.Rooms_Not_Avail_Other, s.arrivals, s.departures, s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, s.total_revenue, s.file_metadata_id, s.month_id, s.year_id);
            ) by odbc;
            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                %let error_codes = 921;
                %goto EXIT;
            %end;

            execute (
                with etldata as (select property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, total_accom_capacity, rooms_not_avail_maint, Rooms_Not_Avail_Other, rooms_sold, arrivals, departures,
                cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id, last_updated_dttm
            from &pace_total_activity.)

                merge pace_total_activity as t

            using etldata as s

                on t.occupancy_dt = s.occupancy_dt and t.property_id = s.property_id and t.business_day_end_dt = s.business_day_end_dt

                when matched and (t.total_accom_capacity <> s.total_accom_capacity 
                or t.rooms_sold <> s.rooms_sold
                or t.arrivals <> s.arrivals
                or t.rooms_not_avail_maint <> s.rooms_not_avail_maint
                or t.Rooms_Not_Avail_Other <> s.Rooms_Not_Avail_Other
                or t.departures <> s.departures
                or t.cancellations <> s.cancellations
                or t.no_shows <> s.no_shows
                or t.room_revenue <> s.room_revenue
                or t.food_revenue <> s.food_revenue
                or t.total_revenue <> s.total_revenue) then

            update set t.total_accom_capacity = s.total_accom_capacity,
                t.rooms_sold = s.rooms_sold,
                t.arrivals = s.arrivals,
                t.rooms_not_avail_maint = s.rooms_not_avail_maint,
                t.Rooms_Not_Avail_Other = s.Rooms_Not_Avail_Other,
                t.departures = s.departures,
                t.cancellations = s.cancellations,
                t.no_shows = s.no_shows,
                t.room_revenue = s.room_revenue,
                t.food_revenue = s.food_revenue,
                t.total_revenue = s.total_revenue,
                t.file_metadata_id = s.file_metadata_id,
                t.SnapShot_DTTM = s.SnapShot_DTTM,
                t.last_updated_dttm = s.last_updated_dttm

                when not matched by target then
            insert (property_id, occupancy_dt, snapshot_dttm, business_day_end_dt, total_accom_capacity, rooms_sold, rooms_not_avail_maint,
                Rooms_Not_Avail_Other, arrivals, departures, cancellations, no_shows, room_revenue, food_revenue, total_revenue, file_metadata_id, month_id, year_id)
            values (s.property_id, s.occupancy_dt, s.snapshot_dttm, s.business_day_end_dt, s.total_accom_capacity, s.rooms_sold, s.rooms_not_avail_maint,
                s.Rooms_Not_Avail_Other, s.arrivals, s.departures, s.cancellations, s.no_shows, s.room_revenue, s.food_revenue, s.total_revenue, s.file_metadata_id, s.month_id, s.year_id);
            ) by odbc;
            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                %let error_codes = 921;
                %goto EXIT;
            %end;
        %end;
    
        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
            execute(rollback) by odbc;
            %let error_codes = 921;
            %goto EXIT;
        %end;

        execute (
            commit
            )by odbc;
    quit;

%EXIT:

    %if &error_codes =921 %then %do;
        %if &maxerrors. ne -1 %then %do;
            %ideas_util_inc_errors;

            %if &err_ct. < &maxerrors. %then %do;
                %ideas_util_report_errors(919, 'RDBMS transaction failed', '', &request_Id., &err_ct.)
            %end;
        %end;
        %else %do;
            %ideas_util_inc_errors;
            %ideas_util_report_errors(919, 'RDBMS transaction failed', '', &request_Id., &err_ct.)
        %end;

        %let etl_status = 1;
    %end;

    proc Sql noprint;
        select count(*) into: error_ct from &work_lib..errors;
    quit;

    %if "&GL_ABORT_FLAG" eq "1" or &syscc > 4 or &error_ct. > 0 %then %do;
        %let GL_ABORT_FLAG =1;
        %let etl_status = 1;
    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_type2_RDBMS_load_resumable;
