%macro ideas_pace_mkt_accom(rc=)/store;

  data &work_lib..mkt_accom_pace (keep=Source property_id Transaction_Type reservation_identifier mkt_seg_id accom_type_id occupancy_dt capture_dttm solds rooms_sold room_revenue food_revenue total_revenue arrivals departures cancellations no_shows );
		retain Sold_output Source Transaction_Type property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm solds rooms_sold room_revenue food_revenue total_revenue cancellations no_shows arrivals departures;
		format capture_dttm datetime20.;
		Format Source $20.;
		set &work_lib..Transactions_exploded;
		rooms_sold=0;
		arrivals=0;
		departures=0;
		cancellations=0;
		no_shows=0;
		Sold_output="N";

		if occupancy_dt >= &minimum_occupancy_dt. and occupancy_dt<departure_dt then
			do;
				if (individual_status in ("SS", "CO" ,"CI") ) or 
					( individual_status in ("XX","CX") and booking_dt<cancellation_dt ) or
					(individual_status in ("NS") and booking_dt<arrival_dt ) then
					do;
						if occupancy_dt lt &first_snapshot_dt. then
							do;
								if booking_dt+1>=&minimum_snapshot_dt. then
									capture_dttm=dhms(booking_dt+1,0,0,'03:00:00't);
								else capture_dttm=dhms(max(booking_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
							end;
						else capture_dttm=dhms(max(booking_dt+1,&first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't);
						Source="SOLD";
						rooms_sold=solds;
						room_revenue=(room_revenue*solds);
						total_revenue=(total_revenue*solds);

						if occupancy_dt=arrival_dt then
							arrivals=solds;
						else arrivals=0;
						output &work_lib..mkt_accom_pace;
						Sold_output="Y";
					end;

				if (individual_status in ("XX","CX" ) and cancellation_dt+1 <&first_snapshot_dt. ) then
					do;
						if occupancy_dt=arrival_dt and cancellation_dt le arrival_dt then
							cancellations=1;

						if (sold_output="Y"  OR Transaction_Type EQ "Group") and occupancy_dt=arrival_dt  then
							arrivals=-1;
						else arrivals=0;

						if sold_output="Y" OR Transaction_Type EQ "Group" then
							do;
								rooms_sold=-1;
								room_revenue=(room_revenue*-1);
								total_revenue=(total_revenue*-1);
							end;
						else
							do;
								rooms_sold=0;
								room_revenue=0;
								total_revenue=0;
							end;

						capture_dttm=dhms(max(cancellation_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						Source="SOLD-CANCEL";
						output &work_lib..mkt_accom_pace;
					end;

				if individual_status = 'NS' and arrival_dt+1 < &first_snapshot_dt. then
					do;
						if occupancy_dt=arrival_dt then
							no_shows=1;

						if sold_output="Y" and occupancy_dt=arrival_dt then
							arrivals=-1;
						else arrivals=0;

						if sold_output="Y" then
							do;
								rooms_sold=-1;
								room_revenue=(room_revenue*-1);
								total_revenue=(total_revenue*-1);
							end;
						else
							do;
								rooms_sold=0;
								room_revenue=0;
								total_revenue=0;
							end;

						capture_dttm=dhms(max(arrival_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						Source="SOLD-NOSHOW";

						if no_shows ne 0 or arrivals ne 0 or rooms_sold ne 0 then
							output &work_lib..mkt_accom_pace;
					end;
			end;

        if &IncludeDepartureAdjustment. eq 1 and occupancy_dt>=departure_dt and departure_dt>=&minimum_occupancy_dt. and
            ((individual_status in ("SS", "CO" ,"CI") ) or (individual_status in ("XX","CX") and booking_dt < cancellation_dt) or
            (individual_status in ("NS") and booking_dt<arrival_dt )) then
            do;
                room_revenue=(room_revenue*solds);
                food_revenue=(food_revenue*solds);
                total_revenue=(total_revenue*solds);
                rooms_sold=0;
                arrivals=0;
                cancellations=0;
                no_shows=0;

                if occupancy_dt lt &first_snapshot_dt. then
                    do;
                        if arrival_dt+1>=&minimum_snapshot_dt. then
                            capture_dttm=dhms(arrival_dt+1,0,0,'03:00:00't);
                        else capture_dttm=dhms(max(arrival_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
                    end;
                else capture_dttm=dhms(max(arrival_dt+1,&first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't);
                departures=0;
                Source="SOLDS-POST-DEPARTURES";
                output &work_lib..mkt_accom_pace;
            end;

		if occupancy_dt+1=departure_dt and departure_dt>=&minimum_occupancy_dt. and 
			((individual_status in ("SS", "CO" ,"CI") ) or (individual_status in ("XX","CX") and booking_dt < cancellation_dt) or
			(individual_status in ("NS") and booking_dt<arrival_dt )) then
			do;
				room_revenue=0;
				food_revenue=0;
				total_revenue=0;
				rooms_sold=0;
				arrivals=0;
				cancellations=0;
				no_shows=0;
				occupancy_dt=departure_dt;

				if occupancy_dt lt &first_snapshot_dt. then
					do;
						if booking_dt+1>=&minimum_snapshot_dt. then
							capture_dttm=dhms(booking_dt+1,0,0,'03:00:00't);
						else capture_dttm=dhms(max(booking_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
					end;
				else capture_dttm=dhms(max(booking_dt+1,&first_snapshot_dt.-&ProcessedNumberpaceDays.),0,0,'03:00:00't);
				departures=solds;
				Source="SOLDS-DEPARTURES";
				output &work_lib..mkt_accom_pace;
				departures=-1;

				if individual_status in ("XX","CX" ) and cancellation_dt > booking_dt and cancellation_dt+1 < &first_snapshot_dt. and transaction_type ne 'Group' then
					do;
						Source="LDCANCEL-DEPARTURES";
						capture_dttm=dhms(max(cancellation_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						output &work_lib..mkt_accom_pace;
					end;
				else if individual_status in ("NS") and departure_dt > booking_dt and arrival_dt+1 < &first_snapshot_dt. then
					do;
						Source="LDNOSHOW-DEPARTURES";
						capture_dttm=dhms(max(arrival_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
						output &work_lib..mkt_accom_pace;
					end;
			end;

			if occupancy_dt+1=departure_dt and departure_dt>=&minimum_occupancy_dt. and
			individual_status in ("XX","CX" ) and cancellation_dt <= booking_dt and cancellation_dt+1 < &first_snapshot_dt.
			and booking_dt < arrival_dt  and Transaction_Type EQ "Group" then do;
            					room_revenue=0;
                                food_revenue=0;
                                total_revenue=0;
                                rooms_sold=0;
                                arrivals=0;
                                cancellations=0;
                                no_shows=0;
                                departures=-1;
                                occupancy_dt=departure_dt;

            					Source="LDCANCEL-DEPARTURES";
            					capture_dttm=dhms(max(cancellation_dt+1,occupancy_dt-(&ProcessedNumberpaceDays.-1)),0,0,'03:00:00't);
            					output &work_lib..mkt_accom_pace;
           end;
	run;

	proc sort data =&work_lib..mkt_accom_pace;
		by property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm;
	run;

	data &work_lib..mkt_accom_inventory_temp;
		merge &work_lib..mkt_accom_Zeros (in=zeroin) &work_lib..mkt_accom_pace (in = soldin);
		by property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm;
		retain property_id capture_dttm occupancy_dt mkt_seg_id accom_type_id rooms_sold arrivals departures cancellations no_shows Room_Revenue food_revenue total_revenue;
		arrivals=sum(arrivals,0);
		departures=sum(departures,0);
		Room_Revenue=sum(Room_Revenue,0);
		food_revenue=sum(food_revenue,0);
		total_revenue=sum(total_revenue,0);
		cancellations=sum(cancellations,0);
		no_shows=sum(no_shows,0);
		rooms_sold=sum(rooms_sold,0);

		if occupancy_dt >=&minimum_occupancy_dt. then
			output &work_lib..mkt_accom_inventory_temp;

		proc sql;
		create table &work_lib..mkt_accom_inventory_temp1 as 
			select distinct property_id, mkt_seg_id, accom_type_id, occupancy_dt, capture_dttm , 
				sum(rooms_sold) as rooms_sold length=8 format=7., sum(arrivals) as arrivals length=8 format=7. , sum(departures) as departures length=8 format=7., 
				sum(cancellations) as cancellations length=8 format=7., sum(no_shows) as no_shows length=8 format=7., 
				round (sum(Room_Revenue),.01) as Room_Revenue length=8 format=11.2, round (sum(food_revenue),.01) as food_revenue length=8 format=11.2,
				round (sum(total_revenue),.01) as total_revenue length=8 format=11.2
			from &work_lib..mkt_accom_inventory_temp
				group property_id, mkt_seg_id, accom_type_id, occupancy_dt, capture_dttm 
					order by mkt_seg_id, accom_type_id, occupancy_dt, capture_dttm;
	quit;

	data &work_lib..mkt_accom_inventory (keep=property_id capture_dttm occupancy_dt mkt_seg_id accom_type_id rooms_sold
		arrivals departures cancellations no_shows Room_Revenue food_revenue total_revenue);
		retain property_id mkt_seg_id accom_type_id occupancy_dt capture_dttm rooms_sold room_revenue food_revenue total_revenue cancellations no_shows arrivals departures;
		retain save_rooms_sold 0;
		retain save_room_revenue 0;
		retain save_food_revenue 0;
		retain save_total_revenue 0;
		retain save_cancellations 0;
		retain save_no_shows 0;
		retain save_arrivals 0;
		retain save_departures 0;
		retain previous_rooms_sold 0;
		retain previous_room_revenue 0;
		retain previous_food_revenue 0;
		retain previous_total_revenue 0;
		retain previous_cancellations 0;
		retain previous_no_shows 0;
		retain previous_arrivals 0;
		retain previous_departures 0;
		format capture_dttm datetime20.;
		set &work_lib..mkt_accom_inventory_temp1 end=last;
		by mkt_seg_id accom_type_id occupancy_dt capture_dttm;

		if first.mkt_seg_id=1 or first.accom_type_id=1 or first.occupancy_dt=1 then
			do;
				save_rooms_sold=0;
				save_room_revenue=0;
				save_food_revenue=0;
				save_total_revenue=0;
				save_cancellations=0;
				save_no_shows=0;
				save_arrivals=0;
				save_departures=0;
				previous_rooms_sold=-10;
				previous_room_revenue=-10;
				previous_food_revenue=-10;
				previous_total_revenue=-10;
				previous_cancellations=-10;
				previous_no_shows=-10;
				previous_arrivals=-10;
				previous_departures=-10;
			end;

		save_rooms_sold=save_rooms_sold+rooms_sold;
		save_food_revenue=save_food_revenue+food_revenue;
		save_room_revenue=save_room_revenue+room_revenue;
		save_total_revenue=save_total_revenue+total_revenue;
		save_arrivals=save_arrivals+arrivals;
		save_cancellations=save_cancellations+cancellations;
		save_no_shows=save_no_shows+no_shows;
		save_departures=save_departures+departures;
		arrivals=save_arrivals;
		rooms_sold=save_rooms_sold;
		room_revenue=save_room_revenue;
		food_revenue=save_food_revenue;
		total_revenue=save_total_revenue;
		no_shows=save_no_shows;
		cancellations=save_cancellations;
		arrivals=save_arrivals;
		departures=save_departures;

		if (previous_arrivals ne save_arrivals or 
			previous_rooms_sold ne save_rooms_sold or 
			previous_room_revenue ne save_room_revenue or 
			previous_food_revenue ne save_food_revenue or 
			previous_total_revenue ne save_total_revenue or 
			previous_no_shows ne save_no_shows or 
			previous_cancellations ne save_cancellations or 
			previous_departures ne save_departures) then
			output &work_lib..mkt_accom_inventory;
		previous_rooms_sold=save_rooms_sold;
		previous_room_revenue=save_room_revenue;
		previous_food_revenue=save_food_revenue;
		previous_total_revenue=save_total_revenue;
		previous_no_shows=save_no_shows;
		previous_cancellations=save_cancellations;
		previous_arrivals=save_arrivals;
		previous_departures=save_departures;
	run;

	Proc sql;
		create table &work_lib..accom_pace as 
			select property_id, capture_dttm, occupancy_dt, accom_type_id, rooms_sold 
				from &work_lib..mkt_accom_pace 
					order by property_id, accom_type_id, occupancy_dt, capture_dttm;
	quit;

	data &work_lib..Accom_inventory_temp;
		merge &work_lib..Accom_Zeros (in=zeroin) &work_lib..accom_pace (in = soldin);
		by property_id accom_type_id occupancy_dt capture_dttm;
		retain property_id capture_dttm occupancy_dt accom_type_id rooms_sold;
		rooms_sold=sum(rooms_sold,0);

		if occupancy_dt >=&minimum_occupancy_dt. then
			output &work_lib..Accom_inventory_temp;

	proc sql;
		create table &work_lib..Accom_inventory_temp1 as 
			select distinct property_id, accom_type_id, occupancy_dt, capture_dttm , 
				sum(rooms_sold) as rooms_sold length=8
			from &work_lib..Accom_inventory_temp
				group by property_id, accom_type_id, occupancy_dt, capture_dttm 
					order by accom_type_id, occupancy_dt, capture_dttm;
	quit;

	data &work_lib..Accom_inventory_temp2 (keep=property_id capture_dttm occupancy_dt accom_type_id rooms_sold);
		retain property_id accom_type_id occupancy_dt capture_dttm rooms_sold;
		retain save_rooms_sold 0;
		retain previous_rooms_sold .;
		set &work_lib..Accom_inventory_temp1 end=last;
		by accom_type_id occupancy_dt capture_dttm;

		if first.accom_type_id=1 or first.occupancy_dt=1 then
			do;
				save_rooms_sold=0;
				previous_rooms_sold=-10;
			end;

		save_rooms_sold=save_rooms_sold+rooms_sold;
		rooms_sold=save_rooms_sold;

		if  previous_rooms_sold ne save_rooms_sold then
			output &work_lib..Accom_inventory_temp2;
		previous_rooms_sold=save_rooms_sold;
	run;

	proc Sql;
		create table &work_lib..Accom_inventory as 
			select a.property_id as property_id length=6, a.occupancy_dt, capture_dttm, a.accom_type_id,
				a.rooms_sold,   
				b.accom_capacity,     
				b.rooms_not_avail_maint, 
				b.rooms_not_avail_other
			from &work_lib..Accom_inventory_temp2 as a 
				inner join &work_lib..accom_capacity as b
					on a.accom_type_id=b.accom_type_id and a.occupancy_dt=b.occupancy_dt 
				order by a.accom_type_id, a.occupancy_dt, a.capture_dttm;
	quit;

	%if &syscc >4 %then
		%let return_code=200;
	%else %if %sysfunc(exist(&work_lib..mkt_accom_inventory)) and %ideas_util_nobs(&work_lib..mkt_accom_inventory) > 0 and
		%sysfunc(exist(&work_lib..accom_inventory)) and %ideas_util_nobs(&work_lib..accom_inventory) > 0 %then
		%let return_code=0;
	%else %let return_code=100;
%mend ideas_pace_mkt_accom;