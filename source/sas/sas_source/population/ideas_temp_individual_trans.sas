%macro ideas_individual_trans_temp(snapdt)/store;

 	
	%PUT Value of max offset  &MAX_LOS_HISTORY_OFFSET ;
	PROC SORT DATA=tenant.individual_trans(where=(Departure_DT ge (&snapdt - &MAX_LOS_HISTORY_OFFSET))) OUT=&work_lib..inter_individual_filtered; 
    BY Reservation_Identifier Arrival_DT; 
    RUN;

	data &work_lib..inter_individual_trans_temp (keep=Property_ID Reservation_Identifier Individual_Status Arrival_DT Departure_DT Accom_Type_ID Mkt_Seg_ID los Room_Revenue);
	set &work_lib..inter_individual_filtered;
	los=datdif(Arrival_DT, Departure_DT, 'act/act');

	IF "&multiUnitGnrEnabled" EQ "false" THEN DO;
		Reservation_Identifier_char = put( Reservation_Identifier, 13. );
		digits = length( left( trim( Reservation_Identifier_char ) ) );
		/* PUT digits=; */ 
		IF digits ge 12 THEN DO;
			Reservation_Identifier = STRIP(int( Reservation_Identifier / 1000 ));
		END;
		ELSE DO;
			Reservation_Identifier = STRIP(int( Reservation_Identifier / 100 ));
		END;
	END;

	run;

	
	DATA &work_lib..inter_individual_trans ;
    SET &work_lib..inter_individual_trans_temp;
    BY Reservation_Identifier;  
                
		Retain Arrival_DT_1 Departure_DT_1 Accom_Type_ID_1 Mkt_Seg_ID_1 los_1 Individual_Status_1; 

        IF first.Reservation_Identifier THEN Do;
			NoOfRooms=1;
			Revenue_sum=Room_Revenue;
			Arrival_DT_1=Arrival_DT;
			Departure_DT_1=Departure_DT;
			Accom_Type_ID_1=Accom_Type_ID;
			Mkt_Seg_ID_1=Mkt_Seg_ID;
			los_1=los;
			Individual_Status_1=Individual_Status;

        END;
		
		ElSE DO;
			Revenue_sum+Room_Revenue;
			NoOfRooms+1;
		END;

		 IF last.Reservation_Identifier THEN DO;
			Arrival_DT=Arrival_DT_1;
			Departure_DT=Departure_DT_1;
			Accom_Type_ID=Accom_Type_ID_1;
			Mkt_Seg_ID=Mkt_Seg_ID_1;
			los=los_1;
			Individual_Status=Individual_Status_1;
			Room_Revenue=Revenue_sum;
			DROP Arrival_DT_1 Departure_DT_1 Accom_Type_ID_1 Mkt_Seg_ID_1 los_1 Individual_Status_1 Revenue_sum;

			OUTPUT;
        END;
		
    RUN;
	



%mend ideas_individual_trans_temp;
