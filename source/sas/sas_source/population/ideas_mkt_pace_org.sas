%macro ideas_mkt_pace_org/store;
	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql noprint;
		select distinct mkt_seg_id into: list SEPARATED by '|' from tenant.mkt_seg where Property_ID EQ &property_id.
			order by mkt_seg_id;
	quit;

	%let ms_list = &list.;
	%let i = 1;

	%do %while (%scan(&ms_list., &i., |) ne);

		data _null_;
			call symputx ('ms',%scan(&ms_list, &i, |),'l');
		run;

		%let libdsn = rm_part.ma_org_&ms.;

		%if %sysfunc(exist(rm_partp.ma_org_&ms.)) eq 1 %then
			%do;

				proc Datasets library=rm_datap nolist noprint;
					copy in=rm_partp out=rm_part 
						CLONE 
						CONSTRAINT=YES 
						INDEX=YES;
					select Ma_org_&ms.;
				run;

				quit;

				proc sql noprint;
					create table &work_lib..ms_pace (drop= mkt_seg_id) as select * from &work_lib..org_mkt_accom_inventory_pace 
						where mkt_seg_id = &ms.;
				quit;

				proc append base=&libdsn. data=&work_lib..ms_pace force;
				run;

			%end;
		%else
			%do;

				proc sql;
					create table &libdsn. like rm_partp.ma_inv_pace_template;
					create table &work_lib..ms_pace (drop= mkt_seg_id) as select * from &work_lib..org_mkt_accom_inventory_pace 
						where mkt_seg_id = &ms.;
				quit;

				proc append base=&libdsn. data=&work_lib..ms_pace force;
				run;

			%end;

		%let i = %sysevalf(&i. + 1);
	%end;

	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_mkt_pace_org;