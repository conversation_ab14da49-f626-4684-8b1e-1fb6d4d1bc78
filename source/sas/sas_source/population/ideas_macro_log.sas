

%macro ideas_macro_log (start_end =, macroname =)/store;
	
	%if %upcase(%trim(&start_end)) eq START  %then %do;

        %PUT *********************************************************************;
		%PUT NOTE:ENTERING &macroname WITH SYSCC=&SYSCC;
		%PUT *********************************************************************;

        data &macroname (keep = start_time);
                                                 
			format start_time  datetime22.3;
			start_time=datetime();
	       run;
	
		
		
	%end;
	%else %if %upcase(%trim(&start_end)) eq END  %then %do;
	 	%if &SYSCC lt 5 %then
			%let Status_desc=SUCCESSFUL;
		%else %let Status_desc=FAILED;

		data &macroname(keep = macro_name macro_start_time macro_end_time macro_run_time_seconds
                                                 macro_status);
			format macro_start_time macro_end_time datetime22.3;
			length macro_name macro_status $ 32;
			set &macroname;
			macro_name="&macroname.";
			macro_start_time=start_time;
			macro_end_time=datetime();
			macro_run_time_seconds=round(macro_end_time-macro_start_time, .005);
			macro_status="&status_desc.";

		proc append base=performance
				data=&macroname force;
		run;

		%PUT *********************************************************************;
		%PUT NOTE:LEAVING &macroname WITH SYSCC=&SYSCC: - &status_desc;
		%PUT *********************************************************************;
		
	%end;
	%mend ideas_macro_log ;

