%macro ideas_mspace_rmv_hist_fin_org/store;
	
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=START, macroname=&macroname.);
    
	proc sql noprint;
		select distinct mkt_seg_id into: list SEPARATED by '|' from &work_lib..org_mkt_accom_inventory_pace;
	quit;

	%if (%symexist(list)) %then %do;
		%let ms_list = &list.;
		%let i = 1;

		%do %while (%scan(&ms_list., &i., |) ne);

			data _null_;
				call symputx ('ms',%scan(&ms_list, &i, |),'l');
			run;
           	%let dsn = Ma_org_&ms.;
			%let libdsn = rm_part.ma_org_&ms.;

			%if %sysfunc(exist(&libdsn)) eq 1 %then %do;

				proc datasets library=rm_part nolist;
					delete &dsn.;
					change &dsn._fin = &dsn.;
                run; 

			%end;

			%let i = %sysevalf(&i. + 1);
		%end;
	%end;
%let macroname=&SYSMACRONAME; 
	
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_mspace_rmv_hist_fin_org;

