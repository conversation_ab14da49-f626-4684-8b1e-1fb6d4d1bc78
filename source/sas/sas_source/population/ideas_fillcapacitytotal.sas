%macro ideas_fillcapacity_total (indns, capvar)/store;
	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sort data = &indsn.;
		by  occupancy_dt;
	run;

	data &indsn. (drop=save_capacity);
		set &indsn.;
		by   occupancy_dt;
		retain save_capacity;
		
		if _n_ =1 then
			save_capacity = .;

		if &capvar. ne . then
			save_capacity = &capvar.;
			if save_capacity=. then 
			&capvar=0;
           else
            &capvar.=save_capacity;  
		run;

	%let macroname=&SYSMACRONAME;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ideas_fillcapacity_total;
