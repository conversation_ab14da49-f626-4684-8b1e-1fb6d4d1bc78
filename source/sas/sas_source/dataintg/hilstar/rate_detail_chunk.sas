%macro rate_detail_chunk /store;

proc datasets nolist lib=&work_lib.;
     delete RatedetailChunk;
     append data = ratedetail
     out  = RatedetailChunk ;
quit;

%if (NOT %sysfunc(exist(&work_lib..RatedetailChunk_Temp))) %then %do;
	proc sql;
	      CREATE TABLE &work_lib..RatedetailChunk_Temp (
	                  accomtyp char(21),
	                  DetailStartDate num format=yymmdd10. informat=yymmdd10.,
	                  DetailEndDate num format=yymmdd10. informat=yymmdd10.,
	                  Sunday num format=12.2,
	                  Monday num format=12.2, 
	                  Tuesday num format=12.2,
	                  Wednesday num format=12.2,
	                  Thursday num format=12.2,
	                  Friday num format=12.2,
	                  Saturday num format=12.2,
	                  Name char(40),
	                  rtRtLvl char(1),
	                  recordseq num format=8.0,
	                  constraint prim_key_RateDetailChunkTemp PRIMARY KEY (Name,accomtyp,DetailStartDate,DetailEndDate)
	                  );
	quit;
%end;

%ideas_UTIL_TRUNCATE_TABLE(&work_lib..Ratedetail);

%put "resolveHiltonRawRateOverlapChunkSize value is " hiltonRawRateOverlapChunkSize;

%do %while(%ideas_util_nobs(&work_lib..RatedetailChunk) > 0);

      proc sql outobs=&hiltonRawRateOverlapChunkSize.;
           select distinct quote(Name, "'") into: ratecodes SEPARATED by ',' from &work_lib..RatedetailChunk;
            %put  &ratecodes;
      quit;
      	  
	  Proc Append base=&work_lib..RatedetailChunk_Temp data=&work_lib..RatedetailChunk (where= (Name in (&ratecodes))) force;

	  Run; 

      %last_one_wins;

      proc sql;
            delete * from &work_lib..RatedetailChunk where Name in (&ratecodes);
      quit;

        %ideas_UTIL_TRUNCATE_TABLE(&work_lib..RatedetailChunk_Temp);
%end;


%mend rate_detail_chunk;
