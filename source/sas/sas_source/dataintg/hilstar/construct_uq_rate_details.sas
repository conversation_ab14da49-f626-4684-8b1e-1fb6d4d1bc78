%macro construct_uq_rate_details /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct UQ Rate Details - Hilton - FLOW";
	 proc Sql;
            insert into &work_lib..RateDetail select distinct rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate, 
                rates.Sunday_1 as Sunday,rates.Monday_1 as Monday,rates.Tuesday_1 as Tuesday,rates.Wednesday_1 as Wednesday,rates.Thursday_1 
            as Thursday,rates.Friday_1 as Friday,rates.Saturday_1 as Saturday,
                case 
                    when "&rtlevel" like "LV%" then cats( "LV" , trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "RL" , trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "RT" , trim(rates.srpid))
                    when "&rtlevel" like "%_LV%" then cats( "LV" , trim(rates.srpid))
                    else "."
                end 
            as Name,rates.rtRtLvl as rtRtLvl,rates.recordseq as recordseq from hilstar.rates where rates.rtlvl >= 0 and rates.rtlvl <= 8;
        quit;
	
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	
%mend;
