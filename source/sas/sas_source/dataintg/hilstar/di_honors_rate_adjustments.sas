%macro di_honors_rate_adjustments(hilstar, prop_id, rat_client_id) /store;
%let macroname=&SYSMACRONAME;
 	%ideas_macro_log (start_end=START, macroname=&macroname.);

 	%if (%sysfunc(exist(hilstar.Hotel_Occupancy_Percentage))) %then %do;
        PROC SQL;
         DROP TABLE hilstar.Hotel_Occupancy_Percentage;
         DROP TABLE hilstar.Group_Stay_Level_Data;
         DROP TABLE hilstar.Stay_Level_Data;
         DROP TABLE hilstar.Honors_Srp_Solds;
         DROP TABLE hilstar.Honors_Srp_Occupancy_Percentage;
         DROP TABLE hilstar.Stay_Level_Adr;
         DROP TABLE hilstar.Final_Occupancy_Percentage;
         DROP TABLE hilstar.tempAdjustments;
         DROP TABLE hilstar.tempAdjustments1;
         DROP TABLE hilstar.Stay_Level_Adjustments;
         DROP TABLE hilstar.Stay_Level_Adj_Adr;
         DROP TABLE hilstar.tempAdjust;
        QUIT;
 	%end;

    /*Calculate Hotel level occupancy percentage*/
    PROC SQL;
        CREATE TABLE hilstar.Hotel_Occupancy_Percentage AS
            SELECT Cdate, Idate AS stay_date, NumRms, RmsSold, GrpConvAvl, ((RmsSold+GrpConvAvl)/NumRms* 100) AS Occupancy_Percent FORMAT 7.2
            FROM hilstar.hotel;
    QUIT;

    /*Group level solds and revenue from srprm for status A only, block - pickup = remaning_solds revenue = remaning_solds * rate */
    PROC SQL;
     CREATE TABLE hilstar.Group_Stay_Level_Data AS
        SELECT srprm.hotelid, srprm.srpid as srp, srprm.Idate AS stay_date, srprm.rmsauth-srprm.rmssold AS stay_date_nites,
                     (srprm.rmsauth-srprm.rmssold) * srprm.rate AS stay_date_revenue, srprm.rate AS rate
                    FROM hilstar.grpcvt
                    LEFT JOIN hilstar.srprm
                    ON grpcvt.hotelid=srprm.hotelid AND grpcvt.srpid=srprm.srpid AND grpcvt.idate=srprm.idate
                    AND grpcvt.status="A" AND grpcvt.SRPType ^="D";
    QUIT;

    /*Expand books data to stay level data for calculation of ADR and SRP level Occupancy percentage*/
    DATA hilstar.Stay_Level_Data(KEEP=hotelid srp stay_date stay_date_nites stay_date_revenue rate);
        SET hilstar.books;
        IF rescode NE 'D' AND rescode NE 'N' THEN DO;
            DO i = doa TO (dod-1);
            stay_date=i;
            stay_date_revenue= revenue/nites;
            stay_date_nites=1;
            OUTPUT;
            END;
        END;
        FORMAT stay_date yymmdd10.;
    RUN;

    /* Merge group stay level data into books stay level data */
    PROC APPEND BASE=hilstar.Stay_Level_Data DATA=hilstar.Group_Stay_Level_Data FORCE;
        RUN;

    PROC SQL;
       SELECT Ratchet_Property_ID into:rat_prop_id
         FROM tenant.Ratchet_Property
         WHERE Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;
    QUIT;

	/* Calculate Honors SRP Level Occupancy Percentage */
	PROC SQL;
         CREATE TABLE hilstar.Honors_Srp_Solds AS
             SELECT Stay_Level_Data.hotelid,stay_date,sum(stay_date_nites) as solds
             FROM hilstar.Stay_Level_Data
             WHERE srp in (SELECT Srp_Name from tenant.Honors_Srps WHERE Ratchet_Property_ID = &rat_prop_id)
             GROUP BY Stay_Level_Data.hotelid,stay_date;
	QUIT;

	PROC SQL;
	 CREATE TABLE hilstar.Honors_Srp_Occupancy_Percentage AS
         SELECT Honors_Srp_Solds.hotelid,stay_date,(solds/NumRms * 100) AS Srp_Occupancy_Percentage
         FROM hilstar.Honors_Srp_Solds
         INNER JOIN hilstar.hotel
         on Honors_Srp_Solds.hotelid = hotel.hotelid
         AND Honors_Srp_Solds.stay_date = hotel.Idate;
	QUIT;

    /*Calculate Stay level ADR */
    PROC SQL;
        CREATE TABLE hilstar.Stay_Level_Adr AS
            SELECT stay_date,(sum(stay_date_revenue)/sum(stay_date_nites)) AS adr
            FROM hilstar.Stay_Level_Data
            WHERE srp not in (SELECT Srp_Name from tenant.Honors_Srps WHERE Ratchet_Property_ID = &rat_prop_id) AND stay_date NE .
            GROUP BY stay_date;
    QUIT;

    PROC SORT DATA=hilstar.Hotel_Occupancy_Percentage;
        BY stay_date;

    PROC SORT DATA=hilstar.Honors_Srp_Occupancy_Percentage;
        BY stay_date;

    /*Merge both the tables to get the srp level and hotel level occupancy percentage in single table */
    DATA hilstar.Final_Occupancy_Percentage;
        MERGE hilstar.Hotel_Occupancy_Percentage hilstar.Honors_Srp_Occupancy_Percentage;
        BY stay_date;
    RUN;

    PROC SQL;
     CREATE TABLE hilstar.tempAdjustments AS
          SELECT "&prop_id" AS hotelid, Low_Hhnsrr_Occupancy_Percent,High_Hhnsrr_Occupancy_Percent,Low_Hotel_Occupancy_Percent,High_Hotel_Occupancy_Percent
            FROM tenant.Honors_Srps_Adjustments;
    QUIT;

    PROC SQL;
     CREATE TABLE  hilstar.tempAdjustments1 AS
      SELECT a.*,Low_Hhnsrr_Occupancy_Percent,High_Hhnsrr_Occupancy_Percent,Low_Hotel_Occupancy_Percent,High_Hotel_Occupancy_Percent
        FROM hilstar.Final_Occupancy_Percentage as a
        JOIN hilstar.tempAdjustments as b
        on a.hotelid = b.hotelid
        where Low_Hotel_Occupancy_Percent NE .;
    QUIT;

    /*Find the Adjustment bucket based on the Srp and Hotel Occupancy Percentage*/
    DATA hilstar.Stay_Level_Adjustments (KEEP = hotelid stay_date NumRms RmsSold GrpConvAvl Occupancy_Percent Srp_Occupancy_Percentage Adjustment_Percentage);
        SET hilstar.tempAdjustments1;
            hotelid = hotelid;
            stay_date = stay_date;
            NumRms = NumRms;
            RmsSold = RmsSold;
            GrpConvAvl = GrpConvAvl;
            Occupancy_Percent = Occupancy_Percent;
            Srp_Occupancy_Percentage = Srp_Occupancy_Percentage;
            IF Srp_Occupancy_Percentage GE Low_Hhnsrr_Occupancy_Percent AND Srp_Occupancy_Percentage LE High_Hhnsrr_Occupancy_Percent
            AND Occupancy_Percent LE 90 THEN DO;
                Adjustment_Percentage = Low_Hotel_Occupancy_Percent;
                    OUTPUT;
            END;
            ELSE IF Srp_Occupancy_Percentage GE Low_Hhnsrr_Occupancy_Percent AND Srp_Occupancy_Percentage LE High_Hhnsrr_Occupancy_Percent
            AND Occupancy_Percent GT 90 THEN DO;
                Adjustment_Percentage = High_Hotel_Occupancy_Percent;
                    OUTPUT;
            END;
    RUN;

    PROC SQL;
     CREATE TABLE hilstar.Stay_Level_Adj_Adr AS
         SELECT hotelid,sla.stay_date,Adjustment_Percentage,adr,adr * (Adjustment_Percentage / 100) AS Rate
         FROM hilstar.Stay_Level_Adjustments AS sla
         INNER JOIN hilstar.Stay_Level_Adr AS slr
         ON sla.stay_date = slr.stay_date;
    QUIT;

    PROC SQL;
    CREATE TABLE hilstar.tempAdjust AS
        SELECT bks.hotelid,resid,srp,stay_date,slaa.Rate AS newRate,slaa.Rate * nites AS adjustRevenue
        FROM hilstar.Stay_Level_Adj_Adr AS slaa
        INNER JOIN hilstar.books AS bks
        ON slaa.stay_date = bks.doa
        WHERE bks.srp in (SELECT Srp_Name from tenant.Honors_Srps WHERE Ratchet_Property_ID = &rat_prop_id);
    QUIT;

    PROC SORT DATA=hilstar.tempAdjust;
            BY hotelid resid;

    data hilstar.books;
        modify hilstar.books hilstar.tempAdjust;
        BY hotelid resid;
            if _iorc_=0 then do;
                rate = newRate;
                revenue = adjustRevenue;
                replace;
            end;
            else do;
                %put "NO match found";
            end;
          _iorc_=0;
          _error_=0;
        run;

 %ideas_macro_log (start_end=END, macroname=&macroname.);

%mend di_honors_rate_adjustments;