%macro di_hs_update_submcat_mapping(hilstar, prop_id, usesubmcatmapping , defaultmarketsegment, isfullpush,rat_client_id) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    *PS use usablemktseg field to start with as mktseg values;
    *PS 03-Jul added to where clause to ignore default submcat mappings to take care of history / future sub_mcat rightly below;
    proc Sql;
        update hilstar.books set usablemktseg=mktseg where isdifferential in (3,4);
		 select Ratchet_Property_ID into:Ratchet_Property_ID from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;
    quit;

    proc Sql;
	
        create table &work_lib..smdefaults as 
            select mktcode, Default_History_Submcat, Default_Future_Submcat from 
                Tenant.Default_Mcat_Mapping left join Tenant.Ratchet_client
                on Default_Mcat_Mapping.Ratchet_client_ID=Ratchet_client.Ratchet_client_ID
            where Ratchet_client_code= "&client_code.";
        create table &work_lib..mcatsrpattributes as 
            select a.srpid, a.sub_mcat, a.status, a.attributed_start_dt, a.attributed_end_dt, a.mktcode, ratchet_property_id from 
                tenant.Ratchet_Srp_Attributes as a 
            where a.ratchet_property_id=&Ratchet_Property_ID 
                order by a.srpid, a.sub_mcat;

       create table &work_lib..smbookstemp as 
            select a.hotelid, a.resid, a.srp, b.trxChangeDt,a.mktseg from 
                hilstar.books as a left join hilstar.bookdates as b on a.resid=b.resid and a.hotelid=b.hotelid
            where a.hotelid="&prop_id" and mktseg ^="&defaultmarketsegment" and 
                ( a.isdifferential=3 or a.isdifferential=4) and trim(a.srp) ^= "";

         create table &work_lib..smbooks as 
            select a.hotelid, a.resid, a.srp, b.sub_mcat , a.mktseg, b.mktcode from 
                &work_lib..smbookstemp as a inner join &work_lib..mcatsrpattributes as b on a.srp=b.srpid and a.mktseg=b.mktcode
            where trim(b.sub_mcat) ^= "" and  a.trxChangeDt between b.attributed_start_dt and b.attributed_end_dt and 
                b.sub_mcat not in (select Default_Future_Submcat from &work_lib..smdefaults)               
            order by hotelid, resid;
     quit;
 
    data hilstar.books;
        set &work_lib..smbooks;
        modify hilstar.books key=prim_key_books;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if "&usesubmcatmapping" EQ 'TRUE' then do;
                    usablemktseg = sub_mcat;
                    put "submcat : replaced usablemktseg for " resid sub_mcat;
                end;
                else do;
                    mktseg = sub_mcat;
                    put "submcat : replaced mktseg for " resid sub_mcat;
                end;

                isdifferential=1;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records during Sub MCAT mapping" resid;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    /*Default mcat handling*/

    /*PS 10 May - TTRS-2698 Assign reservations without SRP but having submcat category mcat to default submcat for that mcat
    removing the where clause and trim(books.srp) ^= "" */
    proc Sql;
        create table &work_lib..smdefbooks as 
            select books.hotelid, books.resid, 
                case 
                    when "&isfullpush."="1" AND trxChangeDt < CapEdDate then Default_History_Submcat
                    else Default_Future_Submcat 
                end
            as sub_mcat
                from 
                    ((hilstar.books left join hilstar.bookdates on books.resid=bookdates.resid and books.hotelid=bookdates.hotelid)
                left join
                    hilstar.hottr on books.hotelid=hottr.hotelid)
                inner join 
                    &work_lib..smdefaults on books.mktseg=smdefaults.mktcode
                where 
                    isdifferential in (3, 4) and mktseg ^="&defaultmarketsegment"
                order by hotelid, resid;
    quit;

    data hilstar.books;
        set &work_lib..smdefbooks;
        modify hilstar.books key=prim_key_books;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if "&usesubmcatmapping" EQ 'TRUE' then do;
                    usablemktseg = sub_mcat;
                    put "default submcat : replaced usablemktseg for " resid sub_mcat;
                end;
                else do;
                    mktseg = sub_mcat;
                    put "default submcat : replaced mktseg for " resid sub_mcat;
                end;

                isdifferential=1;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records during Sub MCAT mapping" resid;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;
        /*Now doing Groups*/
        proc Sql;
            create table &work_lib..smgroups as
                select a.hotelid, a.idate, a.srpid, a.mktcode, b.sub_mcat from
                    hilstar.grpcvt as a inner join &work_lib..mcatsrpattributes as b on a.srpid=b.srpid
                where trim(a.hotelid)="&prop_id" and trim(b.sub_mcat) ^= "" and
                    a.cdate between b.attributed_start_dt and b.attributed_end_dt
                order by a.hotelid, a.Idate, a.srpid;
        quit;

    data hilstar.grpcvt;
        set &work_lib..smgroups;
        modify hilstar.grpcvt key=prim_key_grpcvt;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                OrgMktCode = mktcode;
                mktcode = sub_mcat;
                put "replaced Grpcvt Sub MCAT mapping" srpid mktcode OrgMktCode;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records during Sub MCAT mapping";
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    /*Default group mcat handling*/
    proc Sql;
        create table &work_lib..smdefgroups as 
            select grpcvt.hotelid, grpcvt.Idate, grpcvt.srpid, grpcvt.mktcode, Default_Future_Submcat as sub_mcat
                from 
                    hilstar.grpcvt inner join &work_lib..smdefaults on grpcvt.mktcode=smdefaults.mktcode 
                order by hotelid, Idate, SRPID;
    quit;

    data hilstar.grpcvt;
        set &work_lib..smdefgroups;
        modify hilstar.grpcvt key=prim_key_grpcvt;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                OrgMktCode = mktcode;
                mktcode = sub_mcat;
                put "Default Grpcvt Sub MCAT mapping" srpid sub_mcat;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records in grpcvt during Sub MCAT mapping";
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_update_submcat_mapping;
