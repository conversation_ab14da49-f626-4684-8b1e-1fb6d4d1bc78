%macro di_hs_exportcnrrates(dirpathForReport,prop_id,client_code)/store;
	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %let psvFilePathName =%str(&dirpathForReport.\&client_code._&prop_id._RATEPROTECT_RATES.psv);
    %put str = &psvFilePathName;

    data _null_;
        fname="tempfile";
        rc=filename(fname,"&psvFilePathName");
        if rc = 0 and fexist(fname) then
            rc=fdelete(fname);
        rc=filename(fname);
    run;

    %if %symexist(fedRoomsSRPEvalutionEnabled) and %upcase(&fedRoomsSRPEvalutionEnabled.) eq TRUE %then
        %do;
            %let isFedRoomsSRPEvalutionEnabled = 1;
        %end;
    %else
    	%do;
    		%let isFedRoomsSRPEvalutionEnabled = 0;
    	%end;

    %if %sysfunc(exist(hilstar.srpcnt)) and %ideas_util_nobs(hilstar.srpcnt) gt 0 and %sysfunc(exist(hilstar.rates)) and %ideas_util_nobs(hilstar.rates) gt 0 %then
    %do;
        proc sql;
            create table &work_lib..cnrrates as
            select
                srpcnt.srpid,rates.roomType, rates.startDate, rates.endDate,
                rates.Sunday_1, rates.Monday_1, rates.Tuesday_1, rates.Wednesday_1,
                rates.Thursday_1, rates.Friday_1, rates.Saturday_1, rates.recordseq
            from hilstar.srpcnt srpcnt
            join hilstar.rates rates
            on srpcnt.srpid = rates.srpid
            where srpcnt.days EQ 0
            and srpcnt.SRPType NE 'D'
            and srpcnt.Status EQ 'A'
            and srpcnt.BarSrp EQ 'N'
            and srpcnt.MktCode EQ 'CNR'
            union
            select
                srpcnt.srpid, rates.roomType, rates.startDate, rates.endDate,
                rates.Sunday_1, rates.Monday_1, rates.Tuesday_1, rates.Wednesday_1,
                rates.Thursday_1, rates.Friday_1, rates.Saturday_1, rates.recordseq
            from hilstar.srpcnt srpcnt
            join hilstar.rates rates
            on srpcnt.srpid = rates.srpid
            where &isFedRoomsSRPEvalutionEnabled. = 1
            and srpcnt.days EQ 0
            and srpcnt.SRPType NE 'D' /* not day use */
            and srpcnt.Status EQ 'A' /* only active */
            and srpcnt.BarSrp EQ 'N' /* not bar rate */
            and srpcnt.srpid IN (select fedRateProductId from &lib..FedRateProductsIdList) order by 1,2,12;
        quit;
        %if %sysfunc(exist(&work_lib..cnrrates)) and %ideas_util_nobs(&work_lib..cnrrates) gt 0 %then
        %do;
            proc export
                data=&work_lib..cnrrates
                outfile="&psvFilePathName"
                dbms=csv replace;
                delimiter='|';
                putnames=no;
            run;
        %end;
    %end;
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_exportcnrrates;
