%macro split_trx_chng(defaultmarketsegment) /store;
	proc sql;
	                create table &work_lib..yield_split_trx_chng  as 
			select cresid , max(date_stamp) as max_dt format date7.,1 as forceIt  from 
	                                &work_lib..yield_split 
	                                where rescode ne 'D' and (new_srp ne old_srp or new_mkt_code ne old_mkt_code or 
	                                ( new_level ne old_level and (new_mkt_code = "BAR" or new_mkt_code = "&defaultmarketsegment" )) 
	                                ) 
	                                group by cresid order by cresid;
	
quit;

%mend;
