%macro last_one_wins /store;
  %let macroname=&SYSMACRONAME;

  %ideas_macro_log (start_end=START, macroname=&macroname.);
  %put "Hilstar flow last one wins";
   /* Last one wins */
 %if (NOT %sysfunc(exist(&work_lib..RateDetail_expo))) %then %do;
  proc Sql;
	 CREATE TABLE &work_lib..RateDetail_expo (
				accomtyp char(21),
				occupancyDt num format=yymmdd10. informat=yymmdd10.,
				Sunday num format=12.2,
				Monday num format=12.2, 
				Tuesday num format=12.2,
				Wednesday num format=12.2,
				Thursday num format=12.2,
				Friday num format=12.2,
				Saturday num format=12.2,
				Name char(40),
				rtRtLvl char(1),
				recordseq num format=8.0,
				constraint prim_key_RateDetail_expo PRIMARY KEY (Name,accomtyp,occupancyDt,recordseq)
				);
	quit;
 %end;
 
 %if (NOT %sysfunc(exist(&work_lib..RateDetail_Single))) %then %do;
  proc Sql;
	CREATE TABLE &work_lib..RateDetail_Single (
				accomtyp char(21),
				occupancyDt num format=yymmdd10. informat=yymmdd10.,
				Sunday num format=12.2,
				Monday num format=12.2, 
				Tuesday num format=12.2,
				Wednesday num format=12.2,
				Thursday num format=12.2,
				Friday num format=12.2,
				Saturday num format=12.2,
				Name char(40),
				rtRtLvl char(1),
				recordseq num format=8.0,
				constraint prim_key_RateDetail_Single PRIMARY KEY (Name,accomtyp,occupancyDt,recordseq)
				);
	quit;
 %end;
 
  data &work_lib..RateDetail_expo (keep=accomtyp occupancyDt  Sunday Monday Tuesday Wednesday Thursday Friday Saturday Name rtRtLvl recordseq);
        set &work_lib..RatedetailChunk_Temp;
	if(DetailEndDate > &tempEndDate.) then do;
			DetailEndDate = &tempEndDate.;
	end;
	if(DetailStartDate <= DetailEndDate) then do;
		do occupancyDt = DetailStartDate to DetailEndDate;
			output;
		end;	
	end;
  run;

proc Sort data= &work_lib..RateDetail_expo;
        by name accomtyp occupancyDt recordseq; 
quit;

data &work_lib..RateDetail_Single (keep = accomtyp occupancyDt  Sunday Monday Tuesday Wednesday Thursday Friday Saturday Name rtRtLvl recordseq);
  set &work_lib..RateDetail_expo end = eof;
  retain accomtyp_0 occupancyDt_0  Sunday_0 Monday_0 Tuesday_0 Wednesday_0 Thursday_0 Friday_0 Saturday_0 Name_0 recordseq_0
		 accomtyp_1 occupancyDt_1	Sunday_1 Monday_1 Tuesday_1 Wednesday_1 Thursday_1 Friday_1 Saturday_1 Name_1 recordseq_1;

   format occupancyDt yymmdd10.;
   if _n_ = 1 then do;
   	accomtyp_0 = accomtyp;
	occupancyDt_0 = occupancyDt;
	Sunday_0 = Sunday;
	Monday_0 = Monday;
	Tuesday_0 = Tuesday;
	Wednesday_0 = Wednesday;
	Thursday_0 = Thursday;
	Friday_0 = Friday;
	Saturday_0 = Saturday; 
	Name_0 = Name;
	recordseq_0 = recordseq;
   end;
   if _n_ > 1 then do;
    	if occupancyDt NE occupancyDt_0 then do;
			occupancyDt_1 = occupancyDt;
			accomtyp_1 = accomtyp;
		 	Sunday_1 = Sunday;
	   		Monday_1 = Monday;
	   		Tuesday_1 = Tuesday;
	   		Wednesday_1 = Wednesday;
	   		Thursday_1 = Thursday;
	   		Friday_1 = Friday;
	   		Saturday_1 = Saturday;
			Name_1 = Name;
			recordseq_1 = recordseq;

			occupancyDt = occupancyDt_0;
			accomtyp = accomtyp_0;
			Sunday = Sunday_0;
	   	  	Monday = Monday_0;
	      	Tuesday = Tuesday_0;
	      	Wednesday = Wednesday_0;
	      	Thursday = Thursday_0;
	      	Friday = Friday_0;
	      	Saturday = Saturday_0;
			Name = Name_0;
			recordseq = recordseq_0;
		 	output;
			occupancyDt_0 = occupancyDt_1;
			accomtyp_0 = accomtyp_1;
		 	Sunday_0 = Sunday_1;
	   		Monday_0 = Monday_1;
	   		Tuesday_0 = Tuesday_1;
	   		Wednesday_0 = Wednesday_1;
	   		Thursday_0 = Thursday_1;
	   		Friday_0 = Friday_1;
	   		Saturday_0 = Saturday_1;
			Name_0 = Name_1;
			recordseq_0 = recordseq_1;
		end;
		else do;
		 	occupancyDt_0 = occupancyDt;
		 	accomtyp_0 = accomtyp;
		 	Sunday_0 = Sunday;
	   		Monday_0 = Monday;
	   		Tuesday_0 = Tuesday;
	   		Wednesday_0 = Wednesday;
	   		Thursday_0 = Thursday;
	   		Friday_0 = Friday;
	   		Saturday_0 = Saturday;
			Name_0 = Name;
			recordseq_0 = recordseq;
		 end;
		 if eof then do;
		 	output;
		 end;
   end; 
run;

data &work_lib..RatedetailChunk_Temp(Keep = accomtyp DetailStartDate DetailEndDate Sunday Monday Tuesday Wednesday Thursday Friday Saturday Name rtRtLvl recordseq);
 	set &work_lib..RateDetail_Single end = eof;
	retain DetailStartDate DetailEndDate  accomtyp_0 occupancyDt_0  Sunday_0 Monday_0 Tuesday_0 Wednesday_0 Thursday_0 Friday_0 Saturday_0 Name_0 recordseq_0
										accomtyp_1 occupancyDt_1	Sunday_1 Monday_1 Tuesday_1 Wednesday_1 Thursday_1 Friday_1 Saturday_1 Name_1 recordseq_1;
	format DetailStartDate yymmdd10.;
	format DetailEndDate yymmdd10.;
	if _n_= 1 then do;
	 DetailStartDate = occupancyDt;
	 occupancyDt_0 = occupancyDt;
	 accomtyp_0 = accomtyp;
	 Sunday_0 = Sunday;
	 Monday_0 = Monday;
	 Tuesday_0 = Tuesday;
	 Wednesday_0 = Wednesday;
	 Thursday_0 = Thursday;
	 Friday_0 = Friday;
	 Saturday_0 = Saturday; 
	 Name_0 = Name;
	 recordseq_0 = recordseq;
	end;
	if _n_ > 1 then do;
		if accomtyp_0 NE accomtyp OR Sunday NE Sunday_0 OR Monday_0 NE Monday OR Tuesday_0 NE Tuesday
		 OR Wednesday_0 NE Wednesday OR Thursday_0 NE Thursday OR Friday_0 NE Friday OR Saturday_0 NE Saturday OR Name_0 NE Name then do;
			DetailEndDate = occupancyDt_0;
			
			occupancyDt_1 = occupancyDt;
			accomtyp_1 = accomtyp;
		 	Sunday_1 = Sunday;
	   		Monday_1 = Monday;
	   		Tuesday_1 = Tuesday;
	   		Wednesday_1 = Wednesday;
	   		Thursday_1 = Thursday;
	   		Friday_1 = Friday;
	   		Saturday_1 = Saturday;
			Name_1 = Name;
			recordseq_1 = recordseq;
			
			accomtyp = accomtyp_0;
			Sunday = Sunday_0;
	   	  	Monday = Monday_0;
	      	Tuesday = Tuesday_0;
	      	Wednesday = Wednesday_0;
	      	Thursday = Thursday_0;
	      	Friday = Friday_0;
	      	Saturday = Saturday_0;
			Name = Name_0;
			recordseq = recordseq_0;
			output;
			DetailStartDate = occupancyDt_1;
			occupancyDt_0 = occupancyDt_1;
			accomtyp_0 = accomtyp_1;
		 	Sunday_0 = Sunday_1;
	   		Monday_0 = Monday_1;
	   		Tuesday_0 = Tuesday_1;
	   		Wednesday_0 = Wednesday_1;
	   		Thursday_0 = Thursday_1;
	   		Friday_0 = Friday_1;
	   		Saturday_0 = Saturday_1;
			Name_0 = Name_1;
			recordseq_0 = recordseq_1;
		end;
		else do;
		 occupancyDt_0 = occupancyDt;
	 	end;
	end;
	if eof then do;
			DetailEndDate = occupancyDt;
			accomtyp = accomtyp_1;
			Sunday = Sunday_1;
	   	  	Monday = Monday_1;
	      	Tuesday = Tuesday_1;
	      	Wednesday = Wednesday_1;
	      	Thursday = Thursday_1;
	      	Friday = Friday_1;
	      	Saturday = Saturday_1;
			Name = Name_1;
			recordseq = recordseq_1;
		output;
	end; 	
run;

Proc Append base=&work_lib..Ratedetail data=&work_lib..RatedetailChunk_Temp force;
 Run; 


	%ideas_UTIL_TRUNCATE_TABLE(&work_lib..RateDetail_expo);
	%ideas_UTIL_TRUNCATE_TABLE(&work_lib..RateDetail_Single);

  %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend last_one_wins;
