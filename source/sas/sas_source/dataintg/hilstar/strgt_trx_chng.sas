%macro strgt_trx_chng(defaultmarketsegment) /store;
		
		proc sql;
		                create table &work_lib..yield_strgt_trx_chng as 
				select gnr_num , max(date_stamp) as max_dt , 1 as forceIt from 
		                                &work_lib..yield_straight where rescode ne 'D' 
		                                and (new_srp ne old_srp or new_mkt_code ne old_mkt_code or 
		                                ( new_level ne old_level and (new_mkt_code = "BAR" or new_mkt_code = "&defaultmarketsegment" )) 
		                                )
		                                group by gnr_num order by gnr_num;
		
		quit;


%mend;