%macro construct_rate_detail /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct Rate Details - HILSTAR - FLOW";

proc Sql;
        insert into &work_lib..RateDetail 
			select rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate,
                rates.Sunday_1 as Sunday,rates.Monday_1 as Monday,rates.Tuesday_1 as Tuesday,rates.Wednesday_1 as Wednesday,rates.Thursday_1 
            	as Thursday,rates.Friday_1 as Friday,rates.Saturday_1 as Saturday, trim(rates.srpId) as Name,rates.rtRtLvl as rtRtLvl,rates.recordseq as recordseq
			from hilstar.rates 
			where rates.srpid in (select distinct name from &work_lib..RateHeader);

        insert into &work_lib..RateDetail 
			select rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate,
                rates.Sunday_1 as Sunday,rates.Monday_1 as Monday,rates.Tuesday_1 as Tuesday,rates.Wednesday_1 as Wednesday,rates.Thursday_1 
            	as Thursday,rates.Friday_1 as Friday,rates.Saturday_1 as Saturday,rates.srpId as Name,rtRtLvl as rtRtLvl,rates.recordseq as recordseq 
			from hilstar.rates 
			where rates.srpid in (select distinct name from &work_lib..temprateheader);
 quit;

 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
