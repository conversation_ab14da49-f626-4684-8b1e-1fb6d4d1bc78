%macro construct_rate_header(ispcrs,includeNonZeroLckLvlYSRP,basecurrency,rms_rates,rtlevel,yield_currency_code,semiyieldableRatesAsLinkedSRPs) /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%let semiyieldableRatesAsLinkedSRPs = %upcase(&semiyieldableRatesAsLinkedSRPs);
%put "Construct Rate Header - HILSTAR - FLOW";

            proc sql;
                CREATE TABLE &work_lib..semiyieldableheaders (
                    srpid char(21)
                );
            quit;

            %if &semiyieldableRatesAsLinkedSRPs = TRUE %then %do;
                %put "semiyieldableRatesAsLinkedSRPs is TRUE hence all the semi yieldable rates will be treated as derived SRPs";
                proc sql;
                    insert into &work_lib..semiyieldableheaders
                    select distinct srpcnt.srpid from hilstar.srpcnt where srpcnt.ControlType EQ 'S';
                quit;
            %end;

            %if %upcase(&ratesextracttype) eq QRATES %then %do;
               %put "ratesextracttype is QRATES hence all the rates starting with RL will be filtered out";
               proc sql;
                insert into &work_lib..semiyieldableheaders
                    select distinct srpcnt.srpid from hilstar.srpcnt where trim(srpcnt.SRPID) like "RL%";
              quit;
            %end;

	        %if &ispcrs = TRUE %then %do;
	            %put "inside ispcrs loop";

	           %if &includeNonZeroLckLvlYSRP eq FALSE %then %do;
	           %put "includeNonZeroLckLvlYSRP is set to FALSE.Hence considering only Lock Level 0 Yieldable SRPs";
	           proc Sql;
	                insert into &work_lib..RateHeader select distinct srpcnt.SRPID as Name, srpcnt.StrDate as StartDate, srpcnt.EndDate as EndDate,
	                    case 
	                        when trim(srpcnt.SRPCur)="" then "&basecurrency." 
	                        else trim(srpcnt.SRPCur) 
	                    end 
	                as Currency,
	                    case
	                        when trim(srpcnt.SRPID) like "LV%" then "Unqualified" 
	                        when trim(srpcnt.SRPID) like "RL%" then "Unqualified"
	                        else "Qualified" 
	                    end 
	                as Type,
	                    case
	                        when trim(srpcnt.Yield) = "Y" then "true"
	                        when trim(srpcnt.Yield) = "" then "false"
	                        else "false"
	                    end 
	                as Yieldable,
	                    case
	                        when srpcnt.rtlvl ^= 0 then "true" 
	                        else "false" 
	                    end 
	                as PriceRelative,
	                    case 
	                        when "&rtlevel" like "LV%" then cats( "LV" ,srpcnt.rtlvl)
	                        when "&rtlevel" like "RL%" then cats( "RL" ,srpcnt.rtlvl)
	                        when "&rtlevel" like "%_LV%" then cats( "LV" ,srpcnt.rtlvl)
	                        else "."
	                    end
	                as DerivedRateCode,
	                    case 
	                        when "&rms_rates" = "true" then
	                        "true" 
	                        else "false" 
	                    end 
	                as IncludesPackage, 
	                    "" as Remarks, trim(srpcnt.MktCode) as mktSegment from hilstar.srpcnt 
	                where srpcnt.status eq "A" and srpcnt.SRPType NE "D" and srpcnt.Days eq 0 and
	                    srpcnt.barsrp eq 'N' and trim(srpcnt.raterlv) not in ('VAR','ALL') and srpcnt.SRPLockLevel in (0,.)
	                    and srpcnt.srpid not in (select srpid from &work_lib..semiyieldableheaders);
	            quit;
	          %end;
	          %else %do;
	          	%put "includeNonZeroLckLvlYSRP is set to TRUE.Hence considering All Yieldable SRPs";
	          	 proc Sql;
				insert into &work_lib..RateHeader select distinct srpcnt.SRPID as Name, srpcnt.StrDate as StartDate, srpcnt.EndDate as EndDate,
				    case 
					when trim(srpcnt.SRPCur)="" then "&basecurrency." 
					else trim(srpcnt.SRPCur) 
				    end 
				as Currency,
				    case
					when trim(srpcnt.SRPID) like "LV%" then "Unqualified" 
					when trim(srpcnt.SRPID) like "RL%" then "Unqualified" 
					else "Qualified" 
				    end 
				as Type,
				    case
					when trim(srpcnt.Yield) = "Y" then "true"
					when trim(srpcnt.Yield) = "" then "false"
					else "false"
				    end 
				as Yieldable,
				    case
					when srpcnt.rtlvl ^= 0 then "true" 
					else "false" 
				    end 
				as PriceRelative,
				    case 
					when "&rtlevel" like "LV%" then cats( "LV" ,srpcnt.rtlvl)
					when "&rtlevel" like "RL%" then cats( "RL" ,srpcnt.rtlvl)
					when "&rtlevel" like "%_LV%" then cats( "LV" ,srpcnt.rtlvl)
					else "."
				    end
				as DerivedRateCode,
				    case 
					when "&rms_rates" = "true" then
					"true" 
					else "false" 
				    end 
				as IncludesPackage, 
				    "" as Remarks, trim(srpcnt.MktCode) as mktSegment from hilstar.srpcnt 
				where srpcnt.status eq "A" and srpcnt.SRPType NE "D" and srpcnt.Days eq 0 and 
				    srpcnt.barsrp eq 'N' and trim(srpcnt.raterlv) not in ('VAR','ALL')
				    and srpcnt.srpid not in (select srpid from &work_lib..semiyieldableheaders);
	          	quit;          
	          %end;
	        %end;
	        %else %do;
			
	            %if &includeNonZeroLckLvlYSRP. eq FALSE %then %do;
		               %put "includeNonZeroLckLvlYSRP is set to FALSE.Hence considering only Lock Level 0 Yieldable SRPs";
	       
			    proc Sql;
				insert into &work_lib..RateHeader select distinct srpcnt.SRPID as Name, srpcnt.StrDate as StartDate, srpcnt.EndDate as EndDate,
				    case 
					when trim(srpcnt.SRPCur)="" then "&basecurrency." 
					else trim(srpcnt.SRPCur) 
				    end 
				as Currency,
				    case
					when trim(srpcnt.SRPID) like "LV%" then "Unqualified" 
					when trim(srpcnt.SRPID) like "RL%" then "Unqualified" 
					else "Qualified" 
				    end 
				as Type,
				    case
					when trim(srpcnt.Yield) = "Y" then "true" 
					else "false" 
				    end 
				as Yieldable, 
				    case
					when srpcnt.rtlvl ^= 0 then "true" 
					else "false" 
				    end 
				as PriceRelative,
				    case 
					when "&rtlevel" like "LV%" then cats( "LV" ,srpcnt.rtlvl)
					when "&rtlevel" like "RL%" then cats( "RL" ,srpcnt.rtlvl)
					when "&rtlevel" like "%_LV%" then cats( "LV" ,srpcnt.rtlvl)
					else "." 
				    end
				as DerivedRateCode,
				    case 
					when "&rms_rates" = "true" then "true" 
					else "false" 
				    end 
				as IncludesPackage,
				    "" as Remarks, trim(srpcnt.MktCode) as mktSegment from hilstar.srpcnt 
				where srpcnt.status eq "A" and srpcnt.SRPType NE "D" and 
				    srpcnt.Days eq 0 and srpcnt.barsrp eq 'N' and trim(srpcnt.raterlv) not in ('VAR','ALL') and srpcnt.SRPLockLevel in (0,.)
				    and srpcnt.srpid not in (select srpid from &work_lib..semiyieldableheaders);
			    quit;
		     %end;
		     %else %do;
		     	   %put "includeNonZeroLckLvlYSRP is set to TRUE.Hence considering All Yieldable SRPs";
		     	   proc Sql;
				insert into &work_lib..RateHeader select distinct srpcnt.SRPID as Name, srpcnt.StrDate as StartDate, srpcnt.EndDate as EndDate,
				    case 
					when trim(srpcnt.SRPCur)="" then "&basecurrency." 
					else trim(srpcnt.SRPCur) 
				    end 
				as Currency,
				    case
					when trim(srpcnt.SRPID) like "LV%" then "Unqualified" 
					when trim(srpcnt.SRPID) like "RL%" then "Unqualified" 
					else "Qualified" 
				    end 
				as Type,
				    case
					when trim(srpcnt.Yield) = "Y" then "true" 
					else "false" 
				    end 
				as Yieldable, 
				    case
					when srpcnt.rtlvl ^= 0 then "true" 
					else "false" 
				    end 
				as PriceRelative,
				    case 
					when "&rtlevel" like "LV%" then cats( "LV" ,srpcnt.rtlvl)
					when "&rtlevel" like "RL%" then cats( "RL" ,srpcnt.rtlvl)
					when "&rtlevel" like "%_LV%" then cats( "LV" ,srpcnt.rtlvl)
					else "." 
				    end
				as DerivedRateCode,
				    case 
					when "&rms_rates" = "true" then "true" 
					else "false" 
				    end 
				as IncludesPackage,
				    "" as Remarks, trim(srpcnt.MktCode) as mktSegment from hilstar.srpcnt 
				where srpcnt.status eq "A" and srpcnt.SRPType NE "D" and 
				    srpcnt.Days eq 0 and srpcnt.barsrp eq 'N' and trim(srpcnt.raterlv) not in ('VAR','ALL')
				    and srpcnt.srpid not in (select srpid from &work_lib..semiyieldableheaders);
			    quit;	     
		     %end;
	
	        %end;

 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
