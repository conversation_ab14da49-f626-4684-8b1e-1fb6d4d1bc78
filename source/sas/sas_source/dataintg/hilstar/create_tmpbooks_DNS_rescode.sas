%macro create_tmpbooks_DNS_rescode(rmcostname,usesubmcatmapping,useextendedstaymapping,defaultmarketsegment,prop_id) /store;
           proc Sql;
            create table &work_lib..tmpbooks as 
                SELECT yield.hotelid as hotelid , oresid as resid, old_doa as doa , old_nites as nites, 
                    old_rooms as rooms, old_type as roomtype, 
                    old_srp as srp, old_mkt_code as mktseg, old_group as srpgroup, old_block as block, &rmcostname as revenue,0 as rate, 
                    <PERSON>urr as <PERSON><PERSON><PERSON> , rescode as rescode ,date_stamp as transactionDate,f_walked as walked ,old_level as level,old_gtdcode as bookingtype,old_nchild as nchild,old_nadults as nadults,
                    old_type as oldroomtype,
                case 
                    when "&usesubmcatmapping"="TRUE" AND ((trim(yield.new_srp) ^= "" OR yield.old_mkt_code = "&defaultmarketsegment" 
							OR yield.old_mkt_code = "BAR") AND (yield.new_srp NE yield.old_srp OR yield.new_mkt_code NE yield.old_mkt_code)) then 3
                    when "&useextendedstaymapping"="TRUE" AND (yield.new_level NE yield.old_level OR yield.new_mkt_code NE yield.old_mkt_code) AND (yield.new_mkt_code = "&defaultmarketsegment" 
							OR yield.new_mkt_code = "BAR") then 2
                    else 4
                end
	    			as isdifferential,f_noshow as noshow
                from hilstar.yield
                    WHERE rescode in('S','D','N') AND yield.hotelid="&prop_id."
                        order by yield.hotelid,resid,date_stamp,time_stamp;
        quit;
%mend;