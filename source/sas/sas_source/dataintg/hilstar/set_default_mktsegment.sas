%macro set_default_mktsegment(defaultmarketsegment,prop_id) /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "HILSTAR - FLOW";

	     proc Sql;
	        update hilstar.yield set new_mkt_code = "&defaultmarketsegment" WHERE trim(new_mkt_code) = "" AND new_level like "LV%" AND trim(new_group) = "" AND hotelid="&prop_id.";
	        update hilstar.yield set old_mkt_code = "&defaultmarketsegment" WHERE trim(old_mkt_code) = "" AND old_level like "LV%" AND trim(old_group) = "" AND hotelid="&prop_id.";
	    quit; 
	    
		%if &ispcrs = TRUE %then %do;
			proc sql;
				update hilstar.yield set new_mkt_code = "BAR" WHERE new_mkt_code = "B" AND old_mkt_code = "BAR" AND trans_type= "C" AND hotelid="&prop_id.";
			quit;
		%end;
	    
	       %let macroname=&SYSMACRONAME;
	         %ideas_macro_log (start_end=END, macroname=&macroname.);  	

	
%mend;