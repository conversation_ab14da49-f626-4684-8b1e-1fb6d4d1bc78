%macro di_hs_create_srp_transactions(rat_client_id)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc Sql noprint;
       	        select Ratchet_Property_ID into:rat_prop_id from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;
    quit;

    proc Sql noprint;
        select paramvalue into:booksempty from hilstar.switch where hotelid ="&prop_id" and paramname = 'isbooksempty';
    quit;

    %global isfirstload RatchetPropertyID;
    %let RatchetPropertyID = &rat_prop_id.;
    %let isfirstload = &booksempty;
    %put "isfirstload = " &isfirstload.;

    /* Changes for US3204 */
    proc Sql noprint;
        select capstdate into:capturedate from hilstar.hottr;
    quit;

    data _null_;
        snapshot_dt = input ("&capturedate", yymmdd10.);
        call symput('snapshot_dt', snapshot_dt);
        srp_end_dt = input ("&capturedate", yymmdd10.);
        srp_end_dt = intnx('day',srp_end_dt,-1);
        call symput('srp_end_dt', srp_end_dt);
    run;

    %if &isPrimingExtract EQ TRUE %then %do;

        proc Sql noprint;
            insert into tenant.Ratchet_Attributes_LastUpdated
                (Ratchet_Property_ID,Default_Attribution_Dt
                ,Attributes_Last_Update_Dt)
            VALUES
                (&RatchetPropertyID,&snapshot_dt
                ,&snapshot_dt);
        quit;

        %if &sqlrc ne 0 or &sqlobs=0 %then %do;
            %let syscc=6;
            %let error_str = %str( insert into Ratchet_Attributes_LastUpdated table failed);
            %put " insert into Ratchet_Attributes_LastUpdated table failed";

            %ideas_util_report_errors(1271, 'ERROR: insert into Ratchet_Attributes_LastUpdated table failed', '', &req_id, &err_ct.);

            /*%return;*/
        %end;
    %end;

    proc Sql noprint;
        select Default_Attribution_Dt into:start_attribution_date from tenant.Ratchet_Attributes_LastUpdated
            where Ratchet_Property_ID = &RatchetPropertyID;

        %if &sqlrc ne 0 or &sqlobs=0 %then %do;
            %let syscc=6;
            %let error_str = %str(SRP Attribution will not be processed: attribution start date not set in Ratchet_Attributes_LastUpdated table);
            %put "SRP Attribution will not be processed: attribution start date not set in Ratchet_Attributes_LastUpdated table";

            %ideas_util_report_errors(1271, 'ERROR:SRP Attribution will not be processed: attribution start date not set in Ratchet_Attributes_LastUpdated table', '', &req_id, &err_ct.);

            /*%return;*/
        %end;
    quit;

    /* Changes for US3204 */

    /* Moved the code up as it is required for the insert query
    proc Sql noprint;
    select capeddate into:capturedate from hilstar.hottr;
    quit;
    */
    data _null_;
        attributed_start_date = input ("&start_attribution_date", date9.);
        call symput('attributed_start_date', attributed_start_date);
    run;

    %put "RatchetPropertyID = " &RatchetPropertyID;
    %put "attributed_start_date = " &attributed_start_date;
    %put "snapshot dt = " &snapshot_dt;
    %put "capturedate = " &capturedate;
    %put "srp end dt = " &srp_end_dt;

    /* Changes for US3204 */
    %if &snapshot_dt GE &attributed_start_date and &attributed_start_date ne . %then %do;
        %put "SRP Attribution will be processed: snapshot date greater than attribution start date";

        proc Sql;
            create table &work_lib..tempcurrentsrpcnt1 as 
                select a.* , "N" as srp_included_in_srpcnt_grpcvt, "srpcnt" as srp_type 
                    from hilstar.srpcnt as a
                        where a.days=0 
                            order by a.srpid;
        quit;

        %if &sqlrc ne 0 %then %do;
            %ideas_util_report_errors(1256, 'ERROR:Error creating srpcvt data set: tempcurrentsrpcnt1', '', &req_id, &err_ct.);
            %return;
        %end;
        quit;

        proc Sql;
            create table &work_lib..temproomrates1 as 
                select distinct srpid, rtRtLvl 
                    from hilstar.rates as a
                        order by a.srpid;
        quit;

        %if &sqlrc ne 0 %then %do;
            %ideas_util_report_errors(1257, 'ERROR:Error creating room rates data set: temproomrates1', '', &req_id, &err_ct.);
            %return;
        %end;
        quit;

        data &work_lib..tempcurrentgrpcvt1;
            set hilstar.grpcvt;
            srp_included_in_srpcnt_grpcvt="N";
            srp_type="grpcvt";
            output &work_lib..tempcurrentgrpcvt1;

            %if &SYSNOBS=0 or &syserr > 0 %then %do;
                %ideas_util_report_errors(1258, 'ERROR:No observations in SRP attributes data set: tempcurrentgrpcvt1', '', &req_id, &err_ct.);
                %return;
            %end;

        proc Sort data=&work_lib..tempcurrentgrpcvt1 nodupkey;
            by srpid status;

        proc Sort data=&work_lib..tempcurrentgrpcvt1 nodupkey;
            by srpid;

        proc Sql;
            create table &work_lib..tempcurrentgrpcvt2 as 
                select a.*, b.rtRtLvl 
                    from &work_lib..tempcurrentgrpcvt1 as a 
                        left join &work_lib..temproomrates1 as b 
                            on a.srpid=b.srpid 
                        order by a.srpid;

            %if &sqlrc ne 0 %then %do;
                %ideas_util_report_errors(1259, 'ERROR:Error creating group cvt data set: tempcurrentgrpcvt2', '', &req_id, &err_ct.);
                %return;
            %end;
        quit;

        proc Sql;
            create table &work_lib..tempsrp_and_grp as 
                select a.srpid, a.rateconditional as grpcvt_rateconditional, "Y" as srp_included_in_srpcnt_grpcvt 
                    from &work_lib..tempcurrentgrpcvt2 as a 
                        inner join &work_lib..tempcurrentsrpcnt1 as b 
                            on a.srpid=b.srpid 
                        order by a.srpid;

            %if &sqlrc ne 0 %then %do;
                %ideas_util_report_errors(1260, 'ERROR:Error creating grpgvt and srpcnt data set: temp_srp_and_grp', '', &req_id, &err_ct.);
                %return;
            %end;
        quit;

        data &work_lib..tempcurrentgrpcvt3;
            merge &work_lib..tempcurrentgrpcvt2 (in=srpin) &work_lib..tempsrp_and_grp (in=grpin);
            by srpid;

            if (srpin=1 & grpin=1) or (srpin=1 & grpin=0 ) then
                output &work_lib..tempcurrentgrpcvt3;

            %if &syserr > 0 %then %do;
                %ideas_util_report_errors(1261, 'ERROR:Error creating SRP transaction data set: tempcurrentgrpcvt3', '', &req_id, &err_ct.);
                %return;
            %end;
        run;

        data &work_lib..tempcurrentsrpcnt2;
            merge &work_lib..tempcurrentsrpcnt1 (in=srpin) &work_lib..tempsrp_and_grp (in=grpin);
            by srpid;

            if (srpin=1 & grpin=1) or (srpin=1 & grpin=0 ) then
                output &work_lib..tempcurrentsrpcnt2;

            %if &syserr > 0 %then %do;
                %ideas_util_report_errors(1262, 'ERROR:Error creating SRP transaction data set: tempcurrentsrpcnt2', '', &req_id, &err_ct.);
                %return;
            %end;
        run;

        data &work_lib..tempsrpattributes1 (drop=status);
            set &work_lib..tempcurrentsrpcnt2 &work_lib..tempcurrentgrpcvt3;

        proc Sql;
            create table &work_lib..tempsrpattributes2 as 
                select a.*, b.is_mcat_plus_eligible, b.Default_History_Submcat, b.Default_Future_Submcat
                    from &work_lib..tempsrpattributes1 as a 
                        inner join &work_lib..mcatattributesdistinct as b 
                            on b.mktcode = a.mktcode 
                        order by a.srpid;

            %if &sqlrc ne 0 %then %do;
                %ideas_util_report_errors(1263, 'ERROR:Error merging SRP attributes with ratchet_property/mkt segment tables', '', &req_id, &err_ct.);
                %return;
            %end;
        quit;

        data &work_lib..tempsrpattributes3 (keep=Ratchet_Property_ID mktcode srpid qualified block linked fenced package 
            yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status is_mcat_plus_eligible Default_History_Submcat Default_Future_Submcat);
            retain Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined 
                status is_mcat_plus_eligible Default_History_Submcat Default_Future_Submcat;
            format attributed_start_dt date9.;
            format attributed_end_dt date9.;
            length mktcode $20.;
            length sub_mcat $20.;
            Length qualified block linked fenced package yieldable control_type $1.;
            length srpid $21.;
            length attributes_combined $10.;
            set &work_lib..tempsrpattributes2;

            /* attributed_start_dt=input (StrDate, yymmdd10.) ; */
            /* attributed_end_dt = input (EndDate, yymmdd10.) ; */
            /* SG: Added this logic to set the start date to 1980 in the past while loading the priming extract */
            if &isfirstload NE 1 then do;
                attributed_start_dt=&snapshot_dt;
            end;
            else attributed_start_dt ='01JAN1980'd;
            attributed_end_dt='31DEC2173'd;
            Ratchet_Property_ID=&RatchetPropertyID;
            status=1;
            sub_mcat=" ";
            mktcode=MktCode;

            if srp_type="srpcnt" or srp_type="grpcvt" then do;
                if upcase(RateConditional) EQ "Y" then
                    qualified="Y";
                else qualified="N";

                /*US4087 Changed the check from upcase(Yield) NE "N" then to if upcase(Yield) EQ "Y" then*/
                if upcase(Yield) EQ "Y" then
                    yieldable = "Y";
                else yieldable = "N";

                if upcase(yieldable)="Y" then do;
                    if upcase(ControlType) EQ "F" then
                        control_type = "F";
                    else if upcase(ControlType) EQ "S" then control_type = "S";
                    else control_type="N";
                end;
                else control_type="N";

                if upcase(fenced)="Y" then
                    fenced="Y";
                else fenced="N";

                if upcase(package)="Y" then

                    package="Y";
                else package="N";

                if srp_type="srpcnt" and upcase(BarSrp)="Y" and substr(upcase(RateRlv),1,2) NE "LV" then
                    linked="Y";
                else linked="N";
                if srp_type="srpcnt" and (upcase(grpcvt_rateconditional) EQ "Y" or (mktcode ne "GT" and mktcode ne "SMRF" and mktcode ne "CONV" and mktcode ne "CMTG")) and
                    srp_included_in_srpcnt_grpcvt="Y" then
                    block="Y";
                else block="N";

                if srp_type="grpcvt" then do;
                    if (upcase(RateConditional) EQ "Y" or (mktcode ne "GT" and mktcode ne "SMRF" and mktcode ne "CONV" and mktcode ne "CMTG")) and 
                        srp_included_in_srpcnt_grpcvt="N" then do;
                        block="Y";
                        yieldable = "N";
                        control_type="N";

                        if upcase(rtRtLvl) EQ "A" then
                            linked="Y";
                        else linked="N";
                    end;
                    else block="N";

                    /* duplicate record in grpcvt also on srpcvt. sprcvt is king */
                    if srp_included_in_srpcnt_grpcvt="Y" then
                        delete;
                end;
            end;

            attributes_combined=trim(qualified)||trim(block)||trim(linked)||trim(package)||trim(fenced)|| 
                trim(yieldable)||trim(control_type);
            output &work_lib..tempsrpattributes3;

            %if &SYSNOBS=0 or &syserr > 0 %then %do;
                %ideas_util_report_errors(1212, 'ERROR:PMS process data failed', '', &req_id, &err_ct.);
                %ideas_util_report_errors(1258, 'ERROR:No observations in SRP attributes data set: temp_srpattributes', '', &req_id, &err_ct.);
                %return;
            %end;

        proc Sort data=&work_lib..tempsrpattributes3;
            by mktcode attributes_combined;

        data &work_lib..srp_transactions (keep=Ratchet_Property_ID mktcode new_mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined new_attributes_combined status);
            retain Ratchet_Property_ID mktcode new_mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined new_attributes_combined status;
            merge &work_lib..tempsrpattributes3 (in=srpin) &work_lib..mcatattributes (in=mcatin);
            by mktcode attributes_combined;

            if srpin=1 & mcatin=1 then do;
                if sub_mcat=" " then
                    sub_mcat=mktcode;
                status=1;
                new_attributes_combined=attributes_combined;
                new_mktcode=mktcode;
                output &work_lib..srp_transactions;
            end;

            if srpin=1 & mcatin=0 then do;
                if is_mcat_plus_eligible= "Y" then do;

                    /* left in code if we need to change later */
                    /* Initial spec is to set all unmatched SRP's to default future */
                    if '&snapshot_dt' gt '&attributed_start_date' then
                        sub_mcat=Default_Future_Submcat;
                    else sub_mcat=Default_Future_Submcat;
                end;
                else sub_mcat=mktcode;
                status=1;
                new_attributes_combined=attributes_combined;
                new_mktcode=mktcode;
                output &work_lib..srp_transactions;
            end;
        run;

        %if &syserr > 0 %then %do;
            %ideas_util_report_errors(1264, 'ERROR:Error creating SRP transaction data set: srp_transactions', '', &req_id, &err_ct.);
            %return;
        %end;
        run;

        proc Sql;
            create table &work_lib..srp_system as 
                select a.*, a.status as curr_status, a.attributes_combined as curr_attributes_combined, a.mktcode as curr_mktcode, a.attributed_start_dt as curr_attributed_start_dt, a.attributed_end_dt as curr_attributed_end_dt  from tenant.Ratchet_Srp_Attributes as a 
                    where a.Ratchet_Property_ID = &RatchetPropertyID and (a.status=1 or a.attributed_end_dt > &srp_end_dt. )
                        order by a.Ratchet_Property_ID, a.srpid, attributes_combined, attributed_start_dt;

            %if &sqlrc ne 0 %then %do;
                %ideas_util_report_errors(1265, 'ERROR:Error creating SRP system(DB) data set: srp_system', '', &req_id, &err_ct.);
                %return;
            %end;
        quit;

		proc Sort data=&work_lib..srp_system;
            by Ratchet_Property_ID srpid attributes_combined attributed_start_dt;

        proc Sort data=&work_lib..srp_transactions;
            by Ratchet_Property_ID srpid attributes_combined attributed_start_dt;

        data &work_lib..srp_new_add_transactions 
                (keep=Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status) 
                &work_lib..srp_update_transactions 
                (keep=Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status)
                &work_lib..srp_update_add_transactions 
                (keep=Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status);
            retain Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status;
            merge &work_lib..srp_system (in=masterin) &work_lib..srp_transactions (in=srpin);
            by Ratchet_Property_ID srpid;

            if srpin=1 & masterin=0 then do;
                status=1;
                output &work_lib..srp_new_add_transactions;
            end;

            if srpin=1 & masterin=1 and (new_attributes_combined ne curr_attributes_combined or new_mktcode ne curr_mktcode) and "&snapshot_dt" gt curr_attributed_start_dt then do;
                status=curr_status;
                attributed_end_dt="&srp_end_dt";
                output &work_lib..srp_update_transactions;
                status=1;
                attributed_start_dt="&snapshot_dt";
                attributed_end_dt='31DEC2173'd;
                attributes_combined=new_attributes_combined;
                mktcode=new_mktcode;
                output &work_lib..srp_update_add_transactions;
            end;

            %if &syserr > 0 %then %do;
                %ideas_util_report_errors(1266, 'ERROR:Error creating SRP add and update transaction data sets:', '', &req_id, &err_ct.);
                %return;
            %end;
        run;

        data &work_lib..srp_add_transactions (keep=Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status);
            retain Ratchet_Property_ID mktcode sub_mcat srpid qualified block linked fenced package yieldable control_type attributed_start_dt attributed_end_dt attributes_combined status;
            set &work_lib..srp_new_add_transactions &work_lib..srp_update_add_transactions;

            %if &syserr > 0 %then %do;
                %ideas_util_report_errors(1269, 'ERROR:Error creating SRP transaction dataset: srp_add_transactions', '', &req_id, &err_ct.);
                %return;
            %end;
        run;

        data &work_lib..srp_last_update_transaction (keep=Ratchet_Property_ID Default_Attribution_Dt Attributes_Last_Update_Dt );
            retain Ratchet_Property_ID Default_Attribution_Dt Attributes_Last_Update_Dt Ratchet_Property_ID Default_Attribution_Dt Attributes_Last_Update_Dt;
            Ratchet_Property_ID=&RatchetPropertyID;
            Default_Attribution_Dt=&snapshot_dt;
            Attributes_Last_Update_Dt=&snapshot_dt;

            %if &syserr > 0 %then %do;
                %ideas_util_report_errors(1270, 'ERROR:Error creating SRP add transactions data set: srp_last_update_transaction', '', &req_id, &err_ct.);
                %return;
            %end;
        run;

    %end;
    %else %do;
        %put "SRP Attribution will not be processed: snapshot date is less than or equal attribution start date";
    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_create_srp_transactions;
