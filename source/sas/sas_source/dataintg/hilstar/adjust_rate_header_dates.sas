%macro adjust_rate_header_dates /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Adjust Rate Header dates - HILSTAR - FLOW";


        proc Sql;
            insert into &work_lib..tempdates 
                select temprateheader.Name, temprateheader.StartDate , temprateheader.EndDate , 
                    min(DetailStartDate)as DtlStartDate, max(DetailEndDate) as DtlEndDate 
                from &work_lib..RateDetail left join &work_lib..temprateheader on RateDetail.Name = temprateheader.Name
                    where RateDetail.Name in (select distinct Name from &work_lib..temprateheader)
                        group by temprateheader.Name, temprateheader.StartDate , temprateheader.EndDate;
        quit;

        data &work_lib..temprateheader;
            set &work_lib..tempdates;

            /*Chaitanya-Added-TTRS 2589*/
            /*Added the clause of key for matching*/
            modify &work_lib..temprateheader key=prim_key_temprateheader;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    StartDate=DtlStartDate;

                    if(EndDate>DtlEndDate) then do;
                        EndDate=DtlEndDate;
                    end;

                    replace;
                end;

                when(%sysrc(_dsenom)) do;
                    output;
                end;

                otherwise
                    do;
                        put "ERROR: Unexpected value for _IORC_= " _iorc_;
                        put "Program terminating. DATA step iteration # " _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        proc Sql;
            insert into &work_lib..srpcnttempdates 
                select rateheader.Name,rateheader.type, rateheader.StartDate , rateheader.EndDate , 
                    min(DetailStartDate)as DtlStartDate, max(DetailEndDate) as DtlEndDate 
                from &work_lib..RateDetail left join &work_lib..rateheader on RateDetail.Name = rateheader.Name
                    where RateDetail.Name in (select distinct Name from &work_lib..rateheader)
                        group by rateheader.Name,rateheader.type,rateheader.StartDate , rateheader.EndDate;
        quit;

        data &work_lib..rateheader;
            set &work_lib..srpcnttempdates;
            modify &work_lib..rateheader key=prim_key_rateheader;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    StartDate=DtlStartDate;

                    if(EndDate>DtlEndDate) then do;
                        EndDate=DtlEndDate;
                    end;

                    replace;
                end;

                when(%sysrc(_dsenom)) do;
                    output;
                end;

                otherwise
                    do;
                        put "ERROR: Unexpected value for _IORC_= " _iorc_;
                        put "Program terminating. DATA step iteration # " _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        proc Sql;
            insert into &work_lib..RateHeader select h.* from &work_lib..temprateheader as h;
        quit;

%let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	
%mend;
