%macro di_hs_update_srp_database(tenant_database, tenant_server, tenant_db_port, tenant_server_instance, tenant_user, tenant_password,isPrimingExtract)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    %if ( (%sysfunc(exist(&work_lib..srp_add_transactions))) and (%sysfunc(exist(&work_lib..srp_update_transactions))) and (%sysfunc(exist(&work_lib..srp_last_update_transaction))) ) %then %do;
        %local connect_str;
        %local tempload_id;
        %local load_id;
        %local srpadds;
        %local srpupds;
        %local lastupd;

        /*Generate a unique load id for the creation of the temp tables;*/
        %let tempload_id=%sysfunc(compress(&req_id.,' -'));
        %let load_id = %sysfunc(substr(&tempload_id.,1,5));
        %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&tenant_server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_password%str(;)database=&tenant_database%str(;)port=&tenant_db_port;
        %let connect_str=complete="&connect_str";

        /* %put _all_; */
        /* Books table is empty this is the first extract being processed so load the discontinued srp's submcat*/
        %put isPrimingExtract = &isPrimingExtract;

        %if &isPrimingExtract. EQ 1 %then %do;

            proc Sql;
                connect to odbc (&connect_str autocommit=no);
                execute(
                    UPDATE Ratchet_Srp_Attributes SET sub_mcat = B.Sub_Mkt_Seg_Name from Ratchet_Srp_Attributes AS A INNER JOIN Ratchet_Exploded_Mcat_Attributes AS B 
                        ON A.mktcode =B.Mkt_Seg_Name WHERE A.attributes_combined = B.Attributes_Combined AND A.Ratchet_Property_ID = &RatchetPropertyID;
                ) by odbc;
                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                    execute(rollback) by odbc;
                    disconnect from odbc;

                    %ideas_util_report_errors(1231, 'Error updating SRP database table: ratchet_srp_attributes ', '', &req_id, &err_ct.);
                    %return;
                %end;

                execute( 
                    update Ratchet_Srp_Attributes set sub_mcat = y.Default_History_Submcat from Ratchet_Srp_Attributes as x INNER JOIN Default_Mcat_Mapping as y
                        on x.mktcode = y.mktcode where x.sub_mcat='' AND x.Ratchet_Property_ID = &RatchetPropertyID;
                ) by odbc;
                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                    execute(rollback) by odbc;
                    disconnect from odbc;

                    %ideas_util_report_errors(1232, 'Error updating SRP data base table: ratchet_srp_attributes ', '', &req_id, &err_ct.);
                    %return;
                %end;
            quit;

        %end;

        %put "SRP Attribution update transactions will be processed";
        %if %upcase(&useRegularTableAsTempInSAS.)=TRUE %then
            %do;
                %let srpadds = srp_add_transactions_stg where ratchet_property_id=&rat_prop_id;
                %let srpupds = srp_update_transactions_stg where ratchet_property_id=&rat_prop_id;
                %let lastupd = srp_last_update_transaction_stg where ratchet_property_id=&rat_prop_id;
                %ideas_flush_temp_records(table_to_flush=srp_add_transactions_stg,rat_prop_id=&rat_prop_id,
                    connect_str=&connect_str);
                %ideas_trans_append(data_table=&work_lib..srp_add_transactions,trans_table=srp_add_transactions_stg,
                     trans_lib=tenant);

                %ideas_flush_temp_records(table_to_flush=srp_update_transactions_stg,rat_prop_id=&rat_prop_id,
                    connect_str=&connect_str);
                %ideas_trans_append(data_table=&work_lib..srp_update_transactions,trans_table=srp_update_transactions_stg,
                    trans_lib=tenant);

                %ideas_flush_temp_records(table_to_flush=srp_last_update_transaction_stg,rat_prop_id=&rat_prop_id,
                    connect_str=&connect_str);
                %ideas_trans_append(data_table=&work_lib..srp_last_update_transaction,trans_table=srp_last_update_transaction_stg,
                    trans_lib=tenant);
            %end;
        %else
            %do;
                %ideas_trans_upload_tmp(upload_table=&work_lib..srp_add_transactions,
                    _name_in_db=srpadds,
                    like_table=tenant.ratchet_srp_attributes(drop=Ratchet_Srp_Attributes_ID),
                    tmp_trans_lib=ten_tmp,cnt=1&load_id);
                %ideas_trans_upload_tmp(upload_table=&work_lib..srp_update_transactions,
                    _name_in_db=srpupds,
                    like_table=tenant.ratchet_srp_attributes(drop=Ratchet_Srp_Attributes_ID),
                    tmp_trans_lib=ten_tmp,cnt=2&load_id);
                %ideas_trans_upload_tmp(upload_table=&work_lib..srp_last_update_transaction,
                    _name_in_db=lastupd,
                    like_table=tenant.Ratchet_Attributes_Lastupdated(drop=Ratchet_Attributes_LastUpd_ID),
                    tmp_trans_lib=ten_tmp,cnt=3&load_id);
                %let srpadds = ##&srpadds;
                %let srpupds = ##&srpupds;
                %let lastupd = ##&lastupd;
            %end;

        %if &syscc > 4 %then %do;
            %ideas_util_report_errors(1230, 'Failed to update srp transactions into temp table', ''&req_id, &err_ct.)
            %Return;
        %end;

        proc Sql;
            connect to odbc (&connect_str autocommit=no);
            execute(
                with etldata as (select Ratchet_Property_ID ,srpid,mktcode,sub_mcat,qualified,block,linked,package,fenced,yieldable,control_type,attributed_start_dt, attributed_end_dt, attributes_combined, status
            from &srpupds.)

                merge ratchet_srp_attributes as a 
            using etldata as b

                on a.Ratchet_Property_ID=b.Ratchet_Property_ID and a.srpid=b.srpid and (a.status=b.status) 

                when matched and (a.qualified <> b.qualified
                or a.block <> b.block
                or a.linked <> b.linked 
                or a.package <> b.package
                or a.fenced <> b.fenced
                or a.yieldable <> b.yieldable
                or a.control_type <> b.control_type
                or a.mktcode <> b.mktcode
                ) then 

            update set a.status=2, a.attributed_end_dt=b.attributed_end_dt;
            ) by odbc;
            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                disconnect from odbc;

                %ideas_util_report_errors(1231, 'Error updating SRP database table: ratchet_srp_attributes ', '', &req_id, &err_ct.);
                %return;
            %end;

            execute( 
                insert into Ratchet_srp_attributes 
                    ( 
                    Ratchet_Property_ID,srpid,mktcode,sub_mcat,qualified,block,linked,package,fenced,yieldable,control_type,attributes_combined,attributed_start_dt,attributed_end_dt,status
                    ) 
                select 
                    Ratchet_Property_ID,srpid,mktcode,sub_mcat,qualified,block,linked,package,fenced,yieldable,control_type,attributes_combined,attributed_start_dt,attributed_end_dt,status
                from &srpadds.;
            ) by odbc;

            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                disconnect from odbc;

                %ideas_util_report_errors(1232, 'Error updating SRP database table: ratchet_srp_attributes ', '', &req_id, &err_ct.);
                %return;
            %end;

            execute(
                with etldata as (select Ratchet_Property_ID, Default_Attribution_Dt, Attributes_Last_Update_Dt
            from &lastupd.)

                merge Ratchet_Attributes_Lastupdated as a 

            using etldata as b

                on a.Ratchet_Property_ID=b.Ratchet_Property_ID 

                when matched then 

            update set a.Attributes_Last_Update_Dt=b.Attributes_Last_Update_Dt;
            ) by odbc;

            %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                execute(rollback) by odbc;
                disconnect from odbc;

                %ideas_util_report_errors(1233, 'Error updating Last Updated table: Ratchet_Attributes_Lastupdate Ratchet_Attributes_Lastupdate', '', &req_id, &err_ct.);
                %return;
            %end;

            execute (
                commit
                ) by odbc;
            disconnect from odbc;
        quit;

    %end;
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_update_srp_database;
