%macro assign_noshows() /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "HILSTAR - FLOW";
%put "HILSTAR - ispcrs = &ispcrs.";
%put "HILSTAR - treatCXLFtrArvlAsNS = &treatCXLFtrArvlAsNS.";

	
	    proc Sql;
	        update hilstar.yield set rescode = "N" 
	            WHERE rescode in ("D", "C") AND f_noshow = "Y";
	       quit;
	       

	    %if(&treatCXLFtrArvlAsNS eq true AND &ispcrs eq true) %then %do;
		   proc Sql;
			update hilstar.yield set rescode = "N" where old_doa < date_stamp and rescode="D" AND f_noshow = "N";
		   quit;
	    %end;

	    %if(&isPCRS eq true) %then %do;
		proc Sql;
			update hilstar.yield set rescode = "N" where old_doa=date_stamp and time_stamp ge '22:00't and rescode="D";
		quit;
	    %end;
	    
	    
	    
	    

 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
