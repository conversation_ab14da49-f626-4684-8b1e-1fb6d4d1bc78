%macro handle_LV0_Rate(rtlevel,marketcode,basecurrency,yield_currency_code) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %put "Handle LV0 Rate - HILSTAR - FLOW";


	/* FOR LV0 Rates; */
        proc Sql;
            insert into &work_lib..RateHeader 
				select 
	                case 
	                    when "&rtlevel" like "LV%" then cats( "QLV" , trim(rates.srpid))
	                    when "&rtlevel" like "RL%" then cats( "QRL" , trim(rates.srpid))
	                    when "&rtlevel" like "RT%" then cats( "QRT" , trim(rates.srpid))
	                    when "&rtlevel" like "%_LV%" then cats( "QLV" , trim(rates.srpid))
	                    else "." 
	                	end 
	            	as Name, 
	                min(rates.startDate) as StartDate, max(rates.endDate) as EndDate,
	                "&basecurrency." as Currency,
	                "Qualified" as Type, "true" as Yieldable, "false" as PriceRelative,"." as DerivedRateCode,
	                "false" as IncludesPackage,"" as Remarks, trim("&marketcode.") as mktSegment 
				from hilstar.rates 
            	where rates.rtlvl = 0
                group by rates.srpid;
        quit;

        proc Sql;
            insert into &work_lib..RateDetail select distinct rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate,
                rates.Sunday_1 as Sunday,rates.Monday_1 as Monday,rates.Tuesday_1 as Tuesday,rates.Wednesday_1 as Wednesday,rates.Thursday_1 
            as Thursday,rates.Friday_1 as Friday,rates.Saturday_1 as Saturday,
                case 
                    when "&rtlevel" like "LV%" then cats( "QLV" ,trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "QRL" ,trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "QRT" ,trim(rates.srpid))
                     when "&rtlevel" like "%_LV%" then cats( "QLV" , trim(rates.srpid))
                    else "." 
                end 
            as Name,rates.rtRtLvl as rtRtLvl,rates.recordseq as recordseq from hilstar.rates where rates.rtlvl= 0;
        quit;

        proc Sql;
            insert into &work_lib..MarketSegment select distinct trim("&marketcode.") as name, "T" as SegmentType, 
                case 
                    when "&rtlevel" like "LV%" then cats( "QLV" ,trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "QRL" ,trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "QRT" ,trim(rates.srpid))
                     when "&rtlevel" like "%_LV%" then cats( "QLV" , trim(rates.srpid))
                    else "." 
                end 
            as srpid from hilstar.rates where rates.rtlvl= 0;
        quit;

%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ;
