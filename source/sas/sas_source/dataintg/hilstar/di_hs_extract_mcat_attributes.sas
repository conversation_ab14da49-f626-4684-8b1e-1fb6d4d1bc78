%macro di_hs_extract_mcat_attributes(rat_client_id,clientSrpMappingCloneEnabled)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc Sql;
        select Ratchet_Client_ID into:RatchetClientID from tenant.Ratchet_Client
            where Ratchet_Client_Code = "&client_code";
    quit;

    proc Sql;
        create table &work_lib..tempmcatattributes as 
            select ratchet_client_id, mkt_seg_name as mktcode, sub_mkt_seg_name as sub_mcat, Qualified,Block,
                Linked,Package,Fenced,Yieldable,Control_Type,Is_Mcat_Plus_Eligible
            from tenant.Ratchet_Master_Mcat_Attributes
                where Ratchet_Client_ID =&RatchetClientID;

        %if &sqlrc ne 0 %then %do;
            %let syscc =5;

            %ideas_util_report_errors(1248, 'ERROR:Error reading Ratchet_Mcat_Attributes table', '', &req_id, &err_ct.);
            %let error_str=%str(ERROR:Error reading Ratchet_Mcat_Attributes table);

            %return;
        %end;
    quit;

    %if &SYSNOBS =0 %then %do;
        %let syscc =5;

        %ideas_util_report_errors(1249, 'ERROR:No Mcat Attribute rows read from the database Ratchet_Mcat_Attributes table', '', &req_id, &err_ct.);
        %let error_str=%str(ERROR:Error reading Ratchet_Mcat_Attributes table);

        %return;
    %end;

    proc Sql;
        create table &work_lib..tempmcatdefaultmapping as 
            select * from tenant.Default_Mcat_Mapping
                where Ratchet_Client_ID =&RatchetClientID
                    order by mktcode;

        %if &sqlrc ne 0 %then %do;
            %let syscc =5;

            %ideas_util_report_errors(1250, 'ERROR:The MSSQL table &work_lib..Default_Mcat_Mapping is empty.SO cannot proceed FURTHER ', '', &req_id, &err_ct.);
            %let error_str=%str(ERROR:The MSSQL table &work_lib..Default_Mcat_Mapping is empty.SO cannot proceed FURTHER );

            %return;
        %end;
    quit;

    %if &SYSNOBS =0 %then %do;
        %let syscc =5;

        %ideas_util_report_errors(1251, 'ERROR:The MSSQL table &work_lib..Default_Mcat_Mapping is empty OR NO ROWS found for given Client.SO cannot proceed FURTHER ', '', &req_id, &err_ct.);
        %let error_str=%str(ERROR:The MSSQL table &work_lib..Default_Mcat_Mapping is empty OR NO ROWS found for given Client.SO cannot proceed FURTHER);

        %return;
    %end;

    data &work_lib..mcatattributes (keep=hotelid ratchet_client_id mktcode sub_mcat qualified block linked package fenced yieldable control_type attributes_combined is_mcat_plus_eligible );
        retain hotelid ratchet_client_id mktcode count sub_mcat qualified block linked package fenced yieldable control_type attributes_combined is_mcat_plus_eligible;
        array wildcard_two_values{2} $1. ("Y" "N");
        array wildcard_three_values{3} $1. ("F" "S" "N");
        array wildcard_four_values{4,2} $1. ("Y" "Y" "Y" "N" "N" "N" "N" "Y" );
        array wildcard_six_values{6,2} $1. ("Y" "F" "Y" "S" "Y" "N" "N" "F" "N" "S" "N" "N" );
        array wildcard_eight_values{8,3} $1. 
            ("Y" "Y" "Y" "Y" "Y" "N" "Y" "N" "Y" "Y" "N" "N" 
            "N" "N" "N" "N" "N" "Y" "N" "Y" "Y" "N" "Y" "N");
        array wildcard_twelve_values{12,3} $1. (
            "Y" "Y" "F" "Y" "Y" "S" "Y" "Y" "N" "Y" "N" "F" "Y" "N" "S" "Y" "N" "N" 
            "N" "N" "F" "N" "N" "S" "N" "N" "N" "N" "Y" "F" "N" "Y" "S" "N" "Y" "N");
        array wildcard_twentyfour_values{24,4} $1. (
            "Y" "Y" "Y" "F" "Y" "Y" "Y" "S" "Y" "Y" "Y" "N" "Y" "Y" "N" "F" "Y" "Y" "N" "S" "Y" "Y" "N" "N" 
            "Y" "N" "Y" "F" "Y" "N" "Y" "S" "Y" "N" "Y" "N" "Y" "N" "N" "F" "Y" "N" "N" "S" "Y" "N" "N" "N" 
            "N" "Y" "Y" "F" "N" "Y" "Y" "S" "N" "Y" "Y" "N" "N" "Y" "N" "F" "N" "Y" "N" "S" "N" "Y" "N" "N" 
            "N" "N" "Y" "F" "N" "N" "Y" "S" "N" "N" "Y" "N" "N" "N" "N" "F" "N" "N" "N" "S" "N" "N" "N" "N" );
        set &work_lib..tempmcatattributes;
        count=1;
        save_package=package;
        save_fenced=fenced;
        save_yieldable=yieldable;
        save_control_type=control_type;
        hotelid="&prop_id";

        if package="*" then
            count=count*2;

        if fenced="*" then
            count=count*2;

        if yieldable="*" then
            count=count*2;

        if control_type="*" then
            count=count*3;

        do I = 1 to count;
            select (count);
                when (2) do;
                    if save_package="*" then

                        package=wildcard_two_values(I);

                    if save_fenced="*" then
                        fenced=wildcard_two_values(I);

                    if save_yieldable="*" then
                        yieldable=wildcard_two_values(I);
                end;

                when (3)
                    if save_control_type="*" then
                        control_type=wildcard_three_values(I);
                when (4) do;
                    if save_package="*" then

                        package=wildcard_four_values(I,1);

                    if save_fenced="*" then do;
                        if save_package="*" then
                            fenced=wildcard_four_values(I,2);
                        else fenced=wildcard_four_values(I,1);
                    end;

                    if save_yieldable="*" then
                        yieldable=wildcard_four_values(I,2);
                end;

                when (6) do;
                    if save_package="*" then

                        package=wildcard_six_values(I,1);

                    if save_fenced="*" then
                        fenced=wildcard_six_values(I,1);

                    if save_yieldable="*" then
                        yieldable=wildcard_six_values(I,1);

                    if save_control_type="*" then
                        control_type=wildcard_six_values(I,2);
                end;

                when (8) do;
                    if save_package="*" then

                        package=wildcard_six_values(I,1);

                    if save_fenced="*" then
                        fenced=wildcard_six_values(I,2);

                    if save_yieldable="*" then
                        yieldable=wildcard_six_values(I,3);
                end;

                when (12) do;
                    if save_package="*" then

                        package=wildcard_twelve_values(I,1);

                    if save_fenced="*" then do;
                        if save_package="*" then
                            fenced=wildcard_twelve_values(I,2);
                        else fenced=wildcard_twelve_values(I,1);
                    end;

                    if save_yieldable="*" then
                        yieldable=wildcard_twelve_values(I,2);

                    if save_control_type="*" then
                        control_type=wildcard_twelve_values(I,3);
                end;

                when (24) do;
                    if save_package="*" then

                        package=wildcard_twentyfour_values(I,1);

                    if save_fenced="*" then
                        fenced=wildcard_twentyfour_values(I,2);

                    if save_yieldable="*" then
                        yieldable=wildcard_twentyfour_values(I,3);

                    if save_control_type="*" then
                        control_type=wildcard_twentyfour_values(I,4);
                end;

                otherwise;
            end;

            attributes_combined=trim(qualified)||trim(block)||trim(linked)||trim(package)||trim(fenced)|| 
                trim(yieldable)||trim(control_type);
            output &work_lib..mcatattributes;
        end;
    run;

    %if &SYSNOBS=0 or &syserr > 0 %then %do;
        %let syscc =5;

        %ideas_util_report_errors(1252, 'ERROR:No observations in mcat attributes dataset: mcatattributes ', '', &req_id, &err_ct.);
        %let error_str=%str(ERROR:No observations in mcat attributes dataset: mcatattributes);

        %return;
    %end;

    proc Sort data=&work_lib..mcatattributes;
        by mktcode attributes_combined;

    proc Sql;
        create table &work_lib..mcatattributesdistinct as 
            select distinct mktcode, is_mcat_plus_eligible, ratchet_client_id 
                from &work_lib..mcatattributes 
                    order by mktcode;

        %if &sqlrc ne 0 %then %do;
            %let syscc =5;

            %ideas_util_report_errors(1253, 'ERROR:Error reading mcatattributes', '', &req_id, &err_ct.);
            %let error_str=%str(ERROR:Error reading mcatattributes);

            %return;
        %end;
    quit;

    %if &SYSNOBS =0 %then %do;
        %let syscc =5;

        %ideas_util_report_errors(1254, 'ERROR:No observations in mcat attributes dataset: mcatattributesdistinct', '', &req_id, &err_ct.);
        %let error_str=%str(ERROR:No observations in mcat attributes dataset: mcatattributesdistinct);

        %return;
    %end;

    data &work_lib..mcatattributesdistinct (keep=ratchet_client_id mktcode is_mcat_plus_eligible Default_History_Submcat Default_Future_Submcat);
        retain ratchet_client_id mktcode is_mcat_plus_eligible Default_History_Submcat Default_Future_Submcat;
        merge &work_lib..mcatattributesdistinct (in=srpin) &work_lib..tempmcatdefaultmapping (in=mcatin);
        by mktcode;

        if srpin=1 & mcatin=1 then do;
            output &work_lib..mcatattributesdistinct;
        end;

        if srpin=1 & mcatin=0 then do;
            if is_mcat_plus_eligible= "Y" then do;
                Default_History_Submcat="DEFAULT_MISSING";
                Default_Future_Submcat="DEFAULT_MISSING";
            end;
            else do;
                Default_History_Submcat=mktcode;
                Default_Future_Submcat=mktcode;
            end;

            output &work_lib..mcatattributesdistinct;
        end;

        %if &syserr > 0 %then %do;
            %let syscc =5;

            %ideas_util_report_errors(1255, 'ERROR:Error updating mcat distinct dataset: mcatattributesdistinct', '', &req_id, &err_ct.);
            %let error_str=%str(ERROR:Error updating mcat distinct dataset: mcatattributesdistinct);

            %return;
        %end;
    run;   

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_extract_mcat_attributes;
