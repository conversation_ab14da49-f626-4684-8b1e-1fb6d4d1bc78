%macro intg_functions() /store;

	          proc fcmp outlib=work.funcs.test;
	     	     	     	      function identifyValidForTrxDateChange(new_mkt_code $, old_mkt_code $,new_srp $,old_srp $,new_level,old_level,defaultmarketsegment $) $;
	     	     	     	      	%put "Inside function";
	     	     	     	      	if (new_mkt_code ne old_mkt_code or new_srp ne old_srp or 
	     	     							  (new_level ne old_level and (new_mkt_code = "BAR" or new_mkt_code = "&defaultmarketsegment" ))) then 
	     	     	     	      	return ("TRUE");
	     	     	     	      	else return ("FALSE");	      	 
	     	     	     	      endsub;
	     	     	     	      run; 

%mend;