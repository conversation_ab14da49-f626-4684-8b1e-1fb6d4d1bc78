%macro read_load_snap_file(rms_filepath,snap_etl_file_name,resetGRPConvAval) /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "HILSTAR - FLOW";

data &work_lib..temphothd(keep = recordtype hotelid SnapDays)

        &work_lib..tempsrpcnt(keep = recordtype hotelid SRPID SRPType Cdate Idate Days StrDate EndDate rtlvl grpid Inv 
            InvType MinLOS MaxLOS CTA Status MktCode TotRmsAuth TotRmsSold TotRmsAvail TotLOSPattern
            TotMinLOS TotMaxLOS TotCTA TotCLO Avlexc Yield AddRev SRPCur IncTxSvc MktSegType 
            EffDate MinLdTime MaxLdTime StayReqOn ChildFree GrpOrg GrpSlsPer HousingBy RateRlv BarSrp 
            yieldableVal yieldableValtype SRPLockLevel RateConditional ControlType Fenced Package)
            &work_lib..tempsrprm (keep = recordtype HotelID SRPID RoomType NumRms Cdate Idate Days rtlvl Grpid RmsAuth 
            RmsSold RmsAvail LOSPattern MinLOS MaxLOS CTA CLO Rate)
            &work_lib..tempgrpcvt (keep = recordtype hotelid SRPID SRPType Cdate Idate Days StrDate EndDate RmsAuth RmsSold RmsAvail
            MinLOS MaxLOS CTA Status MktCode Yield AddRev SRPCur IncTxSvc MktSegType EffDate MinLdTime 
            MaxLdTime StayReqOn ChildFree GrpOrg GrpSlsPer HousingBy Grpid CutOffDate CutOffDays missvalue1 
            missvalue2 missvalue3 GRPLockLevel RateConditional ControlType Fenced Package)
        &work_lib..temphotel (keep = recordtype hotelid NumRms Cdate Idate Days RmsAuth Ovblvl LOSPattern MinLOS MaxLOS StayMLOS StayLim CTA CLO GTDPol RmsSold 
            RmsAvail RmsOOO MgrHld GrpConv GrpConvBlk GrpConvSld GrpConvAvl MktCode PropCurr)
            &work_lib..temprmtype (keep = HotelID RmType NumRms Cdate Idate Days RmsAuth RmsSold RmsAvail RmsOOO MgrHld 
            LOSPattern MinLOS MaxLOS CTA CLO RmTypeClass)
            &work_lib..temphottr (keep = recordtype hotelid CapStDate CapStTime CapEdDate CapEdTime);
        infile "&rms_filepath\&snap_etl_file_name" delimiter='|' missover DSD;
        input recordtype $ @;

        if recordtype='hothd' then do;
            input hotelid$ SnapDays;

            /* Check for nubmer of fileds in the record if less than 3 exit with error incorrect number of fields; */
            if sum( missing(recordtype),missing(hotelid),missing(SnapDays)) > 0 then do;
                call symputx('error_codes',1224,'l');
                call symputx('err_str','ERROR parsing hothd record','l');
            end;

            output &work_lib..temphothd;
        end;

        if recordtype='srpcnt' then do;
            length GrpOrg$30 GrpSlsPer$30 SRPID$21;
            input hotelid$ SRPID$ SRPType$ Cdate:mmddyy10. Idate:mmddyy10. Days StrDate:mmddyy10. EndDate:mmddyy10.
                rtlvl grpid$ Inv$ InvType$ MinLOS MaxLOS CTA$ Status$ MktCode$ TotRmsAuth TotRmsSold TotRmsAvail TotLOSPattern$ 
                TotMinLOS TotMaxLOS TotCTA$ TotCLO$ Avlexc$ Yield$ AddRev$ SRPCur$ IncTxSvc$ MktSegType$ EffDate:mmddyy10. 
                MinLdTime MaxLdTime StayReqOn$ ChildFree$ GrpOrg$ GrpSlsPer$ HousingBy$ RateRlv$ BarSrp$ yieldableVal
                yieldableValtype$ SRPLockLevel RateConditional$ ControlType$ Fenced$ Package$;
            format Cdate Idate StrDate EndDate EffDate yymmdd10.;

            output &work_lib..tempsrpcnt;
        end;

        if recordtype='srprm' then do;
        length SRPID$21 RoomType$21;
            input HotelID$ SRPID$ RoomType$ Cdate:mmddyy10. Idate:mmddyy10. Days rtlvl Grpid$ RmsAuth 
                RmsSold RmsAvail LOSPattern$ MinLOS MaxLOS CTA$ CLO$ Rate;
            format Cdate Idate yymmdd10.;

            /* Check for nubmer of fileds in the record if less than 18 exit with error incorrect number of fields; */
            if sum(missing(recordtype),missing(HotelID),missing(SRPID),missing(RoomType),missing(NumRms),missing(Cdate),
                missing(Idate),missing(Days),missing(rtlvl),missing(Grpid),missing(RmsAuth),missing(RmsSold),missing(RmsAvail),
                missing(LOSPattern),missing(MinLOS),missing(MaxLOS),missing(CTA),missing(CLO)) > 0 then do;
                call symputx('error_codes',1226,'l');
                call symputx('err_str','ERROR parsing srprm record','l');
            end;

			if RmsAuth < 0 then do;
				RmsAuth = 0;
			end;
			if RmsSold < 0 then do;
				RmsSold = 0;
			end;
			if RmsAvail < 0 then do;
				RmsAvail = 0;
			end;

            if(missing(Rate)) then do;
                Rate=0;
            end;

            output &work_lib..tempsrprm;
        end;

        if recordtype='grpcvt' then do;
            length GrpOrg$30 GrpSlsPer$30 SRPID$21;
            input hotelid$ SRPID$ SRPType$ Cdate:mmddyy10. Idate:mmddyy10. Days StrDate:mmddyy10.
                EndDate:mmddyy10. RmsAuth RmsSold RmsAvail MinLOS MaxLOS CTA$ Status$ MktCode$ Yield$ AddRev$ 
                SRPCur$ IncTxSvc$ MktSegType$ EffDate:mmddyy10. MinLdTime MaxLdTime StayReqOn$ ChildFree$ 
                GrpOrg$ GrpSlsPer$ HousingBy$ Grpid$ CutOffDate:mmddyy10. CutOffDays missvalue1 missvalue2 missvalue3 GRPLockLevel 
                RateConditional$ ControlType$ Fenced$ Package$;
            format Cdate Idate StrDate EndDate EffDate CutOffDate yymmdd10.;

            /* Check for if old type record or new type record based on number of fields olddata=17(true) oldata>=33(false) */
            /* read the recod according to the type od data avaliable oldtype or newtype; */
            if sum(missing(recordtype),missing(hotelid),missing(SRPID),missing(SRPType),missing(Cdate),missing(Idate),
                missing(Days),missing(StrDate),missing(EndDate),missing(RmsAuth),missing(RmsSold),missing(RmsAvail),
                missing(MinLOS),missing(MaxLOS),missing(CTA),missing(Status),missing(MktCode),missing(Yield),missing(AddRev),
                missing(SRPCur),missing(IncTxSvc),missing(MktSegType),missing(EffDate),missing(MinLdTime),missing(MaxLdTime),
                missing(StayReqOn),missing(ChildFree),missing(GrpOrg),missing(GrpSlsPer),missing(HousingBy),missing(Grpid),
                missing(CutOffDate),missing(CutOffDays)) > 0 then do;
                call symputx('error_codes',1227,'l');
                call symputx('err_str','ERROR parsing grpcvt record','l');
            end;

            output &work_lib..tempgrpcvt;
        end;

        if recordtype='hotel' then do;
            input hotelid$ NumRms Cdate:mmddyy10. Idate:mmddyy10. Days RmsAuth Ovblvl LOSPattern$
                MinLOS MaxLOS StayMLOS StayLim CTA$ CLO$ GTDPol$ RmsSold RmsAvail RmsOOO MgrHld GrpConv 
                GrpConvBlk GrpConvSld GrpConvAvl MktCode$ PropCurr$;
            format Cdate Idate yymmdd10.;

            /*PS 9 Jan US2063, Reset MgrHeld rooms for past days to zero if OOO + mgrheld exceeds capacity*/
            /*CN-21 Dec-if OOO is > capacity, set it to capacity. but only for past*/
            /*PS 11 Jan US 1840 Reset GrpConAvl to zero for past if greater than zero*/
            if Days < 0 then do;
                if RmsOOO+MgrHld > NumRms then do;
                    MgrHld=0;
                end;

                if RmsOOO > NumRms then do;
                    RmsOOO=NumRms;
                end;

                /*CN-17 Jan US 1840 Reset GrpConAvl to zero for past if greater than zero*/
                %put resetGRPConvAval = &resetGRPConvAval.;

                %if &resetGRPConvAval = TRUE %then %do;
                    %put "Inside TRUE";

                    if GrpConvAvl > 0 then do;
                        GrpConvAvl = 0;
                    end;
                %end;
            end;

			/*US12834 Hilstar - Out-Of_Order = RmsOOO & Out-of_service = MgrHld with negative values*/
			if RmsOOO < 0 then do;
				RmsOOO = 0;
			end;

			if MgrHld < 0 then do;
				MgrHld = 0;
			end;
			/*US12834 Hilstar - Out-Of_Order = RmsOOO & Out-of_service = MgrHld with negative values*/
            /* Check for nubmer of fileds in the record if less than 26 exit with error incorrect number of fields; */
            if sum(missing(recordtype),missing(hotelid),missing(NumRms),missing(Cdate),missing(Idate),missing(Days),
                missing(RmsAuth),missing(Ovblvl),missing(LOSPattern),missing(MinLOS),missing(MaxLOS),missing(StayMLOS),
                missing(StayLim),missing(CTA),missing(CLO),missing(GTDPol),missing(RmsSold),missing(RmsAvail),missing(RmsOOO),
                missing(MgrHld),missing(GrpConv),missing(GrpConvBlk),missing(GrpConvSld),missing(GrpConvAvl),missing(MktCode),
                missing(PropCurr)) > 0 then do;
                call symputx('error_codes',1228,'l');
                call symputx('err_str','ERROR parsing hotel record','l');
            end;

            output &work_lib..temphotel;
        end;

        if recordtype='rmtype' then do;
            length RmType$21;
            input HotelID$ RmType$ NumRms Cdate:mmddyy10. Idate:mmddyy10. Days RmsAuth RmsSold RmsAvail RmsOOO MgrHld 
                LOSPattern$ MinLOS MaxLOS CTA$ CLO$ RmTypeClass$;
            format Cdate Idate yymmdd10.;

            /*PS 9 Jan US2063, Reset MgrHeld rooms for past days to zero if OOO + mgrheld exceeds capacity*/
            /*CN-21 Dec-if OOO is > capacity, set it to capacity. but only for past*/
            if Days < 0 then do;
                if RmsOOO+MgrHld > NumRms then do;
                    MgrHld=0;
                end;

                if RmsOOO > NumRms then do;
                    RmsOOO=NumRms;
                end;
            end;
			/*US12834 Hilstar - handle Out-Of_Order = RmsOOO & Out-of_service = MgrHld with negative values*/
			if RmsOOO < 0 then do;
				RmsOOO = 0;
			end;

			if MgrHld < 0 then do;
				MgrHld = 0;
			end;
			/*US12834 Hilstar - handle Out-Of_Order = RmsOOO & Out-of_service = MgrHld with negative values*/

            /* Check for nubmer of fileds in the record if less than 18 exit with error incorrect number of fields; */
            if sum(missing(recordtype),missing(HotelID),missing(RmType),missing(NumRms),missing(Cdate),missing(Idate),missing(Days),
                missing(RmsAuth),missing(RmsSold),missing(RmsAvail),missing(RmsOOO),missing(MgrHld),missing(LOSPattern),
                missing(MinLOS),missing(MaxLOS),missing(CTA),missing(CLO),missing(RmTypeClass)) > 0 then do;
                call symputx('error_codes',1229,'l');
                call symputx('err_str','ERROR parsing rmtype record','l');
            end;

            output &work_lib..temprmtype;
        end;

        /* TODO: Insert a flag and check whether rmtype records present or not. If not then give proper error and exit.; */
        if recordtype='hottr' then do;
            input hotelid$ CapStDate:mmddyy10. CapStTime1$ CapEdDate:mmddyy10. CapEdTime1$;
            format CapStDate CapEdDate yymmdd10.;

            *format CapStTime CapEdTime TOD.;/*May be this is creating a problem for the case where thetime is 00 hrs */

            /* Added to handle time format issue for time starting with 00 Hrs. e.g. 000427 
            for the hottr record can be applied everywhere we are dealing with time format */
            format CapStTime CapEdTime time5.;
            shrs = substr(CapStTime1,1,2);
            smin = substr(CapStTime1,3,2);
            CapStTime = hms(shrs,smin,00);
            ehrs = substr(CapEdTime1,1,2);
            emin = substr(CapEdTime1,3,2);
            CapEdTime = hms(ehrs,emin,00);

            /* Check for nubmer of fileds in the record if less than 6 exit with error incorrect number of fields; */
            if sum( missing(recordtype),missing(hotelid),missing(CapStDate),missing(CapStTime),missing(CapEdDate),
                missing(CapEdTime)) > 0 then do;
                call symputx('error_codes',1230,'l');
                call symputx('err_str','ERROR parsing hottr record','l');
            end;

            output &work_lib..temphottr;
        end;
    run;

   data &work_lib..tempgrpcvt;
	set    &work_lib..tempgrpcvt;
	pcrsMktCode = mktcode;
	output;
   run;
    
    %let macroname=&SYSMACRONAME;
     %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
