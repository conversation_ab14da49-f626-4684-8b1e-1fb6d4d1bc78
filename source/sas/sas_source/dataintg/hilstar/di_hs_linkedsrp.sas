%macro di_hs_linkedsrp(dirpathForReport,prop_id,client_code)/store;
	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %let linkedSrpFilePathName =%str(&dirpathForReport.\&client_code._&prop_id._LINKEDSRP.psv);
    %let linkedSrpHeaderFilePathName =%str(&dirpathForReport.\&client_code._&prop_id._LINKEDSRP_HEADERS.psv);
    %let linkedSrpAdjustmentFilePathName =%str(&dirpathForReport.\&client_code._&prop_id._LINKEDSRP_ADJUSTMENTS.psv);
    %put str = &linkedSrpFilePathName;

    data _null_;
        fname="tempfile";
        rc=filename(fname,"&linkedSrpFilePathName");
        if rc = 0 and fexist(fname) then
            rc=fdelete(fname);
        rc=filename(fname);
    run;

    %if %sysfunc(exist(hilstar.rates)) and %ideas_util_nobs(hilstar.rates) gt 0 %then
    %do;
        proc export
            data=hilstar.rates
                (keep=SRPID roomType startDate endDate Sunday_1 Monday_1 Tuesday_1 Wednesday_1 Thursday_1 Friday_1 Saturday_1 recordseq)
            outfile="&linkedSrpFilePathName"
            dbms=csv replace;
            delimiter='|';
            putnames=no;
        run;
    %end;

    %if %sysfunc(exist(hilstar.srpcnt)) and %ideas_util_nobs(hilstar.srpcnt) gt 0 %then
    %do;
        proc sql;
            create table &work_lib..headers as
            SELECT
                srpcnt.SRPID, srpcnt.SRPCur, srpcnt.StrDate, srpcnt.EndDate,
                srpcnt.Yield, srpcnt.BarSrp, srpcnt.ControlType, srpcnt.Status,
                srpcnt.yieldableVal, srpcnt.yieldableValtype,
                currex.effectiveconversionfactor
            FROM hilstar.srpcnt srpcnt
            JOIN hilstar.currex currex
            ON srpcnt.SRPCur=currex.currencyId
            where srpcnt.days = 0
            and srpcnt.SRPType NE "D";
        quit;
        %if %sysfunc(exist(&work_lib..headers)) and %ideas_util_nobs(&work_lib..headers) gt 0 %then
        %do;
            proc export
                data=&work_lib..headers
                outfile="&linkedSrpHeaderFilePathName"
                dbms=csv replace;
                delimiter='|';
                putnames=no;
            run;
        %end;
    %end;

    %if %sysfunc(exist(hilstar.rmsrates)) and %ideas_util_nobs(hilstar.rmsrates) gt 0 %then
    %do;
        proc export
            data=hilstar.rmsrates
            outfile="&linkedSrpAdjustmentFilePathName"
            dbms=csv replace;
            delimiter='|';
            putnames=no;
        run;
    %end;


    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_linkedsrp;
