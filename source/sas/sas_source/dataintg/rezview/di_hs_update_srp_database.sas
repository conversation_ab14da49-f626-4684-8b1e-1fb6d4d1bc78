%macro di_hs_update_srp_database(tenant_database, tenant_server, tenant_db_port, tenant_server_instance, tenant_user, tenant_password,isPrimingExtract)/store;
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);
	
	%put "ESA Flow so srp update database will not happen";
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_update_srp_database;
