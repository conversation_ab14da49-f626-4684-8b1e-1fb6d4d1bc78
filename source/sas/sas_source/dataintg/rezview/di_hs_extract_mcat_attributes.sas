%macro di_hs_extract_mcat_attributes(rat_client_id,clientSrpMappingCloneEnabled)/store;
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow "; 
	/* Get the client srp mapping table from tenant */
	%get_client_srp_mapping(&clientSrpMappingCloneEnabled.);
	/* Get distinct srp's from srpcnt grpcvt yield and books */
	%get_distinct_srpids;
	/* Create srp_mkt_mapping table with hotel level default submcat */
	%def_srp_mkt_map;
	/* Update srp_mkt_mapping table with two char matching submcat */
	%di_hs_two_char_rule;
	/* Update srp_mkt_mapping table with single char matching submcat */
	%di_hs_single_char_rule;
	/* Update srp_mkt_mapping table with exact char matching submcat */
	%di_hs_exact_match_rule;
	/*Solution for property level overide*/
	%di_exact_match_rule_property(&rat_client_id.);
	/* Update srp_mkt_mapping end date with 2173-12-31 for hotel level default srp's */
	%update_def_mkt_date;

	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_extract_mcat_attributes;
