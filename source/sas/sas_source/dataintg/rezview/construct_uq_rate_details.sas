%macro construct_uq_rate_details /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct UQ Rate Details - ESA - FLOW";
	proc Sql;
            insert into &work_lib..RateDetail select distinct rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate, 
                case 
				 when "&useDoubleOccupancyRates" = "false" then rates.Sunday_1 
				 else rates.Sunday_2
				end as Sunday,
				case 
				 when  "&useDoubleOccupancyRates" = "false" then rates.Monday_1 
				 else rates.Monday_2
				end as Monday,
				case 
				 when  "&useDoubleOccupancyRates" = "false" then rates.Tuesday_1 
				 else rates.Tuesday_2 
				end as Tuesday,
				case 
				 when "&useDoubleOccupancyRates" = "false" then rates.Wednesday_1 
				 else rates.Wednesday_2
				end as Wednesday,
				case 
				 when "&useDoubleOccupancyRates" = "false" then rates.Thursday_1 
				 else rates.Thursday_2
				end as Thursday,
				case 
				 when "&useDoubleOccupancyRates" = "false" then rates.Friday_1 
				 else rates.Friday_2
				end as Friday,
				case 
				 when "&useDoubleOccupancyRates" = "false" then rates.Saturday_1 
				 else rates.Saturday_2
				end as Saturday,
                case 
                    when "&rtlevel" like "LV%" then cats( "LV" , trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "RL" , trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "RT" , trim(rates.srpid))
                    else "."
                end 
            as Name,rates.rtRtLvl as rtRtLvl from hilstar.rates 
			where rates.rtlvl >= 0 and rates.rtlvl <= 8;
        quit;
	
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	
%mend;
