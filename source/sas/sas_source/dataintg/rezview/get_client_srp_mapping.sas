%macro get_client_srp_mapping(clientSrpMappingCloneEnabled)/store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow ";
	%put "ESA: Client SRP Mapping clone enabled = "&clientSrpMappingCloneEnabled.;

	%let clientSrpMappingCloneEnabled = %upcase(&clientSrpMappingCloneEnabled);
	%if &clientSrpMappingCloneEnabled. = TRUE %then
	    %do;
	        %put "ESA: Client SRP Mapping clone true";
	        proc sql;
                create table &work_lib..tempClientSrpMapping as
                select srp, Analytic_Market_Segment_Code,Start_Date,End_Date
                from tenant.Client_Srp_Mapping_Clone
                    where ratchet_client_id =(select Ratchet_Client_ID from tenant.Ratchet_Client
                where Ratchet_Client_Code = "&client_code");
            quit;
	    %end;
	%else
	    %do;
	        %put "ESA: Client SRP Mapping clone false";
	        proc sql;
	            create table &work_lib..tempClientSrpMapping as
                select srp, Analytic_Market_Segment_Code,Start_Date,End_Date
                from tenant.Client_Srp_Mapping
                    where ratchet_client_id =(select Ratchet_Client_ID from tenant.Ratchet_Client
                where Ratchet_Client_Code = "&client_code");
	        quit;
	    %end;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend get_client_srp_mapping;
