%macro process_rate_detail /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct Rate Details - ESA - FLOW";
	
	
	data &work_lib..ratedetailinput;
	set &work_lib..ratedetailinput  ; 
	drop numdays2 numdays1;
		nstartDt=startDate;
		nendDt = endDate;
		if weekday(startDate) > 1 then do;
			numdays1 = -1 * (weekday(startDate) -2);
			nstartDt=intnx('day',startDate,numdays1);
		end;
		if weekday(startDate) = 1 then do;
			nstartDt=intnx('day',startDate,-6);
		end;
		if weekday(endDate) > 1  then do;
			numdays2 = 8 - (weekday(endDate)) ;
			nendDt=intnx('day',endDate,numdays2);
		end;
		output;
	run;
	
	data &work_lib..correctDOWRates;
	set &work_lib..ratedetailinput  ; 
	drop  startDate endDate nstartDt nendDt Sunday Monday Tuesday Wednesday Thursday Friday Saturday ;
	do occupancyDt = nstartDt to nendDt;
		if occupancyDt >=startDate and occupancyDt <=endDate then do;
			if  weekday(occupancyDt)  = 1 then rate = Sunday;
			if  weekday(occupancyDt)  = 2 then rate = Monday;
			if  weekday(occupancyDt)  = 3 then rate = Tuesday;
			if  weekday(occupancyDt)  = 4 then rate = Wednesday;
			if  weekday(occupancyDt)  = 5 then rate = Thursday;
			if  weekday(occupancyDt)  = 6 then rate = Friday;
			if  weekday(occupancyDt)  = 7 then rate = Saturday;
		end;
		else do;
			rate = .;
		end;
		if rate = 0 then do;
			rate = .;
		end;
	output ;
	end ;
	run;
	
	proc sql ;
	   create table &work_lib..dowRates as  
			select grpid, accomTyp, occupancyDt, 
				case when (weekday(occupancyDt)-1) > 0 then (weekday(occupancyDt)-1)
					 else (weekday(occupancyDt)+6) 
				end as DOW, avg(rate) as dowRate
	   		from &work_lib..correctDOWRates 
			group by  accomTyp, grpid, occupancyDt, weekday(occupancyDt)-1 ;
 	quit ;

	
	data &work_lib..correctRange;
	set &work_lib..dowRates;
	drop dt2 dow occupancyDt dowrate;
		dt2 = intnx('day',occupancyDt,6);
		if dow = 1 then do;
			do dt3 = occupancyDt to  dt2;
				newStDt = occupancyDt;
				format newStDt date9.;
				newEndDt = dt2;
				format newEndDt date9.;
				format dt3 date9.;
				output;
			end ; 
		end;
	run;
	
		
	data &work_lib..correctRangeRate;
	set  &work_lib..dowRates;
	set &work_lib..correctRange;
	drop occupancyDt dt3;
	run;
	
	proc sql;
		create table &work_lib..allDateRates as
			select newStDt,newEndDt ,accomTyp,grpid,
				case when dow = 1 then dowRate end as monday,
				case when dow = 2 then dowRate end as tuesday,
				case when dow = 3 then dowRate end as wednesday,
				case when dow = 4 then dowRate end as thursday,
				case when dow = 5 then dowRate end as friday,
				case when dow = 6 then dowRate end as saturday,
				case when dow = 7 then dowRate end as sunday
		from &work_lib..correctRangeRate
	quit;


	/* Group by needs aggregate functions in sleect when column is not participating in group by clause; max can be replaced with sum,min etc*/
	proc sql;
			insert into &work_lib..RateDetail
			select accomtyp , newStDt as DetailStartDate,newEndDt as DetailEndDate,
				max(sunday) as sunday,max(monday) as monday,max(tuesday) as tuesday,
				max(wednesday) as wednesday, max(thursday) as thursday,
				max(friday) as friday, max(saturday) as saturday,
				grpid as Name , "." as rtRtLvl
			from &work_lib..allDateRates
			group by accomtyp,grpid,newStDt,newEndDt;
	quit;
	
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
