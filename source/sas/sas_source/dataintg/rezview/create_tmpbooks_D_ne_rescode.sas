 %macro create_tmpbooks_D_ne_rescode(rmcostfieldname,usesubmcatmapping,useextendedstaymapping,defaultmarketsegment,prop_id) /store;
 
 %let macroname=&SYSMACRONAME;
 	%ideas_macro_log (start_end=START, macroname=&macroname.);

 proc Sql;
            create table &work_lib..tmpbooks as 
                SELECT hotelid as hotelid , resid as resid, new_doa as doa, new_nites as nites, 
                    new_rooms as rooms, new_type as roomtype, 
                    new_srp as srp, new_mkt_code as mktseg, new_group as srpgroup, new_block as block, &rmcostfieldname. as revenue,0 as rate, 
                    yield.Curr as Curr, rescode,date_stamp as transactionDate,f_walked as walked ,new_level as level,new_gtdcode as bookingtype,new_nchild as nchild,new_nadults as nadults,
                    old_type as oldroomtype,
                case 
                    when "&usesubmcatmapping"="TRUE" AND ((trim(yield.new_srp) ^= "" OR yield.old_mkt_code = "&defaultmarketsegment" ) 
					AND (yield.new_srp NE yield.old_srp )) then 3
                    else 4
                end
	   				as isdifferential,f_noshow as noshow
                from hilstar.yield 
                    WHERE rescode ^= "D" and hotelid="&prop_id."
                        order by hotelid,resid,date_stamp,time_stamp;
 quit;
 
  %let macroname=&SYSMACRONAME;
 	         %ideas_macro_log (start_end=END, macroname=&macroname.);  	

 
 %mend;
