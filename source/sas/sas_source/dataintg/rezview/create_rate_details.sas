%macro create_rate_details /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Create Rate Details - ESA - FLOW";
	proc sql;
		DROP TABLE &work_lib..RateDetail;
		CREATE TABLE &work_lib..RateDetail (
            accomtyp char(6),
            DetailStartDate num format=yymmdd10. informat=yymmdd10.,
            DetailEndDate num format=yymmdd10. informat=yymmdd10.,
            Sunday num format=12.2,
            Monday num format=12.2, 
            Tuesday num format=12.2,
            Wednesday num format=12.2,
            Thursday num format=12.2,
            Friday num format=12.2,
            Saturday num format=12.2,
            Name char(40),
            rtRtLvl char(1),
			constraint prim_key_RateDetail PRIMARY KEY (Name,accomtyp,DetailStartDate,DetailEndDate)
            );
	quit;
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
