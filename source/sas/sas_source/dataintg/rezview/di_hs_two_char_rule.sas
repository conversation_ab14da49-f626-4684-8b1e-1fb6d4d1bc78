%macro di_hs_two_char_rule /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow "; 

	proc sql;
		create table &work_lib..srptwocharmatch as select a.srpid as srpid,b.Analytic_Market_Segment_Code as submcat,
		b.Start_Date as startdate,b.End_Date as enddate from &work_lib..distsrpids as a inner join
		&work_lib..tempClientSrpMapping as b on substr(a.srpid,1,2)||'*' = b.srp 
		where a.srpid notin(select srpid from &work_lib..SrpExactMatch);
	quit;

	data &work_lib..SrpRuleMatch;
        set &work_lib..srptwocharmatch (rename = (srpid = srpid submcat = submcat1 startdate = startdate enddate = enddate1));
        modify &work_lib..SrpRuleMatch key = prim_key_SrpRuleMatch;
			submcat = submcat1;
			startdate = startdate;
			enddate = enddate1;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                 replace;
            end;
            when(%sysrc(_dsenom)) do;
               output;                
            end;
        end;
        _iorc_=0;
        _error_=0;
    run;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_two_char_rule;
