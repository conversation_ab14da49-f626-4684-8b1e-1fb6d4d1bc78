%macro def_srp_mkt_map /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow ";
	%global sub_mcat;
	
	proc sql;
		CREATE TABLE &work_lib..SrpRuleMatch (
            srpid char(21),
            submcat char(20),
            startdate num format=yymmdd10. informat=yymmdd10.,
			enddate num format=yymmdd10. informat=yymmdd10.,
			constraint prim_key_SrpRuleMatch PRIMARY KEY (srpid,startdate)
            );
	quit;

	proc sql;
		CREATE TABLE &work_lib..SrpExactMatch (
            srpid char(21),
            submcat char(20),
            startdate num format=yymmdd10. informat=yymmdd10.,
			enddate num format=yymmdd10. informat=yymmdd10.,
			constraint prim_key_SrpExactMatch PRIMARY KEY (srpid,startdate)
            );
	quit;

	proc sql;
		select Analytic_Market_Segment_Code into:sub_mkt_code  from &work_lib..tempClientSrpMapping  where srp='*';
	quit;
	%let sub_mcat=&sub_mkt_code;
	%put "sub_mcat " &sub_mcat;
	
	proc sql;
		insert into &work_lib..SrpExactMatch select a.srpid,b.Analytic_Market_Segment_Code as submcat,b.start_date as startdate,
		b.end_date as enddate from &work_lib..distsrpids as a inner join &work_lib..tempClientSrpMapping as b on a.srpid=b.srp;
	quit;

	proc sql;
		insert into &work_lib..SrpRuleMatch select srpid as srpid,"&sub_mkt_code" as submcat,
		input("1980-01-01",yymmdd10.) as startdate, input("2014-07-28",yymmdd10.) as enddate 
		from &work_lib..distsrpids where srpid notin(select srpid from &work_lib..SrpExactMatch);  
	quit;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend def_srp_mkt_map;
