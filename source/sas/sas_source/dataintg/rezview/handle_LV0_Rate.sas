%macro handle_LV0_Rate(rtlevel,marketcode,basecurrency,yield_currency_code) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %put "Handle LV0 Rate - ESA - FLOW";


	/* FOR LV0 Rates; */
        proc Sql;
            insert into &work_lib..RateHeader 
				select 
	                case 
	                    when "&rtlevel" like "LV%" then cats( "QLV" , trim(rates.srpid))
	                    when "&rtlevel" like "RL%" then cats( "QRL" , trim(rates.srpid))
	                    when "&rtlevel" like "RT%" then cats( "QRT" , trim(rates.srpid))
	                    else "." 
	                	end 
	            	as Name, 
	                min(rates.startDate) as StartDate, max(rates.endDate) as EndDate,
	                 case 
    				when "&yield_currency_code" = "LOCAL" then "&basecurrency."
    				
    				else "&yield_currency_code."
    			    	end 
    			    as Currency, 
	                "Qualified" as Type, "true" as Yieldable, "false" as PriceRelative,"." as DerivedRateCode,
	                "false" as IncludesPackage,"" as Remarks, trim("&marketcode.") as mktSegment 
				from hilstar.rates 
            	where rates.rtlvl = 0
                group by rates.srpid;
        quit;

        proc Sql;
            insert into &work_lib..RateDetail select distinct rates.RoomType as accomtyp, rates.startDate as DetailStartDate,rates.endDate as DetailEndDate,
                rates.Sunday_2 as Sunday,rates.Monday_2 as Monday,rates.Tuesday_2 as Tuesday,rates.Wednesday_2 as Wednesday,rates.Thursday_2 
            as Thursday,rates.Friday_2 as Friday,rates.Saturday_2 as Saturday,
                case 
                    when "&rtlevel" like "LV%" then cats( "QLV" ,trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "QRL" ,trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "QRT" ,trim(rates.srpid))
                    else "." 
                end 
            as Name,rates.rtRtLvl as rtRtLvl from hilstar.rates where rates.rtlvl= 0;
        quit;

        proc Sql;
            insert into &work_lib..MarketSegment select distinct trim("&marketcode.") as name, "T" as SegmentType, 
                case 
                    when "&rtlevel" like "LV%" then cats( "QLV" ,trim(rates.srpid))
                    when "&rtlevel" like "RL%" then cats( "QRL" ,trim(rates.srpid))
                    when "&rtlevel" like "RT%" then cats( "QRT" ,trim(rates.srpid))
                    else "." 
                end 
            as srpid from hilstar.rates where rates.rtlvl= 0;
        quit;


	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend ;
