%macro load_grpcvt() /store;

%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

	proc Sql;
			        create table &work_lib..cdp_distinctSrpID as 
			                select distinct srpid from &work_lib..tempgrpcvt;
		quit;
				
		proc Sql;
				delete from hilstar.grpcvt where srpid in ( select srpid from &work_lib..cdp_distinctSrpID);	
	quit;
	
	proc Sort data = &work_lib..tempgrpcvt;
	            by hotelid Idate SRPID;
	
	            /* replace grpcvt; */
	        data hilstar.grpcvt;
	            set &work_lib..tempgrpcvt(rename=(SRPType=SRPType1 Cdate=Cdate1 
	                Days=Days1 StrDate=StrDate1 EndDate=EndDate1 
	                RmsAuth=RmsAuth1 RmsSold=RmsSold1 RmsAvail=RmsAvail1
	                MinLOS=MinLOS1 MaxLOS=MaxLOS1 CTA=CTA1 Status=Status1 
	                MktCode=MktCode1 Yield=Yield1 AddRev=AddRev1 SRPCur=SRPCur1 
	                IncTxSvc=IncTxSvc1 MktSegType=MktSegType1 EffDate=EffDate1 
	                MinLdTime=MinLdTime1 MaxLdTime=MaxLdTime1 StayReqOn=StayReqOn1 
	                ChildFree=ChildFree1 GrpOrg=GrpOrg1 GrpSlsPer=GrpSlsPer1 
	                HousingBy=HousingBy1 Grpid=Grpid1 CutOffDate=CutOffDate1 
	                CutOffDays=CutOffDays1 GRPLockLevel=GRPLockLevel1 
	                RateConditional=RateConditional1 ControlType=ControlType1 Fenced=Fenced1 Package=Package1 ));
	            modify hilstar.grpcvt key=prim_key_grpcvt;
	            SRPType=SRPType1;
	            Cdate=Cdate1;
	            Days=Days1;
	            StrDate=StrDate1;
	            EndDate=EndDate1;
	            RmsAuth=RmsAuth1;
	            RmsSold=RmsSold1;
	            RmsAvail=RmsAvail1;
	            MinLOS=MinLOS1;
	            MaxLOS=MaxLOS1;
	            CTA=CTA1;
	            Status=Status1;
	            MktCode=MktCode1;
	            Yield=Yield1;
	            AddRev=AddRev1;
	            SRPCur=SRPCur1;
	            IncTxSvc=IncTxSvc1;
	            MktSegType=MktSegType1;
	            EffDate=EffDate1;
	            MinLdTime=MinLdTime1;
	            MaxLdTime=MaxLdTime1;
	            StayReqOn=StayReqOn1;
	            ChildFree=ChildFree1;
	            GrpOrg=GrpOrg1;
	            GrpSlsPer=GrpSlsPer1;
	            HousingBy=HousingBy1;
	            Grpid=Grpid1;
	            CutOffDate=CutOffDate1;
	            CutOffDays=CutOffDays1;
	            GRPLockLevel=GRPLockLevel1;
	            RateConditional=RateConditional1;
	            ControlType=ControlType1;
	            Fenced=Fenced1;
		    	
	            Package=Package1;
	                        
	            
	            select(_iorc_);
	                when(%sysrc(_sok)) do;
	                    replace;
	                end;
	
	                when(%sysrc(_dsenom)) do;
	                	orgMktCode='';
	                	output;
	                end;
	            end;
	
	            _error_=0;
	            _iorc_=0;
	        run;

 %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=END, macroname=&macroname.);   

%mend;