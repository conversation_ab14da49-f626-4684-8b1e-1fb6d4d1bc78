
%macro create_input_rate_details /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct Input Rate Details - ESA - FLOW";


/* Create a dataset for processing rate details*/

proc sql;
	CREATE TABLE &work_lib..rateDetailInput(
            grpid char(6) ,
	    srpid char(21),
            StartDate num format=yymmdd10. informat=yymmdd10.,
            EndDate num format=yymmdd10. informat=yymmdd10., 
            accomtyp char(6),
            Monday num format=12.2, 
            Tuesday num format=12.2,
            Wednesday num format=12.2,
            Thursday num format=12.2,
            Friday num format=12.2,
            Saturday num format=12.2,
            Sunday num format=12.2
            );

   	insert into &work_lib..rateDetailInput
	select grpRateHdr.grpid,grpRateHdr.srpid, rates.startDate as startDate,
		rates.endDate as endDate,rates.RoomType as accomtyp,
    	rates.Monday_2 as Monday,rates.Tuesday_2 as Tuesday,
		rates.Wednesday_2 as Wednesday,rates.Thursday_2 as Thursday,
		rates.Friday_1 as Friday,rates.Saturday_2 as Saturday,rates.Sunday_2 as Sunday
	from &work_lib..GrpRateHeader as grpRateHdr
	join
	hilstar.rates as rates	on 
	grpRateHdr.srpid=rates.srpid;

	insert into &work_lib..rateDetailInput
	select srpRateHdr.grpid,srpRateHdr.srpid, rates.startDate as startDate,
		rates.endDate as endDate,rates.RoomType as accomtyp,
    	rates.Monday_2 as Monday,rates.Tuesday_2 as Tuesday,
		rates.Wednesday_2 as Wednesday,rates.Thursday_2 as Thursday,
		rates.Friday_1 as Friday,rates.Saturday_2 as Saturday,rates.Sunday_2 as Sunday
	from &work_lib..SRPRateHeader as srpRateHdr
	join
	hilstar.rates as rates
	on 
	srpRateHdr.srpid=rates.srpid;

quit;

proc sql;
	delete from &work_lib..rateDetailInput where grpid not in (select distinct name from &work_lib..RateHeader);
quit;


%let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
