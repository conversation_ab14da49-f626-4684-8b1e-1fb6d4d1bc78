%macro di_exact_match_rule_property(rat_client_id) /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow Property Level SRP Mapping Override";

	proc sql;
		CREATE TABLE &work_lib..SrpExactMatchProperty (
            srpid char(21),
            submcat char(20),
            startdate num format=yymmdd10. informat=yymmdd10.,
			enddate num format=yymmdd10. informat=yymmdd10.,
			constraint prim_key_SrpExactMatchProperty PRIMARY KEY (srpid,startdate)
            );
	quit;


	proc sql;
		create table &work_lib..tempPropertySrpMapping as 
            select SRP, Analytic_Market_Segment_Code,Start_Date
      		,End_Date
            from tenant.Srp_Mapping_Override
                where Ratchet_Property_ID = (select Ratchet_Property_ID  
				from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID = &rat_client_id);
	quit;

	proc sql;
		insert into &work_lib..SrpExactMatchProperty select a.srpid,b.Analytic_Market_Segment_Code as submcat,b.start_date as startdate,
		b.end_date as enddate from &work_lib..distsrpids as a inner join &work_lib..tempPropertySrpMapping as b on a.srpid=b.srp;
	quit;

	data &work_lib..SrpMktMap;
        set &work_lib..SrpExactMatchProperty (rename = (srpid = srpid submcat = submcat1 startdate = startdate enddate = enddate1));
        modify &work_lib..SrpMktMap key = prim_key_SrpMktMap;
			submcat = submcat1;
			startdate = startdate;
			enddate = enddate1;

		select(_iorc_);
            when(%sysrc(_sok)) do;
                 replace;
            end;
			when(%sysrc(_dsenom)) do;
               output;                
            end;
        end;
        _iorc_=0;
        _error_=0;
    run;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_exact_match_rule_property;
