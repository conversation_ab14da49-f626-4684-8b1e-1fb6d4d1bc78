%macro construct_rate_header(ispcrs,includeNonZeroLckLvlYSRP,basecurrency,rms_rates,rtlevel,yield_currency_code,semiyieldableRatesAsLinkedSRPs) /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct Rate Header - ESA - FLOW";

	proc Sql;
	
	CREATE TABLE &work_lib..SRPRateHeader (
	            srpid char(21),
	            GrpID char(6),
	            StartDate num format=yymmdd10. informat=yymmdd10.,
	            EndDate num format=yymmdd10. informat=yymmdd10., 
	            Type char(12),
	            Yieldable char(5),
	            PriceRelative char(5),
	            DerivedRateCode char(12),
	            IncludesPackage char(5),
	            mktSegment char(50),
	            constraint prim_key_SrpRateHeader PRIMARY KEY (SRPID,Type,StartDate, EndDate)
            );
            
            
		insert into &work_lib..SRPRateHeader  
			select distinct srpcnt.SRPID as SRPID, grpid as GrpID, srpcnt.StrDate as StartDate, srpcnt.EndDate as EndDate,
			    case
					when trim(srpcnt.SRPID) like "LV%" then "Unqualified" 
					when trim(srpcnt.SRPID) like "RL%" then "Unqualified" 
					else "Qualified" 
				    end 
				as Type,
			    case
					when trim(srpcnt.Yield) = "Y" then "true" 
					else "false" 
				    end 
				as Yieldable, 
			    case
					when srpcnt.rtlvl ^= 0 then "true" 
					else "false" 
				    end 
				as PriceRelative,
			    case 
					when "&rtlevel" like "LV%" then cats( "LV" ,srpcnt.rtlvl)
					when "&rtlevel" like "RL%" then cats( "RL" ,srpcnt.rtlvl)
					else "." 
				    end
				as DerivedRateCode,
			    case 
					when "&rms_rates" = "true" then "true" 
					else "false" 
				    end 
				as IncludesPackage,
			    trim(srpcnt.MktCode) as mktSegment 
			from hilstar.srpcnt,hilstar.rates 
			where srpcnt.srpid=rates.srpid and (rates.rtrtlvl in ('0','1','2','3','4','5','6','7','8','')
					or ("%upcase(&esaPriceRelativeSrpHandling.)" like "TRUE" and rates.rtrtlvl = 'S'))
					and srpcnt.status eq "A" and srpcnt.SRPType NE "D" and srpcnt.Days eq 0;
	quit;
	
			        
   proc Sql;
   	CREATE TABLE &work_lib..GRPRateHeader (
		            srpid char(21),
		            GrpID char(6),
		            StartDate num format=yymmdd10. informat=yymmdd10.,
		            EndDate num format=yymmdd10. informat=yymmdd10., 
		            Type char(12),
		            Yieldable char(5),
		            mktSegment char(50),
		            constraint prim_key_GrpRateHeader PRIMARY KEY (SRPID,StartDate, EndDate)
	            );
            
	    insert into &work_lib..GrpRateHeader 
			select distinct trim(grpcvt.SRPID) as SRPID,grpcvt.GRPID as GrpID, grpcvt.strDate as StartDate, grpcvt.endDate as EndDate,
			    case 
					when trim(grpcvt.SRPID) like "LV%" then "Unqualified" 
					when trim(grpcvt.SRPID) like "RL%" then "Unqualified" 
					else "Qualified" 
				    end 
				as Type, 
			    case
					when trim(grpcvt.Yield) = "Y" then "true" 
					else "false" 
				    end 
				as Yieldable, 
			    case
			    		when trim(grpcvt.OrgMktCode) = "" then trim(grpcvt.MktCode)
			    		else trim(grpcvt.OrgMktCode) 
				    end 
		    		as mktSegment 
			from hilstar.grpcvt 
			where grpcvt.status eq "A" and grpcvt.SRPType NE "D";
	quit;  
	
	/*To get Rate Header at SRP Group Level*/
	proc sql;
		insert into &work_lib..RateHeader  
			select grpid as name
				, min(startdate) as startDate format=yymmdd10. 
				, max(endDate) as endDate format=yymmdd10.
				,case 
					when "&yield_currency_code" = "LOCAL" then "&basecurrency."
					else "&yield_currency_code."
				 end as Currency
				,max(type) as Type
				,max(yieldable) as yieldable
				,max(pricerelative) as pricerelative
				,"." as derivedratecode
				,max(includesPackage) as includesPackage
				,"" as Remarks
				,max(mktSegment) as mktSegment
			from &work_lib..SRPRateHeader
			group by grpid;
	quit;

	proc sql;
		insert into &work_lib..RateHeader  
			select grpid as name
				, min(startdate) as startDate format=yymmdd10.
				, max(endDate) as endDate format=yymmdd10.
				, case 
					when "&yield_currency_code" = "LOCAL" then "&basecurrency."
					else "&yield_currency_code."
				 end as Currency
				,max(type) as Type
				,max(yieldable) as yieldable
				,"false" as PriceRelative
				,"." as DerivedRateCode
			    	,"false" as IncludesPackage
				,"" as Remarks
				,max(mktSegment) as mktSegment
			from &work_lib..GrpRateHeader
			group by grpid;
	quit;
	
	*Take care of this based of setallsrpsasyieldableflag;
	%if &all_srps_as_yieldable = FALSE %then %do;
		proc sql;
			delete from &work_lib..RateHeader where yieldable = "false";
		quit;
	%end;
	%else %do;
	   proc sql;
			update &work_lib..RateHeader set yieldable = "true" where yieldable = "false";
		quit;
	%end;
	
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=END, macroname=&macroname.);  
	
%mend;	
