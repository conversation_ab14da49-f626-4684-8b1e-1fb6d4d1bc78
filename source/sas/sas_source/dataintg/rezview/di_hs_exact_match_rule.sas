%macro di_hs_exact_match_rule /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow ";

	data &work_lib..SrpMktMap;
		merge &work_lib..SrpRuleMatch &work_lib..srpexactmatch;
		by srpid startdate;
	run;

	proc datasets library=&work_lib nolist;
   		modify SrpMktMap;                                                                                                                      
      		ic create prim_key_SrpMktMap =primary key(srpid startdate);                                                                                       
   		run;
	quit;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_exact_match_rule;
