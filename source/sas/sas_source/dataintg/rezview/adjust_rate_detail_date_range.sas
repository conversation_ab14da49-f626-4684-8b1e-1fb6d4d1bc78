
%macro adjust_rate_detail_date_range /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Truncate Date Range for Rate Details - ESA - FLOW";

proc sql noprint ;
	select SnapshotDate into :snapDate from &work_lib..RateDefinition;
quit;

data _null_;
	tmpdate = input("&snapDate", yymmdd10.);
	enddt=intnx('week', tmpdate, 104 );
	call symput('maxEDt', enddt);
run;

data &work_lib..ratedetailinput;
set &work_lib..ratedetailinput;
	if endDate > &maxEDt. then endDate = &maxEDt.;
	output;
run;

%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;


