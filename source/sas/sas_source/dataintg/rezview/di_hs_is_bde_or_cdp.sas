%macro di_hs_is_bde_or_cdp /store;
  %let macroname=&SYSMACRONAME;
  %ideas_macro_log (start_end=START, macroname=&macroname.);
  %PUT processCRSBDEEnabled = &processCRSBDEEnabled;
  %IF %UPCASE(&processCRSBDEEnabled) = TRUE %THEN %DO;
	%let snap_etl_file_name = %str(&rms_extract_filename..snap);
 	%ideas_UTIL_TRUNCATE_TABLE(hilstar.exttype);
	   
    data &work_lib..tempexttype (keep= recordtype extType sequence);
        	infile "&rms_filepath\&snap_etl_file_name" delimiter='|' missover DSD;
        	input recordtype $ @;

        	if recordtype='exttype' then do;
            	input extType$ sequence;
            /*The logic to be applied on the data from the file before storing it*/
            /* Check for nubmer of fileds in the record if less than 6 exit with error incorrect number of fields */
            if sum( missing(extType),missing(sequence)) > 0 then do;
                call symputx('error_codes',1221,'l');
                call symputx('err_str','ERROR parsing exttype record','l');
            end;

            output &work_lib..tempexttype;
       	 end;
    run;

    Proc Append base=hilstar.exttype data=&work_lib..tempexttype(drop=recordtype) force;
    Run;

    proc sql noprint;
        select extType into:extractType from hilstar.exttype;
    quit;

    %put &extractType.;

    %if &extractType = FULL %then %do;
        %let isCDP=0;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1302, 'INFO:ExtractType is BDE', '', &req_Id,&err_ct.);
    %end;
    %else %if &extractType = INCR %then %do;
        %let isCDP=1;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1303, 'INFO:ExtractType is CDP', '', &req_Id,&err_ct.);    
    %end;
    %put isCDP = &isCDP;
%END;
%ELSE %DO;
	%let isCDP=0;
	%ideas_util_inc_errors;
    %ideas_util_report_errors(1302, 'INFO:ExtractType is BDE', '', &req_Id,&err_ct.);
%END
  
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_is_bde_or_cdp;
