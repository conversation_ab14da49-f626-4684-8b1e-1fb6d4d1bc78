%macro set_default_mktsegment(defaultmarketsegment,prop_id) /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "ESA - FLOW";

	 proc Sql;
	        update hilstar.yield set new_mkt_code = "&defaultmarketsegment"
	            WHERE trim(new_srp)= "" AND  new_level <> "" AND  hotelid="&prop_id.";
	        update hilstar.yield set old_mkt_code = "&defaultmarketsegment"
	            WHERE trim(old_srp)= "" AND  old_level <> ""  AND  hotelid="&prop_id.";
    	 quit; 
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	
%mend;