%macro update_def_mkt_date /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow ";

	proc sql;
		create table &work_lib..htldef as select srpid,startdate,count(srpid) as count 
			from &work_lib..SrpMktMap group by srpid having count=1 
		and  submcat="&sub_mcat";
	quit;


data &work_lib..SrpMktMap;
        set &work_lib..htldef (rename = (srpid = srpid startdate = startdate));
        modify &work_lib..SrpMktMap key = prim_key_SrpMktMap;
		format attributed_end_dt date9.;
		attributed_end_dt='31DEC2173'd;
		enddate = attributed_end_dt;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                 replace;
            end;
        end;
        _iorc_=0;
        _error_=0;
    run;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend update_def_mkt_date;
