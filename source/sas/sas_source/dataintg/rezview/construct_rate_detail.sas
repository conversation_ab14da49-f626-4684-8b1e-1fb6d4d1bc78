%macro construct_rate_detail /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Construct Rate Details - ESA - FLOW";
	%create_rate_details;
	%create_input_rate_details;
	%adjust_rate_detail_date_range;
	%process_rate_detail;
	%adjust_missing_rates;
	
 %let macroname=&SYSMACRONAME;
 %ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;
