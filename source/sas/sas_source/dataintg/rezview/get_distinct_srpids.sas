%macro get_distinct_srpids /store;
	%let macroname=&SYSMACRONAME;
	%ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow ";

	proc sql;
		create table &work_lib..distsrpids as select distinct srpid from hilstar.srpcnt where srpid NE ""
		union
		select distinct srpid from hilstar.grpcvt where srpid NE ""
		union 
		select distinct new_srp from hilstar.yield where new_srp NE ""
		union 
		select distinct srp from hilstar.books where srp NE "";
	quit;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend get_distinct_srpids;
