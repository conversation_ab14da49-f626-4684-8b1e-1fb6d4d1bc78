%macro di_hs_update_submcat_mapping(hilstar, prop_id, usesubmcatmapping , defaultmarketsegment, isfullpush,rat_client_id)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
	%put "ESA: Flow submcat mapping";

	proc Sql;
        update hilstar.books set usablemktseg=mktseg where isdifferential in (3,4);
    quit;

    proc Sql;
       create table &work_lib..smbookstemp as 
            select a.hotelid, a.resid, a.srp, b.trxChangeDt,a.mktseg from 
                hilstar.books as a left join hilstar.bookdates as b on a.resid=b.resid and a.hotelid=b.hotelid
            where a.hotelid="&prop_id" and mktseg ^="&defaultmarketsegment" and 
                ( a.isdifferential=3 or a.isdifferential=4) and trim(a.srp) ^= "";

				/*Use transaction date instead of bookingdate after booking date fix */
         create table &work_lib..smbooks as 
            select a.hotelid, a.resid, a.srp, b.submcat , a.mktseg from 
                &work_lib..smbookstemp as a inner join &work_lib..srpmktmap as b on a.srp=b.srpid
            where a.trxChangeDt between b.startdate and b.enddate             
            order by hotelid, resid;
     quit;
 
    data hilstar.books;
        set &work_lib..smbooks;
        modify hilstar.books key=prim_key_books;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if "&usesubmcatmapping" EQ 'TRUE' then do;
                    usablemktseg = submcat;
                end;
                else do;
                    mktseg = submcat;
                end;
                isdifferential=1;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records during Sub MCAT mapping" resid;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    /*Now doing Groups*/
    proc Sql;
        create table &work_lib..smgroups as 
            select a.hotelid, a.idate, a.srpid, a.mktcode, b.submcat from 
                hilstar.grpcvt as a inner join &work_lib..srpmktmap as b on a.srpid=b.srpid 
            where trim(a.hotelid)="&prop_id" and 
                a.cdate between b.startdate and b.enddate
            order by a.hotelid, a.Idate, a.srpid;
    quit;

    data hilstar.grpcvt;
        set &work_lib..smgroups;
        modify hilstar.grpcvt key=prim_key_grpcvt;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                OrgMktCode = mktcode;
                mktcode = submcat;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                put "ERROR Should not come here for adding records during Sub MCAT mapping";
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_update_submcat_mapping;
