%macro intg_functions() /store;

	%put "Inside Rezview Intg functions";
	     proc fcmp outlib=work.funcs.test;
	     	      function identifyValidForTrxDateChange(new_mkt_code $, old_mkt_code $,new_srp $,old_srp $,new_level $,old_level $,defaultmarketsegment $) $;
	     	      	if new_srp ne old_srp then 
	     	      		return ("TRUE");
	     	      	else return ("FALSE");	      	 
	     	      endsub;
	     run;

%mend;


	     