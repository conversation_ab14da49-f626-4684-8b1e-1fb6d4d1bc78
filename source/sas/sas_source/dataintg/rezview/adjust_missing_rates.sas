%macro adjust_missing_rates /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);
%put "Adjust Missing Rate Details - ESA - FLOW";

data &work_lib..RateDetail;
set &work_lib..RateDetail;

	if sunday = "." then sunday = -1.00;
	if monday = "." then monday = -1.00;
	if tuesday = "." then tuesday = -1.00;
	if wednesday = "." then wednesday = -1.00;
	if thursday = "." then thursday = -1.00;
	if friday = "." then friday = -1.00;
	if saturday = "." then saturday = -1.00;
	output;
run;

%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=END, macroname=&macroname.);  	

%mend;