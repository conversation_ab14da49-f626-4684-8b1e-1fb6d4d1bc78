%macro di_hs_YieldCurrencyConversion(hilstar, prop_id,numpastdays, numfuturedays,yieldcurrency) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    /* Get the property Currency from &work_lib..Hotel table where days=0; */
    proc Sql noprint;
        select PropCurr into:propertyCurrency from hilstar.hotel where days=0 and hotelid="&prop_id";
    quit;

    %put PropertyCurrency IS &propertyCurrency;
    %let yieldcurrency = %upcase(&yieldcurrency);

    /* Check whether there is any change in PropCurr; */
    %let diffDays=0;

    proc Sql noprint;
        select count(*) into:diffDays from hilstar.hotel where PropCurr NE "&propertyCurrency." or PropCurr = " " 
            and hotelid="&prop_id";
    quit;

    %put DiffDays IS &diffDays;

    %if &diffDays. > 0 %then %do;
        %put "More than one distinct PropCurrency  found in Hotel table.Program Will TERMINATE";
        %let error_str = %str(More than one distinct PropCurrency  found in Hotel table.Program Will TERMINATE);

        %ideas_util_inc_errors;
        %ideas_util_report_errors(1215, 'ERROR:More than one distinct PropCurrency  found in Hotel table.Program Will TERMINATE', '', &req_Id, &err_ct.);
     	%let syscc=5;
        %end;
        
        %if &syscc > 4 %then %do;
                    %goto EXIT;
        %end;

    %PUT "yieldcurrency IS:-" &yieldcurrency.;

    %if &yieldcurrency = LOCAL %then %do;
        %put "Yield currency code is defined as local.All the Rates,Revenues and cost will be considered in local currency only";

        %ideas_util_inc_errors;
        %ideas_util_report_errors(1318, 'Yield currency code is defined as local. All the Rates,Revenues and cost will be considered in local currency only', '', &req_Id, &err_ct.);
    %end;
    %else %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1319, 'Yield currency code is other than LOCAL', '', &req_Id, &err_ct.);
    %end;

	proc Sql;
        update hilstar.yield set Curr="&propertyCurrency" where Curr eq "";    		
    quit;
	

    /* Get the conversionFactor for YIELD_CURR; */
    proc Sql;
        %if &yieldcurrency = LOCAL %then %do;
            UPDATE hilstar.currex set effectiveConversionFactor = conversionFactor;
        %end;
        %else %do;
            %put "Getting Conversion Factor for Yield Currency";
            select conversionFactor into:yconversionFactor from hilstar.currex where currencyId="&yieldcurrency.";
            %put CONVERSIONFACTOR is &yconversionFactor;
			update hilstar.currex set effectiveConversionFactor = ( case when conversionFactor=0 then 0 else &yconversionFactor/conversionFactor end);
     
        %end;
    quit;

data hilstar.yield;
modify hilstar.yield (rename=(Curr=currencyId));
length effectiveConversionFactor 8;
    if _n_ = 1 then do;
         /* use hashes when the tables are small less than 1million records - does this fit in that category; */
         declare hash CurrexAdj(dataset:"hilstar.Currex");
         CurrexAdj.defineKey('currencyId');
         CurrexAdj.defineData('effectiveConversionFactor');
         CurrexAdj.defineDone();
     end;
         rc = CurrexAdj.find(); /* if rc = 0 we found a match ; */
		 if rc=0 then do;
		 	
		 	if new_rmcost NE . then do;
        		new_rmcost = new_rmcost * effectiveConversionFactor;
		 	end;
		 	
			if new_cost NE . then do;
        		new_cost = new_cost * effectiveConversionFactor;
		 	end;
			if old_rmcost NE . then do;
        		old_rmcost = old_rmcost * effectiveConversionFactor;
		 	end;
			if old_cost NE . then do;
        		old_cost = old_cost * effectiveConversionFactor;
		 	end;
			if new_rmcost_raw NE . then do;
        		new_rmcost_raw = new_rmcost_raw * effectiveConversionFactor;
		 	end;
			if old_rmcost_raw NE . then do;
        		old_rmcost_raw = old_rmcost_raw * effectiveConversionFactor;
		 	end;
                replace;
         end;
		 /*As discussed dont do any thing if the currency does not match so no else block*/
  run; 

    %put CDP= &isCDP;

	data hilstar.srpcnt;
		modify hilstar.srpcnt (where = (SRPCur = ""));/* doing an where like this the  data  is not read in and hence less IO; */
			SRPCur="&propertyCurrency";
			replace;
	run;

	data hilstar.grpcvt;
		modify hilstar.grpcvt (where = (SRPCur = ""));
			SRPCur="&propertyCurrency";
			replace;
	run;

    %if &isCDP. =0  %then %do;
        %let currencycode=" ";

        proc Sql noprint;
            SELECT LEFT(PUT(COUNT(DISTINCT currencyID),3.))
                INTO:CURRCNT from hilstar.currex;
            SELECT DISTINCT currencyID 
                INTO:CUR1-:CUR&CURRCNT from hilstar.currex;
           
        quit;

        %do I=1 %TO &CURRCNT;
            %let currencycode="&&CUR&I";
            %put "CURRENCYCODE is->" &currencycode;
            proc Sql;

			/* Changes to avoid update on srpcnt and grpcvt tables as update is a costly operation */
			%if &currencycode NE &propertyCurrency %then %do;
                create table &work_lib..tmpsrps as
                    SELECT DISTINCT srpid
                        from hilstar.srpcnt where hotelid="&prop_id." and srpcur=&currencycode.;

                /* ------MR - TTRS-360 -START------------; */
                /* taking srpids from &work_lib..grpcvt; */
                insert into &work_lib..tmpsrps 
                    select distinct srpid from hilstar.grpcvt where hotelid="&prop_id." and srpcur = &currencycode.;
			%end;
			%else %do;
				%put The Property Currency &propertyCurrency and the Yield Currency code are same &currencycode so no conversion will be done;
			%end;



                /* Update rates table; */
                %if &isCDP. =0 and %ideas_util_nobs(&work_lib..tmpsrps) > 0 %then %do;

					/* Added this step to take care of blank srpcur coulmns in srpcnt and grpcvt */
					insert into &work_lib..tmpsrps 
                    	select distinct srpid from hilstar.srpcnt where hotelid="&prop_id." and srpcur ="";
					insert into &work_lib..tmpsrps 
                    	select distinct srpid from hilstar.grpcvt where hotelid="&prop_id." and srpcur ="";
					/* Added this step to take care of blank srpcur coulmns in srpcnt and grpcvt */

                    select effectiveconversionFactor into:effconversionFactor from hilstar.currex where hotelid="&prop_id." and currencyId=&currencycode.;
                    %put "EFFECTIVE CONVERSION RATE is->" &effconversionFactor;
                    update hilstar.rates set 
                        Monday_1=Monday_1*&effconversionFactor,
						Monday_2=Monday_2*&effconversionFactor, 
                        Tuesday_1=Tuesday_1*&effconversionFactor,
						Tuesday_2=Tuesday_2*&effconversionFactor,
                        Wednesday_1=Wednesday_1*&effconversionFactor,
						Wednesday_2=Wednesday_2*&effconversionFactor,
                        Thursday_1=Thursday_1*&effconversionFactor,
						Thursday_2=Thursday_2*&effconversionFactor,
                        Friday_1=Friday_1*&effconversionFactor,
						Friday_2=Friday_2*&effconversionFactor,
						Saturday_1=Saturday_1*&effconversionFactor,
                        Saturday_2=Saturday_2*&effconversionFactor,
						Sunday_1=Sunday_1*&effconversionFactor,
                        Sunday_2=Sunday_2*&effconversionFactor
                    where srpid in ( select srpid from &work_lib..tmpsrps);

                    /* Update rmsrates; */
                    /*Prafulla - Code fix for Percentage values getting Yield Currency Coverted*/
                  
                    update hilstar.rmsrates set PerNightRate=PerNightRate*&effconversionFactor,PerStayRate=PerStayRate*&effconversionFactor
                        where srpid in ( select srpid from &work_lib..tmpsrps) and type in ('A', 'S');
                       

                    /* update &work_lib..srpcnt; */
                    update hilstar.srpcnt set yieldableVal= yieldableValOrg*&effconversionFactor
                        where srpid in ( select srpid from &work_lib..tmpsrps) and yieldableValOrg NE .;
                %end;
            quit;

        %end;
    %end;

    /* ------MR - TTRS-360 -START------------;
    Updating the rates for Unqulaified Rates(Rtlvl 0 to 8); */

    /* Sandeep Added check for CDP processing (TTRS-2021) Yield currency is again getting applied on rates table &work_lib..when CDP Extract gets processed */
    %if &isCDP. =0 %then %do;

        proc Sql;
            select effectiveConversionFactor into:effconversionFactor from hilstar.currex where currencyId="&propertyCurrency";
            update hilstar.rates set 
                Monday_1=Monday_1*&effconversionFactor, 
				Monday_2=Monday_2*&effconversionFactor, 
               	Tuesday_1=Tuesday_1*&effconversionFactor,
				Tuesday_2=Tuesday_2*&effconversionFactor,
                Wednesday_1=Wednesday_1*&effconversionFactor,
				Wednesday_2=Wednesday_2*&effconversionFactor,
                Thursday_1=Thursday_1*&effconversionFactor,
				Thursday_2=Thursday_2*&effconversionFactor,
                Friday_1=Friday_1*&effconversionFactor,
				Friday_2=Friday_2*&effconversionFactor,
				Saturday_1=Saturday_1*&effconversionFactor,
                Saturday_2=Saturday_2*&effconversionFactor,
				Sunday_1=Sunday_1*&effconversionFactor,
                Sunday_2=Sunday_2*&effconversionFactor
            where rtlvl between 0 and 8;
        quit;

        %put effectiveConversionFactor is &effconversionFactor;
    %end;

    /* Sandeep Added check for CDP processing (TTRS-2021) Yield currency is again getting applied on rates table &work_lib..when CDP Extract gets processed */
    /* ------MR - TTRS-360 -END------------; */
     %EXIT:
        %put Falling in YieldCurrencyConversion as error is = &error_str;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend;
