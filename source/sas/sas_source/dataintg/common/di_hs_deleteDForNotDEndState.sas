%macro delete_D_for_not_DEndState(multiUnitCutOffDate)/store;
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

	proc sql;
		create table &work_lib..lt_bookdate_gnrnum as
		select distinct gnr_num from hilstar.bookdates where bookingdate <= &multiUnitCutOffDate.;
	run;

	proc sql;
		create table &work_lib..gt_cutOff_tmpy as
		select trans_type, gnr_num, date_stamp, time_stamp, resid
		from &work_lib..tmpy where gnr_num not in (select gnr_num from &work_lib..lt_bookdate_gnrnum)
		and date_stamp>&multiUnitCutOffDate.;
	run;

    proc sort data=&work_lib..gt_cutOff_tmpy;
    by gnr_num;
    run;

    data &work_lib..not_D_end_state;
        set &work_lib..gt_cutOff_tmpy;
        by gnr_num;
        if last.gnr_num and first.gnr_num ne last.gnr_num and trans_type ne 'D' then
            output;
    run;

	proc sql;
		delete from &work_lib..tmpy where gnr_num in (select gnr_num from &work_lib..not_D_end_state) and trans_type='D';
	run;
	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend delete_D_for_not_DEndState;