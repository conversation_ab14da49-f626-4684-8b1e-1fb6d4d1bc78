%macro di_hs_pms_load_data(hilstar, prop_id,crs_time_offset,property_time_offset,sas_xle_mapfile,client_code,resetGRPConvAval,ispcrs,
            tenant_database,tenant_server,tenant_db_port,tenant_server_instance,tenant_user,tenant_password,resetSegmentValues,rat_client_id) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %let isinglemktseg=FALSE;
    %put resetSegmentValues=&resetSegmentValues;

    %if (NOT %sysfunc(exist(hilstar.hottr))) %then
        %do;
            %put Generating TABLES using hilstarDDL;
            %di_hs_hilstarddl(prop_id=&prop_id.,client_code=&client_code.);
            %let f_file = 1;
        %end;
    %else
        %do;
            /* Purging of Hotel and RMTYPE table.Delete records where days are greater than 365 
             proc sql;

            *In order to populate new data which starts from -2 days;
             *PS 13 July store the history snap records;
             *delete from hilstar.hotel where days < -365 or days >= -2;
             *TODO:Need to do same for Rmtype and check for no of past days and then finalize.;
             *PS 13 July store the history snap records;
             *delete from hilstar.rmtype where days < -365 or days >= -2;

             quit;
            */
        %end;

    %di_hs_restrict_param_value(multiUnitCutOffDateEnabled, &prop_id., &isMultiUnitCutOffDateEnabled., TRUE);
    %if &syscc > 4 %then
        %do;
            %goto EXIT;
        %end;


    /* Check for the existing value of the parameter in the switch table */

    /*US 3022- Validation - Parameter value for usesubmcatmapping toggle handling -Chaitanya*/
    proc Sql noprint;
        select count(*) into:usesubmcatmappingvalue from hilstar.switch where hotelid="&prop_id." and paramname = 'usesubmcatmapping';
    quit;

    %put usesubmcatmappingvalue= &usesubmcatmappingvalue.;

    %if &usesubmcatmappingvalue. = 0 %then
        %do;

            proc Sql;
                insert into hilstar.switch values ("&prop_id.",'usesubmcatmapping',"&usesubmcatmapping.");
            quit;

            %put The value of syscc = &syscc;

            %if &syscc >4 %Then
                %do;
                    %let syscc=4;
                    %put The value of syscc after set = &syscc;
                %end;
        %end;
    %else
        %do;

            proc Sql noprint;
                select paramvalue into:submcatmappingflag from hilstar.switch where hotelid="&prop_id." and paramname = 'usesubmcatmapping';
            quit;

            %put submcatmappingflag = &submcatmappingflag.;
            %put usesubmcatmapping = &usesubmcatmapping.;

            %if &submcatmappingflag. NE &usesubmcatmapping. %then
                %do;
                    %put "The value of the variable usesubmcatmapping has changed since its last run. Hence program will terminate";
                    %let syscc=6;

                    %ideas_util_report_errors(1231, 'ERROR:Value of the variable usesubmcatmapping has changed since its last run', '', &req_Id,'');
                    %let error_str = %str(ERROR:Value of the variable usesubmcatmapping has changed since its last run);
                %end;
        %end;
	
	%di_validate_multiunit_param(&prop_id.);
	
    /*CN-17Jan - US 1846-Ratchet to handle History extarct from HCRS/PCRS */
    %let isbooksempty=0;
    %let val = %ideas_util_nobs(hilstar.books);

    %if &val.=0 %then
        %do;
            %let isbooksempty = 1;
        %end;
    %else
        %do;
            %let isbooksempty = 0;
        %end;

    proc Sql;
        CREATE table &work_lib..tempswitch (
            hotelid char(10),
            paramname char(50),
            paramvalue char(10),
            constraint prim_key_tempswitch PRIMARY KEY (hotelid, paramname)
            );
        insert into &work_lib..tempswitch values ("&prop_id.",'propertyTZOffSet',"&property_time_offset.");
        insert into &work_lib..tempswitch values ("&prop_id.",'isbooksempty',"&isbooksempty.");
    quit;

	proc sort data=hilstar.switch;
		by hotelid paramname;
	run;

    data hilstar.switch;
        update hilstar.switch &work_lib..tempswitch;
        by hotelid paramname;
    run;

    %di_hs_is_bde_or_cdp;

    /* Sandeep changes for falling if CDP extract is run on empty DB */
    %if &syscc > 4 %then
        %do;
            %goto EXIT;
        %end;

    %if &isCDP.= 1 %then
        %do;
            %let nobs_rates = %ideas_util_nobs(hilstar.rates);
            %let nobs_curr = %ideas_util_nobs(hilstar.currex);

            %if &isbooksempty.=1 and &nobs_rates.= 0 and &nobs_curr.= 0 %then
                %do;
                    %ideas_util_inc_errors;
                    %ideas_util_report_errors(1303, 'ERROR:ExtractType is CDP and this is the first extract to get processed.No BDE processed, No rates and currex records present. First process a BDE extract',
                        '', &req_Id,&err_ct.);

                    data _null_;
                        call symputx('error_codes',1303,'l');
                        call symputx('err_str','ERROR:ExtractType is CDP and this is the first extract to get processed.No BDE processed, first process a BDE extract','l');
                    run;

                    %let syscc=6;
                %end;
        %end;

    /* Sandeep changes for falling if CDP extract is run on empty DB */
    %if &isCDP.= 0 %then
        %do;
            %put "BDE Processing Request";

            %ideas_UTIL_TRUNCATE_TABLE(hilstar.yield);

            /* %ideas_UTIL_TRUNCATE_TABLE(hilstar.rates); */
            /* %ideas_UTIL_TRUNCATE_TABLE(hilstar.currex); */
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.rmsrates);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.snap);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.hothd);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.hottr);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.srpcnt);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.srprm);

            proc Sql;
                create table hilstar.grpcvt_past as 
                    select distinct grpcvt.srpid ,grpcvt.srpid As GroupCode, grpcvt.HotelID,
                        grpcvt.GrpOrg,grpcvt.MktCode,grpcvt.StrDate,grpcvt.MktSegType,grpcvt.SRPTYPE,grpcvt.HousingBy,grpcvt.GrpSlsPer,grpcvt.Idate,grpcvt.status,
                    case 
                        when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                        else grpcvt.srpid
                    end 
                As GroupName, 
                    case 
                        when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                        else grpcvt.srpid
                    end 
                As Description, 
                    "" As MasterGroupCode, "DEFINITE" As GroupStatusCode,grpcvt.MktCode As MarketSegmentCode, grpcvt.StrDate As StartDate,
                    grpcvt.EndDate As EndDate, 
                case 
                    when grpcvt.MktSegType ^= "" then 
                case 
                    when grpcvt.MktSegType ="T" then "TRANS"
                    else "GROUP"
                end 
                    else 
                case 
                    when Grpcvt.SRPTYPE = "C" then "TRANS"
                    else "GROUP"
                end
                end 
            as GroupTypeCode,grpbooking.BookingDate As BookingDate,
                grpcvt.HousingBy As PickupTypeCode,. As CancellationDate,"" As BookingType,grpcvt.GrpSlsPer As SalesPerson,grpcvt.CutOffDate as CutOffDate,grpcvt.CutOffDays as CutOffDays
            from hilstar.grpcvt left join hilstar.grpbooking on 
                (grpcvt.hotelid=grpbooking.hotelid and grpcvt.srpid=grpbooking.srpid )
            left join hilstar.hottr on grpcvt.hotelid=hottr.hotelid where grpcvt.HotelID="&prop_id" and grpcvt.status="A" and grpcvt.SRPType ^="D" 
                order by grpcvt.hotelid,grpcvt.srpid;
            quit;

            %ideas_UTIL_TRUNCATE_TABLE(hilstar.grpcvt);
        %end;

    %if &isCDP.= 1 %then
        %do;
            %put "CDP Processing Request";

            %ideas_UTIL_TRUNCATE_TABLE(hilstar.yield);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.snap);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.hothd);
            %ideas_UTIL_TRUNCATE_TABLE(hilstar.hottr);
        %end;

    %let val = %ideas_util_nobs(hilstar.fullpush);

    %if &val.=1 %then
        %do;

            data hilstar.historyfullpush;
                set hilstar.fullpush;
                modify hilstar.historyfullpush key=prim_key_HistoryFullPush;
                select(_iorc_);
                    when(%sysrc(_sok))
                        do;
                            replace;
                        end;

                    when(%sysrc(_dsenom))
                        do;
                            output;
                        end;

                    otherwise
                        do;
                            put "ERROR: Unexpected value for _IORC_= " _iorc_;
                            put "Program terminating. data step iteration # " _n_;
                            put _all_;
                            stop;
                        end;
                end;

                _iorc_=0;
                _error_=0;
            run;

            %ideas_UTIL_TRUNCATE_TABLE(hilstar.fullpush);
        %end;

    %let yield_etl_file_name = %str(&rms_extract_filename..yield);

    %if %sysfunc(fileexist(&rms_filepath\&rms_extract_filename..yield)) %then
        %do;
            %let yield_etl_file_name = %str(&rms_extract_filename..yield);
        %end;
    %else %if %sysfunc(fileexist(&rms_filepath\&rms_extract_filename..initial_yield)) %then
        %do;
            %let yield_etl_file_name = %str(&rms_extract_filename..initial_yield);
        %end;
    %else
        %do;
            %put The yield file &rms_extract_filename..yield or &rms_extract_filename..initial_yield does not exist.;
        %end;

    %let i = 1;

    %do i = 1 %to 15;
        %if %sysfunc(fileexist("&rms_filepath\&yield_etl_file_name.")) %then
            %do;
                %put The etl file "&rms_filepath\&yield_etl_file_name" exists.;
                %goto YIELD_FILE_EXISTS;
            %end;
        %else
            %do;
                %if %sysfunc(fileexist("&rms_filepath\&rms_extract_filename..initial_yield")) %then
                    %do;
                        %put The etl file "&rms_filepath\&rms_extract_filename..initial_yield" exists.;
                        %goto YIELD_FILE_EXISTS;
                    %end;
                %else
                    %do;
                    %end;
            %end;

        data _null_;
            slept= sleep(1);
        run;

        %put Type two Extract Genration program Slept for "&i" seconds;

        %if &i = 15 %then
            %do;
                %put "15 retry attemps failed. Aborting. The yield file does not exist";

                %ideas_util_inc_errors;
                %ideas_util_report_errors(940, 'ERROR:YIELD File does not exist','', &req_Id, '');
                %let error_str = %str(ERROR:YIELD File does not exist);
                %let etl_status = 1;
                %let syscc=6;
                %goto EXIT; /*retry attempt failed*/
            %end;
    %end;

%YIELD_FILE_EXISTS:

    data &work_lib..tmpfullpush(keep =hotelid) &work_lib..tmpy;
        infile "&rms_filepath\&yield_etl_file_name" delimiter='|' missover DSD;
        input trans_type $ @;

        if trans_type ne 'fullpush' then
            do;
                %if &isbooksempty.=1 and %upcase(&limitedDataBuildEnabled.)=FALSE %then
                    %do;
                        %let isfullp=1;
                        %put "Full Push Detected";
                    %end;

                length new_srp$21 new_type$21 old_srp$21 old_type$21 new_level$14 old_level$14;
                input gnr_num:11.0 date_stamp:mmddyy10. time_stamp hotelid$ log_type$ new_doa:mmddyy10. 
                    bseg_number:11.0 new_srp$ new_type$ new_rooms:11.0 new_nites:11. new_cost:12.3 new_rmcost:12.3 
                    new_level$ new_block$ new_group$ oldseg_number:11.0 old_doa:mmddyy10. old_srp$ old_type$ old_rooms:11.0 
                    old_nites:11.0 old_cost:12.3 old_rmcost:12.3 old_level$ old_block$ old_group$ new_onreq$ char1. new_gtdcode$ 
                    old_onreq$ char1. old_gtdcode$ new_srp_type$ old_srp_type$ new_mkt_code$ old_mkt_code$ New_nadults:11.0 
                    Old_nadults:11.0 New_nchild:BZ11.0 old_nchild:BZ11.0 Curr$ f_nrcan$ f_noshow$ f_pmrelease$ f_walked$ 
                    new_bkg_lvl$ old_bkg_lvl$ new_rmcost_raw:12.3 old_rmcost_raw:12.3 rescode$ resid$ char14. oresid$ char14.;
                format date_stamp new_doa old_doa yymmdd10.;

                if missing(bseg_number) then
                    do;
                        bseg_number=-1;
                    end;

                if missing(oldseg_number) then
                    do;
                        oldseg_number=-1;
                    end;

                /* Check for nubmer of fileds in the record if less than 48 exit with error incorrect number of fields */
                if sum(missing(trans_type),missing(gnr_num),missing(date_stamp),missing(time_stamp),missing(hotelid),missing(log_type),
                    missing(new_doa),missing(bseg_number),missing(new_srp),missing(new_type),missing(new_rooms),missing(new_nites),
                    missing(new_cost),missing(new_rmcost),missing(new_level),missing(new_block),missing(new_group),missing(oldseg_number),
                    missing(old_doa),missing(old_srp),missing(old_type),missing(old_rooms),missing(old_nites),missing(old_cost),missing(old_rmcost),
                    missing(old_level),missing(old_block),missing(old_group),missing(new_onreq),missing(new_gtdcode),missing(old_onreq),
                    missing(old_gtdcode),missing(new_srp_type),missing(old_srp_type),missing(new_mkt_code),missing(old_mkt_code),missing(New_nadults),
                    missing(Old_nadults),missing(New_nchild),missing(old_nchild),missing(Curr),missing(f_nrcan),missing(f_noshow),
                    missing(f_pmrelease),missing(f_walked),missing(new_rmcost_raw),missing(old_rmcost_raw),missing(rescode)) > 0 then
                    do;
                        /* remove rescode resid and oresid from here as it is computed from other fields */
                        call symputx('error_codes',1222,'l');
                        call symputx('err_str','ERROR parsing yield record','l');
                    end;

                /* Compute the rescode from information in other fields. We do this here, instead of in writeData, so we can
                  throw an exception if necessary.*/
                %if &isbooksempty. = 1 %then
                    %do;
                        %if &resetSegmentValues = TRUE %then
                            %do;
                                if oldseg_number > 0 then
                                    do;
                                        oldseg_number=0;
                                    end;

                                if bseg_number > 0 then
                                    do;
                                        bseg_number=0;
                                    end;
                            %end;
                    %end;

                if trans_type = 'A' | trans_type = 'C' & oldseg_number = -1 then
                    do;
                        rescode = 'A';
                    end;
                else if trans_type = 'D' | trans_type='C' & bseg_number = -1 then
                    do;
                        rescode = 'D';
                    end;
                else if trans_type = 'C' & bseg_number NE -1 & oldseg_number NE -1 & bseg_number = oldseg_number then
                    do;
                        rescode = 'C';
                    end;
                else if trans_type='C' & bseg_number NE -1 & oldseg_number NE -1 & bseg_number NE oldseg_number then
                    do;
                        rescode = 'S';
                    end;
                else
                    do;
                        /* ("can't generate valid rescode type from extract data ") */
                        call symputx('error_codes',1241,'l');
                        call symputx('err_str','ERROR parsing yield record cant generate valid rescode type from data','l');
                    end;

                /* Validate the arrival date field. If the rescode is "D" there must be an
                  // old arrival date. All other res codes require a new arrival date.*/
                if rescode='D' then
                    do;
                        if old_doa = '' then
                            do;
                                *"rescode is 'D' and old arrival date is null";
                                call symputx('error_codes',1242,'l');
                                call symputx('err_str','ERROR parsing yield record rescode is D and old arrival date is null','l');
                            end;
                    end;
                else
                    do;
                        if new_doa ='' then
                            do;
                                /* "rescode is '" + rescode+ "' and new arrival date is null" */
                                call symputx('error_codes',1243,'l');
                                call symputx('err_str','ERROR parsing yield record rescode is &rescode and new arrival date is null','l');
                                STOP;
                            end;
                    end;

                /* Set RESID based onm other fields; */
                if bseg_number = -1 then
                    do;
                        resid = gnr_num * 1000 + oldseg_number;
                    end;
                else
                    do;
                        resid = gnr_num * 1000 + bseg_number;
                    end;

                /* Set oresid based on other fields */
                if oldseg_number = -1 then
                    do;
                        oresid = ' ';
                    end;
                else
                    do;
                        oresid = gnr_num * 1000 + oldseg_number;
                    end;

                if trans_type = 'D' then
                    do;
                        if missing(old_doa) or missing(old_nites) or (missing(old_type) and missing(old_mkt_code) ) then
                            delete;
                    end;

                /* outputing the data to the dataset */
                output &work_lib..tmpy;
            end;
    run;

    /* Check whether yield is empty */
    %if &isbooksempty.=1 %then
        %do;
            %if %ideas_util_nobs(&work_lib..tmpy)=0 and %upcase(&limitedDataBuildEnabled.)=FALSE %then
                %do;
                    %let syscc=6;
                    %let error_cd="1300";
                    %let error_str=%str(ERROR: Its a FULLPUSH EXTRACT, but Yield is Empty.Hence program will TERMINATE.);

                    %ideas_util_report_errors(1300, "&error_str", '', &req_Id,'');
                    %goto EXIT;
                %end;
        %end;

    /* Always load the yeild table fresh.; */
    %if &ispcrs. = FALSE %then
        %do;

            proc Sql;
                CREATE table &work_lib..SINGLELETTERMKTSEG AS
                    select gnr_num from &work_lib..tmpy where length(new_mkt_code) = 1 and (new_mkt_code=' ' and length(old_mkt_code)=1 ) order by gnr_num;
            quit;

            %let val = %ideas_util_nobs(&work_lib..SINGLELETTERMKTSEG);

            %if &val. > 0 %then
                %do;
                    %let syscc=6;
                    %let error_cd="1200";
                    %let error_str=%str(ERROR: CRSType is of HILSTAR , but Single Letter MKTSEGMENT Found.Hence program will TERMINATE.);

                    %ideas_util_report_errors(1200, "&error_str", '', &req_Id,'');
                    %goto EXIT;
                %end;
        %end;

    %IF &macroSrpEnabled = TRUE %THEN %DO;
        %di_adjust_special_rate_plans;
    %END;

    %IF %UPCASE(&isMultiUnitCutOffDateEnabled.)=TRUE %THEN
    %DO;
        %di_multi_unit_cutoff_adjustments;
    %END;

    %IF &multiUnitGnrEnabled. = true %THEN
        %DO;
			%di_reset_books_resid;
            %di_multi_unit_res;
        %END;


    Proc Append base=hilstar.yield data=&work_lib..tmpy force;
    Run;

    /* TTRS-4248- Chaitanya-START */
    data hilstar.yield_aud;
        set hilstar.yield;
    run;

    proc sql;
        create table &work_lib..tmpDRecords as 
            select * from hilstar.yield where trans_type = "D";
    quit;

    proc sql;
        delete from hilstar.yield
            where trans_type="D" and resid  in (select distinct yield.resid from hilstar.yield,&work_lib..tmpDRecords where tmpDRecords.hotelid=yield.hotelid and 
                tmpDRecords.resid=yield.resid and yield.date_stamp > tmpDRecords.date_stamp);
    quit;

    /* TTRS-4248- Chaitanya-END */
    Proc Append base=hilstar.fullpush data=&work_lib..tmpfullpush force;
    Run;

    %if error_codes >0 %then
        %do;
            %put error_codes = &error_codes;
            %put err_str = &err_str;
        %end;

    %let rates_etl_file_name = %str(&rms_extract_filename..room_rates);
    %let rates_fp_fname= %str(&rms_filepath\&rates_etl_file_name);

    /* The file ......room_rates is present only in case of BDE extract and hence need to load it only during BDE.; */
    %let isPrimingExtract = %upcase(&isPrimingExtract);
    %put isPrimingExtract = &isPrimingExtract;

    %if &isCDP.=0 %then
        %do;
            %if not %Sysfunc (FileExist("&rms_filepath\&rates_etl_file_name")) %then
                %do;
                    %if &isbooksempty.=1 OR &isPrimingExtract EQ TRUE %then
                        %do;
                            %let syscc=6;
                            %let error_id="1223";
                            %let error_str=%str(ERROR:Physical file does not exist :&rms_filepath\&rates_etl_file_name);

                            %ideas_util_report_errors(1223, "&error_str", '', &req_Id,'');
                        %end;
                    %else
                        %do;
                            %put "Books is not empty but room_rates file is missing.Hence skipping room_rates file.";

                            %ideas_util_inc_errors;
                            %ideas_util_report_errors(1223,'WARN:Books is not empty but room_rates file is missing.Hence skipping room_rates file.', '', &req_id, &err_ct.);

                            /*Chaitanya-Room_rates presence Added -START*/
                            proc Sql;
                                select Ratchet_Property_ID into:rat_prop_id from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;
                            quit;

                            proc Sql;
                                select Ratchet_PRAM_ID into:rat_param_id from tenant.Ratchet_PRAM where Ratchet_PRAM_Name='RatesFileSkippedDays';
                            quit;

                            /* Changes for US3204 updae the snapshot date for non priming extracts only */
                            %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&tenant_server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_password%str(;)database=&tenant_database%str(;)port=&tenant_db_port;
                            %let connect_str=complete="&connect_str";
                            %let tempload_id=%sysfunc(compress(&req_id.,' -'));
                            %let load_id = %sysfunc(substr(&tempload_id.,1,5));

                            proc Sql;
                                select Ratchet_Parameter_Value into:rpv from tenant.Ratchet_Parameter_Value_Mapping
                                    where Ratchet_Property_ID = &rat_prop_id and Ratchet_Parameter_ID=&rat_param_id;
                            quit;

                            %let insertparamvalue='1';

                            %if &sqlrc ne 0 or &sqlobs=0 %then
                                %do;

                                    proc Sql;
                                        connect to odbc (&connect_str autocommit=no connection=shared);
                                        execute(
                                            insert into Ratchet_Parameter_Value_Mapping(Ratchet_Property_ID,Ratchet_Parameter_ID,Ratchet_Parameter_Value) 
                                                values(&rat_prop_id,&rat_param_id,&insertparamvalue);
                                        ) by odbc;
                                        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                            %do;
                                                execute(rollback) by odbc;
                                                disconnect from odbc;

                                                %ideas_util_report_errors(1231, 'Error updating Ratchet data base table: Ratchet_Parameter_Value_Mapping ', '', &req_id, &err_ct.);
                                                %return;
                                            %end;
                                    quit;

                                %end;
                            %else
                                %do;
                                    %let updatedparamvalue = %eval(&rpv+1);
                                    %local paraupds;
                                    %put "The value of " &updatedparamvalue;

                                    data &work_lib..update_ratchet_parameter_value(keep=Ratchet_Property_ID Ratchet_Parameter_ID Ratchet_Parameter_Value);
                                        retain Ratchet_Property_ID Ratchet_Parameter_ID Ratchet_Parameter_Value;
                                        Ratchet_Property_ID = &rat_prop_id;
                                        Ratchet_Parameter_ID = &rat_param_id;
                                        Ratchet_Parameter_Value = strip(&updatedparamvalue);

                                        %if &syserr > 0 %then
                                            %do;
                                                %ideas_util_report_errors(1231, 'ERROR:Error creating update ratchet parameter value  data set: update_ratchet_parameter_value', '', &req_id, &err_ct.);
                                                %return;
                                            %end;
                                    run;

                                    %ideas_trans_upload_tmp(upload_table=&work_lib..update_ratchet_parameter_value,
                                        _name_in_db=paraupds,
                                        like_table=tenant.Ratchet_Parameter_Value_Mapping(drop=Ratchet_Param_Value_Mapping_ID),
                                        tmp_trans_lib=ten_tmp,cnt=0&load_id);

                                    %if &syscc > 4 %then
                                        %do;
                                            %ideas_util_report_errors(1231, 'Failed to update ratchet parameter value mapping into temp table', ''&req_id, &err_ct.)
                                            %Return;
                                        %end;

                                    %let paraupds = ##&paraupds;

                                    proc Sql;
                                        connect to odbc (&connect_str autocommit=no connection=shared);
                                        execute(
                                            UPDATE Ratchet_Parameter_Value_Mapping 
                                                set Ratchet_Parameter_Value = y.Ratchet_Parameter_Value from Ratchet_Parameter_Value_Mapping 
                                                    as x INNER JOIN &paraupds 
                                                    as y ON x.Ratchet_Property_ID=y.Ratchet_Property_ID 
                                                        where x.Ratchet_Property_ID = &rat_prop_id 
                                                            and x.Ratchet_Parameter_ID=&rat_param_id
                                                            ) by odbc;

                                        %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                            %do;
                                                execute(rollback) by odbc;
                                                disconnect from odbc;

                                                %ideas_util_report_errors(1231, 'Error updating Ratchet database table: Ratchet_Parameter_Value_Mapping ', '', &req_id, &err_ct.);
                                                %return;
                                            %end;
                                    quit;

                                %end;

                            /*Chaitanya-Room_rates presence Added -END*/
                        %end;
                %end;
            %else
                %do;
                    %put "Room_rates file is present.";

                    %ideas_UTIL_TRUNCATE_TABLE(hilstar.rates);
                    %ideas_UTIL_TRUNCATE_TABLE(hilstar.currex);

                    proc Sql;
                        select Ratchet_Property_ID into:rat_prop_id from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;
                    quit;

                    proc Sql;
                        select Ratchet_PRAM_ID into:rat_param_id from tenant.Ratchet_PRAM where Ratchet_PRAM_Name="RatesFileSkippedDays";
                    quit;

                    /* Changes for US3204 updae the snapshot date for non priming extracts only */
                    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&tenant_server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_password%str(;)database=&tenant_database%str(;)port=&tenant_db_port;
                    %let connect_str=complete="&connect_str";
                    %let tempload_id=%sysfunc(compress(&req_id.,' -'));
                    %let load_id = %sysfunc(substr(&tempload_id.,1,5));

                    proc Sql;
                        select Ratchet_Parameter_Value into:rpv from tenant.Ratchet_Parameter_Value_Mapping
                            where Ratchet_Property_ID = &rat_prop_id and Ratchet_Parameter_ID=&rat_param_id;
                    quit;

                    %let insertparamvalue= '0';

                    %if &sqlrc ne 0 or &sqlobs=0 %then
                        %do;
                            %put "Values are :";
                            %put &rat_prop_id;
                            %put &rat_param_id;
                            %put &insertparamvalue;

                            proc Sql;
                                connect to odbc (&connect_str autocommit=no connection=shared);
                                execute(
                                    insert into Ratchet_Parameter_Value_Mapping(Ratchet_Property_ID,Ratchet_Parameter_ID,Ratchet_Parameter_Value) 
                                        values(&rat_prop_id,&rat_param_id,&insertparamvalue);
                                ) by odbc;
                                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                    %do;
                                        execute(rollback) by odbc;
                                        disconnect from odbc;

                                        %ideas_util_report_errors(1231, 'Error updating Ratchet database table: Ratchet_Parameter_Value_Mapping ', '', &req_id, &err_ct.);
                                        %return;
                                    %end;
                            quit;

                        %end;
                    %else
                        %do;
                            %let updatedparamvalue= 0;

                            /*New code change */
                            %local paraupds;
                            %put "The value of " &updatedparamvalue;

                            data &work_lib..update_ratchet_parameter_value(keep=Ratchet_Property_ID Ratchet_Parameter_ID Ratchet_Parameter_Value);
                                retain Ratchet_Property_ID Ratchet_Parameter_ID Ratchet_Parameter_Value;
                                Ratchet_Property_ID = &rat_prop_id;
                                Ratchet_Parameter_ID = &rat_param_id;
                                Ratchet_Parameter_Value = strip(&updatedparamvalue);

                                %if &syserr > 0 %then
                                    %do;
                                        %ideas_util_report_errors(1231, 'ERROR:Error creating update ratchet parameter value  data set: update_ratchet_parameter_value', '', &req_id, &err_ct.);
                                        %return;
                                    %end;
                            run;

                            %ideas_trans_upload_tmp(upload_table=&work_lib..update_ratchet_parameter_value,
                                _name_in_db=paraupds,
                                like_table=tenant.Ratchet_Parameter_Value_Mapping(drop=Ratchet_Param_Value_Mapping_ID),
                                tmp_trans_lib=ten_tmp,cnt=4&load_id);

                            %if &syscc > 4 %then
                                %do;
                                    %ideas_util_report_errors(1231, 'Failed to update ratchet parameter value mapping into temp table', ''&req_id, &err_ct.)
                                    %Return;
                                %end;

                            %let paraupds = ##&paraupds;

                            /*New code change */
                            proc Sql;
                                connect to odbc (&connect_str autocommit=no connection=shared);
                                execute(
                                    UPDATE Ratchet_Parameter_Value_Mapping 
                                        set Ratchet_Parameter_Value = y.Ratchet_Parameter_Value from Ratchet_Parameter_Value_Mapping 
                                            as x INNER JOIN &paraupds 
                                            as y ON x.Ratchet_Property_ID = y.Ratchet_Property_ID 
                                                where x.Ratchet_Property_ID = &rat_prop_id 
                                                    and x.Ratchet_Parameter_ID = &rat_param_id

                                                    ) by odbc;

                                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then
                                    %do;
                                        execute(rollback) by odbc;
                                        disconnect from odbc;

                                        %ideas_util_report_errors(1231, 'Error updating Ratchet database table: Ratchet_Parameter_Value_Mapping ', '', &req_id, &err_ct.);
                                        %return;
                                    %end;
                            quit;

                        %end;

                    data &work_lib..temprates (keep = recordtype HotelID rtRtLvl rtlvl srpId roomType captureDate startDate endDate Sunday_1 Sunday_2 Sunday_3 Sunday_4 Sunday_xa 
                        Sunday_xc Monday_1 Monday_2 Monday_3 Monday_4 Monday_xa Monday_xc Tuesday_1 Tuesday_2 Tuesday_3 Tuesday_4 Tuesday_xa 
                        Tuesday_xc Wednesday_1 Wednesday_2 Wednesday_3 Wednesday_4 Wednesday_xa Wednesday_xc Thursday_1 Thursday_2 Thursday_3 
                        Thursday_4 Thursday_xa Thursday_xc Friday_1 Friday_2 Friday_3 Friday_4 Friday_xa Friday_xc Saturday_1 Saturday_2 Saturday_3 
                        Saturday_4 Saturday_xa Saturday_xc recordseq);
                        infile "&rms_filepath\&rates_etl_file_name" delimiter='|' missover DSD;
                        input recordtype $ @;

                        if recordtype='rates' then
                            do;
                                length srpId$21 roomType$21;
                                input HotelID$ rtRtLvl$ rtlvl srpId$ roomType$ captureDate:mmddyy10. startDate:mmddyy10. endDate:mmddyy10. 
                                    Sunday_1:12.3 Sunday_2:12.3 Sunday_3:12.3 Sunday_4:12.3 Sunday_xa:12.3 Sunday_xc:12.3 Monday_1:12.3
                                    Monday_2:12.3 Monday_3:12.3 Monday_4:12.3 Monday_xa:12.3 Monday_xc:12.3 Tuesday_1:12.3 Tuesday_2:12.3 
                                    Tuesday_3:12.3 Tuesday_4:12.3 Tuesday_xa:12.3 Tuesday_xc:12.3 Wednesday_1:12.3 Wednesday_2:12.3 
                                    Wednesday_3:12.3 Wednesday_4:12.3 Wednesday_xa:12.3 Wednesday_xc:12.3 Thursday_1:12.3 Thursday_2:12.3 
                                    Thursday_3:12.3 Thursday_4:12.3 Thursday_xa:12.3 Thursday_xc:12.3 Friday_1:12.3 Friday_2:12.3 Friday_3:12.3 
                                    Friday_4:12.3 Friday_xa:12.3 Friday_xc:12.3 Saturday_1:12.3 Saturday_2:12.3 Saturday_3:12.3 Saturday_4:12.3 
                                    Saturday_xa:12.3 Saturday_xc:12.3;
                                format captureDate startDate endDate yymmdd10.;
                                recordseq = _n_;

                                /* Check for nubmer of fileds in the record if less than 51 exit with error incorrect number of fields; */
                                if sum(missing(recordtype),missing(HotelID),missing(rtRtLvl),missing(rtlvl),missing(srpId),missing(roomType),
                                    missing(captureDate),missing(startDate),missing(endDate),missing(Sunday_1),missing(Sunday_2),missing(Sunday_3),
                                    missing(Sunday_4),missing(Sunday_xa),missing(Sunday_xc),missing(Monday_1),missing(Monday_2),missing(Monday_3),
                                    missing(Monday_4),missing(Monday_xa),missing(Monday_xc),missing(Tuesday_1),missing(Tuesday_2),missing(Tuesday_3),
                                    missing(Tuesday_4),missing(Tuesday_xa),missing(Tuesday_xc),missing(Wednesday_1),missing(Wednesday_2),missing(Wednesday_3),
                                    missing(Wednesday_4),missing(Wednesday_xa),missing(Wednesday_xc),missing(Thursday_1),missing(Thursday_2),missing(Thursday_3),
                                    missing(Thursday_4),missing(Thursday_xa),missing(Thursday_xc),missing(Friday_1),missing(Friday_2),missing(Friday_3),
                                    missing(Friday_4),missing(Friday_xa),missing(Friday_xc),missing(Saturday_1),missing(Saturday_2),missing(Saturday_3),
                                    missing(Saturday_4),missing(Saturday_xa),missing(Saturday_xc)) > 0 then
                                    do;
                                        call symputx('error_codes',1223,'l');
                                        call symputx('err_str','ERROR parsing rates record','l');
                                    end;

                                /* Examine the rtLvl and srpId fiels. One must be null, but not both; */
                                if rtlvl =. & srpId = '' then
                                    do;
                                        /* ("rtlvl and srpid are both null"); */
                                        call symputx('error_codes',1244,'l');
                                        call symputx('err_str','ERROR parsing rates record rtlvl and srpid are both null','l');
                                    end;
                                else if rtlvl NE . && srpId NE '' then
                                    do;
                                        /* ("rtlvl and srpid both contain values ('" + rtLvlStr+ "', '" + srpId + "') one should be null"); */
                                        call symputx('error_codes',1245,'l');
                                        call symputx('err_str','ERROR parsing rates record rtlvl and srpid both contain values one should be null','l');
                                    end;

                                if srpId='' & rtlvl NE . then
                                    do;
                                        temp = put(rtlvl,6.0);
                                        srpId=temp;
                                    end;

                                output &work_lib..temprates;
                            end;
                    run;

                    Proc Append base=hilstar.rates data=&work_lib..temprates force;
                    Run;

                    proc Sql OUTOBS=1 noprint;
                        select HotelID into:hotel_id from hilstar.rates;
                    quit;

                    data hilstar.currex (keep = recordtype captureDate currencyId currencyName conversionFactor displayDecimals hotelid effectiveconversionfactor)
                        hilstar.proptax(keep= HotelID captureDate startDate endDate tax0_amount tax0_type tax0_basis tax0_period);
                        infile "&rms_filepath\&rates_etl_file_name" delimiter='|' missover DSD;
                        input recordtype $ @;

                        if recordtype='currex' then
                            do;
                                length currencyName $28;
                                length hotelid $10;
                                input captureDate:mmddyy10. currencyId$ currencyName$ conversionFactor:12.8 displayDecimals:11.0;
                                format captureDate yymmdd10.;
                                format effectiveconversionfactor 12.8;
                                x=roundz(conversionFactor,.0001);

                                conversionFactor=x;

                                if sum( missing(recordtype),missing(captureDate),missing(currencyId),missing(currencyName),
                                    missing(conversionFactor),missing(displayDecimals)) > 0 then
                                    do;
                                        call symputx('error_codes',1231,'l');
                                        call symputx('err_str','ERROR parsing currex record','l');
                                    end;

                                hotelid="&prop_id";

                                output hilstar.currex;
                            end;
                        else if recordtype='proptax' then
                            do;
                                output=' ';
                                input HotelID$ captureDate:mmddyy10. startDate:mmddyy10. endDate:mmddyy10. tax0_amount:12.2 tax0_type$ tax0_basis$ tax0_period$;
                                format captureDate yymmdd10.;
                                format startDate yymmdd10.;
                                format endDate yymmdd10.;
                                output hilstar.proptax;
                            end;
                    run;

                    %if error_codes > 0 %then
                        %do;
                            %put error_codes = &error_codes;
                            %put err_str = &err_str;
                        %end;

                %end; /* End of room rates file */

            %if &syscc > 4 %then
                %do;
                    %goto EXIT;
                %end;

            /* End of CDP/BDE check for Room_rates file; */
        %end;

    %let snap_etl_file_name = %str(&rms_extract_filename..snap);

    %read_load_snap_file(&rms_filepath,&snap_etl_file_name,&resetGRPConvAval);

    /* Irrespective of BDE/CDP the tables need to be proc aapeneded; */
    Proc Append base=hilstar.hothd data=&work_lib..temphothd force;
    Run;

    Proc Append base=hilstar.hottr data=&work_lib..temphottr force;
    Run;

    data hilstar.hotel;
        set &work_lib..temphotel (rename = (NumRms=NumRms1 Cdate=Cdate1 Days=Days1 
            RmsAuth=RmsAuth1 Ovblvl=Ovblvl1 LOSPattern=LOSPattern1
            MinLOS=MinLOS1 MaxLOS=MaxLOS1 StayMLOS=StayMLOS1 StayLim=StayLim1 CTA=CTA1 CLO=CLO1 
            GTDPol=GTDPol1 RmsSold=RmsSold1 RmsAvail=RmsAvail1 RmsOOO=RmsOOO1 MgrHld=MgrHld1 
            GrpConv=GrpConv1 GrpConvBlk=GrpConvBlk1 GrpConvSld=GrpConvSld1 GrpConvAvl=GrpConvAvl1 
            MktCode=MktCode1 PropCurr=PropCurr1));
        modify hilstar.hotel key=prim_key_hotel;
        NumRms=NumRms1;
        Cdate=Cdate1;
        Days=Days1;
        RmsAuth=RmsAuth1;
        Ovblvl=Ovblvl1;
        LOSPattern=LOSPattern1;
        MinLOS=MinLOS1;
        MaxLOS=MaxLOS1;
        StayMLOS=StayMLOS1;
        StayLim=StayLim1;
        CTA=CTA1;
        CLO=CLO1;
        GTDPol=GTDPol1;
        RmsSold=RmsSold1;
        RmsAvail=RmsAvail1;
        RmsOOO=RmsOOO1;
        MgrHld=MgrHld1;
        GrpConv=GrpConv1;
        GrpConvBlk=GrpConvBlk1;
        GrpConvSld=GrpConvSld1;
        GrpConvAvl=GrpConvAvl1;
        MktCode=MktCode1;
        PropCurr=PropCurr1;
        select(_iorc_);
            when(%sysrc(_sok))
                do;
                    replace;
                end;

            when(%sysrc(_dsenom))
                do;
                    output;
                end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    proc Sort data = &work_lib..temprmtype;
        by HotelID RmType Idate;

    data hilstar.rmtype;
        format RmType $21.;
        set &work_lib..temprmtype(rename = ( NumRms=NumRms1 Cdate=Cdate1 Days=Days1 RmsAuth=RmsAuth1 
            RmsSold=RmsSold1 RmsAvail=RmsAvail1 RmsOOO=RmsOOO1 MgrHld=MgrHld1 
            LOSPattern=LOSPattern1 MinLOS=MinLOS1 MaxLOS=MaxLOS1 CTA=CTA1 CLO=CLO1 RmTypeClass=RmTypeClass1));
        modify hilstar.rmtype key=prim_key_rmtype;
        NumRms=NumRms1;
        Cdate=Cdate1;
        Days=Days1;
        RmsAuth=RmsAuth1;
        RmsSold=RmsSold1;
        RmsAvail=RmsAvail1;
        RmsOOO=RmsOOO1;
        MgrHld=MgrHld1;
        LOSPattern=LOSPattern1;
        MinLOS=MinLOS1;
        MaxLOS=MaxLOS1;
        CTA=CTA1;
        CLO=CLO1;
        RmTypeClass=RmTypeClass1;
        select(_iorc_);
            when(%sysrc(_sok))
                do;
                    replace;
                end;

            when(%sysrc(_dsenom))
                do;
                    output;
                end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    %if &isCDP.=0 %then
        %do;

            Proc Append base=hilstar.srpcnt data=&work_lib..tempsrpcnt force;
            Run;

            Proc Append base=hilstar.srprm data=&work_lib..tempsrprm force;
            Run;

            Proc Append base=hilstar.grpcvt data=&work_lib..tempgrpcvt force;
            Run;

            /* Moved from CDP to BDE processing Defect ID DE1066*/
            proc Sql;
                update hilstar.srpcnt set yieldableValOrg=yieldableVal;
            quit;

        %end;

    %if &isCDP.=1 %then
        %do;
            %put "Inside CDP";

            %load_srpcnt();
            %load_srprm();
            %load_grpcvt();
        %end;

    proc Sql noprint;
        select CapEdDate into:capturedate from hilstar.hottr where hotelid="&prop_id";
        select CapEdTime into:capturetime from hilstar.hottr where hotelid="&prop_id";
        select CapStDate into:captureStdate from hilstar.hottr where hotelid="&prop_id";
        select CapStTime into:captureSttime from hilstar.hottr where hotelid="&prop_id";
    quit;

    data  _null_;
        tmpdate = input("&capturedate", yymmdd10.);
        tmptime = input("&capturetime", hhmmss.);
        call symput('tmpdate1', tmpdate);
        call symput('tmptime1', tmptime);
    run;

    %let hours = %sysfunc(hour(&tmptime1),z2.);
    %let mins = %sysfunc(minute(&tmptime1),z2.);
    %put hours = &hours.;
    %let localdatetime = %sysfunc(DHMS(&tmpdate1,&hours,&mins,00));
    %put localdatetime=&localdatetime;
    %put crs_time_offset=&crs_time_offset;
    %put property_time_offset=&property_time_offset;

    data _null_;
        datetime1 = intnx('seconds', "&localdatetime", "&crs_time_offset");
        datetime2 = intnx('seconds', datetime1, "&property_time_offset");
        call symput('propertydatetime', datetime2);
    run;

    %put propertydatetime=&propertydatetime;

    /* Property Date and time; */
    %let cepdate = %sysfunc(DATEPART(&propertydatetime));
    %let cepday1 = %sysfunc(DAY(&cepdate),z2.);
    %let cepyear1 = %sysfunc(YEAR(&cepdate),z4.);
    %let cepmonth1 = %sysfunc(MONTH(&cepdate),z2.);
    %let cephours = %sysfunc(hour(&propertydatetime),z2.);
    %let cepmins = %sysfunc(minute(&propertydatetime),z2.);
    %let cedate = %str(&cepyear1.-&cepmonth1.-&cepday1.);
    %let cetime = %str(&cephours.:&cepmins.);
    %put cedate=&cedate cetime=&cetime;

    data _null_;
        tempcedate = input("&cedate", yymmdd10.);
        tempcetime = input("&cetime", time5.);
        tempcestdate = input("&captureStdate", yymmdd10.);
        tempcesttime = input("&captureSttime", time5.);
        call symput('cedate1', tempcedate);
        call symput('cetime1', tempcetime);
        call symput('csdate', tempcestdate);
        call symput('cstime', tempcesttime);
    run;


    /*Inserting values into fullpush so keep a record of alkl the full push extracts processed*/
    %if &isbooksempty.=1 %then
        %do;

            proc Sql;
                insert into hilstar.fullpush values( "&rms_extract_filename", &cedate1,&cetime1,"&prop_id");
            quit;

        %end;

    proc Sql;
        update hilstar.hottr set CapStDate=&csdate, CapStTime=&cstime, CapEdDate=&cedate1, CapEdTime=&cetime1 where hotelid="&prop_id";
    quit;

    /* writeSnapDate; */
    %let sdate = %sysfunc(date());
    %let stime = %sysfunc(time());
    %let timestr = %str(00:00:00);

    data _null_;
        array pieces[3] $ 10
            piece1-piece3;

        do i = 1 to 3;
            pieces[i] = scan("&rms_extract_filename.",i,',.! ');
            put i pieces[i];
        end;

        tmpdate = input(pieces[2], yymmdd8.);
        call symput('sdate', tmpdate);
        call symput('timestr', pieces[3]);
    run;

    %let hours = %substr(&timestr,1,2);
    %let mins = %substr(&timestr,3,2);
    %let timestr1 = %str(&hours.:&mins);
    %put &hours &mins &timestr1;
    %put sdate=&sdate.;
    %put propid=&prop_id.;

    data _null_;
        ntime = input("&timestr1",time5.);
        put ntime=;
        call symput('stime', ntime);
    run;

    proc Sql;
        delete from hilstar.snap;
        insert into hilstar.snap set file="&rms_extract_filename.", snapdate=&sdate. ,exdate=&sdate. , starttm=&stime., endtm=&stime., hotelid="&prop_id.";
    quit;

    /* START of Code to read in the Rms_rates.XML START; */
    %put process_rms_rates = &process_rms_rates;
    %put "isCDP=" &isCDP.;
    %put "Just before RMS rates xml";

    %if &isCDP.=0 AND %sysfunc(fileexist(&rms_filepath\&rms_extract_filename..rms_rates.xml)) %then
        %do;
            %let rmsrates_etl_file_name = %str(&rms_extract_filename..rms_rates.xml);
            %put rmsrates_etl_file_name = &rmsrates_etl_file_name;
            filename RRATES "&rms_filepath.\&rmsrates_etl_file_name.";
            %put rms_filepath = &rms_filepath;
            filename SXLEMAP "&sas_xle_mapfile.\hilstarroomrates.map";
            %put sas_xle_mapfile = &sas_xle_mapfile;
            libname RRATES xml xmlmap=SXLEMAP access=READONLY;

            data hilstar.rmsrates;
                SET RRATES.rmsrates;

                if period='N' then
                    do;
                        if action='S' then
                            do;
                                PerStayRate = 0.0;
                            end;
                    end;
                else if period='S' then
                    do;
                        PerStayRate = PerNightRate;

                        if action='S' then
                            do;
                                PerNightRate = 0.0;
                            end;
                    end;
            run;

        %end;
    %else
        %do;
            %put process_rms_rates is &process_rms_rates so rms rates will not be populated;
        %end;

    %put The value of syscc after run = &syscc;

    /* Sandeep changes for falling if CDP extract is run on empty DB */
%EXIT:
    %put Falling in PMS load data as error is = &err_str;

    proc Sql;
        drop table &work_lib..tmpy;
    quit;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_pms_load_data;
