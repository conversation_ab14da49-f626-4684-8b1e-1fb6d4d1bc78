%macro di_hs_check_srpid_validation(hilstar,prop_id,usesrpidsubstitution)/store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %put usesrpidsubstitution=&usesrpidsubstitution.;

    proc Sql;
        create table &work_lib..invalidsrpid as 
            select srpid from &work_lib..tempsrpiddata where srpid not in (select distinct(srpid) from hilstar.srpcnt);
    quit;

    %let val = %ideas_util_nobs(&work_lib..invalidsrpid);

    %if &val. > 0 %then %do;
        %put "Invalid SRPID found in the list of comfigured SRPID. Hence program will terminate";

        %ideas_util_inc_errors;
        %ideas_util_report_errors(1207,'WARN:Invalid SRPID found in the list of comfigured SRPID', '', &req_id, &err_ct.);

        proc Sql;
            insert into &work_lib..errors 
                select 1 as err_ct , 1207 as error_cd , "&req_id." as request_id, "" as error_params , 
                    cats("INVALID SRPID is->",srpid) as error_message from &work_lib..invalidsrpid;
        quit;

    %end;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_check_srpid_validation;
