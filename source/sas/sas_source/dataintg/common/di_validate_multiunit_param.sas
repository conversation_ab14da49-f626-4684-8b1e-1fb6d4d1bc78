%macro di_validate_multiunit_param(prop_id) /store;
  %let macroname=&SYSMACRONAME;
  %ideas_macro_log (start_end=START, macroname=&macroname.);

	proc Sql noprint;
        select count(*) into:multiUnitGnrEnabledvalue from hilstar.switch where hotelid="&prop_id." and paramname = 'multiUnitGnrEnabled';
    quit;

    %put multiUnitGnrEnabledvalue = &multiUnitGnrEnabledvalue.;
	%put multiUnitGnrEnabled = &multiUnitGnrEnabled;

    %if &multiUnitGnrEnabledvalue. EQ 0 AND &multiUnitGnrEnabled EQ true %then
        %do;

            proc Sql;
                insert into hilstar.switch values ("&prop_id.",'multiUnitGnrEnabled',"&multiUnitGnrEnabled");
            quit;

            %put The value of syscc = &syscc;

            %if &syscc >4 %Then
                %do;
                    %let syscc=4;
                    %put The value of syscc after set = &syscc;
                %end;
    %end;
    %else %if &multiUnitGnrEnabledvalue. EQ 1 %then
        %do;

            proc Sql noprint;
                select paramvalue into:multiUnitGnrEnabledflag from hilstar.switch where hotelid="&prop_id." and paramname = 'multiUnitGnrEnabled';
            quit;

            %put multiUnitGnrEnabledflag = &multiUnitGnrEnabledflag;
            %put multiUnitGnrEnabled = &multiUnitGnrEnabled;

            %if &multiUnitGnrEnabledflag NE &multiUnitGnrEnabled %then
                %do;
                    %put "The value of the variable MultiUnitGnrEnabled has changed since its last run. Hence program will terminate";
                    %let syscc=6;

                    %ideas_util_report_errors(1325, 'ERROR:Value of the variable MultiUnitGnrEnabled has changed since its last run', '', &req_Id,'');
                    %let error_str = %str(ERROR:Value of the variable MultiUnitGnrEnabled has changed since its last run);
                %end;
    %end;

	%ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_validate_multiunit_param;
