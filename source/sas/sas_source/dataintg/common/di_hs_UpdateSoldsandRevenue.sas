%macro di_hs_UpdateSoldsandRevenue(hilstar, prop_id,numpastdays, numfuturedays,use_new_rmcost_raw,usemktoccupancyrates,applytax,usesrpidsubstitution) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc Sql NOPRINT;
        select CapEdDate into:capturedate from hilstar.hottr where hotelid="&prop_id";
    quit;

    %put CAPTURE DATE IS &capturedate;

    /* PS 13 July store history snap data and refer to tmppastdays; */

    proc Sql NOPRINT;
        select pastdayscnt into:pdays from hilstar.tmppastdays;
    quit;

    %let numpastdays = &pdays;
    %put numpastdays IS &numpastdays;

    /* PS end changes; */
    data _null_;
        format tmpdate yymmdd10.;
        tmpdate = input("&capturedate", yymmdd10.);
        startdt=intnx('day', tmpdate, "&numpastdays" );
        enddt=intnx('day', tmpdate, "&numfuturedays" );
        call symput('startdate', LEFT(PUT(startdt,yymmdd10.)));
        call symput('enddate', LEFT(PUT(enddt,yymmdd10.)));
    run;

    proc Sql;
        create table &work_lib..inactivesrpids as
            select distinct srpid from hilstar.srpcnt where Srptype='D' and Inv='I';
    quit;

    %put startdate is &startdate, enddate is &enddate;

    %if (NOT %sysfunc(exist(&work_lib..tmpsolds))) %then %do;

        proc Sql;
            create table &work_lib..tmpsolds (
                hotelid char(10) ,
                idate num format=yymmdd10. informat=yymmdd10.,
                mktcode char(50) ,
                roomtype char(21),
                sold num format=11.0,
                remainingSold num format=11.0,
                bookssold num format=11.0,
                roomrevenue num format=12.2,
                booksrevenue num format=12.2,
                constraint prim_key_tmpsolds PRIMARY KEY (hotelid, idate, mktcode, roomtype)
                );
        quit;

    %end;
    %else %do;
        %ideas_UTIL_TRUNCATE_TABLE(&work_lib..tmpsolds);
    %end;

    /* reading group solds and roomrevenue in temporary table tmpsolds from srprm & grpcvt */
    %put applytax= &applytax.;
    %put usemktoccupancyrates= &usemktoccupancyrates.;

    %if &applytax=true %then %do;
        %if &usemktoccupancyrates=true %then %do;

            data &work_lib..tmpsrprmtax;
                length startDate 8;
                length endDate 8;
                length rate1 $10;
                length SUNDAY_1 6;
                length SUNDAY_2 6;
                length MONDAY_1 6;
                length MONDAY_2 6;
                length TUESDAY_1 6;
                length TUESDAY_2 6;
                length WEDNESDAY_1 6;
                length WEDNESDAY_2 6;
                length THURSDAY_1 6;
                length THURSDAY_2 6;
                length FRIDAY_1 6;
                length FRIDAY_2 6;
                length SATURDAY_1 6;
                length SATURDAY_2 6;
                length tax_period $3;

                /* declare variables used in the hash that are no there in the dataset used in set statement; */
                /* -startDate and endDate upfront - else it will fail; */
                set &work_lib..tmp_srpm_ratesmkt (where = (hotelId = "&prop_id."));

                if _n_ = 1 then do;

                    /* use hashes when the tables are small less than 1million records - does this fit in that category; */
                    declare hash Txadj(dataset: "&work_lib..Tax_adjustment",multidata:"y");/* multidata indicates for that key you will have multiple records; */
                    Txadj.defineKey('srpId','roomType');
                    Txadj.defineData('startDate','endDate','SUNDAY_1','SUNDAY_2','MONDAY_1','MONDAY_2','TUESDAY_1',
                        'TUESDAY_2','WEDNESDAY_1','WEDNESDAY_2','THURSDAY_1','THURSDAY_2','FRIDAY_1','FRIDAY_2',
                        'SATURDAY_1','SATURDAY_2','tax_period');
                    Txadj.defineDone();
                end;

                rc = Txadj.find(); /* if rc = 0 we found a match ; */

                do while(rc=0);
                    tax_period=tax_period;

                    if idate >= startDate and idate <= endDate then do;
                       
                        if weekday(iDate) = 1 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = SUNDAY_1;
                            else if ratetype = 'DOUBLE' then rate1=SUNDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=SUNDAY_2;
                        end;

                        if weekday(iDate) = 2 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = MONDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = MONDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=MONDAY_1;
                        end;

                        if weekday(iDate) = 3 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = TUESDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = TUESDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=TUESDAY_1;
                        end;
                        else if weekday(iDate) = 4 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = WEDNESDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = WEDNESDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=WEDNESDAY_1;
                        end;
                        else if weekday(iDate) = 5 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = THURSDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = THURSDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=THURSDAY_1;
                        end;
                        else if weekday(iDate) = 6 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = FRIDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = FRIDAY_2;
                            else if ratetype = 'DEFAULT' then rate1 = FRIDAY_1;
                        end;
                        else if weekday(iDate) = 7 then do;
                            if ratetype = 'SINGLE' then
                                rate1 = SATURDAY_1;
                            else if ratetype = 'DOUBLE' then rate1 = SATURDAY_2;
                            else if ratetype = 'DEFAULT' then rate1=SATURDAY_2;
                        end;

                        output;
                    end;

                    rc = Txadj.find_next(); /* not sure about your logic ...once u find one ..do you want to iterate or stop; */

                    /* the logic here assumes u want to iterate across rates for different days; */
                end;

                keep hotelId srpId roomtype idate ratetype rate1 tax_period;
            run;

        %end;
        %else %if &usemktoccupancyrates=false %then %do;

            data &work_lib..tmpsrprmtax;
                length startDate 8;
                length endDate 8;
                length rate1 $10;
                length SUNDAY_2 6;
                length MONDAY_1 6;
                length TUESDAY_1 6;
                length WEDNESDAY_1 6;
                length THURSDAY_1 6;
                length FRIDAY_1 6;
                length SATURDAY_2 6;
                length tax_period $3;

                /* declare variables used in the hash that are no there in the dataset used in set statement; */
                /* -startDate and endDate upfront - else it will fail; */
                set hilstar.srprm (where = (hotelId = "&prop_id.")); /* doing an where like this the data is not read in and hence less IO; */

                if _n_ = 1 then do;

                    /* use hashes when the tables are small less than 1million records - does this fit in that category; */
                    declare hash Txadj(dataset: "&work_lib..Tax_adjustment",multidata:"y"); /* multidata indicates for that key you will have multiple records; */
                    Txadj.defineKey('srpId','roomType');
                    Txadj.defineData('startDate','endDate','SUNDAY_2','MONDAY_1','TUESDAY_1','WEDNESDAY_1','THURSDAY_1','FRIDAY_1','SATURDAY_2','tax_period');
                    Txadj.defineDone();
                end;

                rc = Txadj.find(); /* if rc = 0 we found a match ; */

                do while(rc=0);
                    if idate >= startDate and idate <= endDate then do;
                        if weekday(iDate) = 1 then
                            rate1 = SUNDAY_2;
                        else if weekday(iDate) = 2 then rate1 = MONDAY_1;
                        else if weekday(iDate) = 3 then rate1 = TUESDAY_1;
                        else if weekday(iDate) = 4 then rate1 = WEDNESDAY_1;
                        else if weekday(iDate) = 5 then rate1 = THURSDAY_1;
                        else if weekday(iDate) = 6 then rate1 = FRIDAY_1;
                        else if weekday(iDate) = 7 then rate1 = SATURDAY_2;
                        output;
                    end;

                    rc = Txadj.find_next(); /* not sure about your logic ...once u find one ..do you want to iterate or stop; */

                    /* the logic here assumes u want to iterate across rates for different days; */
                end;

                keep hotelId srpId roomtype idate rate1 tax_period;
            run;

        %end;

        proc Sql;
            create table &work_lib..srprmtax_j_grpcvt
                as 
                    select a.hotelid, a.srpid, a.roomtype, a.rate1, a.idate, a.tax_period,
                        b.strdate, b.enddate from &work_lib..tmpsrprmtax as a ,hilstar.grpcvt as b 
                    where 
                        a.hotelid=b.hotelid and 
                        a.srpid=b.srpid and
                        a.idate=b.idate 
                        and b.inctxsvc='Y' 
                    order by a.HotelID,a.SRPID,a.RoomType,a.Idate;
        quit;

        %let val = %ideas_util_nobs(&work_lib..srprmtax_j_grpcvt);
        %put "srprmtax_j_grpcvt records =" &val.;

        proc Sql;
            update hilstar.srprm set nettax = 0;
        quit;

        %if &val. GE 1 %then %do;
            %put "INside if. Updating srprm for net tax";

            data hilstar.srprm;
                set &work_lib..srprmtax_j_grpcvt;
                modify hilstar.srprm key=prim_key_srprm;
                select(_iorc_);
                    when(%sysrc(_sok)) do;
                        if tax_period = 'N' then do;
                            tax2= rate1;
                        end;
                        if tax_period = 'S' then do;
                            tax2=0;
                         end;

                        nettax=tax2;
                        replace;
                    end;

                    when(%sysrc(_dsenom)) do;
                        nettax=rate1;
                        output;
                    end;
                end;

                _iorc_=0;
                _error_=0;
            run;

			%di_preserve_srprm_past_data(hilstar, &prop_id.);
			
            proc Sql;
                insert into &work_lib..tmpsolds 
                    SELECT srprm.hotelid as hotelid, srprm.idate as idate, grpcvt.mktcode as mktcode, srprm.roomtype as roomtype, 
                        sum
                        (
                    CASE
                        WHEN srprm.RmsAuth > srprm.RmsSold THEN srprm.RmsAuth 
                        ELSE srprm.RmsSold
                    END )
                AS sold , sum(srprm.RmsAvail) as remainingSold, 0 as bookssold,
                    sum(srprm.RmsAvail * (srprm.rate -srprm.nettax)) as roomrevenue , 0 as booksrevenue
                FROM hilstar.srprm left join hilstar.grpcvt on 
                    srprm.hotelid= grpcvt.hotelid and srprm.SRPID = grpcvt.SRPID 
                    and srprm.Idate = grpcvt.Idate  and grpcvt.status="A"
                WHERE
                    grpcvt.mktcode is not null and
                    srprm.idate >= input("&startdate",yymmdd10.) and srprm.idate <= input("&enddate",yymmdd10.)
                    and grpcvt.srptype ^= 'D'
                GROUP BY srprm.hotelid, srprm.idate, grpcvt.mktcode, srprm.roomtype;
            quit;

        %end;
        %else %do;
			
			%di_preserve_srprm_past_data(hilstar, &prop_id.);
			
            proc Sql;
                insert into &work_lib..tmpsolds 
                    SELECT srprm.hotelid as hotelid, srprm.idate as idate, grpcvt.mktcode as mktcode, srprm.roomtype as roomtype, 
                        sum
                        (
                    CASE
                        WHEN srprm.RmsAuth > srprm.RmsSold THEN srprm.RmsAuth 
                        ELSE srprm.RmsSold
                    END )
                AS sold , sum(srprm.RmsAvail) as remainingSold, 0 as bookssold,
                    sum(srprm.RmsAvail * srprm.rate) as roomrevenue , 0 as booksrevenue
                FROM hilstar.srprm left join hilstar.grpcvt on 
                    srprm.hotelid= grpcvt.hotelid and srprm.SRPID = grpcvt.SRPID 
                    and srprm.Idate = grpcvt.Idate and grpcvt.status="A"
                WHERE 
                    grpcvt.mktcode is not null and 
                    srprm.idate >= input("&startdate",yymmdd10.) and srprm.idate <= input("&enddate",yymmdd10.) and 
                    srprm.hotelid = "&prop_id"
                    and grpcvt.srptype ^= 'D'
                GROUP BY srprm.hotelid, srprm.idate, grpcvt.mktcode, srprm.roomtype;
            quit;

        %end;
    %end;

    %put "Before Inside &work_lib..tmpsolds loop";
    %put "applytax=" &applytax.;

    %if &applytax = false %then %do;
        %put "Inside &work_lib..tmpsolds loop";
		
		%di_preserve_srprm_past_data(hilstar, &prop_id.);
			
        proc Sql;
            insert into &work_lib..tmpsolds 
                SELECT srprm.hotelid as hotelid, srprm.idate as idate, grpcvt.mktcode as mktcode, srprm.roomtype as roomtype, 
                    sum
                    (
                CASE
                    WHEN srprm.RmsAuth > srprm.RmsSold THEN srprm.RmsAuth 
                    ELSE srprm.RmsSold
                END )
            AS sold , sum(srprm.RmsAvail) as remainingSold, 0 as bookssold,
                sum(srprm.RmsAvail * srprm.rate) as roomrevenue , 0 as booksrevenue
            FROM hilstar.srprm left join hilstar.grpcvt on 
                srprm.hotelid= grpcvt.hotelid and srprm.SRPID = grpcvt.SRPID 
                and srprm.Idate = grpcvt.Idate and grpcvt.status="A"
            WHERE 
                grpcvt.mktcode is not null and 
                srprm.idate >= input("&startdate",yymmdd10.) and srprm.idate <= input("&enddate",yymmdd10.) and 
                srprm.hotelid = "&prop_id"
                and grpcvt.srptype ^= 'D'
            GROUP BY srprm.hotelid, srprm.idate, grpcvt.mktcode, srprm.roomtype;
        quit;

    %end;

    proc Sql;
        delete from &work_lib..tmpsolds where mktcode is null;
    quit;

    /* update future solds from books - here we take only transient solds i.e. without pickups; */
    %if &usesrpidsubstitution. = TRUE %then %do;
        %put Inside TRUE lOOP;

        /* USE the column books.usablemktseg from books table; */
        /* update future solds from books - here we take only transient solds i.e. without pickups; */
        proc Sql;
            create table &work_lib..tmpbooks as 
                select books.hotelid, occdate as IDate, books.usablemktseg as mktcode, books.roomtype ,
                    sum(rooms) As bookssold 
                FROM (hilstar.books inner join hilstar.occdates 
                    ON (books.hotelid = occdates.hotelid) AND books.resid=occdates.resid )
                left join hilstar.hottr on books.hotelid=hottr.hotelid
                    WHERE books.hotelid = "&prop_id" AND block NE 'Y' AND rescode NOT IN ('D', 'N') 
                        AND srp NOT IN (select srpid from &work_lib..inactivesrpids)
                        AND occdate > input("&capturedate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                        AND nites ^= 0
                    GROUP BY books.hotelid, occdate, books.usablemktseg, books.roomtype
                        order by books.hotelid, idate, mktcode, books.roomtype;
        quit;

        data &work_lib..tmpsolds;
            set &work_lib..tmpbooks( rename=(bookssold=bookssold1));
            modify &work_lib..tmpsolds key=prim_key_tmpsolds;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    bookssold= bookssold1;
                    replace;
                end;

                when(%sysrc(_dsenom)) do;

                    bookssold= bookssold1;
                    sold=0;
                    remainingSold=0;
                    roomrevenue=0;
                    booksrevenue=0;
                    output;
                end;

                otherwise
                    do;
                        put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                        put 'Program terminating. data step iteration # ' _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* getting solds from books for past days - we take individuals + pickups; */
        proc Sql;
            drop table &work_lib..tmpbooks;
            create table &work_lib..tmpbooks as 
                select books.hotelid, occdate as IDate, books.usablemktseg as mktcode, books.roomtype ,
                    sum(rooms) As bsold 
                FROM (hilstar.books inner join hilstar.occdates 
                    ON (books.hotelid = occdates.hotelid) AND books.resid=occdates.resid )
                left join hilstar.hottr on books.hotelid=hottr.hotelid
                    WHERE books.hotelid = "&prop_id" AND rescode NOT IN ('D', 'N') 
                        AND occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&capturedate",yymmdd10.)
                        AND nites ^= 0
                        AND srp NOT IN (select srpid from &work_lib..inactivesrpids)
                    GROUP BY books.hotelid, occdate, books.usablemktseg, books.roomtype
                        order by books.hotelid, idate, mktcode, books.roomtype;
        quit;

        data &work_lib..tmpsolds;
            set &work_lib..tmpbooks 
                (rename =(bsold=bookssold1 ));
            modify &work_lib..tmpsolds key=prim_key_tmpsolds;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    bookssold= bookssold1;
                    replace &work_lib..tmpsolds;
                end;

                when(%sysrc(_dsenom)) do;
                    bookssold = bookssold1;
                    sold=0;
                    remainingSold=0;
                    roomrevenue=0;
                    booksrevenue=0;
                    output;
                end;

                otherwise
                    do;
                        put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                        put 'Program terminating. data step iteration # ' _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* getting roomrevenue from books for all days(past and future) - we take individuals + pickups; */
        /* PS rmcost_raw handling of costs needs to be corrected; */
        %if &use_new_rmcost_raw = true %then %do;

            proc Sql;
                create table &work_lib..troomrev as 
                    SELECT books.hotelid, occdate as Idate , books.usablemktseg as mktcode, books.roomtype, 
                        sum
                        (
                    CASE
                        WHEN books.setcost > 0 THEN books.setcost
                        ELSE books.rate + books.staycost + books.nightcost
                    END )
                AS revenue 
                    FROM hilstar.books inner join hilstar.occdates 
                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
                        WHERE 
                            books.hotelid = "&prop_id" AND 
                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                            AND rescode NOT IN ('D', 'N') 
                        GROUP BY books.hotelid, occdate, books.usablemktseg , books.roomtype
                            order by books.hotelid, idate, mktcode, books.roomtype;
            quit;

        %end;
        %else %do;

            proc Sql;
                create table &work_lib..troomrev as 
                    SELECT books.hotelid, occdate as Idate , books.usablemktseg as mktcode, books.roomtype, 
                        sum( books.rate ) AS revenue 
                    FROM hilstar.books inner join hilstar.occdates 
                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
                        WHERE 
                            books.hotelid = "&prop_id" AND 
                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                            AND rescode NOT IN ('D', 'N') 
                        GROUP BY books.hotelid, occdate, books.usablemktseg , books.roomtype
                            order by books.hotelid, idate, mktcode, books.roomtype;
            quit;

        %end;
    %end;
    %else %do;
        %put Inside False Loop;

        proc Sql;
            drop table &work_lib..tmpbooks;
            create table &work_lib..tmpbooks as 
                select books.hotelid, occdate as IDate, books.mktseg as mktcode, books.roomtype ,
                    sum(rooms) As bookssold 
                FROM (hilstar.books inner join hilstar.occdates 
                    ON (books.hotelid = occdates.hotelid) AND books.resid=occdates.resid )
                left join hilstar.hottr on books.hotelid=hottr.hotelid
                    WHERE books.hotelid = "&prop_id" AND block NE 'Y' AND rescode NOT IN ('D', 'N') 
                        AND srp NOT IN (select srpid from &work_lib..inactivesrpids)
                        AND occdate > input("&capturedate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                        AND nites ^= 0
                    GROUP BY books.hotelid, occdate, books.mktseg, books.roomtype
                        order by books.hotelid, idate, mktcode, books.roomtype;
        quit;

        data &work_lib..tmpsolds;
            set &work_lib..tmpbooks( rename=(bookssold=bookssold1));
            modify &work_lib..tmpsolds key=prim_key_tmpsolds;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    bookssold= bookssold1;
                    replace;
                end;

                when(%sysrc(_dsenom)) do;

                    bookssold= bookssold1;
                    sold=0;
                    remainingSold=0;
                    roomrevenue=0;
                    booksrevenue=0;
                    output;
                end;

                otherwise
                    do;
                        put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                        put 'Program terminating. data step iteration # ' _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* getting solds from books for past days - we take individuals + pickups; */
        proc Sql;
            drop table &work_lib..tmpbooks;
            create table &work_lib..tmpbooks as 
                select books.hotelid, occdate as IDate, books.mktseg as mktcode, books.roomtype ,
                    sum(rooms) As bsold 
                FROM (hilstar.books inner join hilstar.occdates 
                    ON (books.hotelid = occdates.hotelid) AND books.resid=occdates.resid )
                left join hilstar.hottr on books.hotelid=hottr.hotelid
                    WHERE books.hotelid = "&prop_id" AND rescode NOT IN ('D', 'N') 
                        AND nites ^= 0
                        AND occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&capturedate",yymmdd10.)
                        AND srp NOT IN (select srpid from &work_lib..inactivesrpids)
                    GROUP BY books.hotelid, occdate, books.mktseg, books.roomtype
                        order by books.hotelid, idate, mktcode, books.roomtype;
        quit;

        data &work_lib..tmpsolds;
            set &work_lib..tmpbooks 
                (rename =(bsold=bookssold1 ));
            modify &work_lib..tmpsolds key=prim_key_tmpsolds;
            select(_iorc_);
                when(%sysrc(_sok)) do;
                    bookssold= bookssold1;
                    replace &work_lib..tmpsolds;
                end;

                when(%sysrc(_dsenom)) do;
                    bookssold = bookssold1;
                    sold=0;
                    remainingSold=0;
                    roomrevenue=0;
                    booksrevenue=0;
                    output;
                end;

                otherwise
                    do;
                        put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                        put 'Program terminating. data step iteration # ' _n_;
                        put _all_;
                        stop;
                    end;
            end;

            _iorc_=0;
            _error_=0;
        run;

        /* getting roomrevenue from books for all days(past and future) - we take individuals + pickups; */
        /* PS rmcost_raw handling; */
      %if &applytax= true %then %do;
      
      	%if &use_new_rmcost_raw = true %then %do;

            proc Sql;
                create table &work_lib..troomrev as 
                    SELECT books.hotelid, occdate as Idate , books.mktseg as mktcode, books.roomtype, 
                        sum
                        (
                    CASE
                        WHEN books.setcost > 0 THEN books.setcost - books.netrate
                        ELSE books.rate + books.staycost + books.nightcost - books.netrate
                    END )
                AS revenue 
                    FROM hilstar.books inner join hilstar.occdates 
                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
                        WHERE 
                            books.hotelid = "&prop_id" AND 
                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                            AND rescode NOT IN ('D', 'N')
                            AND nites ^= 0 
                        GROUP BY books.hotelid, occdate, books.mktseg , books.roomtype
                            order by books.hotelid, idate, mktcode, books.roomtype;
            quit;

        %end;
        %else %do;

            proc Sql;
                create table &work_lib..troomrev as 
                    SELECT books.hotelid, occdate as Idate , books.mktseg as mktcode, books.roomtype, 
                        sum( books.rate - books.netrate ) AS revenue 
                    FROM hilstar.books inner join hilstar.occdates 
                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
                        WHERE 
                            books.hotelid = "&prop_id" AND 
                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
                            AND rescode NOT IN ('D', 'N') 
                        GROUP BY books.hotelid, occdate, books.mktseg , books.roomtype
                            order by books.hotelid, idate, mktcode, books.roomtype;;
            quit;

        %end;
     %end;
     %else %do;
     		
     		    	%if &use_new_rmcost_raw = true %then %do;
		
		            proc Sql;
		                create table &work_lib..troomrev as 
		                    SELECT books.hotelid, occdate as Idate , books.mktseg as mktcode, books.roomtype, 
		                        sum
		                        (
		                    CASE
		                        WHEN books.setcost > 0 THEN books.setcost
		                        ELSE books.rate + books.staycost + books.nightcost
		                    END )
		                AS revenue 
		                    FROM hilstar.books inner join hilstar.occdates 
		                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
		                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
		                        WHERE 
		                            books.hotelid = "&prop_id" AND 
		                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
		                            AND rescode NOT IN ('D', 'N')
		                            AND nites ^= 0 
		                        GROUP BY books.hotelid, occdate, books.mktseg , books.roomtype
		                            order by books.hotelid, idate, mktcode, books.roomtype;
		            quit;
		
		        %end;
		        %else %do;
		
		            proc Sql;
		                create table &work_lib..troomrev as 
		                    SELECT books.hotelid, occdate as Idate , books.mktseg as mktcode, books.roomtype, 
		                        sum( books.rate ) AS revenue 
		                    FROM hilstar.books inner join hilstar.occdates 
		                        ON (books.hotelid = occdates.hotelid AND books.resid=occdates.resid) 
		                    left join hilstar.hottr ON books.hotelid=hottr.hotelid
		                        WHERE 
		                            books.hotelid = "&prop_id" AND 
		                            occdate >= input("&startdate",yymmdd10.) AND occdate <= input("&enddate",yymmdd10.)
		                            AND rescode NOT IN ('D', 'N') 
		                        GROUP BY books.hotelid, occdate, books.mktseg , books.roomtype
		                            order by books.hotelid, idate, mktcode, books.roomtype;;
		            quit;
		
        %end;
     		
     
     
     %end;
     
     
     
     
    %end;

    %let usenewrcost = %str(&use_new_rmcost_raw);
    %put usenewrcost = &usenewrcost;

    /* PS rmcost_raw handling */
    data &work_lib..tmpsolds;
        set &work_lib..troomrev;
        modify &work_lib..tmpsolds key=prim_key_tmpsolds;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                booksrevenue =revenue;
                replace &work_lib..tmpsolds;
            end;

            when(%sysrc(_dsenom)) do;
                booksrevenue =revenue;
                sold=0;
                remainingSold=0;
                bookssold=0;
                roomrevenue=0.00;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* updating solds and other information in cattotal from rmtype records */
    data hilstar.cattotal;
        set hilstar.rmtype (rename = (hotelid = hotelid Idate = repdate rmtype=roomtype 
            RmsSold = sold1 NumRms = capacity1 RmsOOO = notavlmnt1 MgrHld = notavloth1));
        drop recordtype Cdate days MinLOS MaxLOS CTA RmsAuth LOSPattern CTA CLO 
            RmTypeClass RmsAvail PropCurr;
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                sold = sold1;
                arrivals = 0;
                departures = 0;
                cancels = 0;
                noshows = 0;
                roomrev = 0;
                foodrev = 0;
                Totalrev = 0;
                capacity=capacity1;
                notavlmnt=notavlmnt1;
                notavloth=notavloth1;
                replace hilstar.cattotal;
            end;

            when(%sysrc(_dsenom)) do;
                sold = sold1;
                arrivals = 0;
                departures = 0;
                cancels = 0;
                noshows = 0;
                roomrev = 0;
                foodrev = 0;
                Totalrev = 0;
                capacity=capacity1;
                notavlmnt=notavlmnt1;
                notavloth=notavloth1;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* updating group solds and group roomrevenue from tmpsolds in cattotal */
    /* as rmtype does not contain information for NON PICKED UP BLOCKS */
    /* so will add remainingsold & revenue for remainingsold from tmpsolds(srprm+grpcvt) ; */
    proc Sql;
        drop table &work_lib..tmptotalsolds;
        create table &work_lib..tmptotalsolds as 
            SELECT hotelid , idate, roomtype, 
                sum(remainingSold) as gsolds , 
                sum( roomrevenue) As roomrevenue , sum( booksrevenue) As booksrevenue
            FROM &work_lib..tmpsolds
                WHERE 
                    idate >= input("&startdate",yymmdd10.) and idate <= input("&enddate",yymmdd10.) and 
                    hotelid = "&prop_id" 
                GROUP BY hotelid, idate, roomtype
                    ORDER BY hotelid, idate, roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmptotalsolds (rename = (hotelid=hotelid idate=repdate roomtype=roomtype
            gsolds=sold1 roomrevenue=groomrevenue booksrevenue=broomrevenue));
        drop gsolds roomrevenue booksrevenue;
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                sold=sold+sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                replace hilstar.cattotal;
            end;

            when(%sysrc(_dsenom)) do;
                sold=sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                _error_=0;
                output;
            end;
        end;
    run;

    /* updating group solds and roomrevenue in csegment from tmpsolds */
    data hilstar.csegment;
        set &work_lib..tmpsolds (rename =(idate=repdate mktcode=segment 
            sold=sold1 ));
        drop sold1 roomrevenue bookssold booksrevenue remainingSold;
        modify hilstar.csegment key = prim_key_csegment;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                sold=sold1+bookssold;
                roomrev= roomrevenue+ booksrevenue;
                arrivals=0;
                departures=0;
                cancels=0;
                noshows=0;
                foodrev = 0;
                Totalrev=0;
                replace hilstar.csegment;
            end;

            when(%sysrc(_dsenom)) do;
                sold=sold1+bookssold;
                roomrev= roomrevenue+ booksrevenue;
                arrivals=0;
                departures=0;
                cancels=0;
                noshows=0;
                foodrev = 0;
                Totalrev=0;
                _error_=0;
                output;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                    stop;
                end;
        end;
    run;

    /* updating solds & other information in ctotal from hotel records */
    data hilstar.ctotal;
        set hilstar.hotel (rename = (hotelid = hotelid Idate = repdate RmsSold = sold1 
            NumRms = capacity1 RmsOOO = notavlmnt1 MgrHld = notavloth1 GrpConvAvl=sold2));
        drop recordtype Cdate days MinLOS MaxLOS CTA MktCode RmsAuth LOSPattern StayMLOS StayLim CTA CLO 
            Ovblvl GTDPol RmsAvail GrpConv GrpConvBlk GrpConvSld PropCurr;
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if sold2 gt 0 then do;
                    sold = sold1+sold2;
                    arrivals = 0;
                    departures = 0;
                    cancels = 0;
                    noshows = 0;
                    roomrev = 0;
                    foodrev = 0;
                    Totalrev = 0;
                    capacity=capacity1;
                    notavlmnt=notavlmnt1;
                    notavloth=notavloth1;
                    replace hilstar.ctotal;
                end;
                else do;
                    sold = sold1;
                    arrivals = 0;
                    departures = 0;
                    cancels = 0;
                    noshows = 0;
                    roomrev = 0;
                    foodrev = 0;
                    Totalrev = 0;
                    capacity=capacity1;
                    notavlmnt=notavlmnt1;
                    notavloth=notavloth1;
                    replace hilstar.ctotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                if sold2 gt 0 then do;
                    sold = sold1+sold2;
                    arrivals = 0;
                    departures = 0;
                    cancels = 0;
                    noshows = 0;
                    roomrev = 0;
                    foodrev = 0;
                    Totalrev = 0;
                    capacity=capacity1;
                    notavlmnt=notavlmnt1;
                    notavloth=notavloth1;
                    output;
                end;
                else do;
                    sold = sold1;
                    arrivals = 0;
                    departures = 0;
                    cancels = 0;
                    noshows = 0;
                    roomrev = 0;
                    foodrev = 0;
                    Totalrev = 0;
                    capacity=capacity1;
                    notavlmnt=notavlmnt1;
                    notavloth=notavloth1;
                    output;
                end;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* Chaitanya-11Jul-START; */
    /* After analysis it has been found the max days in hotel and rmtype is always same; */
    /* FOR CATTOAL CAPACITY FOR FUTURE */

    /* 
    PS: 4 Jan 2012 Capacity for tail end records not getting set rightly

    Situation when below 2 scenarios coincide: 
    1) Hiltons update their inventories only on weekly basis
    so every day the days column value keeps reducing by 1, however idate is maintained until next inventory update 
    2) When a roomtype is deleted, we suddenly stop getting rmtype records in CRS extract.
    3) ratchet keeps all data  given in CRS data  and tries to use as much hotel data  as possible, so it mainatined 
    the data  for this deleted roomtype.

    on 2011-12-21
    CRS data  
    EXCP 0 2012-12-21 366
    RMT1 10 2012-12-21 366
    RMT2 10 2012-12-21 366
    Ratchet data  
    EXCP 0 2012-12-21 366
    RMT1 10 2012-12-21 366
    RMT2 10 2012-12-21 366

    on 2011-12-22
    CRS data on 2011-12-22
    RMT1 10 2012-12-21 365
    RMT2 10 2012-12-21 365
    Ratchet data  
    EXCP 0 2012-12-21 366
    RMT1 10 2012-12-21 365
    RMT2 10 2012-12-21 365

    Conclusion: do not build any logic on Days column data  , just use IDate

    %let maxrmtypedays=0;

    proc sql noprint;
    select max(days) into:maxrmtypedays from hilstar.rmtype;
    quit;

    proc sql noprint;
    select distinct IDate into:maxrmtypedate from hilstar.rmtype where days=&maxrmtypedays;
    quit;

    proc sql;
    create table  tcapmax as
    select HotelID,Rmtype,NumRms,IDate,Days from hilstar.rmtype where days=&maxrmtypedays;
    quit;
    */
    %let imaxrmtypedate=0;
    %let maxrmtypedate=%str("");

    proc Sql noprint;
        select max (IDate) into:imaxrmtypedate from hilstar.rmtype;
    quit;

    data _null_;
        call symput('maxrmtypedate', LEFT(PUT(&imaxrmtypedate,yymmdd10.)));
    run;

    %put imaxrmtypedate is &imaxrmtypedate and maxrmtypedate is &maxrmtypedate;

    proc Sql;
        create table &work_lib..tcapmax as
            select HotelID, Rmtype, NumRms, IDate, Days from hilstar.rmtype where Idate =input("&maxrmtypedate",yymmdd10.);
    quit;

    /*PS 4 Jan 2012 Capacity for tail end records not getting set rightly end changes*/
    proc Sql;
        create table &work_lib..tjoint as
            select a.HotelID, a.Roomtype, b.NumRms, a.RepDate from hilstar.cattotal as a inner join &work_lib..tcapmax as b on 
                a.roomtype=b.rmtype
            WHERE 
                a.repdate > input("&maxrmtypedate",yymmdd10.)
            order by a.hotelid, a.repdate, a.roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tjoint;
        modify hilstar.cattotal key=prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                capacity=NumRms;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                _error_=0;
                output;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                    stop;
                end;
        end;
    run;

    /* FOR CTOTAL CAPACITY FOR FUTURE ROOMS */
    proc Sql NOPRINT;
        select capacity into:cotcapacity from hilstar.ctotal where repdate=input("&maxrmtypedate",yymmdd10.);
    quit;

    proc Sql;
        update hilstar.ctotal set capacity=&cotcapacity
            WHERE 
                repdate > input("&maxrmtypedate",yymmdd10.);
    quit;

    /* FOR CATTOTAL SOLDS FOR FURTURE */
    proc Sql;
        drop table &work_lib..tmptotalsolds;
        create table &work_lib..tmptotalsolds as 
            SELECT hotelid , idate, roomtype, 
                sum(remainingSold+bookssold) as gsolds , 
                sum( roomrevenue) As roomrevenue , sum( booksrevenue) As booksrevenue
            FROM &work_lib..tmpsolds
                WHERE 
                    idate >= input("&maxrmtypedate",yymmdd10.) AND 
                    hotelid = "&prop_id" 
                GROUP BY hotelid, idate, roomtype
                    ORDER BY hotelid, idate, roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmptotalsolds (rename = (hotelid=hotelid idate=repdate roomtype=roomtype
            gsolds=sold1 roomrevenue=groomrevenue booksrevenue=broomrevenue));
        drop gsolds roomrevenue booksrevenue;
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                sold=sold+sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                replace hilstar.cattotal;
            end;

            when(%sysrc(_dsenom)) do;
                sold=sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                _error_=0;
                output;
            end;
        end;
    run;

    /* FOR CTOTAL SOLDS FOR FURTURE */
    proc Sql;
        drop table &work_lib..tmptotalsolds;
        create table &work_lib..tmptotalsolds as 
            SELECT hotelid , idate, 
                sum(remainingSold+bookssold) as gsolds , 
                sum( roomrevenue) As roomrevenue , sum( booksrevenue) As booksrevenue
            FROM &work_lib..tmpsolds
                WHERE 
                    idate >= input("&maxrmtypedate",yymmdd10.) AND 
                    hotelid = "&prop_id" 
                GROUP BY hotelid, idate
                    ORDER BY hotelid, idate;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmptotalsolds (rename = (hotelid=hotelid idate=repdate
            gsolds=sold1 roomrevenue=groomrevenue booksrevenue=broomrevenue));
        drop gsolds roomrevenue booksrevenue;
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                sold=sold+sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                replace;
            end;

            when(%sysrc(_dsenom)) do;
                sold=sold1;
                roomrev=groomrevenue+broomrevenue;
                Totalrev=groomrevenue+broomrevenue;
                _error_=0;
                output;
            end;
        end;
    run;

    /* CHAITANYA-11THjUL-END; */
    /* updating roomrevenue from tmpsolds in ctotal; */
    proc Sql;
        drop table &work_lib..tmptotalsolds;
        create table &work_lib..tmptotalsolds as 
            SELECT hotelid , idate, 
                sum( roomrevenue) As roomrevenue , sum( booksrevenue) As booksrevenue
            from &work_lib..tmpsolds
                WHERE 
                    idate >= input("&startdate",yymmdd10.) AND idate <= input("&enddate",yymmdd10.) AND 
                    hotelid = "&prop_id" 
                GROUP BY hotelid, idate
                    ORDER BY hotelid, idate;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmptotalsolds (rename = (hotelid=hotelid idate=repdate 
            roomrevenue=groomrevenue ));
        drop roomrevenue booksrevenue;
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                roomrev=groomrevenue+booksrevenue;
                Totalrev=groomrevenue+booksrevenue;
                replace hilstar.ctotal;
            end;

            when(%sysrc(_dsenom)) do;
                roomrev=groomrevenue+booksrevenue;
                Totalrev=groomrevenue+booksrevenue;
                _error_=0;
                output;
            end;

            otherwise
                do;
                    put 'ERROR: Unexpected value for _IORC_= ' _iorc_;
                    put 'Program terminating. data step iteration # ' _n_;
                    put _all_;
                    stop;
                end;
        end;
    run;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_UpdateSoldsandRevenue;
