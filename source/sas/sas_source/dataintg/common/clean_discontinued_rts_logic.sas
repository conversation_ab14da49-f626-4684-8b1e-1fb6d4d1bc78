%macro clean_discontinued_rts_logic(hilstar)/store;

	proc sql noprint;
		select max(snapdate) into :caughtupdate from hilstar.snap;
	quit;

	proc sort data=hilstar.rmtype out=&work_lib..rmtypesorted;
		by rmtype cdate idate;
	run;

	data &work_lib..disc_rt (keep=rmtype disc_date);
		set &work_lib..rmtypesorted (rename=(cdate=disc_date));
		by rmtype;
		if last.rmtype and disc_date ne &caughtupdate.;
	run;

    %if %ideas_util_nobs(&work_lib..disc_rt) > 0 %then %do;

        data hilstar.rmtype;
            merge &work_lib..rmtypesorted (in=a)
                    &work_lib..disc_rt (in=b);
            by rmtype;
            if a and b and idate ge disc_date then do;
                Numrms =0;
                RmsAuth=0;
                RmsAvail=0;
            end;
            drop disc_date;
        run;

    %end;

    %else
        %put "No discontinued room types detected!";

%mend clean_discontinued_rts_logic;
