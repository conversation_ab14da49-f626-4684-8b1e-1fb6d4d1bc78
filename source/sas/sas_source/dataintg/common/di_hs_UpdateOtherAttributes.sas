%macro di_hs_UpdateOtherAttributes(hilstar, prop_id,numpastdays, numfuturedays,isfullpush,usesrpidsubstitution,tenant_database, tenant_server, tenant_db_port, tenant_server_instance, tenant_user, tenant_password,rat_client_id) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);
    %local use_mkt_seg;

    proc Sql noprint;
        select CapEdDate into:capturedate from hilstar.hottr where hotelid="&prop_id";
        select pastdayscnt into:pdays from hilstar.tmppastdays;
    quit;

    %let numpastdays = &pdays;
    /* PS 13 July end changes; */
    data _null_;
        format tmpdate yymmdd10.;
        tmpdate = input("&capturedate", yymmdd10.);
        startdt=intnx('day', tmpdate, "&numpastdays" );
        enddt=intnx('day', tmpdate, "&numfuturedays" );
        call symput('startdate', LEFT(PUT(startdt,yymmdd10.)));
        call symput('enddate', LEFT(PUT(enddt,yymmdd10.)));
    run;

    %put CAPTURE DATE IS &capturedate;
    %put numpastdays IS &numpastdays;
    %put startdate is &startdate, enddate is &enddate;

    %if &usesrpidsubstitution. = TRUE %then %do ;
        %let use_mkt_seg=usablemktseg;
        %put Using srp substitution;
    %end;
    %else %do;
        %put Not Using srp substitution;
        %let use_mkt_seg=mktseg;
    %end;

    /**************************** Cattotal update section starts here ***************************************/
    /* update arrivals in cattotal; */
    proc Sql;
        create table &work_lib..inactivesrps as 
            select distinct srpid, Srptype, Inv from hilstar.srpcnt where Srptype='D' and Inv='I' 
                order by srpid;
        select count(*) into:inactivesrp_ct from &work_lib..inactivesrps;
    quit;

    *SASFILE hilstar.books open;
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.doa as doa, books.roomtype, sum(books.rooms) As barrivals 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                        AND rescode NOT IN ('D', 'N') AND nites ^= 0 and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.doa, books.roomtype 
                        order by books.hotelid, books.doa, books.roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmpbooks
            (rename =(hotelid=hotelid doa=repdate roomtype=roomtype ));
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if arrivals ne barrivals then do;
                    arrivals= barrivals;
                    replace hilstar.cattotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                arrivals= barrivals;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update departures in cattotal; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.dod as repdate, books.roomtype, 
                sum(books.rooms) As bdepartures 
            from hilstar.books
                WHERE books.hotelid= "&prop_id" 
                    AND dod >= input("&startdate",yymmdd10.) and dod <= input("&enddate",yymmdd10.)
                    AND rescode NOT IN ('D', 'N') 
                    and nites ^= 0
                    and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                    AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                GROUP BY books.HotelID, books.dod, books.roomtype
                    order by books.hotelid, books.dod, books.roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmpbooks;
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if departures ne bdepartures then do;
                    departures= bdepartures;
                    replace hilstar.cattotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                departures= bdepartures;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update arrivals in ctotal; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.doa as doa, sum(books.rooms) As barrivals 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                        AND rescode NOT IN ('D', 'N') 
                        AND nites ^= 0
                        and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.doa
                        order by hotelid, doa;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid doa=repdate ));
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if arrivals ne barrivals then do;
                    arrivals= barrivals;
                    replace hilstar.ctotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                arrivals= barrivals;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update cancels in cattotal; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.doa as doa, books.roomtype, sum(books.rooms) As bcancels 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                        AND rescode='D' and nites ^= 0
                        and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.doa, books.roomtype
                        order by books.hotelid, books.doa, books.roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid doa=repdate roomtype=roomtype ));
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if cancels ne bcancels then do;
                    cancels= bcancels;
                    replace hilstar.cattotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                cancels= bcancels;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    * update noshows in cattotal;
    proc Sql;
        create table &work_lib..tmpbooks as 

        SELECT books.HotelID as hotelid, books.doa as doa, books.roomtype, 
            sum(books.rooms) As bnoshows 
        from hilstar.books 
            WHERE books.hotelid= "&prop_id" 
                AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                AND rescode='N' and nites ^= 0
                and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
            GROUP BY books.HotelID, books.doa, books.roomtype
                order by books.hotelid, books.doa, books.roomtype;
    quit;

    data hilstar.cattotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid doa=repdate roomtype=roomtype ));
        modify hilstar.cattotal key = prim_key_cattotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if noshows ne bnoshows then do;
                    noshows= bnoshows;
                    replace hilstar.cattotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                noshows= bnoshows;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /**************************** Ctotal update section starts here ***************************************/
    /* update departures in ctotal; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.dod as dod, sum(books.rooms) As bdepartures 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND dod >= input("&startdate",yymmdd10.) and dod <= input("&enddate",yymmdd10.)
                        AND rescode NOT IN ('D', 'N') 
                        AND nites ^= 0
                        and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.dod
                        order by hotelid, dod;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid dod=repdate ));
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if departures ne bdepartures then do;
                    departures= bdepartures;
                    replace hilstar.ctotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                departures= bdepartures;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    * update noshows in ctotal;
    proc Sql;
        create table &work_lib..tmpbooks as 

        SELECT books.HotelID as hotelid, books.doa as doa, sum(books.rooms) As bnoshows 
            from hilstar.books 
                WHERE books.hotelid= "&prop_id" 
                    AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                    AND rescode ='N' and nites ^= 0
                    and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                    AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                GROUP BY books.HotelID, books.doa 
                    order by hotelid, doa;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid doa=repdate ));
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if noshows ne bnoshows then do;
                    noshows= bnoshows;
                    replace hilstar.ctotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                noshows= bnoshows;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update cancels in ctotal; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.doa as doa, sum(books.rooms) As bcancels 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                        AND rescode ='D' and nites ^= 0 
                        and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.doa 
                        order by hotelid, doa;
    quit;

    data hilstar.ctotal;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid doa=repdate ));
        modify hilstar.ctotal key = prim_key_ctotal;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if cancels ne bcancels then do;
                    cancels= bcancels;
                    replace hilstar.ctotal;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                cancels= bcancels;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /**************************** Csegment update section starts here ***************************************/
    /* update arrivals in csegment; */
    proc Sql;
        create table &work_lib..tmpbooks as 

        SELECT books.HotelID as hotelid, books.doa as repdate, books.&use_mkt_seg. as segment, books.roomtype, sum(books.rooms) As barrivals 
            from hilstar.books
                WHERE books.hotelid= "&prop_id" 
                    AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                    and rescode NOT IN ('D', 'N')
                    AND nites ^= 0 
                    and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                    AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                GROUP BY books.HotelID, books.doa, books.&use_mkt_seg., books.roomtype 
                    order by hotelid, repdate, segment, roomtype;
				
    quit;

    data hilstar.csegment;
        set &work_lib..tmpbooks;
        modify hilstar.csegment key = prim_key_csegment;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if arrivals ne barrivals then do;
                    arrivals= barrivals;
                    replace hilstar.csegment;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                arrivals= barrivals;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update departures in csegment; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.dod as repdate, books.&use_mkt_seg. as segment, books.roomtype, 
                sum(books.rooms) As bdepartures 
            from hilstar.books 
                WHERE books.hotelid= "&prop_id" 
                    AND dod >= input("&startdate",yymmdd10.) and dod <= input("&enddate",yymmdd10.)
                    AND rescode NOT IN ('D', 'N') 
                    AND nites ^= 0
                    and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                    AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                GROUP BY books.HotelID, books.dod, books.&use_mkt_seg., books.roomtype
                    order by hotelid, repdate, segment, roomtype;
    quit;

    data hilstar.csegment;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid repdate=repdate segment=segment roomtype=roomtype ));
        modify hilstar.csegment key = prim_key_csegment;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if departures ne bdepartures then do;
                    departures= bdepartures;
                    replace hilstar.csegment;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                departures= bdepartures;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    /* update cancels in csegment; */
    proc Sql;
        create table &work_lib..tmpbooks as 
            SELECT books.HotelID as hotelid, books.doa as repdate, books.&use_mkt_seg. as segment, books.roomtype, sum(books.rooms) As bcancels 
                from hilstar.books 
                    WHERE books.hotelid= "&prop_id" 
                        AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                        AND rescode='D'
                        AND nites ^= 0 
                        and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                        AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
                    GROUP BY books.HotelID, books.doa, books.&use_mkt_seg., books.roomtype 
                        order by hotelid, repdate, segment, roomtype;
    quit;

    data hilstar.csegment;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid repdate=repdate segment=segment roomtype=roomtype ));
        modify hilstar.csegment key = prim_key_csegment;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if cancels ne bcancels then do;
                    cancels= bcancels;
                    replace hilstar.csegment;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                cancels= bcancels;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    * update noshows in csegment;
    proc Sql;
        create table &work_lib..tmpbooks as 

        SELECT books.HotelID as hotelid, books.doa as repdate, books.&use_mkt_seg. as segment, books.roomtype, 
            sum(books.rooms) As bnoshows 
        from hilstar.books 
            WHERE books.hotelid= "&prop_id" 
                AND doa >= input("&startdate",yymmdd10.) and doa <= input("&enddate",yymmdd10.)
                AND rescode='N' and nites ^= 0
                and (&inactivesrp_ct=0 or ( &inactivesrp_ct>0 
                AND srp NOT IN (select distinct srpid from &work_lib..inactivesrps ))) 
            GROUP BY books.HotelID, books.doa, books.&use_mkt_seg., books.roomtype 
                order by hotelid, repdate, segment, roomtype;
    quit;

    data hilstar.csegment;
        set &work_lib..tmpbooks 
            (rename =(hotelid=hotelid repdate=repdate segment=segment roomtype=roomtype ));
        modify hilstar.csegment key = prim_key_csegment;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                if noshows ne bnoshows then do;
                    noshows= bnoshows;
                    replace hilstar.csegment;
                end;
            end;

            when(%sysrc(_dsenom)) do;
                noshows= bnoshows;
                output;
            end;
        end;

        _iorc_=0;
        _error_=0;
    run;

    *update entry from snap to csnap- mark the snapshot as processed;
    data hilstar.csnap;
        set hilstar.snap (rename =(file=file1 exdate=exdate1 starttm=starttm1 endtm=endtm1));
        modify hilstar.csnap key=prim_key_csnap;
        select(_iorc_);
            when(%sysrc(_sok)) do;
                file=file1;
                exdate=exdate1;
                starttm=starttm1;
                endtm=endtm1;
                replace hilstar.csnap;
            end;

            when(%sysrc(_dsenom)) do;
                file=file1;
                exdate=exdate1;
                starttm=starttm1;
                endtm=endtm1;
                output;
            end;
        end;

        _error_=0;
        _iorc_=0;
    run;

    proc Sql;
        select exdate into:edate from hilstar.snap where hotelid="&prop_id";
        select endtm into:etime from hilstar.snap where hotelid="&prop_id";
	        select Ratchet_Property_ID into:rat_prop_id from tenant.Ratchet_Property where Ratchet_Property_Code="&prop_id" and Ratchet_Client_ID= &rat_client_id;

    quit;

    /* Changes for US3204 updae the snapshot date for non priming extracts only */
    %let connect_str = driver={SQL Server Native Client 10.0}%str(;)server=&tenant_server\&tenant_server_instance%str(;)uid=&tenant_user%str(;)pwd=&tenant_password%str(;)database=&tenant_database%str(;)port=&tenant_db_port;
    %let connect_str=complete="&connect_str";
    %let tempload_id=%sysfunc(compress(&req_id.,' -'));
    %let load_id = %sysfunc(substr(&tempload_id.,1,5));

    %if &isPrimingExtract EQ FALSE %then %do;

        proc Sql;
            select Capture_End_date into:ced from tenant.Ratchet_Snap_Hottr
                where Ratchet_Property_ID = &rat_prop_id;
        quit;

        %if &sqlrc ne 0 or &sqlobs=0 %then %do;

            proc Sql;
                insert into tenant.Ratchet_Snap_Hottr(Ratchet_Property_ID,Capture_End_Date,Capture_End_Time) values(&rat_prop_id,"&edate","&etime");
            quit;

        %end;
        %else %do;
			%local snaphottr;
            proc Sql;
                create table &work_lib..update_ratchet_snap_hottr as
                    select "&edate" as Capture_End_Date,"&etime" as Capture_End_time,&rat_prop_id as Ratchet_Property_ID from hilstar.snap where hotelid="&prop_id";
            quit;
            %if %upcase(&useRegularTableAsTempInSAS.)=TRUE %then
                %do;
                    %let snaphottr = snap_hottr_stg;
                    %ideas_flush_temp_records(table_to_flush=snap_hottr_stg,rat_prop_id=&rat_prop_id,
                        connect_str=&connect_str);
                    %ideas_trans_append(data_table=&work_lib..update_ratchet_snap_hottr,trans_table=snap_hottr_stg,
                        trans_lib=tenant);
                %end;
            %else
                %do;
                    %ideas_trans_upload_tmp(upload_table=&work_lib..update_ratchet_snap_hottr,
                        _name_in_db=snaphottr,
                        like_table=tenant.Ratchet_Snap_Hottr(drop=Snap_Hottr_ID),
                        tmp_trans_lib=ten_tmp,cnt=9&load_id);
                    %let snaphottr = ##&snaphottr;
                %end;

			%if &syscc > 4 %then %do;
      			%ideas_util_report_errors(1231, 'Failed to update Ratchet Snap Hottr into temp table', ''&req_id, &err_ct.)
   				%Return;
 			%end;

            proc Sql;
                connect to odbc (&connect_str autocommit=no connection=shared);
                execute(
                    UPDATE Ratchet_Snap_Hottr set Capture_End_Date = y.Capture_End_Date from Ratchet_Snap_Hottr as x INNER JOIN &snaphottr as y 
						ON x.Ratchet_Property_ID = y.Ratchet_Property_ID where x.Ratchet_Property_ID = &rat_prop_id;
                ) by odbc;

                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                    execute(rollback) by odbc;
                    disconnect from odbc;

                    %ideas_util_report_errors(1231, 'Error updating Ratchet_Snap_Hottr data base table: Ratchet_Snap_Hottr ', '', &req_id, &err_ct.);
                    %return;
                %end;

                execute(
                    UPDATE Ratchet_Snap_Hottr set Capture_End_Time = y.Capture_End_time from Ratchet_Snap_Hottr as x INNER JOIN &snaphottr as y 
						ON x.Ratchet_Property_ID = y.Ratchet_Property_ID where x.Ratchet_Property_ID = &rat_prop_id;
                ) by odbc;

                %if NOT (&sysdbrc eq 0 or "&sysdbrc" eq "") %then %do;
                    execute(rollback) by odbc;
                    disconnect from odbc;

                    %ideas_util_report_errors(1231, 'Error updating Ratchet_Snap_Hottr data base table: Ratchet_Snap_Hottr ', '', &req_id, &err_ct.);
                    %return;
                %end;
            quit;           
        %end;
    %end;

    *SASFILE hilstar.books close;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_UpdateOtherAttributes;
