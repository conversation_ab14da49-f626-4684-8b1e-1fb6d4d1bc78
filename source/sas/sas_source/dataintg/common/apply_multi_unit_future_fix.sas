%macro apply_multi_unit_future_fix /store;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    data &work_lib..tmpfeature;
       featureid=3;
       featurename="MultiUnit";
       featureapplied="true";
    output;

    proc append base=hilstar.feature data=&work_lib..tmpfeature force;
        run;

    %put "Multi Unit Future Fix has not been applied earlier, hence applying it now";

    %if %sysfunc(exist(hilstar.books)) %then %do;

	    	proc Sql;

                /* Create temp table from books and bookdates by selectin specific columns */
                create table &work_lib..tmp_books_formatted_records
                    as select b.resid as resid,
                    bd.gnr_num as gnr,
                    mod(input(b.resid, 14.0),1000) as bseg,
                    bd.bookingdate as bookingdate
                from hilstar.books as b inner join  hilstar.bookdates as bd
                on b.resid=bd.resid order by bd.gnr_num, b.resid;

                /* Create tmp table of all multiunit records from above result */
                create table &work_lib..tmp_multiunit_records
                as select gnr,max(input(resid, 14.0)) as multi_resid from &work_lib..tmp_books_formatted_records
                where bseg > 0 and bookingdate > &multiUnitCutOffDate.  group by gnr ;

             quit;

            %if %ideas_util_nobs(&work_lib..tmp_multiunit_records) > 0 %then
            %do;
                 proc Sql;
                    /* Delete overlapping records */
                    create table &work_lib..tmp_multiunit_skip_records
                    as select gnr from &work_lib..tmp_books_formatted_records where bookingdate <= &multiUnitCutOffDate.
                    and gnr in (select gnr from &work_lib..tmp_multiunit_records);

                    /* Create tmp table of all multiunit records that need to be deleted */
                    create table &work_lib..tmp_delete_multiunit as select resid,gnr from &work_lib..tmp_books_formatted_records
                    where gnr in (select gnr from &work_lib..tmp_multiunit_records)
                    and resid not in (select put(multi_resid,14.0) from &work_lib..tmp_multiunit_records);


                quit;

                    %if %ideas_util_nobs(&work_lib..tmp_multiunit_skip_records) > 0 %then
                    %do;
                                proc Sql;

                                    /* delete all overlapping records */
                                    delete from &work_lib..tmp_multiunit_records where gnr in (select gnr from &work_lib..tmp_multiunit_skip_records);
                                    delete from &work_lib..tmp_delete_multiunit where gnr in (select gnr from &work_lib..tmp_multiunit_skip_records);

                                quit;

                                %put "Multi Unit Future Fix skipped overlapping records";

                    %end;

                    %if %ideas_util_nobs(&work_lib..tmp_delete_multiunit) > 0 %then
                    %do;
                            proc Sql;

                                /* delete all but latest multunit resid records from books*/
                                delete from hilstar.books where resid in (select resid from &work_lib..tmp_delete_multiunit);
                            quit;

                            proc Sql;
                                /* delete all but latest multunit resid records from bookdates*/
                                delete from hilstar.bookdates where resid in (select resid from &work_lib..tmp_delete_multiunit);
                            quit;

                            %put "Multi Unit Future Fix deleted future records";

                             proc Sql;
                                  delete from hilstar.multiyield where gnr_num in (select gnr from &work_lib..tmp_delete_multiunit);
                             quit;

                             %put "Multi Unit Future Fix deleted hilstar.multiyield records after cuttoff and non in progress";

                    %end;

                    %if %ideas_util_nobs(&work_lib..tmp_multiunit_records) > 0 %then
                    %do;

                             proc Sql;

                                /* update the resid of the latest multiunit record to zero in books table */
                                update hilstar.books set resid = put(input(put(input(resid, 14.0)/1000,14.),14.0)*1000,14.)
                                where resid in (select put(multi_resid,14.0) from &work_lib..tmp_multiunit_records);
                             quit;

                             proc Sql;

                                /* update the resid of the latest multiunit record to zero in bookdates table */
                                update hilstar.bookdates set resid = put(input(put(input(resid, 14.0)/1000,14.),14.0)*1000,14.)
                                where resid in (select put(multi_resid,14.0) from &work_lib..tmp_multiunit_records);

                             quit;

                            %put "Multi Unit Future Fix updated resid for future";

                    %end;

            %end;

            %put "Multi Unit Future Fix created temp tables";



	%end;
 %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend apply_multi_unit_future_fix;
