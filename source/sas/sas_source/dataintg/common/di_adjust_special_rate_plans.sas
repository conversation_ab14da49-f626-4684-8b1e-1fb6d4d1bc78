%macro di_adjust_special_rate_plans /store;
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

    data _null_;
      length temp $2000;
      temp = cat('"',tranwrd("&adjustedSpecialRatePlans.","|",'","'),'"');
      call symput('adjustedSrps',temp);
    run;
    %put &adjustedSrps.;

        proc sql;
        create table &work_lib..booksLastValues as
         select b.resid, b.revenue, b.srp, b.mktseg
            from hilstar.books as b inner join &work_lib..tmpy t
            on  b.resid = t.resid
            where (t.new_srp in(&adjustedSrps) and b.srp not in(&adjustedSrps) )
            or (t.old_srp in(&adjustedSrps) and b.srp not in(&adjustedSrps) and trans_type="D");
        quit;

    %if %ideas_util_nobs(&work_lib..booksLastValues) > 0 %then %do;
        proc datasets lib = &work_lib. nolist nodetails;
            modify tmpy;
            index create resid;
        quit;

        data &work_lib..tmpy;
        set &work_lib..booksLastValues;
            do until(eof);
                modify &work_lib..tmpy key = resid end = eof;
                if _IORC_ then _ERROR_ = 0;
                else do;
                   if trans_type="D" then do;
                      old_srp = srp;
                      old_mkt_code = mktseg;
                      old_rmcost_raw = revenue;
                   end;
                   else do;
                      new_srp = srp;
                      new_mkt_code = mktseg;
                      new_rmcost_raw = revenue;
                   end;
                    replace;
                end;
            end;
        run;
     %end;
     %else %do;
       %put No eligible records found for the configured special rate plans;
     %end;

     %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_adjust_special_rate_plans;