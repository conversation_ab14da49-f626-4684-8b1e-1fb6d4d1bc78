%macro di_hs_generate_group_extract(hilstar,filePathName,prop_id,capturedate,startdate,enddate,applytax) /store;
    %let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc Sql;
        create table &work_lib..distinctSrpID as 
            select distinct grpcvt.srpid as GroupCode from ( hilstar.grpcvt left join hilstar.grpbooking on 
                grpcvt.hotelid=grpbooking.hotelid and grpcvt.srpid=grpbooking.srpid )
            left join hilstar.hottr on grpcvt.hotelid=hottr.hotelid where grpcvt.HotelID="&prop_id" and grpcvt.Idate between 
                input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt.status="A" and grpcvt.SRPType ^="D" 
                and strDate ge input("&capturedate",yymmdd10.)
            order by GroupCode;
    quit;

    %let val = %ideas_util_nobs(hilstar.grpcvt_past);

    %if &val. GE 1 %then %do;

        proc Sql;
            create table &work_lib..distinctSrpID_Past as 
                select distinct grpcvt_past.srpid as GroupCode from ( hilstar.grpcvt_past left join hilstar.grpbooking on 
                    grpcvt_past.hotelid=grpbooking.hotelid and grpcvt_past.srpid=grpbooking.srpid )
                left join hilstar.hottr on grpcvt_past.hotelid=hottr.hotelid where grpcvt_past.HotelID="&prop_id" and grpcvt_past.Idate between 
                    input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt_past.status="A" and grpcvt_past.SRPType ^="D" 
                    and strDate ge input("&capturedate",yymmdd10.)
                order by GroupCode;
        quit;

        data &work_lib..cancelled_srpid;
            merge &work_lib..distinctSRPID_past(in = a)
                &work_lib..distinctSRPID(in = b);
            by GroupCode;

            if a and not b then
                output &work_lib..cancelled_srpid;
        run;

    %end;

    proc Sql;
        drop table &work_lib..GroupMaster;
        create table &work_lib..GroupMaster (
            GroupCode char(21),
            GroupName char(30),
            Description char(30),
            MasterGroupCode char(10),
            GroupStatusCode char(10),
            MarketSegmentCode char(50),
            StartDate num format=yymmdd10. informat=yymmdd10.,
            EndDate num format=yymmdd10. informat=yymmdd10.,
            GroupTypeCode char(5),
            BookingDate num format=yymmdd10. informat=yymmdd10.,
            PickupTypeCode char(4),
            CancellationDate num format=yymmdd10. informat=yymmdd10.,
            BookingType char(10),
            SalesPerson char(30),
            CutOffDate num format=yymmdd10. informat=yymmdd10.,
            CutOffDays num format=11.,
            constraint prim_key_GroupMaster PRIMARY KEY (GroupCode)
            );
    quit;

    proc Sql;
        drop table &work_lib..tempGroupMaster;
        create table &work_lib..tempGroupMaster (
            GroupCode char(21),
            GroupName char(30),
            Description char(30),
            MasterGroupCode char(10),
            GroupStatusCode char(10),
            MarketSegmentCode char(50),
            StartDate num format=yymmdd10. informat=yymmdd10.,
            EndDate num format=yymmdd10. informat=yymmdd10.,
            GroupTypeCode char(5),
            BookingDate num format=yymmdd10. informat=yymmdd10.,
            PickupTypeCode char(4),
            CancellationDate num format=yymmdd10. informat=yymmdd10.,
            BookingType char(10),
            SalesPerson char(30),
            CutOffDate num format=yymmdd10. informat=yymmdd10.,
            CutOffDays num format=11.,
            constraint prim_key_tempGroupMaster PRIMARY KEY (GroupCode)
            );
    quit;

    %let val = %ideas_util_nobs(&work_lib..cancelled_srpid);

    %if &val. GE 1 %then %do;

        proc Sql;
            insert into &work_lib..tempGroupMaster
                select distinct grpcvt_past.srpid As GroupCode,
                    case
                        when grpcvt_past.GrpOrg ^= "" then grpcvt_past.GrpOrg
                        else grpcvt_past.srpid
                    end
                As GroupName,
                    case
                        when grpcvt_past.GrpOrg ^= "" then grpcvt_past.GrpOrg
                        else grpcvt_past.srpid
                    end
                As Description,
                    "" As MasterGroupCode, "CANCELLED" As GroupStatusCode,grpcvt_past.MktCode As MarketSegmentCode, grpcvt_past.StrDate As StartDate,
                    grpcvt_past.EndDate As EndDate,
                case
                    when grpcvt_past.MktSegType ^= "" then
                case
                    when grpcvt_past.MktSegType ="T" then "TRANS"
                    else "GROUP"
                end
                    else
                case
                    when grpcvt_past.SRPTYPE = "C" then "TRANS"
                    else "GROUP"
                end
                end
            as GroupTypeCode,grpbooking.BookingDate As BookingDate,
                grpcvt_past.HousingBy As PickupTypeCode,input("&capturedate",yymmdd10.) As CancellationDate,"" As BookingType,grpcvt_past.GrpSlsPer As SalesPerson,grpcvt_past.CutOffDate as CutOffDate,grpcvt_past.CutOffDays as CutOffDays
            from hilstar.grpcvt_past,hilstar.srprm,hilstar.grpbooking where
                grpcvt_past.hotelid=grpbooking.hotelid and grpcvt_past.srpid=grpbooking.srpid
                and grpcvt_past.Idate between
                input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.)
                and
                grpcvt_past.status="A" and grpcvt_past.SRPType ^="D" and grpcvt_past.groupcode in ( select GroupCode from &work_lib..cancelled_srpid);
        quit;

    %end;

    proc Sql;
        insert into &work_lib..GroupMaster select distinct grpcvt.srpid As GroupCode,
            case
                when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                else grpcvt.srpid
            end
        As GroupName,
            case
                when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                else grpcvt.srpid
            end
        As Description,
            "" As MasterGroupCode, "DEFINITE" As GroupStatusCode,grpcvt.MktCode As MarketSegmentCode, grpcvt.StrDate As StartDate,
            grpcvt.EndDate As EndDate,
        case
            when grpcvt.MktSegType ^= "" then
        case
            when grpcvt.MktSegType ="T" then "TRANS"
            else "GROUP"
        end
            else
        case
            when Grpcvt.SRPTYPE = "C" then "TRANS"
            else "GROUP"
        end
        end
    as GroupTypeCode,grpbooking.BookingDate As BookingDate,
        grpcvt.HousingBy As PickupTypeCode,. As CancellationDate,"" As BookingType,grpcvt.GrpSlsPer As SalesPerson,grpcvt.CutOffDate as CutOffDate,grpcvt.CutOffDays as CutOffDays
    from ( hilstar.grpcvt INNER JOIN hilstar.srprm on grpcvt.hotelid=srprm.hotelid AND grpcvt.srpid=srprm.srpid
        AND grpcvt.idate=srprm.idate ) left join hilstar.grpbooking on
        (grpcvt.hotelid=grpbooking.hotelid and grpcvt.srpid=grpbooking.srpid )
    left join hilstar.hottr on grpcvt.hotelid=hottr.hotelid where grpcvt.HotelID="&prop_id" and grpcvt.Idate between
        input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt.status="A" and grpcvt.SRPType ^="D" ;
    quit;

    %if &val. GE 1 %then %do;

        DATA &work_lib..mergedData;
            MERGE &work_lib..tempgroupmaster &work_lib..groupmaster;
            by groupcode;
        run;

        data &work_lib..groupmaster;
            set &work_lib..mergedData;
        run;
    %end;

    %if %upcase(&enableReusableGroupSrps.) eq TRUE %then %do;
        data &work_lib..updatedGroupMaster;
            stop;
            set &work_lib..groupmaster;
        run;
        proc Sql;
            insert into &work_lib..updatedGroupMaster select distinct grpcvt.srpid As GroupCode,
                case
                    when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                    else grpcvt.srpid
                end
            As GroupName,
                case
                    when grpcvt.GrpOrg ^= "" then grpcvt.GrpOrg
                    else grpcvt.srpid
                end
            As Description,
                "" As MasterGroupCode, "DEFINITE" As GroupStatusCode,grpcvt.MktCode As MarketSegmentCode, grpcvt.StrDate As StartDate,
                grpcvt.EndDate As EndDate,
            case
                when grpcvt.MktSegType ^= "" then
            case
                when grpcvt.MktSegType ="T" then "TRANS"
                else "GROUP"
            end
                else
            case
                when Grpcvt.SRPTYPE = "C" then "TRANS"
                else "GROUP"
            end
            end
        as GroupTypeCode,grpbooking.BookingDate As BookingDate,
            grpcvt.HousingBy As PickupTypeCode,. As CancellationDate,"" As BookingType,grpcvt.GrpSlsPer As SalesPerson,grpcvt.CutOffDate as CutOffDate,grpcvt.CutOffDays as CutOffDays
        from hilstar.grpcvt left join hilstar.grpbooking on
            grpcvt.hotelid=grpbooking.hotelid and grpcvt.srpid=grpbooking.srpid
        left join hilstar.hottr on grpcvt.hotelid=hottr.hotelid where grpcvt.HotelID="&prop_id" and grpcvt.Idate between
            input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt.status="A" and grpcvt.SRPType ^="D"
            and strDate ge input("&capturedate",yymmdd10.)
            and grpcvt.SRPID not in ( select GroupCode from &work_lib..groupmaster);
        quit;

        proc append base=&work_lib..groupmaster data=&work_lib..updatedGroupMaster;
        run;
    %end;

    proc Sql;
        drop table &work_lib..GroupBlock;
        create table &work_lib..GroupBlock (
            GroupCode char(21),
            OccupancyDate num format=yymmdd10. informat=yymmdd10.,
            AccommodationTypeCode char(21),
            RoomsBlocked num format=11.,
            RoomsPickedup num format=11.,
            RoomsOriginallyBlocked num format=11.,
            Rate num format=12.2
            );
    quit;

    %put "GroupBlock applytax = " &applytax.;

    %if &applytax = true %then %do;
        %put "Inside GROUPBlock applytax";

        proc Sql;
            insert into &work_lib..GroupBlock select srprm.srpid As GroupCode,srprm.Idate As OccupancyDate,srprm.roomType As AccommodationTypeCode, 
                srprm.rmsauth As RoomsBlocked,srprm.rmssold As RoomsPickedup, srprm.rmsauth As RoomsOriginallyBlocked, 
            case 
                when srprm.rmsauth ^= 0 then srprm.Rate-srprm.nettax
                else 0
            end 
        As Rate
            from (hilstar.grpcvt left join hilstar.srprm on grpcvt.hotelid=srprm.hotelid and grpcvt.srpid=srprm.srpid and grpcvt.idate=srprm.idate) 
                left join hilstar.hottr on srprm.hotelid=hottr.hotelid where srprm.HotelID="&prop_id" and grpcvt.idate 
                    between input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt.status="A" and grpcvt.SRPType ^="D";

        quit;

    %end;
    %else %do;

        proc Sql;
            insert into &work_lib..GroupBlock select srprm.srpid As GroupCode,srprm.Idate As OccupancyDate,srprm.roomType As AccommodationTypeCode, 
                srprm.rmsauth As RoomsBlocked,srprm.rmssold As RoomsPickedup, srprm.rmsauth As RoomsOriginallyBlocked, 
            case 
                when srprm.rmsauth ^= 0 then srprm.Rate
                else 0
            end 
        As Rate
            from (hilstar.grpcvt left join hilstar.srprm on grpcvt.hotelid=srprm.hotelid and grpcvt.srpid=srprm.srpid and grpcvt.idate=srprm.idate) 
                left join hilstar.hottr on srprm.hotelid=hottr.hotelid where srprm.HotelID="&prop_id" and grpcvt.idate 
                    between input("&startdate",yymmdd10.) and input("&enddate",yymmdd10.) and grpcvt.status="A" and grpcvt.SRPType ^="D";

        quit;

    %end;
	
	%if &use_group_past_data = TRUE %then %do;
		%di_group_extract_pd(hilstar,&prop_id.,&capturedate.,&startdate.,&enddate.,&applytax.);
	%end;

    data _null_;
        FILE "&filePathName" dsd dlm='|' MOD;
        set &work_lib..GroupMaster;
        Record_Type='_GM_';
        %let CancellationDate1="";
        %let CutOffDate1="";

        IF CutOffDate ='.' 
            then do;
            IF CancellationDate ='.' 
                then do;
                PUT Record_Type $ GroupCode $ GroupName $ Description $ MasterGroupCode $ GroupStatusCode $ MarketSegmentCode $ 
                    StartDate:yymmdd10. EndDate:yymmdd10. GroupTypeCode $ BookingDate $ PickupTypeCode $ CancellationDate1$ BookingType$ SalesPerson$ CutOffDate1$ CutOffDays;
            end;
            else do;
                PUT Record_Type $ GroupCode $ GroupName $ Description $ MasterGroupCode $ GroupStatusCode $ MarketSegmentCode $ 
                    StartDate:yymmdd10. EndDate:yymmdd10. GroupTypeCode $ BookingDate $ PickupTypeCode $ CancellationDate:yymmdd10. BookingType$ SalesPerson$ CutOffDate1$ CutOffDays;
            end;
        end;
        else do;
            IF CancellationDate ='.' 
                then do;
                PUT Record_Type $ GroupCode $ GroupName $ Description $ MasterGroupCode $ GroupStatusCode $ MarketSegmentCode $ 
                    StartDate:yymmdd10. EndDate:yymmdd10. GroupTypeCode $ BookingDate $ PickupTypeCode $ CancellationDate1$ BookingType$ SalesPerson$ CutOffDate:yymmdd10. CutOffDays;
            end;
            else do;
                PUT Record_Type $ GroupCode $ GroupName $ Description $ MasterGroupCode $ GroupStatusCode $ MarketSegmentCode $ 
                    StartDate:yymmdd10. EndDate:yymmdd10. GroupTypeCode $ BookingDate $ PickupTypeCode $ CancellationDate:yymmdd10. BookingType$ SalesPerson$ CutOffDate:yymmdd10. CutOffDays;
            end;
        END;
    run;

    data _null_;
        FILE "&filePathName" dsd dlm='|' MOD;
        set &work_lib..GroupBlock;
        Record_Type='_GB_';
        PUT Record_Type $ GroupCode $ OccupancyDate:yymmdd10. AccommodationTypeCode $ RoomsBlocked:11. RoomsPickedup:11. RoomsOriginallyBlocked:11. 
            Rate:12.2;
    run;

    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_generate_group_extract;
