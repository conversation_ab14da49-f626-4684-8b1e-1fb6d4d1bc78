%macro clean_discontinued_room_types (lib=in_xml)/store;

    %local req_id app_start_time app_end_time runtime;
    %global work_lib work_dir; 

    %let app_start_time=%sysfunc(datetime());
    %let runTime=0;
    %PUT ---------------------------------------------------------------------;
    %PUT ------------- Starting Discontinued Room Types Clean Up ------------;
    %PUT ---------------------------------------------------------------------;
    %PUT;
    
    data _null_;
        set &lib..RequestHeader;
        call symputx('property_id_number',PropertyId,'|');
        call symputx('req_id',requestId,'l');
        call symputx('tenant_server',tenant_server,'l');
        call symputx('tenant_server_instance',tenant_server_instance,'l');
        call symputx('tenant_password',tenant_password,'l');
        call symputx('tenant_user',tenant_user,'l');
        call symputx('tenant_database',tenant_database,'l');
        call symputx('tenant_db_port',tenant_db_port,'l');
        call symputx('dataset_path',dataset_path,'l');
        stop;
    run;
      
    %if &syscc > 4 %then %do;
        %unquote(%nrstr(%%)ideas_util_log_msg(
            msg="Failed to read request header from xml",lvl=5,macro=&sysmacroname));
        %goto EXIT;
    %end;

    /*Creating required parameters from discontinuedRoomTypesRequestDataType table*/
    data _null_;
        set &lib..DiscontinuedRoomTypesRequestType;
        call symputx('prop_id',propertyCode,'l');
        call symputx('client_code', clientCode,'l');
        call symputx('sas_data_setpath', sasDataSetPath,'l');
        stop;
    run;

    %if &syscc > 4 %then
        %do;
            %unquote(%nrstr(%%)ideas_util_log_msg(
                msg="Failed to read Ratchetextract request from xml",lvl=5,macro=&sysmacroname));
            %goto EXIT;
        %end;

    %if &work_library.=work %then %do;
        %let work_lib=work;
        %let work_path=%sysfunc(pathname(work));
    %end;
    %else %do;
        %let work_lib=&work_library.;
        %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
        %let work_path_root=&work_path_drive./sas;

        %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp;

        %ideas_util_create_dir(dir=&client_code.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&client_code.;

        %ideas_util_create_dir(dir=&prop_id.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&client_code./&prop_id.;

        %ideas_util_create_dir(dir=work,base_path=&work_path_root.);
        %let work_path=&work_path_drive./sas/temp/&client_code./&prop_id./work;

        %if &memlib_option. = memlib %then %do;
            libname &work_lib "&work_path." memlib;
        %end;
        %else %do;
            libname &work_lib "&work_path.";
        %end;
    %end;
   
    %if &ratchet_use_local_stg=YES %then %do;
        %let work_path_drive=%substr (%sysfunc(pathname(work)),1,2);
        %let work_path_root=&work_path_drive./sas;

        %ideas_util_create_dir(dir=temp,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp;

        %ideas_util_create_dir(dir=&client_code.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&client_code.;

        %ideas_util_create_dir(dir=&prop_id.,base_path=&work_path_root.);
        %let work_path_root=&work_path_drive./sas/temp/&client_code./&prop_id.;

        %ideas_util_create_dir(dir=hilstar,base_path=&work_path_root.);
        %let hilstar_work_path=&work_path_drive./sas/temp/&client_code./&prop_id./hilstar;
        %let hilstar_perm_path=&sas_data_setpath.&client_code.\&prop_id.;

        %if &memlib_option. = memlib %then %do;
            libname hilstar "&hilstar_work_path" memlib;
        %end;
        %else %do;
            libname hilstar "&hilstar_work_path";
        %end;

        libname hilperm "&hilstar_perm_path";

        %if (%sysfunc(exist(hilperm.hottr))) %then %do;

            proc Copy in=hilperm out=hilstar
                CLONE 
                CONSTRAINT=YES 
                INDEX=YES;
            run;

        %end;
  
    %end;
    %else %do;
	    %let hilstar_work_path=&sas_data_setpath.\&client_code.\&prop_id.;
		%let hilstar_perm_path=&sas_data_setpath.\&client_code.\&prop_id.;
        libname hilstar "&hilstar_work_path.";
    %end;

	data &work_lib..errors;
        length err_ct error_cd 8 request_id error_params error_message $ 200;
        stop;
    run;

    %if &syscc > 4 %then %do;
        %put Problem after setting hilstar libname;

        data &work_lib..failure;
            length error_cd 8 request_id error_params error_message $200;
            request_id = "&req_id";
            error_cd = 1329;
            error_params = "";
            error_message = "Failed to attach to hilstar library";
        run;

        proc append base=&work_lib..errors data=&work_lib..failure;
        run;

        %goto EXIT;
    %end;

	%clean_discontinued_rts_logic(hilstar);

	%if &syscc > 4 %then %do;
        %ideas_util_inc_errors;
        %ideas_util_report_errors(1328, 'Clean up of discontinued room types failed', '', &req_id, &err_ct.);
        %goto EXIT;
    %end;	


%EXIT:
    data _null_;
        FILE resXml;
        put '<?xml version="1.0" encoding="UTF-8"?>';
        put '<SASResponse xmlns="http://xml.common.pacman.tetris.ideas.com/schema/discontinuedroomtypes/response/v1" ';
        put 'xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ';
        put 'xsi:schemaLocation="http://xml.common.pacman.tetris.ideas.com/schema/discontinuedroomtypes/response/v1/discontinuedRoomTypesResponse.xsd ">';
        put "<ResponseHeader> <PropertyId>&property_id_number</PropertyId>" @;
        put "<operationName>discontinuedroomtypes</operationName>" @;
        put "<requestId>&req_id</requestId>";
        stop;
    run;

    proc sort data=&work_lib..errors;
        by request_id error_cd;
    run;

    data _null_;
        FILE resXml MOD;
        set &work_lib..errors end=eof;
        by error_cd;

        if _N_ = 1 then
            put "<Errors>";

        if first.error_cd then
            put '<Error id="' error_cd '">';
        put "<ErrorMessage>" error_message "</ErrorMessage>";
        put "<ErrorData>" error_params "</ErrorData>";

        if last.error_cd then
            put "</Error>";

        if eof then
            put "</Errors>";
    run;

    data _null_;
        FILE resXml MOD;
        put "</ResponseHeader><DiscontinuedRoomTypesResponse>";
    run;
    data _null_;
        FILE resXml MOD;
		set &work_lib..disc_rt;
        put "<roomType>" rmtype "</roomType>";
    run;
    data _null_;
        FILE resXml MOD;
        put "</DiscontinuedRoomTypesResponse></SASResponse>";
    run;

    %let app_end_time=%sysfunc(datetime());
	%let runTime=%sysfunc(round(&app_end_time-&app_start_time, .05));

	%if &work_lib. ne work %then %do;
	    proc Datasets library = &work_lib. kill memtype=data;
	    quit;
	%end;

	%if &ratchet_use_local_stg. eq YES and &syscc <= 4  %then %do;
	    proc Copy in=hilstar out=hilperm
	        CLONE 
	        CONSTRAINT=YES 
	        INDEX=YES;
	    run;

	    proc Datasets library = hilstar kill memtype=data;
	    quit;
	%end;


%mend clean_discontinued_room_types;
