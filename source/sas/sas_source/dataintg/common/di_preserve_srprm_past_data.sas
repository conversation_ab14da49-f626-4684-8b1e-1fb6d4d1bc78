%macro di_preserve_srprm_past_data(hilstar, prop_id) /store;
%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=START, macroname=&macroname.);

/* DE5674 Hilton Group Block info is not correctly populated */
			%put "use_group_past_data=" &use_group_past_data;
			%if &use_group_past_data = TRUE %then %do;
			/* Truncate srprmpd in BDE only */
                %if &isCDP.=0 %then
                    %do;
                        %IDEAS_UTIL_TRUNCATE_TABLE(hilstar.srprmpd);
                    %end;
                proc Sql noprint;
					          select CapEdDate into:capturedate from hilstar.hottr where hotelid="&prop_id.";
                quit;
                /* add or update srprmpd */
                data hilstar.srprmpd;
                    modify hilstar.srprmpd hilstar.srprm;
                    by SRPID RoomType Idate;
                    if _iorc_ = %sysrc(_sok) then
                        do;
                            replace;
                        end;
                    else if _iorc_ = %sysrc(_dsenmr) then
                        do;
                            _error_=0;
                            _iorc_=0;
                            if idate <= input("&capturedate.",yymmdd10.) then
                            do;
                                output;
                            end;
                        end;
                run;

				proc Sql;
					delete from hilstar.srprm where idate <= input("&capturedate.",yymmdd10.);
				quit;
			%end;

%let macroname=&SYSMACRONAME;
%ideas_macro_log (start_end=END, macroname=&macroname.);

%mend di_preserve_srprm_past_data;
