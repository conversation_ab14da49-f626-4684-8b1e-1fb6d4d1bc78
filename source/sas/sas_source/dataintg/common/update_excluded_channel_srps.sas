%macro update_channel_srps(hilstar, rat_prop_id, rat_client_id, tenant_database,tenant_server,tenant_db_port,tenant_server_instance,tenant_user,tenant_password) /store;

	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);

    proc sql;
        /* seeded channel srp groups */
        create table &work_lib..seed_channel_srp_grps as
        select distinct csg.srp_group_name as grpname, csg.channel_srp_groups_id as groupid
        from tenant.channel_srp_groups csg where csg.ratchet_client_id=&rat_client_id.;
    run;

    proc sql;
        /* dump of previous channel srps */
        create table &work_lib..channel_srps_ratchet as
        select cs.srp_name as srpname, cs.channel_srp_groups_id as groupid from tenant.channel_srps cs
        where cs.ratchet_property_id=&rat_prop_id.;
    run;

    proc sql;
        /* channel srps from srpcnt and grpcvt */
        create table &work_lib..channel_srps_temp as
        select distinct srpcnt.srpid as srpname, scsg.grpname as grpname, scsg.groupid as groupid
        from hilstar.srpcnt srpcnt
        inner join &work_lib..seed_channel_srp_grps scsg
        on srpcnt.grpid=scsg.grpname
        union
        select distinct grpcvt.srpid as srpname, scsg.grpname as grpname, scsg.groupid as groupid
        from hilstar.grpcvt grpcvt
        inner join &work_lib..seed_channel_srp_grps scsg
        on grpcvt.grpid=scsg.grpname;
    run;

    proc sql;
        /* calculate delta srpcnt */
        create table &work_lib..channel_srps_delta as
        select case when cst.srpname is null then csr.srpname
         else cst.srpname end as srp_name, cst.groupid as channel_srp_groups_id, &rat_prop_id as ratchet_property_id
        from &work_lib..channel_srps_ratchet csr
        full outer join &work_lib..channel_srps_temp cst on csr.srpname=cst.srpname
        where csr.srpname is null or cst.srpname is null or cst.groupid <> csr.groupid;
    run;

    %if %ideas_util_nobs(&work_lib..channel_srps_delta) gt 0 %then
        %do;
            %ideas_trans_append(data_table=&work_lib..channel_srps_delta,trans_table=channel_srps_stg,
                                                                trans_lib=tenant);
        %end;
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend update_channel_srps;