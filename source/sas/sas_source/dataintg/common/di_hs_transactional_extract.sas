%macro di_hs_transactional_extract(hilstar,dirpathForReport,numpastdays,numfuturedays,prop_id,client_code,usetranslatedmktcode) /store;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=START, macroname=&macroname.);

    /* START write the header record STEP:1 */
    proc Sql noprint;
        select CapEdDate into:capturedate from hilstar.hottr where hotelid="&prop_id";
        select CapEdTime into:capturetime from hilstar.hottr where hotelid="&prop_id";
    quit;

    /*PS: 18 Jan Use the calculated past days window size*/
    proc Sql noprint;
        select pastdayscnt into:pdays from hilstar.tmppastdays;
    quit;

    %let numpastdays = &pdays;
    %put numpastdays IS reset to &numpastdays;

    data _null_;
        tmpdate = input("&capturedate", yymmdd10.);
        startdt=intnx('day', tmpdate, "&numpastdays" );
        enddt=intnx('day', tmpdate, "&numfuturedays" );
        call symput('tmpdate1', tmpdate);
        call symput('startdate', LEFT(PUT(startdt,yymmdd10.)));
        call symput('enddate', LEFT(PUT(enddt,yymmdd10.)));
        tmptime = input("&capturetime", hhmmss.);
        call symput('tmptime1', tmptime);
    run;

    %let day1 = %sysfunc(DAY(&tmpdate1),z2.);
    %let year = %sysfunc(YEAR(&tmpdate1),z4.);
    %let month = %sysfunc(MONTH(&tmpdate1),z2.);
    %let hours = %sysfunc(hour(&tmptime1),z2.);
    %let mins = %sysfunc(minute(&tmptime1),z2.);

    %di_hs_tz_converter();
    %put "PreparedDt"= "&prepared_date.";
    %put "PreparedTM"= "&prepared_time.";
    %let capdt=%str(&year.-&month.-&day1.);
    %let captm=%str(&hours.:&mins.:00);
    %let filePathName =%str(&dirpathForReport.\&client_code._&prop_id._&year.&month.&day1._&hours.&mins._TRANS.psv);
    %put str = &filePathName;

    data _null_;
        fname="tempfile";
        rc=filename(fname,"&filePathName");

        if rc = 0 and fexist(fname) then
            rc=fdelete(fname);
        rc=filename(fname);
    run;

    data _null_;
        FILE "&filePathName" dlm='|' MOD;
        attrib File_Header format= $8.;
        File_Header ='_HEADER_';
        PUT File_Header;
    run;

    /* END write the header record STEP:1 */
    /* Added code for Adjusting window size for changed reservations */
    proc Sql noprint;
        select min(new_doa) into:newdoa1 from hilstar.yield;
        select min(old_doa) into:olddoa1 from hilstar.yield;
        select max(new_doa) into:newdoa2 from hilstar.yield;
        select max(old_doa) into:olddoa2 from hilstar.yield;
    quit;

    /* Added code for Adjusting window size for changed reservations */
    /* PS Added new window size adjustment; */
    /* START STEP 2: find out new window size */
    proc Sql noprint;
        select min(doa) into:doa1 from hilstar.books where doa >= input("&startdate",yymmdd10.);
        select min(doa) into:doa2 from hilstar.books;
        select max(doa) into:doa3 from hilstar.books where doa <= input("&enddate",yymmdd10.);
        select max(doa) into:doa4 from hilstar.books;
    quit;

    %put 'startdate for transaction extract=' &startdate.;
    %put 'enddate for transaction extract=' &enddate.;

    data _null_;
        /* Added code for Adjusting window size for changed reservations */
        /* Logic to detrmine min Start date */
        if &newdoa1 <= &olddoa1 then
            do;
                newmindoa = &newdoa1;
            end;
        else
            do;
                newmindoa = &olddoa1;
            end;

        if newmindoa <= &doa2 then
            do;
                newmindoa = newmindoa;
            end;
        else
            do;
                newmindoa = &doa2;
            end;

        /* This condition will come when either &newdoa1 or &olddoa1 is missing "." */
        if newmindoa = "." then
            do;
                newmindoa = &doa1;
            end;

        /* Logic to detrmine min Start date */
        /* Logic to detrmine min End date */
        if &newdoa2 >= &olddoa2 then
            do;
                newmaxdoa = &newdoa2;
            end;
        else
            do;
                newmaxdoa = &olddoa2;
            end;

        if newmaxdoa >= &doa4 then
            do;
                newmaxdoa = newmaxdoa;
            end;
        else
            do;
                newmaxdoa = &doa4;
            end;

        /* This condition will come when either &newdoa2 or &olddoa2 is missing "." */
        if newmaxdoa = "." then
            do;
                newmaxdoa = &doa3;
            end;

        /* Logic to detrmine min End date */
        call symput('dnewmindoa', LEFT(PUT(newmindoa,yymmdd10.)));
        call symput('dnewmaxdoa', LEFT(PUT(newmaxdoa,yymmdd10.)));

        /* Added code for Adjusting window size for changed reservations */
        if &doa1 >= newmindoa then
            do;
                mindoa = &doa1;
            end;
        else
            do;
                mindoa = newmindoa;
            end;

        if &doa3 <= newmaxdoa then
            do;
                maxdoa = &doa3;
            end;
        else
            do;
                maxdoa = newmaxdoa;
            end;

        if mindoa = "." then
            do;
                mindoa = &doa1;
            end;

        if maxdoa = "." then
            do;
                maxdoa = &doa3;
            end;

        pastdiff = int(intck('day',"&tmpdate1", mindoa));
        call symput('pastdays',pastdiff);
        futurediff = int(intck('day',"&tmpdate1", maxdoa));
        call symput('futuredays',futurediff);
        call symput('dmindoa', LEFT(PUT(mindoa,yymmdd10.)));
        call symput('dmaxdoa', LEFT(PUT(maxdoa,yymmdd10.)));
    run;

    %put 'newmindoa for transaction extract=' &dnewmindoa.;
    %put 'newmaxdoa for transaction extract=' &dnewmaxdoa.;
    %put 'mindoa for transaction extract=' &dmindoa.;
    %put 'maxdoa for transaction extract=' &dmaxdoa.;
    %put 'capturedate for transaction extract=' &capturedate.;
    %put 'numpastdays was ' &numpastdays.;
    %put 'pasdays now' = &pastdays.;
    %put 'numfuturedays was ' &numfuturedays.;
    %put 'futuredays now' = &futuredays.;

    /* END STEP 2: find out new window size */
    /* START write the data file metadata STEP:3 */
    /*
        NOTE/TODO: Eliminate code duplication - With a preference to minimal change during parameter introduction,
            the below snippet is duplicated, we can directly use curr_file_metadata in the foll.
            data _null_ step
    */
    %if %upcase(&enableDifferentialTrans.)=TRUE %then %do;
        data hilstar.current_file_metadata;
            set hilstar.hottr;
            Record_Type='_TRANS-META_';
            clientcode = "&client_code";
            pid = "&prop_id";
            past=-( "&pastdays");
            future = "&futuredays";
            prepared_date = "&prepared_date.";
            prepared_time = "&prepared_time.";
            capture_date="&capdt.";
            capture_time="&captm.";

            if past < 0 or past=. then
                past = 0;

            if future < 0 or future = . then
                future=0;
        run;
    %end;
    data _null_;
        FILE "&filePathName" dlm='|' MOD;
        set hilstar.hottr;
        Record_Type='_TRANS-META_';
        clientcode = "&client_code";
        pid = "&prop_id";
        past=-( "&pastdays");
        future = "&futuredays";
        prepared_date = "&prepared_date.";
        prepared_time = "&prepared_time.";
        capture_date="&capdt.";
        capture_time="&captm.";

        if past < 0 or past=. then
            past = 0;

        if future < 0 or future = . then
            future=0;
			
        PUT Record_Type clientcode$ pid$ past future
            capture_date$ capture_time$ prepared_date$ prepared_time$;
    run;

    /* END write the data file metadata STEP:3 */
    /* START write the TRANS_LEVEL RECORDS record STEP:4 */
    /* PS Adding srpidsubstitution related changes; */
    /* (TTRS-1599) Extended Stay : TRANS extract has market segment as NONE when SRP Substitution is turned OFF */
    /* %let usesrpidsubstitution = TRUE; *PS hardcoding for now; */
    %put "usetranslatedmktcode =" &usetranslatedmktcode;

    %create_trans_data(&dmindoa.,&dmaxdoa.,&usetranslatedmktcode.);

      %if %upcase(&enableDifferentialTrans.)=TRUE %then %do;
          data &work_lib..current_trans_1;
              set &work_lib..temptrans1(keep = reservationNumber reservationStatus1  arrivalDate
                departureDate bookedDate CancellationDate oldRoomType occRoomType
                MarketSegment roomRevenue foodRevenue beverageRevenue telecomRevenue otherRevenue totalRevenue source
                Nationality  rateCode  rateValue roomNumber  bookingType noChild noAdults bookingupdatetime isdifferential
                );

                    Record_Type='_TRANS_';

                    /* Added additional fields for compliant with opera extract START */
                    StayDate = "";
                    GroupNameId = "";
                    GroupName = '';
                    CancellationReason = '';
                    CompanyID = "";
                    CompanyName = '';
                    GuestHistoryID = "";
                    TravelAgentID = "";
                    TravelAgentName = '';
                    ConfirmationNumber = '';
                    Channel = '';
                    PaymentMethod = '';
                    CurrencyCode = '';
              run;

          %if %sysfunc(exist(hilstar.current_trans))=1 %then %do;
              proc sql;
                update &work_lib..current_trans_1 set isdifferential=1 where reservationNumber in (
                    select distinct new.reservationNumber from &WORK_LIB..temptrans1 new join hilstar.current_trans old
                        on new.reservationNumber = old.reservationNumber and new.occRoomType=old.occRoomType and
                            new.marketSegment=old.marketSegment
                        where new.roomRevenue <> old.roomRevenue or new.foodRevenue <> old.foodRevenue or new.beverageRevenue<>old.beverageRevenue
                            or new.telecomRevenue <> old.telecomRevenue or new.otherRevenue <> old.otherRevenue or new.totalRevenue <> old.totalRevenue
                            or new.rateCode <> old.rateCode or new.rateValue <> old.rateValue
                );
                run;
          %end;
          proc sql;
            create table hilstar.current_trans as
                select * from &work_lib..current_trans_1;
          run;

      %end;

    %if ( %sysevalf(&pastdays. ne .) and %sysevalf(-(&pastdays.) gt 0) ) or ( %sysevalf(&futuredays. ne .) and %sysevalf(&futuredays. gt 0) ) %then %do;
        data _null_;
        FILE "&filePathName" dsd dlm='|' MOD;
        set &work_lib..temptrans1;
        Record_Type='_TRANS_';

        /* Added additional fields for compliant with opera extract START */
        %let StayDate = "";
        %let GroupNameId = "";
        GroupName = '';
        CancellationReason = '';
        %let CompanyID = ""
            CompanyName = '';
        %let GuestHistoryID = "";
        %let TravelAgentID = "";
        TravelAgentName = '';
        ConfirmationNumber = '';
        Channel = '';
        PaymentMethod = '';
        CurrencyCode = '';

        /* Added additional fields for compliant with opera extract END */
        /* PS commenting out as we need to send complete set of reservations and not incremental; */
        /* if isdifferential = 1 then do; */
        IF CancellationDate ='.' OR reservationStatus1 = 'SS' then
            do;
                %let CancellationDate1=" ";
                PUT Record_Type reservationNumber $ reservationStatus1 $ arrivalDate:yymmdd10.
                    departureDate:yymmdd10. bookedDate:yymmdd10. CancellationDate1$ oldRoomType $ occRoomType $
                    MarketSegment roomRevenue foodRevenue beverageRevenue telecomRevenue otherRevenue totalRevenue source
                    $ Nationality $ rateCode $ rateValue roomNumber $ bookingType noChild noAdults bookingupdatetime:time5. StayDate:yymmdd10.
                    GroupNameId $ GroupName CancellationReason CompanyID $ CompanyName GuestHistoryID $ TravelAgentID $ TravelAgentName
                    ConfirmationNumber Channel PaymentMethod CurrencyCode;
            END;
        else
            do;
                PUT Record_Type reservationNumber $ reservationStatus1 $ arrivalDate:yymmdd10.
                    departureDate:yymmdd10. bookedDate:yymmdd10. CancellationDate:yymmdd10. oldRoomType $ occRoomType $
                    MarketSegment roomRevenue foodRevenue beverageRevenue telecomRevenue otherRevenue totalRevenue source
                    $ Nationality $ rateCode $ rateValue roomNumber $ bookingType noChild noAdults bookingupdatetime:time5. StayDate:yymmdd10.
                    GroupNameId $ GroupName CancellationReason CompanyID $ CompanyName GuestHistoryID $ TravelAgentID $ TravelAgentName
                    ConfirmationNumber Channel PaymentMethod CurrencyCode;
            end;
        run;
    %end;

    /* END write the TRANS_LEVEL RECORDS record STEP:4 */
    /* START write the _FOOTER_ record last STEP:5 */
    data _null_;
        FILE "&filePathName" dsd dlm='|' MOD;
        attrib File_Header format= $8.;
        File_FOOTER ='_FOOTER_';
        PUT File_FOOTER;
    run;

    /* END write the _FOOTER_ record last STEP:5 */

    %if %upcase(&enableDifferentialTrans.)=TRUE %then %do;
        proc sql;
        update hilstar.books set isdifferential=0 where isdifferential=1;
        quit;

        %if %sysfunc(exist(&work_lib..tmp_multiunit_records)) %then %do;
            proc sql;
                update hilstar.current_trans set isdifferential = 1
                where reservationNumber in (select put(input(put(input(put(multi_resid,14.0), 14.0)/1000,14.),14.0)*1000,14.)
                from &work_lib..tmp_multiunit_records);
            quit;
        %end;
    %end;
    
    %let trans_extract_file_name= &filePathName;
    %let macroname=&SYSMACRONAME;

    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_hs_transactional_extract;