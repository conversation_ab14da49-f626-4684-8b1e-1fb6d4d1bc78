%macro di_multi_unit_cutoff_adjustments/store;
	%let macroname=&SYSMACRONAME;
    %ideas_macro_log (start_end=START, macroname=&macroname.);
	proc Sql;
	    /* Create temp table from bookdates by selectin specific columns */
	    create table &work_lib..tmp_multiunit_skip_records as
	    select distinct bd.gnr_num as gnr
	    from hilstar.bookdates as bd
	    where mod(input(bd.resid, 14.0),1000)>0 and bookingdate <= &multiUnitCutOffDate.;
	run;

	proc sql;
        create table &work_lib..tmpy_skip_rec as
        select distinct gnr_num as gnr from &work_lib..tmpy group by gnr_num having min(date_stamp)<=&multiUnitCutOffDate. and bseg_number>0;
    run;

    %ideas_trans_append(data_table=&work_lib..tmp_multiunit_skip_records, trans_table=tmpy_skip_rec, trans_lib=&work_lib.);

    %if %ideas_util_nobs(&work_lib..tmpy_skip_rec)=0 %then
        %do;
            data &work_lib..tmpy_skip_rec;
                gnr=-1;
            run;
        %end;

    proc sql;
        create table &work_lib..update_need_mutli_rec as
        select distinct gnr_num as gnr from &work_lib..tmpy
        where gnr_num not in (select gnr from &work_lib..tmpy_skip_rec) group by gnr_num having max(bseg_number)>0 or max(oldseg_number)>0;
    run;


    %if %ideas_util_nobs(&work_lib..update_need_mutli_rec)>0 %then
        %do;
            proc sql;
                update &work_lib..tmpy set bseg_number=(case when bseg_number ne -1 then 0 else bseg_number end),
                    oldseg_number=(case when oldseg_number ne -1 then 0 else oldseg_number end),
                    resid=(case when resid ne ' ' then put(gnr_num * 1000, 14.) else resid end),
                    oresid=(case when oresid ne ' ' then put(gnr_num * 1000, 14.) else oresid end)
                    where gnr_num in (select gnr from &work_lib..update_need_mutli_rec);
            run;
        %end;
    %delete_D_for_not_DEndState(&multiUnitCutOffDate.);
    %ideas_macro_log (start_end=END, macroname=&macroname.);
%mend di_multi_unit_cutoff_adjustments;