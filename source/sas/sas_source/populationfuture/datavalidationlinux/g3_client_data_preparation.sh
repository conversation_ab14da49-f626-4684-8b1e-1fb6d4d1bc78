User=$(id | cut -f2 -d'(' | cut -f1 -d')')

if (( $# < 3))
 then
  echo " Client ID, Property Code, and (Y=Debug/N=NoDebug)  required as command line parameters "
  echo "  Example:  g3_client_data_preparation.sh Hilton Laxag N"
  exit 1
elif (( $# == 3)) 
 then 
  CLIENT_ID=$1
  PROPERTY=$2
  DEBUG=$3
else
  echo "Only Three parameters allowed - Client ID, Property Code,  and (Y=Debug/N=NoDebug)"
   echo "  Example:  g3_client_data_preparation.sh Hilton Laxag Y"
  exit 1
fi

CLIENT_UPPER="${CLIENT_ID^^}"
echo  Client ID:$CLIENT_UPPER
PROPERTY_UPPER="${PROPERTY^^}"
echo   Property:$PROPERTY_UPPER
DEBUG_UPPER="${DEBUG^^}"
echo  Debug Y/N:$DEBUG_UPPER

G3_PROPERTY_RUNNING=`ps -ef  | grep  /g3_data_preparation.sas | grep "$CLIENT_UPPER"_"$PROPERTY_UPPER" | grep -v grep | grep -v awk | awk '{ print $2 }' |wc -l`
if [ $G3_PROPERTY_RUNNING -gt 0 ]
then 
echo "Data Validation currently running for client/property: ""$CLIENT_UPPER"/"$PROPERTY_UPPER"
exit 1
fi

export G3_SUBMITTED=false
while [[ $G3_SUBMITTED = false ]]; do
G3_SUBMITTED=true
export G3_SUBMITTED
G3_COUNT=`ps -ef  | grep  /g3_data_preparation.sas |grep -v grep | grep -v awk | awk '{ print $2 }' |wc -l`
if [ $G3_COUNT -ge 20 ]
then 
G3_SUBMITTED=false
sleep 10
export G3_SUBMITTED 
fi
done 
echo "Data Validation will be Submitted for ""$CLIENT_UPPER"-"$PROPERTY_UPPER"
G3_ROOT=/cps
G3_OPERATIONS_ROOT=$G3_ROOT/hoteljobs
AUTOEXEC_TEMPLATE_ROOT=$G3_OPERATIONS_ROOT/autoexectemplate
AUTOEXEC_ROOT=$G3_OPERATIONS_ROOT/autoexec
   
tmpfile1=$AUTOEXEC_ROOT/tmpfile1_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
tmpfile2=$AUTOEXEC_ROOT/tmpfile2_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
tmpfile3=$AUTOEXEC_ROOT/tmpfile3_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
tmpfile4=$AUTOEXEC_ROOT/tmpfile4_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
tmpfile5=$AUTOEXEC_ROOT/tmpfile5_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
tmpfile6=$AUTOEXEC_ROOT/tmpfile6_"$CLIENT_UPPER"_"$PROPERTY_UPPER"_Data_Preparation_$(date +%Y%m%d-%T).txt
 
cp $AUTOEXEC_TEMPLATE_ROOT/autoexec_data_preparation.txt $tmpfile1
sed -e 's/dummyproperty/'"$PROPERTY_UPPER"'/g' $tmpfile1 >$tmpfile2
sed -e 's/dummyclient/'"$CLIENT_UPPER"'/g' $tmpfile2 >$tmpfile3
sed -e 's/dummydebug/'"$DEBUG_UPPER"'/g' $tmpfile3 >$tmpfile4
cp $tmpfile4 $AUTOEXEC_ROOT/"$CLIENT_UPPER"_"$PROPERTY_UPPER"_autoexec_data_preparation.txt
rm $tmpfile1 $tmpfile2 $tmpfile3 $tmpfile4 
AUTOEXEC_FILE=$AUTOEXEC_ROOT/"$CLIENT_UPPER"_"$PROPERTY_UPPER"_autoexec_data_preparation.txt
 
if [ ! -d "$G3_ROOT"/hotelclients/"$CLIENT_UPPER" ]
 then
  mkdir "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"
fi

if [ ! -d "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"/operations ]
 then
  mkdir "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"/operations
fi

if [ ! -d "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"/operations/logs ]
 then
  mkdir "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"/operations/logs
fi
/local/SAS_94_M2/SASFoundation/9.4/bin/sas_en -sysin "$G3_ROOT"/hoteljobs/applications/g3_data_preparation.sas -NOPRINT -sysparm Dummy -log  "$G3_ROOT"/hotelclients/"$CLIENT_UPPER"/operations/logs/"$CLIENT_UPPER"_"$PROPERTY_UPPER"_G3_Data_Preparation.log -autoexec $AUTOEXEC_FILE -config "$G3_ROOT"/hoteljobs/config/sasv9.cfg  &

 
