%macro DP_Process_Data_Preparation;
	%if &return_code.=0 and &Property. ne . %then
		%do;
			libname G3glb "&Root_Directory.&slash.hoteljobs&slash.globaldb";

			%DP_Determine_G3_Database(Property=&Property.,rc=&return_code.);

			%if &return_code >=100 %then
				%goto ENDALL;

			%put- Requested Property:: &Property.;

			Proc Sql;
				create table &work_lib..selected_property as 
					select property,property_id,client_code,property_name,unique_property_Id,dbname,server_name,server_inst,port_number,stage 
						from G3glb.DB_Connection_info where "&property."=upcase(property) and "&Client_ID."=upcase(client_code);
			quit;

			%if %sysfunc(exist(&work_lib..selected_property )) and %util_nobs(&work_lib..selected_property )=1 %then
				%do;
					systask command "cd &Root_Directory.&slash.clientoutput ; rm &Root_Directory.&slash.clientoutput&slash.&Client_ID._&Property._Validation_Summary.*" wait shell cleanup;

					Proc Sql noprint;
						select trim(upcase(client_code)),trim(property_name),trim(property),unique_property_Id,property_id,trim(dbname),trim(server_name),trim(server_inst),port_number,trim(stage)
							into:Client_id,:property_name,:property_cd,:unique_property_Id,:property_id,:dbname,:server_name,:server_inst,:port_number,:stage
						from &work_lib..selected_property where "&property."=upcase(property);
					quit;

					%if %upcase(&stage.) ne ATULPLACEHOLDER %then
						%do;
							%let client_id=%trim(&client_id.);
							%let property_name=%BQUOTE(&Property_name.);
							%let property_code=&property_cd.;
							%let work_dir_root=&Root_Directory.;

							%util_create_dir(dir=hotelclients,base_path=&work_dir_root.);
							%let work_dir_root=&Root_Directory.&slash.hotelclients;

							%util_create_dir(dir=&Client_ID.,base_path=&work_dir_root.);
							%let work_dir_root=&Root_Directory.&slash.hotelclients&slash.&Client_ID.;

							%util_create_dir(dir=clientattributes,base_path=&work_dir_root.);
							%util_create_dir(dir=&Property.,base_path=&work_dir_root.);
							%let work_dir_root=&Root_Directory.&slash.hotelclients&slash.&Client_ID.&slash.&Property.;

							%util_create_dir(dir=propertyattributes,base_path=&work_dir_root.);
							%util_create_dir(dir=savework,base_path=&work_dir_root.);
							%util_create_dir(dir=logs,base_path=&work_dir_root.);
							%util_create_dir(dir=summary,base_path=&work_dir_root.);

							proc Printto log="&Root_Directory.&slash.hotelclients&slash.&Client_ID.&slash.&Property.&slash.logs&slash.&property._NGI_Data_Preparation.log" new;
								*proc Setinit noalias;
								%let Savework_lib=savework;
								libname client "&Root_Directory.&slash.clientinput";
								libname &Savework_lib. "&Root_Directory.&slash.hotelclients&slash.&Client_ID.&slash.&Property.&slash.savework";
								libname Summary "&Root_Directory.&slash.hotelclients&slash.&Client_ID.&slash.&Property.&slash.summary";
								libname status "&Root_Directory.&slash.hotelclients&slash.&Client_ID." FILELOCKWAIT=10;
								libname Output "&Root_Directory.&slash.hotelclients&slash.&Client_ID.&slash.&Property";

							proc Datasets library=Output kill memtype=data;
							run;

							quit;

							proc Datasets library=Summary nolist noprint kill memtype=data;
							run;

							quit;

							%DP_Connect_g3_database(Property=&Property.,rc=&return_code.);

							%if &return_code >=100 %then
								%goto ENDALL;

							Proc Sql noprint;
								select count(*) into:total_activity_count
									from g3_db.total_activity;
							quit;

							%if &total_activity_count.>0 %then
								%do;
									%DP_Extract_G3_Database_Data(Property=&Property.,rc=&return_code.);

									%if &return_code >=100 %then
										%goto ENDALL;

									%DP_Extract_Client_Attributes(Property=&Property.,rc=&return_code.);

									%if &return_code >=100 %then
										%goto ENDALL;

									%DP_Summary_Validate(Property=&Property.,rc=&return_code.);

									%if &return_code >=100 %then
										%goto ENDALL;

									%DP_Oxy_Validate(Property=&Property.,rc=&return_code.);

									%if &return_code >=100 %then
										%goto ENDALL;

									%if &return_code=0 %THEN
										%do;
											%DP_Output_Files(Property=&Property.,rc=&return_code.);

											%if &return_code >=100 %then
												%goto ENDALL;
										%end;
								%end;
							%else
								%do;
									%let Data_Preparation_message2=Property &property. Transactions/Summary Data not Loaded and Available on G3 For Processing;
									%put Property &property. Transactions/Summary Data not Loaded and Available on G3 For Processing;
									%let return_code=-1;
								%end;
						%end;
					%else
						%do;
							%let Data_Preparation_message2=Property &property. is in Two Way and is Not Available For Processing;
							%put Property &property. is in Two Way and Not Available For Processing;
							%let return_code=-3;
						%end;
				%end;
			%else
				%do;
					%let property_name=Property Not on G3;
					%let Data_Preparation_message2=Property Not Defined in G3;
					%let return_code=-3;
					%put Property Not Defined in G3;
				%end;
		%end;
	%else
		%do;
			%let property_name=G3 Property Not provided as parameter;
			%let Data_Preparation_message2=G3 Property not provided as parameter;
			%put G3 property not provided as parameter;
			%let return_code=-3;
		%end;

%ENDALL:

	%if &return_code ne -3 %then
		%DP_Update_Property_Status (Property=&Property.,rc=&return_code.);
	%DP_Output_Email_Response(Property=&Property.,rc=&return_code.);
	run;

%mend DP_Process_Data_Preparation;

%macro DP_Determine_G3_Database(Property=,rc=);
	%if (%sysfunc(exist(G3glb.Global_Last_refreshed_date)))=0 %then
		%do;

			data &work_lib..Global_Last_refreshed_date;
				format Global_Last_Refreshed_Date date9.;
				Global_Last_Refreshed_Date="&Todays_date."d;
			run;

		%end;
	%else
		%do;

			data &work_lib..Global_Last_refreshed_date;
				set G3glb.Global_Last_refreshed_date;
			run;

		%end;

	Proc Sql noprint;
		select Global_Last_Refreshed_Date into:Global_Last_Refreshed_Date
			from &work_lib..Global_Last_refreshed_date;
	quit;

	%put Last Refreshed: &Global_Last_refreshed_date.;
	%put Todays Date: &Todays_date.;

	%if (%sysfunc(exist(G3glb.DB_Connection_info)))=0 or %sysfunc(putn("&Global_Last_refreshed_date."d,5.)) < %sysfunc(putn("&Todays_date."d,5.)) %then
		%do;
			%let global_count=1;

			%if (%sysfunc(exist(G3glb.DB_Connection_info)))=1 %then
				%do;

					proc Sql;
						drop table G3glb.DB_Connection_info;
					quit;

				%end;

			%if (%sysfunc(exist(G3glb.G3_Global_PSEUDO_Info)))=1 %then
				%do;

					proc Sql;
						drop table G3glb.G3_Global_PSEUDO_Info;
					quit;

				%end;

			%do %while (%scan(&global_servers.,&global_count.,|) ne);
				%let server=%scan(&global_servers.,&global_count.,|);
				%let port=%scan(&global_ports.,&global_count.,|);
				%let lib_name=Global;
				%let db=Global;
				%let schema_name=dbo;
				%let user=G3sas;
				%let pwd=IDeaS123;
				%let user=G3ROS;
				%let pwd=%str(G%)3F]Nd5XSE-NuY);

				%dp_connect_database(lib_name=&lib_name.,db=&db.,Hostname=&server.,port=&port.,schema_name=&schema_name.,user=&user.,pwd=&pwd.,rc=&return_code. );

				proc Sql;
					create table &work_lib..DB_Connection_info_&global_count. as 
						select c.client_code,c.client_name,b.property_id,b.property_code as property format=$15.,b.property_name,
							a.dbname,a.server_name,a.server_inst,a.port_number,a.dbtype_id,b.stage,monotonic() as unique_property_Id format=5.,&global_count. as environment format=3. 

						from Global.DBloc as a 
							inner join Global.property as b on 
								a.dbloc_id=b.dbloc_id 
							inner join Global.Client as c 
								on b.client_id=c.client_id 
							where a.status_id=1 and b.property_code ne "-1" and upcase(c.client_code) ne "SANDBOX";
				Quit;

				proc Sql;
					create table &work_lib..Global_PSEUDO_Info_&global_count. as 
						select c.Context,c.Config_Parameter_ID,c.FixedValue
							from Global.Config_Parameter_Value c 
								where c.Config_Parameter_ID=318 and c.FixedValue ne ''
									order by c.Context,c.Config_Parameter_ID,c.FixedValue;
				quit;

				%if &Global_count.=1 %then
					%do;

						PROC APPEND BASE=G3glb.DB_Connection_info DATA=&work_lib..DB_Connection_info_&global_count. FORCE;

						PROC APPEND BASE=G3glb.G3_Global_PSEUDO_Info DATA=&work_lib..Global_PSEUDO_Info_&global_count. FORCE;
					%end;
				%else
					%do;

						proc Sort data=G3glb.DB_Connection_info;
							by property dbname;

						proc Sort data=&work_lib..DB_Connection_info_&global_count.;
							by property dbname;

						data G3glb.DB_Connection_info;
							merge &work_lib..DB_Connection_info_&global_count. (in=tranin) G3glb.DB_Connection_info (in=masterin);
							by property dbname;

							if (masterin=0 and tranin=1 ) or masterin=1 Then
								output G3glb.DB_Connection_info;
						run;

						PROC APPEND BASE=G3glb.G3_Global_PSEUDO_Info DATA=&work_lib..Global_PSEUDO_Info_&global_count. FORCE;
					%end;

				proc sort data=G3glb.DB_Connection_info;
					by client_name property_name property;
					%let global_count=%sysevalf(&global_count. + 1);
					libname Global clear;
			%end;

			data G3glb.Global_Last_refreshed_date;
				format Global_Last_Refreshed_Date date9.;
				Global_Last_Refreshed_Date="&Todays_date."d;
			run;

		%end;

	%if &syscc >4 %then
		%let return_code=200;
	%else
		%if %sysfunc(exist(G3glb.Global_Last_refreshed_date )) and %util_nobs(G3glb.Global_Last_refreshed_date ) > 0 %then
		%let return_code=0;
	%else %let return_code=100;
%mend DP_Determine_G3_Database;

%macro DP_Connect_G3_Database(Property=,rc=);
	%let lib_name=G3_DB;
	%let server=&server_name.;
	%let db=&dbname.;
	%let port=&port_number.;
	%let schema_name=dbo;
	%let user=G3ROS;
	%let pwd=%str(G%)3F]Nd5XSE-NuY);

	%dp_connect_database(lib_name=&lib_name.,db=&db.,Hostname=&server.,port=&port.,schema_name=&schema_name.,user=&user.,pwd=&pwd.,rc=&return_code. );
	run;

	%let db_pacman_connect_str = Driver=SQLServer2008_dsn_less%str(;)Hostname=&server%str(;)uid=&user%str(;)pwd=&pwd%str(;)database=&db%str(;)port=&port;
	%let db_pacman_connect_str=noprompt="&db_pacman_connect_str";
	%let lib_name=Opera;
	%let server=&server_name.;
	%let db=&dbname.;
	%let port=&port_number.;
	%let schema_name=opera;
	%let user=G3ROS;
	%let pwd=%str(G%)3F]Nd5XSE-NuY);

	%dp_connect_database(lib_name=&lib_name.,db=&db.,Hostname=&server.,port=&port.,schema_name=&schema_name.,user=&user.,pwd=&pwd.,rc=&return_code. );
	run;

	%if &syscc >4 %then
		%let return_code=200;
	%else %if %sysfunc(libref(G3_DB))=0 %then
		%let return_code=0;
	%else %let return_code=100;
%mend DP_Connect_G3_Database;

%macro DP_Extract_Client_Attributes(Property=,rc=);
	%let clientattributefile=&Root_Directory.&slash.clientinput&slash.&client_id._&Property._validate.csv;
	%let clienttransactionsfile=&Root_Directory.&slash.clientinput&slash.&client_id._&Property._transactions.csv;

	%if %sysfunc(fileexist(&clientattributefile.)) %then
		%do;
			%let File_desc=Client File &client_id._&Property._validate.csv Validated;

			PROC IMPORT DATAFILE="&clientattributefile."
				DBMS=csv
				OUT=&work_lib..Property_Total_Activity_temp REPLACE;
				guessingrows=3000;
				GETNAMES=YES;
			RUN;

			data &work_lib..Property_Total_Activity (keep=property_id Occupancy_dt property_source Property_Total_Accom_Capacity 
				Property_rooms_not_avail_maint Property_rooms_not_avail_other Property_Rooms_Sold Property_Arrivals Property_Departures Property_Cancellations Property_No_Shows Property_Room_Revenue
				Property_Food_Revenue Property_Total_Revenue property_group_Rooms_sold Property_Group_Room_Revenue
				rooms_sold room_revenue arrivals departures no_shows cancellations  
				Trans_Rooms_Sold Trans_Room_Revenue Trans_arrivals Trans_departures Trans_no_shows Trans_cancellations  
				Group_Rooms_Sold Group_Room_Revenue Group_arrivals Group_departures 
				total_accom_capacity rooms_not_avail_maint rooms_not_avail_other);
				Format occupancy_dt date9.;
				format property_source $15.;
				attrib Property_Total_Accom_Capacity Property_rooms_not_avail_maint Property_rooms_not_avail_other Property_Rooms_Sold Property_Arrivals Property_Departures Property_Cancellations Property_No_Shows Property_Group_Rooms_Sold length=8;
				attrib Property_Room_Revenue Property_Food_Revenue Property_Total_Revenue Property_Group_Room_Revenue format=11.2 length=8;
				Set &work_lib..Property_Total_Activity_temp;
				Property_id=&Property_id.;
				Occupancy_dt=considered_date;
				Property_source=rec_type_desc;

				/*Occupancy_dt=datepart(input(trim(considered_date),anydtdtm.)); */
				Property_Total_Accom_Capacity=input(Inventory_rooms,best12.);
				Property_rooms_not_avail_maint=input(CF_OOO_ROOMS,best12.);
				Property_rooms_not_avail_other=0;
				Property_Rooms_sold=sum(input(NO_Rooms,best12.),0);

				* Property_Rooms_sold=sum(input(IND_DEDUCT_ROOMS,best12.)+input(GRP_DEDUCT_ROOMS,best12.),0);
				Property_Arrivals=input(Arrival_Rooms,best12.);
				Property_Departures=input(Departure_Rooms,best12.);
				Property_Cancellations=0;
				Property_No_Shows=sum(input(No_show_Rooms,best12.),0);
				Property_Room_Revenue=sum(input(IND_DEDUCT_REVENUE,best12.) + input(GRP_DEDUCT_REVENUE,best12.),0);
				Property_Food_Revenue=0;
				Property_Total_Revenue=sum(input(IND_DEDUCT_REVENUE,best12.) + input(GRP_DEDUCT_REVENUE,best12.),0);
				Property_Group_Rooms_Sold=sum(input(GRP_DEDUCT_ROOMS,best12.),0);
				Property_Group_Room_Revenue=sum(input(GRP_DEDUCT_REVENUE,best12.),0);
				rooms_sold=0;
				room_revenue=0;
				arrivals=0;
				departures=0;
				no_shows=0;
				cancellations=0;
				Trans_Rooms_Sold=0;
				Trans_Room_Revenue=0;
				Trans_arrivals=0;
				Trans_departures=0;
				Trans_no_shows=0;
				Trans_cancellations=0;
				Group_Rooms_Sold=0;
				Group_Room_Revenue=0;
				Group_arrivals=0;
				Group_departures=0;
				total_accom_capacity=0;
				rooms_not_avail_maint=0;
			run;

		%end;
	%else %let File_desc=No Client Validation File Provided;

	%if %sysfunc(fileexist(&clienttransactionsfile.)) %then
		%do;

			PROC IMPORT DATAFILE="&clienttransactionsfile."
				DBMS=csv
				OUT=&work_lib..Property_Transactions_temp REPLACE;
				guessingrows=100;
				GETNAMES=YES;
			RUN;

			data &work_lib..Property_Transactions (keep=property_id reservation_identifier);
				Format occupancy_dt date9.;
				format reservation_identifier $50.;
				Set &work_lib..Property_Transactions_temp;
				Property_id=&Property_id.;

				*Occupancy_dt=input(trim(Occupancy_date),date9.);
				*Occupancy_dt=Occupancy_date;
				Reservation_identifier=strip(transaction_id);
			run;

		%end;

	%if &syscc >4 %then
		%let return_code=200;
	%else %let return_code=0;
%mend DP_Extract_Client_Attributes;

%macro DP_Extract_G3_Database_Data(Property=,rc=);

	proc Sql;
		create table &work_lib..file_metadata as
			select distinct snapshot_dt as Current_Snapshot_Date format=date9. 
				from G3_db.file_metadata
					where property_id = &property_id and record_type_id = 3 and 
						(select max(snapshot_dt) from G3_db.file_metadata 
							where property_id = &property_id and isbde=1 and record_type_id = 3) = snapshot_dt;
	quit;

	proc Sql noprint;
		select count(*) into :backfillcount 
			from G3_db.file_metadata 
				where record_type_id=3 and upcase(file_name)="PACEHISTORYBUILD" and isbde=1;
	quit;

	data _null_;
		attrib Processing_Start_Date format=Date9.;
		attrib Processing_End_Date format=Date9.;
		attrib Snapshot_Date_LY format=Date9.;
		set &work_lib..file_metadata;
		Snapshot_date_LY=INTNX('week',Current_Snapshot_Date,-52,"same");
		processing_Start_Date=INTNX( 'YEAR',Current_Snapshot_Date,-1,'S' );
		processing_Select_Start_Date=INTNX( 'YEAR',Current_Snapshot_Date,-2,'S' );
		processing_End_Date=INTNX( 'YEAR',Current_Snapshot_Date+1,1,'S' );
		call symput('Processing_Start_Date',Processing_Start_Date);
		call symput('Processing_End_Date',Processing_End_Date);
		call symput('Current_Snapshot_Date',Current_Snapshot_Date);
		call symput('Snapshot_date_LY',Snapshot_date_LY);
		call symput('Processing_Select_Start_Date',Processing_Select_Start_Date);
	run;

	proc Sql;
		create table &work_lib..G3_Total_Activity as 
			select property_id, Occupancy_dt , datepart(snapshot_dttm) as Snapshot_dt format=date9., Total_accom_capacity, 
				rooms_sold format=7. length=6, rooms_not_avail_maint, rooms_not_avail_other, Arrivals, departures, Cancellations, no_shows,
				round(Room_Revenue,.01) as Room_Revenue format=11.2, round(Food_Revenue,.01) as Food_Revenue format=11.2, round(Total_Revenue,.01) as Total_Revenue format=11.2 
			from G3_DB.Total_Activity 
				order by property_id, occupancy_dt;
	quit;

	proc sql;
		create table &work_lib..G3_Group_Transactions_temp as 
			select "Group" as Transaction_Type format=$15., c.property_id , "Grp"||put(c.group_id,9.)||put(b.group_block_id,7.) as reservation_identifier format=$50.,
				"SS" as individual_status format=$2., c.booking_dt as booking_dt format=date9.,
				b.occupancy_dt as arrival_dt format=date9., b.occupancy_dt+1 as departure_dt format=date9., 1 as los length=3 format=5., . as cancellation_dt , 
				b.accom_type_id, b.rate as room_revenue format=11.2, 0 as food_revenue format=11.2, 
				0 as Beverage_revenue format=11.2, 0 as telecom_revenue format=11.2, 0 as other_revenue format=11.2, b.rate as total_revenue format=11.2 , b.blocks-b.pickup as solds
			from G3_DB.group_block as b 
				inner join G3_DB.Group_master as c 
					on b.group_id=c.group_id
				where upcase(c.group_status_code)="DEFINITE" and b.blocks > 0 and b.occupancy_dt> &Current_Snapshot_Date.    
					order by reservation_identifier, arrival_dt;
	quit;

	data &work_lib..G3_Group_Transactions (drop=isolds solds save_reservation_identifier);
		format save_reservation_identifier $50.;
		set &work_lib..G3_Group_Transactions_Temp;
		save_reservation_identifier=compress(reservation_identifier);

		if solds > 1 then
			do;
				do Isolds = 1 to solds;
					reservation_identifier=trim(save_reservation_identifier)||put(isolds,4.);
					output &work_lib..G3_Group_Transactions;
				end;
			end;
		else
			do;
				reservation_identifier=trim(save_reservation_identifier);
				output &work_lib..G3_Group_Transactions;
			end;
	run;

	proc sql;
		create table &work_lib..G3_Transactions as 
			select &Property_id. as Property_id, a.Reservation_Identifier as reservation_identifier format=$50. ,a.Individual_Status,a.booking_dt,a.arrival_dt,a.departure_dt,a.cancellation_dt,
				a.mkt_seg_id, c.mkt_seg_code format=$30. , a.rate_code,a.accom_type_id, b.accom_type_code format=$20. , a.booked_Accom_Type_Code,a.nationality,a.Source_booking,a.channel,a.room_number,
				a.rate_value,a.number_children,a.number_adults,
				a.Room_revenue,a.Food_revenue,a.beverage_revenue,a.telecom_revenue,a.other_revenue,a.Total_Revenue
			from G3_DB.Individual_Trans as a 
				inner join G3_DB.accom_type as b
					on a.accom_type_id=b.accom_type_id
				inner join G3_DB.mkt_seg as c
					on a.mkt_seg_id=c.mkt_seg_id
				order by a.arrival_dt;
	quit;

	proc sql;
		create table &work_lib..G3_Group_master as 
			select a.*,b.mkt_seg_code 
				from G3_DB.Group_master as a 
					inner join G3_DB.mkt_seg as b
						on a.mkt_seg_id=b.mkt_seg_id
					order by a.Group_Name;
	quit;

	data &work_lib..G3_Transactions_Exploded (keep=property_id occupancy_DT departure_DT arrival_DT booking_DT cancellation_DT los mkt_seg_id mkt_seg_code rate_code room_number 
		booked_Accom_Type_Code Accom_Type_Code room_number number_adults departure_DT individual_Status reservation_Identifier rate_Value Room_revenue food_Revenue other_Revenue total_Revenue );
		attrib Los format=3.;
		attrib Occupancy_DT format=date9.;
		set &work_lib..G3_Transactions;

		if Arrival_DT < Departure_DT Then
			do;
				Los=Departure_Dt-Arrival_Dt;
				calc_Room_Revenue=Room_Revenue/Los;
				calc_Food_Revenue=Food_Revenue/Los;
				calc_Telecom_revenue=Telecom_revenue/Los;
				calc_Beverage_revenue=Beverage_revenue/Los;
				calc_Other_revenue=Other_revenue/Los;

				do Occupancy_DT=Arrival_DT to Departure_DT -1;
					room_Revenue=calc_room_Revenue;
					Food_Revenue=calc_Food_Revenue;
					Beverage_Revenue=calc_Beverage_revenue;
					Telecom_Revenue=calc_telecom_revenue;
					Other_Revenue=calc_Other_revenue;
					Total_Revenue=sum(room_Revenue,Food_Revenue,Beverage_Revenue,Telecom_Revenue,Other_Revenue);
					output &work_lib..G3_Transactions_Exploded;
				end;
			end;
		else
			do;
				Occupancy_DT=arrival_DT;
				output &work_lib..G3_Transactions_Exploded;
			end;
	run;

	proc sql;
		create table &work_lib..G3_Group_arrivals as 
			select a.property_id, a.Arrival_dt as occupancy_dt format=date9., count(*) as Group_arrivals length=8
				from &work_lib..G3_Group_Transactions as a 
					where a.individual_status in ("SS", "CO" ,"CI") and  
						a.departure_dt > a.arrival_dt  
					group by a.property_id, a.arrival_dt
						order by a.property_id, occupancy_dt;
		create table &work_lib..G3_Group_departures as 
			select a.property_id , a.Departure_dt as occupancy_dt format=date9.,count(*) as Group_departures length=8
				from &work_lib..G3_Group_Transactions as a 
					where a.individual_status in ("SS", "CO" ,"CI") and  
						a.departure_dt > a.arrival_dt 
					group by a.property_id, a.departure_dt
						order by a.property_id, occupancy_dt;
	quit;

	proc sql;
		create table &work_lib..G3_Cancels_by_arrival as 
			select a.property_id, a.Arrival_dt as occupancy_dt format=date9., count(*) as Cancels_By_arrival length=8
				from &work_lib..G3_Transactions as a 
					where a.individual_status in ("XX") and  
						a.departure_dt > a.arrival_dt  
					group by a.property_id, a.arrival_dt
						order by a.property_id, occupancy_dt;
		create table &work_lib..G3_Reserved_occupancy_dt as 
			select a.property_id, a.occupancy_dt format=date9., count(*) as Reserved_Solds length=8
				from &work_lib..G3_Transactions_exploded as a 
					where a.individual_status in ("SS") and  a.departure_dt < &current_snapshot_date.  and 
						a.departure_dt > a.arrival_dt  
					group by a.property_id, a.occupancy_dt
						order by a.property_id, occupancy_dt;

	proc sql;
		create table &work_lib..G3_Group_block as 
			select &property_id. as property_id, a.group_id,a.occupancy_dt,a.accom_type_id,a.blocks,a.pickup,a.original_blocks,a.rate,b.group_code,
				case 
					when a.occupancy_dt < &current_snapshot_date. then pickup 
					else a.blocks-a.pickup 
				end  
			as solds,
				b.Group_name,b.group_status_code
			from G3_DB.GROUP_BLOCK as a 
				inner join G3_DB.group_master as  b 
					on a.group_id=b.group_id 
				where a.occupancy_dt >=&processing_Select_Start_Date. 
					order by a.group_id, a.accom_type_id, a.occupancy_dt;
		create table &work_lib..G3_Group_Definite_occupancy_dt as 
			select &property_id as Property_id, occupancy_dt,sum(solds) as Group_Definite_solds, sum(blocks) as Group_Definite_blocks,sum(pickup) as Group_Definite_pickup, sum(original_blocks) as Group_Definite_original_blocks,group_status_code
				from &work_lib..G3_Group_block
					where upcase(group_status_code) in ("DEFINITE") and occupancy_dt >=&processing_Select_Start_Date.
						group by occupancy_dt,group_status_code
							order by Property_id, occupancy_dt;
		create table &work_lib..G3_Group_Tentative_occupancy_dt as 
			select &property_id as Property_id, occupancy_dt,sum(solds) as Group_Tentative_solds, sum(blocks) as Group_Tentative_blocks,sum(pickup) as Group_Tentative_pickup, sum(original_blocks) as Group_Tentative_original_blocks,group_status_code
				from &work_lib..G3_Group_block
					where upcase(group_status_code) in ("TENTATIVE") and occupancy_dt >=&processing_Select_Start_Date.
						group by occupancy_dt,group_status_code
							order by Property_id, occupancy_dt;
	quit;

	%Run_SQL( results_lib=&work_lib., results_table=custom_query_Group_Block, query=&block_query., db_connect_str=&db_pacman_connect_str.);
	%Run_SQL( results_lib=&work_lib., results_table=custom_query_No_Group_Block, query=&no_block_query., db_connect_str=&db_pacman_connect_str.);

	proc sql;
		create table &work_lib..custom_query_group_block as 
			select &Property_id. as Property_id format=7. ,*  
				from &work_lib..custom_query_group_block
					order by property_id, occupancy_dt;
		create table &work_lib..custom_query_No_Group_Block as 
			select *  
				from &work_lib..custom_query_No_Group_Block
					order by property_id, occupancy_dt;
	quit;

	libname G3_DB clear;

	%if &syscc >4 %then
		%let return_code=200;
	%else %if  %sysfunc(exist(&work_lib..file_metadata))  and %util_nobs(&work_lib..G3_Transactions) > 0 %then
		%let return_code=0;
	%else %let return_code=100;
	options label;
%mend DP_Extract_G3_Database_Data;

%macro DP_Summary_Validate(Property=,rc=);
	%if %sysfunc(exist(&work_lib..Property_Total_Activity)) and %sysfunc(exist(&work_lib..G3_Total_Activity)) > 0 %then
		%do;

			data &work_lib..Audit_Property_File_Differences;
				retain property_id occupancy_dt property_source 
					rooms_sold room_revenue arrivals departures no_shows cancellations  
					Trans_Rooms_Sold Trans_Room_Revenue Trans_arrivals Trans_departures Trans_no_shows Trans_cancellations  
					Group_Rooms_Sold Group_Room_Revenue Group_arrivals Group_departures 
					property_rooms_sold property_room_revenue property_arrivals property_departures property_no_shows property_cancellations  
					total_accom_capacity property_total_accom_capacity rooms_not_avail_maint property_rooms_not_avail_maint rooms_not_avail_other property_rooms_not_avail_other 
					diff_rooms_sold diff_arrivals diff_departures diff_no_shows diff_cancellations diff_Room_Revenue diff_capacity diff_rooms_not_avail_maint diff_rooms_not_avail_other diff_Group_Room_Revenue;
				format Property_source $12.;
				format percent_diff_Trans_rooms_sold percent_diff_rooms_sold percent_diff_Trans_room_revenue percent_diff_Group_Room_Revenue percent_diff_Group_rooms_sold percent_diff_Room_Revenue 8.2;
				merge &work_lib..Property_Total_Activity (in=filein) &work_lib..G3_Total_Activity (in=g3in) &work_lib..custom_query_Group_Block (in=tranin) &work_lib..G3_Group_arrivals (in=g3arrin) &work_lib..G3_Group_departures (in=g3depin);
				by property_id occupancy_dt;

				if filein=1 /* and g3in=1 and tranin=1 */
				then

					do;
						diff_Trans_rooms_sold=property_rooms_sold-Trans_Rooms_Sold;

						if Trans_rooms_sold=0 then
							percent_diff_Trans_rooms_sold=1;
						else percent_diff_Trans_rooms_sold=(property_rooms_sold/Trans_rooms_sold) -1;
						diff_Trans_arrivals=property_arrivals-Trans_arrivals;
						diff_Trans_departures=property_departures-Trans_departures;
						diff_Trans_no_shows=property_no_shows-Trans_no_shows;
						diff_Trans_cancellations=property_cancellations-Trans_cancellations;
						diff_Trans_Room_Revenue=property_Room_revenue-Trans_Room_Revenue;

						if Trans_Room_Revenue=0 then
							percent_diff_Trans_Room_Revenue=1;
						else percent_diff_Trans_Room_Revenue=(property_Room_Revenue/Trans_Room_Revenue) -1;
						diff_rooms_sold=property_rooms_sold-rooms_sold;

						if rooms_sold=0 then
							percent_diff_rooms_sold=1;
						else percent_diff_rooms_sold=(property_rooms_sold/rooms_sold) -1;

						if occupancy_dt>&Current_Snapshot_Date. then
							do;
								diff_Group_Room_Revenue=property_Group_Room_revenue-Group_Room_Revenue;

								if Group_Room_Revenue=0 then
									percent_diff_Group_Room_Revenue=1;
								else percent_diff_Group_Room_Revenue=(property_Group_Room_Revenue/Group_Room_Revenue) -1;
							end;
						else
							do;
								percent_diff_Group_Room_Revenue=0;
								diff_Group_Room_Revenue=0;
							end;

						if occupancy_dt>&Current_Snapshot_Date. then
							do;
								diff_Group_rooms_sold=property_Group_rooms_sold-Group_Rooms_Sold;

								if Group_rooms_sold=0 then
									percent_diff_Group_rooms_sold=1;
								else percent_diff_Group_rooms_sold=(property_Group_rooms_sold/Group_rooms_sold) -1;
							end;
						else
							do;
								percent_diff_Group_rooms_sold=0;
								diff_Group_rooms_sold=0;
							end;

						diff_arrivals=property_arrivals-arrivals;
						diff_departures=property_departures-departures;
						diff_no_shows=property_no_shows-no_shows;
						diff_cancellations=property_cancellations-cancellations;
						diff_Room_Revenue=property_Room_revenue-Room_revenue;

						if Room_Revenue=0 then
							percent_diff_Room_Revenue=1;
						else percent_diff_Room_Revenue=(property_Room_Revenue/Room_Revenue) -1;
						diff_capacity=property_total_accom_capacity-total_accom_capacity;
						diff_rooms_not_avail_maint=property_rooms_not_avail_maint-rooms_not_avail_maint;
						diff_rooms_not_avail_other=property_rooms_not_avail_other-rooms_not_avail_other;
						output &work_lib..Audit_Property_File_Differences;
					end;
			run;

		%end;

	%if %sysfunc(exist(&work_lib..Property_transactions)) and %sysfunc(exist(&work_lib..G3_transactions)) > 0 %then
		%do;

			proc sort data=&work_lib..Property_transactions nodupkey;
				by reservation_identifier;
			run;

			proc sort data=&work_lib..G3_transactions;
				by reservation_identifier;
			run;

			data &work_lib..reservation_audit( keep=PROPERTY_ID reservation_identifier individual_status status );
				retain reservation_identifier individual_status status;
				format status $15.;
				merge &work_lib..Property_transactions (in=filein) &work_lib..G3_transactions (in=g3in);
				by reservation_identifier;

				if filein=1 and g3in=1 then
					do;
						status="Matched";
						output &work_lib..reservation_audit;
					end;

				if filein=1 and g3in=0 then
					do;
						status="G3 Missing";
						output &work_lib..reservation_audit;
					end;
			run;

		%end;

	%if &syscc >4 %then
		%do;
			%let return_code=200;
		%end;
	%else
		%do;
			%let return_code=0;
		%end;
%mend DP_Summary_Validate;

%macro DP_Oxy_Validate(Property=,rc=);

	data &work_lib..Audit_differences;
		/* (keep=property_id source snapshot_dt occupancy_dt rooms_sold Trans_Rooms_Sold Reserved_Solds Cancels_By_arrival total_accom_capacity diff_rooms_sold diff_rooms_sold_without_SS);
													retain property_id source occupancy_dt rooms_sold Trans_Rooms_Sold Reserved_Solds Cancels_By_arrival total_accom_capacity diff_rooms_sold diff_rooms_sold_without_SS; */
		format source $10.;
		merge &work_lib..G3_Total_Activity (in=g3in) &work_lib..custom_query_Group_Block (in=tranin) &work_lib..G3_Group_Tentative_occupancy_dt (in=grptin) &work_lib..g3_group_definite_occupancy_dt (in=grpdin) &work_lib..G3_Cancels_by_arrival (in=g3arrin) &work_lib..G3_Reserved_occupancy_dt (in=g3ssin);
		by property_id occupancy_dt;

		if g3in=1 and tranin=1 then
			do;
				if occupancy_dt<="&Current_Snapshot_Date." then
					source="History";
				else source="Future";
				Reserved_Solds=sum(Reserved_Solds,0);
				Cancels_By_arrival=sum(Cancels_By_arrival,0);
				Group_Definite_Blocks=sum(Group_Definite_Blocks,0);
				Group_Definite_Pickup=sum(Group_Definite_Pickup,0);
				Group_Definite_solds=sum(Group_Definite_solds,0);
				Group_Tentative_Blocks=sum(Group_Tentative_Blocks,0);
				Group_Tentative_Pickup=sum(Group_Tentative_Pickup,0);
				Group_Tentative_solds=sum(Group_Tentative_solds,0);
				available_capacity=total_accom_capacity-Trans_Rooms_Sold;
				available_capacity_without_ss=total_accom_capacity-(Trans_Rooms_Sold-Reserved_Solds);
				output &work_lib..Audit_differences;
			end;
	run;

	%if &syscc >4 %then
		%do;
			%let return_code=200;
		%end;
	%else
		%do;
			%if %sysfunc(exist(&work_lib..Audit_differences)) %then
				%let return_code=0;
			%else
				%do;
					%let return_code=100;
				%end;
		%end;
%mend DP_Oxy_Validate;

%macro DP_Update_Property_Status(Property=,rc=);
	%IF %SYSFUNC(EXIST(status.property_status))=0 %THEN
		%do;

			proc Sql;
				create table status.property_status (
					Client_ID char(20),
					property char(15),
					property_name char(50),
					Status num format=3.,
					Status_message Char(50),   
					Snapshot_Date num format date9.,
					Processing_start_date num format date9.,
					Processing_end_date num format date9.,
					Status_DTtm num format datetime17. 
					);
			quit;

		%end;

	data &work_lib..Status_add_Transaction (keep=client_id property property_name Snapshot_date Processing_start_date Processing_end_date status status_message status_DTTM );
		retain client_id property property_name Snapshot_date Processing_start_date Processing_end_date status status_message status_DTTM;
		format property $15.;
		format client_id $20.;
		format status 3.;
		format status_DTTM datetime17.;
		format Processing_end_date date9.;
		format processing_start_date date9.;
		format Snapshot_date date9.;
		format status_message $50.;
		format property_name $50.;
		client_id="&client_ID.";
		property="&Property_Code.";
		property_name="&Property_Name.";
		Processing_start_date=&processing_start_date.;
		Processing_end_date=&processing_end_date.;
		Snapshot_Date=&Current_Snapshot_Date.;
		Status=&return_code.;

		if Status=0 then
			status_message="Successful";
		else if status in ("100","200") then
			status_message=" Failed - Application Error";
		else status_message="&data_preparation_message2.";
		Status_DTtm=datetime();
	run;

	Proc Sql;
		delete from status.property_status 
			where "&Property_Code."=Property and "&client_ID."=client_id;
	quit;

	proc Sql;
		insert into status.property_status (client_id, property, property_name, Snapshot_date, Processing_start_date, Processing_end_date, status, status_message, status_DTTM)
			select client_id, property, property_name, Snapshot_date, Processing_start_date, Processing_end_date, status, status_message, status_DTTM
				from &work_lib..Status_add_transaction;
	quit;

	%if &syscc >4 %then
		%do;
			%let return_code=200;
			%let Data_Preparation_message=G3 Data Validation Completed: Failed;
		%end;
	%else 
		%if %sysfunc(exist(&work_lib..Status_add_transaction)) and %util_nobs(&work_lib..Status_add_transaction) > 0 and 
			%sysfunc(exist(status.property_status)) and %util_nobs(status.property_status) > 0 %then
		%do;
			%let return_code=0;
			%let Data_Preparation_message=G3 Data Validation Completed: Successful;
		%end;
	%else
		%do;
			%let return_code=100;
			%let Data_Preparation_message=G3 Data Validation Completed: Failed;
		%end;
%mend DP_Update_Property_Status;

%macro DP_Output_Files(Property=,rc=);
	%let Number_Total_checked=%util_nobs(&work_lib..Audit_Property_File_Differences);
	%let status_description=%str(&validation_desc. - Check Sheet for Differences);

	data &work_lib..summary_counts;
		format Description $50.;
		format Count 9.;
		format snapshot_dt date9.;
		snapshot_dt=&Current_Snapshot_Date.;
		Description="Current Snapshot Date: "||PUT(snapshot_dt, date9.);
		output &work_lib..summary_counts;

		if &backfillcount.>0 then
			Description="Pace Backfilled: YES";
		else Description="Pace Backfilled: NO";
		output &work_lib..summary_counts;
	run;

	%let property_desc=%BQUOTE(%SYSFUNC(trim(&client_ID.)):%SYSFUNC(trim(&property.)) - %SYSFUNC(trim(&property_name.)));
	ods listing close;
	ods excel file="&Root_Directory.&slash.clientoutput&slash.&Client_ID._&Property._Validation_Summary.xlsx" 
		options( embedded_titles='Yes' embedded_footnotes='yes' suppress_bylines='no');
	ods excel options(sheet_interval='none' sheet_name="Occupancy Validation");

	%IF %SYSFUNC(EXIST(&work_lib..summary_counts)) = 1 and %util_nobs(&work_lib..summary_counts) >0 %THEN
		%DO;

			proc Print data= &work_lib..summary_counts label noobs width = minimum;
				title1 bold h=13pt f='Courier New' color=black 'G3 Occupancy Date Validation';
				title2 bold h=13pt f='Courier New' color=black "&property_desc";
				title3 ' ';
				title4 bold h=13pt f='Courier New' color=black "&File_desc.";
				var description;
				label description="Validation Description";
			run;

			title;
		%end;

	%IF %SYSFUNC(EXIST(&work_lib..Audit_Property_File_Differences)) = 1 and %util_nobs(&work_lib..Audit_Property_File_Differences) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit - Client/G3 Validation");

			proc Report data=&work_lib..Audit_Property_File_Differences nowd nofs;
				columns property_id occupancy_dt Snapshot_dt Property_source 

					diff_rooms_sold percent_diff_rooms_sold Diff_Room_Revenue percent_diff_Room_Revenue diff_arrivals diff_departures diff_no_shows diff_cancellations 
					diff_trans_rooms_sold percent_diff_Trans_rooms_sold Diff_trans_Room_Revenue percent_diff_Trans_room_revenue diff_trans_arrivals diff_trans_departures diff_trans_no_shows diff_trans_cancellations 
					diff_group_rooms_sold percent_diff_Group_rooms_sold Diff_group_Room_Revenue percent_diff_Group_Room_Revenue 
					diff_capacity diff_rooms_not_avail_maint diff_rooms_not_avail_other
					rooms_sold Trans_rooms_sold Property_rooms_sold Group_rooms_sold Property_Group_rooms_sold Room_Revenue Trans_Room_Revenue Property_Room_revenue Group_Room_Revenue Property_group_room_revenue
					arrivals Trans_arrivals Property_arrivals Group_arrivals 
					departures Trans_departures Property_departures Group_departures no_shows trans_no_shows Property_no_shows 
					cancellations Trans_cancellations Property_cancellations 
					total_accom_capacity Property_total_accom_capacity rooms_not_avail_maint Property_rooms_not_avail_maint rooms_not_avail_other Property_rooms_not_avail_other;
				define property_id /display width=10 "Property";
				define occupancy_dt /display width=9 "Occupancy Date";
				define Snapshot_dt /display width=17 "Snapshot Date";
				define Property_source /display width=15 "Source";
				define diff_rooms_sold /display width=14 "Diff G3 Rooms Sold";
				define percent_diff_rooms_sold /display width=14 format=8.2 '%Diff G3 Rooms Sold';
				define diff_arrivals /display width=13 "Diff G3 Arrivals";
				define diff_departures /display width=15 "Diff G3 Departures";
				define diff_no_shows /display width=12 "Diff G3 No Shows";
				define diff_cancellations /display width=17 "Diff G3 Cancels";
				define Diff_Room_Revenue /display width=10 format=9.2 "Diff G3 Room Rev";
				define percent_diff_Room_Revenue /display width=10 format=8.2 '%Diff G3 Room Rev';
				define diff_capacity /display width=12 "Diff G3 Capacity";
				define diff_rooms_not_avail_maint /display width=14 "Diff G3 OOO Maint";
				define diff_rooms_not_avail_other /display width=14 "Diff G3 OOO Other";
				define diff_trans_rooms_sold /display width=14 "Diff G3Trans Rooms Sold";
				define percent_diff_Trans_rooms_sold /display width=14 format=8.2 '%Diff G3Trans Rooms Sold';
				define diff_trans_arrivals /display width=13 "Diff G3Trans Arrivals";
				define diff_trans_departures /display width=15 "Diff G3Trans Departures";
				define diff_trans_no_shows /display width=12 "Diff G3Trans No Shows";
				define diff_trans_cancellations /display width=17 "Diff G3Trans Cancels";
				define Diff_trans_Room_Revenue /display width=10 format=9.2 "Diff G3Trans Room Rev";
				define percent_diff_Trans_room_revenue /display width=10 format=8.2 '%Diff G3Trans Room Rev';
				define diff_group_rooms_sold /display width=14 "Diff G3Group Rooms Sold";
				define percent_diff_Group_rooms_sold /display width=14 format=8.2 '%Diff G3Group Rooms Sold';
				define Diff_group_Room_Revenue /display width=10 format=9.2 "Diff G3Group Room Rev";
				define percent_diff_Group_Room_Revenue /display width=10 format=8.2 '%Diff G3Group Room Rev';
				define arrivals /display width=8 "G3 Arrivals";
				define Trans_arrivals /display width=8 "G3Trans Arrivals";
				define Group_arrivals /display width=8 "G3Group Block Arrivals";
				define Property_arrivals /display width=11 "File Arrivals";
				define departures /display width=10 "G3 Departures";
				define Trans_departures /display width=10 "G3Trans Departures";
				define Group_departures /display width=10 "G3Group Block Departures";
				define Property_departures /display width=13 "File Departures";
				define no_shows /display width=8 "G3 NoShows";
				define trans_no_shows /display width=8 "G3Trans NoShows";
				define Property_no_shows /display width=10 "File No Shows";
				define cancellations /display width=8 "G3 Cancels";
				define Trans_cancellations /display width=8 "G3Trans Cancels";
				define Property_cancellations /display width=11 "(Not Provided) File Cancels";
				define Room_Revenue /display width=10 format=9.2 "G3 Room Rev";
				define Trans_Room_Revenue /display width=10 format=9.2 "G3Trans Room Rev";
				define Group_Room_Revenue /display width=10 format=9.2 "G3Group Room Rev";
				define Property_Room_revenue /display width=11 format=9.2 "File Room Rev";
				define Property_Group_Room_revenue /display width=11 format=9.2 "File Group Room Rev";
				define rooms_sold /display width=10 "G3 Rooms Sold";
				define Trans_rooms_sold /display width=10 "G3Trans Rooms Sold";
				define Group_rooms_sold /display width=10 "G3Group Rooms Sold";
				define Property_rooms_sold /display width=13 "File Rooms Sold";
				define Property_Group_rooms_sold /display width=13 "File Group Rooms Sold";
				define total_accom_capacity /display width=8 "G3 Capacity";
				define Property_total_accom_capacity /display width=12 "File Capacity";
				define rooms_not_avail_maint /display width=8 "G3 OOO Maint";
				define Property_rooms_not_avail_maint /display width=12 "File OOO Maint";
				define rooms_not_avail_other /display width=8 "G3 OOO Other";
				define Property_rooms_not_avail_other /display width=12 "File OOO Other";
			run;

		%end;

	%IF %SYSFUNC(EXIST(&work_lib..custom_query_no_group_block)) = 1 and %util_nobs(&work_lib..custom_query_no_group_block) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit - G3 Only No Group ");

			proc Print data= &work_lib..custom_query_no_group_block label noobs width = minimum;
			run;

		%end;

	%IF %SYSFUNC(EXIST(&work_lib..Audit_differences)) = 1 and %util_nobs(&work_lib..Audit_differences) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit - G3 Only With Group");

			proc Report data= &work_lib..Audit_differences nowd nofs;
				columns property_id source snapshot_dt occupancy_dt Diff_roomssold Diff_RoomRevenuePercent Diff_arrivals Diff_departures Diff_no_shows Diff_cancellations 
					available_capacity available_capacity_without_SS Trans_rooms_sold Stat_Rooms_sold Group_Rooms_sold Group_Definite_Blocks Group_definite_pickup Group_definite_solds Group_Tentative_Blocks Group_Tentative_pickup Group_Tentative_solds Reserved_Solds total_accom_capacity Cancels_By_arrival;
				define property_id /display width=10 "Property";
				define occupancy_dt /display width=9 "Occupancy Date";
				define Snapshot_dt /display width=17 "Snapshot Date";
				define source /display width=15 "Source";
				define Diff_roomssold /display width=14 "Diff G3 Rooms Sold";
				define Diff_RoomRevenuePercent /display width=14 "Diff G3 Room Rev %";
				define Diff_arrivals /display width=14 "Diff G3 Arrivals";
				define Diff_departures /display width=14 "Diff G3 Departures";
				define Diff_no_shows /display width=14 "Diff G3 No Shows";
				define Diff_cancellations /display width=14 "Diff G3Cancels";
				define available_capacity /display width=14 "Available G3 Capacity(Capacity-Solds)";
				define available_capacity_without_ss /display width=14 "Available G3 Capacity(Capacity-Solds No SS)";
				define Trans_rooms_sold /display width=10 "G3Trans Rooms Sold";
				define Group_rooms_sold /display width=10 "G3 Group Rooms Sold";
				define stat_rooms_sold /display width=10 "G3Summary Rooms Sold";
				define Reserved_Solds /display width=10 "G3 SS Rooms sold";
				define total_accom_capacity/display width=10 "G3 Capacity";
				define Cancels_By_arrival/display width=10 "G3 Cancels By Arrival Dt";
				define Group_Definite_Blocks/display width=14 "G3 Definite Group Blocks";
				define Group_Definite_Pickup/display width=14 "G3 Definite Group Pickup";
				define Group_Definite_Solds/display width=14 "G3 Definite Group Solds";
				define Group_Tentative_Blocks/display width=14 "G3 Tentative Group Blocks";
				define Group_Tentative_Pickup/display width=14 "G3 Tentative Group Pickup";
				define Group_Tentative_Solds/display width=14 "G3 Tentative Group Solds";

				compute available_capacity;

					if available_capacity<0 then
						call define(_col_,"style","style={background=red}");
				endcomp;

				compute available_capacity_without_ss;

					if available_capacity_without_ss<0 then
						call define(_col_,"style","style={background=yellow");
				endcomp;
			run;

		%end;

	%IF %SYSFUNC(EXIST(&work_lib..g3_group_block)) = 1 and %util_nobs(&work_lib..g3_group_block) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit - Group Block Detail");

			proc Print data= &work_lib..g3_group_block label noobs width = minimum;
				var property_id occupancy_dt accom_type_id group_id Group_code Group_name Group_Status_code blocks pickup solds rate original_blocks;
				label Property_id="Property";
				label Occupancy_dt="Occupancy Dt";
				label group_id="Group ID";
				label Group_name="Group Name";
				label Group_Status_code="Group Status";
				label Accom_type_id="Accom Type";
				label blocks="Blocks";
				label pickup="Pickup";
				label solds="Solds";
				label original_blocks="Original Blocks";
			run;

		%end;

	%IF %SYSFUNC(EXIST(&work_lib..G3_Group_master)) = 1 and %util_nobs(&work_lib..G3_Group_master) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit - Group Master Detail");

			proc Print data= &work_lib..G3_Group_master label noobs width = minimum;
				var property_id group_id Group_code Group_name Group_Description Group_status_code mkt_seg_code start_dt end_dt Pickup_type_code cancel_dt booking_type sales_person cut_off_date Cut_off_days;
				label Property_id="Property";
				label group_id="Group ID";
				label Group_code="Group Code";
				label Group_name="Group Name";
				label Group_Description="Group Description";
				label Group_Status_code="Group Status";
				label Mkt_seg_code="Mkt Seg";
				label Start_dt="Start Date";
				label end_dt="End Date";
				label cancel_dt="Cancel Date";
				label booking_type="Booking Type";
				label sales_person="Sales Person";
				label cut_off_date="CutOff Date";
				label Cut_off_days="CutOff Days";
			run;

		%end;

	/* 	%IF %SYSFUNC(EXIST(&work_lib..G3_Transactions)) = 1 and %util_nobs(&work_lib..G3_Transactions) >0 and %upcase(&debug.)=Y %THEN
	%DO;
	ods excel options(sheet_interval='proc' sheet_name="Audit- Reservations");

	proc Print data= &work_lib..G3_Transactions label noobs width = minimum;
	var property_id  reservation_identifier mkt_seg_code accom_type_code room_number individual_status booking_dt arrival_dt Departure_dt Cancellation_dt rate_code Room_revenue Total_revenue;
	label Property_id="Property";
	label reservation_identifier="Reservation ID";
	label arrival_dt="Arrival Date";
	label booking_dt="Booking Date";
	label departure_dt="Departure Date";
	label cancellation_dt="Cancel Date";
	label Room_revenue="Room Revenue";
	label Total_revenue="Total Revenue";
	label Rate_code="Rate Code";
	label Room_number="Room Number";
	label mkt_seg_code="Mkt Seg";
	label accom_type_code="Accom Type";
	run;

	%end; */
	%IF %SYSFUNC(EXIST(&work_lib..G3_Transactions_Exploded)) = 1 and %util_nobs(&work_lib..G3_Transactions_Exploded) >0 %then
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit- Reservations By Stay Date");

			proc Print data= &work_lib..G3_Transactions_Exploded label noobs width = minimum;
				var property_id  reservation_identifier mkt_seg_code accom_type_code room_number individual_status booking_dt occupancy_dt arrival_dt Departure_dt Cancellation_dt rate_code Room_revenue Total_revenue;
				label Property_id="Property";
				label occupancy_dt="Occupancy Date";
				label reservation_identifier="Reservation ID";
				label booking_dt="Booking Date";
				label arrival_dt="Arrival Date";
				label departure_dt="Departure Date";
				label cancellation_dt="Cancel Date";
				label Room_revenue="Room Revenue";
				label Total_revenue="Total Revenue";
				label Rate_code="Rate Code";
				label Room_number="Room Number";
				label mkt_seg_code="Mkt Seg";
				label accom_type_code="Accom Type";
			run;

		%end;

	%IF %SYSFUNC(EXIST(&work_lib..reservation_audit)) = 1 and %util_nobs(&work_lib..reservation_audit) >0 %THEN
		%DO;
			ods excel options(sheet_interval='proc' sheet_name="Audit- Reservations");

			proc Print data= &work_lib..reservation_audit label noobs width = minimum;
				var property_id reservation_identifier individual_status status;
				label Property_id="Property";
				label reservation_identifier="Reservation ID";
				label individual_status="Status";
				label status="Match Status";
			run;

		%end;

	ods excel close;
	ods _all_ close;
	ods listing close;

	%if &syscc >4 %then
		%do;
			%let return_code=200;
		%end;
	%else %let return_code=0;

	%if %upcase(&debug.)=Y %then
		%do;
			options compress=yes;

			proc Copy in=work out=&Savework_lib. move 
				memtype=Data
				NOCLONE 
				CONSTRAINT=YES 
				INDEX=YES;
			run;

			options compress=no;
		%end;

	%if %upcase(&debug.)=N %then
		%do;

			proc Datasets library=&Savework_lib. nolist noprint kill memtype=data;
			run;

			quit;

		%end;

	%if &syscc >4 %then
		%let return_code=200;
	%else %let return_code=0;
	libname Output clear;
%mend DP_Output_Files;

%macro DP_Output_Email_Response(property=,rc=);
	options emailsys  = smtp;
	options emailhost=mailhost.ideasprod.int;
	filename outbox email '<EMAIL>';

	%if &debug.=Y %then
		%let outbox_list=%str(("Installation Validation   <<EMAIL>>"));
	%else %let outbox_list=%str(("Installation Validation  <<EMAIL>>"));

	%if %sysfunc(fileexist("&Root_Directory.&slash.clientoutput&slash.&Client_ID._&Property._Validation_Summary.xlsx")) %then
		%do;
			%let File_Directory=&Root_Directory.&slash.clientoutput;
			%let zip_directory=&Root_Directory.&slash.clientoutput;
			%let zip_name=&Client_ID._&Property._Validation_Summary.zip;
			%let file_name=&Client_ID._&Property._Validation_Summary;

			%util_zip_files(File_Directory=&File_directory.,File_Extention=xlsx,File_Name=&Client_ID._&Property._Validation_Summary,Zip_Directory=&Zip_Directory.,Zip_Name=&Zip_name.,Rc=&return_code.);

			data _null_;
				file outbox to = &outbox_list. 
					replyto = ("Installation Validation   <<EMAIL>>")
					from = "Installation Validation       <<EMAIL>>"
					subject = " G3 Data Validation for &Client_id. - &property. - &property_name.";
				put "Data Validation has completed for &client_ID. - &property. - &property_name. .";
				Put ' ';
				put "&Data_Preparation_message.";
				Put ' ';
				put "&Data_Preparation_message2.";
				put ' ';
			run;

		%end;
	%else
		%do;

			data _null_;
				file outbox to = &outbox_list. 
					replyto = ("Gary Lutz           <<EMAIL>>")
					from = "Gary Lutz        <<EMAIL>>"
					subject = " G3 Data Validation for &Client_id. - &property. - &property_name.";
				put "Data Validation has completed for &client_ID. - &property. - &property_name. .";
				Put ' ';
				put "&Data_Preparation_message.";
				Put ' ';
				put "&Data_Preparation_message2.";
				Put ' ';
			run;

			run;

		%end;

	systask command "cd &Root_Directory.&slash.clientoutput ; rm &Root_Directory.&slash.clientoutput&slash.&Client_ID._&Property._Validation_Summary.xlsx" wait shell cleanup;
%mend DP_Output_Email_Response;

%macro Run_SQL( results_lib=, results_table=, query=, db_connect_str= );

	proc sql;
		connect to odbc as myodbc (&db_connect_str.);
		create table &results_lib..&results_table. as select * from connection to myodbc (
			%unquote( &query. )
			);
		disconnect from myodbc;
	quit;

%mend Run_SQL;

options nolabel;
%global db_pacman_connect_str no_block_query block_query Pseudo_Room_Types_String unique_property_Id Property_key todays_date return_code server_environment Processing_Start_Date Processing_End_Date;
%global backfillcount Processing_Select_Start_Date Current_Snapshot_Date Snapshot_Date_LY;
%global data_preparation_message2 File_desc status property_code property_name dbname server_name server_inst port_number stage Property Data_Preparation_message;
%let syscc=0;
%let Processing_Start_Date=.;
%let Processing_End_Date=.;
%let Current_Snapshot_Date=.;
%let return_code=0;
%let todays_date=&sysdate9.;
%Let root_directory=&rootdirectory;
%let validation_desc=Total Property G3 Data Validated;

/*********************************  With G3Group block data **************************************************************/
%let block_query = %nrbquote(
	SELECT TotalActivity.Occupancy_DT,
	CASE WHEN GroupBlock.Rooms_Sold IS NULL THEN TotalActivity.Rooms_Sold-IndTrans.RoomSolds
	ELSE TotalActivity.Rooms_Sold-IndTrans.RoomSolds-GroupBlock.Rooms_Sold END AS Diff_RoomsSold ,
	IndTrans.RoomSolds AS Trans_Rooms_Sold,
	IndTrans.Room_Revenue AS Trans_Room_Revenue,
	IndTrans.Arrivals AS Trans_Arrivals,
	IndTrans.Departures AS Trans_Departures,
	IndTrans.Cancellations AS Trans_Cancellations,
	IndTrans.NoShows AS Trans_No_Shows,
	CASE WHEN GroupBlock.Rooms_Sold IS NULL THEN 0 
	ELSE GroupBlock.Rooms_Sold END AS Group_Rooms_Sold,
	CASE WHEN GroupBlock.Room_Revenue IS NULL THEN 0 
	ELSE GroupBlock.Room_Revenue END AS Group_Room_revenue,
	0 as Group_arrivals, 
	0 as Group_departures, 
	0 as Group_tentative_solds,
	0 as Group_tentative_blocks,
	0 as Group_tentative_Pickup,
	0 as Group_Definite_solds,
	0 as Group_Definite_blocks,
	0 as Group_Definite_Pickup,  
	TotalActivity.Rooms_Sold AS Stat_Rooms_sold,
	CASE WHEN TotalActivity.Room_Revenue= 0 THEN 0
	ELSE (TotalActivity.Room_Revenue-IndTrans.Room_Revenue -
	(CASE WHEN GroupBlock.Room_Revenue IS NULL THEN 0 
	ELSE GroupBlock.Room_Revenue END ))*100/TotalActivity.Room_Revenue END
	AS Diff_RoomRevenuePercent,
	TotalActivity.Arrivals-IndTrans.Arrivals AS Diff_Arrivals,
	TotalActivity.Departures-IndTrans.Departures AS Diff_Departures,
	TotalActivity.Cancellations-IndTrans.Cancellations AS Diff_Cancellations,
	TotalActivity.No_Shows-IndTrans.NoShows AS Diff_No_Shows
	FROM (
	SELECT StayDate,SUM (A.Arrivals)  AS Arrivals ,SUM(Cancellations) AS Cancellations,SUM(NoShows) AS NoShows,SUM(A.Departures) AS Departures ,SUM (RoomSolds) RoomSolds
	,ROUND(SUM(Room_Revenue),2) Room_Revenue,ROUND(SUM(Total_Revenue),2) Total_Revenue
	FROM
	(
	SELECT
	CASE WHEN Arrival_DT = StayDate 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS Arrivals
	,CASE WHEN StayDate < Departure_DT 
	AND stayDate=Arrival_DT 
	AND Individual_Status IN ('XX') THEN 1 
	ELSE 0 END AS Cancellations
	,CASE WHEN StayDate < Departure_DT 
	AND stayDate=Arrival_DT 
	AND Individual_Status IN ('NS') THEN 1 
	ELSE 0 END AS NoShows
	,CASE WHEN Departure_DT = StayDate 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS Departures
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS RoomSolds
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN Room_Revenue/DATEDIFF(D,arrival_dt,Departure_dt) 
	ELSE 0 END AS Room_Revenue
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN Total_Revenue/DATEDIFF(D,arrival_dt,Departure_dt) 
	ELSE 0 END AS Total_Revenue
	,Channel,StayDate,DATENAME(DW,StayDate)AS DOW,Accom_Type_ID,DATEDIFF(D,Arrival_DT,Departure_DT) AS los
	,Nationality,mkt_seg_ID,Rate_Code,Source_Booking
	FROM (
	SELECT CAST(calendar_date AS date)AS StayDate,IT.*
	FROM Individual_Trans IT CROSS JOIN calendar_dim c
	WHERE CAST(calendar_date AS date) BETWEEN Arrival_DT 
	AND Departure_DT ) B
		) A
		GROUP BY  StayDate
		) IndTrans,
		(
		SELECT * 
		FROM Total_Activity  ) TotalActivity LEFT JOIN
		(
		SELECT occupancy_dt,SUM(Rooms_Sold)Rooms_Sold,SUM(Room_Revenue) Room_Revenue 
		FROM (
		SELECT occupancy_dt,(blocks-pickup) AS Rooms_Sold , rate*(blocks-pickup) AS Room_Revenue
		FROM Group_Block 
		WHERE Group_ID IN (
		SELECT Group_ID 
		FROM Group_Master 
		WHERE Group_Status_Code = 'DEFINITE')) A 
		GROUP BY occupancy_dt) GroupBlock
		ON (TotalActivity.Occupancy_DT = GroupBlock.Occupancy_DT)
		WHERE IndTrans.StayDate = TotalActivity.Occupancy_DT
		/* and IndTrans.StayDate > '2016-09-09' */
		ORDER BY StayDate;
			);

/* and IndTrans.StayDate > '2016-09-09' */
%let no_block_query = %nrbquote(
	SELECT TotalActivity.Rooms_Sold-IndTrans.RoomSolds AS Diff_RoomsSold,
	CASE WHEN TotalActivity.Room_Revenue= 0 THEN 0 
	ELSE (TotalActivity.Room_Revenue-IndTrans.Room_Revenue)*100/TotalActivity.Room_Revenue END AS Diff_RoomRevenuePercent,
	TotalActivity.Arrivals-IndTrans.Arrivals AS Diff_Arrivals,
	TotalActivity.Departures-IndTrans.Departures AS Diff_Departures,
	TotalActivity.Cancellations-IndTrans.Cancellations AS Diff_Cancellations,
	TotalActivity.No_Shows-IndTrans.NoShows AS Diff_No_Shows
	,* 
	FROM (
	SELECT StayDate,SUM (A.Arrivals)  AS Arrivals ,SUM(Cancellations) AS Cancellations,SUM(NoShows) AS NoShows,SUM(A.Departures) AS Departures ,SUM (RoomSolds) RoomSolds
	,ROUND(SUM(Room_Revenue),2) Room_Revenue,ROUND(SUM(Total_Revenue),2) Total_Revenue,
	CASE WHEN SUM(RoomSolds)= 0 THEN 0 
	ELSE  SUM(Room_Revenue)/SUM(RoomSolds) END AS AvgRoomRevenue
	,CASE WHEN SUM(RoomSolds)= 0 THEN 0 
	ELSE SUM(Total_Revenue)/SUM(RoomSolds) END AS AvgTotalRevenue
	FROM
	(SELECT
	CASE WHEN Arrival_DT = StayDate 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS Arrivals
	,CASE WHEN StayDate < Departure_DT 
	AND stayDate=Arrival_DT 
	AND Individual_Status IN ('XX') THEN 1 
	ELSE 0 END AS Cancellations
	,CASE WHEN StayDate < Departure_DT 
	AND stayDate=Arrival_DT 
	AND Individual_Status IN ('NS') THEN 1 
	ELSE 0 END AS NoShows
	,CASE WHEN Departure_DT = StayDate 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS Departures
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN 1 
	ELSE 0 END AS RoomSolds
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN Room_Revenue/DATEDIFF(D,arrival_dt,Departure_dt) 
	ELSE 0 END AS Room_Revenue
	,CASE WHEN StayDate < Departure_DT 
	AND Individual_Status NOT IN ('XX','NS') THEN Total_Revenue/DATEDIFF(D,arrival_dt,Departure_dt) 
	ELSE 0 END AS Total_Revenue
	,Channel,StayDate,DATENAME(DW,StayDate)AS DOW,Accom_Type_ID,DATEDIFF(D,Arrival_DT,Departure_DT) AS los
	,Nationality,mkt_seg_ID,Rate_Code,Source_Booking 
	FROM (
	SELECT CAST(calendar_date AS date)AS StayDate,IT.* 
	FROM Individual_Trans IT CROSS JOIN calendar_dim c
	WHERE CAST(calendar_date AS date) BETWEEN Arrival_DT 
	AND Departure_DT ) B    ) A
	GROUP BY  StayDate
		) IndTrans,
		( SELECT * FROM Total_Activity  ) TotalActivity
		WHERE IndTrans.StayDate = TotalActivity.Occupancy_DT 
		ORDER BY StayDate;
			);

%DP_Process_Data_Preparation;
run;