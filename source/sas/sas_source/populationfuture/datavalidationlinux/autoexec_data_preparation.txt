     options replace;
    options mprint;
    options NOSOURCE;
    OPTIONS COMPRESS=NO;

    /* ********us2plms01.ideasprod.int**************************************** Global Macro Variables ***************************************************/
    %global RootDirectory Slash work_lib;
    %global Client_ID Property Global_Servers Global_ports Debug ;
    %let debug=dummydebug;
    %let work_lib=work;
    %let Property=dummyproperty;
    %let Client_ID=dummyclient;
    %let Global_Servers=mn5pg3xdbsw001.ideasprod.int|us2p3db21.ideasprod.int;
    %let Global_ports=1433|1433;
  
%macro Set_Defaults;
    %let sysop = &SYSSCP &SYSSCPL;

    %if %index(&SYSOP,WIN) > 0 %then
        %do;
            %let Slash=\;
            %let RootDirectory=C:&Slash.cps;
        %end;
    %else
        %do;
            %let Slash=/;
            %let RootDirectory=&Slash.cps;
        %end;

    filename cmmacros "&RootDirectory&slash.hoteljobs&slash.autocall&slash.common";
    filename dpmacros "&RootDirectory&slash.hoteljobs&slash.autocall&slash.datapreparation";
    options mautosource sasautos=(sasautos cmmacros dpmacros );

%MEND;

%Set_Defaults;
%put Root Directory: &RootDirectory;
