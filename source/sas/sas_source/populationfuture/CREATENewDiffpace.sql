SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Mkt_Bk_Accom_Activity](
	[Mkt_Bk_Accom_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Occupancy_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Rooms_Sold] [numeric](18, 0) NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Departures] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Food_Revenue] [numeric](19, 5) NOT NULL,
	[Total_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Mkt_Bk_Accom_Activity] PRIMARY KEY CLUSTERED 
(
	[Mkt_Bk_Accom_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Mkt_Bk_Accom_Activity] ON [dbo].[Mkt_Bk_Accom_Activity] 
(
	[Property_ID] ASC,
	[Occupancy_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC 

)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Rooms_Sold]  DEFAULT ((0)) FOR [Rooms_Sold]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Arrivals]  DEFAULT ((0)) FOR [Arrivals]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Departures]  DEFAULT ((0)) FOR [Departures]
;
ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Cancellations]  DEFAULT ((0)) FOR [Cancellations]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Food_Revenue]  DEFAULT ((0)) FOR [Food_Revenue]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Total_Revenue]  DEFAULT ((0)) FOR [Total_Revenue]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Activity_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]


ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Accom_Type]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Activity_File_Metadata]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Mkt_Seg]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Activity_Property]



SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Mkt_Bk_Accom_Los_Activity](
	[Mkt_Bk_Accom_Los_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Mkt_Bk_Accom_Los_Activity] PRIMARY KEY CLUSTERED 
(
	[Mkt_Bk_Accom_Los_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Mkt_Bk_Accom_Los_Activity] ON [dbo].[Mkt_Bk_Accom_Los_Activity] 
(
	[Property_ID] ASC,
	[Arrival_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC, 
	[Los] ASC 
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;


ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_Arrivals]  DEFAULT ((0)) FOR [Arrivals]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_Cancellations]  DEFAULT ((0)) FOR [Cancellations]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Bk_Accom_Los_Activity_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]


ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Accom_Type]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_File_Metadata]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Mkt_Seg]

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Bk_Accom_Los_Activity_Property]

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[PACE_Mkt_Accom_Activity](
	[PACE_Mkt_Accom_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Occupancy_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Business_Day_End_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Rooms_Sold] [numeric](18, 0) NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Departures] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Food_Revenue] [numeric](19, 5) NOT NULL,
	[Total_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Month_ID] [int] NOT NULL,
	[Year_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_PACE_Mkt_Accom_Activity] PRIMARY KEY CLUSTERED 
(
	[PACE_Mkt_Accom_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE NONCLUSTERED INDEX [NC_pace_mkt_accom_activity_file_metadata] ON [dbo].[PACE_Mkt_Accom_Activity] 
(
	[File_Metadata_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_PACE_Mkt_Accom_Activity] ON [dbo].[PACE_Mkt_Accom_Activity] 
(
	[Property_ID] ASC,
	[Occupancy_DT] ASC,
	[Business_Day_End_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;


ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Activity_Food_Revenue]  DEFAULT ((0)) FOR [Food_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Activity_Total_Revenue]  DEFAULT ((0)) FOR [Total_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Acc_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;


ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Accom_Type]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Activity_File_Metadata]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Mkt_Seg]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Activity_Property]

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Mkt_Accom_Los_Activity](
	[Mkt_Accom_Los_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Total_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Mkt_Accom_Los_Activity] PRIMARY KEY CLUSTERED 
(
	[Mkt_Accom_Los_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Mkt_Accom_Los_Activity] ON [dbo].[Mkt_Accom_Los_Activity] 
(
	[Property_ID] ASC,
	[Arrival_DT] ASC,
	[SnapShot_DTTM],
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC, 
	[Los] ASC 
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;


ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_Arrivals]  DEFAULT ((0)) FOR [Arrivals]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_Cancellations]  DEFAULT ((0)) FOR [Cancellations]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_Total_Revenue]  DEFAULT ((0)) FOR [Total_Revenue]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_Mkt_Accom_Los_Activity_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
;

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Accom_Los_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Accom_Los_Activity_Accom_Type]

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Accom_Los_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Accom_Los_Activity_File_Metadata]

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Accom_Los_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Accom_Los_Activity_Mkt_Seg]

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_Mkt_Accom_Los_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_Mkt_Accom_Los_Activity_Property]

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[PACE_Mkt_Accom_Los_Activity](
	[PACE_Mkt_Accom_Los_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Business_Day_End_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Total_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Month_ID] [int] NOT NULL,
	[Year_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_PACE_Mkt_Accom_Los_Activity] PRIMARY KEY CLUSTERED 
(
	[PACE_Mkt_Accom_Los_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE NONCLUSTERED INDEX [NC_PACE_Mkt_Accom_Los_Activity_file_metadata] ON [dbo].[PACE_Mkt_Accom_Los_Activity] 
(
	[File_Metadata_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_PACE_Mkt_Accom_Los_Activity] ON [dbo].[PACE_Mkt_Accom_Los_Activity] 
(
	[Property_ID] ASC,
	[Arrival_DT] ASC,
	[Business_Day_End_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC,
	[Los] ASC
		
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;


ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Los_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Los_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Los_Activity_Total_Revenue]  DEFAULT ((0)) FOR [Total_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Accom_Los_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Accom_Type]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_File_Metadata]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Mkt_Seg]

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[PACE_Mkt_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Accom_Los_Activity_Property]

SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity](
	[PACE_Mkt_BkAccom_Los_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Business_Day_End_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Month_ID] [int] NOT NULL,
	[Year_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_PACE_Mkt_Bk_Accom_Los_Activity] PRIMARY KEY CLUSTERED 
(
	[PACE_Mkt_BkAccom_Los_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE NONCLUSTERED INDEX [NC_PACE_Mkt_Bk_Accom_Los_Activity_file_metadata] ON [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] 
(
	[File_Metadata_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_PACE_Mkt_Bk_Accom_Los_Activity] ON [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] 
(
	[Property_ID] ASC,
	[Arrival_DT] ASC,
	[Business_Day_End_DT],
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC, 
	[Los] ASC 
	
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Los_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Los_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Los_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Accom_Type]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_File_Metadata]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Mkt_Seg]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Los_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Los_Activity_Property]


SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity](
	[PACE_Mkt_BkAccom_Activity_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Occupancy_DT] [date] NOT NULL,
	[SnapShot_DTTM] [smalldatetime] NOT NULL,
	[Business_Day_End_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Rooms_Sold] [numeric](18, 0) NOT NULL,
	[Arrivals] [numeric](18, 0) NOT NULL,
	[Departures] [numeric](18, 0) NOT NULL,
	[Cancellations] [numeric](18, 0) NOT NULL,
	[No_Shows] [numeric](18, 0) NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[Month_ID] [int] NOT NULL,
	[Year_ID] [int] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
 CONSTRAINT [PK_PACE_Mkt_Bk_Accom_Activity] PRIMARY KEY CLUSTERED 
(
	[PACE_Mkt_BkAccom_Activity_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE NONCLUSTERED INDEX [NC_pace_Mkt_Bk_Accom_activity_file_metadata] ON [dbo].[PACE_Mkt_Bk_Accom_Activity] 
(
	[File_Metadata_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_PACE_Mkt_Bk_Accom_Activity] ON [dbo].[PACE_Mkt_Bk_Accom_Activity] 
(
	[Property_ID] ASC,
	[Occupancy_DT] ASC,
	[Business_Day_End_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Activity_No_Shows]  DEFAULT ((0)) FOR [No_Shows]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Activity_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] ADD  CONSTRAINT [DF_PACE_Mkt_Bk_Accom_Activity_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
;

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Accom_Type]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_File_Metadata]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Mkt_Seg]

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity]  WITH NOCHECK ADD  CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[PACE_Mkt_Bk_Accom_Activity] CHECK CONSTRAINT [FK_PACE_Mkt_Bk_Accom_Activity_Property]


SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Los_Extension_Info](
	[Los_Extension_Info_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Reservation_Identifier] [nvarchar](50) NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[NoOfRooms] [int] NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Los_Extension_Info] PRIMARY KEY CLUSTERED 
(
	[Los_Extension_Info_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Los_Extension_Info] ON [dbo].[Los_Extension_Info] 
(
	[Property_ID] ASC,
	[Reservation_Identifier] ASC,
	[Arrival_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC, 
	[Los] ASC 
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[Los_Extension_Info] ADD  CONSTRAINT [DF_Los_Extension_Info_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;
ALTER TABLE [dbo].[Los_Extension_Info] ADD  CONSTRAINT [DF_Los_Extension_Info_NoOfRooms]  DEFAULT ((0)) FOR [NoOfRooms]
;
ALTER TABLE [dbo].[Los_Extension_Info] ADD  CONSTRAINT [DF_Los_Extension_Info_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
;

ALTER TABLE [dbo].[Los_Extension_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Los_Extension_Info_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Los_Extension_Info] CHECK CONSTRAINT [FK_Los_Extension_Info_Accom_Type]

ALTER TABLE [dbo].[Los_Extension_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Los_Extension_Info_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Los_Extension_Info] CHECK CONSTRAINT [FK_Los_Extension_Info_File_Metadata]

ALTER TABLE [dbo].[Los_Extension_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Los_Extension_Info_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Los_Extension_Info] CHECK CONSTRAINT [FK_Los_Extension_Info_Mkt_Seg]

ALTER TABLE [dbo].[Los_Extension_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Los_Extension_Info_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Los_Extension_Info] CHECK CONSTRAINT [FK_Los_Extension_Info_Property]


SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Inhouse_Los_Chg_Summary](
	[Inhouse_Los_Chg_Summary_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Snapshot_DT] [date] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Mkt_Seg_ID_Updated] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Accom_Type_ID_Updated] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Los_Updated] [int] NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Room_Revenue_Updated] [numeric](19, 5) NOT NULL,
	[Arrivals] [int] NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Inhouse_Los_Chg_Summary] PRIMARY KEY CLUSTERED 
(
	[Inhouse_Los_Chg_Summary_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Inhouse_Los_Chg_Summary] ON [dbo].[Inhouse_Los_Chg_Summary] 
(
	[Property_ID] ASC,
	[Snapshot_DT] ASC,
	[Arrival_DT] ASC,
	[Mkt_Seg_ID] ASC,
	[Accom_Type_ID] ASC, 
	[Los] ASC, 
	[Mkt_Seg_ID_Updated] ASC,
	[Accom_Type_ID_Updated] ASC, 
	[Los_Updated] ASC 
		
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] ADD  CONSTRAINT [DF_Inhouse_Los_Chg_Summary_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] ADD  CONSTRAINT [DF_Inhouse_Los_Chg_Summary_Room_Revenue_Updated]  DEFAULT ((0)) FOR [Room_Revenue_Updated]
;

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] ADD  CONSTRAINT [DF_Inhouse_Los_Chg_Summary_Arrivals]  DEFAULT ((0)) FOR [Arrivals]
;
ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] ADD  CONSTRAINT [DF_Inhouse_Los_Chg_Summary_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
;

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD  CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Accom_Type]


ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD  CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Accom_Type_Updated] FOREIGN KEY([Accom_Type_ID_Updated])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Accom_Type_Updated]
ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD  CONSTRAINT [FK_Inhouse_Los_Chg_Summary_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_File_Metadata]

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD  CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Mkt_Seg]

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Mkt_Seg_Updated] FOREIGN KEY([Mkt_Seg_ID_Updated])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Mkt_Seg_Updated]

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary]  WITH NOCHECK ADD  CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Inhouse_Los_Chg_Summary] CHECK CONSTRAINT [FK_Inhouse_Los_Chg_Summary_Property]

SET ANSI_PADDING OFF


SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[Original_Info](
	[Original_Info_ID] [int] IDENTITY(1,1) NOT NULL,
	[Property_ID] [int] NOT NULL,
	[Reservation_Identifier] [nvarchar](50) NOT NULL,
	[Individual_Status] [nvarchar](2) NOT NULL,
	[Snapshot_DT] [date] NOT NULL,
	[Arrival_DT] [date] NOT NULL,
	[Departure_DT] [date] NOT NULL,
	[Mkt_Seg_ID] [int] NOT NULL,
	[Accom_Type_ID] [int] NOT NULL,
	[Los] [int] NOT NULL,
	[Room_Revenue] [numeric](19, 5) NOT NULL,
	[Food_Revenue] [numeric](19, 5) NOT NULL,
	[Total_Revenue] [numeric](19, 5) NOT NULL,
	[Arrivals] [int] NOT NULL,
	[Departures] [int] NOT NULL,
	[NoOfRooms] [int] NOT NULL,
	[File_Metadata_ID] [int] NOT NULL,
	[CreateDate] [datetime] NOT NULL,
 CONSTRAINT [PK_Original_Info] PRIMARY KEY CLUSTERED 
(
	[Original_Info_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
;
CREATE UNIQUE NONCLUSTERED INDEX [UQ_Original_Info] ON [dbo].[Original_Info] 
(
	[Reservation_Identifier] ASC,
	[Property_ID] ASC,
	[Arrival_DT] ASC
	
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
;

ALTER TABLE [dbo].[Original_Info] ADD  CONSTRAINT [DF_Original_Info_Room_Revenue]  DEFAULT ((0)) FOR [Room_Revenue]
;
ALTER TABLE [dbo].[Original_Info] ADD  CONSTRAINT [DF_Original_Info_Food_Revenue]  DEFAULT ((0)) FOR [Food_Revenue]
;
ALTER TABLE [dbo].[Original_Info] ADD  CONSTRAINT [DF_Original_Info_Total_Revenue]  DEFAULT ((0)) FOR [Total_Revenue]
;

ALTER TABLE [dbo].[Original_Info] ADD  CONSTRAINT [DF_Original_Info_Arrivals]  DEFAULT ((0)) FOR [Arrivals]
;
ALTER TABLE [dbo].[Original_Info] ADD  CONSTRAINT [DF_Original_Info_CreateDate]  DEFAULT (getdate()) FOR [CreateDate]
;

ALTER TABLE [dbo].[Original_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Original_Info_Accom_Type] FOREIGN KEY([Accom_Type_ID])
REFERENCES [dbo].[Accom_Type] ([Accom_Type_ID])

ALTER TABLE [dbo].[Original_Info] CHECK CONSTRAINT [FK_Original_Info_Accom_Type]

ALTER TABLE [dbo].[Original_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Original_Info_File_Metadata] FOREIGN KEY([File_Metadata_ID])
REFERENCES [dbo].[File_Metadata] ([File_Metadata_ID])

ALTER TABLE [dbo].[Original_Info] CHECK CONSTRAINT [FK_Original_Info_File_Metadata]

ALTER TABLE [dbo].[Original_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Original_Info_Mkt_Seg] FOREIGN KEY([Mkt_Seg_ID])
REFERENCES [dbo].[Mkt_Seg] ([Mkt_Seg_ID])

ALTER TABLE [dbo].[Original_Info] CHECK CONSTRAINT [FK_Original_Info_Mkt_Seg]

ALTER TABLE [dbo].[Original_Info]  WITH NOCHECK ADD  CONSTRAINT [FK_Original_Info_Property] FOREIGN KEY([Property_ID])
REFERENCES [dbo].[Property] ([Property_ID])

ALTER TABLE [dbo].[Original_Info] CHECK CONSTRAINT [FK_Original_Info_Property]

SET ANSI_PADDING OFF