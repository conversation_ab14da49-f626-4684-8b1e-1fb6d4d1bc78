<?xml version="1.0" encoding="windows-1252"?>

<!-- ############################################################ -->
<!-- 2011-05-23T14:19:44 -->
<!-- SAS XML Libname Engine Map -->
<!-- Generated by XML Mapper, 902000.3.6.20090116170000_v920 -->
<!-- ############################################################ -->
<!-- ###  Validation report                                   ### -->
<!-- ############################################################ -->
<!-- XMLMap validation completed successfully. -->
<!-- ############################################################ -->
<SXLEMAP name="hilstarpopulation" version="1.2">

    <!-- ############################################################ -->
    <TABLE name="HilstarSASRequest">
        <TABLE-PATH syntax="XPath">/HilstarSASRequest</TABLE-PATH>

        <COLUMN name="RequestHeader">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="Type2extractRequestData">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="RequestHeader">
        <TABLE-PATH syntax="XPath">/HilstarSASRequest/RequestHeader</TABLE-PATH>

        <COLUMN name="PropertyId">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/PropertyId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="operationName">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/operationName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
            <ENUM>
                <VALUE>createForecastGroup</VALUE>
                <VALUE>commitForecastGroup</VALUE>
                <VALUE>forecast</VALUE>
                <VALUE>optimization</VALUE>
                <VALUE>population</VALUE>
                <VALUE>createType2Extract</VALUE>
                <VALUE>hilstartypetwoextract</VALUE>
                <VALUE>type3population</VALUE>
            </ENUM>
        </COLUMN>

        <COLUMN name="resume">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/resume</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="requestId">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/requestId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_server">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_server</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>256</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_server_instance">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_server_instance</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_password">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_password</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_user">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_user</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_database">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_database</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="tenant_db_port">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/tenant_db_port</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="dataset_path">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/dataset_path</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="whatif">
            <PATH syntax="XPath">/HilstarSASRequest/RequestHeader/whatif</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="Type2extractRequestData">
        <TABLE-PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData</TABLE-PATH>

        <COLUMN name="propertyId">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/propertyId</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="clientCode">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/clientCode</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="rmsExtractFileName">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/rmsExtractFileName</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="rmsFilePath">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/rmsFilePath</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="pastDays">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/pastDays</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="futureDays">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/futureDays</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="processRmsRates">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/processRmsRates</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="yieldCurrencyCode">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/yieldCurrencyCode</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="sasXleMapFile">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/sasXleMapFile</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="sasDataSetPath">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/sasDataSetPath</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>52</LENGTH>
        </COLUMN>

        <COLUMN name="crsTimeOffSet">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/crsTimeOffSet</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="propertyTimeOffSet">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/propertyTimeOffSet</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="usenewrmcostraw">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/usenewrmcostraw</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="ispcrs">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/ispcrs</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="mktratedata">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/mktratedata</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="usemktoccupancyrates">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/usemktoccupancyrates</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="useSRPIDsubstitution">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/useSRPIDsubstitution</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="srpiddata">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/srpiddata</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="applytax">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/applytax</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>
        
        <COLUMN name="validationlogpath">
	            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/validationlogpath</PATH>
	            <TYPE>character</TYPE>
	            <DATATYPE>string</DATATYPE>
	            <LENGTH>200</LENGTH>
        </COLUMN>
        
        <COLUMN name="resetGRPConAval">
			<PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/resetGRPConAval</PATH>
			<TYPE>character</TYPE>
			<DATATYPE>string</DATATYPE>
			<LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="mktratedata">
        <TABLE-PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/mktratedata</TABLE-PATH>

        <COLUMN name="mktcode">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/mktratedata/mktcode</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="ratetype">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/mktratedata/ratetype</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

    <!-- ############################################################ -->
    <TABLE name="srpiddata">
        <TABLE-PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/srpiddata</TABLE-PATH>

        <COLUMN name="propID">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/srpiddata/propID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

        <COLUMN name="srpID">
            <PATH syntax="XPath">/HilstarSASRequest/Type2extractRequestData/srpiddata/srpID</PATH>
            <TYPE>character</TYPE>
            <DATATYPE>string</DATATYPE>
            <LENGTH>32</LENGTH>
        </COLUMN>

    </TABLE>

</SXLEMAP>
