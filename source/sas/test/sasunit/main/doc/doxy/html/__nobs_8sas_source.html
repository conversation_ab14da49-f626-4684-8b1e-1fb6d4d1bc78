<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: _nobs.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('__nobs_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">_nobs.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="__nobs_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;%MACRO _nobs(i_data</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;            );</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;   %local dsid nobs;</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;   %let nobs=0;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;   %let dsid=%sysfunc(open(&amp;i_data));</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;   %<span class="keywordflow">if</span> &amp;dsid&gt;0 %then %<span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;      %let nobs=%sysfunc(attrn(&amp;dsid,nlobs));</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;      %let dsid=%sysfunc(close(&amp;dsid));</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;   %end;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;&amp;nobs</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;%MEND _nobs;</div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="__nobs_8sas.html">_nobs.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:08 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
