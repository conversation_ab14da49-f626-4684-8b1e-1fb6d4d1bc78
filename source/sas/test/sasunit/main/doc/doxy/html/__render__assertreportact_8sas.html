<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: _render_assertreportact.sas File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('__render__assertreportact_8sas.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">_render_assertreportact.sas File Reference<div class="ingroups"><a class="el" href="group__SASUNIT__REPORT.html">Reporting</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>renders the layout of the actual column for assertReport  
<a href="#details">More...</a></p>

<p><a href="__render__assertreportact_8sas_source.html">Go to the source code of this file.</a></p>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>renders the layout of the actual column for assertReport </p>
<dl class="section version"><dt>Version</dt><dd>$Revision: 319 $ </dd></dl>
<dl class="section author"><dt>Author</dt><dd>$Author: klandwich $ </dd></dl>
<dl class="section date"><dt>Date</dt><dd>$Date: 2014-03-16 12:28:51 +0100 (So, 16 Mrz 2014) $ </dd></dl>
<dl class="section see"><dt>See Also</dt><dd>For further information please refer to <a href="https://sourceforge.net/p/sasunit/wiki/User&apos;s%20Guide/" target="_blank">SASUnit User's Guide</a> </dd>
<dd>
$HeadURL: <a href="https://svn.code.sf.net/p/sasunit/code/trunk/saspgm/sasunit/_render_assertreportact.sas">https://svn.code.sf.net/p/sasunit/code/trunk/saspgm/sasunit/_render_assertreportact.sas</a> $ </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Copyright 2010, 2012 HMS Analytical Software GmbH. This file is part of SASUnit, the Unit testing framework for SAS(R) programs. For terms of usage under the GPL license see included file readme.txt or <a href="https://sourceforge.net/p/sasunit/wiki/readme.v1.2/">https://sourceforge.net/p/sasunit/wiki/readme.v1.2/</a>.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">i_sourceColumn</td><td>name of the column holding the value </td></tr>
    <tr><td class="paramname">i_expectedColumn</td><td>name of the column holding the expected value.<em>(optional: Default=tst_exp)</em> </td></tr>
    <tr><td class="paramname">o_html</td><td>Test report in HTML-format? </td></tr>
    <tr><td class="paramname">o_targetColumn</td><td>name of the target column holding the ODS formatted value </td></tr>
  </table>
  </dd>
</dl>

<p>Definition in file <a class="el" href="__render__assertreportact_8sas_source.html">_render_assertreportact.sas</a>.</p>
</div></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="__render__assertreportact_8sas.html">_render_assertreportact.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:09 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
