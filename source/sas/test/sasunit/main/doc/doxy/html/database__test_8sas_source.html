<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: database_test.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('database__test_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">database_test.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="database__test_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="comment">/*-- Creation of base datasets -----------------------------------------*/</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;%initTestcase(i_object=basedatasets.sas, i_desc=Creation of base datasets)</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;%basedatasets</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;%endTestCall()</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;%assertTableExists (i_libref =work</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                   ,i_memname=Customer</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                   ,i_desc   =Table was successfuly created?</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                   )</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;%assertRecordCount (i_libref    =work</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                   ,i_memname   =Customer</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;                   ,i_recordsExp=5</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                   ,i_desc      =Does table contain all expected Rows?</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                   )</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;%assertPrimaryKey (i_library  =work</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                  ,i_dataset  =Customer</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                  ,i_variables=CustomerNumber</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;                  ,i_desc     =Is the generated key unique?</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                  )</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;%assertTableExists (i_libref =work</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                   ,i_memname=Contracts</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                   ,i_desc   =Table was successfuly created?</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                   )</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;%assertRecordCount (i_libref    =work</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                   ,i_memname   =Contracts</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                   ,i_recordsExp=8</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                   ,i_desc      =Does table contain all expected Rows?</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;                   )</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;%assertPrimaryKey (i_library  =work</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                  ,i_dataset  =Contracts</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                  ,i_variables=ContractNumber)</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;%assertRecordExists (i_dataset  =work.Contracts</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;                    ,i_whereExpr=%str(ContractType=1)</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                    ,i_desc     =Do we have at least one record per contract type 1?</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;                    );</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;%assertRecordExists (i_dataset  =work.Contracts</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                    ,i_whereExpr=%str(ContractType=2)</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                    ,i_desc     =Do we have at least one record per contract type 2?</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                    );</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;%assertRecordExists (i_dataset  =work.Contracts</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                    ,i_whereExpr=%str(ContractType=3)</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                    ,i_desc     =Do we have at least one record per contract type 3?</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                    );</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;%assertTableExists (i_libref =work</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                   ,i_memname=CustomerContracts</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                   ,i_desc   =Table was successfuly created?</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                   )</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;%assertRecordCount (i_libref    =work</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                   ,i_memname   =CustomerContracts</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                   ,i_recordsExp=8</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                   ,i_desc      =Does table contain all expected Rows?</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;                   )</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;%assertPrimaryKey (i_library  =work</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                  ,i_dataset  =CustomerContracts</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                  ,i_variables=ContractNumber CustomerNumber)</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;%assertForeignKey (i_mstrLib  =work</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                  ,i_mstMem   =CustomerContracts</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                  ,i_mstKey   =ContractNumber</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                  ,i_lookupLib=work</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                  ,i_lookupMem=Contracts</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                  ,i_lookupKey=ContractNumber</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                  ,i_desc     =Check ContractNumber</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                  );</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;%assertForeignKey (i_mstrLib  =work</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                  ,i_mstMem   =CustomerContracts</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                  ,i_mstKey   =CustomerNumber</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                  ,i_lookupLib=work</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;                  ,i_lookupMem=Customer</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;                  ,i_lookupKey=CustomerNumber</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;                  ,i_desc     =Check CustomerNumber</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;                  );</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;%assertLog (i_errors=0, i_warnings=0)</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;%endTestCase()</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="comment">/*-- Creation of combined table --------------------------------------------*/</span></div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;%initTestcase(i_object=combineddataset.sas, i_desc=Creating combined dataset)</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;%combineddataset</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;%endTestCall()</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;%assertTableExists (i_libref =work</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                   ,i_memname=CombinedDataset</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                   ,i_desc   =Table was successfuly created?</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                   )</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;%assertRecordCount (i_libref    =work</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;                   ,i_memname   =CombinedDataset</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;                   ,i_recordsExp=8</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;                   ,i_desc      =Does table contain all expected Rows?</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;                   )</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;%assertPrimaryKey (i_library  =work</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;                  ,i_dataset  =CombinedDataset</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;                  ,i_variables=ContractNumber CustomerNumber)</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;%assertRowExpression(i_libref  =work</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;                    ,i_memname =CustomerContracts</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;                    ,i_where   =%str(not missing %(ContractNumber%))</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;                    ,i_desc    =There should be no customers without contracts</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;                    );</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;%assertRowExpression(i_libref  =work</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;                    ,i_memname =CustomerContracts</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;                    ,i_where   =%str(not missing %(CustomerNumber%))</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;                    ,i_desc    =There should be no contracts without customer</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;                    );</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;%assertLog (i_errors=0, i_warnings=0)</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;%endTestCase()</div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_daf6843f8d8a856d8cd51ed41f14f715.html">saspgm</a></li><li class="navelem"><a class="el" href="database__test_8sas.html">database_test.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:08 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
