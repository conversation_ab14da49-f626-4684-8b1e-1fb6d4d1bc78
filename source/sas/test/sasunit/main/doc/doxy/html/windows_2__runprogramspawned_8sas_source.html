<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: _runprogramspawned.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('windows_2__runprogramspawned_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">windows/_runprogramspawned.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="windows_2__runprogramspawned_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;%macro _runProgramSpawned(i_program           =</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;                         ,i_scnid             = </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;                         ,i_generateMcoverage = 0</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;                         ,r_sysrc             = </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                         );</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;   %local l_cmdFile l_parms l_parenthesis l_tcgFilePath l_tcgOptionsString l_tcgOptionsStringLINUX l_rc l_macname;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;   %let l_macname=&amp;sysmacroname.;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;   </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;   <span class="comment">/*-- prepare sasuser ---------------------------------------------------*/</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;   %let l_cmdFile=%sysfunc(pathname(work))/prep_sasuser.cmd;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;   DATA _null_;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;      FILE <span class="stringliteral">&quot;&amp;l_cmdFile.&quot;</span>;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;      PUT <span class="stringliteral">&quot;&amp;g_removedir &quot;</span><span class="stringliteral">&quot;%sysfunc(pathname(work))/sasuser&quot;</span><span class="stringliteral">&quot;&amp;g_endcommand&quot;</span>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;      PUT <span class="stringliteral">&quot;&amp;g_makedir &quot;</span><span class="stringliteral">&quot;%sysfunc(pathname(work))/sasuser&quot;</span><span class="stringliteral">&quot;&amp;g_endcommand&quot;</span>;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;      %IF %length(&amp;g_sasuser) %THEN %DO;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;         PUT <span class="stringliteral">&quot;&amp;g_copydir &quot;</span><span class="stringliteral">&quot;&amp;g_sasuser&quot;</span><span class="stringliteral">&quot; &quot;</span><span class="stringliteral">&quot;%sysfunc(pathname(work))/sasuser&quot;</span><span class="stringliteral">&quot;&amp;g_endcommand&quot;</span>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;      %END;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;   RUN;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;   %_executeCMDFile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;   %LET l_rc=%_delfile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;   <span class="comment">/*-- set config and autoexec -------------------------------------------*/</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;   %let l_cmdFile=%sysfunc(pathname(work))/_runprogramspawned.cmd;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;   %LET l_parms=;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;   %LET l_parenthesis=(;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;   %IF <span class="stringliteral">&quot;&amp;g_autoexec&quot;</span> NE <span class="stringliteral">&quot;&quot;</span> %THEN %DO;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;      %LET l_parms=&amp;l_parms -autoexec <span class="stringliteral">&quot;&quot;</span>&amp;g_autoexec<span class="stringliteral">&quot;&quot;</span>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;   %END;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;   %IF <span class="stringliteral">&quot;&amp;g_sascfg&quot;</span> NE <span class="stringliteral">&quot;&quot;</span> %THEN %DO;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;      %LET l_parms=&amp;l_parms -config <span class="stringliteral">&quot;&quot;</span>&amp;g_sascfg<span class="stringliteral">&quot;&quot;</span>;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;   %END;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;   %ELSE %IF %length(%sysfunc(getoption(config))) NE 0 AND %index(%quote(%sysfunc(getoption(config))),%bquote(&amp;l_parenthesis)) NE 1 %THEN %DO; </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;     %LET l_parms=&amp;l_parms -config &quot;&quot;%sysfunc(getoption(config))&quot;&quot;;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;   %END; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;   %IF &amp;i_generateMcoverage. EQ 1 %THEN %DO;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      <span class="comment">/*-- generate a local macro variable containing the </span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="comment">           path to the generated coverage file if necessary ---------------*/</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      %LET   l_tcgFilePath      = &amp;g_log./&amp;i_scnid..tcg;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      %LET   l_tcgOptionsString = -mcoverage -mcoverageloc = &quot;&quot;&amp;l_tcgFilePath.&quot;&quot;;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;   %END;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;   DATA _null_;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;      ATTRIB</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;         _sCmdString LENGTH = $32000</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;      ;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;      FILE </div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;         &quot;&amp;l_cmdFile.&quot;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;         LRECL=32000</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      ;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    _sCmdString = </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;      &quot;&quot;&quot;&quot; !! &amp;g_sasstart !! &quot;&quot;&quot;&quot;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;      !! &quot; &quot; </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;      !! &quot;&amp;l_parms. &quot;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;      !! &quot;-sysin &quot;&quot;&amp;i_program.&quot;&quot; &quot;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      !! &quot;-initstmt &quot;&quot;%nrstr(%%%_scenario%(io_target=)&amp;g_target%nrstr(%);%%%let g_scnid=)&amp;i_scnid.;&quot;&quot; &quot;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      !! &quot;-log   &quot;&quot;&amp;g_log/&amp;i_scnid..log&quot;&quot; &quot;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;      !! &quot;-print &quot;&quot;&amp;g_testout/&amp;i_scnid..lst&quot;&quot; &quot;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;      !! &quot;&amp;g_splash &quot;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;      !! &quot;-noovp &quot;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      !! &quot;-icon &quot;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;      !! &quot;-nosyntaxcheck &quot;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;      !! &quot;-mautosource &quot;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      !! &quot;-mcompilenote all &quot;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      !! &quot;-sasautos &quot;&quot;&amp;g_sasunit&quot;&quot; &quot;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      !! &quot;-sasuser &quot;&quot;%sysfunc(pathname(work))/sasuser&quot;&quot; &quot;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;      !! &quot;-termstmt &quot;&quot;%nrstr(%%%_termScenario())&quot;&quot; &quot;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;      !! &quot;&amp;l_tcgOptionsString. &quot;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;      !! &quot;&quot;;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;      PUT _sCmdString;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;   RUN;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;   %_executeCMDFile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;   %LET &amp;r_sysrc. = &amp;sysrc.;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;   %LET l_rc=%_delfile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;   <span class="comment">/*-- delete sasuser ----------------------------------------------------*/</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;   %let l_cmdFile=%sysfunc(pathname(work))/del_sasuser.cmd;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;   DATA _null_;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;      FILE &quot;&amp;l_cmdFile.&quot;;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;      PUT &quot;&amp;g_removedir &quot;&quot;%sysfunc(pathname(work))/sasuser&quot;&quot;&amp;g_endcommand&quot;;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;   RUN;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;   %_executeCMDFile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;   %LET l_rc=%_delfile(&amp;l_cmdFile.);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;      </div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;%mend _runprogramspawned;   </div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="dir_4057a5ef4fec1e4d36e45ce09c9ea7ec.html">windows</a></li><li class="navelem"><a class="el" href="windows_2__runprogramspawned_8sas.html">_runprogramspawned.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:08 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
