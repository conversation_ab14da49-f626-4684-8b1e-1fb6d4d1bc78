<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: _asserts.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('__asserts_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">_asserts.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="__asserts_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;%MACRO _asserts (i_type     =       </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                ,i_expected =       </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;                ,i_actual   =       </div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;                ,i_desc     =       </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;                ,i_result   =   </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;                ,i_errMsg   = _NONE_ </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                ,r_casid    = _NONE_ </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                ,r_tstid    = _NONE_       </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                );</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;   %LOCAL l_errMsg;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;   %IF (&amp;r_casid=_NONE_) %THEN %DO;</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;      %LOCAL l_casid;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;      %LET r_casid=l_casid;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;   %END;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;   %IF (&amp;r_tstid=_NONE_) %THEN %DO;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;      %LOCAL l_tstid;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;      %LET r_tstid=l_tstid;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;   %END;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;   %IF (&amp;i_result. eq 0) %THEN %DO;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;      %LET l_errMsg =&amp;i_type.: assert passed.;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;   %END;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;   %ELSE %IF (&amp;i_result. eq 1) %THEN %DO;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;      %LET l_errMsg =&amp;i_type.: assert passed, but manual check necessary.;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;   %END;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;   %ELSE %DO;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;      %IF (%nrbquote(&amp;i_errMsg.) eq _NONE_) %THEN %DO;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;         %LET l_errMsg =%bquote(&amp;i_type. failed: expected value equals &amp;i_expected., but actual value equals &amp;i_actual.);</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;      %END;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      %ELSE %DO;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;         %LET l_errMsg =%bquote(&amp;i_type. failed: &amp;i_errMsg.);</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;      %END;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;   %END;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;   %IF (&amp;g_verbose.) %THEN %DO;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      %IF (&amp;i_result. NE 2) %THEN %DO;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;         %PUT &amp;G_NOTE.(SASUNIT): &amp;l_errMsg.;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;      %END;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;      %ELSE %DO;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;         %PUT &amp;G_ERROR.(SASUNIT): &amp;l_errMsg.;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      %END;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;   %END;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;   PROC SQL NOPRINT;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;      <span class="comment">/* determine number of test case */</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;      SELECT max(cas_id) INTO :&amp;r_casid FROM target.cas WHERE cas_scnid=&amp;g_scnid;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;      %IF &amp;&amp;&amp;r_casid=. %THEN %DO;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;         %PUT &amp;g_error.(SASUNIT): _asserts: Fehler beim Ermitteln der Testfall-Id;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;         %RETURN;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;      %END;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;      <span class="comment">/* generate a new check number */</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      SELECT max(tst_id) INTO :&amp;r_tstid </div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;      FROM target.tst </div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;      WHERE </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;         tst_scnid = &amp;g_scnid AND</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;         tst_casid = &amp;&amp;&amp;r_casid</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      ;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;      %IF &amp;&amp;&amp;r_tstid=. %THEN %LET &amp;r_tstid=1;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;      %ELSE                  %LET &amp;r_tstid=%eval(&amp;&amp;&amp;r_tstid+1);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      INSERT INTO target.tst VALUES (</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;          &amp;g_scnid</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;         ,&amp;&amp;&amp;r_casid</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;         ,&amp;&amp;&amp;r_tstid</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;         ,&quot;&amp;i_type&quot;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;         ,%sysfunc(quote(&amp;i_desc%str( )))</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;         ,%sysfunc(quote(&amp;i_expected%str( )))</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;         ,%sysfunc(quote(&amp;i_actual%str( )))</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;         ,&amp;i_result</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;         ,&quot;&amp;l_errMsg&quot;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;      );</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;   QUIT;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;   %PUT ========================== Check &amp;&amp;&amp;r_casid...&amp;&amp;&amp;r_tstid (&amp;i_type) =====================================;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;   %LET &amp;r_casid = %sysfunc(putn(&amp;&amp;&amp;r_casid,z3.));</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;   %LET &amp;r_tstid = %sysfunc(putn(&amp;&amp;&amp;r_tstid,z3.));</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;%MEND _asserts;</div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="__asserts_8sas.html">_asserts.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:07 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
