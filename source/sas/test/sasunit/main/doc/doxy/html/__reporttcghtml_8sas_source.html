<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: _reporttcghtml.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('__reporttcghtml_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">_reporttcghtml.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="__reporttcghtml_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;%macro _reporttcghtml(i_macroName=</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;                     ,i_macroLocation=</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                     ,i_mCoverageName=</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                     ,i_mCoverageLocation=</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                     ,o_outputFile=</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                     ,o_outputPath=</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                     ,o_resVarName=</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                     ,o_html=0</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                     );</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;   %local l_MacroName;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;   %local l_MCoverageName;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;   %local l_linesize;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;   %let l_MacroName=%lowcase(&amp;i_macroName.);</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;   %let l_MCoverageName=%lowcase(&amp;i_mCoverageName.);</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;   <span class="comment">/*** Check existence of input files */</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;   %IF (NOT %SYSFUNC(FILEEXIST(&amp;i_mCoverageLocation./&amp;l_MCoverageName.)) OR &amp;l_MCoverageName=) %THEN %DO;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;     %PUT  ERROR(SASUNIT): Input file with coverage data does not exist.;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;     %GOTO _macExit;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;   %END;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;   %IF (NOT %SYSFUNC(FILEEXIST(&amp;i_macroLocation./&amp;l_MacroName.)) OR &amp;l_MacroName=) %THEN %DO;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;     %PUT  ERROR(SASUNIT): Input file with macro code does not exist.;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;     %GOTO _macExit;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;   %END;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;   <span class="comment">/*** Read records from flat file and keep only those of given macro ***/</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;   data WORK._MCoverage1 (where=(upcase (MacName)=<span class="stringliteral">&quot;%scan(%upcase(&amp;l_MacroName.),1,.)&quot;</span>));</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;      length MacName $40;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      infile <span class="stringliteral">&quot;&amp;i_mCoverageLocation./&amp;l_MCoverageName.&quot;</span>;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      input;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      RecordType = input (scan (_INFILE_, 1, <span class="charliteral">&#39; &#39;</span>), ??8.);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      FirstLine  = input (scan (_INFILE_, 2, <span class="charliteral">&#39; &#39;</span>), ??8.);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;      LastLine   = input (scan (_INFILE_, 3, <span class="charliteral">&#39; &#39;</span>), ??8.);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;      MacName    = scan (_INFILE_, 4, <span class="charliteral">&#39; &#39;</span>);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;   run;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;   <span class="comment">/*** Keep only one record per combination of record type first_line and last_line ***/</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;   proc sort data=WORK._MCoverage1 out=WORK._MCoverage3 nodupkey;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;      by Firstline RecordType LastLine;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;   run;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;   <span class="comment">/*** Get the covered rows of the macro ***/</span>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;   data WORK._MCoverage4;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;      set WORK._MCoverage3;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;      <span class="comment">/*** Keep value of last record the detect changes from one observation to the other ***/</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;      lag_LastLine = lag (LastLine);</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;      lag_FirstLine = lag (FirstLine);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;      <span class="comment">/*** Generate line numbers for covered contributing rows ***/</span>;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;      <span class="comment">/*** 2 3 5 MacName .         will be converted to:                            ***/</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;      <span class="comment">/*** 2 3 5 MacName 2                                                          ***/</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      <span class="comment">/*** 2 3 5 MacName 3                                                          ***/</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;      <span class="comment">/*** 2 3 5 MacName 4                                                          ***/</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;      <span class="comment">/*** 2 3 5 MacName 5                                                          ***/</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;      <span class="keywordflow">if</span> (RecordType in (2)) then <span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;         <span class="keywordflow">do</span> _line_ = FirstLine to LastLine;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;            output;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;         end;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;      end;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;      <span class="comment">/*** Generate line numbers for non-contributing rows ***/</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;      <span class="comment">/*** 3 3 5 MacName .         will be converted to:                            ***/</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;      <span class="comment">/*** 3 3 5 MacName 2                                                          ***/</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;      <span class="comment">/*** 3 3 5 MacName 3                                                          ***/</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;      <span class="comment">/*** 3 3 5 MacName 4                                                          ***/</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;      <span class="comment">/*** 3 3 5 MacName 5                                                          ***/</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;      <span class="keywordflow">if</span> (RecordType in (3)) then <span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;         <span class="keywordflow">do</span> nonEx = FirstLine to LastLine;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;            output;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;         end;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;      end;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;   run;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;   <span class="comment">/*** Due to the order of check in above data step, line numbers are not sorted properly ***/</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;   <span class="comment">/*** Sort lines and generate a second data set with non-contributing rows ***/</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;   proc sort data=WORK._MCoverage4 out=WORK._MCoverage5 NODUPKEY;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;      by _line_;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;   run;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;   proc sort data=WORK._MCoverage4 out=WORK._NonEx NODUPKEY;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;      by nonEx;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;   run;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;   <span class="comment">/*** Enumerate lines in source code file , flagging all lines before %macro statement with -1 ***/</span></div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;   data WORK.rowsOfInputFile;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;      length srcrow $300 nCounter 8;  </div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;      retain srcrow <span class="stringliteral">&quot; &quot;</span> nCounter -1; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;      infile <span class="stringliteral">&quot;&amp;i_macroLocation./&amp;l_MacroName.&quot;</span>;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;      input;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;      srcrow = _INFILE_;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;      <span class="keywordflow">if</span> (index (upcase (srcrow), <span class="stringliteral">&quot;%nrstr(%MACRO )%scan(%upcase(&amp;l_MacroName.),1,.)&quot;</span>)) then <span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;         nCounter=0;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      end;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <span class="keywordflow">if</span> (nCounter &gt;= 0) then <span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;         nCounter=nCounter+1;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;      end;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;   run;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;   </div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;   <span class="comment">/*** Read all lines not explicitly marked as covered          ***/</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;   <span class="comment">/*** This can result in selecting no rows! So we need to      ***/</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;   <span class="comment">/*** preassign a value to missinglines. Does zero sound okay? ***/</span></div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;   %let MissingLines=0;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;   proc sql noprint;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;      select distinct nCounter into :MissingLines separated by <span class="charliteral">&#39; &#39;</span> from WORK.rowsOfInputFile </div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;      where nCounter not in (select distinct _line_ from WORK._MCoverage5  where _line_ not eq .);</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;   quit;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;   <span class="comment">/*** If there is an %if-statement with %do and %end an adjustment is made: ***/</span></div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;   <span class="comment">/*** If the %if-expression is evaluated to false then the corresponding    ***/</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;   <span class="comment">/*** %end is not marked as covered... therefore it is marked manually,     ***/</span></div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;   <span class="comment">/*** same procedure for %mend                                              ***/</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;   </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;   data WORK.MCoverage <span class="comment">/*(keep=srcrow nCounter covered srcRowCopy)*/</span>;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      length srcrow $300 nCounter 8 srcRowCopy $2048 inExecutedBlock 8 inExecutedMBlock 8;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;      retain srcrow <span class="stringliteral">&quot; &quot;</span> nCounter -1 inExecutedBlock 0 inExecutedMBlock 0;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;      label srcrow=<span class="stringliteral">&quot;Macrostatements&quot;</span>; </div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      infile <span class="stringliteral">&quot;&amp;i_macroLocation./&amp;l_MacroName.&quot;</span>;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;      input;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;      <span class="keywordflow">if</span> (index (upcase (_INFILE_), <span class="stringliteral">&quot;%nrstr(%MACRO )%scan(%upcase(&amp;l_MacroName.),1,.)&quot;</span>)) then <span class="keywordflow">do</span>;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        <span class="keywordflow">if</span> not(1 in (&amp;MissingLines.)) then do;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;           inExecutedMBlock = inExecutedMBlock + 1;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;       end;</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;         nCounter=0;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;      end;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      if (nCounter &gt;= 0) then do;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;         nCounter=nCounter+1;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;      end;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;      srcrow = cats (&quot;&quot;, _INFILE_, &quot;&quot;);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      srcRowCopy = _INFILE_;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;      covered = 1;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;     </div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;      if (nCounter in (&amp;MissingLines.)) then do;</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;         srcrow = cats (&quot;&quot;, _INFILE_, &quot;&quot;);</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;         covered = 0;</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;         _temp_row = compress (upcase (_INFILE_));</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;         if (length (_temp_row) &gt; 4) then do;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;            if ( (substr (_temp_row,1,5) = &#39;%END;&#39;) or (substr (_temp_row,1,5) = &#39;%END &#39;) ) then do;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;               srcrow = cats (&quot;&quot;, _INFILE_, &quot;&quot;);</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;            if inExecutedBlock gt 0 then do;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                  covered = 1;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;            end;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;            inExecutedBlock = inExecutedBlock - 1;</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;            end;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;         end;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;         if (length (_temp_row) &gt; 4) then do;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;            if ( (substr (_temp_row,1,6) = &#39;%MEND;&#39;) or (substr (_temp_row,1,5) = &#39;%MEND &#39;) ) then do;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;               srcrow = cats (&quot;&quot;, _INFILE_, &quot;&quot;);</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;               if inExecutedMBlock gt 0 then do;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;                  covered = 1;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;            end;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;            inExecutedMBlock = inExecutedMBlock - 1;</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;            end;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;         end;</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      end;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;     else do;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        _temp_row = compress (upcase (_INFILE_));</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;         if ( (count (_temp_row,&#39;%DO&#39;) gt 0) ) then do;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;            inExecutedBlock = inExecutedBlock + count (_temp_row,&#39;%DO&#39;);</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;         end;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;       if (length (_temp_row) &gt; 4) then do;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;            if ( (substr (_temp_row,1,5) = &#39;%END;&#39;) or (substr (_temp_row,1,5) = &#39;%END &#39;) ) then do;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;               inExecutedBlock = inExecutedBlock - 1;</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;            end;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;         end;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;         if (length (_temp_row) &gt; 4) then do;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;            if ( (substr (_temp_row,1,6) = &#39;%MEND;&#39;) or (substr (_temp_row,1,5) = &#39;%MEND &#39;) ) then do;</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;               inExecutedMBlock = inExecutedMBlock - 1;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;            end;</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;         end;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;     end;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;   run;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;   </div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;   <span class="comment">/*** Scan rows for comment lines ***/</span></div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;   DATA _commentLines;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;     SET Rowsofinputfile(rename=(srcrow=srcline));</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;     RETAIN inComment oneLineComment endCommentNextLine commentStartsNextLine</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;            ;</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;     IF _N_=1 THEN DO;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;        inComment  = 0;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        oneLineComment = 0;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        endCommentNextLine = 0;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;        commentStartsNextLine = 0;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;     END;</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;     IF oneLineComment = 1 THEN DO;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        inComment  = 0;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;        oneLineComment = 0;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;     END;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;     IF endCommentNextLine = 1 THEN DO;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        inComment  = 0;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        endCommentNextLine =0;</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;     END;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;     IF commentStartsNextLine = 1 THEN DO;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;        inComment  = 1;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        commentStartsNextLine =0;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;     END;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;     </div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;     IF NOT ((index(srcline, &#39;<span class="comment">/*&#39;) &gt; 0) AND (index(srcline, &#39;*/</span>&#39;) &gt; 0))THEN DO;</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;        IF index(srcline, &#39;*/&#39;) &gt; 0 THEN DO;</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;           endCommentNextLine = 1;</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;        END;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        ELSE DO;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;           IF (index(srcline, &#39;<span class="comment">/*&#39;) GT 0)  THEN DO;</span></div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;<span class="comment">              IF index(compress(srcline,, &#39;s&#39;),&#39;/*&#39;) EQ 1 THEN DO;</span></div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;<span class="comment">                 inComment=1;  </span></div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;<span class="comment">              END;</span></div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;<span class="comment">              commentStartsNextLine=1;</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;<span class="comment">           END;</span></div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="comment">        END;</span></div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="comment">     END;</span></div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;<span class="comment">     ELSE DO;</span></div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;<span class="comment">       IF index(compress(srcline,, &#39;s&#39;),&#39;/*&#39;) EQ 1 AND index(compress(srcline,, &#39;s&#39;),&#39;*/</span>&#39;) EQ length(compress(srcline,, &#39;s&#39;))-1 THEN DO;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;         inComment=1;</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;         oneLineComment=1;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;       END;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;       ELSE IF count(srcline,&#39;*/&#39;) gt count(srcline,&#39;<span class="comment">/*&#39;) THEN DO;</span></div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="comment">         endCommentNextLine = 1;</span></div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;<span class="comment">       END;</span></div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;<span class="comment">     END; </span></div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;<span class="comment">   RUN;</span></div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="comment"></span></div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="comment">   /*** Update WORK.MCoverage to flag the non contributing rows identified by MCOVERAGE OPTION ***/</span>  </div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;   proc sql noprint;</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;      update WORK.MCoverage </div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;         set covered = -2</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;                where nCounter in (select distinct nonEx from WORK._nonex where nonEx not eq .);</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;      update WORK.MCoverage </div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;         set covered = -1</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;                where nCounter in ((select distinct nCounter from _commentLines where inComment eq 1 or compress(compress(srcline),&quot;0D&quot;x) eq &#39;&#39;));</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;   quit;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;   <span class="comment">/*** Get sets of rows of different different types: covered contributing, non-covered contributing and non contributing ***/</span></div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;   proc sql noprint;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;      </div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;      create table rowNumbersCovered as</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;         select distinct nCounter as row from WORK.MCoverage where covered EQ  1;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;      create table rowNumbersNonCovered  as</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;         select distinct nCounter as row from WORK.MCoverage where covered EQ  0;</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;      create table rowNumbersNonCbuting as</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;         select distinct nCounter as row from WORK.MCoverage where covered LE -1;</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;      select count(*) into:ContributingLocCovered from rowNumbersCovered;</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;      select count(*) into:ContributingLocNonCovered from rowNumbersNonCovered;</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;   quit;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;   <span class="comment">/*** Calculate the percentage of covered contributing rows ***/</span></div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;   %let Coverage = %sysevalf (&amp;ContributingLocCovered. / (&amp;ContributingLocCovered. + &amp;ContributingLocNonCovered.));</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;   %let CoveragePCT = %sysfunc (putn (&amp;Coverage., nlpct));</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;   %if &quot;&amp;o_resVarName.&quot; NE &quot;&quot; %then %do;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;      %let &amp;o_resVarName. = %sysevalf(%sysfunc (round(&amp;Coverage.,0.01))*100);</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;   %end;</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;   data work._tcg_legend;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;      length dummy $3 Text $140;</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;      dummy=&quot;   &quot;;Text=&quot;^{unicode 25CF} ^{style tcgCoveredData &amp;g_nls_reportAuton_018.}<span class="stringliteral">&quot;;output;</span></div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;<span class="stringliteral">      dummy=&quot;</span>   <span class="stringliteral">&quot;;Text=&quot;</span>^{unicode 25CF} ^{style tcgNonCoveredData &amp;g_nls_reportAuton_019.}<span class="stringliteral">&quot;;output;</span></div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;<span class="stringliteral">      dummy=&quot;</span>   <span class="stringliteral">&quot;;Text=&quot;</span>^{unicode 25CF} ^{style tcgCommentData &amp;g_nls_reportAuton_020.}<span class="stringliteral">&quot;;output;</span></div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;<span class="stringliteral">      dummy=&quot;</span>   <span class="stringliteral">&quot;;Text=&quot;</span>^{unicode 25CF} ^{style tcgNonContribData &amp;g_nls_reportAuton_021.}<span class="stringliteral">&quot;;output;</span></div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;<span class="stringliteral">   run;</span></div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;<span class="stringliteral">   data work._tcg_report;</span></div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;<span class="stringliteral">      LENGTH outputRow pgmSourceColumn $2048 RowNumberOut $200;</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;<span class="stringliteral">      SET WORK.MCoverage;</span></div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;<span class="stringliteral">      RowNumber = _N_;</span></div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;<span class="stringliteral">      outputRow = trim(srcRowCopy);</span></div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;<span class="stringliteral">      outputRow = tranwrd (outputRow,&#39;^{&#39;,&#39;^[&#39;);</span></div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;<span class="stringliteral">      outputRow = tranwrd (outputRow,&#39;}&#39;,&#39;]&#39;);</span></div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;<span class="stringliteral">      %_render_dataColumn (i_sourceColumn=RowNumber</span></div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;<span class="stringliteral">                                  ,i_format=Z5.</span></div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;<span class="stringliteral">                                  ,i_columnType=tcgCommentData </span></div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;<span class="stringliteral">                                  ,o_targetColumn=RowNumberOut</span></div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;<span class="stringliteral">                                  );</span></div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;<span class="stringliteral">      IF covered   = -1 THEN DO;</span></div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;<span class="stringliteral">         %_render_dataColumn (i_sourceColumn=outputRow</span></div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;<span class="stringliteral">                                     ,i_columnType=tcgCommentData </span></div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;<span class="stringliteral">                                     ,o_targetColumn=pgmSourceColumn</span></div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;<span class="stringliteral">                                     );</span></div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;<span class="stringliteral">      END;</span></div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;<span class="stringliteral">      ELSE IF covered   = 1 THEN DO;</span></div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;<span class="stringliteral">         %_render_dataColumn (i_sourceColumn=outputRow</span></div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;<span class="stringliteral">                                     ,i_columnType=tcgCoveredData </span></div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;<span class="stringliteral">                                     ,o_targetColumn=pgmSourceColumn</span></div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;<span class="stringliteral">                                     );</span></div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="stringliteral">      END;</span></div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;<span class="stringliteral">      ELSE IF covered   = 0 THEN DO;</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;<span class="stringliteral">         %_render_dataColumn (i_sourceColumn=outputRow</span></div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;<span class="stringliteral">                                     ,i_columnType=tcgNonCoveredData </span></div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;<span class="stringliteral">                                     ,o_targetColumn=pgmSourceColumn</span></div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;<span class="stringliteral">                                     );</span></div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;<span class="stringliteral">      END;</span></div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;<span class="stringliteral">      ELSE DO; </span></div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;<span class="stringliteral">         %_render_dataColumn (i_sourceColumn=outputRow</span></div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;<span class="stringliteral">                                     ,i_columnType=tcgNonContribData </span></div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;<span class="stringliteral">                                     ,o_targetColumn=pgmSourceColumn</span></div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;<span class="stringliteral">                                     );</span></div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;<span class="stringliteral">      END;</span></div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="stringliteral">   RUN;</span></div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;<span class="stringliteral">   options nocenter;</span></div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;<span class="stringliteral">   title;footnote;</span></div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;<span class="stringliteral">   title  j=c &quot;</span>&amp;g_nls_reportAuton_005.: &amp;i_macroName<span class="stringliteral">&quot;;</span></div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;<span class="stringliteral">   title2 &quot;</span>&amp;g_nls_reportAuton_016.: &amp;CoveragePCT.<span class="stringliteral">&quot;;</span></div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;<span class="stringliteral">   %if (&amp;o_html.) %then %do;</span></div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;<span class="stringliteral">      ods html4 file=&quot;</span>&amp;o_outputPath./&amp;o_outputFile..html<span class="stringliteral">&quot; </span></div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;<span class="stringliteral">                    (TITLE=&quot;</span>&amp;l_title.<span class="stringliteral">&quot;) </span></div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;<span class="stringliteral">                    headtext=&#39;&lt;link href=&quot;</span>tabs.css<span class="stringliteral">&quot; rel=&quot;</span>stylesheet<span class="stringliteral">&quot; type=&quot;</span>text/css<span class="stringliteral">&quot;/&gt;&lt;link rel=&quot;</span>shortcut icon<span class="stringliteral">&quot; href=&quot;</span>./favicon.ico<span class="stringliteral">&quot; type=&quot;</span>image/x-icon<span class="stringliteral">&quot; /&gt;&#39;</span></div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;<span class="stringliteral">                    metatext=&quot;</span>http-equiv=<span class="stringliteral">&quot;&quot;</span>Content-Style-Type<span class="stringliteral">&quot;&quot;</span> content=<span class="stringliteral">&quot;&quot;</span>text/css<span class="stringliteral">&quot;&quot;</span> /&gt;&lt;meta http-equiv=<span class="stringliteral">&quot;&quot;</span>Content-Language<span class="stringliteral">&quot;&quot;</span> content=<span class="stringliteral">&quot;&quot;</span>&amp;i_language.<span class="stringliteral">&quot;&quot;</span> /<span class="stringliteral">&quot;</span></div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;<span class="stringliteral">                    style=styles.SASUnit stylesheet=(URL=&quot;</span>SAS_SASUnit.css<span class="stringliteral">&quot;);</span></div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;<span class="stringliteral">   %end;</span></div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;<span class="stringliteral">   proc report data=work._tcg_legend nowd</span></div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;<span class="stringliteral">            style(report)=blindTable [borderwidth=0]</span></div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;<span class="stringliteral">            style(column)=blindData</span></div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;<span class="stringliteral">            style(lines) =blindData</span></div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;<span class="stringliteral">            style(header)=blindHeader;</span></div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;<span class="stringliteral">      columns dummy Text;</span></div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;<span class="stringliteral">      compute before _page_;</span></div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;<span class="stringliteral">         line @1 &quot;</span>Color Legend:<span class="stringliteral">&quot;;</span></div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;<span class="stringliteral">      endcomp;</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;<span class="stringliteral">   run;</span></div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;<span class="stringliteral">   title;</span></div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;<span class="stringliteral">   %_reportFooter(o_html=&amp;o_html.);</span></div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;<span class="stringliteral">   *** Render separation line between legend and source code ***;</span></div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;<span class="stringliteral">   %if (&amp;o_html.) %then %do;</span></div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;<span class="stringliteral">      ods html4 text=&quot;</span>^{RAW &lt;hr size=<span class="stringliteral">&quot;&quot;</span>1<span class="stringliteral">&quot;&quot;</span>&gt;}<span class="stringliteral">&quot;;</span></div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;<span class="stringliteral">   %end;</span></div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;<span class="stringliteral">   proc print data=work._tcg_report noobs</span></div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;<span class="stringliteral">      style(report)=blindTable [borderwidth=0]</span></div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;<span class="stringliteral">      style(column)=blindFixedFontData</span></div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;<span class="stringliteral">      style(header)=blindHeader;</span></div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="stringliteral">      var RowNumberOut pgmSourceColumn;</span></div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;<span class="stringliteral">   run;</span></div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;<span class="stringliteral">   %if (&amp;o_html.) %then %do;</span></div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;<span class="stringliteral">      %_closeHtmlPage;</span></div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;<span class="stringliteral">   %end;</span></div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;<span class="stringliteral"></span></div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;<span class="stringliteral">   options center;</span></div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;<span class="stringliteral">   title;footnote;</span></div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="stringliteral">   %_macExit:</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;<span class="stringliteral">%mend _reporttcghtml;</span></div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="__reporttcghtml_8sas.html">_reporttcghtml.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:08 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
