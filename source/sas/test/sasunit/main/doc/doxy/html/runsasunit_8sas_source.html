<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.4"/>
<title>SASUnit Examples: runsasunit.sas Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<script type="text/javascript">
  $(document).ready(initResizable);
  $(window).load(resizeHeight);
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td style="padding-left: 0.5em;">
   <div id="projectname">SASUnit Examples
   &#160;<span id="projectnumber">Version 1.3.0</span>
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.4 -->
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
$(document).ready(function(){initNavTree('runsasunit_8sas_source.html','');});
</script>
<div id="doc-content">
<div class="header">
  <div class="headertitle">
<div class="title">runsasunit.sas</div>  </div>
</div><!--header-->
<div class="contents">
<a href="runsasunit_8sas.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;%MACRO runSASUnit(i_source     =</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                 ,i_recursive  = 0</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                 );</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;   %LOCAL </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;      l_macname</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;      d_dir  </div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;      d_examinee </div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;      l_source</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;      l_nscn </div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;      i</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;      l_auto </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;      l_autonr</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;      l_scn </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;      l_scnid </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;      l_dorun </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;      l_scndesc </div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;      l_sysrc</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;      l_rc</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;      l_scnlogfullpath</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;      l_filled</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;      l_lstfile</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;      l_error_count </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;      l_warning_count</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;      l_result0 </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;      l_result1 </div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;      l_result2</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;      l_result</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;      l_nscncount</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;      l_c_scnid</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;      l_sysrc</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;   ;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;   %LET l_macname=&amp;sysmacroname;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;   %_tempFileName(d_dir);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;   %_tempFileName(d_examinee);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;   <span class="comment">/*-- check if testdatabase can be accessed -----------------------------------*/</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;   %IF %_handleError(&amp;l_macname.</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;                    ,NoTestDB</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                    ,NOT %sysfunc(exist(target.tsu)) OR NOT %symexist(g_project)</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;                    ,%nrstr(test database cannot be accessed, call %initSASUnit before %runSASUnit)</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                    ,i_verbose=&amp;g_verbose.</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                    )</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;      %THEN %GOTO errexit;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;   <span class="comment">/*-- parameter i_recursive ---------------------------------------------------*/</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;   %IF <span class="stringliteral">&quot;&amp;i_recursive&quot;</span> NE <span class="stringliteral">&quot;1&quot;</span> %THEN %LET i_recursive=0;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;   <span class="comment">/*-- find out all test scenarios ---------------------------------------------*/</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;   %LET l_source = %_abspath(&amp;g_root, &amp;i_source);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;   %_dir(i_path=&amp;l_source, i_recursive=&amp;i_recursive, o_out=&amp;d_dir)</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;   %IF %_handleError(&amp;l_macname.</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                    ,NoSourceFiles</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                    ,%_nobs(&amp;d_dir) EQ 0</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                    ,Error in parameter i_source: no test scenarios found</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                    ,i_verbose=&amp;g_verbose.</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                    ) </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;      %THEN %GOTO errexit;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;   %DO i=1 %TO %_nobs(&amp;d_dir); </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;      %LOCAL </div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;         l_scnfile&amp;i </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;         l_scnchanged&amp;i</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;      ;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;   %END;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;   DATA _null_;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;      SET &amp;d_dir;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;      CALL symput (<span class="stringliteral">&#39;l_scnfile&#39;</span> !! left(put(_n_,8.)), trim(filename));</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;      CALL symput (<span class="stringliteral">&#39;l_scnchanged&#39;</span> !! left(put(_n_,8.)), compress(put(changed,12.)));</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;      CALL symput (<span class="stringliteral">&#39;l_nscn&#39;</span>, compress(put(_n_,8.)));</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;   RUN;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;   <span class="comment">/*-- find out all possible units under test ----------------------------------*/</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;   %LET l_auto=&amp;g_sasautos;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;   %LET l_autonr=0;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;   %DO %WHILE(<span class="stringliteral">&quot;&amp;l_auto&quot;</span> ne <span class="stringliteral">&quot;&quot;</span>);  </div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;      %LET l_auto=%quote(&amp;l_auto/);</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;      %_dir(i_path=&amp;l_auto.*.sas, o_out=&amp;d_dir)</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;      data &amp;d_examinee;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;         set %IF &amp;l_autonr&gt;0 %THEN &amp;d_examinee; &amp;d_dir(in=indir);</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;         if indir then auton=&amp;l_autonr.+2;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;      run; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;      %LET l_autonr = %eval(&amp;l_autonr+1);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      %LET l_auto=;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      %IF %symexist(g_sasautos&amp;l_autonr) %THEN %LET l_auto=&amp;&amp;g_sasautos&amp;l_autonr;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;   %END;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;   %LET l_auto=&amp;g_sasunit;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;   %LET l_auto=%quote(&amp;l_auto/);</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;   %_dir(i_path=&amp;l_auto.*.sas, o_out=&amp;d_dir)</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;   data &amp;d_examinee;</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;      set &amp;d_examinee &amp;d_dir(in=indir);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;      if indir then auton=0;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;   run; </div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;   %LET l_auto=&amp;g_sasunit_os;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;   %LET l_auto=%quote(&amp;l_auto/);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;   %_dir(i_path=&amp;l_auto.*.sas, o_out=&amp;d_dir)</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;   data &amp;d_examinee;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;      set &amp;d_examinee &amp;d_dir(in=indir);</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;      if indir then auton=1;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;   run; </div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;   <span class="comment">/*-- loop over all test scenarios --------------------------------------------*/</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;   %DO i=1 %TO &amp;l_nscn;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;      %LET l_scn = %_stdPath(&amp;g_root, &amp;&amp;l_scnfile&amp;i);</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;      <span class="comment">/* check if test scenario must be run */</span></div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;      %_checkScenario(</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;         i_scnfile = &amp;&amp;l_scnfile&amp;i</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        ,i_changed = &amp;&amp;l_scnchanged&amp;i</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        ,i_dir     = &amp;d_examinee</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        ,r_scnid   = l_scnid</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        ,r_run     = l_dorun</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;      )</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;      <span class="comment">/*-- if scenario not present in test database: create new scenario --------*/</span></div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;      %IF &amp;l_scnid = 0 %THEN %DO;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;         PROC SQL NOPRINT;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;            SELECT max(scn_id) INTO :l_scnid FROM target.scn;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;            %IF &amp;l_scnid=. %THEN %LET l_scnid=0;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;            %LET l_scnid = %eval(&amp;l_scnid+1);</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;            INSERT INTO target.scn VALUES (</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;                &amp;l_scnid</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;               ,&quot;&amp;l_scn&quot;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;               ,&quot;&quot;,.,.,.,.,.,.</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;            );</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;         QUIT;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;      %END;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;      <span class="comment">/*-- if scenario already exists and has been changed: delete scenario -----*/</span></div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;      %ELSE %IF &amp;l_dorun %THEN %DO;</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;         <span class="comment">/*-- delete corresponding files -----*/</span></div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;         %_deletescenariofiles(i_scnid=&amp;l_scnid.);  </div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;      </div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;         PROC SQL NOPRINT;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;            DELETE FROM target.cas WHERE cas_scnid = &amp;l_scnid;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;            DELETE FROM target.tst WHERE tst_scnid = &amp;l_scnid;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;         QUIT;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;       </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      %END;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;      </div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      %IF &amp;l_dorun %THEN %DO;</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;         %PUT ======== test scenario &amp;l_scnid (&amp;l_scn) will be run ========;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;         %PUT;</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;         %PUT;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      %END;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;      %ELSE %DO;</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;         %PUT ======== test scenario &amp;l_scnid (&amp;l_scn) will not be run ==;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;         %PUT;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;         %PUT;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;      %END;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      <span class="comment">/*-- start test scenario if necessary -------------------------------------*/</span></div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;      %IF &amp;l_dorun %THEN %DO;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;         <span class="comment">/*-- save description and start date and time of scenario --------------*/</span></div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;         %_getPgmDesc (i_pgmfile=&amp;&amp;l_scnfile&amp;i, r_desc=l_scndesc)</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;         PROC SQL NOPRINT;</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;            UPDATE target.scn SET</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;               scn_desc  = &quot;&amp;l_scndesc&quot;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;              ,scn_start = %sysfunc(datetime())</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;            WHERE scn_id = &amp;l_scnid</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;            ;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;         QUIT;</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;       </div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;         %LET l_c_scnid        = %substr(00&amp;l_scnid.,%length(&amp;l_scnid));</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;         %LET l_scnlogfullpath = &amp;g_log/&amp;l_c_scnid..log;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;         %_runProgramSpawned(i_program          =&amp;&amp;l_scnfile&amp;i</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;                            ,i_scnid            =&amp;l_c_scnid.</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;                            ,i_generateMcoverage=&amp;g_testcoverage.</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;                            ,r_sysrc            =l_sysrc</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;                            );    </div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;                     </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;         <span class="comment">/*-- delete listing if empty -------------------------------------------*/</span></div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;         %LET l_filled=0;</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;         %LET l_lstfile=&amp;g_testout/%substr(00&amp;l_scnid,%length(&amp;l_scnid)).lst;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;         %IF %SYSFUNC(FILEEXIST(&quot;&amp;l_lstfile&quot;)) %THEN %DO;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;           DATA _null_;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;              INFILE &quot;&amp;l_lstfile&quot;;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;              INPUT;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;              CALL symput (&#39;l_filled&#39;,&#39;1&#39;);</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;              STOP;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;           RUN;</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;         %END;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;         %IF NOT &amp;l_filled %THEN %DO;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;            %LET l_filled=%_delfile(&amp;l_lstfile);</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;         %END;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;         <span class="comment">/*-- save metadata of test scenario ------------------------------------*/</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;         <span class="comment">/* scan log for errors outside test cases */</span></div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;         %_checklog (</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;             i_logfile = &amp;l_scnlogfullpath.</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;            ,i_error   = &amp;g_error.</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;            ,i_warning = &amp;g_warning.</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;            ,r_errors  = l_error_count</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;            ,r_warnings= l_warning_count</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;         )</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;         PROC SQL NOPRINT;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;            <span class="comment">/* determine results of the test cases */</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;            %*** Treat missing scenario as error ***;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;            %let l_result=2;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;            SELECT max (cas_res) INTO :l_result FROM target.cas WHERE cas_scnid=&amp;l_scnid;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;            %*** Treat missing scenario as failed and treat scenario wit errors in scenario log as failed ***;</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;            %if (&amp;l_result. = . or &amp;l_error_count. &gt; 0) %then %let l_result=2;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;            </div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;            UPDATE target.scn</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;               SET </div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;                   scn_end          = %sysfunc(datetime())</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;                  ,scn_rc           = &amp;l_sysrc.</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;                  ,scn_errorcount   = &amp;l_error_count.</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;                  ,scn_warningcount = &amp;l_warning_count.</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;                  ,scn_res          = &amp;l_result.</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;               WHERE </div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;                  scn_id = &amp;l_scnid.</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;               ;</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;         QUIT;</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;      %END; <span class="comment">/* run scenario */</span></div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;   %END; <span class="comment">/* loop for all scenarios */</span></div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;   %GOTO exit;</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;%errexit:</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;      %PUT;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;      %PUT =========================== Error! runSASUnit aborted! ==========================================;</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;      %PUT;</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;      %PUT;</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;      %IF %EVAL(&quot;%UPCASE(&amp;g_error_code.)&quot; EQ &quot;%UPCASE(NoSourceFiles)&quot;) %THEN %DO;</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;         <span class="comment">/* ensure that dummy entry for inexisting scenario is present in test database, to be able to report it later</span></div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;<span class="comment">         */</span></div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;         %LET l_scn = %_stdPath(&amp;g_root., &amp;l_source.);</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;         %LET l_nscncount = 0;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;         PROC SQL NOPRINT;</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;            SELECT Count(scn_id)</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;               INTO :l_nscncount SEPARATED BY &#39;&#39;</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;            FROM target.scn</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;            WHERE Upcase(scn_path) = &quot;%UPCASE(&amp;l_scn.)&quot;;</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;         QUIT;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;         %IF %EVAL(&amp;l_nscncount. EQ 0) %THEN %DO;</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;            %LET l_scndesc = %STR(Scenario not found - has to fail!);</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;           </div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;            PROC SQL NOPRINT;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;               SELECT max(scn_id) INTO :l_scnid FROM target.scn;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;               %IF &amp;l_scnid=. %THEN %LET l_scnid=0;</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;               %LET l_scnid = %eval(&amp;l_scnid+1);</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;               INSERT INTO target.scn </div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;                  ( </div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;                    scn_id</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;                   ,scn_path</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;                   ,scn_desc</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;                   ,scn_start</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;                   ,scn_end</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;                   ,scn_rc</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;                   ,scn_errorcount</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;                   ,scn_warningcount</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;                   ,scn_res</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;                  )</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;                  VALUES </div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;                  (</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;                      &amp;l_scnid</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;                     ,&quot;&amp;l_scn.&quot;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;                     ,&quot;&amp;l_scndesc.&quot;</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;                     ,.</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;                     ,.</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;                     ,.</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;                     ,.</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;                     ,.</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;                     ,2</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;                  )</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;               ;</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;            QUIT;</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;         %END; <span class="comment">/* if scenario is not present in database */</span></div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;      %END;</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;%exit:</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;   PROC DATASETS NOLIST NOWARN LIB=%scan(&amp;d_dir,1,.);</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;      DELETE %scan(&amp;d_dir,2,.);</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;      DELETE %scan(&amp;d_examinee,2,.);</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;   QUIT;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;%MEND runSASUnit;</div>
</div><!-- fragment --></div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="navelem"><a class="el" href="dir_0e566e4a969139e374804a1266c2d968.html">saspgm</a></li><li class="navelem"><a class="el" href="dir_62453a0376220a2cdb2f3d2e7b1f0e52.html">sasunit</a></li><li class="navelem"><a class="el" href="runsasunit_8sas.html">runsasunit.sas</a></li>
    <li class="footer">Generated on Fri Mar 21 2014 11:40:08 for SASUnit Examples by
    <a href="http://www.doxygen.org/index.html">
    <img class="footer" src="doxygen.png" alt="doxygen"/></a> 1.8.4 </li>
  </ul>
</div>
</body>
</html>
