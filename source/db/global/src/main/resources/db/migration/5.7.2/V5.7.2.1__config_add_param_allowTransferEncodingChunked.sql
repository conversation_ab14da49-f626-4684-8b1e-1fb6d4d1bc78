-- anyhtng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.anyhtng.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'AnyHtng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- curtisc
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.curtisc.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'curtisc' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- hbsi
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hbsi.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hbsi' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- infor
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.infor.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'infor' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- protel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protel.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- rvng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rvng.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rvng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- siteminder
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.siteminder.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'siteminder' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- suite8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.suite8.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'suite8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis1
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis1.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis1' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis2
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis2.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis2' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis3
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis3.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis3' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis4
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis4.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis4' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis5
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis5.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis5' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis6
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis6.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis6' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis7
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis7.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis7' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis8.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis9
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis9.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis9' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis10
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis10.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis10' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- synxis11
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis11.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis11' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- traveltripper
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.traveltripper.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'traveltripper' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- iHotelier
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.iHotelier.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'iHotelier' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- yourvoyager
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.yourvoyager.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'yourvoyager' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- winnerpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.winnerpms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'winnerpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- windsurfercrs
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.windsurfercrs.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'windsurfercrs' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- webrezpropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.webrezpropms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'webrezpropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- vaillms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.vaillms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'vaillms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- staah
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.staah.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'staah' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- smarthotel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.smarthotel.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'smarthotel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- rmspms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rmspms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rmspms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- rezlynx
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rezlynx.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rezlynx' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- ratetiger
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.ratetiger.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'ratetiger' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- rategain
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rategain.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rategain' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- protelioair
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protelioair.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protelioair' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- newbookpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.newbookpms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'newbookpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- leanpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.leanpms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'leanpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- hotelspider
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelspider.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelspider' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- hotelnetsolution
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelnetsolution' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- hermeshotels
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hermeshotels.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hermeshotels' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- groupmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.groupmax.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'groupmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- cubilis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.cubilis.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'cubilis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- connecterevmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.connecterevmax.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'connecterevmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- bookingexpert
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookingexpert.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookingexpert' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- bookassist
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookassist.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookassist' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- blastness
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.blastness.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'blastness' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- avvio
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.avvio.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'avvio' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- availpro
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.availpro.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'availpro' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- advantagereserve
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.advantagereserve.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'advantagereserve' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;

-- maestropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.allowTransferEncodingChunked')
  BEGIN 
  INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.maestropms.allowTransferEncodingChunked',
  'Send Transfer Encoding chunked in the SOAP message',
  (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean') ,
  getdate(),
  getdate(),
  (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
  (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'maestropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
  'null',
  'true')
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'false' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Do not send Transfer Encoding chunked in the SOAP message',
   getdate())
 
  INSERT INTO [dbo].[Config_Parameter_Value_Meaning] ([Config_Param_ID] ,[Config_Parameter_Predefined_Value_ID], [Meaning] ,[CreateDate])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.allowTransferEncodingChunked'),
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Value = 'true' and Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  ) ,
  'Send Transfer Encoding chunked in the SOAP message',
  getdate())
 
  if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.allowTransferEncodingChunked') and context = 'pacman')
  INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.allowTransferEncodingChunked'),
  'pacman',
  null,
  (select top 1 [Config_Parameter_Predefined_Value_ID] from [dbo].[Config_Parameter_Predefined_Value] where Config_Parameter_Predefined_Value_Type_ID = (select top 1 [Config_Parameter_Predefined_Value_Type_ID] from [dbo].[Config_Parameter_Predefined_Value_Type] where code = 'boolean')  and Value = 'true'), getdate(),
  getdate())
 
END;