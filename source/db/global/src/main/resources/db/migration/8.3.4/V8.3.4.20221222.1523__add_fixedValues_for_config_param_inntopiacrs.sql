

--1. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.AllMessagesSentAsDelta') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.AllMessagesSentAsDelta'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--2. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name =
    'pacman.integration.inntopiacrs.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'DailyBarSelectiveUpload')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--3. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.UploadAdultsBeyond2') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.UploadAdultsBeyond2'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--4. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.UploadChildAgeBuckets') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.UploadChildAgeBuckets'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--5. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.UploadChildrenBeyondExtra') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.UploadChildrenBeyondExtra'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--6. DailyBAR.dailybarRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.dailybarRateCode') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.dailybarRateCode'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--7. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.DailyBAR.dailybarRoundingPrecision') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.dailybarRoundingPrecision'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--8. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.miscAdjustment') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.miscAdjustment'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--9. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.newDailyBARFormat') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.newDailyBARFormat'),
    'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
    from [dbo].[Config_Parameter_Predefined_Value]
    where value = 'false'),
    GETDATE(),  GETDATE())
END

--10. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.optimizedDailyBarAgileRateUpload') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.optimizedDailyBarAgileRateUpload'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--11. DailyBAR.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.taxAdjustmentValue') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.taxAdjustmentValue'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--12. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--13. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.AgileRates.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.AgileRates.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--14. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.useDeltaForDifferential') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.useDeltaForDifferential'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--15. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.useYieldCurrencyForDailyBar') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.useYieldCurrencyForDailyBar'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--16. UploadCurrencyForDailyBAR
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.UploadCurrencyForDailyBAR') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.UploadCurrencyForDailyBAR'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--17. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.BarByLOS.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.BarByLOS.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--18. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.BARByLOSByRoomType.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.BARByLOSByRoomType.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--19. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.Fplos.fplosAtRoomCategory') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.Fplos.fplosAtRoomCategory'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--20. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.BarFplosByRoomType.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.BarFplosByRoomType.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--21. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.Fplos.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.Fplos.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--22. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRAControlFPLOS.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRAControlFPLOS.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--23. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRAControlMinLOS.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRAControlMinLOS.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--24. UploadCurrencyForLRV
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.UploadCurrencyForLRV') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.UploadCurrencyForLRV'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--25. LRVatRoomClass.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--26. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.includeCeilingDeltaMaxSold'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--27. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.consolidateCeilingDelta'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'lrvConsolidationType')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--28. LRVatRoomClass.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.roundingPrecision') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.roundingPrecision'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--29. LRVatRoomClass.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomClass.taxAdjustmentValue') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.taxAdjustmentValue'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END


--31. LRVatRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomType.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--32. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomType.consolidateCeilingDelta') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.consolidateCeilingDelta'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'lrvConsolidationType')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--33. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.includeCeilingDeltaMaxSold '),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--34. LRVatRoomType.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRVatRoomType.roundingPrecision') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.roundingPrecision'),
    'pacman', '2', NULL,
    GETDATE(),  GETDATE())
END

--35. LRVatRoomType.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRVatRoomType.taxAdjustmentValue') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.taxAdjustmentValue'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--36. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--37. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.inntopiacrs.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.MinLOSChunkedOnArrivalDateRateCode'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--38. propertycode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.propertycode') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.propertycode'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--39. clientcode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.clientcode') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.clientcode'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--40. YieldCurrencyCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.YieldCurrencyCode') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.YieldCurrencyCode'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--41. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.HotelOverbooking.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.HotelOverbooking.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--42. RoomTypeOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--43. HotelOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.HotelOverbooking.valueType') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.HotelOverbooking.valueType'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--44. RoomTypeOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.valueType') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.valueType'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--45. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DeferredDecisionDeliverySeconds') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DeferredDecisionDeliverySeconds'),
    'pacman', '0', NULL,
    GETDATE(),  GETDATE())
END

--46. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.allowTransferEncodingChunked') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.allowTransferEncodingChunked'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--47. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.async') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.async'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--48. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.ignoreHttpResponse') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.ignoreHttpResponse'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--49. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.includeHTNGAsyncHeaders') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.includeHTNGAsyncHeaders'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--50. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.ack.includeSoapBody') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.ack.includeSoapBody'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--51. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.reverseFplosTranslation') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.reverseFplosTranslation'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--52. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.soapChunkSize') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.soapChunkSize'),
    'pacman', '1000', NULL,
    GETDATE(),  GETDATE())
END

--53. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.async.timeout') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.async.timeout'),
    'pacman', '-1', NULL,
    GETDATE(),  GETDATE())
END

--54. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.ManualRestrictions.uploadtype') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.ManualRestrictions.uploadtype'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--55. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.useCustomMessage') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.useCustomMessage'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'NULL'),
    GETDATE(),  GETDATE())
END

--56. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.useHttpBasicAuth') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.useHttpBasicAuth'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--57. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.useSoapChunks') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.useSoapChunks'),
    'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--58. BarByLOSatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.BarByLOSatRoomClass.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.BarByLOSatRoomClass.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--59. BarByLOSatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.BarByLOSatRoomType.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.BarByLOSatRoomType.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--60. customAvailAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.customAvailAction') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.customAvailAction'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--61. customRateAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.customRateAction') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.customRateAction'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--62. customReplyToAddress
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.customReplyToAddress') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.customReplyToAddress'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--63. DailyBAR.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.DailyBAR.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.DailyBAR.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--64. Fplos.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.Fplos.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.Fplos.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--65. HotelOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.HotelOverbooking.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.HotelOverbooking.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--66. LRVatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where Config_Parameter_ID in (select Config_Parameter_ID
                                            from [dbo].[Config_Parameter]
                                            where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomClass.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--67. LRVatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.LRVatRoomType.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.LRVatRoomType.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--68. password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.password') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.password'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--69. reply to
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.replyTo') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.replyTo'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--70. RoomTypeOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.alternateURL') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.RoomTypeOverbooking.alternateURL'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--71. Url
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.url') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.url'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--72. username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.username') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.username'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--73. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.useSoap2') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.useSoap2'),
    'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
    from [dbo].[Config_Parameter_Predefined_Value]
    where value = 'true'),
    GETDATE(),  GETDATE())
END

-- Inbound Username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.inboundUsername') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.inboundUsername'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END

--75 Inbound Password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.inntopiacrs.inboundPassword') AND Context = 'pacman')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.inntopiacrs.inboundPassword'),
    'pacman', NULL, NULL,
    GETDATE(),  GETDATE())
END