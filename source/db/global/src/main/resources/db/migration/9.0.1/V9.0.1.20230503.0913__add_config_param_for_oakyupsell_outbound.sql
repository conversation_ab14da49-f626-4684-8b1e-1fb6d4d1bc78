IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'OakyUpsell'
                AND CPC.Category_Name = 'Outbound Connection Details')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Connection Details'),
                'OakyUpsell', N'Outbound OakyUpsell Connection Details group', getdate(), getdate())
END


IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'OakyUpsell'
                AND CPC.Category_Name = 'Outbound Data')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Data'),
                'OakyUpsell', N'Outbound OakyUpsell group', getdate(), getdate())
END


--Outbound Data Param Starts

--#1  DailyBAR.AllMessagesSentAsDelta
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.AllMessagesSentAsDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.AllMessagesSentAsDelta',
    'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#2 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
    'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. NULL (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'DailyBarSelectiveUpload'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#3 UploadAdultsBeyond2
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.UploadAdultsBeyond2')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.UploadAdultsBeyond2',
    'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#4 UploadChildAgeBuckets
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.UploadChildAgeBuckets')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.UploadChildAgeBuckets',
    'Use child age buckets for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#5 UploadChildrenBeyondExtra
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.UploadChildrenBeyondExtra')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.UploadChildrenBeyondExtra',
    'Include child rates beyond extra child for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#6 DailyBAR.dailybarRateCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.dailybarRateCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.dailybarRateCode',
    'Parameter for the dailybar rate code',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#7 DailyBAR.dailybarRoundingPrecision
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.dailybarRoundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.dailybarRoundingPrecision',
    'Parameter for the dailybar rounding precision',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#8 DailyBAR.miscAdjustment
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.miscAdjustment')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.miscAdjustment',
    'The value of the miscelaneous adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '0')
END

-- #9 DailyBAR.newDailyBARFormat
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.newDailyBARFormat')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.newDailyBARFormat',
    'Toggle to send DailyBAR in the new format',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#10 optimizedDailyBarAgileRateUpload
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.optimizedDailyBarAgileRateUpload')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.optimizedDailyBarAgileRateUpload',
    'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#11 DailyBAR.taxAdjustmentValue
   if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.taxAdjustmentValue',
    'Parameter for the tax adjustment',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#12 DailyBAR.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#13 AgileRates.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.AgileRates.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.AgileRates.uploadtype',
    'Indicates if Agile Rate decisions should be sent and if they are full or differential',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#14 DailyBAR.useDeltaForDifferential
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.useDeltaForDifferential')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.useDeltaForDifferential',
    'Toggle to send Delta as the RatePlanNotifType for differential files',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#15  useYieldCurrencyForDailyBar
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.useYieldCurrencyForDailyBar')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.useYieldCurrencyForDailyBar',
    'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#16 UploadCurrencyForDailyBAR
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.UploadCurrencyForDailyBAR')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.UploadCurrencyForDailyBAR',
    'Set value to convert DailyBAR decisions into this currency code.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#17 BarByLOS.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.BarByLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.BarByLOS.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#18 BARByLOSByRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.BARByLOSByRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.BARByLOSByRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#19 Fplos.fplosAtRoomCategory
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.Fplos.fplosAtRoomCategory')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.Fplos.fplosAtRoomCategory',
    'Toggle to send FPLOS with RoomCategory Element',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#20 BarFplosByRoomType.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.BarFplosByRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.BarFplosByRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#21 Fplos.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.Fplos.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.Fplos.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#22 LRAControlFPLOS.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRAControlFPLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRAControlFPLOS.uploadtype',
    'If full or differential decisions are to be sent',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#23 LRAControlMinLOS.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRAControlMinLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRAControlMinLOS.uploadtype',
    'If full or differential decisions are to be sent',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#24 UploadCurrencyForLRV
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.UploadCurrencyForLRV')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.UploadCurrencyForLRV',
    'Set value to convert LRV decisions into this currency code.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#25  LRVatRoomClass.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvUploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#26 LRVatRoomClass.includeCeilingDeltaMaxSold
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.includeCeilingDeltaMaxSold')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.includeCeilingDeltaMaxSold',
    'Toggle to include Ceiling, Deltas, and Max Solds values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#27 LRVatRoomClass.consolidateCeilingDelta
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.consolidateCeilingDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.consolidateCeilingDelta',
    'Select how to send consolidated Ceiling and Delta values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvConsolidationType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#28 LRVatRoomClass.roundingPrecision
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.roundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.roundingPrecision',
    'Precision of the rounding to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#29 LRVatRoomClass.taxAdjustmentValue
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.taxAdjustmentValue',
    'Tax adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#30 SendRoomTypesAsRoomClassLRV
-- if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.SendRoomTypesAsRoomClassLRV')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
-- VALUES ( 'pacman.integration.oakyupsell.SendRoomTypesAsRoomClassLRV',
--    'Parameter for sending room type as room class lrv',
--    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
--    FROM
--    [dbo].[Config_Parameter_Predefined_Value_Type]
--    WHERE
--    code = 'boolean'),
--    getdate(),
--    getdate(),
--    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'boolean') ,
--    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
--    null,
--    'FALSE')
-- END

--#31  LRVatRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvUploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#32  LRVatRoomType.consolidateCeilingDelta
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.consolidateCeilingDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.consolidateCeilingDelta',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvConsolidationType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#33 LRVatRoomType.includeCeilingDeltaMaxSold
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.includeCeilingDeltaMaxSold')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.includeCeilingDeltaMaxSold',
    'Toggle to include Ceiling, Deltas, and Max Solds values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#34 LRVatRoomType.roundingPrecision
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.roundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.roundingPrecision',
    'Precision of the rounding to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#35 LRVatRoomType.taxAdjustmentValue
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.taxAdjustmentValue',
    'Tax adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#36  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#37  MinLOSChunkedOnArrivalDateRateCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.MinLOSChunkedOnArrivalDateRateCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.MinLOSChunkedOnArrivalDateRateCode',
    'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#38 propertyCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.propertycode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.propertycode',
    'pacman.integration.oakyupsell.propertycode',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#39 clientcode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.clientcode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.clientcode',
    'The client code to include with HTNG decisions.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#40  YieldCurrencyCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.YieldCurrencyCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.YieldCurrencyCode',
    'Set value to override the property yield currency code',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

--#41 HotelOverbooking.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.HotelOverbooking.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.HotelOverbooking.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#42 RoomTypeOverbooking.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.RoomTypeOverbooking.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.RoomTypeOverbooking.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#43 HotelOverbooking.valueType
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.HotelOverbooking.valueType')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.HotelOverbooking.valueType',
    'Whether to send Overbooking or Authorized value',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'overbookingValueType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'overbooking')
END

--#44 RoomTypeOverbooking.valueType
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.RoomTypeOverbooking.valueType')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.RoomTypeOverbooking.valueType',
    'Whether to send Overbooking or Auhthorized value',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'overbookingValueType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'overbooking')
END

--#45 DeferredDecisionDeliverySeconds
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DeferredDecisionDeliverySeconds')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DeferredDecisionDeliverySeconds',
    'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '0')
END

--#46 allowTransferEncodingChunked
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.allowTransferEncodingChunked')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.allowTransferEncodingChunked',
    'Send Transfer Encoding chunked in the SOAP message',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

--#47 async
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.async')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.async',
    'Deliver decisions for.asynchronously',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

--#48 ignoreHttpResponse
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.ignoreHttpResponse')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.ignoreHttpResponse',
    'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#49 includeHTNGAsyncHeaders
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.includeHTNGAsyncHeaders')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.includeHTNGAsyncHeaders',
    'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

--#50 ack.includeSoapBody
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.ack.includeSoapBody')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.ack.includeSoapBody',
    'Include empty SOAP body tag when sending HTNG decision acknowledgements',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

--#51 reverseFplosTranslation
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.reverseFplosTranslation')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.reverseFplosTranslation',
    'Reverse Y and N meaning',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#52 soapChunkSize
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.soapChunkSize')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.soapChunkSize',
    'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '1000')
END

--#53 async.timeout
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.async.timeout')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.async.timeout',
    'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '-1')
END

--#54 ManualRestrictions.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.ManualRestrictions.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.ManualRestrictions.uploadtype',
    'Parameter to support full, differential and NULL type of Manual-Restriction upload.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

--#55 useCustomMessage
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.useCustomMessage')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.useCustomMessage',
    'Set to true to use a custom SOAP message',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#56 useHttpBasicAuth
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.useHttpBasicAuth')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.useHttpBasicAuth',
    'Use HTTP Basic Auth in addition to SOAP header authorization.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--#57  useSoapChunks
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.useSoapChunks')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.useSoapChunks',
    'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

--Outbound Connection Details Param Starts

--#58 BarByLOSatRoomClass.alternateURL
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.BarByLOSatRoomClass.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.BarByLOSatRoomClass.alternateURL',
    'An alternate url to which to send BarByLOSatRoomClass decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#59 BarByLOSatRoomType.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.BarByLOSatRoomType.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.BarByLOSatRoomType.alternateURL',
    'An alternate url to which to send BarByLOSatRoomType decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#60 customAvailAction
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.customAvailAction')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.customAvailAction',
    'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#61 customRateAction
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.customRateAction')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.customRateAction',
    'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#62 customReplyToAddress
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.customReplyToAddress')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.customReplyToAddress',
    'Toggle for whether or not to add a custom replyTo header in the outbound messages to.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'URL') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#63 DailyBAR.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.DailyBAR.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.DailyBAR.alternateURL',
    'An alternate url to which to send DailyBAR decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#64 Fplos.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.Fplos.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.Fplos.alternateURL',
    'An alternate url to which to send Fplos decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#65 HotelOverbooking.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.HotelOverbooking.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.HotelOverbooking.alternateURL',
    'An alternate url to which to send HotelOverbooking decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#66 inboundPassword
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.inboundPassword')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.inboundPassword',
    'The password to use for authenticating incoming HTNG messages.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#67 inboundUsername
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.inboundUsername')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.inboundUsername',
    'The username to use for authenticating incoming HTNG messages.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#68 LRVatRoomClass.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomClass.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomClass.alternateURL',
    'An alternate url to which to send LRVatRoomClass decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#69 LRVatRoomType.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.LRVatRoomType.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.LRVatRoomType.alternateURL',
    'An alternate url to which to send LRVatRoomType decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#70 password

if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.password')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.password',
    'integration password',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#71 pacman.integration.replyTo
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.replyTo')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.replyTo',
    'The url of the local callback server',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#72 RoomTypeOverbooking.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.RoomTypeOverbooking.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.RoomTypeOverbooking.alternateURL',
    'An alternate url to which to send RoomTypeOverbooking decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#73 pacman.integration.url
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.url')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.url',
    'Integration URL',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#74 pacman.integration.username
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.username')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.username',
    'Integration username',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

--#75 pacman.integration.useSoap2
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.oakyupsell.useSoap2')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.oakyupsell.useSoap2',
    'Set to true to use SOAP 1.2 protocol',
    (SELECT Config_Parameter_Predefined_Value_Type_ID
    FROM [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE code ='boolean'
    ),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'OakyUpsell' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    'TRUE')
END