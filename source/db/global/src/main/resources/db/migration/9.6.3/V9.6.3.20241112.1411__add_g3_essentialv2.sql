BEGIN

use [Global]

declare @PackageName varchar(250) = 'G3 Essential'

if not exists (select 1 from License_Package where Package_Name = @PackageName)
insert into License_Package values(@PackageName, 0, null);

-- Insert into License_Feature_type
if not exists (select 1 from License_Feature_type where name = 'PageCategory')
insert into License_Feature_type values('PageCategory');

if not exists (select 1 from License_Feature_type where name = 'Tab')
insert into License_Feature_type values('Tab');

if not exists (select 1 from License_Feature_type where name = 'WindowSize')
insert into License_Feature_type values('WindowSize');

if not exists (select 1 from License_Feature_type where name = 'Feature')
insert into License_Feature_type values('Feature');

if not exists (select 1 from License_Feature_type where name = 'WizardStep')
insert into License_Feature_type values('WizardStep');

-- Insert into License_Feature
-- Page Category

if not exists (select 1 from License_Feature where Feature_Code = 'DASHBOARDS')
insert into License_Feature values('DASHBOARDS','DASHBOARDS',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'MANAGE')
insert into License_Feature values('MANAGE','MANAGE',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'MONITOR')
insert into License_Feature values('MONITOR','MONITOR',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE')
insert into License_Feature values('CONFIGURE','CONFIGURE',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'FUNCTION_SPACE')
insert into License_Feature values('FUNCTION_SPACE','FUNCTION_SPACE',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'OTHER')
insert into License_Feature values('OTHER','OTHER',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DASHBOARDS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DASHBOARDS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MANAGE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MANAGE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MONITOR'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MONITOR'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FUNCTION_SPACE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'FUNCTION_SPACE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OTHER'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OTHER'), null);

-- Dashboard Screens

-- At A Glance
if not exists (select 1 from License_Feature_type where name = 'Page')
insert into License_Feature_type values('Page');

if not exists (select 1 from License_Feature where Feature_Code = 'at-a-glance')
insert into License_Feature values('at-a-glance','At A Glance',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'at-a-glance'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'at-a-glance'), null);

-- Business Analysis Dashboard
if not exists (select 1 from License_Feature where Feature_Code = 'business-analysis')
insert into License_Feature values('business-analysis','Business Analysis',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'business-analysis'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'business-analysis'), null);

-- Business Analysis Summary View
if not exists (select 1 from License_Feature where Feature_Code = 'BusinessAnalysisSummaryView')
insert into License_Feature values('BusinessAnalysisSummaryView','Summary',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisSummaryView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisSummaryView'), null);

-- Business Analysis Details View
if not exists (select 1 from License_Feature where Feature_Code = 'BusinessAnalysisDataDetailsView')
insert into License_Feature values('BusinessAnalysisDataDetailsView','Data Details',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisDataDetailsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisDataDetailsView'), null);

-- Dashboard Summary
if not exists (select 1 from License_Feature where Feature_Code = 'dashboard-summary')
insert into License_Feature values('dashboard-summary','Summary',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'dashboard-summary'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'dashboard-summary'), null);

-- Data Details
if not exists (select 1 from License_Feature where Feature_Code = 'dataDetails')
insert into License_Feature values('dataDetails','Data Details',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'dataDetails'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'dataDetails'), null);

-- Manage Screens
if not exists (select 1 from License_Feature where Feature_Code = 'demand-and-wash-management')
insert into License_Feature values('demand-and-wash-management','Demand and Wash',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'pricing')
insert into License_Feature values('pricing','Pricing',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'overbooking-management')
insert into License_Feature values('overbooking-management','Overbooking Management',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);


if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'demand-and-wash-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'demand-and-wash-management'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'overbooking-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'overbooking-management'), null);

-- Monitor Screens

-- Information Manager
if not exists (select 1 from License_Feature where Feature_Code = 'information-manager')
insert into License_Feature values('information-manager','Information Manager',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'information-manager'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'information-manager'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'IMSystemHealthView')
insert into License_Feature values('IMSystemHealthView','System Health',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'IMAlertsView')
insert into License_Feature values('IMAlertsView','Alerts',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'IMNotificationsView')
insert into License_Feature values('IMNotificationsView','Notifications',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

-- Investigator
if not exists (select 1 from License_Feature where Feature_Code = 'investigator')
insert into License_Feature values('investigator','Investigator',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'investigator'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'investigator'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMAlertsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMAlertsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMSystemHealthView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMSystemHealthView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMNotificationsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMNotificationsView'), null);



-- Monitor Reports>

if not exists (select 1 from License_Feature where Feature_Code = 'MONITOR_REPORTS')
insert into License_Feature values('MONITOR_REPORTS','MONITOR REPORTS',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'data-extraction-report')
insert into License_Feature values('data-extraction-report','Data Extraction',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'operations-report')
insert into License_Feature values('operations-report','Operations',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'pick-up-change-and-differential-control-report')
insert into License_Feature values('pick-up-change-and-differential-control-report','Pickup/Change and Differential Controls',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'pricing-report')
insert into License_Feature values('pricing-report','Pricing',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'schedule-reports')
insert into License_Feature values('schedule-reports','Scheduled Reports',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MONITOR_REPORTS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MONITOR_REPORTS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'data-extraction-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'data-extraction-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'operations-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'operations-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pick-up-change-and-differential-control-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pick-up-change-and-differential-control-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'schedule-reports'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'schedule-reports'), null);

-- Configure Screens
if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_INVENTORY')
insert into License_Feature values('CONFIGURE_INVENTORY','CONFIGURE INVENTORY',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_DECISIONS')
insert into License_Feature values('CONFIGURE_DECISIONS','CONFIGURE DECISIONS',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_EXTERNAL_DATA')
insert into License_Feature values('CONFIGURE_EXTERNAL_DATA','CONFIGURE EXTERNAL DATA',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_FORECASTS')
insert into License_Feature values('CONFIGURE_FORECASTS','CONFIGURE FORECASTS',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_PROPERTY')
insert into License_Feature values('CONFIGURE_PROPERTY','CONFIGURE PROPERTY',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CONFIGURE_PERMISSIONS')
insert into License_Feature values('CONFIGURE_PERMISSIONS','CONFIGURE PERMISSIONS',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'user-management')
insert into License_Feature values('user-management','User Management',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_INVENTORY'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_INVENTORY'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_DECISIONS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_DECISIONS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_EXTERNAL_DATA'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_EXTERNAL_DATA'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_FORECASTS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_FORECASTS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_PROPERTY'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_PROPERTY'), null);

-- Configure > Users
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_PERMISSIONS'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_PERMISSIONS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'user-management'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'user-management'), null);

-- Configure > Decisions Screen
if not exists (select 1 from License_Feature where Feature_Code = 'pricing-config')
insert into License_Feature values('pricing-config','Pricing Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'vendor-integration-mapping')
insert into License_Feature values('vendor-integration-mapping','Vendor Integration Mapping',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'PricingConfigurationFloorCeilingView')
insert into License_Feature values('PricingConfigurationFloorCeilingView','Pricing Configuration Ceiling Floor',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'IndependentProductsDefinitionStepView')
insert into License_Feature values('IndependentProductsDefinitionStepView','Pricing Configuration Definition',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'PricingConfigurationOffsetsView')
insert into License_Feature values('PricingConfigurationOffsetsView','Pricing Configuration Offsets',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'PricingConfigurationSupplementView')
insert into License_Feature values('PricingConfigurationSupplementView','Pricing Configuration Supplement',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-config'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-config'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'vendor-integration-mapping'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'vendor-integration-mapping'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationFloorCeilingView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationFloorCeilingView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndependentProductsDefinitionStepView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IndependentProductsDefinitionStepView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationOffsetsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationOffsetsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationSupplementView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationSupplementView'), null);

--  Configure > External Data Screen
if not exists (select 1 from License_Feature where Feature_Code = 'client-budget')
insert into License_Feature values('client-budget','Budget and My Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'corporate-business-views')
insert into License_Feature values('corporate-business-views','Business View',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CorporateBusinessView')
insert into License_Feature values('CorporateBusinessView','Corporate Business',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'PropertyBusinessView')
insert into License_Feature values('PropertyBusinessView','Property Business',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'group-status-code')
insert into License_Feature values('group-status-code','Group Status Codes',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'AccomClassMappingView')
insert into License_Feature values('AccomClassMappingView','Room Class Mapping',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ChannelSettingsView')
insert into License_Feature values('ChannelSettingsView','Channel Settings',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'CompetitorSettingsView')
insert into License_Feature values('CompetitorSettingsView','Competitor Settings',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'RateShoppingScheduleView')
insert into License_Feature values('RateShoppingScheduleView','Rate Shopping Schedule',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'rate-shopping-configuration')
insert into License_Feature values('rate-shopping-configuration','Rate Shopping Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'client-questionnaire')
insert into License_Feature values('client-questionnaire','Integration Questionnaire',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-budget'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-budget'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'corporate-business-views'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'corporate-business-views'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'corporate-business-views'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CorporateBusinessView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyBusinessView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PropertyBusinessView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'group-status-code'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'group-status-code'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AccomClassMappingView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AccomClassMappingView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ChannelSettingsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ChannelSettingsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorSettingsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CompetitorSettingsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateShoppingScheduleView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RateShoppingScheduleView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateShoppingScheduleView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rate-shopping-configuration'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-questionnaire'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-questionnaire'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'BudgetUserForecastConfigurationView')
insert into License_Feature values('BudgetUserForecastConfigurationView','Budget And Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'BudgetDataView')
insert into License_Feature values('BudgetDataView','Budget Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'UserForecastDataView')
insert into License_Feature values('UserForecastDataView','User Forecast Data',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'FixedAboveBarView')
insert into License_Feature values('FixedAboveBarView','Budget And Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ForecastGroupView')
insert into License_Feature values('ForecastGroupView','Budget And Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'AttributeAssignmentView')
insert into License_Feature values('AttributeAssignmentView','Budget And Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'AttributeAssignmentNonAmsView')
insert into License_Feature values('AttributeAssignmentNonAmsView','Budget And Forecast',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BudgetUserForecastConfigurationView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BudgetUserForecastConfigurationView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BudgetDataView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BudgetDataView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'UserForecastDataView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'UserForecastDataView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastGroupView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ForecastGroupView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AttributeAssignmentView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AttributeAssignmentView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AttributeAssignmentNonAmsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AttributeAssignmentNonAmsView'), null);

--  Configure > Forecasts Screen
if not exists (select 1 from License_Feature where Feature_Code = 'limited-data-build')
insert into License_Feature values('limited-data-build','Limited Data Build',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'client-limited-data-build')
insert into License_Feature values('client-limited-data-build','Limited Data Build',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'market-segments')
insert into License_Feature values('market-segments','Market Segments',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'special-events-management')
insert into License_Feature values('special-events-management','Special Events',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'MarketSegmentView')
insert into License_Feature values('MarketSegmentView','Market Segment',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ClientProjectionsView')
insert into License_Feature values('ClientProjectionsView','Projections',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ClientBookingPatternView')
insert into License_Feature values('ClientBookingPatternView','Booking Pattern',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'limited-data-build'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'limited-data-build'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-limited-data-build'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-limited-data-build'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'market-segments'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'market-segments'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'special-events-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'special-events-management'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MarketSegmentView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MarketSegmentView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ClientProjectionsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ClientProjectionsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ClientBookingPatternView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ClientBookingPatternView'), null);

--  Configure > Inventory Screen
if not exists (select 1 from License_Feature where Feature_Code = 'rooms-configuration')
insert into License_Feature values('rooms-configuration','Rooms Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'RoomsConfigurationWelcomeView')
insert into License_Feature values('RoomsConfigurationWelcomeView','Rooms Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'rooms-configuration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rooms-configuration'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomsConfigurationWelcomeView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RoomsConfigurationWelcomeView'), null);

--  Configure > Property Screen
if not exists (select 1 from License_Feature where Feature_Code = 'fiscal-calendar')
insert into License_Feature values('fiscal-calendar','Fiscal Calendar',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'property-groups')
insert into License_Feature values('property-groups','Property Groups',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'property-specific-configuration')
insert into License_Feature values('property-specific-configuration','Property Specific Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'PropertyInformationView')
insert into License_Feature values('PropertyInformationView','Manage Property Information',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'fiscal-calendar'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'fiscal-calendar'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-groups'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'property-groups'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyInformationView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PropertyInformationView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-specific-configuration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'property-specific-configuration'), null);

-- Support Screens
if not exists (select 1 from License_Feature where Feature_Code = 'admin-tools')
insert into License_Feature values('admin-tools','Admin Tools',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'monitoring-dashboard')
insert into License_Feature values('monitoring-dashboard','Monitoring Dashboard',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'admin-tools'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'admin-tools'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'monitoring-dashboard'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'monitoring-dashboard'), null);

-- Other Screens
if not exists (select 1 from License_Feature where Feature_Code = 'data-feed')
insert into License_Feature values('data-feed','Data Feed',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ideashare')
insert into License_Feature values('ideashare','IDeaShare',(select License_Feature_type_id from License_Feature_type where Name = 'PageCategory'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'data-feed'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'data-feed'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ideashare'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ideashare'), null);

-- Features
if not exists (select 1 from License_Feature where Feature_Code = 'RoomClass')
insert into License_Feature values('RoomClass','Room Class',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ExternalSystem')
insert into License_Feature values('ExternalSystem','External System',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'IDP')
insert into License_Feature values('IDP','IDP',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'ContinuousPricing')
insert into License_Feature values('ContinuousPricing','Continuous Pricing',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.isContinuousPricingEnabled',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomClass'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RoomClass'), 1);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IDP'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'IDP'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ExternalSystem'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'ExternalSystem'), 2);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ContinuousPricing'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ContinuousPricing'), 1);

-- Config Parameters
if not exists (select 1 from License_Feature where Feature_Code = 'MaxIDP')
insert into License_Feature values('MaxIDP','Maximum Configurable IDPs',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.core.property.cdp.dailymax',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ForecastWindowBDE')
insert into License_Feature values('ForecastWindowBDE','Forecast Window BDE Value',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.forecasting.forecastWindowBDE',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ForecastWindowCDP')
insert into License_Feature values('ForecastWindowCDP','Forecast Window CDP Value',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.forecasting.forecastWindowCDP',null);

if not exists (select 1 from License_Feature where Feature_Code = 'OptimizationWindowCDP')
insert into License_Feature values('OptimizationWindowCDP','Optimization Window CDP Value',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.optimization.optimizationWindowCDP',null);

if not exists (select 1 from License_Feature where Feature_Code = 'OptimizationWindowBDE')
insert into License_Feature values('OptimizationWindowBDE','Optimization Window BDE Value',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.optimization.optimizationWindowBDE',null);

if not exists (select 1 from License_Feature where Feature_Code = 'WhatIf')
insert into License_Feature values('WhatIf','WhatIf',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.WhatIfEnabled',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxIDP'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'MaxIDP'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastWindowBDE'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ForecastWindowBDE'), 405);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastWindowCDP'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ForecastWindowCDP'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OptimizationWindowCDP'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OptimizationWindowCDP'), null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OptimizationWindowBDE'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OptimizationWindowBDE'), 405);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'WhatIf'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'WhatIf'), 0);

if not exists (select 1 from License_Feature where Feature_Code = 'AMS')
insert into License_Feature values('AMS','Analytical Market Segment',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.AnalyticalMarketSegmentEnabled',null);

if not exists (select 1 from License_Feature where Feature_Code = 'DataFeed')
insert into License_Feature values('DataFeed','Data Feed',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.isDatafeedFileEnabled',null);

if not exists (select 1 from License_Feature where Feature_Code = 'OnDemandOptimization')
insert into License_Feature values('OnDemandOptimization','On Demand Optimization',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.OnDemandIDPEnabled',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AMS'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'AMS'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OnDemandOptimization'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OnDemandOptimization'), 0);

if not exists (select 1 from License_Feature where Feature_Code = 'FutureDays')
insert into License_Feature values('FutureDays','Future Days',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.integration.futureDays',null);

if not exists (select 1 from License_Feature where Feature_Code = 'AgileRates')
insert into License_Feature values('AgileRates','Agile Rates',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.AgileRatesEnabled',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ActiveG3LinkedProducts')
insert into License_Feature values('ActiveG3LinkedProducts','G3 Active Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxActiveAgileRates', null);

if not exists (select 1 from License_Feature where Feature_Code = 'MaxUploadLinkedProducts')
insert into License_Feature values('MaxUploadLinkedProducts','3rd Party Upload Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxUploadedAgileRates', null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'), 0);

if not exists (select 1 from License_Feature where Feature_Code = 'IndependentProducts')
insert into License_Feature values('IndependentProducts','Independent Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.preproduction.IndependentProductsEnabled',null);

if not exists (select 1 from License_Feature where Feature_Code = 'SmallGroupPricing')
insert into License_Feature values('SmallGroupPricing','Small Group Pricing',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.PreProduction.SmallGroupPricingEnabled',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FutureDays'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'FutureDays'), 405);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRates'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'AgileRates'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndependentProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'IndependentProducts'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'SmallGroupPricing'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'SmallGroupPricing'), 0);

if not exists (select 1 from License_Feature where Feature_Code = 'DataExtractionReport')
insert into License_Feature values('DataExtractionReport','DataExtractionReport',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.MaxAllowedSchedulesForDataExtractionReport',null);

if not exists (select 1 from License_Feature where Feature_Code = 'OperationsReport')
insert into License_Feature values('OperationsReport','OperationsReport',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.MaxAllowedSchedulesForOperationsReport',null);

if not exists (select 1 from License_Feature where Feature_Code = 'PickupAndChangeReport')
insert into License_Feature values('PickupAndChangeReport','PickupAndChangeReport',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.MaxAllowedSchedulesForPickupAndChangeReport',null);

if not exists (select 1 from License_Feature where Feature_Code = 'PricingReport')
insert into License_Feature values('PricingReport','PricingReport',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.MaxAllowedSchedulesForPricingReport',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DataExtractionReport'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DataExtractionReport'), 1);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OperationsReport'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OperationsReport'), 1);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PickupAndChangeReport'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'PickupAndChangeReport'), 8);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingReport'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'PricingReport'), 7);


-- Other
if not exists (Select 1 from License_Feature where Feature_Code = 'DemandByOccDateAndWashView')
insert into License_Feature values('DemandByOccDateAndWashView','Demand By Occupancy Date And Wash View',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DemandByOccDateAndWashView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DemandByOccDateAndWashView'), null);

if not exists (Select 1 from License_Feature where Feature_Code = 'RoomTypeOverrideTableView')
insert into License_Feature values('RoomTypeOverrideTableView','Room Type Override Table View',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomTypeOverrideTableView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RoomTypeOverrideTableView'), null);

if not exists (Select 1 from License_Feature where Feature_Code = 'ForecastGroupOverrideTableView')
insert into License_Feature values('ForecastGroupOverrideTableView','Forecast Group Override Table View',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastGroupOverrideTableView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ForecastGroupOverrideTableView'), null);

if not exists (Select 1 from License_Feature where Feature_Code = 'MultiProductInvestigatorChartView')
insert into License_Feature values('MultiProductInvestigatorChartView','Pricing Override Summary Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductInvestigatorChartView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MultiProductInvestigatorChartView'), null);

if not exists (Select 1 from License_Feature where Feature_Code = 'MultiProductOverrideTabPlaceholderView')
insert into License_Feature values('MultiProductOverrideTabPlaceholderView','Pricing Override Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductOverrideTabPlaceholderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MultiProductOverrideTabPlaceholderView'), null);

if not exists (Select 1 from License_Feature where Feature_Code = 'MultiProductOverrideHistoryPlaceholderView')
insert into License_Feature values('MultiProductOverrideHistoryPlaceholderView','Pricing Override History Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductOverrideHistoryPlaceholderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MultiProductOverrideHistoryPlaceholderView'), null);

-- ST19 Configuration Param
if not exists (select 1 from License_Feature where Feature_Code = 'ST19DataFeed')
insert into License_Feature values('ST19DataFeed','ST19 Data Feed Optimized Fetch Enabled',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.preProduction.isOptimizedST19DataFeedFetchEnabled',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ST19DataExtraction')
insert into License_Feature values('ST19DataExtraction','Display ST19 On Data Extraction Report',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.displayST19OnDataExtractionReport',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ST19AtAGlance')
insert into License_Feature values('ST19AtAGlance','Display ST19 On At A Glance Summary',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.displayST19OnAtAGlanceSummary',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ST19BAD')
insert into License_Feature values('ST19BAD','Display ST19 On BAD',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.displayST19OnBAD',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ST19DataFeedMSRT')
insert into License_Feature values('ST19DataFeedMSRT','ST19 Data Feed MSRT',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.preProduction.enableST19ForDatafeedMSRT',null);

if not exists (select 1 from License_Feature where Feature_Code = 'ST19RTDataByDBProc')
insert into License_Feature values('ST19RTDataByDBProc','Room Type ST19 Data By DB Proc',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.preProduction.roomTypeST19DataByDBProc',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataFeed'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19DataFeed'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataExtraction'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19DataExtraction'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19AtAGlance'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19AtAGlance'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19BAD'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19BAD'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataFeedMSRT'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19DataFeedMSRT'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19RTDataByDBProc'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ST19RTDataByDBProc'), 0);

-- SFTP/FTP DeliveryForReports
if not exists (select 1 from License_Feature where Feature_Code = 'FTPDeliveryForReports')
insert into License_Feature values('FTPDeliveryForReports','Enable FTP Delivery For Reports',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.PreProduction.EnableFTPDeliveryForReports',null);
if not exists (select 1 from License_Feature where Feature_Code = 'SFTPDeliveryForReports')
insert into License_Feature values('SFTPDeliveryForReports','Enable SFTP Delivery For Reports',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.PreProduction.EnableSFTPDeliveryForReports',null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FTPDeliveryForReports'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'FTPDeliveryForReports'), 0);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'SFTPDeliveryForReports'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'SFTPDeliveryForReports'), 0);

-- Pricing Summary View
if not exists (select 1 from License_Feature where Feature_Code = 'InvestigatorChartView')
insert into License_Feature values('InvestigatorChartView','Summary Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'InvestigatorOverrideHistoryPlaceholderView')
insert into License_Feature values('InvestigatorOverrideHistoryPlaceholderView','Override History Tab',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'InvestigatorChartView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'InvestigatorChartView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'InvestigatorOverrideHistoryPlaceholderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'InvestigatorOverrideHistoryPlaceholderView'), null);

-- Rooms Configuration Wizard
if not exists (select 1 from License_Feature where Feature_Code = 'CostOfWalkView')
insert into License_Feature values('CostOfWalkView','Step - Cost of Walk',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'RoomTypeMappingView')
insert into License_Feature values('RoomTypeMappingView','Step - Room Type',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'OverbookingView')
insert into License_Feature values('OverbookingView','Step - Overbooking',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CostOfWalkView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'CostOfWalkView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomTypeMappingView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RoomTypeMappingView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OverbookingView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OverbookingView'), null);

--Overbooking screen hide cow column
if not exists (select 1 from License_Feature_type where Name = 'TableColumnName')
insert into License_Feature_type values('TableColumnName');

if not exists (select 1 from License_Feature where Feature_Code = 'OverbookingCostOfWalkOverrideValueColumn')
insert into License_Feature values('OverbookingCostOfWalkOverrideValueColumn','Overbooking CostOfWalk OverrideValue Column',(select License_Feature_type_id from License_Feature_type where Name = 'TableColumnName'),null,null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OverbookingCostOfWalkOverrideValueColumn'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OverbookingCostOfWalkOverrideValueColumn'), 0);

--Info manager notifications
if not exists (select 1 from License_Feature_type where Name = 'Notification')
insert into License_Feature_type values('Notification');
if not exists (select 1 from License_Feature_type where Name = 'NotificationSubType')
insert into License_Feature_type values('NotificationSubType');

if not exists (select 1 from License_Feature where Feature_Code = 'PropertyStep')
insert into License_Feature values('PropertyStep','Step - info manager notification',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'NotificationDetailsStep')
insert into License_Feature values('NotificationDetailsStep','Step - info manager notification',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'ConditionStep')
insert into License_Feature values('ConditionStep','Step - info manager notification',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'MonitoringWindowStep')
insert into License_Feature values('MonitoringWindowStep','Step - info manager notification',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null,null);

if not exists (select 1 from License_Feature where Feature_Code = 'DecisionChangeEx')
insert into License_Feature values('DecisionChangeEx','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'CompetitorPriceChange')
insert into License_Feature values('CompetitorPriceChange','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'DecisionAsOfLastNightlyOptimization')
insert into License_Feature values('DecisionAsOfLastNightlyOptimization','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'CompetitorPriceAsOfLastNightlyOptimization')
insert into License_Feature values('CompetitorPriceAsOfLastNightlyOptimization','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'DecisionAsOfLastOptimization')
insert into License_Feature values('DecisionAsOfLastOptimization','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'DecisionChangeExAsOfLastOptimization')
insert into License_Feature values('DecisionChangeExAsOfLastOptimization','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'Notification'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING')
insert into License_Feature values('NOTIFICATION_SUBTYPE_PRICING','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'NotificationSubType'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_COMP_SUBTYPE')
insert into License_Feature values('NOTIFICATION_SUBTYPE_COMP_SUBTYPE','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'NotificationSubType'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING_BY_VALUE')
insert into License_Feature values('NOTIFICATION_SUBTYPE_PRICING_BY_VALUE','Notification',(select License_Feature_type_id from License_Feature_type where Name = 'NotificationSubType'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyStep'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'PropertyStep'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NotificationDetailsStep'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'NotificationDetailsStep'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ConditionStep'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ConditionStep'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MonitoringWindowStep'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MonitoringWindowStep'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionChangeEx'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DecisionChangeEx'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorPriceChange'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'CompetitorPriceChange'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionAsOfLastNightlyOptimization'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DecisionAsOfLastNightlyOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorPriceAsOfLastNightlyOptimization'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'CompetitorPriceAsOfLastNightlyOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionAsOfLastOptimization'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DecisionAsOfLastOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionChangeExAsOfLastOptimization'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DecisionChangeExAsOfLastOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_COMP_SUBTYPE'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_COMP_SUBTYPE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING_BY_VALUE'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING_BY_VALUE'), null);

-- Configuration -> Decision -> Decision Configuration
if not exists (select 1 from License_Feature where Feature_Code = 'decision-configuration')
insert into License_Feature values('decision-configuration','Decision Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'decision-configuration'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'decision-configuration'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'DecisionConfiguration')
insert into License_Feature values('DecisionConfiguration','Decision Configuration Feature Parameter',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.DecisionConfigurationEnabled',null);
if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionConfiguration'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DecisionConfiguration'), 1);

if not exists (select 1 from License_Feature where Feature_Code = 'RateCodeView')
insert into License_Feature values('RateCodeView','Rate Code Level',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);
if not exists (select 1 from License_Feature where Feature_Code = 'GroupView')
insert into License_Feature values('GroupView','Group Business Level',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateCodeView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RateCodeView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupView'), null);

-- pacman.core.LimitedDataBuildEnabled = true
if not exists (select 1 from License_Feature where Feature_Code = 'LimitedDataBuildEnabled')
insert into License_Feature values('LimitedDataBuildEnabled','LimitedDataBuildEnabled Feature',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.core.LimitedDataBuildEnabled', null);
if not exists (select 1 from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView')
insert into License_Feature values('LimitedDataBuildMarketSegmentView','LimitedDataBuild MarketSegments',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);
if not exists (select 1 from License_Feature where Feature_Code = 'ProjectionsDownloaderView')
insert into License_Feature values('ProjectionsDownloaderView','LimitedDataBuild Projections',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);

if not exists ( select 1 from License_Feature_Value where License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'LimitedDataBuildEnabled'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'LimitedDataBuildEnabled'), 1);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ProjectionsDownloaderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ProjectionsDownloaderView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ActiveG3LinkedProducts')
insert into License_Feature values('ActiveG3LinkedProducts','G3 Active Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxActiveAgileRates', null);
if not exists (select 1 from License_Feature where Feature_Code = 'MaxUploadLinkedProducts')
insert into License_Feature values('MaxUploadLinkedProducts','3rd Party Upload Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxUploadedAgileRates', null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'), 0);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'), 0);

-- Overbooking
if not exists (select 1 from License_Feature where Feature_Code = 'Overbooking')
insert into License_Feature values('Overbooking','Over booking',
(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'Overbooking'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'Overbooking'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'demand-only')
insert into License_Feature values('demand-only','Demand Only and No Wash',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),null,null);

if not exists ( select 1 from License_Feature_Value where
   License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
   License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'Overbooking'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'demand-only'), null);


END;