-- anyhtng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.anyhtng.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'AnyHtng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- curtisc
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.curtisc.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'curtisc' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hbsi
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hbsi.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hbsi' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- infor
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.infor.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'infor' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- protel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protel.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rvng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rvng.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rvng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- siteminder
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.siteminder.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'siteminder' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- suite8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.suite8.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'suite8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis1
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis1.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis1' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis2
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis2.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis2' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis3
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis3.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis3' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis4
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis4.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis4' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis5
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis5.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis5' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis6
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis6.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis6' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis7
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis7.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis7' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis8.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis9
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis9.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis9' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis10
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis10.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis10' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis11
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis11.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis11' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- traveltripper
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.traveltripper.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'traveltripper' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- iHotelier
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.iHotelier.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'iHotelier' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- yourvoyager
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.yourvoyager.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'yourvoyager' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- winnerpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.winnerpms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'winnerpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- windsurfercrs
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.windsurfercrs.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'windsurfercrs' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- webrezpropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.webrezpropms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'webrezpropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- vaillms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.vaillms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'vaillms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- staah
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.staah.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'staah' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- smarthotel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.smarthotel.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'smarthotel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rmspms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rmspms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rmspms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rezlynx
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rezlynx.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rezlynx' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- ratetiger
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.ratetiger.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'ratetiger' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rategain
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rategain.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rategain' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- protelioair
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protelioair.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protelioair' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- newbookpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.newbookpms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'newbookpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- leanpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.leanpms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'leanpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hotelspider
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelspider.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelspider' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hotelnetsolution
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelnetsolution.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelnetsolution' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hermeshotels
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hermeshotels.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hermeshotels' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- groupmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.groupmax.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'groupmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- cubilis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.cubilis.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'cubilis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- connecterevmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.connecterevmax.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'connecterevmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- bookingexpert
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookingexpert.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookingexpert' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- bookassist
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookassist.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookassist' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- blastness
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.blastness.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'blastness' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- avvio
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.avvio.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'avvio' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- availpro
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.availpro.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'availpro' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- advantagereserve
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.advantagereserve.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'advantagereserve' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- maestropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.maestropms.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'maestropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- Vendor
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForLRV')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.UploadCurrencyForLRV',
 'Set value to convert LRV decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Vendor' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForLRV') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForLRV'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;