IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'RezTripBAR'
                AND CPC.Category_Name = 'Outbound Connection Details')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Connection Details'),
                'RezTripBAR', N'Outbound RezTripBAR Connection Details group', getdate(), getdate())
END


IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'RezTripBAR'
                AND CPC.Category_Name = 'Outbound Data')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Data'),
                'RezTripBAR', N'Outbound RezTripBAR group', getdate(), getdate())
END


   --Outbound Data Param Starts

--#1 DailyBAR.dailybarRateCode

if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.dailybarRateCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.dailybarRateCode',
    'Parameter for the dailybar rate code',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

 --#2 DailyBAR.dailybarRoundingPrecision
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.dailybarRoundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.dailybarRoundingPrecision',
    'Parameter for the dailybar rounding precision',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '2')
END

  --#3 DailyBAR.miscAdjustment
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.miscAdjustment')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.miscAdjustment',
    'The value of the miscelaneous adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

   --#4 DailyBAR.taxAdjustmentValue

   if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.taxAdjustmentValue',
    'Parameter for the tax adjustment',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

 -- #5 DailyBAR.uploadtype

if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END



 --#6 propertyCode


if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.propertycode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.propertycode',
    'pacman.integration.reztripbar.propertycode',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

 --#7 AgileRates.uploadtype

 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.AgileRates.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.AgileRates.uploadtype',
    'Indicates if Agile Rate decisions should be sent and if they are full or differential',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

 -- #8 async
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.async')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.async',
    'Deliver decisions for.asynchronously',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

 -- #9 BarByLOS.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.BarByLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.BarByLOS.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  -- #10 BarFplosByRoomType.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.BarFplosByRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.BarFplosByRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  -- #11 DailyBAR.newDailyBARFormat
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.newDailyBARFormat')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.newDailyBARFormat',
    'Toggle to send DailyBAR in the new format',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

  -- #12 DailyBAR.useDeltaForDifferential
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.useDeltaForDifferential')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.useDeltaForDifferential',
    'Toggle to send Delta as the RatePlanNotifType for differential files',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

  -- #13 Fplos.fplosAtRoomCategory
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.Fplos.fplosAtRoomCategory')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.Fplos.fplosAtRoomCategory',
    'Toggle to send FPLOS with RoomCategory Element',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  -- #14 Fplos.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.Fplos.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.Fplos.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')

END

  -- #15 HotelOverbooking.uploadtype
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.HotelOverbooking.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.HotelOverbooking.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  -- #16 HotelOverbooking.valueType
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.HotelOverbooking.valueType')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.HotelOverbooking.valueType',
    'Whether to send Overbooking or Authorized value',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'overbookingValueType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

   -- #17 LRVatRoomClass.consolidateCeilingDelta
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.consolidateCeilingDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.consolidateCeilingDelta',
    'Select how to send consolidated Ceiling and Delta values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvConsolidationType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

    -- #18 LRVatRoomClass.includeCeilingDeltaMaxSold
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.includeCeilingDeltaMaxSold')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.includeCeilingDeltaMaxSold',
    'Toggle to include Ceiling, Deltas, and Max Solds values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

     -- #19 LRVatRoomClass.roundingPrecision
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.roundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.roundingPrecision',
    'Precision of the rounding to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

      -- #20 LRVatRoomClass.taxAdjustmentValue
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.taxAdjustmentValue',
    'Tax adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END


 --#21  LRVatRoomClass.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvUploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  --#22  LRVatRoomType.consolidateCeilingDelta
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.consolidateCeilingDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.consolidateCeilingDelta',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvConsolidationType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  --#23  LRVatRoomType.includeCeilingDeltaMaxSold
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.includeCeilingDeltaMaxSold')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.includeCeilingDeltaMaxSold',
    'Toggle to include Ceiling, Deltas, and Max Solds values',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#24  LRVatRoomType.roundingPrecision
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.roundingPrecision')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.roundingPrecision',
    'Precision of the rounding to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

   --#25 LRVatRoomType.taxAdjustmentValue
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.taxAdjustmentValue')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.taxAdjustmentValue',
    'Tax adjustment to be applied',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

    --#26  LRVatRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'lrvUploadType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

  --#27  RoomTypeOverbooking.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.RoomTypeOverbooking.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.RoomTypeOverbooking.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

   --#28  RoomTypeOverbooking.valueType
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.RoomTypeOverbooking.valueType')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.RoomTypeOverbooking.valueType',
    'Whether to send Overbooking or Auhthorized value',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'overbookingValueType'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

  --#29  reverseFplosTranslation
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.reverseFplosTranslation')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.reverseFplosTranslation',
    'Reverse Y and N meaning',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#30  YieldCurrencyCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.YieldCurrencyCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.YieldCurrencyCode',
    'Set value to override the property yield currency code',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

 --#31  useSoapChunks
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.useSoapChunks')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.useSoapChunks',
    'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#32  soapChunkSize
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.soapChunkSize')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.soapChunkSize',
    'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '1000')
END

  --#33  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

 --#34  BARByLOSByRoomType.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.BARByLOSByRoomType.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.BARByLOSByRoomType.uploadtype',
    'If full or differential decisions are to be sent at a decisiontype level',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

 --#35  useYieldCurrencyForDailyBar
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.useYieldCurrencyForDailyBar')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.useYieldCurrencyForDailyBar',
    'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#36  DeferredDecisionDeliverySeconds
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DeferredDecisionDeliverySeconds')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DeferredDecisionDeliverySeconds',
    'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '0')
END

 --#37  DailyBAR.AllMessagesSentAsDelta
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.AllMessagesSentAsDelta')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.AllMessagesSentAsDelta',
    'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

  --#38  MinLOSChunkedOnArrivalDateRateCode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.MinLOSChunkedOnArrivalDateRateCode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.MinLOSChunkedOnArrivalDateRateCode',
    'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

   --#39 LRAControlFPLOS.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRAControlFPLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRAControlFPLOS.uploadtype',
    'If full or differential decisions are to be sent',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

   --#40 LRAControlMinLOS.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRAControlMinLOS.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRAControlMinLOS.uploadtype',
    'If full or differential decisions are to be sent',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

    --#41 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
    'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. NULL (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'DailyBarSelectiveUpload'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

     --#42 ack.includeSoapBody
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.ack.includeSoapBody')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.ack.includeSoapBody',
    'Include empty SOAP body tag when sending HTNG decision acknowledgements',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

      --#43 useCustomMessage
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.useCustomMessage')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.useCustomMessage',
    'Set to true to use a custom SOAP message',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

    --#44 UploadAdultsBeyond2
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.UploadAdultsBeyond2')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.UploadAdultsBeyond2',
    'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

     --#45 UploadChildrenBeyondExtra
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.UploadChildrenBeyondExtra')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.UploadChildrenBeyondExtra',
    'Include child rates beyond extra child for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

      --#46 optimizedDailyBarAgileRateUpload
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.optimizedDailyBarAgileRateUpload')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.optimizedDailyBarAgileRateUpload',
    'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

       --#47 allowTransferEncodingChunked
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.allowTransferEncodingChunked')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.allowTransferEncodingChunked',
    'Send Transfer Encoding chunked in the SOAP message',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

        --#48 async.timeout
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.async.timeout')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.async.timeout',
    'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '-1')
END

         --#49 clientcode
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.clientcode')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.clientcode',
    'The client code to include with HTNG decisions.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

      --#50 ignoreHttpResponse
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.ignoreHttpResponse')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.ignoreHttpResponse',
    'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

       --#51 includeHTNGAsyncHeaders
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.includeHTNGAsyncHeaders')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.includeHTNGAsyncHeaders',
    'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'TRUE')
END

  --#52 UploadChildAgeBuckets
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.UploadChildAgeBuckets')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.UploadChildAgeBuckets',
    'Use child age buckets for HTNG daily BAR decisions',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#53 useHttpBasicAuth
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.useHttpBasicAuth')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.useHttpBasicAuth',
    'Use HTTP Basic Auth in addition to SOAP header authorization.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'boolean'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

  --#54 UploadCurrencyForDailyBAR
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.UploadCurrencyForDailyBAR')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.UploadCurrencyForDailyBAR',
    'Set value to convert DailyBAR decisions into this currency code.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    '')
END

   --#55 UploadCurrencyForLRV
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.UploadCurrencyForLRV')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.UploadCurrencyForLRV',
    'Set value to convert LRV decisions into this currency code.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'FALSE')
END

    --#56 ManualRestrictions.uploadtype
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.ManualRestrictions.uploadtype')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.ManualRestrictions.uploadtype',
    'Parameter to support full, differential and NULL type of Manual-Restriction upload.',
    (SELECT	TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
    FROM
    [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE
    code = 'uploadtype'),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
    null,
    'none')
END

 --Outbound Connection Details Param Starts

 --#57 password

if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.password')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.password',
    'integration password',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END


 --#58 username
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.username')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.username',
    'Integration username',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

 --#59 url

 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.url')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.url',
    'Integration URL',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

 -- #60 BarByLOSatRoomClass.alternateURL
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.BarByLOSatRoomClass.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.BarByLOSatRoomClass.alternateURL',
    'An alternate url to which to send BarByLOSatRoomClass decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

 --#61  RoomTypeOverbooking.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.RoomTypeOverbooking.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.RoomTypeOverbooking.alternateURL',
    'An alternate url to which to send RoomTypeOverbooking decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

  --#62  HotelOverbooking.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.HotelOverbooking.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.HotelOverbooking.alternateURL',
    'An alternate url to which to send HotelOverbooking decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

  --#63 Fplos.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.Fplos.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.Fplos.alternateURL',
    'An alternate url to which to send Fplos decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

   --#64 customAvailAction
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.customAvailAction')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.customAvailAction',
    'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

    --#65 customRateAction
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.customRateAction')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.customRateAction',
    'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

     --#66 pacman.integration.replyTo
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.replyTo')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.replyTo',
    'The url of the local callback server',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

      --#67 useSoap2
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.useSoap2')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.useSoap2',
    'Set to true to use SOAP 1.2 protocol',
    (SELECT Config_Parameter_Predefined_Value_Type_ID
    FROM [dbo].[Config_Parameter_Predefined_Value_Type]
    WHERE code ='boolean'
    ),
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    'FALSE')
END

       --#69 inboundPassword
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.inboundPassword')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.inboundPassword',
    'The password to use for authenticating incoming HTNG messages.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

       --#70 inboundUsername
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.inboundUsername')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.inboundUsername',
    'The username to use for authenticating incoming HTNG messages.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

       --#71 customReplyToAddress
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.customReplyToAddress')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.customReplyToAddress',
    'Toggle for whether or not to add a custom replyTo header in the outbound messages to.',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'URL') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

     --#72 DailyBAR.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.DailyBAR.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.DailyBAR.alternateURL',
    'An alternate url to which to send DailyBAR decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

   --#73 LRVatRoomType.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomType.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomType.alternateURL',
    'An alternate url to which to send LRVatRoomType decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

    --#74 LRVatRoomClass.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.LRVatRoomClass.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.LRVatRoomClass.alternateURL',
    'An alternate url to which to send LRVatRoomClass decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END

  --#75 BarByLOSatRoomType.alternateURL
 if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.reztripbar.BarByLOSatRoomType.alternateURL')
BEGIN
INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
VALUES ( 'pacman.integration.reztripbar.BarByLOSatRoomType.alternateURL',
    'An alternate url to which to send BarByLOSatRoomType decisions',
    null,
    getdate(),
    getdate(),
    (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
    (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'RezTripBAR' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Connection Details')  ) ,
    null,
    '')
END
