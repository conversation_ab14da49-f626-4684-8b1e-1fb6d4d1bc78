-- anyhtng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.anyhtng.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'AnyHtng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.anyhtng.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- curtisc
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.curtisc.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'curtisc' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.curtisc.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hbsi
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hbsi.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hbsi' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hbsi.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- infor
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.infor.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'infor' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.infor.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- protel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protel.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protel.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rvng
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rvng.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rvng' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rvng.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- siteminder
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.siteminder.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'siteminder' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.siteminder.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- suite8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.suite8.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'suite8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.suite8.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis1
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis1.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis1' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis1.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis2
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis2.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis2' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis2.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis3
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis3.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis3' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis3.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis4
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis4.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis4' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis4.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis5
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis5.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis5' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis5.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis6
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis6.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis6' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis6.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis7
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis7.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis7' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis7.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis8
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis8.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis8' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis8.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis9
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis9.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis9' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis9.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis10
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis10.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis10' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis10.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- synxis11
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.synxis11.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'synxis11' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.synxis11.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- traveltripper
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.traveltripper.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'traveltripper' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.traveltripper.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- iHotelier
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.iHotelier.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'iHotelier' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.iHotelier.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- yourvoyager
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.yourvoyager.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'yourvoyager' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.yourvoyager.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- winnerpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.winnerpms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'winnerpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.winnerpms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- windsurfercrs
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.windsurfercrs.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'windsurfercrs' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.windsurfercrs.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- webrezpropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.webrezpropms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'webrezpropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.webrezpropms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- vaillms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.vaillms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'vaillms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.vaillms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- staah
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.staah.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'staah' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.staah.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- smarthotel
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.smarthotel.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'smarthotel' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.smarthotel.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rmspms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rmspms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rmspms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rmspms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rezlynx
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rezlynx.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rezlynx' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rezlynx.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- ratetiger
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.ratetiger.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'ratetiger' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.ratetiger.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- rategain
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.rategain.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'rategain' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.rategain.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- protelioair
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.protelioair.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'protelioair' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.protelioair.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- newbookpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.newbookpms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'newbookpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.newbookpms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- leanpms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.leanpms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'leanpms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.leanpms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hotelspider
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelspider.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelspider' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelspider.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hotelnetsolution
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hotelnetsolution.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hotelnetsolution' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hotelnetsolution.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- hermeshotels
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.hermeshotels.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'hermeshotels' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.hermeshotels.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- groupmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.groupmax.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'groupmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.groupmax.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- cubilis
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.cubilis.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'cubilis' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.cubilis.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- connecterevmax
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.connecterevmax.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'connecterevmax' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.connecterevmax.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- bookingexpert
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookingexpert.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookingexpert' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookingexpert.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- bookassist
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.bookassist.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'bookassist' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.bookassist.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- blastness
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.blastness.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'blastness' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.blastness.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- avvio
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.avvio.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'avvio' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.avvio.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- availpro
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.availpro.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'availpro' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.availpro.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- advantagereserve
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.advantagereserve.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'advantagereserve' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.advantagereserve.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- maestropms
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.maestropms.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'maestropms' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.maestropms.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;

-- Vendor
if not exists (select 1 from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForDailyBAR')
 BEGIN 
 INSERT INTO [dbo].[Config_Parameter] ([Name] ,[Description] ,[Config_Parameter_Predefined_Value_Type_ID] ,[CreateDate] ,[ModifiedDate] ,[Config_Parameter_Type_ID] ,[Group_ID], [Value_Constraints], [Default_Value])
  VALUES ( 'pacman.integration.UploadCurrencyForDailyBAR',
 'Set value to convert DailyBAR decisions into this currency code.',
 null,
 getdate(),
 getdate(),
 (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char') ,
 (select top 1 [Group_ID] from [dbo].[Config_Parameter_Group] where Group_Name = 'Vendor' and Category_ID = (select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data')  ) ,
 'null',
 null)
 
if not exists (select 1 from [dbo].[Config_Parameter_Value] where [Config_Parameter_ID] = (select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForDailyBAR') and context = 'pacman')
 INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID] ,[Context] ,[FixedValue] ,[Config_Parameter_Predefined_Value_ID] ,[Created_DTTM] ,[Last_Updated_DTTM])
  VALUES ((select top 1 [Config_Parameter_ID] from [dbo].[Config_Parameter] where name = 'pacman.integration.UploadCurrencyForDailyBAR'),
 'pacman',
 null,
 null, getdate(),
 getdate())
 
END;