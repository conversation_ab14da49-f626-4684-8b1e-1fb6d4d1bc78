BEGIN

use [Global]

declare @PackageName char(50) = 'G3 Select'

if not exists (select 1 from License_Package where Package_Name = @PackageName)
insert into License_Package values(@PackageName,0, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DASHBOARDS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DASHBOARDS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MANAGE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MANAGE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MONITOR'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MONITOR'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FUNCTION_SPACE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'FUNCTION_SPACE'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OTHER'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OTHER'), null);


-- Dashboard Screens

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'at-a-glance'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'at-a-glance'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'business-analysis'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'business-analysis'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisSummaryView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisSummaryView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisDataDetailsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisDataDetailsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'dashboard-summary'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'dashboard-summary'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'dataDetails'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'dataDetails'), null);

-- Manage Screens

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'demand-and-wash-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'demand-and-wash-management'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'overbooking-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'overbooking-management'), null);

-- Monitor Screens

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'information-manager'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'information-manager'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMAlertsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMAlertsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMSystemHealthView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMSystemHealthView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMNotificationsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMNotificationsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'investigator'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'investigator'), null);

-- Monitor Reports>

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MONITOR_REPORTS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MONITOR_REPORTS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'data-extraction-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'data-extraction-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'operations-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'operations-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pick-up-change-and-differential-control-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pick-up-change-and-differential-control-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-report'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'schedule-reports'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'schedule-reports'), null);

-- Configure Screens

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_INVENTORY'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_INVENTORY'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_DECISIONS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_DECISIONS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_EXTERNAL_DATA'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_EXTERNAL_DATA'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_FORECASTS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_FORECASTS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_PROPERTY'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_PROPERTY'), null);

-- Configure > Users
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CONFIGURE_PERMISSIONS'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'CONFIGURE_PERMISSIONS'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'user-management'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'user-management'), null);

-- Configure > Decisions Screen
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-config'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-config'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'vendor-integration-mapping'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'vendor-integration-mapping'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationFloorCeilingView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationFloorCeilingView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndependentProductsDefinitionStepView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IndependentProductsDefinitionStepView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationOffsetsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationOffsetsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingConfigurationSupplementView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingConfigurationSupplementView'), null);

--  Configure > External Data Screen

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-budget'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-budget'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'corporate-business-views'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'corporate-business-views'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CorporateBusinessView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CorporateBusinessView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyBusinessView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PropertyBusinessView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'group-status-code'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'group-status-code'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AccomClassMappingView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AccomClassMappingView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ChannelSettingsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ChannelSettingsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorSettingsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CompetitorSettingsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateShoppingScheduleView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RateShoppingScheduleView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'rate-shopping-configuration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rate-shopping-configuration'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-questionnaire'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-questionnaire'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BudgetUserForecastConfigurationView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BudgetUserForecastConfigurationView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BudgetDataView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BudgetDataView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'UserForecastDataView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'UserForecastDataView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastGroupView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ForecastGroupView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AttributeAssignmentView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AttributeAssignmentView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AttributeAssignmentNonAmsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AttributeAssignmentNonAmsView'), null);

--  Configure > Forecasts Screen

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'limited-data-build'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'limited-data-build'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'client-limited-data-build'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'client-limited-data-build'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'market-segments'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'market-segments'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'special-events-management'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'special-events-management'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MarketSegmentView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MarketSegmentView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ClientProjectionsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ClientProjectionsView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ClientBookingPatternView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ClientBookingPatternView'), null);

--  Configure > Inventory Screen
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'rooms-configuration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rooms-configuration'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomsConfigurationWelcomeView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RoomsConfigurationWelcomeView'), null);

--  Configure > Property Screen
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'fiscal-calendar'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'fiscal-calendar'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-groups'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'property-groups'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyInformationView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PropertyInformationView'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-specific-configuration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'property-specific-configuration'), null);

-- Support Screens
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'admin-tools'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'admin-tools'), null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'monitoring-dashboard'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'monitoring-dashboard'), null);

-- Other Screens
if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'data-feed'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'data-feed'), null);

 if not exists ( select 1 from License_Feature_Value where
     License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
     License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ideashare'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ideashare'), null);

-- Features
if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomClass'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RoomClass'), 1);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IDP'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IDP'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ExternalSystem'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ExternalSystem'), 2);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ContinuousPricing'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ContinuousPricing'), 1);

-- Config Parameters

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxIDP'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MaxIDP'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastWindowBDE'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'ForecastWindowBDE'), 560);

 if not exists ( select 1 from License_Feature_Value where
     License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
     License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastWindowCDP'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ForecastWindowCDP'), null);


if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OptimizationWindowCDP'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OptimizationWindowCDP'), null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OptimizationWindowBDE'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OptimizationWindowBDE'), 405);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'WhatIf'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'WhatIf'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AMS'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AMS'), 1);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OnDemandOptimization'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OnDemandOptimization'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FutureDays'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'FutureDays'), 405);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRates'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AgileRates'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndependentProducts'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IndependentProducts'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'SmallGroupPricing'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'SmallGroupPricing'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DataExtractionReport'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DataExtractionReport'), 1);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OperationsReport'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'OperationsReport'), 1);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PickupAndChangeReport'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PickupAndChangeReport'), 8);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PricingReport'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PricingReport'), 7);

-- Other
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DemandByOccDateAndWashView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DemandByOccDateAndWashView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomTypeOverrideTableView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'RoomTypeOverrideTableView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastGroupOverrideTableView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ForecastGroupOverrideTableView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductInvestigatorChartView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MultiProductInvestigatorChartView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductOverrideTabPlaceholderView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MultiProductOverrideTabPlaceholderView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MultiProductOverrideHistoryPlaceholderView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'MultiProductOverrideHistoryPlaceholderView'), null);

-- ST19 Configuration Param

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataFeed'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19DataFeed'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataExtraction'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19DataExtraction'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19AtAGlance'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19AtAGlance'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19BAD'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19BAD'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19DataFeedMSRT'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19DataFeedMSRT'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ST19RTDataByDBProc'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ST19RTDataByDBProc'), 0);

-- SFTP/FTP DeliveryForReports

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FTPDeliveryForReports'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'FTPDeliveryForReports'), 0);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'SFTPDeliveryForReports'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'SFTPDeliveryForReports'), 0);

-- Pricing Summary View

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'InvestigatorChartView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'InvestigatorChartView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'InvestigatorOverrideHistoryPlaceholderView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'InvestigatorOverrideHistoryPlaceholderView'), null);

-- Rooms Configuration Wizard
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CostOfWalkView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'CostOfWalkView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomTypeMappingView'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'RoomTypeMappingView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OverbookingView'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'OverbookingView'), null);

--Overbooking screen hide cow column

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OverbookingCostOfWalkOverrideValueColumn'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'OverbookingCostOfWalkOverrideValueColumn'), 1);

--Info manager notifications
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyStep'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'PropertyStep'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NotificationDetailsStep'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'NotificationDetailsStep'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ConditionStep'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'ConditionStep'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MonitoringWindowStep'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'MonitoringWindowStep'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionChangeEx'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'DecisionChangeEx'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorPriceChange'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'CompetitorPriceChange'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionAsOfLastNightlyOptimization'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'DecisionAsOfLastNightlyOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CompetitorPriceAsOfLastNightlyOptimization'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'CompetitorPriceAsOfLastNightlyOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionAsOfLastOptimization'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'DecisionAsOfLastOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionChangeExAsOfLastOptimization'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'DecisionChangeExAsOfLastOptimization'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_COMP_SUBTYPE'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_COMP_SUBTYPE'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING_BY_VALUE'))
insert into License_Feature_Value values	((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                             (select License_Feature_id from License_Feature where Feature_Code = 'NOTIFICATION_SUBTYPE_PRICING_BY_VALUE'), null);



-----------------------------------------------G3 Select ----------------------------------------------------------


-- Dashboard Screens
-- Business Analysis Dashboard
if not exists (select 1 from License_Feature where Feature_Code = 'BusinessAnalysisPaceDataView')
insert into License_Feature values('BusinessAnalysisPaceDataView','Pace Data',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);
if not exists (select 1 from License_Feature where Feature_Code = 'BusinessAnalysisPerformanceView')
insert into License_Feature values('BusinessAnalysisPerformanceView','Performance',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisPaceDataView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisPaceDataView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BusinessAnalysisPerformanceView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BusinessAnalysisPerformanceView'), null);


-- Business Insights Dashboard
if not exists (select 1 from License_Feature where Feature_Code = 'business-insights')
insert into License_Feature values('business-insights','Business Insights',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'business-insights'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'business-insights'), null);


-- Channel Forecast Dashboard
if not exists (select 1 from License_Feature where Feature_Code = 'channel-forecast-dashboard')
insert into License_Feature values('channel-forecast-dashboard','Channel Forecast',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'channel-forecast-dashboard'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'channel-forecast-dashboard'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ChannelForecast')
insert into License_Feature values('ChannelForecast','Channel Forecast Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.channelcost.forecastdashboardenabled',null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ChannelForecast'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ChannelForecast'), 1);



-- Monitor Screens
-- Benefits Measurement Dashboard --> pacman.feature.benefitMeasurementEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'benefit-measurement')
insert into License_Feature values('benefit-measurement','Benefit Measurement',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'benefit-measurement'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'benefit-measurement'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'BenefitMeasurement')
insert into License_Feature values('BenefitMeasurement','Benefit Measurement Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.benefitMeasurementEnabled',null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'BenefitMeasurement'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'BenefitMeasurement'), 1);


-- Information Manager -> Exceptions
if not exists (select 1 from License_Feature where Feature_Code = 'IMExceptionsView')
insert into License_Feature values('IMExceptionsView','Benefits Measurement',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IMExceptionsView'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IMExceptionsView'), null);

-- Arrival by LOS
if not exists (select 1 from License_Feature where Feature_Code = 'arrival-bylos-report')
insert into License_Feature values('arrival-bylos-report','Arrival by LOS',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'arrival-bylos-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'arrival-bylos-report'), null);


-- Arrival by LOS Remaining Demand -- pacman.demandAndWashManagement.UIExtendedStayEnabled

if not exists (select 1 from License_Feature where Feature_Code = 'arrival-by-los-remaining-demand-report')
insert into License_Feature values('arrival-by-los-remaining-demand-report','Arrival by LOS Remaining Demand',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'arrival-by-los-remaining-demand-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'arrival-by-los-remaining-demand-report'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'DemandAndWashManagementUIExtendedStayEnabled')
insert into License_Feature values('DemandAndWashManagementUIExtendedStayEnabled','DemandAndWashManagement UIExtendedStayEnabled Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.demandAndWashManagement.ExtendedStayEnabled',null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DemandAndWashManagementUIExtendedStayEnabled'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DemandAndWashManagementUIExtendedStayEnabled'), 0);


-- Booking Pace
if not exists (select 1 from License_Feature where Feature_Code = 'booking-pace-report')
insert into License_Feature values('booking-pace-report','Booking Pace',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'booking-pace-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'booking-pace-report'), null);

-- Booking Situation
if not exists (select 1 from License_Feature where Feature_Code = 'booking-situation-report')
insert into License_Feature values('booking-situation-report','Booking Situation',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'booking-situation-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'booking-situation-report'), null);


-- Comparative Booking Pace
if not exists (select 1 from License_Feature where Feature_Code = 'comparative-booking-pace-report')
insert into License_Feature values('comparative-booking-pace-report','Comparative Booking Pace',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'comparative-booking-pace-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'comparative-booking-pace-report'), null);


-- Decision Pace
if not exists (select 1 from License_Feature where Feature_Code = 'pricing-pace-report')
insert into License_Feature values('pricing-pace-report','Decision Pace',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-pace-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-pace-report'), null);


-- Input Override
if not exists (select 1 from License_Feature where Feature_Code = 'input-override-report')
insert into License_Feature values('input-override-report','Input Override',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'input-override-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'input-override-report'), null);


-- Individual Group Wash -- pacman.feature.IndividualGroupWashReportEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'individual-group-wash-report')
insert into License_Feature values('individual-group-wash-report','Individual Group Wash',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'individual-group-wash-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'individual-group-wash-report'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'IndividualGroupWash')
insert into License_Feature values('IndividualGroupWash','Individual Group Wash Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.IndividualGroupWashReportEnabled',null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndividualGroupWash'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'IndividualGroupWash'), 1);

-- Market Segment Mapping
if not exists (select 1 from License_Feature where Feature_Code = 'mcat-mapping-report')
insert into License_Feature values('mcat-mapping-report','Inventory History',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'mcat-mapping-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'mcat-mapping-report'), null);


-- Output Override
if not exists (select 1 from License_Feature where Feature_Code = 'output-override-report')
insert into License_Feature values('output-override-report','Output Override',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'output-override-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'output-override-report'), null);


-- Performance Comparison
if not exists (select 1 from License_Feature where Feature_Code = 'performance-comparison-report')
insert into License_Feature values('performance-comparison-report','Performance Comparison',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'performance-comparison-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'performance-comparison-report'), null);


-- Pricing Override History
if not exists (select 1 from License_Feature where Feature_Code = 'pricing-override-history-report')
insert into License_Feature values('pricing-override-history-report','Pricing Override History',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'pricing-override-history-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'pricing-override-history-report'), null);


-- Production - All Rate Codes
-- Is it "Production - All Rate Codes" OR "Production - All Rate Plans"  -- pacman.feature.AllSRPReportEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'rate-plan-report')
insert into License_Feature values('rate-plan-report','Production - All Rate Codes',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'rate-plan-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rate-plan-report'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'AllSRPReportEnabled')
insert into License_Feature values('AllSRPReportEnabled','All SRP Report Enabled Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.AllSRPReportEnabled',null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AllSRPReportEnabled'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AllSRPReportEnabled'), 1);

-- Property Attributes
if not exists (select 1 from License_Feature where Feature_Code = 'property-attribute-assignment-report')
insert into License_Feature values('property-attribute-assignment-report','Property Attributes',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-attribute-assignment-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'property-attribute-assignment-report'), null);


-- Rate Plan Production
if not exists (select 1 from License_Feature where Feature_Code = 'rate-plan-production-report')
insert into License_Feature values('rate-plan-production-report','Rate Plan Production',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'rate-plan-production-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'rate-plan-production-report'), null);


-- Restriction
if not exists (select 1 from License_Feature where Feature_Code = 'restriction-level-report')
insert into License_Feature values('restriction-level-report','Restriction',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'restriction-level-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'restriction-level-report'), null);


-- Special Events
if not exists (select 1 from License_Feature where Feature_Code = 'special-events-report')
insert into License_Feature values('special-events-report','Special Events',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'special-events-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'special-events-report'), null);


-- Straightline Availability
if not exists (select 1 from License_Feature where Feature_Code = 'sla-report')
insert into License_Feature values('sla-report','Straightline Availability',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'sla-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'sla-report'), null);


-- User Activity Log
if not exists (select 1 from License_Feature where Feature_Code = 'user-activity-log-report')
insert into License_Feature values('user-activity-log-report','User Activity Log',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'user-activity-log-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'user-activity-log-report'), null);


-- User Report
if not exists (select 1 from License_Feature where Feature_Code = 'user-report')
insert into License_Feature values('user-report','User Report',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'user-report'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'user-report'), null);


-- Category => Manage ->
-- Group Wash by Group -- pacman.feature.GroupWashByGroupEnabled


if not exists (select 1 from License_Feature where Feature_Code = 'group-wash')
insert into License_Feature values('group-wash','Group Wash by Group',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'group-wash'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'group-wash'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupWashMonthView')
insert into License_Feature values('GroupWashMonthView','By Occupancy Date',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupWashMonthView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupWashMonthView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'IndividualGroupsPlaceholderView')
insert into License_Feature values('IndividualGroupsPlaceholderView','Individual Groups',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndividualGroupsPlaceholderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'IndividualGroupsPlaceholderView'), null);



--Manage -> Out Of Override
if not exists (select 1 from License_Feature where Feature_Code = 'out-of-order-overrides')
insert into License_Feature values('out-of-order-overrides','Out of Order Overrides',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'out-of-order-overrides'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'out-of-order-overrides'), null);




-- Configuration -> Decisions -> Restrictions
if not exists (select 1 from License_Feature where Feature_Code = 'qualified-rate-plan-configuration')
insert into License_Feature values('qualified-rate-plan-configuration','Restriction Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'qualified-rate-plan-configuration'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'qualified-rate-plan-configuration'), null);
/*

if not exists (select 1 from License_Feature where Feature_Code = 'RateHeaderView')
insert into License_Feature values('RateHeaderView','Rate Headers',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateHeaderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RateHeaderView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'RateDetailsView')
insert into License_Feature values('RateDetailsView','Rate Details',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null);
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateDetailsView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RateDetailsView'), null);
*/

if not exists (select 1 from License_Feature where Feature_Code = 'RatePlanHeaderConfigurationView')
insert into License_Feature values('RatePlanHeaderConfigurationView','Rate Headers',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RatePlanHeaderConfigurationView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RatePlanHeaderConfigurationView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'RatePlanDetailsView')
insert into License_Feature values('RatePlanDetailsView','Rate Details',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RatePlanDetailsView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RatePlanDetailsView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'AgileRateRestrictionView')
insert into License_Feature values('AgileRateRestrictionView','',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRateRestrictionView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'AgileRateRestrictionView'), null);


-- Configuration -> Decisions -> Mass Restrictions
if not exists (select 1 from License_Feature where Feature_Code = 'mass-restriction-upload')
insert into License_Feature values('mass-restriction-upload','Mass Restriction Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'mass-restriction-upload'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'mass-restriction-upload'), null);

-- Configuration -> External Data -> Inventory Groups
if not exists (select 1 from License_Feature where Feature_Code = 'InventoryGroupsConfigurationView')
insert into License_Feature values('InventoryGroupsConfigurationView','Inventory Groups',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'InventoryGroupsConfigurationView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'InventoryGroupsConfigurationView'), null);

-- Configuration -> External Data -> Channel
if not exists (select 1 from License_Feature where Feature_Code = 'channel-costs')
insert into License_Feature values('channel-costs','Channel Costs',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'channel-costs'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'channel-costs'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'channel-costs-v3')
insert into License_Feature values('channel-costs-v3','Channel Costs V3',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'channel-costs-v3'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'channel-costs-v3'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ChannelCostsV3View')
insert into License_Feature values('ChannelCostsV3View','Channel Costs',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ChannelCostsV3View'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ChannelCostsV3View'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ForecastSettingsView')
insert into License_Feature values('ForecastSettingsView','Forecast Settings',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ForecastSettingsView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ForecastSettingsView'), null);


-- Configuration -> Forecast -> Special Event Upload
if not exists (select 1 from License_Feature where Feature_Code = 'special-event-upload')
insert into License_Feature values('special-event-upload','Special Event Upload',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'special-event-upload'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'special-event-upload'), null);

-- Configuration -> Rooms Configuration -> Wizard Steps

if not exists (select 1 from License_Feature where Feature_Code = 'PriceRankingAndUpgradeView')
insert into License_Feature values('PriceRankingAndUpgradeView','Price Ranking And Upgrade View',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PriceRankingAndUpgradeView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'PriceRankingAndUpgradeView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupPriceRankingView')
insert into License_Feature values('GroupPriceRankingView','Group Price Ranking View',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPriceRankingView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupPriceRankingView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'MinimumPriceDifferentialView')
insert into License_Feature values('MinimumPriceDifferentialView','Minimum Price Differential View',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MinimumPriceDifferentialView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MinimumPriceDifferentialView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'RoomClassRatioConfigView')
insert into License_Feature values('RoomClassRatioConfigView','Room Class Ratio Config View',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RoomClassRatioConfigView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RoomClassRatioConfigView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'DisplayPreferencesView')
insert into License_Feature values('DisplayPreferencesView','Display Preferences View',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DisplayPreferencesView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'DisplayPreferencesView'), null);





-- Configure -> Property -> Property Attribute Assignment

if not exists (select 1 from License_Feature where Feature_Code = 'property-attribute-assignments')
insert into License_Feature values('property-attribute-assignments','Property Attribute Assignments',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-attribute-assignments'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'property-attribute-assignments'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'PropertyTabView')
insert into License_Feature values('PropertyTabView','',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'PropertyTabView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'PropertyTabView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'FilteredPropertyTabView')
insert into License_Feature values('FilteredPropertyTabView','',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'FilteredPropertyTabView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'FilteredPropertyTabView'), null);

-- Configure -> Property -> Property Attribute
if not exists (select 1 from License_Feature where Feature_Code = 'property-attributes')
insert into License_Feature values('property-attributes','Property Attributes',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'property-attributes'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'property-attributes'), null);


-- Configure -> Property -> Property Specific Configuration
if not exists (select 1 from License_Feature where Feature_Code = 'OptimizationSettingsView')
insert into License_Feature values('OptimizationSettingsView','Optimization Settings',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'OptimizationSettingsView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'OptimizationSettingsView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'HeatMapSettingsView')
insert into License_Feature values('HeatMapSettingsView','Heat Map',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'HeatMapSettingsView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'HeatMapSettingsView'), null);


-- Configuration -> Configuration Manager --> pacman.preproduction.isConfigurationManagerEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'configuration-manager')
insert into License_Feature values('configuration-manager','Configuration Manager',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'configuration-manager'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'configuration-manager'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ConfigurationManager')
insert into License_Feature values('ConfigurationManager','Configuration Manager Feature Parameter',
                                   (select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.preproduction.isConfigurationManagerEnabled',null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ConfigurationManager'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'ConfigurationManager'), 1);


-- Decision Configuration -->
if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'decision-configuration'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName)
                                         ,(select License_Feature_id from License_Feature where Feature_Code = 'decision-configuration'), null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DecisionConfiguration'))
insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'DecisionConfiguration'), 1);

--Advanced Pricing Configuration
if not exists (select 1 from License_Feature where Feature_Code = 'AdvancedPricingConfiguration')
insert into License_Feature values('AdvancedPricingConfiguration','Advanced Pricing Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),null, null);
if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AdvancedPricingConfiguration'))insert into License_Feature_Value values
    ((select License_Package_Id from License_Package where Package_Name = @PackageName),
     (select License_Feature_id from License_Feature where Feature_Code = 'AdvancedPricingConfiguration'), 1);

--Group Pricing Evaluation
if not exists (select 1 from License_Feature where Feature_Code = 'GroupPricing')
insert into License_Feature values('GroupPricing','GroupPricing',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.GroupPricingEnabled',null);
if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPricing'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),
                                          (select License_Feature_id from License_Feature where Feature_Code = 'GroupPricing'), 1);

if not exists (select 1 from License_Feature where Feature_Code = 'group-pricing-evaluation')
insert into License_Feature values('group-pricing-evaluation','Group Pricing Evaluation',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'group-pricing-evaluation'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'group-pricing-evaluation'), null);

--Group Pricing Configuration
if not exists (select 1 from License_Feature where Feature_Code = 'group-pricing-configuration')
insert into License_Feature values('group-pricing-configuration','Group Pricing Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Page'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'group-pricing-configuration'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'group-pricing-configuration'), 1);

if not exists (select 1 from License_Feature where Feature_Code = 'CeilingFloorGroupView')
insert into License_Feature values('CeilingFloorGroupView','Ceiling/Floor',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CeilingFloorGroupView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'CeilingFloorGroupView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupPricingGeneralConfigurationView')
insert into License_Feature values('GroupPricingGeneralConfigurationView','General Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPricingGeneralConfigurationView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupPricingGeneralConfigurationView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupPricingConferenceBanquetView')
insert into License_Feature values('GroupPricingConferenceBanquetView','Conference & Banquet',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPricingConferenceBanquetView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupPricingConferenceBanquetView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupPricingAncillaryView')
insert into License_Feature values('GroupPricingAncillaryView','Ancillary',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPricingAncillaryView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupPricingAncillaryView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'GroupPricingAccomTypeConfigurationView')
insert into License_Feature values('GroupPricingAccomTypeConfigurationView','General Configuration',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupPricingAccomTypeConfigurationView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupPricingAccomTypeConfigurationView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'MinimumProfitMarginView')
insert into License_Feature values('MinimumProfitMarginView','Minimum Profit Percentage',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null,null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MinimumProfitMarginView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'MinimumProfitMarginView'), null);

--insert into License_Feature values('RateCodeView','Rate Code Level',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);
--insert into License_Feature values('GroupView','Group Business Level',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateCodeView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'RateCodeView'), null);

if not exists ( select 1 from License_Feature_Value where
 License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
 License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'RateCodeView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupView'), null);

--pacman.core.LimitedDataBuildEnabled = true
if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'), null);

if not exists ( select 1 from License_Feature_Value where
License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ProjectionsDownloaderView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'ProjectionsDownloaderView'), null);

if not exists (select 1 from License_Feature where Feature_Code = 'ActiveG3LinkedProducts')
insert into License_Feature values('ActiveG3LinkedProducts','G3 Active Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxActiveAgileRates', null);
if not exists (select 1 from License_Feature where Feature_Code = 'MaxUploadLinkedProducts')
insert into License_Feature values('MaxUploadLinkedProducts','3rd Party Upload Linked Products',(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.agilerates.MaxUploadedAgileRates', null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'ActiveG3LinkedProducts'), 0);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName), (select License_Feature_id from License_Feature where Feature_Code = 'MaxUploadLinkedProducts'), 0);

-- AgileRatesAgeBasedPricingStep
if not exists (select 1 from License_Feature where Feature_Code = 'AgileRatesAgeBasedPricingStep')
insert into License_Feature values('AgileRatesAgeBasedPricingStep','Add linked product - AgeBasedPricing step',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRatesAgeBasedPricingStep'))
insert into License_Feature_Value values
((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'AgileRatesAgeBasedPricingStep'), null);

-- AgileRatesDefaultsStep
if not exists (select 1 from License_Feature where Feature_Code = 'AgileRatesDefaultsStep')
insert into License_Feature values('AgileRatesDefaultsStep','Add linked product - Defaults step',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRatesDefaultsStep'))
insert into License_Feature_Value values
((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'AgileRatesDefaultsStep'), null);

-- AgileRatesDefinitionStep
if not exists (select 1 from License_Feature where Feature_Code = 'AgileRatesDefinitionStep')
insert into License_Feature values('AgileRatesDefinitionStep','Add linked product - definition step',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRatesDefinitionStep'))
insert into License_Feature_Value values
((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'AgileRatesDefinitionStep'), null);

-- AgileRatesRestrictionsStep
if not exists (select 1 from License_Feature where Feature_Code = 'AgileRatesRestrictionsStep')
insert into License_Feature values('AgileRatesRestrictionsStep','Add linked product - Restriction step',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRatesRestrictionsStep'))
insert into License_Feature_Value values
((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'AgileRatesRestrictionsStep'), null);

-- AgileRatesSeasonsStep
if not exists (select 1 from License_Feature where Feature_Code = 'AgileRatesSeasonsStep')
insert into License_Feature values('AgileRatesSeasonsStep','Add linked product - Seasons step',(select License_Feature_type_id from License_Feature_type where Name = 'WizardStep'), null, null);

if not exists ( select 1 from License_Feature_Value where
    License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
    License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'AgileRatesSeasonsStep'))
insert into License_Feature_Value values
((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'AgileRatesSeasonsStep'), null);

-- GroupViewpacman.feature.MarketPerformanceEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'GroupView')
insert into License_Feature values('GroupView','Group Business Level',(select License_Feature_type_id from License_Feature_type where Name = 'Tab'),null, null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'GroupView'))
insert into License_Feature_Value values ((select License_Package_Id from License_Package where Package_Name = @PackageName),(select License_Feature_id from License_Feature where Feature_Code = 'GroupView'), null);

-- CeilingOverrideEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'CeilingOverrideEnabled')
insert into License_Feature values('CeilingOverrideEnabled','Ceiling Override Enabled',
(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.CeilingOverrideEnabled', null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'CeilingOverrideEnabled'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'CeilingOverrideEnabled'), 1);

-- DailyBarConfigurationEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'DailyBarConfigurationEnabled')
insert into License_Feature values('DailyBarConfigurationEnabled','Daily Bar Configuration Enabled',
(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.DailyBarConfigurationEnabled', null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'DailyBarConfigurationEnabled'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'DailyBarConfigurationEnabled'), 0);

-- EnableSingleBarDecision
if not exists (select 1 from License_Feature where Feature_Code = 'EnableSingleBarDecision')
insert into License_Feature values('EnableSingleBarDecision','Decision Configuration',
(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.bar.EnableSingleBarDecision', null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'EnableSingleBarDecision'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'EnableSingleBarDecision'), 0);

-- MassBarConfigurationEnabled
if not exists (select 1 from License_Feature where Feature_Code = 'MassBarConfigurationEnabled')
insert into License_Feature values('MassBarConfigurationEnabled','Mass Bar Configuration Enabled',
(select License_Feature_type_id from License_Feature_type where Name = 'Feature'),'pacman.feature.MassBarConfigurationEnabled', null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'MassBarConfigurationEnabled'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'MassBarConfigurationEnabled'), 0);

-- IndividualGroupWashByNameView
if not exists (select 1 from License_Feature where Feature_Code = 'IndividualGroupWashByNameView')
insert into License_Feature values('IndividualGroupWashByNameView','Individual Group Wash By Name View',
(select License_Feature_type_id from License_Feature_type where Name = 'Tab'), null, null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'IndividualGroupWashByNameView'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'IndividualGroupWashByNameView'), null);

-- LimitedDataBuildMarketSegmentView
if not exists (select 1 from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView')
insert into License_Feature values('LimitedDataBuildMarketSegmentView','Limited Data Build Market Segment View',
(select License_Feature_type_id from License_Feature_type where Name = 'Tab'), null, null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'LimitedDataBuildMarketSegmentView'), null);

-- LOSDemandOverrideView
if not exists (select 1 from License_Feature where Feature_Code = 'LOSDemandOverrideView')
insert into License_Feature values('LOSDemandOverrideView','LOS Demand Override View',
(select License_Feature_type_id from License_Feature_type where Name = 'Tab'), null, null);

if not exists ( select 1 from License_Feature_Value where
      License_Package_Id = (select License_Package_Id from License_Package where Package_Name = @PackageName) and
      License_Feature_Id = (select License_Feature_Id from License_Feature where Feature_Code = 'LOSDemandOverrideView'))
insert into License_Feature_Value values
			((select License_Package_Id from License_Package where Package_Name = @PackageName),
		(select License_Feature_id from License_Feature where Feature_Code = 'LOSDemandOverrideView'), null);

END;