BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'Opera Data Extract Creation Issue'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'Opera Data Extract Creation Issue',
           '<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Hello <span style="color: rgb(31, 73, 125);">$firstName</span>,
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Please be informed that we are currently facing a data extract creation issue for the property, <span style="color: rgb(31, 73, 125);">$propertyName.</span>.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    The error indicates that we could not connect to the Oracle Database and the possible reasons could be any of the following:
</p>
<ol style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li><strong>Network Issues -</strong> Network connection issues with the Database Host IP Address []</li>
    <li><strong>Oracle database login problems -</strong> Oracle Database user/password has changed/expired.</li>
    <li><strong>Opera PMS login -</strong> Opera PMS User credentials have changed/expired [Default User - IDEAS]</li>
</ol>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    **If the IDeaS Opera agent is installed in the Oracle Data Centre or Cloud environment, may we request your valuable assistance to consult &amp; open a ticket with the Oracle support team to restart the service. Please request your Oracle Support representative to follow the appropriate steps to help expedite the resolution of this issue.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong>If your IDeaS Opera agent is installed on a local machine, please follow the steps specified below to help expedite the resolution of this issue.</strong> 
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thus, we request you to review the above settings. After reviewing, below are the action items you could take.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong>Action item:</strong> Details to be re-entered in IDeaS Agent setup configuration. Please follow the steps below:
</p>
<ol style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>In your C: Drive, of Agent machine, you will find a folder for IDeaS G3. Please open <strong>"IdeasOperaAgentAdminPage"</strong>.</li>
    <li>Go to the <strong>"G3 Opera Agent"</strong> Screen and click on the <strong>"Property Settings"</strong>link.
        <ul>
            <li><strong>Username</strong> = <i>ideasadmin</i> / <strong>Password</strong> = <i>IDeaS123</i></li>
        </ul>
    </li>
    <li>The properties which are added to the remote agent will appear in the top left corner of the screen.</li>
    <li>Click on the respective property name -> Make the necessary changes in the configuration.</li>
    <li>Click on Save and then click on Test PMS Connection. It should be successful.</li>
    <li>Post completion of the above steps, request you to provide us the screenshot of the “Success” message.</li>
</ol>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong><u>Any other reason for failure</u> – </strong>Should there be any other reason apart from the stated above, please do mention it to us via the client portal or an email.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    We look forward to hearing from you soon.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thank you for your cooperation. Please do contact us for any further queries.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Following are our contact details:
</p>
<ul style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>IDeaS Client portal</li>
    <li>Email us at <a href="mailto:<EMAIL>" style="color: rgb(149, 79, 114); text-decoration: underline;"><EMAIL></a> (addressing the CARE team)</li>
</ul>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thanks and regards,<br> 
    IDeaS Support
</p>
<p style="font-family: Arial, sans-serif; font-size: 8pt; color: rgb(129, 129, 129);">
    This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.
</p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Hello <span style="color: rgb(31, 73, 125);">$firstName</span>,
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Please be informed that we are currently facing a data extract creation issue for the property, <span style="color: rgb(31, 73, 125);">$propertyName.</span>.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    The error indicates that we could not connect to the Oracle Database and the possible reasons could be any of the following:
</p>
<ol style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li><strong>Network Issues -</strong> Network connection issues with the Database Host IP Address []</li>
    <li><strong>Oracle database login problems -</strong> Oracle Database user/password has changed/expired.</li>
    <li><strong>Opera PMS login -</strong> Opera PMS User credentials have changed/expired [Default User - IDEAS]</li>
</ol>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    **If the IDeaS Opera agent is installed in the Oracle Data Centre or Cloud environment, may we request your valuable assistance to consult &amp; open a ticket with the Oracle support team to restart the service. Please request your Oracle Support representative to follow the appropriate steps to help expedite the resolution of this issue.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong>If your IDeaS Opera agent is installed on a local machine, please follow the steps specified below to help expedite the resolution of this issue.</strong> 
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thus, we request you to review the above settings. After reviewing, below are the action items you could take.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong>Action item:</strong> Details to be re-entered in IDeaS Agent setup configuration. Please follow the steps below:
</p>
<ol style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>In your C: Drive, of Agent machine, you will find a folder for IDeaS G3. Please open <strong>"IdeasOperaAgentAdminPage"</strong>.</li>
    <li>Go to the <strong>"G3 Opera Agent"</strong> Screen and click on the <strong>"Property Settings"</strong>link.
        <ul>
            <li><strong>Username</strong> = <i>ideasadmin</i> / <strong>Password</strong> = <i>IDeaS123</i></li>
        </ul>
    </li>
    <li>The properties which are added to the remote agent will appear in the top left corner of the screen.</li>
    <li>Click on the respective property name -> Make the necessary changes in the configuration.</li>
    <li>Click on Save and then click on Test PMS Connection. It should be successful.</li>
    <li>Post completion of the above steps, request you to provide us the screenshot of the “Success” message.</li>
</ol>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <strong><u>Any other reason for failure</u> – </strong>Should there be any other reason apart from the stated above, please do mention it to us via the client portal or an email.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    We look forward to hearing from you soon.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thank you for your cooperation. Please do contact us for any further queries.
</p>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Following are our contact details:
</p>
<ul style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>IDeaS Client portal</li>
    <li>Email us at <a href="mailto:<EMAIL>" style="color: rgb(149, 79, 114); text-decoration: underline;"><EMAIL></a> (addressing the CARE team)</li>
</ul>
<p style="font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    Thanks and regards,<br> 
    IDeaS Support
</p>
<p style="font-family: Arial, sans-serif; font-size: 8pt; color: rgb(129, 129, 129);">
    This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.
</p>
'
WHERE Template_Name = 'Opera Data Extract Creation Issue' AND Client_Id IS NULL;
END
END

BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'Processing Delay'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'Processing Delay',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We wanted to inform you that today’s <strong>&lt;BDE/IDP&gt;</strong> processing for your property is experiencing a delay. Rest assured, we are actively investigating the issue and will keep you updated as soon as we have more information.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your understanding. If you have any questions, please do not hesitate to reach out.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We wanted to inform you that today’s <strong>&lt;BDE/IDP&gt;</strong> processing for your property is experiencing a delay. Rest assured, we are actively investigating the issue and will keep you updated as soon as we have more information.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your understanding. If you have any questions, please do not hesitate to reach out.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'Processing Delay' AND Client_Id IS NULL;
END
END

BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'Slow Response OXI Decision Upload Issue'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'Slow Response OXI Decision Upload Issue',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing an issue while uploading &lt;Decision Type&gt; decisions to OXI for the property.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are receiving slow responses while uploading decisions to the below URL:<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL&gt;<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Can you please investigate this issue and provide us with an update?<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Additionally, please ensure that the OXI interface/Message queue is functioning correctly.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Once this has been verified, we can proceed with reinstating the decision upload and informing you of the outcome.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be aware that this may affect any subsequent processing or manual uploads.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please contact us if you have any questions.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing an issue while uploading &lt;Decision Type&gt; decisions to OXI for the property.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are receiving slow responses while uploading decisions to the below URL:<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL&gt;<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Can you please investigate this issue and provide us with an update?<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Additionally, please ensure that the OXI interface/Message queue is functioning correctly.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Once this has been verified, we can proceed with reinstating the decision upload and informing you of the outcome.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be aware that this may affect any subsequent processing or manual uploads.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please contact us if you have any questions.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'Slow Response OXI Decision Upload Issue' AND Client_Id IS NULL;
END
END


BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'No Response OXI Decision Upload Issue'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'No Response OXI Decision Upload Issue',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing an issue while uploading &lt;Decision Type&gt; decisions to OXI for the property.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are not receiving any acknowledgement response while uploading decisions to the below URL:<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL&gt;<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Can you please investigate this issue and provide us with an update?<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Additionally, please ensure that the OXI interface/Message queue is functioning correctly.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Once this has been verified, we can proceed with reinstating the decision upload and informing you of the outcome.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be aware that this may affect any subsequent processing or manual uploads.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please contact us if you have any questions.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing an issue while uploading &lt;Decision Type&gt; decisions to OXI for the property.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are not receiving any acknowledgement response while uploading decisions to the below URL:<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL&gt;<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Can you please investigate this issue and provide us with an update?<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Additionally, please ensure that the OXI interface/Message queue is functioning correctly.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Once this has been verified, we can proceed with reinstating the decision upload and informing you of the outcome.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be aware that this may affect any subsequent processing or manual uploads.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please contact us if you have any questions.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'No Response OXI Decision Upload Issue' AND Client_Id IS NULL;
END
END

BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'OXI Decision Upload Expiration'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'OXI Decision Upload Expiration',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing issues while uploading decisions to OXI for the property.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We have reached the maximum wait threshold; however, we have not yet received the success/error ack status of the decisions which we have sent.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are sending decisions to the below URL:<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL Name&gt;<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Could you please investigate this and update us accordingly?<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Also, please check if the above URL is working and ready to accept decisions from IDeaS.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We could then re-instate the decision upload and update you on the same.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your co-operation. Please contact us if you have any questions.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content =  '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">This is to inform you that we are facing issues while uploading decisions to OXI for the property.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We have reached the maximum wait threshold; however, we have not yet received the success/error ack status of the decisions which we have sent.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We are sending decisions to the below URL:<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;URL Name&gt;<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Could you please investigate this and update us accordingly?<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Also, please check if the above URL is working and ready to accept decisions from IDeaS.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We could then re-instate the decision upload and update you on the same.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your co-operation. Please contact us if you have any questions.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'OXI Decision Upload Expiration' AND Client_Id IS NULL;
END
END

BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'Generic Decision Upload Delay'
		  AND Client_Id IS NULL
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'Generic Decision Upload Delay',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We wanted to bring to your attention that there will be a delay in the<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;BDE/IDP1/IDP2/...&gt; &lt;Outbound name&gt; decision upload for the property today.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Our team is currently conducting a thorough investigation into the issue. Rest assured, we understand the importance of this matter and are working diligently to resolve it.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your understanding, and please feel free to reach out if you have any questions or require additional information.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           NULL
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We wanted to bring to your attention that there will be a delay in the<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">&lt;BDE/IDP1/IDP2/...&gt; &lt;Outbound name&gt; decision upload for the property today.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Our team is currently conducting a thorough investigation into the issue. Rest assured, we understand the importance of this matter and are working diligently to resolve it.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your understanding, and please feel free to reach out if you have any questions or require additional information.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'Generic Decision Upload Delay' AND Client_Id IS NULL;
END
END


BEGIN
    IF EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Name = 'Inventory Snapshot – Not Received'
		AND Client_Id IS NULL
    )
BEGIN
UPDATE Sfdc_Email_Template SET Template_Name = 'Inventory Snapshot (RTAV) – Not Received', Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello $firstName,<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be informed you that we have not received the Inventory Snapshot messages for the property $propertyName.<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Could you please confirm whether the Inventory Snapshot messages for today have been sent successfully? You may need to reach out to Oracle Support to assist in delivering the Inventory Snapshot messages successfully.<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We look forward to hearing from you soon.<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thank you for your co-operation. Please do contact us for any further queries.<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p><p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p><p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(149, 79, 114); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and Regards,<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p><p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'Inventory Snapshot – Not Received' AND Client_Id IS NULL;
END
END

BEGIN
    IF EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Name = 'Incomplete Inventory Snapshot'
		AND Client_Id IS NULL
    )
BEGIN
UPDATE Sfdc_Email_Template SET Template_Name = 'Incomplete Inventory Snapshot (RTAV)' WHERE Template_Name = 'Incomplete Inventory Snapshot' AND Client_Id IS NULL;
END
END

DECLARE @ClientID AS INT = (SELECT Client_ID FROM Client WHERE Client_Code = 'Hilton');
IF @ClientID IS NOT NULL
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'BDE Inventory Not Received'
		  AND Client_Id = @ClientID
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'BDE Inventory Not Received',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello GRO Support,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Greetings from IDeaS!<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be informed that we have not received the BDE Inventory messages for the property below:<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Property Code: $propertyCode<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Property Name: $propertyName<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Could you please review and send the BDE Inventory messages and update us?<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We look forward to hearing from you.<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
           <p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
           <p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           @ClientID
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Hello GRO Support,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Greetings from IDeaS!<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Please be informed that we have not received the BDE Inventory messages for the property below:<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Property Code: $propertyCode<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Property Name: $propertyName<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Could you please review and send the BDE Inventory messages and update us?<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">We look forward to hearing from you.<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Following are our contact details:<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpFirst" style="margin: 0in 0in 0.0001pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">IDeaS Client portal<o:p></o:p></span></p>
			<p class="MsoListParagraphCxSpMiddle" style="margin: 0in 0in 8pt 0.5in; line-height: 11.75pt; font-size: 11pt; font-family: Calibri, sans-serif; text-indent: -0.25in;"><span lang="EN-GB" style="font-family: Symbol; color: rgb(31, 73, 125);">·<span style="font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 7pt; line-height: normal; font-family: &quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span lang="EN-GB" style="color: rgb(31, 73, 125);">Email us at <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;"><EMAIL></a> (addressing the CARE team)<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">Thanks and regards,<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="color: rgb(31, 73, 125);">IDeaS Support<o:p></o:p></span></p>
			<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;"><span style="font-size: 8pt; line-height: 11.3067px; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.</span></p>'
WHERE Template_Name = 'BDE Inventory Not Received' AND Client_Id = @ClientID;
END
END

BEGIN
    IF EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = '0 rates against LV0'
		  AND Client_Id = @ClientID
    )
BEGIN
UPDATE Sfdc_Email_Template SET Template_Name = 'zero rates against LV0' WHERE Template_Name = '0 rates against LV0' AND Client_Id = @ClientID;
END
END

DECLARE @ClientId_Hyatt AS INT = (SELECT Client_ID FROM Client WHERE Client_Code = 'Hyatt');
IF @ClientId_Hyatt IS NOT NULL
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM Sfdc_Email_Template
        WHERE Template_Category = 'Problem'
          AND Template_Name = 'Extract Creation Issue'
		  AND Client_Id = @ClientId_Hyatt
    )
BEGIN
INSERT INTO Sfdc_Email_Template (
    Template_Category,
    Template_Name,
    Template_Content,
    Created_DTTM,
    Created_By_User_Id,
    Last_Updated_DTTM,
    Last_Updated_By_User_Id,
    Client_Id
)
VALUES (
           'Problem',
           'Extract Creation Issue',
           '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       Hello <span style="color: rgb(31, 73, 125);">$firstName</span>,
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       We hope this message finds you well!
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       We are currently experiencing an issue with data extract creation for <span style="color: rgb(31, 73, 125);">$propertyName</span>.
       The error suggests a connection problem with the Oracle Database, which could be due to:
   </span>
</p>
<ol style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
   <li><strong>Network Issues</strong> - Connectivity problems with the Database Host IP Address []</li>
   <li><strong>Oracle Database Login Issues</strong> - The database user/password may have changed or expired.</li>
   <li><strong>Opera PMS Login Issues</strong> - The Opera PMS user credentials may have changed or expired (Default User: IDEAS).</li>
</ol>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
      <strong> If your IDeaS Opera agent is in the Oracle Data Centre or Cloud environment</strong>, could you please open a ticket with Oracle Support to follow the appropriate steps? Your assistance will help expedite the resolution.
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       <strong>If your IDeaS Opera agent is on a local machine</strong>, please review the following settings:
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       <strong>Action Items:</strong>
   </span>
</p>
<ol style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
   <li>Navigate to the <strong>C: Drive</strong> on the Agent machine and open the <strong>IDeaS G3</strong> folder. Launch <strong>IdeasOperaAgentAdminPage</strong>.</li>
   <li>Go to the <strong>G3 Opera Agent</strong> screen and click on <strong>Property Settings</strong>.</li>
   <ul style="margin-left: 10px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
       <li>Username: ideasadmin</li>
       <li>Password: IDeaS123</li>
   </ul>
   <li>Select the respective property name in the top left corner and make the necessary changes to the configuration.</li>
   <li>Click <strong>Save</strong>, then <strong>Test PMS Connection</strong>. It should succeed.</li>
   <li>After completing these steps, please send us a screenshot of the success message.</li>
</ol>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       If there are any other issues or reasons for the failure, feel free to reach out via the client portal or email us directly.
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       We look forward to your prompt response!
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       Following are our contact details:
   </span>
</p>
<ul style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
   <li>IDeaS Client portal</li>
   <li>
       Email us at
       <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;">
           <EMAIL>
       </a>
       (addressing the CARE team)
   </li>
</ul>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       Thanks and regards,
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
   <span style="color: rgb(31, 73, 125);">
       IDeaS Support
   </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 8pt; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">
   <span>
       This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.
   </span>
</p>',
           GETDATE(),
           11403,
           GETDATE(),
           11403,
           @ClientId_Hyatt
       );
END
ELSE
BEGIN
UPDATE Sfdc_Email_Template SET Template_Content = '<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        Hello <span style="color: rgb(31, 73, 125);">$firstName</span>,
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        We hope this message finds you well!
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        We are currently experiencing an issue with data extract creation for <span style="color: rgb(31, 73, 125);">$propertyName</span>.
        The error suggests a connection problem with the Oracle Database, which could be due to:
    </span>
</p>
<ol style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li><strong>Network Issues</strong> - Connectivity problems with the Database Host IP Address []</li>
    <li><strong>Oracle Database Login Issues</strong> - The database user/password may have changed or expired.</li>
    <li><strong>Opera PMS Login Issues</strong> - The Opera PMS user credentials may have changed or expired (Default User: IDEAS).</li>
</ol>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
       <strong> If your IDeaS Opera agent is in the Oracle Data Centre or Cloud environment</strong>, could you please open a ticket with Oracle Support to follow the appropriate steps? Your assistance will help expedite the resolution.
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        <strong>If your IDeaS Opera agent is on a local machine</strong>, please review the following settings:
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        <strong>Action Items:</strong>
    </span>
</p>
<ol style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>Navigate to the <strong>C: Drive</strong> on the Agent machine and open the <strong>IDeaS G3</strong> folder. Launch <strong>IdeasOperaAgentAdminPage</strong>.</li>
    <li>Go to the <strong>G3 Opera Agent</strong> screen and click on <strong>Property Settings</strong>.</li>
    <ul style="margin-left: 10px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
        <li>Username: ideasadmin</li>
        <li>Password: IDeaS123</li>
    </ul>
    <li>Select the respective property name in the top left corner and make the necessary changes to the configuration.</li>
    <li>Click <strong>Save</strong>, then <strong>Test PMS Connection</strong>. It should succeed.</li>
    <li>After completing these steps, please send us a screenshot of the success message.</li>
</ol>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        If there are any other issues or reasons for the failure, feel free to reach out via the client portal or email us directly.
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        We look forward to your prompt response!
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        Following are our contact details:
    </span>
</p>
<ul style="margin-left: 20px; font-family: Calibri, sans-serif; font-size: 11pt; color: rgb(31, 73, 125);">
    <li>IDeaS Client portal</li>
    <li>
        Email us at
        <a href="mailto:<EMAIL>" style="color: rgb(5, 99, 193); text-decoration-line: underline;">
            <EMAIL>
        </a>
        (addressing the CARE team)
    </li>
</ul>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        Thanks and regards,
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 11pt; font-family: Calibri, sans-serif;">
    <span style="color: rgb(31, 73, 125);">
        IDeaS Support
    </span>
</p>
<p class="MsoNormal" style="margin: 0in 0in 8pt; line-height: 15.5467px; font-size: 8pt; font-family: Arial, sans-serif; color: rgb(129, 129, 129);">
    <span>
        This message and any attachments contain information that may be confidential and privileged. Unless you are the addressee (or authorized to receive for the addressee), you may not use, copy, print or disclose to anyone the message or any information contained in the message. If you have received this e-mail in error, please advise the sender by reply and delete the message and any attachments. Thank you.
    </span>
</p>'
WHERE Template_Name = 'Extract Creation Issue' AND Client_Id = @ClientId_Hyatt;
END
END