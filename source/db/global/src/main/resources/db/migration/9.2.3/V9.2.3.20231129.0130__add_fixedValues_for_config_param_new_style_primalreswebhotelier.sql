IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'primalRESWebHotelier'
                AND CPC.Category_Name = 'Outbound Connection Details')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Connection Details'),
                'primalRESWebHotelier', N'Outbound primalRESWebHotelier Conn Details group', getdate(), getdate())
END


IF NOT EXISTS(SELECT 1
              FROM [DBO].[Config_Parameter_Group] CPG
                       JOIN [dbo].[Config_Parameter_Category] CPC ON CPC.Category_ID = CPG.Category_ID
              WHERE CPG.Group_Name = 'primalRESWebHotelier'
                AND CPC.Category_Name = 'Outbound Data')
BEGIN
        INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
        VALUES ((select top 1 [Category_ID]
                 from [dbo].[Config_Parameter_Category]
                 where Category_Name = 'Outbound Data'),
                'primalRESWebHotelier', N'Outbound primalRESWebHotelier group', getdate(), getdate())
END

--1. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DailyBAR.AllMessagesSentAsDelta') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.AllMessagesSentAsDelta'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--2. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name =
    'pacman.integration.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'DailyBarSelectiveUpload')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--3. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.UploadAdultsBeyond2') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.UploadAdultsBeyond2'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--4. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.UploadChildAgeBuckets') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.UploadChildAgeBuckets'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--5. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.UploadChildrenBeyondExtra') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.UploadChildrenBeyondExtra'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--6. DailyBAR.dailybarRateCode
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.DailyBAR.dailybarRateCode') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.DailyBAR.dailybarRateCode'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--7. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.DailyBAR.dailybarRoundingPrecision') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.dailybarRoundingPrecision'),
    'primalreswebhotelier', '2', NULL,
    GETDATE(),  GETDATE())
END

--8. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DailyBAR.miscAdjustment') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.miscAdjustment'),
    'primalreswebhotelier', '0', NULL,
    GETDATE(),  GETDATE())
END

--9. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DailyBAR.newDailyBARFormat') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.newDailyBARFormat'),
    'primalreswebhotelier', NULL, (select [Config_Parameter_Predefined_Value_ID]
    from [dbo].[Config_Parameter_Predefined_Value]
    where value = 'true'),
    GETDATE(),  GETDATE())
END

--10. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.optimizedDailyBarAgileRateUpload') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.optimizedDailyBarAgileRateUpload'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--11. DailyBAR.taxAdjustmentValue
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.DailyBAR.taxAdjustmentValue') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.DailyBAR.taxAdjustmentValue'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--12. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DailyBAR.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--13. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.AgileRates.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.AgileRates.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--14. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DailyBAR.useDeltaForDifferential') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DailyBAR.useDeltaForDifferential'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--15. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.useYieldCurrencyForDailyBar') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.useYieldCurrencyForDailyBar'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--16. UploadCurrencyForDailyBAR
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.UploadCurrencyForDailyBAR') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.UploadCurrencyForDailyBAR'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--17. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.BarByLOS.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.BarByLOS.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--18. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.BARByLOSByRoomType.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.BARByLOSByRoomType.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--19. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.Fplos.fplosAtRoomCategory') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.Fplos.fplosAtRoomCategory'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--20. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.BarFplosByRoomType.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.BarFplosByRoomType.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--21. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.Fplos.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.Fplos.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--22. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.LRAControlFPLOS.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRAControlFPLOS.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--23. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.LRAControlMinLOS.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRAControlMinLOS.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--24. UploadCurrencyForLRV
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.UploadCurrencyForLRV') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.UploadCurrencyForLRV'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--25. LRVatRoomClass.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.LRVatRoomClass.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomClass.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--26. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomClass.includeCeilingDeltaMaxSold'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--27. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomClass.consolidateCeilingDelta'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'lrvConsolidationType')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--28. LRVatRoomClass.roundingPrecision
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.LRVatRoomClass.roundingPrecision') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomClass.roundingPrecision'),
--     'primalreswebhotelier', '2', NULL,
--     GETDATE(),  GETDATE())
-- END

--29. LRVatRoomClass.taxAdjustmentValue
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name =
--                                                     'pacman.integration.LRVatRoomClass.taxAdjustmentValue') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomClass.taxAdjustmentValue'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--30. SendRoomTypesAsRoomClassLRV
if not exists(select *
             from [dbo].[Config_Parameter_Value]
             where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                             from [dbo].[Config_Parameter]
                                             where name =
                                                   'pacman.integration.SendRoomTypesAsRoomClassLRV') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.SendRoomTypesAsRoomClassLRV'),
    'primalreswebhotelier', 'false', (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--31. LRVatRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.LRVatRoomType.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomType.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--32. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.LRVatRoomType.consolidateCeilingDelta') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomType.consolidateCeilingDelta'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'lrvConsolidationType')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--33. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.LRVatRoomType.includeCeilingDeltaMaxSold '),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--34. LRVatRoomType.roundingPrecision
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.LRVatRoomType.roundingPrecision') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomType.roundingPrecision'),
--     'primalreswebhotelier', '2', NULL,
--     GETDATE(),  GETDATE())
-- END

--35. LRVatRoomType.taxAdjustmentValue
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.LRVatRoomType.taxAdjustmentValue') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomType.taxAdjustmentValue'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--36. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--37. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.MinLOSChunkedOnArrivalDateRateCode'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--38. propertycode
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.propertycode') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.propertycode'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--39. clientcode
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.clientcode') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.clientcode'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--40. YieldCurrencyCode
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.YieldCurrencyCode') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.YieldCurrencyCode'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--41. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.HotelOverbooking.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.HotelOverbooking.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--42. RoomTypeOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.RoomTypeOverbooking.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.RoomTypeOverbooking.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--43. HotelOverbooking.valueType
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.HotelOverbooking.valueType') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.HotelOverbooking.valueType'),
--     'primalreswebhotelier', 'overbooking', NULL,
--     GETDATE(),  GETDATE())
-- END

--44. RoomTypeOverbooking.valueType
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.RoomTypeOverbooking.valueType') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.RoomTypeOverbooking.valueType'),
--     'primalreswebhotelier', 'overbooking', NULL,
--     GETDATE(),  GETDATE())
-- END

--45. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.DeferredDecisionDeliverySeconds') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.DeferredDecisionDeliverySeconds'),
    'primalreswebhotelier', '0', NULL,
    GETDATE(),  GETDATE())
END

--46. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.allowTransferEncodingChunked') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.allowTransferEncodingChunked'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--47. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.async') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.async'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--48. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.ignoreHttpResponse') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.ignoreHttpResponse'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--49. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.includeHTNGAsyncHeaders') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.includeHTNGAsyncHeaders'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--50. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.ack.includeSoapBody') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.ack.includeSoapBody'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'true'),
    GETDATE(),  GETDATE())
END

--51. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.reverseFplosTranslation') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.reverseFplosTranslation'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--52. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.soapChunkSize') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.soapChunkSize'),
    'primalreswebhotelier', '1000', NULL,
    GETDATE(),  GETDATE())
END

--53. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.async.timeout') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.async.timeout'),
    'primalreswebhotelier', '-1', NULL,
    GETDATE(),  GETDATE())
END

--54. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.ManualRestrictions.uploadtype') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.ManualRestrictions.uploadtype'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'uploadtype')
    AND value = 'none'),
    GETDATE(),  GETDATE())
END

--55. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.useCustomMessage') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.useCustomMessage'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--56. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.useHttpBasicAuth') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.useHttpBasicAuth'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--57. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.useSoapChunks') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.useSoapChunks'),
    'primalreswebhotelier', NULL, (Select Config_Parameter_Predefined_Value_Id
    From [dbo].[Config_Parameter_Predefined_Value]
    where [Config_Parameter_Predefined_Value_Type_ID] =
    (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
    from [dbo].[Config_Parameter_Predefined_Value_Type]
    where code = 'boolean')
    AND value = 'false'),
    GETDATE(),  GETDATE())
END

--Outbound Connection Details Param Starts

--58. BarByLOSatRoomClass.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.BarByLOSatRoomClass.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.BarByLOSatRoomClass.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--59. BarByLOSatRoomType.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.BarByLOSatRoomType.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.BarByLOSatRoomType.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--60. customAvailAction
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.customAvailAction') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.customAvailAction'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--61. customRateAction
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.customRateAction') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.customRateAction'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--62. customReplyToAddress
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.customReplyToAddress') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.customReplyToAddress'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--63. DailyBAR.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.DailyBAR.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.DailyBAR.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--64. Fplos.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.Fplos.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.Fplos.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--65. HotelOverbooking.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.HotelOverbooking.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.HotelOverbooking.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--66. inboundPassword
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.inboundPassword') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.inboundPassword'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--67. inboundUsername
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.inboundUsername') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.inboundUsername'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--68. LRVatRoomClass.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where Config_Parameter_ID in (select Config_Parameter_ID
--                                             from [dbo].[Config_Parameter]
--                                             where name = 'pacman.integration.LRVatRoomClass.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomClass.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--69. LRVatRoomType.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.LRVatRoomType.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.LRVatRoomType.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--70. password
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.password') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.password'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--71. reply to
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.replyTo') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.replyTo'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--72. RoomTypeOverbooking.alternateURL
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.RoomTypeOverbooking.alternateURL') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.RoomTypeOverbooking.alternateURL'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--73. Url
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.url') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.url'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--74. username
-- if not exists(select *
--               from [dbo].[Config_Parameter_Value]
--               where [Config_Parameter_ID] in (select [Config_Parameter_ID]
--                                               from [dbo].[Config_Parameter]
--                                               where name = 'pacman.integration.username') AND Context = 'primalreswebhotelier')
-- BEGIN
-- INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
--     [Config_Parameter_Predefined_Value_ID],
--     [Created_DTTM],
-- [Last_Updated_DTTM])
-- VALUES ((select [Config_Parameter_ID]
--     from [dbo].[Config_Parameter]
--     where name = 'pacman.integration.username'),
--     'primalreswebhotelier', NULL, NULL,
--     GETDATE(),  GETDATE())
-- END

--75. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.useSoap2') AND Context = 'primalreswebhotelier')
BEGIN
INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
    [Config_Parameter_Predefined_Value_ID],
    [Created_DTTM],
[Last_Updated_DTTM])
VALUES ((select [Config_Parameter_ID]
    from [dbo].[Config_Parameter]
    where name = 'pacman.integration.useSoap2'),
    'primalreswebhotelier', 'true', (select [Config_Parameter_Predefined_Value_ID]
    from [dbo].[Config_Parameter_Predefined_Value]
    where value = 'true'),
    GETDATE(),  GETDATE())
END