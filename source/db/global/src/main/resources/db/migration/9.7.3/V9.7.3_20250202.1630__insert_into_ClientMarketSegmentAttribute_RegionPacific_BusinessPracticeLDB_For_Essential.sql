DECLARE @ClientID AS INT = (SELECT Client_ID FROM Client WHERE Client_Code='ACCORHG');
DECLARE @Region AS INT
DECLARE @LicensePackageID AS INT = NULL;

IF @ClientID IS NOT NULL
BEGIN
SET @Region = (
  SELECT Client_Attribute_Value_ID
    FROM Client_Attribute_Value CAV
    JOIN Client_Attribute CA ON CAV.Client_Attribute_ID = CA.Client_Attribute_ID
    WHERE CA.Client_Attribute_Name = 'Region' AND CAV.Client_Attribute_Value = 'Pacific'
    AND Client_ID = @ClientID
);
END

BEGIN
 SET @LicensePackageID = (
        SELECT License_package_ID
        FROM License_package LP
        WHERE LP.Package_Name = 'G3 Essential'
    );
END

DECLARE @BusinessPractice AS INT = 6 -- LIMITED_DATA_BUILD -->Ids are mentioned in BusinessPracticeEnum
DECLARE @CurrentDateTime DATETIME = GETDATE();

IF @ClientID IS NOT NULL AND @Region IS NOT NULL
BEGIN
INSERT INTO [dbo].[Client_Market_Segment_Attribute]
(
    [Client_ID],
    [Region],
    [Business_Practice],
    [Market_Code],
    [Rate_Code],
    [Business_Type_ID],
    [Yield_Type_ID],
    [Forecast_Activity_Type_ID],
    [Qualified],
    [Fenced],
    [Package],
    [Link],
    [Booking_Block_Pc],
    [Priced_By_BAR],
    [Created_DTTM],
	[License_package_ID]
)
VALUES
    (@ClientID, @Region, @BusinessPractice, 'PA', NULL, 2, 2, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PB', NULL, 2, 2, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PC', NULL, 2, 2, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PD', NULL, 2, 2, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PI', NULL, 2, 1, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PJ', NULL, 2, 1, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PK', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PL', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CI', NULL, 2, 1, 1, 1, 0, 0, 1, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CJ', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CK', NULL, 2, 2, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CL', NULL, 2, 2, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OR', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OS', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OT', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SR', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SS', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ST', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CR', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CS', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CT', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OY', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SY', NULL, 2, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CY', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TA', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TB', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TC', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TD', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TE', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TF', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TG', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TH', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AFBQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGCI', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGRE', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQAF', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQBQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQCO', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQSL', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COBQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MEOD', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ODME', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SLBQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TGSA', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCD', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCG', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GME', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIN', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GMD', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GMG', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGOG', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGPG', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQFA', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CW', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FABQ', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OP', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OW', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SW', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GID', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIG', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GFA', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCO', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGV', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSM', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TGPO', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TGSE', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WTA', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GLE', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WTS', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GTS', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIT', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TK', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PKG', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OPA', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BILO', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BINA', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIEP', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIIT', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIOV', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIB', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIP', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIV', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIG', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIC', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ICR', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LCR', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GOV', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIFR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIRR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REBA', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TBR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TPK', NULL, 2, 1, 1, 0, 0, 1, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BBR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFA', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TIM', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LBR', NULL, 2, 1, 1, 0, 0, 0, 0, 0, 1, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIAA', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BICP', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BICT', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LISE', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WFO', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FIT', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WFK', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FFT', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGCR', NULL, 2, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGPE', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MCC', NULL, 2, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CRE', NULL, 2, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ATR', NULL, 1, 1, 1, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LAY', NULL, 1, 1, 2, 1, 0, 0, 0, 1, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BICC', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BINW', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIOC', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIOT', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BISP', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LICC', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LINW', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIOC', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIOP', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LISP', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWT', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BBP', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TDS', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LON', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LPP', NULL, 2, 1, 1, 0, 1, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BARE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIAN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BICO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIGP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BILS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BIPO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BRRE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUNH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DIRE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EXRE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LICA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LICD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LICT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIDI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIFT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIIN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LILS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIMA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIMD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIMO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIMR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LISA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LISD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIST', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LITA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LITD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LITO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LITP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIWR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LIYP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LURE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REBR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REDI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REEX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RELU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SOD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HUS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MPR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PMR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZCO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZHO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZCR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZRC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PVE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DAY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PAY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ROTG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUCP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, '0.0', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CII', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CIN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CEV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'INT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MEM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BAR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CCE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CEG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EVE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CPD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DIS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LGS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LGT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LEP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LTC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ARC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HOU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SFT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CPO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'STF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'P', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ON', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GRPLE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GROUPBU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LEIS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ACC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GRSK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'INCONNU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FIXME', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BTR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BUK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BUP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BUS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NEG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CNT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CRW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DSQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DSU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GASS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCOR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCORP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GDIP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GENT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGVN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GINHS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GOFS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSMER', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSPE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSPO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GWHO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HSE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FLS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OPQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTAP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTAR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CON', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TVL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OWN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GRC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GRP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTHR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BUSINESS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GROUP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'INTERNAL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LEISURE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BYE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'A', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GAC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GAM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GAS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GIP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GMM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GTT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'COMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HOUSE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OWNE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTHER', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'B', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'D', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'E', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'G', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'H', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'L', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'O', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'Q', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'R', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'W', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'X', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ID', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RRSS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RAC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FIX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CONS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GASSOC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGOV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GINCENT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GLEIS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GOTHER', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GSOC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTACOM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTANET', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTAOPQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ADV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FLX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PRO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GLF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SEA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SPA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SPC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FPM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGOQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPCITY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CET', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NEGL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PROMO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGSS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGOS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NON', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CORP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGOR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUTM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUTN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CONTRACT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FOT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SAC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LT1', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HOUA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'Empty', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SCBM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUTO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NEGNL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GOT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUTP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGSQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NOM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FBP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SAB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MECR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SCBB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SCB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FPK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQCEV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DMC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGCS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GWE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GAIR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CFM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OTH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'C', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FGE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DIR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SYS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MTG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGCQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REOD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SGC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SABB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPENT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GPG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HSU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZZZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQBQBQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BQCD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'INF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SGB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'T&TFIT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PSEUDO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'T&TGR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SCC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SABM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUCN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FOS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GTA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NONRATE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FCF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SGBM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CVJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XXX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AIR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BGCT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUCD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SGBB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BT_RENT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TRA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CEM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EVENT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CUO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'T', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ANDRIOD10', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CORPC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NEWM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NEWTMC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CGR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GGR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LGR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ACMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AMMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ARO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CCMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CMMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'CRO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EDUC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ENTR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FAM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FGOV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FRAT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GCMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GMMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GRO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GTMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GTRO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IGOV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'INHS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MILT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NATL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'REGL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SCMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SGOV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SMMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SOCL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SPRT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SRO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'STAT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'THRD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TMMP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WHSL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PSBY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TBAR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TDIS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TGAT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TGVT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TINT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TPAC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'TSPC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'AY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'BZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'DZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ED', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ER', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ES', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ET', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'EZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'FZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'GZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'HZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'II', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'IZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'JZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'KZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'LZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ME', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ML', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'MZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ND', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'NZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'OM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'PY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID);




IF @ClientID IS NOT NULL AND @Region IS NOT NULL
INSERT INTO [dbo].[Client_Market_Segment_Attribute]
(
    [Client_ID],
    [Region],
    [Business_Practice],
    [Market_Code],
    [Rate_Code],
    [Business_Type_ID],
    [Yield_Type_ID],
    [Forecast_Activity_Type_ID],
    [Qualified],
    [Fenced],
    [Package],
    [Link],
    [Booking_Block_Pc],
    [Priced_By_BAR],
    [Created_DTTM],
	[License_package_ID]
)
VALUES
    (@ClientID, @Region, @BusinessPractice, 'QU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'QZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'RZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'US', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'UZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'VZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'WZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'XZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'YZ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZA', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZB', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZC', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZD', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZE', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZF', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZG', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZH', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZI', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZJ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZK', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZL', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZM', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZN', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZO', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZP', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZQ', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZR', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZS', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZT', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZU', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZV', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZW', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZX', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'ZY', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'SOV', NULL, 2, 1, 1, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID),
(@ClientID, @Region, @BusinessPractice, 'Catch All', NULL, 2, 1, 3, 1, 0, 0, 0, 0, 0, @CurrentDateTime, @LicensePackageID);
END;
