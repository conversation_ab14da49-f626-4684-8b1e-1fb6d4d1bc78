IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[Agile_Product_Config]') AND TYPE IN (N'U'))
DROP TABLE [dbo].[Agile_Product_Config];
CREATE TABLE [dbo].[Agile_Product_Config](
	[Agile_Product_Config_ID] [bigint] IDENTITY(1,1) NOT NULL,
	[Recovery_State] [nvarchar](50) NULL,
	[Global_Area] [nvarchar](250) NULL,
	[External_System] [nvarchar](250) NULL,
	[Name] [nvarchar](100) NOT NULL,
	[System_Default] [int] NOT NULL,
	[Created_By_User_ID] [bigint] NOT NULL,
	[Created_DTTM] [datetime] NOT NULL,
	[Last_Updated_By_User_ID] [bigint] NOT NULL,
	[Last_Updated_DTTM] [datetime] NOT NULL,
	[Code] [nvarchar](50) NOT NULL,
	[Type] [nvarchar](50) NOT NULL,
	[Dependent_Product_Name] [nvarchar](100) NULL,
	[Description] [nvarchar](250) NULL,
	[Rate_Codes] [nvarchar](max) NULL,
	[Extra_Rate_Codes_For_Restrictions] [nvarchar](max) NULL,
	[To_Product_Hierarchy] [nvarchar](100) NULL,
	[Min_DTA] [int] NOT NULL,
	[Max_DTA] [int] NULL,
	[Min_LOS] [int] NOT NULL,
	[MAX_LOS] [int] NULL,
	[Min_Offset] [numeric](19, 2) NULL,
	[Max_Offset] [numeric](19, 2) NULL,
	[Set_Restrictions] [int] NULL,
	[Is_Offset_For_Extra_Adult] [int] NOT NULL,
	[Is_Offset_For_Extra_Child] [int] NOT NULL,
	[Is_DOW_Offset] [int] NOT NULL,
	[Is_RC_Offset] [int] NOT NULL,
	[Is_DTA_Offset] [int] NOT NULL,
	[Is_Upload] [int] NOT NULL,
	[Status_ID] [int] NOT NULL,
	[Is_Default_Inactive] [int] NOT NULL,
	[Invalid_Reason_ID] [int] NULL,
	[Is_Optimized] [int] NOT NULL,
	[Price_Rounding_Rule] [int] NOT NULL,
	[Is_Publically_Available] [int] NOT NULL,
	[Minimum_Price_Change] [numeric](19, 2) NULL,
	[Offset_Method] [int] NULL,
	[Is_Fixed_Above_Bar] [int] NOT NULL,
 CONSTRAINT [PK_Agile_Product_Config] PRIMARY KEY CLUSTERED 
(
	[Agile_Product_Config_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[Agile_Product_Config] ON 

INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (001, N'TS', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (002, N'TS', N'Americas', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (003, N'TS', N'Americas', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP,OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (004, N'TS', N'Americas', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Travel & Save', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (005, N'TS', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (006, N'TS', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (007, N'TS', N'Americas', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (008, N'TS', N'Americas', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (009, N'TS', N'Americas', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeKQ3', N'Honors Travel & Save', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (010, N'TS', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (011, N'TS', N'Europe, Middle East & Africa', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (012, N'TS', N'Europe, Middle East & Africa', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (013, N'TS', N'Europe, Middle East & Africa', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP,OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (014, N'TS', N'Europe, Middle East & Africa', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (015, N'TS', N'Europe, Middle East & Africa', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (016, N'TS', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (017, N'TS', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (018, N'TS', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (019, N'TS', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeKQ3', N'Honors Dream Away', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (020, N'TS', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (021, N'TS', N'Asia Pacific', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (022, N'TS', N'Asia Pacific', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (023, N'TS', N'Asia Pacific', N'HCRS', N'RPRAM1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ8,ODA0AO,ODA0BK,ODA0ER,ODA0EX,ODA0FA,ODA0FP,ODA0G0,ODA0G1,ODA0G2,ODA0G3,ODA0G5,ODA0HI,ODA0LM,ODA0PA,RPRAM1', N'RPRAM2,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (024, N'TS', N'Asia Pacific', N'HCRS', N'RPRAM2', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPRAM1', N'Honors Travel & Save', N'RPRAM2,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (025, N'TS', N'Asia Pacific', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (026, N'TS', N'Asia Pacific', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (027, N'TS', N'Asia Pacific', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (028, N'TS', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Y', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y', N'R0Z', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (029, N'TS', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Z', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeR0Y', N'Honors Travel & Save', N'R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (030, N'TS', N'Asia Pacific', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (031, N'PP', N'Americas', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGDP12,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP,OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'8AD,KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (032, N'PP', N'Americas', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (033, N'PP', N'Americas', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'8AD,KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (034, N'PP', N'Americas', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (035, N'PP', N'Europe, Middle East & Africa', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGDP15,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,OGADFH,KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP,OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'8AD,KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (036, N'PP', N'Europe, Middle East & Africa', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (037, N'PP', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'8AD,KQ4,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (038, N'PP', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (039, N'PP', N'Asia Pacific', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,OGADFL,OGADFA,OGADJK,KQ8,ODA0AO,ODA0BK,ODA0ER,ODA0EX,ODA0FA,ODA0FP,ODA0G0,ODA0G1,ODA0G2,ODA0G3,ODA0G5,ODA0HI,ODA0LM,ODA0PA,RPRAM1', N'8AD,RPRAM2,2WO', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (040, N'PP', N'Asia Pacific', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,RPRAM2,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (041, N'PP', N'Asia Pacific', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y,R0Z', N'8AD,R0Z', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (042, N'PP', N'Asia Pacific', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (043, N'EarlyRecovery', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (044, N'EarlyRecovery', N'Americas', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (045, N'EarlyRecovery', N'Americas', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (046, N'EarlyRecovery', N'Americas', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Travel & Save', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (047, N'EarlyRecovery', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (048, N'EarlyRecovery', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (049, N'EarlyRecovery', N'Americas', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (050, N'EarlyRecovery', N'Americas', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (051, N'EarlyRecovery', N'Americas', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeKQ3', N'Honors Travel & Save', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (052, N'EarlyRecovery', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (053, N'RampUp', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (054, N'RampUp', N'Americas', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (055, N'RampUp', N'Americas', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGDP12,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (056, N'RampUp', N'Americas', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (057, N'RampUp', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (058, N'RampUp', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (059, N'RampUp', N'Americas', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (060, N'RampUp', N'Americas', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (061, N'RampUp', N'Americas', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (062, N'RampUp', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (063, N'Stabilizing', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (064, N'Stabilizing', N'Americas', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGDP12,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (065, N'Stabilizing', N'Americas', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (066, N'Stabilizing', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (067, N'Stabilizing', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (068, N'Stabilizing', N'Americas', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (069, N'Stabilizing', N'Americas', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (070, N'Stabilizing', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (071, N'Stabilized', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (072, N'Stabilized', N'Americas', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGDP12,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (073, N'Stabilized', N'Americas', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD,KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (074, N'Stabilized', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (075, N'Stabilized', N'Americas', N'HCRS', N'SO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'QO,OGHIS3,OGCOAP,OD15AP,A0,OGCTA2,OD13AP,OGINT4,2D,OD12AP,OGBAH3,OD11AP,KE,PGAAAP,OD10AP,SZ,OD14AP,PGARAP,OGRAK3,HX,OGHRS3,OGHTZ3,OG12AP,TS,AH,4T,OGRT03,LZ,OGTRA3,OS,OGBKAL,OG14A2,5QO,3QO,OGCTP3,OGTUI3,UD,UZ,SO,6QO,4QO,OGMMT3,OGINT3,PGAXY1,QO2', N'HPPAP2,5YY,6YY,7YY,8YY,HPPAP4,HXH,HXU', N'9AD', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-9.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (076, N'Stabilized', N'Americas', N'HCRS', N'HPPAP4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'SO', N'Honors Advance Purchase', N'HPPAP2,5YY,6YY,7YY,8YY,HPPAP4,HXH,HXU', NULL, NULL, 0, 365, 1, 365, CAST(-2.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (077, N'Stabilized', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (078, N'Stabilized', N'Americas', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX', N'8AD,KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (079, N'Stabilized', N'Americas', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (080, N'Stabilized', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (081, N'Stabilized', N'Americas', N'PCRS', N'S:brandCodeSO1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'QO1,TS1,Q3A,O1P,4IN,Q5A,AH1,H3R,HS3,Q2A,T2W,Q4A,YKW,Q1A,CQ7,OS1,2D2,A01,BA3,3HZ,Q0A,3RT,SZ1,4T1,4AP,4RP,4CP,3AT,O4F,BQL,R3K,LZ1,5QO,3QO,TU3,O05,UD1,UZ2,4QO,6QO,SO1,MY3,LD3,QO2', N'H2A,5YY,7YY,HXU,HXH,6YY,8YY,H4A,1PH,H1A', N'L:brandCode9AD', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-9.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (082, N'Stabilized', N'Americas', N'PCRS', N'S:brandCodeH4A', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'S:brandCodeSO1', N'Honors Advance Purchase', N'H2A,5YY,7YY,HXU,HXH,6YY,8YY,H4A,1PH,H1A', NULL, NULL, 0, 365, 1, 365, CAST(-2.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (083, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (084, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (085, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (086, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'RPBBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Dream Away Bed & Breakfast', N'OGBBBQ,OG15BQ,OG00BQ,OG14BQ,OGBGBQ,RPBBBQ,OGEEBQ,OGEBBQ,OG12BQ,OGPLBQ,OGBHBQ,OG15AQ,OGCMBQ,OG10BQ', N'RPHBBQ', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (087, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'RPHBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPBBBQ', N'Honors Dream Away Bed & Breakfast', N'RPHBBQ', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (088, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (089, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (090, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'EB6', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Express Saver Bed & Breakfast', N'EB6,OGLWN4,OGLWT4,OGL15Z,OGLWM4,OGLWA4,OGLWR4,OGLWC4,OGLWY4,OGLWE4,OGLWZ4,OGLWQ4,OGLWL4,OGBKL4,OGLWP4', N'EB7', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (091, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'EB7', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'EB6', N'Honors Express Saver Bed & Breakfast', N'EB7', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (092, N'EarlyRecovery', N'Europe, Middle East & Africa', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(1.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (093, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (094, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (095, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (096, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (097, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (098, N'EarlyRecovery', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(1.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (099, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (100, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (101, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (102, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'RPBBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Dream Away Bed & Breakfast', N'OGBBBQ,OG15BQ,OG00BQ,OG14BQ,OGBGBQ,RPBBBQ,OGEEBQ,OGEBBQ,OG12BQ,OGPLBQ,OGBHBQ,OG15AQ,OGCMBQ,OG10BQ', N'RPHBBQ', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (103, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'RPHBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPBBBQ', N'Honors Dream Away Bed & Breakfast', N'RPHBBQ', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (104, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (105, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (106, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'EB6', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Express Saver Bed & Breakfast', N'EB6,OGLWN4,OGLWT4,OGL15Z,OGLWM4,OGLWA4,OGLWR4,OGLWC4,OGLWY4,OGLWE4,OGLWZ4,OGLWQ4,OGLWL4,OGBKL4,OGLWP4', N'EB7', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (107, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'EB7', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'EB6', N'Honors Express Saver Bed & Breakfast', N'EB7', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (108, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'PR09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'PR09AP,OGINT4,PGARAP,OGTRA3,PGAAAP,OD12AP,OD10AP,OGCTA2,OD13AP,OD15AP,OD14AP,PR09AP,OD18AP,OD09C3,OD09T8,OD20AP,OD09X4,OD16AP,6QO,OGCOAP,OD11AP,OGBKAL,OGRT03,OGCTP3,OD09T1,OD09B2,TL,OS', N'HPPAP4,6YY,8YY,7YY,HXH', N'KQ3,2WY', 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-15.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (109, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'HPPAP4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Honors Advance Purchase', N'HPPAP4,6YY,8YY,7YY,HXH', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (110, N'RampUp', N'Europe, Middle East & Africa', N'HCRS', N'CX09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Advance Purchase Bed & Breakfast', N'ODAPR2,OGINT7,OD18AB,OD12AB,OD13AB,OD15AB,OD16AB,CX09AP,OD09C6,OD20AB,OGBKBL,OD11AB,OGCTB2,OGRT06,OD09XA,OD09TA,OD10AB,OGCTP6,OD09B4,TT', NULL, NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (111, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (112, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (113, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (114, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (115, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (116, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeSO1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'4QO,SO1,T2W,Q2A,OS1,4AP,3RT,Q4A,4IN,O4F,4RP,Q1A,AH1,Q3A,BA3,Q5A,BQL,SZ1,H3R,TS1,2D2,HS3,YKW,3AT,O1P,R3K,6QO,Q0A,CQ7,4CP,LZ1,9PT,TU3,PR3,UD1,6QO,SO1,3AT,BQL,O1P,O4F', N'H4A,8YY,HXH,6YY,7YY', N'L:brandCodeKQ3,L:brandCode2WY', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (117, N'RampUp', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeH4A', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'S:brandCodeSO1', N'Honors Advance Purchase', N'H4A,8YY,HXH,6YY,7YY', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (118, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (119, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'R3X', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Semi Flex', N'R3X,3RX,4RX,5RX,6RX,C3F,C7F,OG10SE,OG10SF,OG11SE,OG11SF,OG12SE,OG12SF,OG13SE,OG13SF,OG14SE,OG14SF,OG15FE,OG15SE,OG15SF,OG15TF,OGBGSE,OGBGSF,OGBOSE,OGBOSF,OGEPRE,OGEPRF,OGEPSE,OGEPSF,OGFASE,OGFASF,OGFPSE,OGFPSF,OGHISE,OGHISF,OGJPBE,OGJPBF,OGJPSE,OGJPSF,OGLTSE,OGLTSF,OGPRSE,OGPRSF,OGTRSE,OGTRSF,R7X', N'H3F,H6F,H7F,H8F', N'HPPRP1', 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (120, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'H3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'R3X', N'Honors Semi Flex', N'H3F,H6F,H7F,H8F', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (121, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'B3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'R3X', N'Semi Flex Bed & Breakfast', N'B3F,B7F,OG12BE,OGBGBE,OGTRBF,OGBGBF,OGLTBS,OGEPBS,OGTRBS,OG13BE,OGHIBS,OG15BE,OGPRBS,OG11BE,OG10BE,OGBOBS,OG14BE,OGBOBF,OG13BF,OG10BF,OG15BF,OG14BF,OG11BF,OGHIBF,OG12BF,OGLTBF,OGPRBF,OGEPBF', NULL, NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (122, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'RPLOCR', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Stay Longer & Save', N'OGSLR2,RPLOCR,OGSLR5,OGSLBR,OGSLR1,OGSLER,OGSLR3,OGSLHR,OGSLLR,OGSLPR,OGSLVR,OGSLR0,OGSERR,OGFLR2,OGSLR7', N'RPLOSR', NULL, 0, 365, 1, 365, CAST(-20.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (123, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'RPLOSR', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPLOCR', N'Honors Stay Longer & Save', N'RPLOSR', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (124, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'RPLOCB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPLOCR', N'Stay Longer & Save Bed & Breakfast', N'OGSLEB,OGSLB5,OGSLLB,OGSLB0,RPLOCB,OGSLB1,OGSLHB,OGSLB2,OGSLPB,OGSLB3,OGSLBB,OGSLVB,OGFLB2,OGSERB', N'RPLOSB', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (125, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'RPLOSB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPLOCB', N'Honors Stay Longer & Save Bed & Breakfast', N'RPLOSB', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (126, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'PR09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'PR09AP,OGINT4,PGARAP,OGTRA3,PGAAAP,OD12AP,OD10AP,OGCTA2,OD13AP,OD15AP,OD14AP,PR09AP,OD18AP,OD09C3,OD09T8,OD20AP,OD09X4,OD16AP,6QO,OGCOAP,OD11AP,OGBKAL,OGRT03,OGCTP3,OD09T1,OD09B2,TL,OS', N'HPPAP4,6YY,8YY,7YY,HXH', N'R3X', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (127, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'HPPAP4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Honors Advance Purchase', N'HPPAP4,6YY,8YY,7YY,HXH', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (128, N'Stabilized', N'Europe, Middle East & Africa', N'HCRS', N'CX09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Advance Purchase Bed & Breakfast', N'ODAPR2,OGINT7,OD18AB,OD12AB,OD13AB,OD15AB,OD16AB,CX09AP,OD09C6,OD20AB,OGBKBL,OD11AB,OGCTB2,OGRT06,OD09XA,OD09TA,OD10AB,OGCTP6,OD09B4,TT', NULL, NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (129, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (130, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeR3X', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Semi Flex', N'R3X,3DE,3DF,3GF,3HE,3HF,3PE,3PF,3RX,3UE,3UF,3XE,3XF,3XR,3XT,4RX,5RX,6RX,C3F,C7F,QE0,QE1,QE2,QE3,QE4,QE5,QF0,QF1,QF2,QF3,QF4,QF5,QSE,QSF,QT5,R7X', N'H3F,3FH,4FH,5FT,6FT,H6F,H7F,H8F', N'L:brandCodeH1P', 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (131, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeR3X', N'Honors Semi Flex', N'H3F,3FH,4FH,5FT,6FT,H6F,H7F,H8F', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (132, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeS3C', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Stay Longer & Save', N'ZCS,ZPS,ZHS,ZBS,Z0S,ZES,Z1S,Z2S,S3C,Z3S,ZLS,ZE0,Z5S,Z2T', N'S3S', NULL, 0, 365, 1, 365, CAST(-20.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (133, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeS3S', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeS3C', N'Honors Stay Longer & Save', N'S3S', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (134, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeSO1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'4QO,SO1,T2W,Q2A,OS1,4AP,3RT,Q4A,4IN,O4F,4RP,Q1A,AH1,Q3A,BA3,Q5A,BQL,SZ1,H3R,TS1,2D2,HS3,YKW,3AT,O1P,R3K,6QO,Q0A,CQ7,4CP,LZ1,9PT,TU3,PR3,UD1,6QO,SO1,3AT,BQL,O1P,O4F', N'H4A,8YY,HXH,6YY,7YY', N'L:brandCodeR3X', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (135, N'Stabilized', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeH4A', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'S:brandCodeSO1', N'Honors Advance Purchase', N'H4A,8YY,HXH,6YY,7YY', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (136, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (137, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,OGADFL,OGADFA,OGADJK', N'8AD', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (138, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (139, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'PGADBB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Prepay & Save Bed & Breakfast', N'OGAB14,PGADBB,OGAB10,OGAB12,OGAB15,OGABER,OGAB00,OGABEX,OGABPL,OGDB15,OGABBA,OGABBK,OGABBV,OGABFA,OGABFL,OGABCT,OGABJK', N'PGADHB', NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (140, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'PGADHB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PGADBB', N'Honors Prepay & Save Bed & Breakfast', N'PGADHB', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (141, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'RPRAM1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ8,ODA0AO,ODA0BK,ODA0ER,ODA0EX,ODA0FA,ODA0FP,ODA0G0,ODA0G1,ODA0G2,ODA0G3,ODA0G5,ODA0HI,ODA0LM,ODA0PA,RPRAM1', N'RPRAM2', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (142, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'RPRAM2', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPRAM1', N'Honors Travel & Save', N'RPRAM2,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (143, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'RPBAM1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPRAM1', N'Travel & Save Bed & Breakfast', N'ODA1BK,ODA1ER,ODA1EX,ODA1FA,ODA1FP,ODA1G0,ODA1G1,ODA1G2,ODA1G3,ODA1G5,ODA1HI,ODA1LM,ODA1PA,RPBAM1,ODA1AO', N'RPBAM2', NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (144, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'RPBAM2', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPBAM1', N'Honors Travel & Save Bed & Breakfast', N'RPBAM2', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (145, N'EarlyRecovery', N'Asia Pacific', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (146, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (147, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y,R0Z', N'8AD', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (148, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (149, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Y', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y,R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (150, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Z', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeR0Y', N'Honors Travel & Save', N'R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (151, N'EarlyRecovery', N'Asia Pacific', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (152, N'Stabilized', N'Asia Pacific', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (153, N'Stabilized', N'Asia Pacific', N'HCRS', N'PR09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'PR09AP,OD12AP,PGAAAP,OGINT4,4QO,PGARAP,OD09T8,OD14AP,6QO,OGCOAP,OD09C3,OD09X4,OD13AP,OD10AP,OD15AP,OD11AP,OD16AP,OD20AP,OD18AP,OGTRA3,OGBKAL,OGRT03,OGCTP3,OGCTA2,OD09B2,OD09T1,ODFLA2,ODFPP2,TL,OS,CX09AP', N'HPPAP4,6YY,HXH,8YY,RPHRS1,RPHBS1,8XX,6XX', N'R3X', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (154, N'Stabilized', N'Asia Pacific', N'HCRS', N'HPPAP4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Honors Advance Purchase', N'HPPAP4,6YY,HXH,8YY,RPHRS1,RPHBS1,8XX,6XX', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (155, N'Stabilized', N'Asia Pacific', N'HCRS', N'CX09AP', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PR09AP', N'Advance Purchase Bed & Breakfast', N'OD09XA,OD13AB,OD12AB,OD20AB,OD15AB,OGINT7,OD09C6,OD18AB,OD16AB,OD11AB,ODAPR2,OD10AB,OGBKBL,OGRT06,OD09TA,OGCTP6,CX09AP,OGCTB2,OD09B4,ODFLB2,ODFPB2,TT,RPHAP2', NULL, NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (156, N'Stabilized', N'Asia Pacific', N'HCRS', N'R3X', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Semi Flex', N'R3X,3RX,4RX,5RX,6RX,C3F,C7F,OG10SE,OG10SF,OG11SE,OG11SF,OG12SE,OG12SF,OG13SE,OG13SF,OG14SE,OG14SF,OG15FE,OG15SE,OG15SF,OG15TF,OGBGSE,OGBGSF,OGBOSE,OGBOSF,OGEPRE,OGEPRF,OGEPSE,OGEPSF,OGFASE,OGFASF,OGFPSE,OGFPSF,OGHISE,OGHISF,OGJPBE,OGJPBF,OGJPSE,OGJPSF,OGLTSE,OGLTSF,OGPRSE,OGPRSF,OGTRSE,OGTRSF,R7X', N'H3F,H6F,H7F,H8F', N'HPPRP1', 0, 365, 1, 365, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (157, N'Stabilized', N'Asia Pacific', N'HCRS', N'H3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'R3X', N'Honors Semi Flex', N'H3F,H6F,H7F,H8F', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (158, N'Stabilized', N'Asia Pacific', N'HCRS', N'B3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'R3X', N'Semi Flex Bed & Breakfast', N'B3F,B7F,OGTRBF,OG12BE,OGEPBS,OG10BE,OGHIBS,OG11BE,OGTRBS,OG13BE,OGBOBS,OGLTBS,OGHIBF,OG13BF,OG15BE,OG11BF,OGBOBF,OGPRBS,OGEPBF,OG14BE,OG12BF,OG15BF,OG14BF,OG10BF,OGLTBF,OGPRBF,OGBGBE,OGBGBF,OGFPBF,OGFABF,OGFPBE,OGFABE,OGJPBF,OGJPBE', NULL, NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (159, N'Stabilized', N'Asia Pacific', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (160, N'Stabilized', N'Asia Pacific', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (161, N'Stabilized', N'Asia Pacific', N'PCRS', N'S:brandCodeSO1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Advance Purchase', N'4QO,UD1,Q4A,Q0A,3AT,SZ1,3RT,OS1,4AP,Q2A,4CP,R3K,4IN,TS1,2D2,4RP,PR3,6QO,Q1A,AH1,Q3A,BQL,Q5A,CQ7,SO1,H3R,T2W,HS3,TU3,LZ1,YKW,O1P,O4F,SO1,3AT,BQL,O1P,O4F', N'H4A,8YY,6YY,HXH', N'L:brandCodeR3X', 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (162, N'Stabilized', N'Asia Pacific', N'PCRS', N'S:brandCodeH4A', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'S:brandCodeSO1', N'Honors Advance Purchase', N'H4A,8YY,6YY,HXH', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (163, N'Stabilized', N'Asia Pacific', N'PCRS', N'L:brandCodeR3X', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Semi Flex', N'R3X,3DE,3DF,3GF,3HE,3HF,3PE,3PF,3RX,3UE,3UF,3XE,3XF,3XR,3XT,4RX,5RX,6RX,C3F,C7F,QE0,QE1,QE2,QE3,QE4,QE5,QF0,QF1,QF2,QF3,QF4,QF5,QSE,QSF,QT5,R7X', N'H3F,3FH,4FH,5FT,6FT,H6F,H7F,H8F', N'L:brandCodeH1P', 0, 365, 1, 365, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (164, N'Stabilized', N'Asia Pacific', N'PCRS', N'L:brandCodeH3F', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeR3X', N'Honors Semi Flex', N'H3F,3FH,4FH,5FT,6FT,H6F,H7F,H8F', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (165, N'Stabilized', N'Asia Pacific', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (166, N'OTHER', N'Americas', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (167, N'OTHER', N'Americas', N'HCRS', N'AA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (168, N'OTHER', N'Americas', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (169, N'OTHER', N'Americas', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Travel & Save', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (170, N'OTHER', N'Americas', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (171, N'OTHER', N'Americas', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (172, N'OTHER', N'Americas', N'PCRS', N'S:brandCodeAAA', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'AAA', N'AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-12.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 0, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (173, N'OTHER', N'Americas', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (174, N'OTHER', N'Americas', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeKQ3', N'Honors Travel & Save', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (175, N'OTHER', N'Americas', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (176, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (177, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'KQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,OGBAKQ,OGBGKQ,OGBKKQ,OGCTKQ,OGEXKQ,OGERKQ,OG15FQ,OGGRKQ,OG10KQ,OG12KQ,OG14KQ,OG15KQ,OGPAKQ,OGRKKQ,OGCMBQ,PGHDAP', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (178, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'KQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (179, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'RPBBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Dream Away Bed & Breakfast', N'OGBBBQ,OG15BQ,OG00BQ,OG14BQ,OGBGBQ,RPBBBQ,OGEEBQ,OGEBBQ,OG12BQ,OGPLBQ,OGBHBQ,OG15AQ,OGCMBQ,OG10BQ', N'RPHBBQ', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (180, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'RPHBBQ', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPBBBQ', N'Honors Dream Away Bed & Breakfast', N'RPHBBQ', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (181, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'OGLWZ2,5WH,2WY,8HW,9LW,2WZ,5WZ,OGLWT2,OGLWR2,OGLWM2,OGLWL2,OGL15W,OGBKL2,OGLWC2,OGLWE2,OGLWI2,OGLWP3,OGLWS2,OGLWY2,PGLWA2', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (182, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (183, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'EB6', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'2WY', N'Express Saver Bed & Breakfast', N'EB6,OGLWN4,OGLWT4,OGL15Z,OGLWM4,OGLWA4,OGLWR4,OGLWC4,OGLWY4,OGLWE4,OGLWZ4,OGLWQ4,OGLWL4,OGBKL4,OGLWP4', N'EB7', NULL, 0, 365, 1, 365, CAST(10.00 AS Numeric(19, 2)), CAST(10.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (184, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'EB7', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'EB6', N'Honors Express Saver Bed & Breakfast', N'EB7', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (185, N'OTHER', N'Europe, Middle East & Africa', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(1.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (186, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (187, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ3', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Dream Away', N'KQ3,KQ6,KQ7,KQ8,BQF,BQK,BQZ,CQK,EQK,EQY,PQQ,PQB,4HD,FQO,GQO,MQE,MQH,MQK,MQM,0EX,2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'KQ4', NULL, 0, 365, 1, 365, CAST(-15.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (188, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCodeKQ4', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'KQ3', N'Honors Dream Away', N'KQ4,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (189, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WY', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Express Saver', N'2WY,8HW,9LW,2WZ,5WZ,W5H,H6L,5WA,L5W,LW7,0LW,W2X,5WC,8HW,5WB,W5F,X3L,5WH', N'2WO', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(0.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (190, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'L:brandCode2WO', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode2WY', N'Honors Express Saver', N'2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (191, N'OTHER', N'Europe, Middle East & Africa', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(1.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (192, N'OTHER', N'Asia Pacific', N'HCRS', N'HPPRP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'HPPRP1,HPPRP2,AA,PGARP3,PGMFR1,PGSRN1', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (193, N'OTHER', N'Asia Pacific', N'HCRS', N'9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,OGADBA,OGADBK,OGADBV,OGADCT,OGADEX,OGADER,OGAD10,OGAD12,OGAD14,OGAD15,OGADPL,OGAD00,OGADFL,OGADFA,OGADJK', N'8AD', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (194, N'OTHER', N'Asia Pacific', N'HCRS', N'8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Honors Prepay & Save', N'8AD', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (195, N'OTHER', N'Asia Pacific', N'HCRS', N'PGADBB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'9AD', N'Prepay & Save Bed & Breakfast', N'OGAB14,PGADBB,OGAB10,OGAB12,OGAB15,OGABER,OGAB00,OGABEX,OGABPL,OGDB15,OGABBA,OGABBK,OGABBV,OGABFA,OGABFL,OGABCT,OGABJK', N'PGADHB', NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (196, N'OTHER', N'Asia Pacific', N'HCRS', N'PGADHB', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'PGADBB', N'Honors Prepay & Save Bed & Breakfast', N'PGADHB', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (197, N'OTHER', N'Asia Pacific', N'HCRS', N'RPRAM1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'KQ8,ODA0AO,ODA0BK,ODA0ER,ODA0EX,ODA0FA,ODA0FP,ODA0G0,ODA0G1,ODA0G2,ODA0G3,ODA0G5,ODA0HI,ODA0LM,ODA0PA,RPRAM1', N'RPRAM2', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (198, N'OTHER', N'Asia Pacific', N'HCRS', N'RPRAM2', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPRAM1', N'Honors Travel & Save', N'RPRAM2,2WO', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (199, N'OTHER', N'Asia Pacific', N'HCRS', N'RPBAM1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPRAM1', N'Travel & Save Bed & Breakfast', N'ODA1BK,ODA1ER,ODA1EX,ODA1FA,ODA1FP,ODA1G0,ODA1G1,ODA1G2,ODA1G3,ODA1G5,ODA1HI,ODA1LM,ODA1PA,RPBAM1,ODA1AO', N'RPBAM2', NULL, 0, 365, 1, 365, CAST(100.00 AS Numeric(19, 2)), CAST(100.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 2, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (200, N'OTHER', N'Asia Pacific', N'HCRS', N'RPBAM2', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'RPBAM1', N'Honors Travel & Save Bed & Breakfast', N'RPBAM2', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (201, N'OTHER', N'Asia Pacific', N'HCRS', N'HPDPT1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'HPDPT1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (202, N'OTHER', N'Asia Pacific', N'PCRS', N'L:brandCodeH1P', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'Honors Discount', N'H1P,H2P,AAA,ARP,MFR,SRN', NULL, NULL, 0, NULL, 1, NULL, CAST(-8.00 AS Numeric(19, 2)), CAST(-2.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (203, N'OTHER', N'Asia Pacific', N'PCRS', N'L:brandCode9AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Prepay & Save', N'9AD,2DP,6DX,9DX,XD4,X4D,X5D,8DX,7DX,9DP,8DP,7DP,6DP,6DP,4DP,R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y,R0Z', N'8AD', NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (204, N'OTHER', N'Asia Pacific', N'PCRS', N'L:brandCode8AD', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCode9AD', N'Honors Prepay & Save', N'8AD,R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (205, N'OTHER', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Y', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', NULL, N'Travel & Save', N'R0A,R0B,R0D,R0G,R0I,R0L,R0O,R0P,R0R,R0T,R0Y,R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-10.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 1, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (206, N'OTHER', N'Asia Pacific', N'PCRS', N'L:brandCodeR0Z', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'FENCED_NO_PACKAGE', N'L:brandCodeR0Y', N'Honors Travel & Save', N'R0Z', NULL, NULL, 0, 365, 1, 365, CAST(-5.00 AS Numeric(19, 2)), CAST(-5.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 0, 1, 0, NULL, 0, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
INSERT [dbo].[Agile_Product_Config] ([Agile_Product_Config_ID], [Recovery_State], [Global_Area], [External_System], [Name], [System_Default], [Created_By_User_ID], [Created_DTTM], [Last_Updated_By_User_ID], [Last_Updated_DTTM], [Code], [Type], [Dependent_Product_Name], [Description], [Rate_Codes], [Extra_Rate_Codes_For_Restrictions], [To_Product_Hierarchy], [Min_DTA], [Max_DTA], [Min_LOS], [MAX_LOS], [Min_Offset], [Max_Offset], [Set_Restrictions], [Is_Offset_For_Extra_Adult], [Is_Offset_For_Extra_Child], [Is_DOW_Offset], [Is_RC_Offset], [Is_DTA_Offset], [Is_Upload], [Status_ID], [Is_Default_Inactive], [Invalid_Reason_ID], [Is_Optimized], [Price_Rounding_Rule], [Is_Publically_Available], [Minimum_Price_Change], [Offset_Method], [Is_Fixed_Above_Bar]) VALUES (207, N'OTHER', N'Asia Pacific', N'PCRS', N'S:brandCodeDP1', 0, 1, GETDATE(), 1, GETDATE(), N'AGILE_RATES', N'UNFENCED_AND_NO_PACKAGED', NULL, N'2X POINTS', N'DP1', NULL, NULL, 0, NULL, 1, NULL, CAST(2.00 AS Numeric(19, 2)), CAST(8.00 AS Numeric(19, 2)), 0, 0, 0, 0, 0, 0, 1, 1, 0, NULL, 1, 1, 1, CAST(0.00 AS Numeric(19, 2)), 1, 0)
SET IDENTITY_INSERT [dbo].[Agile_Product_Config] OFF
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_System_Default]  DEFAULT ((0)) FOR [System_Default]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Created_By_User_ID]  DEFAULT ((1)) FOR [Created_By_User_ID]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Created_DTTM]  DEFAULT (getdate()) FOR [Created_DTTM]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Last_Updated_By_User_ID]  DEFAULT ((1)) FOR [Last_Updated_By_User_ID]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Last_Updated_DTTM]  DEFAULT (getdate()) FOR [Last_Updated_DTTM]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Min_DTA]  DEFAULT ((0)) FOR [Min_DTA]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Min_LOS]  DEFAULT ((0)) FOR [Min_LOS]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Max_LOS]  DEFAULT ((0)) FOR [MAX_LOS]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_Offset_For_Extra_Adult]  DEFAULT ((0)) FOR [Is_Offset_For_Extra_Adult]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_Offset_For_Extra_Child]  DEFAULT ((0)) FOR [Is_Offset_For_Extra_Child]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_DOW_Offset]  DEFAULT ((0)) FOR [Is_DOW_Offset]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_RC_Offset]  DEFAULT ((0)) FOR [Is_RC_Offset]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_DTA_Offset]  DEFAULT ((0)) FOR [Is_DTA_Offset]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF__Agile_Pro__Is_Up__5155CB11]  DEFAULT ((0)) FOR [Is_Upload]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF__Agile_Pro__Statu__5249EF4A]  DEFAULT ((1)) FOR [Status_ID]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF__Agile_Pro__Is_De__533E1383]  DEFAULT ((0)) FOR [Is_Default_Inactive]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_Optimized]  DEFAULT ((0)) FOR [Is_Optimized]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF__Agile_Pro__Price__55265BF5]  DEFAULT ((1)) FOR [Price_Rounding_Rule]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF_Agile_Pro_Is_Publically_Available]  DEFAULT ((1)) FOR [Is_Publically_Available]
GO
ALTER TABLE [dbo].[Agile_Product_Config] ADD  CONSTRAINT [DF__Agile_Pro__Is_Fi__570EA467]  DEFAULT ((0)) FOR [Is_Fixed_Above_Bar]
GO
