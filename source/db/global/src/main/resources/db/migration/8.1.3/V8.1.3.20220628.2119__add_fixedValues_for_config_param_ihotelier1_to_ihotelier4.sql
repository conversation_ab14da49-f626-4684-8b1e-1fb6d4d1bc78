--# iHotelier1
--1. Url
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.url') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.url'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--2. username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.username') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.username'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--3. password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.password') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.password'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--4. reply to
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.replyTo') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.replyTo'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where [value] = 'true'),
                 GETDATE(),  GETDATE())
    END

--5. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.useSoap2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.useSoap2'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where value = 'true'),
                 GETDATE(),  GETDATE())
    END

--6. DailyBAR.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--7. LRVatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--8. LRVatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where Config_Parameter_ID in (select Config_Parameter_ID
                                            from [dbo].[Config_Parameter]
                                            where name = 'pacman.integration.iHotelier1.LRVatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--9. BarByLOSatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.BarByLOSatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.BarByLOSatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--10. BarByLOSatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.BarByLOSatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.BarByLOSatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--11. RoomTypeOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--12. HotelOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.HotelOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.HotelOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--13. Fplos.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.Fplos.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.Fplos.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--14. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.async') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.async'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--15. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.BarByLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.BarByLOS.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--16. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.BarFplosByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.BarFplosByRoomType.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--17. DailyBAR.dailybarRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.dailybarRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.dailybarRateCode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--18. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.DailyBAR.dailybarRoundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.dailybarRoundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--19. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.miscAdjustment') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.miscAdjustment'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--20. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.newDailyBARFormat') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.newDailyBARFormat'),
                'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
                                 from [dbo].[Config_Parameter_Predefined_Value]
                                 where value = 'true'),
                 GETDATE(),  GETDATE())
    END


--21. DailyBAR.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--22. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.useDeltaForDifferential') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.useDeltaForDifferential'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--23. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--24. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.Fplos.fplosAtRoomCategory') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.Fplos.fplosAtRoomCategory'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--25. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.Fplos.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.Fplos.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--26. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.HotelOverbooking.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.HotelOverbooking.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--27. HotelOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.HotelOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.HotelOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--28. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--29. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.includeCeilingDeltaMaxSold'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--30. LRVatRoomClass.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomClass.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--31. LRVatRoomClass.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.LRVatRoomClass.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--32. LRVatRoomClass.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomClass.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomClass.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--33. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.LRVatRoomType.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--34. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.includeCeilingDeltaMaxSold '),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--35. LRVatRoomType.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomType.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--36. LRVatRoomType.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomType.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--37. LRVatRoomType.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRVatRoomType.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRVatRoomType.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--38. RoomTypeOverbooking.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--39. RoomTypeOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.RoomTypeOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--40. propertycode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.propertycode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.propertycode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--41. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.reverseFplosTranslation') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.reverseFplosTranslation'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--42. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.useSoapChunks') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.useSoapChunks'),
                'pacman', 'false', (Select Config_Parameter_Predefined_Value_Id
                                    From [dbo].[Config_Parameter_Predefined_Value]
                                    where [Config_Parameter_Predefined_Value_Type_ID] =
                                          (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                           from [dbo].[Config_Parameter_Predefined_Value_Type]
                                           where code = 'boolean')
                                      AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--43. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.useCustomMessage') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.useCustomMessage'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--44. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.soapChunkSize') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.soapChunkSize'),
                'pacman', '1000', NULL,
                 GETDATE(),  GETDATE())
    END

--45. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--46. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.BARByLOSByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.BARByLOSByRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--47. customRateAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.customRateAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.customRateAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--48. customAvailAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.customAvailAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.customAvailAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--49. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.useYieldCurrencyForDailyBar') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.useYieldCurrencyForDailyBar'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--50. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DeferredDecisionDeliverySeconds') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DeferredDecisionDeliverySeconds'),
                'pacman', '0', NULL,
                 GETDATE(),  GETDATE())
    END

--51. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.DailyBAR.AllMessagesSentAsDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.DailyBAR.AllMessagesSentAsDelta'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--52. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.MinLOSChunkedOnArrivalDateRateCode'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--53. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier1.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name =
                       'pacman.integration.iHotelier1.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'DailyBarSelectiveUpload')
                                   AND value = 'None'),
                 GETDATE(),  GETDATE())
    END

--54. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRAControlFPLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRAControlFPLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--55. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.LRAControlMinLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.LRAControlMinLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--56. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.AgileRates.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.AgileRates.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--57. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.ack.includeSoapBody') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.ack.includeSoapBody'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--58. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.UploadAdultsBeyond2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.UploadAdultsBeyond2'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--59. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.UploadChildrenBeyondExtra') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.UploadChildrenBeyondExtra'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--60. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.UploadChildAgeBuckets') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.UploadChildAgeBuckets'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--61. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.ignoreHttpResponse') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.ignoreHttpResponse'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--62. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.useHttpBasicAuth') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.useHttpBasicAuth'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--63. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.async.timeout') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.async.timeout'),
                'pacman', '-1', NULL,
                 GETDATE(),  GETDATE())
    END

--64. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.includeHTNGAsyncHeaders') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.includeHTNGAsyncHeaders'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--65. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.allowTransferEncodingChunked') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.allowTransferEncodingChunked'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--66. clientcode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.clientcode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.clientcode'),
                'pacman', ' ', NULL,
                 GETDATE(),  GETDATE())
    END

--67. inboundUsername
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.inboundUsername') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.inboundUsername'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--68. inboundPassword
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.inboundPassword') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.inboundPassword'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--69. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.optimizedDailyBarAgileRateUpload') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.optimizedDailyBarAgileRateUpload'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--70. UploadCurrencyForDailyBAR
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.UploadCurrencyForDailyBAR') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.UploadCurrencyForDailyBAR'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--71. UploadCurrencyForLRV
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.UploadCurrencyForLRV') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.UploadCurrencyForLRV'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--72. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier1.ManualRestrictions.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier1.ManualRestrictions.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END


--# iHotelier2
--1. Url
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.url') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.url'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--2. username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.username') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.username'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--3. password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.password') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.password'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--4. reply to
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.replyTo') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.replyTo'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where [value] = 'true'),
                 GETDATE(),  GETDATE())
    END

--5. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.useSoap2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.useSoap2'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where value = 'true'),
                 GETDATE(),  GETDATE())
    END

--6. DailyBAR.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--7. LRVatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--8. LRVatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where Config_Parameter_ID in (select Config_Parameter_ID
                                            from [dbo].[Config_Parameter]
                                            where name = 'pacman.integration.iHotelier2.LRVatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--9. BarByLOSatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.BarByLOSatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.BarByLOSatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--10. BarByLOSatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.BarByLOSatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.BarByLOSatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--11. RoomTypeOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--12. HotelOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.HotelOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.HotelOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--13. Fplos.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.Fplos.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.Fplos.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--14. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.async') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.async'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--15. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.BarByLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.BarByLOS.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--16. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.BarFplosByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.BarFplosByRoomType.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--17. DailyBAR.dailybarRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.dailybarRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.dailybarRateCode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--18. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.DailyBAR.dailybarRoundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.dailybarRoundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--19. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.miscAdjustment') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.miscAdjustment'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--20. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.newDailyBARFormat') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.newDailyBARFormat'),
                'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
                                 from [dbo].[Config_Parameter_Predefined_Value]
                                 where value = 'true'),
                 GETDATE(),  GETDATE())
    END


--21. DailyBAR.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--22. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.useDeltaForDifferential') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.useDeltaForDifferential'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--23. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--24. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.Fplos.fplosAtRoomCategory') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.Fplos.fplosAtRoomCategory'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--25. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.Fplos.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.Fplos.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--26. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.HotelOverbooking.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.HotelOverbooking.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--27. HotelOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.HotelOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.HotelOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--28. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--29. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.includeCeilingDeltaMaxSold'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--30. LRVatRoomClass.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomClass.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--31. LRVatRoomClass.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.LRVatRoomClass.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--32. LRVatRoomClass.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomClass.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomClass.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--33. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.LRVatRoomType.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--34. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.includeCeilingDeltaMaxSold '),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--35. LRVatRoomType.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomType.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--36. LRVatRoomType.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomType.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--37. LRVatRoomType.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRVatRoomType.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRVatRoomType.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--38. RoomTypeOverbooking.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--39. RoomTypeOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.RoomTypeOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--40. propertycode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.propertycode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.propertycode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--41. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.reverseFplosTranslation') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.reverseFplosTranslation'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--42. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.useSoapChunks') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.useSoapChunks'),
                'pacman', 'false', (Select Config_Parameter_Predefined_Value_Id
                                    From [dbo].[Config_Parameter_Predefined_Value]
                                    where [Config_Parameter_Predefined_Value_Type_ID] =
                                          (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                           from [dbo].[Config_Parameter_Predefined_Value_Type]
                                           where code = 'boolean')
                                      AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--43. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.useCustomMessage') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.useCustomMessage'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--44. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.soapChunkSize') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.soapChunkSize'),
                'pacman', '1000', NULL,
                 GETDATE(),  GETDATE())
    END

--45. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--46. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.BARByLOSByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.BARByLOSByRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--47. customRateAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.customRateAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.customRateAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--48. customAvailAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.customAvailAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.customAvailAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--49. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.useYieldCurrencyForDailyBar') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.useYieldCurrencyForDailyBar'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--50. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DeferredDecisionDeliverySeconds') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DeferredDecisionDeliverySeconds'),
                'pacman', '0', NULL,
                 GETDATE(),  GETDATE())
    END

--51. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.DailyBAR.AllMessagesSentAsDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.DailyBAR.AllMessagesSentAsDelta'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--52. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.MinLOSChunkedOnArrivalDateRateCode'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--53. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier2.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name =
                       'pacman.integration.iHotelier2.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'DailyBarSelectiveUpload')
                                   AND value = 'None'),
                 GETDATE(),  GETDATE())
    END

--54. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRAControlFPLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRAControlFPLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--55. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.LRAControlMinLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.LRAControlMinLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--56. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.AgileRates.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.AgileRates.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--57. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.ack.includeSoapBody') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.ack.includeSoapBody'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--58. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.UploadAdultsBeyond2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.UploadAdultsBeyond2'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--59. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.UploadChildrenBeyondExtra') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.UploadChildrenBeyondExtra'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--60. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.UploadChildAgeBuckets') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.UploadChildAgeBuckets'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--61. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.ignoreHttpResponse') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.ignoreHttpResponse'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--62. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.useHttpBasicAuth') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.useHttpBasicAuth'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--63. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.async.timeout') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.async.timeout'),
                'pacman', '-1', NULL,
                 GETDATE(),  GETDATE())
    END

--64. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.includeHTNGAsyncHeaders') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.includeHTNGAsyncHeaders'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--65. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.allowTransferEncodingChunked') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.allowTransferEncodingChunked'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--66. clientcode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.clientcode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.clientcode'),
                'pacman', ' ', NULL,
                 GETDATE(),  GETDATE())
    END

--67. inboundUsername
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.inboundUsername') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.inboundUsername'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--68. inboundPassword
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.inboundPassword') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.inboundPassword'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--69. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.optimizedDailyBarAgileRateUpload') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.optimizedDailyBarAgileRateUpload'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--70. UploadCurrencyForDailyBAR
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.UploadCurrencyForDailyBAR') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.UploadCurrencyForDailyBAR'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--71. UploadCurrencyForLRV
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.UploadCurrencyForLRV') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.UploadCurrencyForLRV'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--72. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier2.ManualRestrictions.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier2.ManualRestrictions.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END


--# iHotelier3
--1. Url
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.url') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.url'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--2. username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.username') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.username'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--3. password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.password') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.password'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--4. reply to
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.replyTo') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.replyTo'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where [value] = 'true'),
                 GETDATE(),  GETDATE())
    END

--5. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.useSoap2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.useSoap2'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where value = 'true'),
                 GETDATE(),  GETDATE())
    END

--6. DailyBAR.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--7. LRVatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--8. LRVatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where Config_Parameter_ID in (select Config_Parameter_ID
                                            from [dbo].[Config_Parameter]
                                            where name = 'pacman.integration.iHotelier3.LRVatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--9. BarByLOSatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.BarByLOSatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.BarByLOSatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--10. BarByLOSatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.BarByLOSatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.BarByLOSatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--11. RoomTypeOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--12. HotelOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.HotelOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.HotelOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--13. Fplos.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.Fplos.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.Fplos.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--14. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.async') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.async'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--15. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.BarByLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.BarByLOS.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--16. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.BarFplosByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.BarFplosByRoomType.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--17. DailyBAR.dailybarRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.dailybarRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.dailybarRateCode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--18. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.DailyBAR.dailybarRoundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.dailybarRoundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--19. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.miscAdjustment') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.miscAdjustment'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--20. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.newDailyBARFormat') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.newDailyBARFormat'),
                'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
                                 from [dbo].[Config_Parameter_Predefined_Value]
                                 where value = 'true'),
                 GETDATE(),  GETDATE())
    END


--21. DailyBAR.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--22. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.useDeltaForDifferential') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.useDeltaForDifferential'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--23. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--24. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.Fplos.fplosAtRoomCategory') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.Fplos.fplosAtRoomCategory'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--25. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.Fplos.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.Fplos.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--26. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.HotelOverbooking.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.HotelOverbooking.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--27. HotelOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.HotelOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.HotelOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--28. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--29. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.includeCeilingDeltaMaxSold'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--30. LRVatRoomClass.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomClass.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--31. LRVatRoomClass.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.LRVatRoomClass.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--32. LRVatRoomClass.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomClass.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomClass.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--33. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.LRVatRoomType.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--34. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.includeCeilingDeltaMaxSold '),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--35. LRVatRoomType.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomType.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--36. LRVatRoomType.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomType.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--37. LRVatRoomType.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRVatRoomType.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRVatRoomType.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--38. RoomTypeOverbooking.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--39. RoomTypeOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.RoomTypeOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--40. propertycode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.propertycode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.propertycode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--41. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.reverseFplosTranslation') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.reverseFplosTranslation'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--42. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.useSoapChunks') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.useSoapChunks'),
                'pacman', 'false', (Select Config_Parameter_Predefined_Value_Id
                                    From [dbo].[Config_Parameter_Predefined_Value]
                                    where [Config_Parameter_Predefined_Value_Type_ID] =
                                          (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                           from [dbo].[Config_Parameter_Predefined_Value_Type]
                                           where code = 'boolean')
                                      AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--43. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.useCustomMessage') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.useCustomMessage'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--44. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.soapChunkSize') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.soapChunkSize'),
                'pacman', '1000', NULL,
                 GETDATE(),  GETDATE())
    END

--45. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--46. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.BARByLOSByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.BARByLOSByRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--47. customRateAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.customRateAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.customRateAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--48. customAvailAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.customAvailAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.customAvailAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--49. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.useYieldCurrencyForDailyBar') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.useYieldCurrencyForDailyBar'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--50. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DeferredDecisionDeliverySeconds') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DeferredDecisionDeliverySeconds'),
                'pacman', '0', NULL,
                 GETDATE(),  GETDATE())
    END

--51. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.DailyBAR.AllMessagesSentAsDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.DailyBAR.AllMessagesSentAsDelta'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--52. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.MinLOSChunkedOnArrivalDateRateCode'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--53. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier3.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name =
                       'pacman.integration.iHotelier3.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'DailyBarSelectiveUpload')
                                   AND value = 'None'),
                 GETDATE(),  GETDATE())
    END

--54. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRAControlFPLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRAControlFPLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--55. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.LRAControlMinLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.LRAControlMinLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--56. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.AgileRates.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.AgileRates.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--57. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.ack.includeSoapBody') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.ack.includeSoapBody'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--58. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.UploadAdultsBeyond2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.UploadAdultsBeyond2'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--59. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.UploadChildrenBeyondExtra') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.UploadChildrenBeyondExtra'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--60. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.UploadChildAgeBuckets') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.UploadChildAgeBuckets'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--61. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.ignoreHttpResponse') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.ignoreHttpResponse'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--62. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.useHttpBasicAuth') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.useHttpBasicAuth'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--63. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.async.timeout') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.async.timeout'),
                'pacman', '-1', NULL,
                 GETDATE(),  GETDATE())
    END

--64. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.includeHTNGAsyncHeaders') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.includeHTNGAsyncHeaders'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--65. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.allowTransferEncodingChunked') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.allowTransferEncodingChunked'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--66. clientcode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.clientcode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.clientcode'),
                'pacman', ' ', NULL,
                 GETDATE(),  GETDATE())
    END

--67. inboundUsername
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.inboundUsername') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.inboundUsername'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--68. inboundPassword
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.inboundPassword') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.inboundPassword'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--69. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.optimizedDailyBarAgileRateUpload') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.optimizedDailyBarAgileRateUpload'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--70. UploadCurrencyForDailyBAR
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.UploadCurrencyForDailyBAR') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.UploadCurrencyForDailyBAR'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--71. UploadCurrencyForLRV
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.UploadCurrencyForLRV') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.UploadCurrencyForLRV'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--72. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier3.ManualRestrictions.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier3.ManualRestrictions.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END


--# iHotelier4
--1. Url
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.url') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.url'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--2. username
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.username') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.username'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--3. password
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.password') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.password'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--4. reply to
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.replyTo') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.replyTo'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where [value] = 'true'),
                 GETDATE(),  GETDATE())
    END

--5. useSoap2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.useSoap2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.useSoap2'),
                'pacman', 'true', (select [Config_Parameter_Predefined_Value_ID]
                                   from [dbo].[Config_Parameter_Predefined_Value]
                                   where value = 'true'),
                 GETDATE(),  GETDATE())
    END

--6. DailyBAR.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--7. LRVatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--8. LRVatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where Config_Parameter_ID in (select Config_Parameter_ID
                                            from [dbo].[Config_Parameter]
                                            where name = 'pacman.integration.iHotelier4.LRVatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--9. BarByLOSatRoomType.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.BarByLOSatRoomType.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.BarByLOSatRoomType.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--10. BarByLOSatRoomClass.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.BarByLOSatRoomClass.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.BarByLOSatRoomClass.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--11. RoomTypeOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--12. HotelOverbooking.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.HotelOverbooking.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.HotelOverbooking.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--13. Fplos.alternateURL
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.Fplos.alternateURL') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.Fplos.alternateURL'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--14. Async
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.async') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.async'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--15. BarByLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.BarByLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.BarByLOS.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--16. BarFplosByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.BarFplosByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.BarFplosByRoomType.uploadtype'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'uploadtype')
                                     AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--17. DailyBAR.dailybarRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.dailybarRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.dailybarRateCode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--18. DailyBAR.dailybarRoundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.DailyBAR.dailybarRoundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.dailybarRoundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--19. DailyBAR.miscAdjustment
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.miscAdjustment') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.miscAdjustment'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--20. DailyBAR.newDailyBARFormat
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.newDailyBARFormat') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.newDailyBARFormat'),
                'pacman', NULL, (select [Config_Parameter_Predefined_Value_ID]
                                 from [dbo].[Config_Parameter_Predefined_Value]
                                 where value = 'true'),
                 GETDATE(),  GETDATE())
    END


--21. DailyBAR.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--22. DailyBAR.useDeltaForDifferential
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.useDeltaForDifferential') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.useDeltaForDifferential'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--23. DailyBAR.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--24. Fplos.fplosAtRoomCategory
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.Fplos.fplosAtRoomCategory') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.Fplos.fplosAtRoomCategory'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--25. Fplos.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.Fplos.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.Fplos.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--26. HotelOverbooking.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.HotelOverbooking.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.HotelOverbooking.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--27. HotelOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.HotelOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.HotelOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--28. LRVatRoomClass.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.LRVatRoomClass.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--29. LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.LRVatRoomClass.includeCeilingDeltaMaxSold') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.includeCeilingDeltaMaxSold'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--30. LRVatRoomClass.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomClass.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--31. LRVatRoomClass.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.LRVatRoomClass.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--32. LRVatRoomClass.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomClass.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomClass.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--33. LRVatRoomType.consolidateCeilingDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.LRVatRoomType.consolidateCeilingDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.consolidateCeilingDelta'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'lrvConsolidationType')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--34. LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.LRVatRoomType.includeCeilingDeltaMaxSold ') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.includeCeilingDeltaMaxSold '),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--35. LRVatRoomType.roundingPrecision
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomType.roundingPrecision') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.roundingPrecision'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--36. LRVatRoomType.taxAdjustmentValue
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomType.taxAdjustmentValue') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.taxAdjustmentValue'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--37. LRVatRoomType.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRVatRoomType.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRVatRoomType.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--38. RoomTypeOverbooking.uploadType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.uploadType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.uploadType'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--39. RoomTypeOverbooking.valueType
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.valueType') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.RoomTypeOverbooking.valueType'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--40. propertycode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.propertycode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.propertycode'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--41. reverseFplosTranslation
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.reverseFplosTranslation') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.reverseFplosTranslation'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--42. useSoapChunks
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.useSoapChunks') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.useSoapChunks'),
                'pacman', 'false', (Select Config_Parameter_Predefined_Value_Id
                                    From [dbo].[Config_Parameter_Predefined_Value]
                                    where [Config_Parameter_Predefined_Value_Type_ID] =
                                          (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                           from [dbo].[Config_Parameter_Predefined_Value_Type]
                                           where code = 'boolean')
                                      AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--43. useCustomMessage
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.useCustomMessage') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.useCustomMessage'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--44. soapChunkSize
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.soapChunkSize') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.soapChunkSize'),
                'pacman', '1000', NULL,
                 GETDATE(),  GETDATE())
    END

--45. MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--46. BARByLOSByRoomType.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.BARByLOSByRoomType.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.BARByLOSByRoomType.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--47. customRateAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.customRateAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.customRateAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--48. customAvailAction
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.customAvailAction') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.customAvailAction'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--49. useYieldCurrencyForDailyBar
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.useYieldCurrencyForDailyBar') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.useYieldCurrencyForDailyBar'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--50. DeferredDecisionDeliverySeconds
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DeferredDecisionDeliverySeconds') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DeferredDecisionDeliverySeconds'),
                'pacman', '0', NULL,
                 GETDATE(),  GETDATE())
    END

--51. DailyBAR.AllMessagesSentAsDelta
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.DailyBAR.AllMessagesSentAsDelta') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.DailyBAR.AllMessagesSentAsDelta'),
                'pacman', 'true', (Select Config_Parameter_Predefined_Value_Id
                                   From [dbo].[Config_Parameter_Predefined_Value]
                                   where [Config_Parameter_Predefined_Value_Type_ID] =
                                         (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                          from [dbo].[Config_Parameter_Predefined_Value_Type]
                                          where code = 'boolean')
                                     AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--52. MinLOSChunkedOnArrivalDateRateCode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.MinLOSChunkedOnArrivalDateRateCode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.MinLOSChunkedOnArrivalDateRateCode'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--53. DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name =
                                                    'pacman.integration.iHotelier4.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name =
                       'pacman.integration.iHotelier4.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'DailyBarSelectiveUpload')
                                   AND value = 'None'),
                 GETDATE(),  GETDATE())
    END

--54. LRAControlFPLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRAControlFPLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRAControlFPLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--55. LRAControlMinLOS.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.LRAControlMinLOS.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.LRAControlMinLOS.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--56. AgileRates.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.AgileRates.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.AgileRates.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END

--57. ack.includeSoapBody
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.ack.includeSoapBody') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.ack.includeSoapBody'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--58. UploadAdultsBeyond2
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.UploadAdultsBeyond2') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.UploadAdultsBeyond2'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--59. UploadChildrenBeyondExtra
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.UploadChildrenBeyondExtra') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.UploadChildrenBeyondExtra'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--60. UploadChildAgeBuckets
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.UploadChildAgeBuckets') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.UploadChildAgeBuckets'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--61. ignoreHttpResponse
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.ignoreHttpResponse') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.ignoreHttpResponse'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--62. useHttpBasicAuth
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.useHttpBasicAuth') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.useHttpBasicAuth'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--63. async.timeout
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.async.timeout') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.async.timeout'),
                'pacman', '-1', NULL,
                 GETDATE(),  GETDATE())
    END

--64. includeHTNGAsyncHeaders
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.includeHTNGAsyncHeaders') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.includeHTNGAsyncHeaders'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--65. allowTransferEncodingChunked
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.allowTransferEncodingChunked') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.allowTransferEncodingChunked'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'true'),
                 GETDATE(),  GETDATE())
    END

--66. clientcode
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.clientcode') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.clientcode'),
                'pacman', ' ', NULL,
                 GETDATE(),  GETDATE())
    END

--67. inboundUsername
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.inboundUsername') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.inboundUsername'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--68. inboundPassword
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.inboundPassword') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.inboundPassword'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--69. optimizedDailyBarAgileRateUpload
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.optimizedDailyBarAgileRateUpload') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.optimizedDailyBarAgileRateUpload'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'boolean')
                                   AND value = 'false'),
                 GETDATE(),  GETDATE())
    END

--70. UploadCurrencyForDailyBAR
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.UploadCurrencyForDailyBAR') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.UploadCurrencyForDailyBAR'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--71. UploadCurrencyForLRV
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.UploadCurrencyForLRV') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.UploadCurrencyForLRV'),
                'pacman', NULL, NULL,
                 GETDATE(),  GETDATE())
    END

--72. ManualRestrictions.uploadtype
if not exists(select *
              from [dbo].[Config_Parameter_Value]
              where [Config_Parameter_ID] in (select [Config_Parameter_ID]
                                              from [dbo].[Config_Parameter]
                                              where name = 'pacman.integration.iHotelier4.ManualRestrictions.uploadtype') AND Context = 'pacman')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter_Value] ([Config_Parameter_ID], [Context], [FixedValue],
                                                    [Config_Parameter_Predefined_Value_ID],
                                                     [Created_DTTM], 
                                                    [Last_Updated_DTTM])
        VALUES ((select [Config_Parameter_ID]
                 from [dbo].[Config_Parameter]
                 where name = 'pacman.integration.iHotelier4.ManualRestrictions.uploadtype'),
                'pacman', NULL, (Select Config_Parameter_Predefined_Value_Id
                                 From [dbo].[Config_Parameter_Predefined_Value]
                                 where [Config_Parameter_Predefined_Value_Type_ID] =
                                       (Select TOP (1) [Config_Parameter_Predefined_Value_Type_ID]
                                        from [dbo].[Config_Parameter_Predefined_Value_Type]
                                        where code = 'uploadtype')
                                   AND value = 'none'),
                 GETDATE(),  GETDATE())
    END








