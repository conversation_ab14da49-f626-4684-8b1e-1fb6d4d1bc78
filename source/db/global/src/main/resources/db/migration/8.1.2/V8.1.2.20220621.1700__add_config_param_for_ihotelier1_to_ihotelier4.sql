INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID]
         from [dbo].[Config_Parameter_Category]
         where Category_Name = 'Outbound Connection Details'),
        'iHotelier1', N'Outbound iHotelier1 Connection Details group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data'),
        'iHotelier1', N'Outbound iHotelier1 group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID]
         from [dbo].[Config_Parameter_Category]
         where Category_Name = 'Outbound Connection Details'),
        'iHotelier2', N'Outbound iHotelier2 Connection Details group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data'),
        'iHotelier2', N'Outbound iHotelier2 group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID]
         from [dbo].[Config_Parameter_Category]
         where Category_Name = 'Outbound Connection Details'),
        'iHotelier3', N'Outbound iHotelier3 Connection Details group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data'),
        'iHotelier3', N'Outbound iHotelier3 group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID]
         from [dbo].[Config_Parameter_Category]
         where Category_Name = 'Outbound Connection Details'),
        'iHotelier4', N'Outbound iHotelier4 Connection Details group', getdate(), getdate())

INSERT [dbo].[Config_Parameter_Group] ([Category_ID], [Group_Name], [Description], [CreateDate], [ModifiedDate])
VALUES ((select top 1 [Category_ID] from [dbo].[Config_Parameter_Category] where Category_Name = 'Outbound Data'),
        'iHotelier4', N'Outbound iHotelier4 group', getdate(), getdate())


--#1 DailyBAR.dailybarRateCode

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.dailybarRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.dailybarRateCode',
                'Parameter for the dailybar rate code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#2 DailyBAR.dailybarRoundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.dailybarRoundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.dailybarRoundingPrecision',
                'Parameter for the dailybar rounding precision',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#3 DailyBAR.miscAdjustment
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.miscAdjustment')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.miscAdjustment',
                'The value of the miscelaneous adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#4 DailyBAR.taxAdjustmentValue

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.taxAdjustmentValue',
                'Parameter for the tax adjustment',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #5 DailyBAR.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#6 propertyCode


if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.propertycode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.propertycode',
                'pacman.integration.ihotelier1.propertycode',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#7 AgileRates.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.AgileRates.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.AgileRates.uploadtype',
                'Indicates if Agile Rate decisions should be sent and if they are full or differential',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #8 async
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.async')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.async',
                'Deliver decisions for.asynchronously',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #9 BarByLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.BarByLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.BarByLOS.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #10 BarFplosByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.BarFplosByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.BarFplosByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #11 DailyBAR.newDailyBARFormat
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.newDailyBARFormat')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.newDailyBARFormat',
                'Toggle to send DailyBAR in the new format',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #12 DailyBAR.useDeltaForDifferential
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.useDeltaForDifferential')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.useDeltaForDifferential',
                'Toggle to send Delta as the RatePlanNotifType for differential files',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #13 Fplos.fplosAtRoomCategory
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.Fplos.fplosAtRoomCategory')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.Fplos.fplosAtRoomCategory',
                'Toggle to send FPLOS with RoomCategory Element',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #14 Fplos.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.Fplos.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.Fplos.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')

    END

-- #15 HotelOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.HotelOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.HotelOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #16 HotelOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.HotelOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.HotelOverbooking.valueType',
                'Whether to send Overbooking or Authorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #17 LRVatRoomClass.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.consolidateCeilingDelta',
                'Select how to send consolidated Ceiling and Delta values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #18 LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #19 LRVatRoomClass.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #20 LRVatRoomClass.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#21  LRVatRoomClass.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#22  LRVatRoomType.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.consolidateCeilingDelta',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#23  LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#24  LRVatRoomType.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#25 LRVatRoomType.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#26  LRVatRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#27  RoomTypeOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.RoomTypeOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.RoomTypeOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#28  RoomTypeOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.RoomTypeOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.RoomTypeOverbooking.valueType',
                'Whether to send Overbooking or Auhthorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#29  reverseFplosTranslation
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.reverseFplosTranslation')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.reverseFplosTranslation',
                'Reverse Y and N meaning',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#30  YieldCurrencyCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.YieldCurrencyCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.YieldCurrencyCode',
                'Set value to override the property yield currency code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#31  useSoapChunks
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.useSoapChunks')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.useSoapChunks',
                'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#32  soapChunkSize
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.soapChunkSize')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.soapChunkSize',
                'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#33  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#34  BARByLOSByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.BARByLOSByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.BARByLOSByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#35  useYieldCurrencyForDailyBar
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.useYieldCurrencyForDailyBar')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.useYieldCurrencyForDailyBar',
                'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#36  DeferredDecisionDeliverySeconds
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DeferredDecisionDeliverySeconds')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DeferredDecisionDeliverySeconds',
                'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#37  DailyBAR.AllMessagesSentAsDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.AllMessagesSentAsDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.AllMessagesSentAsDelta',
                'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#38  MinLOSChunkedOnArrivalDateRateCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.MinLOSChunkedOnArrivalDateRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.MinLOSChunkedOnArrivalDateRateCode',
                'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#39 LRAControlFPLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRAControlFPLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRAControlFPLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#40 LRAControlMinLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRAControlMinLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRAControlMinLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#41 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name =
                    'pacman.integration.ihotelier1.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
                'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. None (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'DailyBarSelectiveUpload'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#42 ack.includeSoapBody
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.ack.includeSoapBody')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.ack.includeSoapBody',
                'Include empty SOAP body tag when sending HTNG decision acknowledgements',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#43 useCustomMessage
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.useCustomMessage')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.useCustomMessage',
                'Set to true to use a custom SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#44 UploadAdultsBeyond2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.UploadAdultsBeyond2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.UploadAdultsBeyond2',
                'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#45 UploadChildrenBeyondExtra
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.UploadChildrenBeyondExtra')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.UploadChildrenBeyondExtra',
                'Include child rates beyond extra child for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#46 optimizedDailyBarAgileRateUpload
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.optimizedDailyBarAgileRateUpload')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.optimizedDailyBarAgileRateUpload',
                'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#47 allowTransferEncodingChunked
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.allowTransferEncodingChunked')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.allowTransferEncodingChunked',
                'Send Transfer Encoding chunked in the SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#48 async.timeout
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.async.timeout')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.async.timeout',
                'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#49 clientcode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.clientcode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.clientcode',
                'The client code to include with HTNG decisions.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#50 ignoreHttpResponse
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.ignoreHttpResponse')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.ignoreHttpResponse',
                'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#51 includeHTNGAsyncHeaders
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.includeHTNGAsyncHeaders')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.includeHTNGAsyncHeaders',
                'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#52 UploadChildAgeBuckets
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.UploadChildAgeBuckets')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.UploadChildAgeBuckets',
                'Use child age buckets for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#53 useHttpBasicAuth
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.useHttpBasicAuth')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.useHttpBasicAuth',
                'Use HTTP Basic Auth in addition to SOAP header authorization.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#54 UploadCurrencyForDailyBAR
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.UploadCurrencyForDailyBAR')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.UploadCurrencyForDailyBAR',
                'Set value to convert DailyBAR decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#55 UploadCurrencyForLRV
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.UploadCurrencyForLRV')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.UploadCurrencyForLRV',
                'Set value to convert LRV decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#56 ManualRestrictions.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.ManualRestrictions.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.ManualRestrictions.uploadtype',
                'Parameter to support full, differential and none type of Manual-Restriction upload.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--Outbound Connection Details Param Starts

--#57 password

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.password')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.password',
                'integration password',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#58 username
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.username')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.username',
                'Integration username',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#59 url

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.url')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.url',
                'Integration URL',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

-- #60 BarByLOSatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.BarByLOSatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.BarByLOSatRoomClass.alternateURL',
                'An alternate url to which to send BarByLOSatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#61  RoomTypeOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.RoomTypeOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.RoomTypeOverbooking.alternateURL',
                'An alternate url to which to send RoomTypeOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#62  HotelOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.HotelOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.HotelOverbooking.alternateURL',
                'An alternate url to which to send HotelOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#63 Fplos.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.Fplos.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.Fplos.alternateURL',
                'An alternate url to which to send Fplos decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#64 customAvailAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.customAvailAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.customAvailAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#65 customRateAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.customRateAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.customRateAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#66 pacman.integration.replyTo
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.replyTo')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.replyTo',
                'The url of the local callback server',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#67 useSoap2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.useSoap2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.useSoap2',
                'Set to true to use SOAP 1.2 protocol',
                (SELECT Config_Parameter_Predefined_Value_Type_ID
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'
                ),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#68 inboundPassword
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.inboundPassword')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.inboundPassword',
                'The password to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#69 inboundUsername
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.inboundUsername')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.inboundUsername',
                'The username to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#70 DailyBAR.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.DailyBAR.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.DailyBAR.alternateURL',
                'An alternate url to which to send DailyBAR decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#71 LRVatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomType.alternateURL',
                'An alternate url to which to send LRVatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#72 LRVatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.LRVatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.LRVatRoomClass.alternateURL',
                'An alternate url to which to send LRVatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#73 BarByLOSatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier1.BarByLOSatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier1.BarByLOSatRoomType.alternateURL',
                'An alternate url to which to send BarByLOSatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier1'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#1 DailyBAR.dailybarRateCode

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.dailybarRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.dailybarRateCode',
                'Parameter for the dailybar rate code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#2 DailyBAR.dailybarRoundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.dailybarRoundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.dailybarRoundingPrecision',
                'Parameter for the dailybar rounding precision',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#3 DailyBAR.miscAdjustment
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.miscAdjustment')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.miscAdjustment',
                'The value of the miscelaneous adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#4 DailyBAR.taxAdjustmentValue

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.taxAdjustmentValue',
                'Parameter for the tax adjustment',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #5 DailyBAR.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#6 propertyCode


if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.propertycode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.propertycode',
                'pacman.integration.ihotelier2.propertycode',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#7 AgileRates.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.AgileRates.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.AgileRates.uploadtype',
                'Indicates if Agile Rate decisions should be sent and if they are full or differential',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #8 async
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.async')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.async',
                'Deliver decisions for.asynchronously',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #9 BarByLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.BarByLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.BarByLOS.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #10 BarFplosByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.BarFplosByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.BarFplosByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #11 DailyBAR.newDailyBARFormat
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.newDailyBARFormat')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.newDailyBARFormat',
                'Toggle to send DailyBAR in the new format',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #12 DailyBAR.useDeltaForDifferential
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.useDeltaForDifferential')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.useDeltaForDifferential',
                'Toggle to send Delta as the RatePlanNotifType for differential files',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #13 Fplos.fplosAtRoomCategory
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.Fplos.fplosAtRoomCategory')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.Fplos.fplosAtRoomCategory',
                'Toggle to send FPLOS with RoomCategory Element',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #14 Fplos.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.Fplos.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.Fplos.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')

    END

-- #15 HotelOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.HotelOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.HotelOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #16 HotelOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.HotelOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.HotelOverbooking.valueType',
                'Whether to send Overbooking or Authorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #17 LRVatRoomClass.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.consolidateCeilingDelta',
                'Select how to send consolidated Ceiling and Delta values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #18 LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #19 LRVatRoomClass.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #20 LRVatRoomClass.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#21  LRVatRoomClass.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#22  LRVatRoomType.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.consolidateCeilingDelta',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#23  LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#24  LRVatRoomType.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#25 LRVatRoomType.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#26  LRVatRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#27  RoomTypeOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.RoomTypeOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.RoomTypeOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#28  RoomTypeOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.RoomTypeOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.RoomTypeOverbooking.valueType',
                'Whether to send Overbooking or Auhthorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#29  reverseFplosTranslation
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.reverseFplosTranslation')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.reverseFplosTranslation',
                'Reverse Y and N meaning',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#30  YieldCurrencyCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.YieldCurrencyCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.YieldCurrencyCode',
                'Set value to override the property yield currency code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#31  useSoapChunks
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.useSoapChunks')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.useSoapChunks',
                'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#32  soapChunkSize
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.soapChunkSize')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.soapChunkSize',
                'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#33  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#34  BARByLOSByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.BARByLOSByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.BARByLOSByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#35  useYieldCurrencyForDailyBar
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.useYieldCurrencyForDailyBar')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.useYieldCurrencyForDailyBar',
                'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#36  DeferredDecisionDeliverySeconds
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DeferredDecisionDeliverySeconds')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DeferredDecisionDeliverySeconds',
                'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#37  DailyBAR.AllMessagesSentAsDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.AllMessagesSentAsDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.AllMessagesSentAsDelta',
                'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#38  MinLOSChunkedOnArrivalDateRateCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.MinLOSChunkedOnArrivalDateRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.MinLOSChunkedOnArrivalDateRateCode',
                'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#39 LRAControlFPLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRAControlFPLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRAControlFPLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#40 LRAControlMinLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRAControlMinLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRAControlMinLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#41 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name =
                    'pacman.integration.ihotelier2.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
                'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. None (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'DailyBarSelectiveUpload'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#42 ack.includeSoapBody
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.ack.includeSoapBody')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.ack.includeSoapBody',
                'Include empty SOAP body tag when sending HTNG decision acknowledgements',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#43 useCustomMessage
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.useCustomMessage')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.useCustomMessage',
                'Set to true to use a custom SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#44 UploadAdultsBeyond2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.UploadAdultsBeyond2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.UploadAdultsBeyond2',
                'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#45 UploadChildrenBeyondExtra
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.UploadChildrenBeyondExtra')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.UploadChildrenBeyondExtra',
                'Include child rates beyond extra child for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#46 optimizedDailyBarAgileRateUpload
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.optimizedDailyBarAgileRateUpload')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.optimizedDailyBarAgileRateUpload',
                'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#47 allowTransferEncodingChunked
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.allowTransferEncodingChunked')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.allowTransferEncodingChunked',
                'Send Transfer Encoding chunked in the SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#48 async.timeout
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.async.timeout')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.async.timeout',
                'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#49 clientcode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.clientcode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.clientcode',
                'The client code to include with HTNG decisions.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#50 ignoreHttpResponse
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.ignoreHttpResponse')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.ignoreHttpResponse',
                'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#51 includeHTNGAsyncHeaders
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.includeHTNGAsyncHeaders')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.includeHTNGAsyncHeaders',
                'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#52 UploadChildAgeBuckets
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.UploadChildAgeBuckets')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.UploadChildAgeBuckets',
                'Use child age buckets for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#53 useHttpBasicAuth
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.useHttpBasicAuth')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.useHttpBasicAuth',
                'Use HTTP Basic Auth in addition to SOAP header authorization.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#54 UploadCurrencyForDailyBAR
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.UploadCurrencyForDailyBAR')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.UploadCurrencyForDailyBAR',
                'Set value to convert DailyBAR decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#55 UploadCurrencyForLRV
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.UploadCurrencyForLRV')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.UploadCurrencyForLRV',
                'Set value to convert LRV decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#56 ManualRestrictions.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.ManualRestrictions.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.ManualRestrictions.uploadtype',
                'Parameter to support full, differential and none type of Manual-Restriction upload.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--Outbound Connection Details Param Starts

--#57 password

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.password')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.password',
                'integration password',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#58 username
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.username')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.username',
                'Integration username',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#59 url

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.url')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.url',
                'Integration URL',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

-- #60 BarByLOSatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.BarByLOSatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.BarByLOSatRoomClass.alternateURL',
                'An alternate url to which to send BarByLOSatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#61  RoomTypeOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.RoomTypeOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.RoomTypeOverbooking.alternateURL',
                'An alternate url to which to send RoomTypeOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#62  HotelOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.HotelOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.HotelOverbooking.alternateURL',
                'An alternate url to which to send HotelOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#63 Fplos.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.Fplos.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.Fplos.alternateURL',
                'An alternate url to which to send Fplos decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#64 customAvailAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.customAvailAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.customAvailAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#65 customRateAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.customRateAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.customRateAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#66 pacman.integration.replyTo
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.replyTo')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.replyTo',
                'The url of the local callback server',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#67 useSoap2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.useSoap2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.useSoap2',
                'Set to true to use SOAP 1.2 protocol',
                (SELECT Config_Parameter_Predefined_Value_Type_ID
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'
                ),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#68 inboundPassword
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.inboundPassword')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.inboundPassword',
                'The password to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#69 inboundUsername
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.inboundUsername')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.inboundUsername',
                'The username to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#70 DailyBAR.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.DailyBAR.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.DailyBAR.alternateURL',
                'An alternate url to which to send DailyBAR decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#71 LRVatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomType.alternateURL',
                'An alternate url to which to send LRVatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#72 LRVatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.LRVatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.LRVatRoomClass.alternateURL',
                'An alternate url to which to send LRVatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#73 BarByLOSatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier2.BarByLOSatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier2.BarByLOSatRoomType.alternateURL',
                'An alternate url to which to send BarByLOSatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier2'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#1 DailyBAR.dailybarRateCode

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.dailybarRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.dailybarRateCode',
                'Parameter for the dailybar rate code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#2 DailyBAR.dailybarRoundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.dailybarRoundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.dailybarRoundingPrecision',
                'Parameter for the dailybar rounding precision',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#3 DailyBAR.miscAdjustment
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.miscAdjustment')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.miscAdjustment',
                'The value of the miscelaneous adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#4 DailyBAR.taxAdjustmentValue

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.taxAdjustmentValue',
                'Parameter for the tax adjustment',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #5 DailyBAR.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#6 propertyCode


if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.propertycode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.propertycode',
                'pacman.integration.ihotelier3.propertycode',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#7 AgileRates.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.AgileRates.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.AgileRates.uploadtype',
                'Indicates if Agile Rate decisions should be sent and if they are full or differential',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #8 async
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.async')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.async',
                'Deliver decisions for.asynchronously',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #9 BarByLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.BarByLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.BarByLOS.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #10 BarFplosByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.BarFplosByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.BarFplosByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #11 DailyBAR.newDailyBARFormat
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.newDailyBARFormat')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.newDailyBARFormat',
                'Toggle to send DailyBAR in the new format',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #12 DailyBAR.useDeltaForDifferential
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.useDeltaForDifferential')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.useDeltaForDifferential',
                'Toggle to send Delta as the RatePlanNotifType for differential files',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #13 Fplos.fplosAtRoomCategory
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.Fplos.fplosAtRoomCategory')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.Fplos.fplosAtRoomCategory',
                'Toggle to send FPLOS with RoomCategory Element',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #14 Fplos.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.Fplos.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.Fplos.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')

    END

-- #15 HotelOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.HotelOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.HotelOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #16 HotelOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.HotelOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.HotelOverbooking.valueType',
                'Whether to send Overbooking or Authorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #17 LRVatRoomClass.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.consolidateCeilingDelta',
                'Select how to send consolidated Ceiling and Delta values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #18 LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #19 LRVatRoomClass.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #20 LRVatRoomClass.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#21  LRVatRoomClass.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#22  LRVatRoomType.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.consolidateCeilingDelta',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#23  LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#24  LRVatRoomType.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#25 LRVatRoomType.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#26  LRVatRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#27  RoomTypeOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.RoomTypeOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.RoomTypeOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#28  RoomTypeOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.RoomTypeOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.RoomTypeOverbooking.valueType',
                'Whether to send Overbooking or Auhthorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#29  reverseFplosTranslation
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.reverseFplosTranslation')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.reverseFplosTranslation',
                'Reverse Y and N meaning',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#30  YieldCurrencyCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.YieldCurrencyCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.YieldCurrencyCode',
                'Set value to override the property yield currency code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#31  useSoapChunks
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.useSoapChunks')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.useSoapChunks',
                'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#32  soapChunkSize
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.soapChunkSize')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.soapChunkSize',
                'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#33  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#34  BARByLOSByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.BARByLOSByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.BARByLOSByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#35  useYieldCurrencyForDailyBar
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.useYieldCurrencyForDailyBar')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.useYieldCurrencyForDailyBar',
                'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#36  DeferredDecisionDeliverySeconds
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DeferredDecisionDeliverySeconds')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DeferredDecisionDeliverySeconds',
                'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#37  DailyBAR.AllMessagesSentAsDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.AllMessagesSentAsDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.AllMessagesSentAsDelta',
                'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#38  MinLOSChunkedOnArrivalDateRateCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.MinLOSChunkedOnArrivalDateRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.MinLOSChunkedOnArrivalDateRateCode',
                'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#39 LRAControlFPLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRAControlFPLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRAControlFPLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#40 LRAControlMinLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRAControlMinLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRAControlMinLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#41 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name =
                    'pacman.integration.ihotelier3.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
                'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. None (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'DailyBarSelectiveUpload'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#42 ack.includeSoapBody
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.ack.includeSoapBody')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.ack.includeSoapBody',
                'Include empty SOAP body tag when sending HTNG decision acknowledgements',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#43 useCustomMessage
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.useCustomMessage')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.useCustomMessage',
                'Set to true to use a custom SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#44 UploadAdultsBeyond2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.UploadAdultsBeyond2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.UploadAdultsBeyond2',
                'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#45 UploadChildrenBeyondExtra
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.UploadChildrenBeyondExtra')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.UploadChildrenBeyondExtra',
                'Include child rates beyond extra child for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#46 optimizedDailyBarAgileRateUpload
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.optimizedDailyBarAgileRateUpload')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.optimizedDailyBarAgileRateUpload',
                'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#47 allowTransferEncodingChunked
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.allowTransferEncodingChunked')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.allowTransferEncodingChunked',
                'Send Transfer Encoding chunked in the SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#48 async.timeout
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.async.timeout')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.async.timeout',
                'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#49 clientcode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.clientcode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.clientcode',
                'The client code to include with HTNG decisions.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#50 ignoreHttpResponse
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.ignoreHttpResponse')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.ignoreHttpResponse',
                'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#51 includeHTNGAsyncHeaders
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.includeHTNGAsyncHeaders')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.includeHTNGAsyncHeaders',
                'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#52 UploadChildAgeBuckets
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.UploadChildAgeBuckets')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.UploadChildAgeBuckets',
                'Use child age buckets for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#53 useHttpBasicAuth
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.useHttpBasicAuth')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.useHttpBasicAuth',
                'Use HTTP Basic Auth in addition to SOAP header authorization.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#54 UploadCurrencyForDailyBAR
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.UploadCurrencyForDailyBAR')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.UploadCurrencyForDailyBAR',
                'Set value to convert DailyBAR decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#55 UploadCurrencyForLRV
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.UploadCurrencyForLRV')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.UploadCurrencyForLRV',
                'Set value to convert LRV decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#56 ManualRestrictions.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.ManualRestrictions.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.ManualRestrictions.uploadtype',
                'Parameter to support full, differential and none type of Manual-Restriction upload.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--Outbound Connection Details Param Starts

--#57 password

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.password')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.password',
                'integration password',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#58 username
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.username')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.username',
                'Integration username',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#59 url

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.url')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.url',
                'Integration URL',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

-- #60 BarByLOSatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.BarByLOSatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.BarByLOSatRoomClass.alternateURL',
                'An alternate url to which to send BarByLOSatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#61  RoomTypeOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.RoomTypeOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.RoomTypeOverbooking.alternateURL',
                'An alternate url to which to send RoomTypeOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#62  HotelOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.HotelOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.HotelOverbooking.alternateURL',
                'An alternate url to which to send HotelOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#63 Fplos.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.Fplos.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.Fplos.alternateURL',
                'An alternate url to which to send Fplos decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#64 customAvailAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.customAvailAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.customAvailAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#65 customRateAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.customRateAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.customRateAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#66 pacman.integration.replyTo
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.replyTo')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.replyTo',
                'The url of the local callback server',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#67 useSoap2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.useSoap2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.useSoap2',
                'Set to true to use SOAP 1.2 protocol',
                (SELECT Config_Parameter_Predefined_Value_Type_ID
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'
                ),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#68 inboundPassword
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.inboundPassword')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.inboundPassword',
                'The password to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#69 inboundUsername
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.inboundUsername')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.inboundUsername',
                'The username to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#70 DailyBAR.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.DailyBAR.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.DailyBAR.alternateURL',
                'An alternate url to which to send DailyBAR decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#71 LRVatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomType.alternateURL',
                'An alternate url to which to send LRVatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#72 LRVatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.LRVatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.LRVatRoomClass.alternateURL',
                'An alternate url to which to send LRVatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#73 BarByLOSatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier3.BarByLOSatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier3.BarByLOSatRoomType.alternateURL',
                'An alternate url to which to send BarByLOSatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier3'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#1 DailyBAR.dailybarRateCode

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.dailybarRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.dailybarRateCode',
                'Parameter for the dailybar rate code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#2 DailyBAR.dailybarRoundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.dailybarRoundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.dailybarRoundingPrecision',
                'Parameter for the dailybar rounding precision',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#3 DailyBAR.miscAdjustment
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.miscAdjustment')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.miscAdjustment',
                'The value of the miscelaneous adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'null',
                'null')
    END

--#4 DailyBAR.taxAdjustmentValue

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.taxAdjustmentValue',
                'Parameter for the tax adjustment',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #5 DailyBAR.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#6 propertyCode


if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.propertycode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.propertycode',
                'pacman.integration.ihotelier4.propertycode',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#7 AgileRates.uploadtype

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.AgileRates.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.AgileRates.uploadtype',
                'Indicates if Agile Rate decisions should be sent and if they are full or differential',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #8 async
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.async')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.async',
                'Deliver decisions for.asynchronously',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #9 BarByLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.BarByLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.BarByLOS.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #10 BarFplosByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.BarFplosByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.BarFplosByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #11 DailyBAR.newDailyBARFormat
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.newDailyBARFormat')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.newDailyBARFormat',
                'Toggle to send DailyBAR in the new format',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #12 DailyBAR.useDeltaForDifferential
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.useDeltaForDifferential')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.useDeltaForDifferential',
                'Toggle to send Delta as the RatePlanNotifType for differential files',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #13 Fplos.fplosAtRoomCategory
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.Fplos.fplosAtRoomCategory')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.Fplos.fplosAtRoomCategory',
                'Toggle to send FPLOS with RoomCategory Element',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #14 Fplos.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.Fplos.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.Fplos.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')

    END

-- #15 HotelOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.HotelOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.HotelOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #16 HotelOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.HotelOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.HotelOverbooking.valueType',
                'Whether to send Overbooking or Authorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #17 LRVatRoomClass.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.consolidateCeilingDelta',
                'Select how to send consolidated Ceiling and Delta values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #18 LRVatRoomClass.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #19 LRVatRoomClass.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

-- #20 LRVatRoomClass.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END


--#21  LRVatRoomClass.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#22  LRVatRoomType.consolidateCeilingDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.consolidateCeilingDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.consolidateCeilingDelta',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvConsolidationType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#23  LRVatRoomType.includeCeilingDeltaMaxSold
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.includeCeilingDeltaMaxSold')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.includeCeilingDeltaMaxSold',
                'Toggle to include Ceiling, Deltas, and Max Solds values',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#24  LRVatRoomType.roundingPrecision
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.roundingPrecision')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.roundingPrecision',
                'Precision of the rounding to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#25 LRVatRoomType.taxAdjustmentValue
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.taxAdjustmentValue')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.taxAdjustmentValue',
                'Tax adjustment to be applied',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Double'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#26  LRVatRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'lrvUploadType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#27  RoomTypeOverbooking.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.RoomTypeOverbooking.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.RoomTypeOverbooking.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#28  RoomTypeOverbooking.valueType
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.RoomTypeOverbooking.valueType')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.RoomTypeOverbooking.valueType',
                'Whether to send Overbooking or Auhthorized value',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'overbookingValueType'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#29  reverseFplosTranslation
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.reverseFplosTranslation')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.reverseFplosTranslation',
                'Reverse Y and N meaning',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#30  YieldCurrencyCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.YieldCurrencyCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.YieldCurrencyCode',
                'Set value to override the property yield currency code',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#31  useSoapChunks
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.useSoapChunks')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.useSoapChunks',
                'For HTNG Decision Delivery of SOAP Messages use Multiple Requests (chunks)',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#32  soapChunkSize
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.soapChunkSize')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.soapChunkSize',
                'For HTNG Decision Delivery of SOAP Messages number of records in a chunk',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#33  MinimumLengthOfStaybyRateCodebyRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#34  BARByLOSByRoomType.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.BARByLOSByRoomType.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.BARByLOSByRoomType.uploadtype',
                'If full or differential decisions are to be sent at a decisiontype level',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#35  useYieldCurrencyForDailyBar
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.useYieldCurrencyForDailyBar')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.useYieldCurrencyForDailyBar',
                'Allows specification for the property to receive their decisions with yield currency applied or in their base currency',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#36  DeferredDecisionDeliverySeconds
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DeferredDecisionDeliverySeconds')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DeferredDecisionDeliverySeconds',
                'Deferred delivery for chunked delivery of decisions set in seconds between 0-999 based on sync communication.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#37  DailyBAR.AllMessagesSentAsDelta
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.AllMessagesSentAsDelta')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.AllMessagesSentAsDelta',
                'Some Integrations have unusual Daily BAR overlay message handling, when the message is presented as RatePlan RatePlanNotifType=Overlay all existing seasons are removed. An issue was highlighted for.CR whereby they were removing our rates where sent as Overlay when messages were chunked. Based on this logic only the last chunk was retained. By setting this parameter to true it will force upload type to DELTA.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#38  MinLOSChunkedOnArrivalDateRateCode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.MinLOSChunkedOnArrivalDateRateCode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.MinLOSChunkedOnArrivalDateRateCode',
                'When true MinLOS decisions will be chunked on Rate code arrival date roomtype and chunking will ensure that all decisions for arrival dates will fall in one chunk.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#39 LRAControlFPLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRAControlFPLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRAControlFPLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#40 LRAControlMinLOS.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRAControlMinLOS.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRAControlMinLOS.uploadtype',
                'If full or differential decisions are to be sent',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#41 DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name =
                    'pacman.integration.ihotelier4.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged',
                'Fetch DailyBAR decisions for an occupancy date: 1. By room class - send decisions for all room types of that room class whose at least one room type decision is changed. 2. For all room classes - send decisions for all room types irrespective of room class even if decision of just one room type is changed. 3. None (this feature will not be applicable) This is applicable only for HTNG outbounds , NON-ESA ,Non-Hilton clients when newDailyBARFormat is true.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'DailyBarSelectiveUpload'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#42 ack.includeSoapBody
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.ack.includeSoapBody')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.ack.includeSoapBody',
                'Include empty SOAP body tag when sending HTNG decision acknowledgements',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#43 useCustomMessage
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.useCustomMessage')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.useCustomMessage',
                'Set to true to use a custom SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#44 UploadAdultsBeyond2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.UploadAdultsBeyond2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.UploadAdultsBeyond2',
                'Include adult rates beyond 2 adults for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#45 UploadChildrenBeyondExtra
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.UploadChildrenBeyondExtra')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.UploadChildrenBeyondExtra',
                'Include child rates beyond extra child for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#46 optimizedDailyBarAgileRateUpload
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.optimizedDailyBarAgileRateUpload')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.optimizedDailyBarAgileRateUpload',
                'Toggles the new logic of merging daily bar and agile rate decisions with similar rates into date range based decisions.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#47 allowTransferEncodingChunked
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.allowTransferEncodingChunked')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.allowTransferEncodingChunked',
                'Send Transfer Encoding chunked in the SOAP message',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#48 async.timeout
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.async.timeout')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.async.timeout',
                'Time to wait for an async response before timing out. Value is in seconds. Ignored if value is <= 0',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Int'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#49 clientcode
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.clientcode')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.clientcode',
                'The client code to include with HTNG decisions.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#50 ignoreHttpResponse
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.ignoreHttpResponse')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.ignoreHttpResponse',
                'When trying to certify Maestro we found that we were not able to read the response. This toggle will allow us to fake a response if we cannot read it. ',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#51 includeHTNGAsyncHeaders
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.includeHTNGAsyncHeaders')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.includeHTNGAsyncHeaders',
                'Allow HTNG messages to be sent with the elements wsa:ReplyTo, htng:CorrelationID, htng:ReplyTo',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#52 UploadChildAgeBuckets
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.UploadChildAgeBuckets')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.UploadChildAgeBuckets',
                'Use child age buckets for HTNG daily BAR decisions',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#53 useHttpBasicAuth
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.useHttpBasicAuth')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.useHttpBasicAuth',
                'Use HTTP Basic Auth in addition to SOAP header authorization.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#54 UploadCurrencyForDailyBAR
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.UploadCurrencyForDailyBAR')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.UploadCurrencyForDailyBAR',
                'Set value to convert DailyBAR decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#55 UploadCurrencyForLRV
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.UploadCurrencyForLRV')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.UploadCurrencyForLRV',
                'Set value to convert LRV decisions into this currency code.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--#56 ManualRestrictions.uploadtype
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.ManualRestrictions.uploadtype')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.ManualRestrictions.uploadtype',
                'Parameter to support full, differential and none type of Manual-Restriction upload.',
                (SELECT TOP 1 [Config_Parameter_Predefined_Value_Type_ID]
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'uploadtype'),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Data')),
                'NULL',
                'NULL')
    END

--Outbound Connection Details Param Starts

--#57 password

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.password')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.password',
                'integration password',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#58 username
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.username')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.username',
                'Integration username',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#59 url

if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.url')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.url',
                'Integration URL',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

-- #60 BarByLOSatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.BarByLOSatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.BarByLOSatRoomClass.alternateURL',
                'An alternate url to which to send BarByLOSatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#61  RoomTypeOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.RoomTypeOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.RoomTypeOverbooking.alternateURL',
                'An alternate url to which to send RoomTypeOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#62  HotelOverbooking.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.HotelOverbooking.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.HotelOverbooking.alternateURL',
                'An alternate url to which to send HotelOverbooking decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#63 Fplos.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.Fplos.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.Fplos.alternateURL',
                'An alternate url to which to send Fplos decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#64 customAvailAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.customAvailAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.customAvailAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#65 customRateAction
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.customRateAction')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.customRateAction',
                'The custom action for Daily Bar decisions, if action is not provided the default used will be: http://htng.org/PWSWG/2010/12/RatePlan_SubmitRequest',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#66 pacman.integration.replyTo
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.replyTo')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.replyTo',
                'The url of the local callback server',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#67 useSoap2
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.useSoap2')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.useSoap2',
                'Set to true to use SOAP 1.2 protocol',
                (SELECT Config_Parameter_Predefined_Value_Type_ID
                 FROM [dbo].[Config_Parameter_Predefined_Value_Type]
                 WHERE code = 'boolean'
                ),
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Boolean'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#68 inboundPassword
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.inboundPassword')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.inboundPassword',
                'The password to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#69 inboundUsername
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.inboundUsername')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.inboundUsername',
                'The username to use for authenticating incoming HTNG messages.',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END


--#70 DailyBAR.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.DailyBAR.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.DailyBAR.alternateURL',
                'An alternate url to which to send DailyBAR decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#71 LRVatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomType.alternateURL',
                'An alternate url to which to send LRVatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#72 LRVatRoomClass.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.LRVatRoomClass.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.LRVatRoomClass.alternateURL',
                'An alternate url to which to send LRVatRoomClass decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END

--#73 BarByLOSatRoomType.alternateURL
if not exists(select 1
              from [dbo].[Config_Parameter]
              where name = 'pacman.integration.ihotelier4.BarByLOSatRoomType.alternateURL')
    BEGIN
        INSERT INTO [dbo].[Config_Parameter] ([Name], [Description], [Config_Parameter_Predefined_Value_Type_ID],
                                              [CreateDate], [ModifiedDate], [Config_Parameter_Type_ID], [Group_ID],
                                              [Value_Constraints], [Default_Value])
        VALUES ('pacman.integration.ihotelier4.BarByLOSatRoomType.alternateURL',
                'An alternate url to which to send BarByLOSatRoomType decisions',
                null,
                getdate(),
                getdate(),
                (select top 1 [Param_Type_ID] from [dbo].[Config_Parameter_Type] where Param_Type = 'Char'),
                (select top 1 [Group_ID]
                 from [dbo].[Config_Parameter_Group]
                 where Group_Name = 'iHotelier4'
                   and Category_ID = (select top 1 [Category_ID]
                                      from [dbo].[Config_Parameter_Category]
                                      where Category_Name = 'Outbound Connection Details')),
                'NULL',
                'NULL')
    END