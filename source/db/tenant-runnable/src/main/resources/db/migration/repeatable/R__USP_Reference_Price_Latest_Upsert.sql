DROP PROCEDURE IF EXISTS [dbo].[usp_Reference_Price_Latest_Upsert]
GO

DROP TYPE IF EXISTS [dbo].[Reference_Price_Latest_Batch]
GO
CREATE TYPE dbo.Reference_Price_Latest_Batch AS TABLE
(

    [Arrival_DT]        [date],
    [Accom_Class_ID]    [int],
    [Forecast_Group_ID] [int],
    [FROM_DTA]          [int],
    [LOS]               [int],
    [REFERENCE_PRICE]   [numeric](19, 5),
    [BDE_DT]            [date]
);
GO

CREATE PROCEDURE usp_Reference_Price_Latest_Upsert @Reference_Price_Latest_Batch Reference_Price_Latest_Batch READONLY
AS
BEGIN
    MERGE INTO [dbo].[Reference_Price_Latest] AS Target
    USING @Reference_Price_Latest_Batch AS Source
    ON (Target.[Forecast_Group_ID] = Source.[Forecast_Group_ID]
        AND Target.[Accom_Class_ID] = Source.[Accom_Class_ID]
        AND Target.[Arrival_DT] = Source.[Arrival_DT]
        AND Target.[LOS] = Source.[LOS]
        AND Target.[BDE_DT] = Source.[BDE_DT])
    WHEN MATCHED THEN
        UPDATE
        SET Target.[REFERENCE_PRICE] = Source.[REFERENCE_PRICE],
            Target.[From_DTA] = Source.[From_DTA]

    WHEN NOT MATCHED THEN
        INSERT ( [Forecast_Group_ID]
               , [Accom_Class_ID]
               , [Arrival_DT]
               , [LOS]
               , [FROM_DTA]
               , [REFERENCE_PRICE]
               , [BDE_DT])
        VALUES ( Source.[Forecast_Group_ID]
               , Source.[Accom_Class_ID]
               , Source.[Arrival_DT]
               , Source.[LOS]
               , Source.[FROM_DTA]
               , Source.[REFERENCE_PRICE]
               , Source.[BDE_DT]);
END
GO



