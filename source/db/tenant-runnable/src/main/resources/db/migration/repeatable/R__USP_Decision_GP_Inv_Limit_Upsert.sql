DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_GP_Inv_Limit_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_GP_Inv_Limit_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_GP_Inv_Limit_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Decision_GP_Inv_Limit_Batch]

CREATE TYPE dbo.Decision_GP_Inv_Limit_Batch AS TABLE (
    [Decision_ID]             bigint,
    [Property_ID]             int,
    [Occupancy_DT]            date,
    [Inv_Limit]               int,
    [Created_DTTM]            datetime
);
GO

CREATE PROCEDURE usp_Decision_GP_Inv_Limit_Upsert @Decision_GP_Inv_Limit_Batch Decision_GP_Inv_Limit_Batch READONLY
AS
BEGIN
MERGE INTO [dbo].[Decision_GP_Inv_Limit] AS Target
    USING @Decision_GP_Inv_Limit_Batch AS Source
    ON (
    Target.[Property_ID]		= Source.[Property_ID] AND
    Target.[Occupancy_DT]		= Source.[Occupancy_DT])
    WHEN MATCHED THEN
UPDATE
    SET Target.[Decision_ID]        = Source.[Decision_ID],
    Target.[Inv_Limit]              = Source.[Inv_Limit],
    Target.[Created_DTTM]           = Source.[Created_DTTM]

    WHEN NOT MATCHED THEN

INSERT ([Decision_ID], [Property_ID], [Occupancy_DT], [Inv_Limit], [Created_DTTM])
VALUES (Source.[Decision_ID], Source.[Property_ID], Source.[Occupancy_DT], Source.[Inv_Limit], Source.[Created_DTTM]);
END
GO

CREATE PROCEDURE usp_Decision_GP_Inv_Limit_Update @Decision_GP_Inv_Limit_Batch Decision_GP_Inv_Limit_Batch READONLY
AS
BEGIN
UPDATE [dbo].[Decision_GP_Inv_Limit]
SET [Decision_ID]        = Source.[Decision_ID],
    [Inv_Limit]              = Source.[Inv_Limit],
    [Created_DTTM]           = Source.[Created_DTTM]
FROM @Decision_GP_Inv_Limit_Batch AS Source
WHERE [Decision_GP_Inv_Limit].[Property_ID]		= Source.[Property_ID] AND
    [Decision_GP_Inv_Limit].[Occupancy_DT]		= Source.[Occupancy_DT];
END
GO

CREATE PROCEDURE usp_Decision_GP_Inv_Limit_Insert @Decision_GP_Inv_Limit_Batch Decision_GP_Inv_Limit_Batch READONLY
AS
BEGIN
INSERT INTO [dbo].[Decision_GP_Inv_Limit] (Decision_ID, Property_ID, Occupancy_DT, Inv_Limit, Created_DTTM)
SELECT Decision_ID, Property_ID, Occupancy_DT, Inv_Limit, Created_DTTM
FROM @Decision_GP_Inv_Limit_Batch Decision_GP_Inv_Limit_Batch;
END
GO