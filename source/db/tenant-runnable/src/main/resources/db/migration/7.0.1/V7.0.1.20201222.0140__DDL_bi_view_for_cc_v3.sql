IF EXISTS(SELECT * FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'VW_BUSINESS_INSIGHTS_TRANSACTIONS_CC_V3')
	DROP VIEW [DBO].[VW_BUSINESS_INSIGHTS_TRANSACTIONS_CC_V3]
GO

CREATE VIEW [dbo].[VW_BUSINESS_INSIGHTS_TRANSACTIONS_CC_V3] AS

SELECT rncc.Individual_Trans_ID AS Individual_Trans_ID,
       rncc.file_metadata_id,
       rncc.property_id,
       rncc.reservation_identifier,
       rncc.individual_status,
       rncc.occupancy_dt Occupancy_Date,
       Datepart(dw, rncc.occupancy_dt) day_of_week,
       Datepart(wk, rncc.occupancy_dt) week_of_year,
       rncc.arrival_dt,
       rncc.departure_dt,
       rncc.booking_dt,
       rncc.cancellation_dt,
       rncc.booked_accom_type_code,
       rncc.accom_type_id,

  mkt_seg_code AS Mkt_Seg_Code,
       CASE
           WHEN (rncc.departure_dt = rncc.occupancy_dt) THEN NULL
           ELSE (CASE WHEN (rncc.Gross_Room_Revenue IS NOT NULL) THEN rncc.Gross_Room_Revenue
                      ELSE rncc.Room_Revenue
                 END
		   )
       END AS Room_Revenue,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE rncc.room_revenue
       END AS Room_Revenue_For_RevPAR,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE rncc.food_revenue
       END AS Food_Revenue,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE rncc.beverage_revenue
       END AS Beverage_Revenue,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE rncc.telecom_revenue
       END AS Telecom_Revenue,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE rncc.other_revenue
       END AS Other_Revenue,
       CASE
           WHEN (rncc.departure_dt = rncc.occupancy_dt) THEN NULL
           ELSE (CASE WHEN (rncc.Gross_Room_Revenue IS NOT NULL) THEN rncc.Gross_Room_Revenue
                      ELSE rncc.Room_Revenue
                 END
		   )
       END AS Total_Revenue,
       CASE
           WHEN (rncc.departure_dt = rncc.occupancy_dt) THEN NULL
           ELSE (CASE WHEN (rncc.Room_Revenue_Acquisition_Cost IS NOT NULL) THEN rncc.Room_Revenue_Acquisition_Cost
                      ELSE 0
                 END
		   )
       END AS Total_Channel_Cost,
       CASE
           WHEN (rncc.departure_dt = rncc.occupancy_dt) THEN NULL
           ELSE (CASE WHEN (rncc.Total_Acquisition_Cost IS NOT NULL) THEN rncc.Total_Acquisition_Cost
                      ELSE 0
                 END
		   )
       END AS Total_Acquisition_Cost,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE (CASE WHEN (rncc.Gross_Room_Revenue IS NOT NULL) THEN (rncc.Gross_Room_Revenue - rncc.Room_Revenue_Acquisition_Cost)
                      ELSE rncc.Room_Revenue
                 END
		   )
       END AS Net_Revenue,
       rncc.source_booking,
       rncc.nationality,
       rncc.rate_code,
       CASE
           WHEN (rncc.departure_dt = rncc.occupancy_dt) THEN NULL
           ELSE (CASE WHEN (rncc.Gross_Rate_Value IS NOT NULL) THEN rncc.Gross_Rate_Value
                      ELSE rncc.Rate_Value
                 END
		   )
       END AS Rate_Value,
       CASE
           WHEN rncc.departure_dt = rncc.occupancy_dt THEN NULL
           ELSE (CASE WHEN (rncc.Gross_Rate_Value IS NOT NULL) THEN (rncc.Gross_Rate_Value - rncc.Total_Acquisition_Cost)
                      ELSE rncc.Rate_Value
                 END
		   )
       END AS Net_Rate_Value,
       rncc.room_number,
       rncc.booking_type,
       rncc.number_children,
       rncc.number_adults,
       rncc.createdate_dttm,
       rncc.confirmation_no,
       rncc.channel,
       rncc.channel + '/' + rncc.source_booking AS Channel_Source_Booking,
       rncc.booking_tm,
       CASE
           WHEN rncc.arrival_dt IS NULL
                OR rncc.departure_dt = rncc.occupancy_dt THEN 0
           ELSE 1
       END AS Rooms_Sold,
       CASE rncc.arrival_dt
           WHEN rncc.occupancy_dt THEN 1
           ELSE 0
       END AS Arrivals,
       CASE rncc.departure_dt
           WHEN rncc.occupancy_dt THEN 1
           ELSE 0
       END AS Departures,
       0 AS Cancellations,

  accom_class_code AS Accom_Class_Code,

  accom_type_code AS Accom_Type_Code,
       Datediff(dd, rncc.booking_dt, rncc.arrival_dt) Days_To_Arrival,
       Datediff(dd, rncc.arrival_dt, rncc.departure_dt) Length_Of_Stay,
       0 AS Capacity,
       'I' AS Transaction_Source
FROM
(
SELECT *
FROM   (SELECT rn.[Individual_Trans_ID]
	  ,rn.[File_Metadata_ID]
      ,rn.[Property_ID]
      ,rn.[Reservation_Identifier]
      ,rn.[Individual_Status]
      ,rn.[Arrival_DT]
      ,rn.[Departure_DT]
      ,rn.[Booking_DT]
      ,rn.[Cancellation_DT]
      ,rn.[Booked_Accom_Type_Code]
      ,rn.[Accom_Type_ID]
      ,rn.[Mkt_Seg_ID]
      ,rn.[Room_Revenue]
      ,rn.[Food_Revenue]
      ,rn.[Beverage_Revenue]
      ,rn.[Telecom_Revenue]
      ,rn.[Other_Revenue]
      ,rn.[Total_Revenue]
      ,rn.[Source_Booking]
      ,rn.[Nationality]
      ,rn.[Rate_Code]
      ,rn.[Rate_Value]
      ,rn.[Room_Number]
      ,rn.[Booking_type]
      ,rn.[Number_Children]
      ,rn.[Number_Adults]
      ,rn.[CreateDate_DTTM]
      ,rn.[Confirmation_No]
      ,rn.[Channel]
      ,rn.[Booking_TM]
      ,rn.[Occupancy_DT]
      ,rn.[Persistent_Key]
      ,rn.[Analytics_Booking_Dt]
      ,rn.[Inv_Block_Code]
      ,rn.[Market_Code]
	  ,rn.[Gross_Rate_Value]
	  ,rn.[Gross_Room_Revenue]
	  ,rn.[Room_Revenue_Acquisition_Cost]
	  ,rn.[Total_Acquisition_Cost]

FROM   (SELECT [Individual_Trans_ID]
	  ,[File_Metadata_ID]
      ,[Property_ID]
      ,[Reservation_Identifier]
      ,[Individual_Status]
      ,[Arrival_DT]
      ,[Departure_DT]
      ,[Booking_DT]
      ,[Cancellation_DT]
      ,[Booked_Accom_Type_Code]
      ,[Accom_Type_ID]
      ,[Mkt_Seg_ID]
      ,[Room_Revenue]
      ,[Food_Revenue]
      ,[Beverage_Revenue]
      ,[Telecom_Revenue]
      ,[Other_Revenue]
      ,[Total_Revenue]
      ,[Source_Booking]
      ,[Nationality]
      ,[Rate_Code]
      ,[Rate_Value]
      ,[Room_Number]
      ,[Booking_type]
      ,[Number_Children]
      ,[Number_Adults]
      ,[CreateDate_DTTM]
      ,[Confirmation_No]
      ,[Channel]
      ,[Booking_TM]
      ,[Occupancy_DT]
      ,[Persistent_Key]
      ,[Analytics_Booking_Dt]
      ,[Inv_Block_Code]
      ,[Market_Code]
	  ,[Gross_Rate_Value]
	  ,[Gross_Room_Revenue]
	  ,[Room_Revenue_Acquisition_Cost]
	  ,[Total_Acquisition_Cost]
                FROM   reservation_night
                WHERE  individual_status  IN (
                       'SS','CI','CO','RESERVED','IN_HOUSE','CHECKED_OUT','CHECKED IN','CHECKED OUT' ) AND Arrival_Dt <> departure_dt)
               AS rn
               INNER JOIN (SELECT *
                           FROM
               Ufn_get_analyticalmarketsegid_originalmarketsegment_mapping()
                       ) mkt
                       ON rn.mkt_seg_id = mkt.ana_mkt_id)
       rn
union all
SELECT [Individual_Trans_ID]
      ,[File_Metadata_ID]
      ,[Property_ID]
      ,[Reservation_Identifier]
      ,[Individual_Status]
      ,[Arrival_DT]
      ,[Departure_DT]
      ,[Booking_DT]
      ,[Cancellation_DT]
      ,[Booked_Accom_Type_Code]
      ,[Accom_Type_ID]
      ,[Mkt_Seg_ID]
      ,[Room_Revenue]
      ,[Food_Revenue]
      ,[Beverage_Revenue]
      ,[Telecom_Revenue]
      ,[Other_Revenue]
      ,[Total_Revenue]
      ,[Source_Booking]
      ,[Nationality]
      ,[Rate_Code]
      ,[Rate_Value]
      ,[Room_Number]
      ,[Booking_type]
      ,[Number_Children]
      ,[Number_Adults]
      ,[CreateDate_DTTM]
      ,[Confirmation_No]
      ,[Channel]
      ,[Booking_TM]
      ,Dateadd(day, 1, occupancy_dt) [Occupancy_DT]
      ,[Persistent_Key]
      ,[Analytics_Booking_Dt]
      ,[Inv_Block_Code]
      ,[Market_Code]
	  ,[Gross_Rate_Value]
	  ,[Gross_Room_Revenue]
	  ,[Room_Revenue_Acquisition_Cost]
	  ,[Total_Acquisition_Cost]
FROM   reservation_night
WHERE  individual_status IN ( 'SS','CI','CO','RESERVED','IN_HOUSE','CHECKED_OUT','CHECKED IN','CHECKED OUT'  )
       AND Dateadd(day, 1, occupancy_dt) = departure_dt
) rncc
INNER JOIN mkt_seg ON rncc.mkt_seg_id = mkt_seg.mkt_seg_id
INNER JOIN accom_type ON rncc.accom_type_id = accom_type.accom_type_id
INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
UNION ALL

SELECT rn.Individual_Trans_ID AS Individual_Trans_ID,
       rn.file_metadata_id,
       rn.property_id,
       rn.reservation_identifier,
       rn.individual_status,
       rn.occupancy_dt Occupancy_Date,
       Datepart(dw, rn.occupancy_dt) day_of_week,
       Datepart(wk, rn.occupancy_dt) week_of_year,
       rn.arrival_dt,
       rn.departure_dt,
       rn.booking_dt,
       rn.cancellation_dt,
       rn.booked_accom_type_code,
       rn.accom_type_id,

  (SELECT ms.mkt_seg_code
   FROM mkt_seg ms
   WHERE rn.mkt_seg_id = ms.mkt_seg_id) AS Mkt_Seg_Code,
       NULL AS Room_Revenue,
       NULL AS Room_Revenue_For_RevPAR,
       NULL AS Food_Revenue,
       NULL AS Beverage_Revenue,
       NULL AS Telecom_Revenue,
       NULL AS Other_Revenue,
       NULL AS Total_Revenue,
       NULL AS Total_Channel_Cost,
       NULL AS Total_Acquisition_Cost,
       NULL AS Net_Revenue,
       rn.source_booking,
       rn.nationality,
       rn.rate_code,
       NULL AS Rate_Value,
       NULL AS Net_Rate_Value,
       rn.room_number,
       rn.booking_type,
       rn.number_children,
       rn.number_adults,
       rn.createdate_dttm,
       rn.confirmation_no,
       rn.channel,
       rn.channel + '/' + rn.source_booking AS Channel_Source_Booking,
       rn.booking_tm,
       0 AS Rooms_Sold,
       0 AS Arrivals,
       0 AS Departures,
       1 AS Cancellations,

  accom_class_code AS Accom_Class_Code,

  accom_type_code AS Accom_Type_Code,
       Datediff(dd, rn.booking_dt, rn.arrival_dt) Days_To_Arrival,
       Datediff(dd, rn.arrival_dt, rn.departure_dt) Length_Of_Stay,
       0 AS Capacity,
       'I' AS Transaction_Source
FROM
(SELECT *
   FROM reservation_night rn
   WHERE rn.individual_status IN ('XX',
                                  'NS',
                                  'NO SHOW',
                                  'CANCELLED')
     AND rn.occupancy_dt = rn.arrival_dt
	 AND rn.arrival_DT <> rn.departure_DT) AS rn
INNER JOIN accom_type ON rn.accom_type_id = accom_type.accom_type_id
INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
UNION ALL
SELECT group_block.group_block_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       group_master.property_id,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
            occupancy_dt AS Occupancy_Date,
            day_of_week,
            Datepart(wk, calendar_date) AS week_of_year,
            occupancy_dt AS Arrival_DT,
            Dateadd(dd, 1, occupancy_dt) AS Departure_DT,
            group_master.booking_dt,
            NULL AS Cancellation_DT,
            accom_type_code AS Booked_Accom_Type_Code,
            group_block.accom_type_id,
            mkt_seg_code,
            rate * (blocks - pickup) AS Room_Revenue,
            rate * (blocks - pickup) AS Room_Revenue_For_RevPAR,
            0.0 AS Food_Revenue,
            0.0 AS Beverage_Revenue,
            0.0 AS Telecom_Revenue,
            0.0 AS Other_Revenue,
            rate * (blocks - pickup) AS Total_Revenue,
            0.0 AS Total_Channel_Cost,
            0.0 AS Total_Acquisition_Cost,
            rate * (blocks - pickup) AS Net_Revenue,
            '' AS Source_Booking,
            '' AS Nationality,
            '' AS Rate_Code,
            rate AS Rate_Value,
            rate AS Net_Rate_Value,
            '' AS Room_Number,
            '' AS Booking_type,
            0 AS Number_Children,
            0 AS Number_Adults,
            '' AS CreateDate_DTTM,
            NULL AS Confirmation_No,
            '' AS Channel,
            '' AS Channel_Source_Booking,
            '' AS Booking_TM,
            (blocks - pickup) AS Rooms_Sold,
            (blocks - pickup) AS Arrivals,
            0 AS Departures,
            0 AS Cancellations,
            accom_class_code,
            accom_type_code,
            Datediff(dd, System_Date.dt, occupancy_dt) AS Days_To_Arrival,
            1 AS Length_Of_Stay,
            0 AS Capacity,
            'G' AS Transaction_Source
FROM group_master
INNER JOIN group_block ON group_master.group_id = group_block.group_id
INNER JOIN mkt_seg ON group_master.mkt_seg_id = mkt_seg.mkt_seg_id
INNER JOIN accom_type ON group_block.accom_type_id = accom_type.accom_type_id
INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
INNER JOIN
  (SELECT Max(snapshot_dt) AS 'DT'
   FROM file_metadata
   WHERE record_type_id = 3
     AND process_status_id = 13) System_Date ON 1 = 1
INNER JOIN calendar_dim ON group_block.occupancy_dt = calendar_dim.calendar_date
AND calendar_dim.calendar_date >= System_Date.dt
WHERE group_master.group_status_code = 'DEFINITE'
  AND (blocks - pickup) > 0
UNION ALL
SELECT group_block.group_block_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       group_master.property_id,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
            Dateadd(dd, 1, occupancy_dt) AS Occupancy_Date,
            day_of_week,
            Datepart(wk, calendar_date) AS week_of_year,
            occupancy_dt AS Arrival_DT,
            Dateadd(dd, 1, occupancy_dt) AS Departure_DT,
            group_master.booking_dt,
            NULL AS Cancellation_DT,
            accom_type_code AS Booked_Accom_Type_Code,
            group_block.accom_type_id,
            mkt_seg_code,
            0.0 AS Room_Revenue,
            0.0 AS Room_Revenue_For_RevPAR,
            0.0 AS Food_Revenue,
            0.0 AS Beverage_Revenue,
            0.0 AS Telecom_Revenue,
            0.0 AS Other_Revenue,
            0.0 AS Total_Revenue,
            0.0 AS Total_Channel_Cost,
            0.0 AS Total_Acquisition_Cost,
            0.0 AS Net_Revenue,
            '' AS Source_Booking,
            '' AS Nationality,
            '' AS Rate_Code,
            NULL AS Rate_Value,
            NULL AS Net_Rate_Value,
            '' AS Room_Number,
            '' AS Booking_type,
            0 AS Number_Children,
            0 AS Number_Adults,
            '' AS CreateDate_DTTM,
            NULL AS Confirmation_No,
            '' AS Channel,
            '' AS Channel_Source_Booking,
            '' AS Booking_TM,
            0 AS Rooms_Sold,
            0 AS Arrivals,
            (blocks - pickup) AS Departures,
            0 AS Cancellations,
            accom_class_code,
            accom_type_code,
            Datediff(dd, System_Date.dt, occupancy_dt) + 1 AS Days_To_Arrival,
            1 AS Length_Of_Stay,
            0 AS Capacity,
            'G' AS Transaction_Source
FROM group_master
INNER JOIN group_block ON group_master.group_id = group_block.group_id
INNER JOIN mkt_seg ON group_master.mkt_seg_id = mkt_seg.mkt_seg_id
INNER JOIN accom_type ON group_block.accom_type_id = accom_type.accom_type_id
INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
INNER JOIN
  (SELECT Max(snapshot_dt) AS 'DT'
   FROM file_metadata
   WHERE record_type_id = 3
     AND process_status_id = 13) System_Date ON 1 = 1
INNER JOIN calendar_dim ON Dateadd(dd, 1, group_block.occupancy_dt) = calendar_dim.calendar_date
AND calendar_dim.calendar_date >= System_Date.dt
WHERE group_master.group_status_code = 'DEFINITE'
  AND (blocks - pickup) > 0
UNION ALL
SELECT accom_activity_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       '' AS Property_ID,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
            occupancy_dt AS Occupancy_Date,
            day_of_week,
            Datepart(wk, calendar_date) AS week_of_year,
            NULL AS Arrival_DT,
            NULL AS Departure_DT,
            '1970-01-01' AS Booking_DT,
            NULL AS Cancellation_DT,
            at.accom_type_code AS Booked_Accom_Type_Code,
            aa.accom_type_id,
            NULL AS mkt_seg_code,
            NULL AS Room_Revenue,
            NULL AS Room_Revenue_For_RevPAR,
            NULL AS Food_Revenue,
            NULL AS Beverage_Revenue,
            NULL AS Telecom_Revenue,
            NULL AS Other_Revenue,
            NULL AS Total_Revenue,
            NULL AS Total_Channel_Cost,
            NULL AS Total_Acquisition_Cost,
            NULL AS Net_Revenue,
            NULL AS Source_Booking,
            NULL AS Nationality,
            NULL AS Rate_Code,
            NULL AS Rate_Value,
            NULL AS Net_Rate_Value,
            '' AS Room_Number,
            '' AS Booking_type,
            0 AS Number_Children,
            0 AS Number_Adults,
            '' AS CreateDate_DTTM,
            NULL AS Confirmation_No,
            NULL AS Channel,
            NULL AS Channel_Source_Booking,
            NULL AS Booking_TM,
            NULL AS Rooms_Sold,
            NULL AS Arrivals,
            NULL AS Departures,
            NULL AS Cancellations,
            accom_class_code,
            accom_type_code,
            NULL AS Days_To_Arrival,
            NULL AS Length_Of_Stay,
            aa.accom_capacity - (aa.rooms_not_avail_maint + aa.rooms_not_avail_other) AS Capacity,
            'A' AS Transaction_Source
FROM accom_activity aa
INNER JOIN calendar_dim ON aa.occupancy_dt = calendar_dim.calendar_date
INNER JOIN accom_type AT ON aa.accom_type_id = at.accom_type_id
INNER JOIN accom_class ac ON at.accom_class_id = ac.accom_class_id;