if exists (select * from sys.objects where object_id = object_id(N'[dbo].[usp_FN_AT_MS_Occupancy]'))
drop procedure [dbo].[usp_FN_AT_MS_Occupancy]

GO

/****** Object:  StoredProcedure [dbo].[usp_FN_AT_MS_Occupancy]    Script Date: 7/27/2020 3:35:14 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[usp_FN_AT_MS_Occupancy]
(
	@caughtUpDate date,
	@includeZeroCapacityRT int,
	@property_id int,
	@start_date date,
	@end_date date,
	@includePseudoRT int,
	@MktExcludeCompFlag varchar(5)
)
AS
BEGIN
	-- Important: The table #Temp_FN_AT_MS_Occupancy has to be created by the calling SP!!
	insert into #Temp_FN_AT_MS_Occupancy
	select innerQuery.Property_ID, innerQuery.Occupancy_DT, innerQuery.Accom_Type_ID, SUM(innerQuery.revenue) as Room_Revenue,
			ADR =
				CASE (SUM(innerQuery.Occupancy_NBR))
					WHEN 0 THEN 0
				ELSE 
					SUM(innerQuery.Revenue) / SUM(innerQuery.Occupancy_NBR) 
				END
	FROM
	(
		SELECT occ.Occupancy_FCST_ID, occ.Occupancy_DT, occ.Property_ID, 
			'Forecast' as Forecast_Actual,
			at.Accom_Class_ID, at.Accom_Type_ID,
			maa.Cancellations as accom_type_cancellations,
			maa.No_Shows as accom_type_no_shows,
			occ.Occupancy_NBR,
			occ.revenue,
			ADR =
				CASE (occ.occupancy_nbr)
					WHEN 0 THEN 0
					ELSE ROUND(occ.revenue / occ.occupancy_nbr, 2) 
				END
		FROM Occupancy_FCST AS occ 
			INNER JOIN Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID=1 and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end) and at.System_Default=0
			INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID=1
			INNER JOIN Mkt_Accom_Activity AS maa ON occ.Property_ID = maa.Property_ID 
				AND occ.Occupancy_DT = maa.Occupancy_DT 
				AND maa.Accom_Type_ID = at.Accom_Type_ID 
				AND maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID
			INNER JOIN Mkt_Seg ms on ms.Mkt_Seg_ID = maa.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag,','))
		WHERE maa.Occupancy_DT >= @caughtUpDate
		UNION
		SELECT maa.Accom_Activity_ID, maa.Occupancy_DT, maa.Property_ID,
			'Actual' as Forecast_Actual,
			at.Accom_Class_ID, at.Accom_Type_ID,
			maa.Cancellations  as accom_type_cancellations,
			maa.No_Shows as accom_type_no_shows,
			maa.Rooms_Sold as Occupancy_NBR,
			maa.Room_Revenue as revenue,
			ADR =
				CASE (maa.rooms_sold)
					WHEN 0 THEN 0
					ELSE ROUND(maa.room_revenue / maa.Rooms_Sold, 2) 
				END
		FROM Accom_Activity AS maa
			INNER JOIN Accom_Type AS at ON maa.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID in (select 1 union select case when @includePseudoRT = 1 then 6 else 1 end) 
						 and at.Display_Status_ID in (select 1 union 
													select case when @includeZeroCapacityRT = 1 then 2 else 1 end union
													select case when @includePseudoRT = 1 then 4 else 1 end) 
						and at.System_Default=0
			INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID and ac.Status_ID=1 and ac.System_Default=0
		WHERE maa.Occupancy_DT < @caughtUpDate
	) as innerQuery
	WHERE innerQuery.Property_ID = @property_id
	AND innerQuery.Occupancy_DT BETWEEN @start_date AND @end_date
	GROUP BY Property_ID, Occupancy_DT, Accom_Type_ID
END

GO


