IF EXISTS(SELECT * FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'VW_BUSINESS_INSIGHTS_TRANSACTIONS')
	DROP VIEW [DBO].[VW_BUSINESS_INSIGHTS_TRANSACTIONS]
GO
CREATE VIEW [dbo].[VW_BUSINESS_INSIGHTS_TRANSACTIONS] AS
SELECT rn.Individual_Trans_ID AS Individual_Trans_ID,
       rn.file_metadata_id,
       rn.property_id,
       rn.reservation_identifier,
       rn.individual_status,
       rn.calendar_date Occupancy_Date,
       rn.day_of_week day_of_week,
       Datepart(wk, rn.calendar_date) week_of_year,
       rn.arrival_dt,
       rn.departure_dt,
       rn.booking_dt,
       rn.cancellation_dt,
       rn.booked_accom_type_code,
       rn.accom_type_id,

       (SELECT ms.mkt_seg_code
        FROM mkt_seg ms
        WHERE rn.mkt_seg_id = ms.mkt_seg_id) AS Mkt_Seg_Code,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.room_revenue
           END AS Room_Revenue,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.room_revenue
           END AS Room_Revenue_For_RevPAR,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.food_revenue
           END AS Food_Revenue,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.beverage_revenue
           END AS Beverage_Revenue,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.telecom_revenue
           END AS Telecom_Revenue,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.other_revenue
           END AS Other_Revenue,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.total_revenue
           END AS Total_Revenue,
       CASE
           WHEN (rn.departure_dt = rn.calendar_date
               OR ccer.rate_code IS NOT NULL) THEN NULL
           ELSE (Isnull(cc.fixed_cost, 0)/ DATEDIFF(dd,rn.Arrival_DT,rn.Departure_DT)  + Isnull(cc.percentage_cost, 0) * rn.room_revenue / 100)
           END AS Total_Channel_Cost,
       CASE
           WHEN (rn.departure_dt = rn.calendar_date
               OR ccer.rate_code IS NOT NULL) THEN NULL
           ELSE (Isnull(cc.fixed_cost, 0)/ DATEDIFF(dd,rn.Arrival_DT,rn.Departure_DT) + Isnull(cc.percentage_cost, 0) * rn.rate_value / 100)
           END AS Total_Acquisition_Cost,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE (rn.room_revenue - CASE
                                       WHEN (cc.fixed_cost IS NULL
                                           OR ccer.rate_code IS NOT NULL OR rn.departure_dt <= rn.calendar_date) THEN 0
                                       ELSE cc.fixed_cost/ DATEDIFF(dd,rn.Arrival_DT,rn.Departure_DT)
               END - CASE
                         WHEN (cc.percentage_cost IS NULL
                             OR ccer.rate_code IS NOT NULL) THEN 0
                         ELSE cc.percentage_cost * rn.rate_value / 100
                     END)
           END AS Net_Revenue,
       rn.source_booking,
       rn.nationality,
       rn.rate_code,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE rn.rate_value
           END AS Rate_Value,
       CASE
           WHEN rn.departure_dt = rn.calendar_date THEN NULL
           ELSE (rn.rate_value - CASE
                                     WHEN (cc.fixed_cost IS NULL
                                         OR ccer.rate_code IS NOT NULL OR rn.departure_dt <= rn.calendar_date) THEN 0
                                     ELSE cc.fixed_cost/ DATEDIFF(dd,rn.Arrival_DT,rn.Departure_DT)
               END - CASE
                         WHEN (cc.percentage_cost IS NULL
                             OR ccer.rate_code IS NOT NULL) THEN 0
                         ELSE cc.percentage_cost * rn.rate_value / 100
                     END)
           END AS Net_Rate_Value,
       rn.room_number,
       rn.booking_type,
       rn.number_children,
       rn.number_adults,
       rn.createdate_dttm,
       rn.confirmation_no,
       rn.channel,
       rn.channel + '/' + rn.source_booking AS Channel_Source_Booking,
       rn.booking_tm,
       CASE
           WHEN rn.arrival_dt IS NULL
               OR rn.departure_dt <= rn.calendar_date THEN 0
           ELSE 1
           END AS Rooms_Sold,
       CASE rn.arrival_dt
           WHEN rn.calendar_date THEN 1
           ELSE 0
           END AS Arrivals,
       CASE rn.departure_dt
           WHEN rn.calendar_date THEN 1
           ELSE 0
           END AS Departures,
       0 AS Cancellations,
       0 AS NO_Show,

       (SELECT ac.accom_class_code
        FROM accom_type AT
                 JOIN accom_class ac ON at.accom_class_id = ac.accom_class_id
        WHERE at.accom_type_id = rn.accom_type_id) AS Accom_Class_Code,

       (SELECT at.accom_type_code
        FROM accom_type AT
        WHERE at.accom_type_id = rn.accom_type_id) AS Accom_Type_Code,
       Datediff(dd, rn.booking_dt, rn.arrival_dt) Days_To_Arrival,
       Datediff(dd, rn.arrival_dt, rn.departure_dt) Length_Of_Stay,
       0 AS Capacity,
       'I' AS Transaction_Source
FROM
    (SELECT *
     FROM
         (SELECT rn.[Individual_Trans_ID],rn.[File_Metadata_ID],rn.[Property_ID],rn.[Reservation_Identifier],rn.[Individual_Status],rn.[Arrival_DT],rn.[Departure_DT],rn.[Booking_DT],rn.[Cancellation_DT],rn.[Booked_Accom_Type_Code],rn.[Accom_Type_ID],rn.[Mkt_Seg_ID],
					rn.[Room_Revenue],rn.[Food_Revenue],rn.[Beverage_Revenue],rn.[Telecom_Revenue],rn.[Other_Revenue],rn.[Total_Revenue],rn.[Source_Booking],rn.[Nationality],rn.[Rate_Code],rn.[Rate_Value],rn.[Room_Number],rn.[Booking_type],rn.[Number_Children],
					rn.[Number_Adults],rn.[CreateDate_DTTM],rn.[Confirmation_No],rn.[Channel],rn.[Booking_TM],rn.[Occupancy_DT],rn.[Persistent_Key],rn.[Inv_Block_Code],rn.[Analytics_Booking_Dt],rn.[Market_Code],rn.[Gross_Rate_Value],rn.[Gross_Room_Revenue],
					rn.[Total_Acquisition_Cost],rn.[Room_Revenue_Acquisition_Cost],rn.[Net_Revenue],rn.[Net_Rate],
                 cd.*
          FROM calendar_dim cd
                   JOIN reservation_night rn ON rn.arrival_DT <> rn.departure_DT and cd.calendar_date = rn.occupancy_dt			   
		UNION ALL
		SELECT        -1 AS Individual_Trans_ID, File_Metadata_ID, Property_ID, pdr.Reservation_Identifier, Individual_Status, minMax.min_arrival_DT, minMax.max_departure_DT, Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, pdr.Accom_Type_ID, pdr.Mkt_Seg_ID, 
                         pdr.Room_Revenue, pdr.Food_Revenue, 0.00000 AS Beverage_Revenue, 0.00000 AS Telecom_Revenue, pdr.Other_Revenue, pdr.Total_Revenue, Source_Booking, Nationality, pdr.Rate_Code, pdr.Rate_Value, Room_Number, Booking_type, Number_Children, 
                         Number_Adults, CreateDate_DTTM, Confirmation_No, Channel, Booking_TM, pdr.Occupancy_DT, Persistent_Key, Analytics_Booking_Dt, Inv_Block_Code, pdr.Market_Code, NULL AS Gross_Rate_Value, NULL AS Gross_Room_Revenue, 
                         Total_Acquisition_Cost, Room_Revenue_Acquisition_Cost, NULL AS Net_Revenue, NULL AS Net_Rate, cd.*
		FROM            dbo.post_departure_revenue AS pdr 
						JOIN dbo.Reservation_Night AS rn on pdr.reservation_identifier = rn.Reservation_Identifier
						JOIN (SELECT t.reservation_identifier, min(arrival_DT) AS min_arrival_DT, max(departure_DT) AS max_departure_DT FROM dbo.reservation_night AS t GROUP BY t.reservation_identifier) minMax ON rn.reservation_identifier = minMax.reservation_identifier and rn.occupancy_DT = minMax.min_arrival_DT
						JOIN calendar_dim cd ON pdr.occupancy_dt = cd.calendar_date and rn.Arrival_DT <> rn.Departure_DT
		WHERE			(SELECT [Value] FROM Business_Insights_Config_Param WHERE [Name] = 'pacman.feature.PostDepartureRevenueAdjustmentEnabled') = 'true'
		) AS rn
     WHERE rn.individual_status NOT IN ('XX',
                                        'NS',
                                        'NO SHOW',
                                        'NO_SHOW',
                                        'CANCELLED')
     UNION ALL SELECT rn.[Individual_Trans_ID],rn.[File_Metadata_ID],rn.[Property_ID],rn.[Reservation_Identifier],rn.[Individual_Status],rn.[Arrival_DT],rn.[Departure_DT],rn.[Booking_DT],rn.[Cancellation_DT],rn.[Booked_Accom_Type_Code],rn.[Accom_Type_ID],rn.[Mkt_Seg_ID],
					rn.[Room_Revenue],rn.[Food_Revenue],rn.[Beverage_Revenue],rn.[Telecom_Revenue],rn.[Other_Revenue],rn.[Total_Revenue],rn.[Source_Booking],rn.[Nationality],rn.[Rate_Code],rn.[Rate_Value],rn.[Room_Number],rn.[Booking_type],rn.[Number_Children],
					rn.[Number_Adults],rn.[CreateDate_DTTM],rn.[Confirmation_No],rn.[Channel],rn.[Booking_TM],rn.[Occupancy_DT],rn.[Persistent_Key],rn.[Inv_Block_Code],rn.[Analytics_Booking_Dt],rn.[Market_Code],rn.[Gross_Rate_Value],rn.[Gross_Room_Revenue],
					rn.[Total_Acquisition_Cost],rn.[Room_Revenue_Acquisition_Cost],rn.[Net_Revenue],rn.[Net_Rate],
				cd.*
     FROM reservation_night rn,
          calendar_dim cd
     WHERE rn.individual_status NOT IN ('XX',
                                        'NS',
                                        'NO SHOW',
                                        'NO_SHOW',
                                        'CANCELLED')
       AND cd.calendar_date = rn.departure_dt
       AND Dateadd(DAY, 1, rn.occupancy_dt) = rn.departure_dt) AS rn
        LEFT JOIN channel_cost cc ON CASE
                                         (SELECT TOP 1 channel_cost_source
                                          FROM property
                                          WHERE property_code <> '-1')
                                         WHEN 'CHANNEL' THEN rn.channel
                                         WHEN 'SOURCE_BOOKING' THEN rn.source_booking
                                         WHEN 'RATE_CODE' THEN rn.rate_code
                                         WHEN 'CHANNEL_AND_SOURCE_BOOKING' THEN rn.channel + '/' + rn.source_booking
                                         END = cc.cost_source_name
        LEFT JOIN channel_cost_excluded_rate ccer ON cc.channel_cost_id = ccer.channel_cost_id
        AND rn.rate_code = ccer.rate_code
UNION ALL
SELECT rn.Individual_Trans_ID AS Individual_Trans_ID,
       rn.file_metadata_id,
       rn.property_id,
       rn.reservation_identifier,
       rn.individual_status,
       rn.calendar_date Occupancy_Date,
       rn.day_of_week day_of_week,
       Datepart(wk, rn.occupancy_dt) week_of_year,
       rn.arrival_dt,
       rn.departure_dt,
       rn.booking_dt,
       rn.cancellation_dt,
       rn.booked_accom_type_code,
       rn.accom_type_id,

       (SELECT ms.mkt_seg_code
        FROM mkt_seg ms
        WHERE rn.mkt_seg_id = ms.mkt_seg_id) AS Mkt_Seg_Code,
       NULL AS Room_Revenue,
       NULL AS Room_Revenue_For_RevPAR,
       NULL AS Food_Revenue,
       NULL AS Beverage_Revenue,
       NULL AS Telecom_Revenue,
       NULL AS Other_Revenue,
       NULL AS Total_Revenue,
       NULL AS Total_Channel_Cost,
       NULL AS Total_Acquisition_Cost,
       NULL AS Net_Revenue,
       rn.source_booking,
       rn.nationality,
       rn.rate_code,
       NULL AS Rate_Value,
       NULL AS Net_Rate_Value,
       rn.room_number,
       rn.booking_type,
       rn.number_children,
       rn.number_adults,
       rn.createdate_dttm,
       rn.confirmation_no,
       rn.channel,
       rn.channel + '/' + rn.source_booking AS Channel_Source_Booking,
       rn.booking_tm,
       0 AS Rooms_Sold,
       0 AS Arrivals,
       0 AS Departures,
       case when rn.individual_status in ('XX','CANCELLED')
       then 1 else 0 end AS Cancellations,
        case when rn.individual_status in ('NS','NO SHOW','NO_SHOW')
                then 1 else 0 end AS No_Show,

       (SELECT ac.accom_class_code
        FROM accom_type AT
                 JOIN accom_class ac ON at.accom_class_id = ac.accom_class_id
        WHERE at.accom_type_id = rn.accom_type_id) AS Accom_Class_Code,

       (SELECT at.accom_type_code
        FROM accom_type AT
        WHERE at.accom_type_id = rn.accom_type_id) AS Accom_Type_Code,
       Datediff(dd, rn.booking_dt, rn.arrival_dt) Days_To_Arrival,
       Datediff(dd, rn.arrival_dt, rn.departure_dt) Length_Of_Stay,
       0 AS Capacity,
       'I' AS Transaction_Source
FROM
    (SELECT rn.[Individual_Trans_ID],rn.[File_Metadata_ID],rn.[Property_ID],rn.[Reservation_Identifier],rn.[Individual_Status],rn.[Arrival_DT],rn.[Departure_DT],rn.[Booking_DT],rn.[Cancellation_DT],rn.[Booked_Accom_Type_Code],rn.[Accom_Type_ID],rn.[Mkt_Seg_ID],
					rn.[Room_Revenue],rn.[Food_Revenue],rn.[Beverage_Revenue],rn.[Telecom_Revenue],rn.[Other_Revenue],rn.[Total_Revenue],rn.[Source_Booking],rn.[Nationality],rn.[Rate_Code],rn.[Rate_Value],rn.[Room_Number],rn.[Booking_type],rn.[Number_Children],
					rn.[Number_Adults],rn.[CreateDate_DTTM],rn.[Confirmation_No],rn.[Channel],rn.[Booking_TM],rn.[Occupancy_DT],rn.[Persistent_Key],rn.[Inv_Block_Code],rn.[Analytics_Booking_Dt],rn.[Market_Code],rn.[Gross_Rate_Value],rn.[Gross_Room_Revenue],
					rn.[Total_Acquisition_Cost],rn.[Room_Revenue_Acquisition_Cost],rn.[Net_Revenue],rn.[Net_Rate],
				cd.*
     FROM reservation_night rn,
          calendar_dim cd
     WHERE cd.calendar_date = rn.arrival_dt
       AND rn.individual_status IN ('XX',
                                    'NS',
                                    'NO SHOW',
                                    'NO_SHOW',
                                    'CANCELLED')
       AND rn.occupancy_dt = rn.arrival_dt
       AND rn.arrival_DT <> rn.departure_DT) AS rn
UNION ALL
SELECT group_block.group_block_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       group_master.property_id,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
       occupancy_dt AS Occupancy_Date,
       day_of_week,
       Datepart(wk, calendar_date) AS week_of_year,
       occupancy_dt AS Arrival_DT,
       Dateadd(dd, 1, occupancy_dt) AS Departure_DT,
       group_master.booking_dt,
       NULL AS Cancellation_DT,
       accom_type_code AS Booked_Accom_Type_Code,
       group_block.accom_type_id,
       mkt_seg_code,
       rate * (blocks - pickup) AS Room_Revenue,
       rate * (blocks - pickup) AS Room_Revenue_For_RevPAR,
       0.0 AS Food_Revenue,
       0.0 AS Beverage_Revenue,
       0.0 AS Telecom_Revenue,
       0.0 AS Other_Revenue,
       rate * (blocks - pickup) AS Total_Revenue,
       0.0 AS Total_Channel_Cost,
       0.0 AS Total_Acquisition_Cost,
       rate * (blocks - pickup) AS Net_Revenue,
       '' AS Source_Booking,
       '' AS Nationality,
       '' AS Rate_Code,
       rate AS Rate_Value,
       rate AS Net_Rate_Value,
       '' AS Room_Number,
       '' AS Booking_type,
       0 AS Number_Children,
       0 AS Number_Adults,
       '' AS CreateDate_DTTM,
       NULL AS Confirmation_No,
       '' AS Channel,
       '' AS Channel_Source_Booking,
       '' AS Booking_TM,
       (blocks - pickup) AS Rooms_Sold,
       (blocks - pickup) AS Arrivals,
       0 AS Departures,
       0 AS Cancellations,
       0 AS NO_Show,
       accom_class_code,
       accom_type_code,
       Datediff(dd, System_Date.dt, occupancy_dt) AS Days_To_Arrival,
       1 AS Length_Of_Stay,
       0 AS Capacity,
       'G' AS Transaction_Source
FROM group_master
         INNER JOIN group_block ON group_master.group_id = group_block.group_id
         INNER JOIN mkt_seg ON group_master.mkt_seg_id = mkt_seg.mkt_seg_id
         INNER JOIN accom_type ON group_block.accom_type_id = accom_type.accom_type_id
         INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
         INNER JOIN
     (SELECT Max(snapshot_dt) AS 'DT'
      FROM file_metadata
      WHERE record_type_id = 3
        AND process_status_id = 13) System_Date ON 1 = 1
         INNER JOIN calendar_dim ON group_block.occupancy_dt = calendar_dim.calendar_date
    AND calendar_dim.calendar_date >= System_Date.dt
WHERE group_master.group_status_code = 'DEFINITE'
  AND (blocks - pickup) > 0
UNION ALL
SELECT group_block.group_block_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       group_master.property_id,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
       Dateadd(dd, 1, occupancy_dt) AS Occupancy_Date,
       day_of_week,
       Datepart(wk, calendar_date) AS week_of_year,
       occupancy_dt AS Arrival_DT,
       Dateadd(dd, 1, occupancy_dt) AS Departure_DT,
       group_master.booking_dt,
       NULL AS Cancellation_DT,
       accom_type_code AS Booked_Accom_Type_Code,
       group_block.accom_type_id,
       mkt_seg_code,
       0.0 AS Room_Revenue,
       0.0 AS Room_Revenue_For_RevPAR,
       0.0 AS Food_Revenue,
       0.0 AS Beverage_Revenue,
       0.0 AS Telecom_Revenue,
       0.0 AS Other_Revenue,
       0.0 AS Total_Revenue,
       0.0 AS Total_Channel_Cost,
       0.0 AS Total_Acquisition_Cost,
       0.0 AS Net_Revenue,
       '' AS Source_Booking,
       '' AS Nationality,
       '' AS Rate_Code,
       NULL AS Rate_Value,
       NULL AS Net_Rate_Value,
       '' AS Room_Number,
       '' AS Booking_type,
       0 AS Number_Children,
       0 AS Number_Adults,
       '' AS CreateDate_DTTM,
       NULL AS Confirmation_No,
       '' AS Channel,
       '' AS Channel_Source_Booking,
       '' AS Booking_TM,
       0 AS Rooms_Sold,
       0 AS Arrivals,
       (blocks - pickup) AS Departures,
       0 AS Cancellations,
       0 AS NO_Show,
       accom_class_code,
       accom_type_code,
       Datediff(dd, System_Date.dt, occupancy_dt) + 1 AS Days_To_Arrival,
       1 AS Length_Of_Stay,
       0 AS Capacity,
       'G' AS Transaction_Source
FROM group_master
         INNER JOIN group_block ON group_master.group_id = group_block.group_id
         INNER JOIN mkt_seg ON group_master.mkt_seg_id = mkt_seg.mkt_seg_id
         INNER JOIN accom_type ON group_block.accom_type_id = accom_type.accom_type_id
         INNER JOIN accom_class ON accom_class.accom_class_id = accom_type.accom_class_id
         INNER JOIN
     (SELECT Max(snapshot_dt) AS 'DT'
      FROM file_metadata
      WHERE record_type_id = 3
        AND process_status_id = 13) System_Date ON 1 = 1
         INNER JOIN calendar_dim ON Dateadd(dd, 1, group_block.occupancy_dt) = calendar_dim.calendar_date
    AND calendar_dim.calendar_date >= System_Date.dt
WHERE group_master.group_status_code = 'DEFINITE'
  AND (blocks - pickup) > 0
UNION ALL
SELECT accom_activity_id AS Individual_Trans_ID,
       '' AS File_Metadata_ID,
       '' AS Property_ID,
       '' AS Reservation_Identifier,
       'SS' Individual_Status,
       occupancy_dt AS Occupancy_Date,
       day_of_week,
       Datepart(wk, calendar_date) AS week_of_year,
       NULL AS Arrival_DT,
       NULL AS Departure_DT,
       '1970-01-01' AS Booking_DT,
       NULL AS Cancellation_DT,
       at.accom_type_code AS Booked_Accom_Type_Code,
       aa.accom_type_id,
       NULL AS mkt_seg_code,
       NULL AS Room_Revenue,
       NULL AS Room_Revenue_For_RevPAR,
       NULL AS Food_Revenue,
       NULL AS Beverage_Revenue,
       NULL AS Telecom_Revenue,
       NULL AS Other_Revenue,
       NULL AS Total_Revenue,
       NULL AS Total_Channel_Cost,
       NULL AS Total_Acquisition_Cost,
       NULL AS Net_Revenue,
       NULL AS Source_Booking,
       NULL AS Nationality,
       NULL AS Rate_Code,
       NULL AS Rate_Value,
       NULL AS Net_Rate_Value,
       '' AS Room_Number,
       '' AS Booking_type,
       0 AS Number_Children,
       0 AS Number_Adults,
       '' AS CreateDate_DTTM,
       NULL AS Confirmation_No,
       NULL AS Channel,
       NULL AS Channel_Source_Booking,
       NULL AS Booking_TM,
       NULL AS Rooms_Sold,
       NULL AS Arrivals,
       NULL AS Departures,
       NULL AS Cancellations,
       NULL AS NO_Show,
       accom_class_code,
       accom_type_code,
       NULL AS Days_To_Arrival,
       NULL AS Length_Of_Stay,
       aa.accom_capacity - (aa.rooms_not_avail_maint + aa.rooms_not_avail_other) AS Capacity,
       'A' AS Transaction_Source
FROM accom_activity aa
         INNER JOIN calendar_dim ON aa.occupancy_dt = calendar_dim.calendar_date
         INNER JOIN accom_type AT ON aa.accom_type_id = at.accom_type_id
         INNER JOIN accom_class ac ON at.accom_class_id = ac.accom_class_id
