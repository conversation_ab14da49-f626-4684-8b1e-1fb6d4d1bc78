DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_data_ms_by_snapshot]
GO
/****** Object:  StoredProcedure [dbo].[usp_get_pace_data_ms_by_snapshot]    Script Date: 01/12/2021 6:53:37 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Stored Procedure Name: usp_get_pace_data_ms_by_snapshot

Input Parameters : 
	@property_id --> property Id Associated with a property (e.g.,'XNAES' id FROM the property table is 010027)
	@start_date --> start date FROM which you need pace ('2020-05-20')
	@end_date --> end date till which you need pace ('2020-05-24')
	@past_start_date --> maximum pASt date till which we want pace FROM 
	@Mkt_Seg_IDs --> market segment ids for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)

Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_data_ms_by_snapshot 10027,'2020-05-20', '2020-05-24','2020-05-23','1,2,3,4,5','0'

Purpose: The purpose of this Procedure is to extract pace of forecast and occupancy for a given property and given date range based on market segments

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/12/2021		Anil				Borgude					Initial Version
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/

CREATE PROCEDURE [dbo].[usp_get_pace_data_ms_by_snapshot]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@Mkt_Seg_IDs nvarchar(4000),
    @exclude_comp_rooms varchar(5)
)	

AS
BEGIN
	SET NOCOUNT ON
	DECLARE @pace_data_ms TABLE (business_day_end_dt DATE, mkt_seg_id INT, mkt_seg_name VARCHAR(50), rooms_sold NUMERIC(8,0), sold_count NUMERIC(8,0), occupancy_nbr NUMERIC(8,2), fcst_count NUMERIC(8,0) )

/** Retriving Rooms Sold from the Pace Activity Procedure **/	
	CREATE table #pace_activity_ms (
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2),
        onBooks_ADR numeric(19,5)
	)
	insert into #pace_activity_ms EXECUTE dbo.usp_get_pace_activity_ms_by_snapshot @property_id,@start_date, @end_date,@past_start_date,@Mkt_Seg_IDs,@exclude_comp_rooms

/** Retriving Occupancy NBR from the Pace Occupancy Procedure **/
	CREATE table #pace_occupancy_fcst(
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Occupancy_NBR numeric(8,2)
	)

	insert into #pace_occupancy_fcst EXECUTE dbo.usp_get_pace_occupancy_fcst_ms_by_snapshot @property_id,@start_date, @end_date,@past_start_date,@Mkt_Seg_IDs,@exclude_comp_rooms

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	SELECT activity.Business_Day_End_DT, activity.Mkt_Seg_ID AS Mkt_Seg_ID, activity.Mkt_Seg_Name, 
	activity.Rooms_Sold AS Rooms_Sold, CAST(activity.Room_Revenue as numeric(19, 2)) AS Room_Revenue,
           CASE
               WHEN (activity.Rooms_Sold IS NULL OR activity.Rooms_Sold = 0) THEN NULL
               WHEN (activity.Room_Revenue IS NULL) THEN NULL
               ELSE CAST(activity.Room_Revenue / activity.Rooms_Sold as numeric(19, 2)) END AS onBooks_ADR,
	fcst.Occupancy_NBR AS Occupancy_NBR
	FROM
		#pace_activity_ms activity
LEFT JOIN
		#pace_occupancy_fcst fcst ON activity.Business_Day_End_DT=fcst.Business_Day_End_DT AND activity.Mkt_Seg_ID=fcst.Mkt_Seg_ID
        ORDER BY activity.Business_Day_End_DT, activity.Mkt_Seg_ID, activity.Mkt_Seg_Name
		  
		DROP table #pace_activity_ms
		DROP table #pace_occupancy_fcst
END
GO