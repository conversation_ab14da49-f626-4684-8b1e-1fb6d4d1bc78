DROP PROCEDURE IF EXISTS [opera].USP_History_Group_Block_Insert
GO
CREATE PROCEDURE [opera].USP_History_Group_Block_Insert
	@History_Group_Block_Batch [opera].History_Group_Block_Batch READONLY
AS
BEGIN
	INSERT INTO [opera].[History_Group_Block] 
	(
		[Hotel_Code], [Group_ID],[Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy],
		[Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID]
	)
	SELECT 
		[Hotel_Code], [Group_ID],[Block_DT], [Forecast], [Room_Type], [Block], [Pickup], [Single_Occupancy], [Double_Occupancy], [Triple_Occupancy],
		[Quadruple_Occupancy], [Extra_Occupancy], [Single_Rate], [Double_Rate], [Triple_Rate], [Quadruple_Rate], [Extra_Rate], [Data_Load_Metadata_ID] 
	FROM 
		@History_Group_Block_Batch
END
GO