if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_pricing_competitor_rates]'))
drop function [ufn_get_pricing_competitor_rates]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE function [dbo].[ufn_get_pricing_competitor_rates]
(
       @property_id int,
       @start_date date,
       @end_date date,
       @comp_id_1 int,
       @comp_id_2 int,
       @comp_id_3 int,
       @comp_id_4 int,
       @comp_id_5 int,
       @comp_id_6 int,
       @comp_id_7 int,
       @RoomClasses nvarchar(200),
       @RoomTypes nvarchar(200)
)             
returns  @pricing_competitor_rates table
(      
       Arrival_DT date,
       Accom_Class_Name nvarchar(150),
       comp1_rate numeric(19,5),
       comp1_name nvarchar(150),
       comp1_status nvarchar(50),
       comp2_rate numeric(19,5),
       comp2_name nvarchar(150),
       comp2_status nvarchar(50),
       comp3_rate numeric(19,5),
       comp3_name nvarchar(150),
       comp3_status nvarchar(50),
       comp4_rate numeric(19,5),
       comp4_name nvarchar(150),
       comp4_status nvarchar(50),
       comp5_rate numeric(19,5),
       comp5_name nvarchar(150),
       comp5_status nvarchar(50),
       comp6_rate numeric(19,5),
       comp6_name nvarchar(150),
       comp6_status nvarchar(50),
       comp7_rate numeric(19,5),
       comp7_name nvarchar(150),
       comp7_status nvarchar(50),
       Accom_Type_Name nvarchar(150)
)
as
begin
              INSERT INTO @pricing_competitor_rates
              SELECT 
                     main.Occupancy_DT AS Arrival_DT,
                     main.Accom_Class_Name,
                     comp1.webrate_ratevalue AS comp1_rate,comp1.webrate_competitors_name AS comp1_name,comp1.webrate_status AS comp1_status,
                     comp2.webrate_ratevalue AS comp2_rate,comp2.webrate_competitors_name AS comp2_name,comp2.webrate_status AS comp2_status,
                     comp3.webrate_ratevalue AS comp3_rate,comp3.webrate_competitors_name AS comp3_name,comp3.webrate_status AS comp3_status,
                     comp4.webrate_ratevalue AS comp4_rate,comp4.webrate_competitors_name AS comp4_name,comp4.webrate_status AS comp4_status,
                     comp5.webrate_ratevalue AS comp5_rate,comp5.webrate_competitors_name AS comp5_name,comp5.webrate_status AS comp5_status,
                     comp6.webrate_ratevalue AS comp6_rate,comp6.webrate_competitors_name AS comp6_name,comp6.webrate_status AS comp6_status,
                     comp7.webrate_ratevalue AS comp7_rate,comp7.webrate_competitors_name AS comp7_name,comp7.webrate_status AS comp7_status,
                     main.Accom_Type_Name AS Accom_Type_Name
              FROM
              (
                     SELECT aa.Property_ID, p.Property_Name, aa.Occupancy_DT, at.Accom_Class_ID, ac.Accom_Class_Name, at.Accom_Type_ID, at.Accom_Type_Name
                     FROM Accom_Activity aa INNER JOIN Property p ON aa.Property_ID = p.Property_ID INNER JOIN Accom_Type at ON aa.Accom_Type_ID = at.Accom_Type_ID INNER JOIN Accom_Class ac on at.Accom_Class_ID = ac.Accom_Class_ID
                     WHERE aa.Property_ID = @property_id AND aa.Occupancy_DT BETWEEN @start_date AND @end_date
                     AND at.Status_ID = 1 AND at.System_Default = 0 AND at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))
              ) main
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_1,@start_date,@end_date,@RoomClasses) WHERE @comp_id_1>0
              ) AS comp1 ON main.Occupancy_DT=comp1.Occupancy_DT AND main.Property_ID=comp1.Property_ID and main.Accom_Class_ID = comp1.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_2,@start_date,@end_date,@RoomClasses) WHERE @comp_id_2>0
              ) AS comp2 ON main.Occupancy_DT=comp2.Occupancy_DT and main.Property_ID=comp2.Property_ID and main.Accom_Class_ID = comp2.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_3,@start_date,@end_date,@RoomClasses) WHERE @comp_id_3>0
              ) AS comp3 ON main.Occupancy_DT=comp3.Occupancy_DT and main.Property_ID=comp3.Property_ID and main.Accom_Class_ID = comp3.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_4,@start_date,@end_date,@RoomClasses) WHERE @comp_id_4>0
              ) AS comp4 ON main.Occupancy_DT=comp4.Occupancy_DT and main.Property_ID=comp4.Property_ID and main.Accom_Class_ID = comp4.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_5,@start_date,@end_date,@RoomClasses) WHERE @comp_id_5>0
              ) AS comp5 ON main.Occupancy_DT=comp5.Occupancy_DT and main.Property_ID=comp5.Property_ID and main.Accom_Class_ID = comp5.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_6,@start_date,@end_date,@RoomClasses) WHERE @comp_id_6>0
              ) AS comp6 ON main.Occupancy_DT = comp6.Occupancy_DT AND main.Property_ID = comp6.Property_ID and main.Accom_Class_ID = comp6.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_pricing_web_competitors_rate_by_rc(@property_id,@comp_id_7,@start_date,@end_date,@RoomClasses) where @comp_id_7>0
              ) AS comp7 ON main.Occupancy_DT = comp7.Occupancy_DT and main.Property_ID = comp7.Property_ID and main.Accom_Class_ID = comp7.accom_Class_id        
              ORDER BY main.Occupancy_DT
       return
end
GO