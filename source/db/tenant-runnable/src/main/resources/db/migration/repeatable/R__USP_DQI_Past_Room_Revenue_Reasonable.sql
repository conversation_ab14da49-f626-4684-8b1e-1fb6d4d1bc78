DROP PROCEDURE [dbo].[DQI_Past_Room_Revenue_Reasonable]
GO
CREATE PROCEDURE [dbo].[DQI_Past_Room_Revenue_Reasonable] @property_ID INT, @system_date Date, @DQI_ID INT, @detail_analysis as CHAR(1) = 'N', @pseudoRoomTypeCodes NVARCHAR(1000)
AS
BEGIN
	DECLARE @no_of_history_days AS Integer
	DECLARE @past_2_years_date AS DATE = DATEADD(D, 2*365*-1, @system_date)
	DECLARE @total_rooms_sold AS Int = 0
	DECLARE @sum_failed_solds as Int = 0
	DECLARE @failures as Int = 0
	
	
	
	IF OBJECT_ID('tempdb..#Table_IndividualTrans_Past2YearsData') IS NOT NULL
	BEGIN
		DROP TABLE #Table_IndividualTrans_Past2YearsData
	END
	select cast(calendar_dim.calendar_date as date) as occupancy_DT, itrans.Booked_Accom_Type_Code, itrans.Individual_Trans_ID, 
		  itrans.Mkt_Seg_ID, itrans.property_ID, at.Accom_Type_ID, itrans.Room_Revenue, 
		  Room_Revenue/DATEDIFF(day, itrans.Arrival_DT, itrans.Departure_DT) as per_day_room_revenue, msd.Priced_By_BAR
	into #Table_IndividualTrans_Past2YearsData
	from calendar_dim 
	INNER JOIN Individual_Trans itrans 
		  ON itrans.Departure_DT > cast(calendar_dim.calendar_date as date)
		  AND itrans.Arrival_DT <= cast(calendar_dim.calendar_date as date)
		  AND itrans.Individual_Status NOT IN ('XX', 'XC', 'NS')
	INNER JOIN Accom_Type at ON itrans.Booked_Accom_Type_Code = at.Accom_Type_Code
	INNER JOIN Mkt_Seg_Details msd on itrans.Mkt_Seg_ID = msd.Mkt_Seg_ID
	where cast(calendar_dim.calendar_date as date) between @past_2_years_date and DATEADD(d, -1, @system_date )
	AND calendar_date NOT IN 
	(
		  select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
		  INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
		  and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
		  WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date) between @past_2_years_date and DATEADD(d, -1, @system_date )
		  and ip_cfg_mark_property_date.Status_ID = 1
	)
	AND itrans.Booked_Accom_Type_Code = at.Accom_Type_Code
	AND Booked_Accom_Type_Code not in (select items from dbo.Split(@pseudoRoomTypeCodes,','))
	--group by itrans.Property_ID, at.Accom_Type_ID, calendar_date,Mkt_Seg_Details.Priced_By_BAR

	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	-- Set NOCOUNT ON is needed, don't remove it.
	SET NOCOUNT ON
	select @total_rooms_sold=COALESCE(COUNT(Individual_Trans_ID),0),@no_of_history_days=COUNT(distinct occupancy_DT) from #Table_IndividualTrans_Past2YearsData as OuterTable
	
	
	/** Room revenue (past) is reasonable*/
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	
	Select Property_ID,Accom_Type_ID,Occupancy_DT, 
			bar_rooms_sold,bar_ADR,non_bar_rooms_sold,
			non_bar_ADR, fifty_percent_bar_ADR,
			error
	into #Table_Final
	from
	( 
		Select 
			bar_calculation.Property_ID,bar_calculation.Accom_Type_ID,bar_calculation.Occupancy_DT as Occupancy_DT,
			bar_rooms_sold,bar_ADR,non_bar_rooms_sold,
			non_bar_ADR, 0.5 * bar_ADR as fifty_percent_bar_ADR,
			case when non_bar_ADR >= 0.5 * bar_ADR then 'N' 
				else 'Y'
			end as error
		from
		(		
			  (
					select Property_ID, Accom_Type_ID, occupancy_DT, COUNT(Individual_Trans_ID) as bar_rooms_sold, CASE WHEN COUNT(Individual_Trans_ID) = 0 then 0 else SUM(per_day_room_revenue)/COUNT(Individual_Trans_ID) end as bar_ADR
					from #Table_IndividualTrans_Past2YearsData
					Where Priced_By_BAR = 1
					group by Property_ID, Accom_Type_ID, occupancy_DT
			  ) bar_calculation
			  inner join
			  (
					select Property_ID, Accom_Type_ID, occupancy_DT, COUNT(Individual_Trans_ID) as non_bar_rooms_sold, CASE WHEN COUNT(Individual_Trans_ID) = 0 then 0 else SUM(per_day_room_revenue)/COUNT(Individual_Trans_ID) end as non_bar_ADR
					from #Table_IndividualTrans_Past2YearsData
					Where Priced_By_BAR = 0
					group by Property_ID, Accom_Type_ID, occupancy_DT
			  ) non_bar_calculation 
 			  on bar_calculation.Property_ID = non_bar_calculation.Property_ID and bar_calculation.Accom_Type_ID = non_bar_calculation.Accom_Type_ID
				and bar_calculation.Occupancy_DT = non_bar_calculation.Occupancy_DT and bar_rooms_sold <> 0 and non_bar_rooms_sold <> 0
		)
	) AS final_table
	
	if (@detail_analysis)='Y'
	BEGIN
		IF OBJECT_ID('tempdb..#Table_Details_Data') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Details_Data
		END
		
		create table #Table_Details_Data(
			column1 nvarchar(50) default '',
			column2 nvarchar(50) default '',
			column3 nvarchar(50) default '',
			column4 nvarchar(50) default '',
			column5 nvarchar(50) default '',
			column6 nvarchar(50) default '',			
			column7 nvarchar(50) default '',
			column8 nvarchar(50) default '',
			column9 nvarchar(50) default '',
			column10 nvarchar(50) default ''			
		)
		
		INSERT INTO #Table_Details_Data(column1,column2,column3,column4,column5,column6,column7,column8,column9,column10) 
		values('1',@property_ID,@total_rooms_sold,'','','','','','','')
				
		INSERT INTO #Table_Details_Data(column1,column2,column3,column4,column5,column6,column7,column8,column9,column10) 
		SELECT 
			'2',Property_ID,(select Accom_Type_Code from Accom_Type where Accom_Type_Id = #Table_Final.Accom_Type_ID),
			Occupancy_DT, 
			bar_rooms_sold,bar_ADR,non_bar_rooms_sold,
			non_bar_ADR, fifty_percent_bar_ADR,
			error
		from #Table_Final
		--order by column 1 in necessary for parsing, don't remove it		
		select * from #Table_Details_Data order by column1
		
		IF OBJECT_ID('tempdb..#Table_Details_Data') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Details_Data
		END
	END
	
	--find @sum_failed_solds
	Select @sum_failed_solds = COALESCE(SUM(non_bar_rooms_sold),0) from #Table_Final where error = 'Y'
	Select @failures = COUNT(*) from #Table_Final where error = 'Y'
	
	SELECT @DQI_ID AS DQI_ID, @property_ID as property_ID, case when @no_of_history_days is null then 0 else  @no_of_history_days end as actual_days_analysed,
		@failures as failures,
		case when (@sum_failed_solds <= (0.1 * @total_rooms_sold)) then 'GREEN'
			 when (@sum_failed_solds > (0.1 * @total_rooms_sold)) and (@sum_failed_solds <= (0.2 * @total_rooms_sold)) then 'YELLOW'
			 else 'RED'
		end as indicator

    IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
   	END
   	
   	IF OBJECT_ID('tempdb..#Table_IndividualTrans_Past2YearsData') IS NOT NULL
	BEGIN
		DROP TABLE #Table_IndividualTrans_Past2YearsData
	END
      
END
GO


