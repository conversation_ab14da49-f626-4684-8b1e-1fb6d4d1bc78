IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ufn_get_special_event_by_property]'))
DROP FUNCTION [dbo].[ufn_get_special_event_by_property]

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Function Name: ufn_get_special_event_by_property

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')

	
Ouput Parameter : NA

Execution: this is just an example
	select * from ufn_get_special_event_by_property_test (18,'2011-07-01', '2011-07-31')

Purpose: The purpose of this function is to extract special events for a property within a given date range. 

Assumptions : NA
		 
Author: Atul and Manohar

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
09/15/2011	Atul				Shendye					Initial Version
09/15/2011	Manohar				Sunkum					Refactored
10/17/2011  Manohar				Sunkum					Modified to capture multiple event values
														on a single row for a given date
21/10/2021	Sourabh				Patil					Modified to get Special Event Instance Name along with Special Event Name with a '-' in between 
***************************************************************************************/
create function [dbo].[ufn_get_special_event_by_property]
(
		@property_id int,
		@start_date date,
		@end_date date
)		
returns  @property_special_event table
	(	
		
		property_id	int,
		special_event_name nvarchar(500),
		event_cal_date date
		
	)
as

begin
		--insert into @property_special_event
		declare @prop_special_event table
		(	
		
			property_id	int,
			special_event_name nvarchar(500),
			event_cal_date date
		
		)
		insert into @prop_special_event
		select 
			PSE.Property_ID,
			(case 
				when TRIM(PSEI.Special_Event_Instance_Name) <> '' then CONCAT(PSE.Special_Event_Name,' - ', PSEI.Special_Event_Instance_Name)
				else PSE.Special_Event_Name 
			end) as Special_Event_Name,
			cast(cd.calendar_date as date) as event_cal_date
	   	from Property_Special_Event PSE
			inner join Property_Special_Event_Instance PSEI on PSE.Property_Special_Event_ID=PSEI.Property_Special_Event_ID
			cross apply calendar_dim cd
	   	where PSE.Property_ID=@property_id
	   	and (
		  		PSEI.Start_DTTM between @start_date and @end_date
				OR PSEI.End_DTTM between @start_date and @end_date
				OR (PSEI.Start_DTTM <= @start_date and PSEI.End_DTTM >= @end_date )
		   ) 
	  	 and PSE.Impact_On_Forecast=1 and PSEI.Enable_Forecast=1 and PSE.Status_ID=1 and PSEI.Status_ID=1
	  	 and cd.calendar_date between PSEI.start_dttm and PSEI.end_dttm
	   		order by PSE.special_event_name,cd.calendar_date
	   
	   -- The following logic extracts the multiple value columns into a single column
	   declare @special_event table
		(	
			event_cal_date date,
			special_event_name nvarchar(500)
		)

		declare @tmp_event table
		(	
			event_cal_date date
	
		)
		insert into @tmp_event
		select distinct event_cal_date 
		from @prop_special_event 
		---
		insert into @special_event
		select
				event_cal_date,
				                                                                
				stuff( 
						(   select ', '+special_event_name
							from @prop_special_event
							where event_cal_date = a.event_cal_date order by special_event_name
							for xml path('')  
						)  
						, 1, 2, '' 
					)as special_event
		from @tmp_event a
		
		-- insert filtered rows into @property_special_event
		insert into @property_special_event
		select distinct b.property_id,a.special_event_name,a.event_cal_date
		from @special_event a left join @prop_special_event b
		on a.event_cal_date = b.event_cal_date
		order by a.special_event_name,a.event_cal_date
	return
end

