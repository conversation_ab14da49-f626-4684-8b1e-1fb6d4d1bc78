IF EXISTS
(
    SELECT * FROM sys.objects WHERE object_id = object_id(N'[usp_get_decision_lrv_by_individual_rc]')
)
    DROP PROCEDURE [dbo].[usp_get_decision_lrv_by_individual_rc]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_get_decision_lrv_by_individual_rc]
(
    @property_id INT,
    @roomclass_id VARCHAR(500),
    @start_date DATE,
    @end_date DATE
)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @decision_lrv TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_class_id INT,
        accom_class_code NVARCHAR(50),
        lrv NUMERIC(19, 5)
    )

    INSERT INTO @decision_lrv
    SELECT dl.property_id,
           dl.occupancy_dt,
           ac.accom_class_id,
           ac.accom_class_code,
           dl.lrv
    FROM decision_lrv dl
        INNER JOIN accom_class ac
            ON dl.accom_class_id = ac.accom_class_id
    WHERE ac.accom_class_id IN (
                                   SELECT value FROM dbo.varcharToInt(@roomclass_id, ',')
                               )
          AND dl.property_id = @property_id
          AND dl.occupancy_dt
          BETWEEN @start_date AND @end_date
    ORDER BY dl.occupancy_dt
    SELECT * FROM @decision_lrv;
    RETURN
END