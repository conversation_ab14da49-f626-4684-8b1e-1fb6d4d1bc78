DROP PROCEDURE IF EXISTS [dbo].[usp_decision_as_of_by_accom_class]

GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

Create procedure [dbo].[usp_decision_as_of_by_accom_class]
(
    @inp_accom_class_id int,
    @startDate date,
    @endDate date,
    @productId int,
    @decisionTypeIds nvarchar(10),
    @propertyId int
)
as
begin

   select 
  CP_Pace_Decision_Bar_Output_ID, 
  Property_ID, 
  Arrival_DT, 
  Accom_Type_ID, 
  Final_BAR as optimalBar, 
  Decision_ID, 
  Pretty_BAR, 
  Final_BAR as diff, 
  LOS 
from 
  CP_Pace_Decision_Bar_Output 
where 
  Accom_Type_ID = (
    select 
      Accom_Type_ID 
    from 
      CP_Cfg_AC 
    where 
      Accom_Class_ID = @inp_accom_class_id
  ) 
  and Arrival_DT between @startDate 
  and @endDate
  and Product_ID =  @productId 
  and Property_ID = @propertyId 
  and Decision_ID = (
    select 
      MAX(Decision_ID) 
    from 
      CP_Pace_Decision_Bar_Output 
    where 
      Product_ID = @productId 
      and Decision_ID = (
        select 
          MAX(Decision_ID) 
        from 
          Decision 
        where 
          Decision_Type_ID in (
            SELECT 
              Value 
            FROM 
              varcharToInt(@decisionTypeIds, ', ')
          )
      )
  )

end