DROP PROCEDURE IF EXISTS [dbo].[USP_MP_Decision_ACK_Status_Insert]
GO
CREATE PROCEDURE [dbo].[USP_MP_Decision_ACK_Status_Insert]
	@MP_Decision_ACK_Status_Batch MP_Decision_ACK_Status_Batch READONLY
AS
BEGIN
    INSERT INTO [dbo].[MP_Decision_Ack_Status] 
	( 
		[Transaction_ID],
		[Occupancy_Date],
		[MP_Product_ID],
		[FS_Cfg_Func_Room_ID],
		[Decision_Type],
		[Decision],
		[Decision_date_Time],
		[Acknowledgement_Date_Time],
		[Error_Code],
		[Error_Description]	
	)
    SELECT	
		[Transaction_ID],
		[Occupancy_Date],
		[MP_Product_ID],
		[FS_Cfg_Func_Room_ID],
		[Decision_Type],
		[Decision],
		[Decision_date_Time],
		[Acknowledgement_Date_Time],
		[Error_Code],
		[Error_Description]
	FROM @MP_Decision_ACK_Status_Batch tableVar 
	WHERE tableVar.MP_Decision_ACK_Status_ID is null;
END
GO



