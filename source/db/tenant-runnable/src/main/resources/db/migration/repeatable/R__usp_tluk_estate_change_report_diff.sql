drop procedure if exists [dbo].[usp_tluk_estate_change_report_diff]
GO

/****** Object:  StoredProcedure [dbo].[usp_tluk_estate_change_report_diff]    Script Date: 3/16/2023 1:27:03 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE procedure [dbo].[usp_tluk_estate_change_report_diff](@report_window_size INT)
as
begin

declare @propertyId as int = (select Property_ID from Property where Property_Code != '-1')
declare @caughtUpDate date = (select dbo.ufn_get_caughtup_date_by_property(@propertyId,3,13) as Caught_Up_Date)
declare @startDate as date = DateAdd(DAY,-1,CAST(@caughtUpDate as date))
declare @endDate as date = DateAdd(DAY,@report_window_size,CAST(@caughtUpDate as date))
SET NOCOUNT ON

create table #Calendar
(
    Occupancy_DT date,
    DOW nvarchar(max)
)
    insert into #Calendar
select CAST(calendar_date as date) Occupancy_DT, dow_name as DOW from calendar_dim where calendar_date between @startDate and @endDate

create table #Property_Table
(
    Property_Name nvarchar(max),
    Property_ID int
)
    insert into #Property_Table
select Property_Name, Property_ID from Property where Property_Code != '-1'

declare @lastBdeDate as date =
(
       select distinct dateadd(day, -1, f.SnapShot_DT) as Business_Date from
       (
              select SnapShot_DT, DENSE_RANK() OVER (order by SnapShot_DT desc) as rank from File_Metadata where IsBDE = 1 and Process_Status_ID = 13
       ) as f
       where f.rank = 2
)

IF OBJECT_ID('tempdb..#defaultBusinessGroup') IS NOT NULL DROP TABLE #defaultBusinessGroup
create table #defaultBusinessGroup
(
    Business_Group_ID int,
    Business_Group_Name nvarchar(max)
)
    insert into #defaultBusinessGroup values(-1,'BARFLEX'),(-2,'CORP'),(-3,'BARSAVER'),(-4,'GROUP'),(-5,'OTA'),(-6,'PROMO'),(-7,'SALE'),(-8,'WHOLESALE')

IF OBJECT_ID('tempdb..#businessGroupFinal') IS NOT NULL DROP TABLE #businessGroupFinal
create table #businessGroupFinal
(
    Business_Group_ID int,
    Business_Group_Name nvarchar(max)
)
    insert into #businessGroupFinal
select isnull(bg.Business_Group_ID, defaultBg.Business_Group_ID), defaultBg.Business_Group_Name from
    #defaultBusinessGroup as defaultBg
        left join Business_Group as bg
                  on bg.Business_Group_Name = defaultBg.Business_Group_Name

declare @businessGroups as nvarchar(max) = (select STRING_AGG(Business_Group_ID, ',') from #businessGroupFinal)


IF OBJECT_ID('tempdb..#tempTableFinal') IS NOT NULL DROP TABLE #tempTableFinal
    IF OBJECT_ID('tempdb..#BusinessGroupActivity') IS NOT NULL DROP TABLE #BusinessGroupActivity
    IF OBJECT_ID('tempdb..#BusinessGroupActivityLastBDE') IS NOT NULL DROP TABLE #BusinessGroupActivityLastBDE
create table #BusinessGroupActivity(
                                       occupancy_dt date, property_id int, Business_Group_ID int, rooms_sold int, room_revenue numeric(19,2), adr numeric(19,2), Business_Group_Name nvarchar(100)
)
    insert into #BusinessGroupActivity
select fn.*,bg.Business_Group_Name from ufn_get_activity_by_individual_bv (@propertyId,@businessGroups,@startDate,@endDate) as fn
                                            inner join Business_Group as bg
                                                       on bg.Business_Group_ID = fn.Business_Group_ID

create table #BusinessGroupActivityLastBDE(
                                              occupancy_dt date, property_id int, Business_Group_Name nvarchar(100), Business_Group_ID int, rooms_sold int, room_revenue numeric(19,2), adr numeric(19,2)--, Business_Group_Name nvarchar(100)
)
    insert into #BusinessGroupActivityLastBDE
exec usp_get_activity_asof_businessdate_by_individual_bv
@propertyId,
@businessGroups,@lastBdeDate,@startDate,@endDate

----------------------------------------------------------- Business Group Rooms Sold Current and Change Pivot Query ----------------------------------------------------------------------

IF OBJECT_ID('tempdb..#BGRoomSoldTempTable') IS NOT NULL DROP TABLE #BGRoomSoldTempTable
create table #BGRoomSoldTempTable(
                                     occupancy_dt date, property_id int,
                                     BARFLEX_Current int, BARFLEX_Change int,
                                     BARSAVER_Current int, BARSAVER_Change int,
                                     CORP_Current int, CORP_Change int,
                                     GROUP_Current int, GROUP_Change int,
                                     OTA_Current int, OTA_Change int,
                                     PROMO_Current int, PROMO_Change int,
                                     SALE_Current int, SALE_Change int,
                                     WHOLESALE_Current int, WHOLESALE_Change int
)
    insert into #BGRoomSoldTempTable
select occupancy_dt, property_id,
       ISNULL(sum([BARFLEX]), 0) as BARFLEX_Current,
       ISNULL(sum([BARFLEX_Change]), 0) as BARFLEX_Change,
       ISNULL(sum([BARSAVER]), 0) as BARSAVER_Current,
       ISNULL(sum([BARSAVER_Change]), 0) as BARSAVER_Change,
       ISNULL(sum([CORP]), 0) as CORP_Current,
       ISNULL(sum([CORP_Change]), 0) as CORP_Change,
       ISNULL(sum([GROUP]), 0) as GROUP_Current,
       ISNULL(sum([GROUP_Change]), 0) as GROUP_Change,
       ISNULL(sum([OTA]), 0) as OTA_Current,
       ISNULL(sum([OTA_Change]), 0) as OTA_Change,
       ISNULL(sum([PROMO]), 0) as PROMO_Current,
       ISNULL(sum([PROMO_Change]), 0) as PROMO_Change,
       ISNULL(sum([SALE]), 0) as SALE_Current,
       ISNULL(sum([SALE_Change]), 0) as SALE_Change,
       ISNULL(sum([WHOLESALE]), 0) as WHOLESALE_Current,
       ISNULL(sum([WHOLESALE_Change]), 0) as WHOLESALE_Change
from
    (
        select
            currentBg.*,
            lastBg.Business_Group_Name + '_Change' as Business_Group_Name_Change,
            isnull(currentBg.rooms_sold, 0) - ISNULL(lastBg.rooms_sold, isnull(currentBg.rooms_sold, 0))  as roomSoldChange
        from
            #BusinessGroupActivity as currentBg
                left join
            #BusinessGroupActivityLastBDE as lastBg
            on currentBg.occupancy_dt = lastBg.occupancy_dt and currentBg.property_id = lastBg.property_id and currentBg.Business_Group_ID = lastBg.Business_Group_ID
    ) as t
    pivot
       (
              sum(rooms_sold) for Business_Group_Name in ([BARFLEX],[BARSAVER],[CORP],[GROUP],[OTA],[PROMO],[SALE],[WHOLESALE])
       ) a
       pivot
       (
              sum(roomSoldChange) for Business_Group_Name_Change in ([BARFLEX_Change],[BARSAVER_Change],[CORP_Change],[GROUP_Change],[OTA_Change],[PROMO_Change],[SALE_Change],[WHOLESALE_Change])
       ) b group by occupancy_dt, property_id

----------------------------------------------------------- Business Group Revenue Current and Change Pivot Query ----------------------------------------------------------------------

    IF OBJECT_ID('tempdb..#BGRevenueTempTable') IS NOT NULL DROP TABLE #BGRevenueTempTable
create table #BGRevenueTempTable(
                                    occupancy_dt date, property_id int,
                                    BARFLEX_Current numeric(19,2), BARFLEX_Change numeric(19,2),
                                    BARSAVER_Current numeric(19,2), BARSAVER_Change numeric(19,2),
                                    CORP_Current numeric(19,2), CORP_Change numeric(19,2),
                                    GROUP_Current numeric(19,2), GROUP_Change numeric(19,2),
                                    OTA_Current numeric(19,2), OTA_Change numeric(19,2),
                                    PROMO_Current numeric(19,2), PROMO_Change numeric(19,2),
                                    SALE_Current numeric(19,2), SALE_Change numeric(19,2),
                                    WHOLESALE_Current numeric(19,2), WHOLESALE_Change numeric(19,2)
)
    insert into #BGRevenueTempTable
select occupancy_dt, property_id,
       ISNULL(sum([BARFLEX]), 0) as BARFLEX_Current,
       ISNULL(sum([BARFLEX_Change]), 0) as BARFLEX_Change,
       ISNULL(sum([BARSAVER]), 0) as BARSAVER_Current,
       ISNULL(sum([BARSAVER_Change]), 0) as BARSAVER_Change,
       ISNULL(sum([CORP]), 0) as CORP_Current,
       ISNULL(sum([CORP_Change]), 0) as CORP_Change,
       ISNULL(sum([GROUP]), 0) as GROUP_Current,
       ISNULL(sum([GROUP_Change]), 0) as GROUP_Change,
       ISNULL(sum([OTA]), 0) as OTA_Current,
       ISNULL(sum([OTA_Change]), 0) as OTA_Change,
       ISNULL(sum([PROMO]), 0) as PROMO_Current,
       ISNULL(sum([PROMO_Change]), 0) as PROMO_Change,
       ISNULL(sum([SALE]), 0) as SALE_Current,
       ISNULL(sum([SALE_Change]), 0) as SALE_Change,
       ISNULL(sum([WHOLESALE]), 0) as WHOLESALE_Current,
       ISNULL(sum([WHOLESALE_Change]), 0) as WHOLESALE_Change
from
    (
        select
            currentBg.*,
            lastBg.Business_Group_Name + '_Change' as Business_Group_Name_Change,
            isnull(currentBg.room_revenue, 0) - ISNULL(lastBg.room_revenue, isnull(currentBg.room_revenue, 0))  as roomRevenueChange
        from
            #BusinessGroupActivity as currentBg
                left join
            #BusinessGroupActivityLastBDE as lastBg
            on currentBg.occupancy_dt = lastBg.occupancy_dt and currentBg.property_id = lastBg.property_id and currentBg.Business_Group_ID = lastBg.Business_Group_ID
    ) as t
    pivot
       (
              sum(room_revenue) for Business_Group_Name in ([BARFLEX],[BARSAVER],[CORP],[GROUP],[OTA],[PROMO],[SALE],[WHOLESALE])
       ) a
       pivot
       (
              sum(roomRevenueChange) for Business_Group_Name_Change in ([BARFLEX_Change],[BARSAVER_Change],[CORP_Change],[GROUP_Change],[OTA_Change],[PROMO_Change],[SALE_Change],[WHOLESALE_Change])
       ) b group by occupancy_dt, property_id

----------------------------------------------------------- Business Group ADR Current and Change Pivot Query ----------------------------------------------------------------------

    IF OBJECT_ID('tempdb..#BGAdrTempTable') IS NOT NULL DROP TABLE #BGAdrTempTable
create table #BGAdrTempTable(
                                occupancy_dt date, property_id int,
                                BARFLEX_Current numeric(19,2), BARFLEX_Change numeric(19,2),
                                BARSAVER_Current numeric(19,2), BARSAVER_Change numeric(19,2),
                                CORP_Current numeric(19,2), CORP_Change numeric(19,2),
                                GROUP_Current numeric(19,2), GROUP_Change numeric(19,2),
                                OTA_Current numeric(19,2), OTA_Change numeric(19,2),
                                PROMO_Current numeric(19,2), PROMO_Change numeric(19,2),
                                SALE_Current numeric(19,2), SALE_Change numeric(19,2),
                                WHOLESALE_Current numeric(19,2), WHOLESALE_Change numeric(19,2)
)
    insert into #BGAdrTempTable
select occupancy_dt, property_id,
       ISNULL(sum([BARFLEX]), 0) as BARFLEX_Current,
       ISNULL(sum([BARFLEX_Change]), 0) as BARFLEX_Change,
       ISNULL(sum([BARSAVER]), 0) as BARSAVER_Current,
       ISNULL(sum([BARSAVER_Change]), 0) as BARSAVER_Change,
       ISNULL(sum([CORP]), 0) as CORP_Current,
       ISNULL(sum([CORP_Change]), 0) as CORP_Change,
       ISNULL(sum([GROUP]), 0) as GROUP_Current,
       ISNULL(sum([GROUP_Change]), 0) as GROUP_Change,
       ISNULL(sum([OTA]), 0) as OTA_Current,
       ISNULL(sum([OTA_Change]), 0) as OTA_Change,
       ISNULL(sum([PROMO]), 0) as PROMO_Current,
       ISNULL(sum([PROMO_Change]), 0) as PROMO_Change,
       ISNULL(sum([SALE]), 0) as SALE_Current,
       ISNULL(sum([SALE_Change]), 0) as SALE_Change,
       ISNULL(sum([WHOLESALE]), 0) as WHOLESALE_Current,
       ISNULL(sum([WHOLESALE_Change]), 0) as WHOLESALE_Change
from
    (
        select
            currentBg.*,
            lastBg.Business_Group_Name + '_Change' as Business_Group_Name_Change,
            isnull(currentBg.adr, 0) - ISNULL(lastBg.adr, isnull(currentBg.adr, 0)) as adrChange
        from
            #BusinessGroupActivity as currentBg
                left join
            #BusinessGroupActivityLastBDE as lastBg
            on currentBg.occupancy_dt = lastBg.occupancy_dt and currentBg.property_id = lastBg.property_id and currentBg.Business_Group_ID = lastBg.Business_Group_ID
    ) as t
    pivot
       (
              sum(adr) for Business_Group_Name in ([BARFLEX],[BARSAVER],[CORP],[GROUP],[OTA],[PROMO],[SALE],[WHOLESALE])
       ) a
       pivot
       (
              sum(adrChange) for Business_Group_Name_Change in ([BARFLEX_Change],[BARSAVER_Change],[CORP_Change],[GROUP_Change],[OTA_Change],[PROMO_Change],[SALE_Change],[WHOLESALE_Change])
       ) b group by occupancy_dt, property_id

----------------------------------------------------------- Business Group Occupancy Forecast Current and Change Pivot Query ----------------------------------------------------------------------

    IF OBJECT_ID('tempdb..#BusinessGroupOccupancyFcstActivity') IS NOT NULL DROP TABLE #BusinessGroupOccupancyFcstActivity
create table #BusinessGroupOccupancyFcstActivity(
                                                    occupancy_dt date, property_id int, Business_Group_ID int, occupancy_forecast_current numeric(19,2), Business_Group_Name nvarchar(100)
)
    insert into #BusinessGroupOccupancyFcstActivity
select fn.occupancy_dt, fn.property_id, fn.Business_Group_ID, occupancy_forecast_current,bg.Business_Group_Name
from dbo.ufn_get_occupancy_forecast_by_individual_bv (@propertyId, @businessGroups, @startDate,@endDate) as fn
         inner join Business_Group as bg
                    on bg.Business_Group_ID = fn.Business_Group_ID

    IF OBJECT_ID('tempdb..#BusinessGroupOccupancyFcstActivityLastBDE') IS NOT NULL DROP TABLE #BusinessGroupOccupancyFcstActivityLastBDE
create table #BusinessGroupOccupancyFcstActivityLastBDE(
                                                           occupancy_dt date, property_id int, Business_Group_ID int, occupancy_nbr numeric(19,2), revenue numeric(19,2), adr numeric(19,2)--, --Business_Group_Name nvarchar(100)
)
    insert into
       #BusinessGroupOccupancyFcstActivityLastBDE
       exec usp_get_occupancy_forecast_asof_businessdate_by_individual_bv   @propertyId, @businessGroups, @lastBdeDate,@startDate,@endDate



IF OBJECT_ID('tempdb..#BGOccFcstTempTable') IS NOT NULL DROP TABLE #BGOccFcstTempTable
create table #BGOccFcstTempTable(
                                    occupancy_dt date, property_id int,
                                    BARFLEX_Current numeric(19,2), BARFLEX_Change numeric(19,2),
                                    BARSAVER_Current numeric(19,2), BARSAVER_Change numeric(19,2),
                                    CORP_Current numeric(19,2), CORP_Change numeric(19,2),
                                    GROUP_Current numeric(19,2), GROUP_Change numeric(19,2),
                                    OTA_Current numeric(19,2), OTA_Change numeric(19,2),
                                    PROMO_Current numeric(19,2), PROMO_Change numeric(19,2),
                                    SALE_Current numeric(19,2), SALE_Change numeric(19,2),
                                    WHOLESALE_Current numeric(19,2), WHOLESALE_Change numeric(19,2)
)
    insert into #BGOccFcstTempTable
select occupancy_dt, property_id,
       ISNULL(sum([BARFLEX]), 0) as BARFLEX_Current,
       ISNULL(sum([BARFLEX_Change]), 0) as BARFLEX_Change,
       ISNULL(sum([BARSAVER]), 0) as BARSAVER_Current,
       ISNULL(sum([BARSAVER_Change]), 0) as BARSAVER_Change,
       ISNULL(sum([CORP]), 0) as CORP_Current,
       ISNULL(sum([CORP_Change]), 0) as CORP_Change,
       ISNULL(sum([GROUP]), 0) as GROUP_Current,
       ISNULL(sum([GROUP_Change]), 0) as GROUP_Change,
       ISNULL(sum([OTA]), 0) as OTA_Current,
       ISNULL(sum([OTA_Change]), 0) as OTA_Change,
       ISNULL(sum([PROMO]), 0) as PROMO_Current,
       ISNULL(sum([PROMO_Change]), 0) as PROMO_Change,
       ISNULL(sum([SALE]), 0) as SALE_Current,
       ISNULL(sum([SALE_Change]), 0) as SALE_Change,
       ISNULL(sum([WHOLESALE]), 0) as WHOLESALE_Current,
       ISNULL(sum([WHOLESALE_Change]), 0) as WHOLESALE_Change
from
    (
        select
            currentBg.*,
            currentBg.Business_Group_Name + '_Change' as Business_Group_Name_Change,
            -(currentBg.Business_Group_ID + 1000) as Business_Group_ID_OccFcst_Change,
            isnull(currentBg.occupancy_forecast_current, 0) - ISNULL(lastBg.occupancy_nbr, isnull(currentBg.occupancy_forecast_current, 0))  as occupancyForecastChange
        from
            #BusinessGroupOccupancyFcstActivity as currentBg
                left join
            #BusinessGroupOccupancyFcstActivityLastBDE as lastBg
            on currentBg.occupancy_dt = lastBg.occupancy_dt and currentBg.property_id = lastBg.property_id and currentBg.Business_Group_ID = lastBg.Business_Group_ID
    ) as t
    pivot
       (
              sum(occupancy_forecast_current) for Business_Group_Name in ([BARFLEX],[BARSAVER],[CORP],[GROUP],[OTA],[PROMO],[SALE],[WHOLESALE])
       ) a
       pivot
       (
              sum(occupancyForecastChange) for Business_Group_Name_Change in ([BARFLEX_Change],[BARSAVER_Change],[CORP_Change],[GROUP_Change],[OTA_Change],[PROMO_Change],[SALE_Change],[WHOLESALE_Change])
       ) b group by occupancy_dt, property_id


--------------------------------------------------------------------------------------
    IF OBJECT_ID('tempdb..#cp_decision_last_bde') IS NOT NULL DROP TABLE #cp_decision_last_bde
create table #cp_decision_last_bde
(
    Arrival_DT date,
    Product_ID int,
    Decision_ID int,
    Accom_Type_ID int,
    Final_BAR numeric(19,2)
)

DECLARE @Max_Decision_Id INT

SELECT @Max_Decision_Id = Max(decision_id)
FROM   decision d
WHERE  d.business_dt = @lastBdeDate
  AND d.decision_type_id = 1;

INSERT INTO #cp_decision_last_bde
select
    Arrival_DT,
    Product_ID,
    Decision_ID,
    Accom_Type_ID,
    Final_BAR from (
    select
        Arrival_DT,
        cpBarPace.Product_ID,
        cpBarPace.Decision_ID,
        cpBarPace.Accom_Type_ID,
        Final_BAR,
        ROW_NUMBER() over(PARTITION BY cpBarPace.Arrival_DT, cpBarPace.Product_Id, cpBarPace.Accom_Type_ID order by cpBarPace.decision_id desc) as rn
    from CP_Pace_Decision_Bar_Output_differential cpBarPace
             inner join Decision d on cpBarPace.Decision_id = d.Decision_ID
             inner join Accom_Type at on cpBarPace.Accom_Type_ID = at.Accom_Type_ID
        inner join Product p on cpBarPace.Product_ID = p.Product_ID
    where
        cpBarPace.business_dt <= @lastBdeDate
            AND cpBarPace.Arrival_DT between dateadd(day, 1, @lastBdeDate)  and @endDate
            and at.status_id = 1
            and p.Status_ID = 1
            and d.decision_type_id = 1
) as n where n.rn = 1
ORDER  BY arrival_dt,
    accom_type_id


--------------------------------------main query starts here---------------------------------------------------------------

select
    (select Property_Name from #Property_Table) as 'Property Name',
        (select right((select property_name from #Property_Table),6)) as 'Property Identifier',
    @caughtUpDate as 'File Date (todays date)',
    cal.Occupancy_DT as 'Occupancy Date',
    cal.DOW as 'Occupancy Date DOW',
    overrides.Overrides as 'Overrides',
    specialEventThisYear.special_event_name as 'Special Event TY',
    specialEventLastYear.special_event_name as 'Special Event LY',
    outOfOrder.Out_Of_Order as 'Out of Order',
    activityChange.current_rooms_sold as 'Total Hotel Current Rooms Sold',
    activityChange.roomssoldchange as 'Total Hotel Rooms Sold Change',
    bgRS.BARFLEX_Current as 'BARFLEX Current Rooms Sold',
    bgRS.BARFLEX_Change as 'BARFLEX Rooms Sold Change',
    bgRS.BARSAVER_Current as 'BARSAVER Current Rooms Sold',
    bgRS.BARSAVER_Change as 'BARSAVER Rooms Sold Change',
    bgRS.CORP_Current as 'CORP Current Rooms Sold',
    bgRS.CORP_Change as 'CORP Rooms Sold Change',
    bgRS.GROUP_Current as 'GROUP Current Rooms Sold',
    bgRS.GROUP_Change as 'GROUP Rooms Sold Change',
    bgRS.OTA_Current as 'OTA Current Rooms Sold',
    bgRS.OTA_Change as 'OTA Rooms Sold Change',
    bgRS.PROMO_Current as 'PROMO Current Rooms Sold',
    bgRS.PROMO_Change as 'PROMO Rooms Sold Change',
    bgRS.SALE_Current as 'SALE Current Rooms Sold',
    bgRS.SALE_Change as 'SALE Rooms Sold Change',
    bgRS.WHOLESALE_Current as 'WHOLESALE Current Rooms Sold',
    bgRS.WHOLESALE_Change as 'WHOLESALE Rooms Sold Change',
    occupancyForecastChange.currentOccupancyForecast as 'Total Hotel Current Occupancy Forecast',
    occupancyForecastChange.occupancyForecastChange as 'Total Hotel Occupancy Forecast Change',
    bgOccFcst.BARFLEX_Current as 'BARFLEX Current Occupancy Forecast',
    bgOccFcst.BARFLEX_Change as 'BARFLEX Occupancy Forecast Change',
    bgOccFcst.BARSAVER_Current as 'BARSAVER Current Occupancy Forecast',
    bgOccFcst.BARSAVER_Change as 'BARSAVER Occupancy Forecast Change',
    bgOccFcst.CORP_Current as 'CORP Current Occupancy Forecast',
    bgOccFcst.CORP_Change as 'CORP Occupancy Forecast Change',
    bgOccFcst.GROUP_Current as 'GROUP Current Occupancy Forecast',
    bgOccFcst.GROUP_Change as 'GROUP Occupancy Forecast Change',
    bgOccFcst.OTA_Current as 'OTA Current Occupancy Forecast',
    bgOccFcst.OTA_Change as 'OTA Occupancy Forecast Change',
    bgOccFcst.PROMO_Current as 'PROMO Current Occupancy Forecast',
    bgOccFcst.PROMO_Change as 'PROMO Occupancy Forecast Change',
    bgOccFcst.SALE_Current as 'SALE Current Occupancy Forecast',
    bgOccFcst.SALE_Change as 'SALE Occupancy Forecast Change',
    bgOccFcst.WHOLESALE_Current as 'WHOLESALE Current Occupancy Forecast',
    bgOccFcst.WHOLESALE_Change as 'WHOLESALE Occupancy Forecast Change',
    occupancyForecastChange.currentOccupancyForecastPercent as 'Total Hotel Current Occupancy Forecast%',
    occupancyForecastChange.occupancyForecastPercentChange as 'Total Hotel Occupancy Forecast% Change',
    activityChange.currentRoomRevenue as 'Total Hotel Current Room Revenue',
    activityChange.roomRevenueChange as 'Total Hotel Room Revenue Change',
    bgRevenue.BARFLEX_Current as 'BARFLEX Current Room Revenue',
    bgRevenue.BARFLEX_Change as 'BARFLEX Room Revenue Change',
    bgRevenue.BARSAVER_Current as 'BARSAVER Current Room Revenue',
    bgRevenue.BARSAVER_Change as 'BARSAVER Room Revenue Change',
    bgRevenue.CORP_Current as 'CORP Current Room Revenue',
    bgRevenue.CORP_Change as 'CORP Room Revenue Change',
    bgRevenue.GROUP_Current as 'GROUP Current Room Revenue',
    bgRevenue.GROUP_Change as 'GROUP Room Revenue Change',
    bgRevenue.OTA_Current as 'OTA Current Room Revenue',
    bgRevenue.OTA_Change as 'OTA Room Revenue Change',
    bgRevenue.PROMO_Current as 'PROMO Current Room Revenue',
    bgRevenue.PROMO_Change as 'PROMO Room Revenue Change',
    bgRevenue.SALE_Current as 'SALE Current Room Revenue',
    bgRevenue.SALE_Change as 'SALE Room Revenue Change',
    bgRevenue.WHOLESALE_Current as 'WHOLESALE Current Room Revenue',
    bgRevenue.WHOLESALE_Change as 'WHOLESALE Room Revenue Change',
    occupancyForecastChange.currentAdr as 'Total Hotel Current Booked ADR',
    occupancyForecastChange.adrChange as 'Total Hotel Booked ADR Change',
    bgAdr.BARFLEX_Current as 'BARFLEX Current Booked ADR',
    bgAdr.BARFLEX_Change as 'BARFLEX Booked ADR Change',
    bgAdr.BARSAVER_Current as 'BARSAVER Current Booked ADR',
    bgAdr.BARSAVER_Change as 'BARSAVER Booked ADR Change',
    bgAdr.CORP_Current as 'CORP Current Booked ADR',
    bgAdr.CORP_Change as 'CORP Booked ADR Change',
    bgAdr.GROUP_Current as 'GROUP Current Booked ADR',
    bgAdr.GROUP_Change as 'GROUP Booked ADR Change',
    bgAdr.OTA_Current as 'OTA Current Booked ADR',
    bgAdr.OTA_Change as 'OTA Booked ADR Change',
    bgAdr.PROMO_Current as 'PROMO Current Booked ADR',
    bgAdr.PROMO_Change as 'PROMO Booked ADR Change',
    bgAdr.SALE_Current as 'SALE Current Booked ADR',
    bgAdr.SALE_Change as 'SALE Booked ADR Change',
    bgAdr.WHOLESALE_Current as 'WHOLESALE Current Booked ADR',
    bgAdr.WHOLESALE_Change as 'WHOLESALE Booked ADR Change',
    lrvForSTDRC.lrv as 'STD LRV Current',
    lrvForSTDRC.lrvChange as 'STD LRV Change',
    lrvForFAMRC.lrv as 'FAM LRV Current',
    lrvForFAMRC.lrvChange as 'FAM LRV Change',
    lrvForSUPRC.lrv as 'SUP LRV Current',
    lrvForSUPRC.lrvChange as 'SUP LRV Change',
    overbookingDecisionChange.currentOverbookingDecision as 'Overbooking Total Hotel Current',
    isnull(overbookingDecisionChange.currentOverbookingDecision, 0) - ISNULL(overbookingDecisionChange.lastBdeOverbookingDecision, isnull(overbookingDecisionChange.currentOverbookingDecision, 0))  as 'Overbooking Total Hotel Change',
    productPrice.currentBarProductFor_BARFLEX_STD as 'BARFLEX STD Current',
    productPrice.changeBarProductFor_BARFLEX_STD as 'BARFLEX STD Change',
    productPrice.currentBarProductFor_BARFLEX_FAM as 'BARFLEX FAM Current',
    productPrice.changeBarProductFor_BARFLEX_FAM as 'BARFLEX FAM Change',
    productPrice.currentBarProductFor_BARFLEX_SUP as 'BARFLEX SUP Current',
    productPrice.changeBarProductFor_BARFLEX_SUP as 'BARFLEX SUP Change',
    productPrice.currentBarProductFor_SAVER_STD as 'SAVER STD Current',
    productPrice.changeBarProductFor_SAVER_STD as 'SAVER STD Change',
    productPrice.currentBarProductFor_SAVER_FAM as 'SAVER FAM Current',
    productPrice.changeBarProductFor_SAVER_FAM as 'SAVER FAM Change',
    productPrice.currentBarProductFor_SAVER_SUP as 'SAVER SUP Current',
    productPrice.changeBarProductFor_SAVER_SUP as 'SAVER SUP Change'
from
    #Calendar as cal
    left join
    (
    select
    case when sum(ovr.Number_Of_Overrides) > 0 then 'Y' end Overrides,
    ovr.Occupancy_DT
    from
    (
    select Count(*) as Number_Of_Overrides, Occupancy_DT from Occupancy_Demand_FCST_OVR where Occupancy_DT between @startDate and @endDate and Status_ID = 1 group by Occupancy_DT
    union
    select Count(*) as Number_Of_Overrides, Arrival_DT as Occupancy_DT from Arrival_Demand_FCST_OVR where Arrival_DT between @startDate and @endDate and Status_ID = 1 group by Arrival_DT
    union
    select Count(*) as Number_Of_Overrides, Occupancy_DT from Wash_Forecast_Group_FCST_OVR where Occupancy_DT between @startDate and @endDate and Status_ID = 1 group by Occupancy_DT
    ) as ovr
    group by Occupancy_DT
    ) as overrides
on overrides.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select
    special_event_name, event_cal_date
    from
    ufn_get_special_event_by_property (@propertyId,@startDate, @endDate)
    ) as specialEventThisYear
    on specialEventThisYear.event_cal_date = cal.Occupancy_DT
    left join
    (
    select
    special_event_name, event_cal_date
    from
    ufn_get_special_event_by_property
    (
    @propertyId,
    DateAdd(WEEK,-52,CAST(@startDate as date)),
    DateAdd(WEEK,-52,CAST(@endDate as date))
    )
    )as specialEventLastYear
    on specialEventLastYear.event_cal_date = DateAdd(WEEK,-52,CAST(cal.Occupancy_DT as date))
    left join
    (
    select
    Occupancy_DT, Rooms_Not_Avail_Maint as Out_Of_Order
    from
    Total_Activity
    where
    Occupancy_DT between @startDate and @endDate
    ) as outOfOrder
    on outOfOrder.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select
    currentActivity.Occupancy_DT,
    currentActivity.Rooms_Sold as current_rooms_sold,
    isnull(currentActivity.rooms_sold, 0) - ISNULL(lastBdeActivity.rooms_sold, isnull(currentActivity.rooms_sold, 0))  as roomssoldchange,
    cast(currentActivity.Room_Revenue as numeric(19,2)) as currentRoomRevenue,
    cast(isnull(currentActivity.Room_Revenue, 0) - ISNULL(lastBdeActivity.Room_Revenue, isnull(currentActivity.Room_Revenue, 0)) as numeric(19,2))  as roomRevenueChange
    from
    (
    select
    Occupancy_DT, Rooms_Sold, Room_Revenue
    from
    Total_Activity
    where
    Occupancy_DT between @startDate and @endDate
    ) as currentActivity
    left join
    (
    select
    Occupancy_DT, Rooms_Sold, Room_Revenue
    from
    PACE_Total_Activity as pace
    where
    Business_Day_End_DT = @lastBdeDate
    and Occupancy_DT between @startDate and @endDate

    )as lastBdeActivity
    on currentActivity.Occupancy_DT = lastBdeActivity.Occupancy_DT
    ) as activityChange
    on activityChange.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select
    currentForecast.Occupancy_DT,
    currentForecast.Occupancy_Forecast as currentOccupancyForecast,
    lastBdeForecast.occupancy_nbr as lastOccupancyForecast,
    isnull(currentForecast.Occupancy_Forecast, 0) - ISNULL(lastBdeForecast.occupancy_nbr, isnull(currentForecast.Occupancy_Forecast, 0))  as occupancyForecastChange,
    currentForecast.Occupancy_Percent_with_physical_capacity as currentOccupancyForecastPercent,
    cast(isnull(currentForecast.Occupancy_Percent_with_physical_capacity, 0) - ISNULL(lastBdeForecast.Occupancy_Percent_with_physical_capacity, isnull(currentForecast.Occupancy_Percent_with_physical_capacity, 0)) as numeric(19,2))  as occupancyForecastPercentChange,
    currentForecast.ADR as currentAdr,
    isnull(currentForecast.ADR, 0) - ISNULL(lastBdeForecast.ADR, isnull(currentForecast.ADR, 0))  as adrChange
    from
    (
    select
    Occupancy_DT,
    cast(Occupancy_NBR as numeric(19,2)) as Occupancy_Forecast,
    cast(Occupancy_Percent as numeric(19,2)) as Occupancy_Percent,
    cast(ADR as numeric(19,2)) as ADR,
    Occupancy_Percent_with_physical_capacity =
    CASE Total_Accom_Capacity
    WHEN 0 THEN 0
    ELSE cast(((Occupancy_NBR  / Total_Accom_Capacity)  * 100) as numeric(19,2))
    END
    from
    fn_occupancy_forecast(@caughtUpDate, 1)
    where
    Occupancy_DT between @startDate and @endDate
    )as currentForecast
    left join
    (
    select
    occupancy_dt,
    cast(occupancy_nbr as numeric(19,2)) as occupancy_nbr,
    cast(occupancy_perc as numeric(19,2)) as occupancy_perc,
    cast(adr as numeric(19,2)) as adr,
    cast(occupancy_perc_PhyCap as numeric(19,2)) as Occupancy_Percent_with_physical_capacity
    from
    dbo.ufn_get_occupancy_forecast_asof_businessdate_by_property
    (
    @propertyId,
    @lastBdeDate,
    @startDate,
    @endDate,
    1
    )
    )as lastBdeForecast
    on lastBdeForecast.occupancy_dt = currentForecast.Occupancy_DT
    ) as occupancyForecastChange
    on occupancyForecastChange.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select
    currentOverBooking.Occupancy_DT,
    currentOverBooking.Overbooking_Decision as currentOverbookingDecision,
    lastBdeOverbooking.overbooking_decision as lastBdeOverbookingDecision,
    currentOverBooking.Property_ID
    from
    (
    select property_id, occupancy_dt, overbooking_decision
    from decision_ovrbk_property
    where property_id = @propertyId
    and occupancy_dt between @startDate and @endDate
    ) as currentOverBooking
    left join
    (
    select
    property_id, occupancy_dt, overbooking_decision
    from ufn_get_ovrbk_decision_asof_businessdate_by_property
    (
    @propertyId,
    @lastBdeDate,
    @startDate,
    @endDate
    )
    )as lastBdeOverbooking
    on lastBdeOverbooking.occupancy_dt = currentOverBooking.Occupancy_DT and lastBdeOverbooking.property_id = currentOverBooking.Property_ID
    ) as overbookingDecisionChange
    on overbookingDecisionChange.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select * from #BGRoomSoldTempTable
    ) as bgRS
    on bgRS.occupancy_dt = cal.Occupancy_DT
    left join
    (
    select * from #BGRevenueTempTable
    ) as bgRevenue
    on bgRevenue.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select * from #BGAdrTempTable
    ) as bgAdr
    on bgAdr.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select * from #BGOccFcstTempTable
    ) as bgOccFcst
    on bgOccFcst.Occupancy_DT = cal.Occupancy_DT
    left join
    (
    select currentLrv.*,
    isnull(currentLrv.lrv, 0) - ISNULL(lastBdeLrv.lrv, isnull(currentLrv.lrv, 0))  as lrvChange
    from
    (
    select * from ufn_get_decision_lrv_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'STD' and Status_ID=1),
    @startDate,
    @endDate
    )
    ) as currentLrv
    left join
    (
    select * from ufn_get_lrv_asof_lastOptimization_or_businessdate_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'STD' and Status_ID=1),
    @lastBdeDate,
    @startDate,
    @endDate,
    null
    )
    ) as lastBdeLrv
    on currentLrv.occupancy_dt = lastBdeLrv.occupancy_dt and currentLrv.property_id = lastBdeLrv.property_id
    ) as lrvForSTDRC
    on lrvForSTDRC.occupancy_dt = cal.Occupancy_DT
    left join
    (
    select currentLrv.*,
    isnull(currentLrv.lrv, 0) - ISNULL(lastBdeLrv.lrv, isnull(currentLrv.lrv, 0))  as lrvChange
    from
    (
    select * from ufn_get_decision_lrv_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'FAM'  and Status_ID=1),
    @startDate,
    @endDate
    )
    ) as currentLrv
    left join
    (
    select * from ufn_get_lrv_asof_lastOptimization_or_businessdate_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'FAM' and Status_ID=1),
    @lastBdeDate,
    @startDate,
    @endDate,
    null
    )
    ) as lastBdeLrv
    on currentLrv.occupancy_dt = lastBdeLrv.occupancy_dt and currentLrv.property_id = lastBdeLrv.property_id
    ) as lrvForFAMRC
    on lrvForFAMRC.occupancy_dt = cal.Occupancy_DT
    left join
    (
    select currentLrv.*,
    isnull(currentLrv.lrv, 0) - ISNULL(lastBdeLrv.lrv, isnull(currentLrv.lrv, 0))  as lrvChange
    from
    (
    select * from ufn_get_decision_lrv_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'SUP' and Status_ID=1),
    @startDate,
    @endDate
    )
    ) as currentLrv
    left join
    (
    select * from ufn_get_lrv_asof_lastOptimization_or_businessdate_by_rc (
    @propertyId,
    (select Accom_Class_ID from Accom_Class where Accom_Class_Name = 'SUP' and Status_ID=1),
    @lastBdeDate,
    @startDate,
    @endDate,
    null
    )
    ) as lastBdeLrv
    on currentLrv.occupancy_dt = lastBdeLrv.occupancy_dt and currentLrv.property_id = lastBdeLrv.property_id
    ) as lrvForSUPRC
    on lrvForSUPRC.occupancy_dt = cal.Occupancy_DT
    left join
    (
    select
    Occupancy_DT,
    sum(currentBarProductFor_BARFLEX_STD) as currentBarProductFor_BARFLEX_STD,
    sum(changeBarProductFor_BARFLEX_STD) as changeBarProductFor_BARFLEX_STD,
    sum(currentBarProductFor_BARFLEX_FAM) as currentBarProductFor_BARFLEX_FAM,
    sum(changeBarProductFor_BARFLEX_FAM) as changeBarProductFor_BARFLEX_FAM,
    sum(currentBarProductFor_BARFLEX_SUP) as currentBarProductFor_BARFLEX_SUP,
    sum(changeBarProductFor_BARFLEX_SUP) as changeBarProductFor_BARFLEX_SUP,
    sum(currentBarProductFor_SAVER_STD) as currentBarProductFor_SAVER_STD,
    sum(changeBarProductFor_SAVER_STD) as changeBarProductFor_SAVER_STD,
    sum(currentBarProductFor_SAVER_FAM) as currentBarProductFor_SAVER_FAM,
    sum(changeBarProductFor_SAVER_FAM) as changeBarProductFor_SAVER_FAM,
    sum(currentBarProductFor_SAVER_SUP) as currentBarProductFor_SAVER_SUP,
    sum(changeBarProductFor_SAVER_SUP) as changeBarProductFor_SAVER_SUP
    from
    (
    select
    Occupancy_DT,
    case t.Product_AccomClass when 'BARFLEXSTD' then t.currentBarProduct end as currentBarProductFor_BARFLEX_STD,
    case t.Product_AccomClass when 'BARFLEXSTD' then t.barProductChange end as changeBarProductFor_BARFLEX_STD,
    case t.Product_AccomClass when 'BARFLEXFAM' then t.currentBarProduct end as currentBarProductFor_BARFLEX_FAM,
    case t.Product_AccomClass when 'BARFLEXFAM' then t.barProductChange end as changeBarProductFor_BARFLEX_FAM,
    case t.Product_AccomClass when 'BARFLEXSUP' then t.currentBarProduct end as currentBarProductFor_BARFLEX_SUP,
    case t.Product_AccomClass when 'BARFLEXSUP' then t.barProductChange end as changeBarProductFor_BARFLEX_SUP,
    case t.Product_AccomClass when 'SAVERSTD' then t.currentBarProduct end as currentBarProductFor_SAVER_STD,
    case t.Product_AccomClass when 'SAVERSTD' then t.barProductChange end as changeBarProductFor_SAVER_STD,
    case t.Product_AccomClass when 'SAVERFAM' then t.currentBarProduct end as currentBarProductFor_SAVER_FAM,
    case t.Product_AccomClass when 'SAVERFAM' then t.barProductChange end as changeBarProductFor_SAVER_FAM,
    case t.Product_AccomClass when 'SAVERSUP' then t.currentBarProduct end as currentBarProductFor_SAVER_SUP,
    case t.Product_AccomClass when 'SAVERSUP' then t.barProductChange end as changeBarProductFor_SAVER_SUP
    from
    (
    select
    currentBarProduct.Arrival_DT as Occupancy_DT,
    currentBarProduct.Accom_Class_ID, currentBarProduct.Accom_Class_Name,
    currentBarProduct.Name,
    currentBarProduct.Name + currentBarProduct.Accom_Class_Name as Product_AccomClass,
    currentBarProduct.Final_BAR as currentBarProduct,
    isnull(currentBarProduct.Final_BAR, 0) - ISNULL(lastBdeBarProduct.Final_BAR, isnull(currentBarProduct.Final_BAR, 0))  as barProductChange
    from
    (
    select Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, cpBar.Product_ID,
    case p.System_Default when 1 then 'SAVER' else p.Name end as Name, Final_BAR
    from
    CP_Decision_Bar_Output as cpBar
    inner join
    CP_Cfg_AC as baseRt
    on cpBar.Accom_Type_ID = baseRt.Accom_Type_ID
    inner join
    Accom_Class as ac
    on ac.Accom_Class_ID = baseRt.Accom_Class_ID and ac.Accom_Class_Name in ('STD','FAM','SUP')
    inner join
    Product as p
    on p.Product_ID = cpBar.Product_ID and (p.Name = 'BARFLEX' or p.System_Default = 1)
    where Arrival_DT between @startDate and @endDate and (p.Name = 'BARFLEX' or p.System_Default = 1)
    and ac.Status_ID=1
    ) as currentBarProduct
    left join
    (
    select cpBarPace.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, cpBarPace.Product_ID, p.Name, cpBarPace.Final_BAR
    from #cp_decision_last_bde cpBarPace
    inner join
    CP_Cfg_AC as baseRt
    on cpBarPace.Accom_Type_ID = baseRt.Accom_Type_ID
    inner join
    Accom_Class as ac
    on ac.Accom_Class_ID = baseRt.Accom_Class_ID and ac.Accom_Class_Name in ('STD','FAM','SUP')
    inner join
    Product as p
    on p.Product_ID = cpBarPace.Product_ID and (p.Name = 'BARFLEX' or p.System_Default = 1)
    where (p.Name = 'BARFLEX' or p.System_Default = 1) and ac.Status_ID=1
    ) as lastBdeBarProduct
    on lastBdeBarProduct.Arrival_DT = currentBarProduct.Arrival_DT and currentBarProduct.Accom_Class_ID = lastBdeBarProduct.Accom_Class_ID
    and currentBarProduct.Product_ID = lastBdeBarProduct.Product_ID
    ) as t
    ) as productRates
    group by Occupancy_DT
    ) as productPrice
    on productPrice.Occupancy_DT = cal.Occupancy_DT
order by cal.Occupancy_DT asc

end
GO

