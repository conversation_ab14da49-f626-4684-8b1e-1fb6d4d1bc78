DROP PROCEDURE IF EXISTS [dbo].[usp_PP_Occupancy_FCST_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_PP_Occupancy_FCST_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_PP_Occupancy_FCST_Insert]
GO
DROP TYPE IF EXISTS [dbo].[PP_Occupancy_FCST_Batch]
GO

CREATE TYPE dbo.PP_Occupancy_FCST_Batch AS TABLE
(
    [Property_ID]  [int],
    [Decision_ID]  [bigint],
    [Occupancy_DT] [date],
    [Num_Adult]    [int],
    [Num_Child]    [int],
    [Created_DTTM] [datetime]
);
GO

CREATE PROCEDURE usp_PP_Occupancy_FCST_Upsert @PP_Occupancy_FCST_Batch PP_Occupancy_FCST_Batch READONLY
AS
BEGIN
MERGE INTO dbo.PP_Occupancy_FCST AS Target
    USING @PP_Occupancy_FCST_Batch AS Source
    ON (Target.Property_ID = Source.Property_ID
        AND Target.Occupancy_DT = Source.Occupancy_DT)
    WHEN MATCHED THEN
        UPDATE
            SET Target.Decision_ID  = Source.Decision_ID,
                Target.Num_Adult    = Source.Num_Adult,
                Target.Num_Child    = Source.Num_Child,
                Target.Created_DTTM = Source.Created_DTTM
    WHEN NOT MATCHED THEN
        INSERT (Property_ID, Decision_ID, Occupancy_DT, Num_Adult, Num_Child, Created_DTTM)
            VALUES (Source.Decision_ID, Source.Property_ID, Source.Occupancy_DT, Source.Num_Adult,
                    Source.Num_Child, Source.Created_DTTM);
END
GO

CREATE PROCEDURE usp_PP_Occupancy_FCST_Update @PP_Occupancy_FCST_Batch PP_Occupancy_FCST_Batch READONLY
AS
BEGIN
UPDATE dbo.PP_Occupancy_FCST
SET Decision_ID  = Source.Decision_ID,
    Num_Adult    = Source.Num_Adult,
    Num_Child    = Source.Num_Child,
    Created_DTTM = Source.Created_DTTM
    FROM @PP_Occupancy_FCST_Batch AS Source
WHERE [PP_Occupancy_FCST].Property_ID = Source.Property_ID
  AND [PP_Occupancy_FCST].Occupancy_DT = Source.Occupancy_DT;
END
GO

CREATE PROCEDURE usp_PP_Occupancy_FCST_Insert @PP_Occupancy_FCST_Batch PP_Occupancy_FCST_Batch READONLY
AS
BEGIN
INSERT INTO dbo.PP_Occupancy_FCST (Property_ID, Decision_ID, Occupancy_DT, Num_Adult, Num_Child, Created_DTTM)
SELECT Decision_ID, Property_ID, Occupancy_DT, Num_Adult, Num_Child, Created_DTTM
FROM @PP_Occupancy_FCST_Batch PP_Occupancy_FCST_Batch;
END
GO