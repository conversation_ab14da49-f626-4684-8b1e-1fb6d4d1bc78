if exists(select *
          from sys.objects
          where object_id = object_id(N'[dbo].[usp_occupancy_by_prop_bv_single_prop]'))
    drop procedure [dbo].[usp_occupancy_by_prop_bv_single_prop]

GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp_occupancy_by_prop_bv_single_prop]
(
	@startDate DATE,
	@endDate DATE,
	@caughtUpDate DATE
)
as
begin

SELECT ISNULL(bg.Business_Group_Name,'Unassigned') as Business_Group_name
     , SUM(CASE WHEN maa.Occupancy_DT >= @caughtUpDate THEN	occ.Occupancy_NBR ELSE maa.Rooms_Sold END) as Occupancy_NBR
FROM Mkt_Accom_Activity AS maa
         INNER JOIN Accom_Type AS at ON maa.Accom_Type_ID = at.Accom_Type_ID
    and at.Status_ID=1
    and at.isComponentRoom = 'N'
    and at.Display_Status_ID = 1
    and at.System_Default=0
    INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID
    and ac.Status_ID=1
    and ac.System_Default=0
    INNER JOIN Mkt_Seg AS ms ON maa.Mkt_Seg_ID = ms.Mkt_Seg_ID
    INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON ms.MKT_SEG_ID = msfg.Mkt_Seg_ID
    and msfg.Status_ID=1
    LEFT JOIN Occupancy_FCST AS occ ON occ.Occupancy_DT = maa.Occupancy_DT
    and occ.Accom_Type_ID = maa.Accom_Type_ID
    and occ.MKT_SEG_ID = msfg.Mkt_Seg_ID
    and occ.Occupancy_DT >= @caughtUpDate
    LEFT JOIN Mkt_Seg_Business_Group msbg on ms.Mkt_Seg_ID=msbg.Mkt_Seg_ID
    LEFT JOIN Business_Group as bg on bg.Business_Group_ID = msbg.Business_Group_ID
WHERE maa.Occupancy_DT between @startDate and @endDate
GROUP BY ISNULL(bg.Business_Group_Name,'Unassigned')

end
GO