if exists(select *
          from sys.objects
          where object_id = object_id(N'[usp_getOccupancy_Fcst_byOccupancyDate]'))
    drop procedure [usp_getOccupancy_Fcst_byOccupancyDate]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************


Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
04/12/2024		Vaibhav				Javadekar					BOSE-8414
***************************************************************************************/

CREATE procedure [dbo].[usp_getOccupancy_Fcst_byOccupancyDate]
(
	
	@occupancyDate date
)		

as

begin
select COALESCE((sum(Rooms_Sold)/NULLIF(sum(Accom_Capacity - (Rooms_Not_avail_maint + Rooms_Not_Avail_other)), 0))*100, 0) as occupancy_percentage
from Accom_Activity where Occupancy_DT = @occupancyDate
end
