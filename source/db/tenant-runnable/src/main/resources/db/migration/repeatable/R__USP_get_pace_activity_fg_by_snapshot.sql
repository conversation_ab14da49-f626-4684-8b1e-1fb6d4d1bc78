DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_activity_fg_by_snapshot]
/****** Object:  Stored Procedure [dbo].[usp_get_pace_activity_fg_by_snapshot]    Script date: 1/23/2021 5:56:22 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Stored Procedure Name: usp_get_pace_activity_fg_by_snapshot

Input Parameters : 
	@property_id --> property Id Associated with a property (e.g.,'BOSCO' id FROM the property table is 12)
	@start_date --> start date FROM which you need pace ('2011-07-01')
	@end_date --> END date till which you need pace ('2011-07-31')
	@past_start_date --> maximum past date till which we want pace FROM 
	@Forecast_group_ids --> Forecast group for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)
	
Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_activity_fg_by_snapshot 10027,'2012-09-20', '2012-10-30','2012-07-17','1,2,3','0'

Purpose: The purpose of this stored procedure is to extract pace of Actvity for a given property and Forecast group by given date range. 

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/23/2021		Anil				Borgude					Initial Version
06/30/2021      Shilpa              Shaha                   Fix for BHASK-1594 BAD Screen : Pace Data tab | 0 Diet
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/

CREATE PROCEDURE [dbo].[usp_get_pace_activity_fg_by_snapshot]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@forecast_group_ids nvarchar(100),
    @exclude_comp_rooms varchar(5)
)		
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Businessdate date 
	SET @Businessdate = DATEADD(DAY, -1, CAST((SELECT dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) AS date)) --> extract caughtup date for a property
	
	DECLARE @temp_end_date AS date = @end_date
	IF(@Businessdate>@end_date)
	BEGIN
		SET @temp_end_date = @Businessdate
	END

	DECLARE @Forecast_Groups table (Forecast_Group_Id int, Forecast_Group_Name varchar(100))
	INSERT @Forecast_Groups SELECT Value, Forecast_Group_Name FROM varcharToInt(@forecast_group_ids,',')
	Inner Join Forecast_Group fg on fg.Forecast_Group_Id = Value AND Status_ID = 1
		
	Create table #temp_fg_activity
	(
		Forecast_GROUP_ID int,
		occupancy_dt date,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
	
	INSERT INTO #temp_fg_activity
	SELECT msfg.Forecast_GROUP_ID, Occupancy_DT, SUM(Rooms_Sold)Rooms_Sold, SUM(Room_Revenue)Room_Revenue FROM 
	(
		SELECT Occupancy_DT,mkt.Mkt_Seg_ID,SUM(case when AT.isComponentRoom = 'Y' then 0 else Rooms_Sold END) AS Rooms_Sold,
               SUM(case when AT.isComponentRoom = 'Y' then 0.0 else Room_Revenue END) AS Room_Revenue FROM Mkt_Accom_Activity mkt
        INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = mkt.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@exclude_comp_rooms, ','))
		INNER JOIN Accom_Type AT ON
				mkt.Accom_Type_ID = AT.Accom_Type_ID AND mkt.Property_ID = AT.Property_ID  
		WHERE Occupancy_DT BETWEEN @start_date AND @temp_end_date AND mkt.Property_ID=@property_id
		GROUP BY Occupancy_DT,mkt.Mkt_Seg_ID
	) maa	
	 INNER JOIN Mkt_Seg_Forecast_GROUP msfg ON maa.Mkt_Seg_ID=msfg.Mkt_Seg_ID AND msfg.Status_ID=1
	 INNER JOIN @Forecast_Groups fg on fg.Forecast_Group_Id = msfg.Forecast_Group_ID
	GROUP BY Occupancy_DT,msfg.Forecast_GROUP_ID

		Create table #temp_fg_activity_2
		(
			Forecast_GROUP_ID int,
			occupancy_dt date,
			Rooms_Sold numeric(8,0),
            Room_Revenue numeric(19,2)
		)

		Insert into #temp_fg_activity_2
		select Forecast_GROUP_ID, occupancy_dt, Rooms_Sold, Room_Revenue from #temp_fg_activity
		WHERE Occupancy_DT BETWEEN @start_date AND @end_date

		Create table #Pace_Mkt_Activity(
			Business_Day_End_DT date,
			Forecast_Group_ID int,
			Rooms_Sold numeric(8,0),
            Room_Revenue numeric(19,2)
		)
		Insert into #Pace_Mkt_Activity
		select pma.Business_Day_End_DT,msfg.Forecast_GROUP_ID,SUM(Rooms_Sold)Rooms_Sold,SUM(Room_Revenue)Room_Revenue FROM PACE_Mkt_Activity pma
        INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = pma.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@exclude_comp_rooms, ','))
		INNER JOIN Mkt_Seg_Forecast_GROUP msfg ON ms.Mkt_Seg_ID=msfg.Mkt_Seg_ID AND msfg.Status_ID=1
		INNER JOIN @Forecast_Groups fg on fg.Forecast_Group_Id = msfg.Forecast_Group_ID
		where pma.Occupancy_DT BETWEEN @start_date AND @end_date
			AND pma.Business_Day_End_DT BETWEEN @past_start_date AND @Businessdate
		Group By pma.Business_Day_End_DT, msfg.Forecast_Group_ID

/** Calculating Rooms Sold and inserting into temp table **/	
	CREATE table #pace_activity_fcst_fg(	
		Business_Day_End_DT date,
		Forecast_Group_ID int,
		Forecast_Group_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
		
	insert into #pace_activity_fcst_fg
	SELECT Business_Day_End_DT, fg.Forecast_GROUP_ID AS Forecast_GROUP_ID, fg.Forecast_Group_Name AS Forecast_Group_Name, SUM(ISNULL(Rooms_Sold,0))Rooms_Sold, SUM(ISNULL(Room_Revenue,0.0))Room_Revenue
	FROM ( 
		SELECT Business_Day_End_DT, Forecast_Group_ID, Rooms_Sold, Room_Revenue FROM #PACE_Mkt_Activity pma
		UNION ALL
		SELECT a.occupancy_dt, tfa.Forecast_GROUP_ID, tfa.Rooms_Sold, tfa.Room_Revenue 
		FROM #temp_fg_activity_2 tfa JOIN #temp_fg_activity a
			ON tfa.Forecast_GROUP_ID=a.Forecast_GROUP_ID
			AND tfa.occupancy_dt < a.occupancy_dt 
			WHERE a.occupancy_dt BETWEEN @start_date AND @Businessdate 
			) AS base
		JOIN Forecast_GROUP FG ON base.Forecast_Group_ID=FG.Forecast_GROUP_ID
	GROUP BY Business_Day_End_DT,fg.Forecast_GROUP_ID,fg.Forecast_Group_Name
	ORDER BY Business_Day_End_DT,fg.Forecast_GROUP_ID,fg.Forecast_Group_Name

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	SELECT ISNULL(paff.Business_Day_End_DT, DATEADD(day, - 1, base.SnapShot_DT)) Business_Day_End_DT,
	ISNULL(paff.Forecast_Group_ID, base.Forecast_Group_ID) Forecast_Group_ID, 
	ISNULL(paff.Forecast_Group_Name, base.Forecast_Group_Name) Forecast_Group_Name,  
	paff.Rooms_Sold AS Rooms_Sold,
    CAST(paff.Room_Revenue as numeric(19, 2)) AS Room_Revenue,
           CASE
               WHEN (paff.Rooms_Sold IS NULL OR paff.Rooms_Sold = 0) THEN NULL
               WHEN (paff.Room_Revenue IS NULL) THEN NULL
               ELSE CAST(paff.Room_Revenue / paff.Rooms_Sold as numeric(19, 2)) END AS onBooks_ADR
		from
		(
				select @property_id Property_ID,CAST(SnapShot_DT as date) SnapShot_DT, max(fm.File_Metadata_ID) as File_Metadata_ID, fgs.Forecast_Group_Id, fgs.Forecast_Group_Name from File_Metadata fm
				INNER JOIN @Forecast_Groups fgs on Property_ID=@property_id
				where IsBDE = 1 and Record_Type_ID = 3 and SnapShot_DT> @past_start_date and Process_Status_ID=13
				group by SnapShot_DT,Property_ID, fgs.Forecast_Group_Id, fgs.Forecast_Group_Name
			 ) base
			FULL OUTER JOIN  #pace_activity_fcst_fg paff on paff.Business_Day_End_DT = DATEADD(day, -1, base.SnapShot_DT) and base.Forecast_Group_Id = paff.Forecast_Group_ID
			
		ORDER BY Business_Day_End_DT

		Drop table #temp_fg_activity
		Drop table #temp_fg_activity_2
		Drop table #Pace_Mkt_Activity
		Drop table #pace_activity_fcst_fg
END
GO