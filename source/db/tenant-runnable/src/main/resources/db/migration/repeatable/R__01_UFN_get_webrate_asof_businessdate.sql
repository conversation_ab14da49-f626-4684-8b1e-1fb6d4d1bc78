if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_webrate_asof_businessdate]'))
drop function [ufn_get_webrate_asof_businessdate]

GO
/****** Object:  UserDefinedFunction [dbo].[ufn_get_webrate_asof_businessdate]    Script Date: 11/20/2019 4:35:39 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Function Name: ufn_get_webrate_asof_businessdate

Input Parameters :
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@accom_class_id --> accom class id for which you want change in web rate
	@webrate_comp_id --> id associated with a webrate competitor
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@asOf_date --> date as of which you want web rate comp example '2010-07-07'

Ouput Parameter : NA

Execution: this is just an example
	select * from dbo.ufn_get_webrate_asof_businessdate (24,2,2,'2012-08-30', '2012-09-27','2012-07-07')

Purpose: The purpose of this function is to extract competitors webrates for a given property and competitor id as of given date range.

Assumptions : NA

Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
10/11/2012		Atul				Shendye					Initial Version
18th Oct 13		Prasad				Kunte					Update to avoid slow query in production
4/3/2020     Kyle          Vierkant      updated to add @isAdjustRSSForTaxEnabled parameter
8/21/2020     Kyle          Vierkant      Removed @isAdjustRSSForTaxEnabled parameter
08/11/2023		Shrey				Vegda					Adding rate shopping rate type condition
***************************************************************************************
dbcc dropcleanbuffers
exec dbo.usp_hotel_differential_control_report_property 488,3,13,'2013-10-14','2013-10-20','2013-10-31',10,9,8,7,6,5,4,3,2,1,-1,-1,-1,-1,-1, 0, 'LAST_UPDATED-92', 'TODAY-90', 'TODAY+90'
select * from ufn_get_webrate_asof_businessdate(488, 2,10,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,9,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,8,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,7,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,6,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,5,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,4,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,3,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,2,'2013-10-20','2013-12-20','2013-10-14');
select * from ufn_get_webrate_asof_businessdate(488, 2,1,'2013-10-20','2013-12-20','2013-10-14');
Currently, its the assumption that::
Webrate_Source_property_ID & Webrate_Accom_Type_ID  is only 1 for a property.
If this assumption changes then below query may not work properly
*/
CREATE FUNCTION [dbo].[ufn_get_webrate_asof_businessdate]
(
            @property_id INT,
            @accom_class_id INT,
            @webrate_comp_id INT,
            @start_date DATE,
            @end_date DATE,
            @asOf_date DATE
)
RETURNS  @webrate_asof_businessdate TABLE
      (
            property_id INT,
            occupancy_dt DATE,
            webrate_competitors_name NVARCHAR(150),
            webrate_ratevalue NUMERIC(19,5),
            generationDate DATE
      )
AS
BEGIN
	declare @webrateTypeID int;
	set @webrateTypeID = (select top 1 wtp.Webrate_Type_ID from Webrate_Type_Product wtp where wtp.Product_ID=1);

	IF @webrate_comp_id > 0
	BEGIN
		 declare @tax numeric(19,5)
		 declare @tax_type nchar(10)
		 declare @other numeric(19,5)
		 declare @other_type nchar(10)
		 select
			@tax = Tax_Offset_Value,
			@tax_type = Tax_Offset_Type,
			@other = Other_Offset_Value,
			@other_type = Other_Offset_Type
		from Rate_Shopping_Adjustment rsa
		where Property_ID=@property_id 	and Webrate_Competitors_ID = @webrate_comp_id

		  DECLARE @varcharAsOfDate varchar(30)
		  SET @varcharAsOfDate = CONVERT(VARCHAR(30),CONVERT(VARCHAR(10),@asOf_date)  + ' 23:59:59.999',21)
      DECLARE @RoomTaxCalc NUMERIC(7,5)
      select @RoomTaxCalc = [dbo].[ufn_add_room_tax_rate](1)
		  DECLARE @webrate_Source_property_ID INT
		  select @webrate_Source_property_ID=Webrate_Source_Property_ID from webrate_source_property where property_ID = @property_id AND Status_ID in (1, 3)

		  DECLARE @generationDate TABLE ([Webrate_Channel_ID] INT, occ_dt DATE, gen_dt DATETIME)
		  INSERT INTO @generationDate
		  SELECT PW1.[Webrate_Channel_ID], PW1.[Occupancy_DT], MAX(PW1.[Webrate_GenerationDate])
			FROM [PACE_Webrate]  PW1
		   WHERE PW1.Webrate_Source_Property_ID = @webrate_Source_property_ID
				 AND PW1.[Webrate_GenerationDate] <= @varcharAsOfDate
				 AND PW1.[Webrate_Competitors_ID] = @webrate_comp_id
				 AND PW1.[Webrate_Accom_Type_ID]   in
									( select webT.webrate_Accom_Type_ID from webrate_accom_type webT inner join Webrate_Accom_Class_Mapping webM
																	on webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID and webM.Accom_Class_ID = @accom_class_id and  property_ID = @property_id)
				 AND PW1.[LOS] = 1
				 AND PW1.[Webrate_Status] in ('A')
				 AND (PW1.[Occupancy_DT] BETWEEN @start_date AND @end_date)
				 AND (@webrateTypeID is null or PW1.Webrate_Type_ID = @webrateTypeID)
		  GROUP BY [Webrate_Channel_ID], [Webrate_Competitors_ID], [Occupancy_DT]

		  INSERT INTO @webrate_asof_businessdate
		 SELECT property_id,
				 Occupancy_DT,
				 Webrate_Competitors_Name,
				 min(Webrate_RateValue) * @RoomTaxCalc,
				 generationDate
		   FROM  (
				  SELECT DISTINCT REPLACE(CONVERT(VARCHAR(10), pace_web.[Webrate_GenerationDate], 111), '/', '-') AS [generationDate],
						 pace_web.[Occupancy_DT],
						 pace_web.[LOS],
						 pace_web.[Webrate_RateValue],
						 comp.[Webrate_Competitors_Name],
						 @property_id as property_id,
						 comp.[Webrate_Competitors_ID]
					FROM (
						  SELECT PW.[Webrate_GenerationDate],
								 PW.[Webrate_Competitors_ID],
								 PW.[Webrate_Channel_ID],
								 PW.[Webrate_Accom_Type_ID],
								 PW.[Occupancy_DT],
								 PW.[LOS],
								 PW.[Webrate_RateValue]
							FROM [PACE_Webrate] PW
							INNER JOIN @generationDate gend ON PW.[Webrate_GenerationDate] = gend.[gen_dt]
							   AND PW.[Webrate_Channel_ID] = gend.[Webrate_Channel_ID]
							   AND PW.[Occupancy_DT] = gend.[occ_dt]
						   WHERE PW.Webrate_Source_Property_ID = @webrate_Source_property_ID
								AND PW.[Webrate_Competitors_ID] = @webrate_comp_id
								AND PW.[Webrate_Accom_Type_ID]   in
									( select webT.webrate_Accom_Type_ID from webrate_accom_type webT inner join Webrate_Accom_Class_Mapping webM
																	on webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID and webM.Accom_Class_ID = @accom_class_id and  property_ID = @property_id)
								AND (PW.[Occupancy_DT] BETWEEN @start_date AND @end_date)
								AND PW.[LOS] = 1
								AND PW.[Webrate_Status] IN ('A')
								AND (@webrateTypeID is null or PW.Webrate_Type_ID = @webrateTypeID)
						 ) pace_web
						 INNER JOIN
						 (
						  SELECT [Webrate_Competitors_ID],
								 [Webrate_Competitors_Name]
							FROM [Webrate_Competitors]
						   WHERE [Property_ID] = @property_id
							 AND [Webrate_Competitors_ID] = @webrate_comp_id
							 AND [Status_ID] IN (1,2,3)
						 ) comp
							   ON pace_web.[Webrate_Competitors_ID] = comp.[Webrate_Competitors_ID]
						 INNER JOIN
						  (
						   SELECT [Webrate_Channel_ID]
							 FROM [Webrate_Channel]
							WHERE [Property_ID] = @property_id
							  AND [Status_ID] IN (1,3)
						 ) channel
							   ON pace_web.[Webrate_Channel_ID] = channel.[Webrate_Channel_ID]
						 INNER JOIN
						 (
						  SELECT [Accom_Class_ID],
								 [Webrate_Accom_Type_ID]
							FROM [Webrate_Accom_Class_Mapping]
						 ) mapping
							   ON pace_web.[Webrate_Accom_Type_ID] = mapping.[Webrate_Accom_Type_ID]
						 INNER JOIN
						 (
						  SELECT [Occupancy_DT],
								 [Channel_ID]
							FROM [Vw_Webrate_Channel]
						   WHERE [Occupancy_DT] BETWEEN @start_date AND @end_date
							 AND [Property_ID] = @property_id
						 ) default_channel
							   ON pace_web.[Occupancy_DT] = default_channel.[Occupancy_DT]
							  AND pace_web.[Webrate_Channel_ID] = default_channel.[Channel_ID]
						  WHERE mapping.[Accom_Class_ID] = @accom_class_id

					)a group by a.Webrate_Competitors_Name,a.generationDate,a.LOS,a.Occupancy_DT,a.property_id
				
	END
	RETURN
END
