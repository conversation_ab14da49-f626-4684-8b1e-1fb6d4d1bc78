DROP PROCEDURE IF EXISTS [dbo].[usp_backfill_for_profit_population]
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_backfill_for_profit_population]
    @start_date DATE,
    @end_date DATE,
    @property_id INT,
    @excludeComplimentaryRoomRevenueFromProfit INT
AS
BEGIN

SELECT expanded_status INTO #STATUS FROM reservation_status WHERE short_status in ('SS','CO','CI')

-- Handling Reservation Data for Profit.
SELECT
    result_trans.Occupancy_DT,
    result_trans.Booking_DT,
    result_trans.Accom_Type_ID,
    result_trans.Mkt_Seg_ID,
    SUM(result_trans.PROFIT) as PROFIT
INTO #ROOM_PROFIT
FROM
    (
        SELECT
            rn.Reservation_Identifier,
            rn.Occupancy_DT,
            rn.Booking_DT,
            rn.Accom_Type_ID,
            rn.Mkt_Seg_ID,
            (Room_Revenue -
             ( ISNULL(rn.Total_Acquisition_Cost, 0)
                 + (
                   CASE WHEN rn.OCCUPANCY_DT > Arrival_DT THEN
                            CASE WHEN (DATEDIFF(DAY,ARRIVAL_DT,rn.DEPARTURE_DT) - DATEDIFF(DAY,rn.OCCUPANCY_DT,rn.DEPARTURE_DT)) % (serviceCost.Full_Servicing_Interval_Days+1)=0 THEN
                                     CASE WHEN  serviceCost.Full_Servicing_Cost IS NOT NULL THEN serviceCost.Full_Servicing_Cost ELSE 0 END
                                 ELSE
                                     CASE WHEN serviceCost.Interim_Servicing_Cost  IS NOT NULL THEN serviceCost.Interim_Servicing_Cost ELSE 0 END
                                END
                        ELSE 0 END
                   )
                 )
                ) as PROFIT
        FROM
            RESERVATION_NIGHT rn
                JOIN Accom_Type at ON rn.Accom_Type_ID = at.Accom_Type_ID
            JOIN Mkt_Seg_Details msd ON rn.Mkt_Seg_ID =  msd.Mkt_Seg_ID
            LEFT JOIN Servicing_Cost_Cfg serviceCost ON serviceCost.Servicing_Cost_Cfg_ID =
            ( Select
            CASE WHEN
            (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code = rn.Rate_Code ) IS NULL
            THEN (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code IS NULL)
            ELSE (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code = rn.Rate_Code)
            END
            )
        WHERE
            rn.Occupancy_DT >= @start_date AND  rn.Occupancy_DT <= @end_date AND rn.Individual_Status in (SELECT expanded_status FROM #STATUS)
          AND rn.Property_ID = @property_id

        UNION ALL

        SELECT
            DISTINCT rn.Reservation_Identifier,
            rn.Departure_DT as OCCUPANCY_DT,
            rn.Booking_DT,
            rn.Accom_Type_ID,
            rn.Mkt_Seg_ID,
            CASE WHEN serviceCost.Full_Turn_Servicing_Cost IS NOT NULL THEN -serviceCost.Full_Turn_Servicing_Cost ELSE 0 END
            as PROFIT
        FROM
            Reservation_Night rn
            JOIN Accom_Type at ON rn.Accom_Type_ID = at.Accom_Type_ID
            JOIN Mkt_Seg_Details msd ON rn.Mkt_Seg_ID =  msd.Mkt_Seg_ID
            LEFT JOIN Servicing_Cost_Cfg serviceCost ON serviceCost.Servicing_Cost_Cfg_ID =
            ( Select
            CASE WHEN
            (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code=rn.Rate_Code ) IS NULL
            THEN (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code IS NULL)
            ELSE (Select Servicing_Cost_Cfg_ID from Servicing_Cost_Cfg WHERE Accom_Class_ID= at.Accom_Class_ID and Business_Type_ID = msd.Business_Type_ID and Rate_Code=rn.Rate_Code)
            END
            )
        WHERE
            rn.Departure_DT BETWEEN @start_date AND @end_date
          AND rn.Property_ID = @property_id
          AND rn.Individual_Status IN (SELECT expanded_status FROM #STATUS)

        UNION ALL

        Select
            Reservation_Identifier,
            Occupancy_dt,
            Booking_DT,
            accom_type_id,
            Mkt_seg_id,
            room_revenue as Profit
        FROM
            reservation_night
        WHERE
            Individual_status in (SELECT expanded_status FROM reservation_status WHERE short_status IN ('XX','NS'))
          AND Occupancy_DT >= @start_date
          AND  Occupancy_DT <= @end_date
          AND Property_ID = @property_id
    ) result_trans
WHERE
        result_trans.Occupancy_DT >= @start_date AND
        result_trans.Occupancy_DT <= @end_date
GROUP BY result_trans.Occupancy_DT, result_trans.Booking_DT, result_trans.Accom_Type_ID, result_trans.Mkt_Seg_ID

-- Handling Group Data For Profit FROM Pace_Group_Block.
SELECT result_group.Occupancy_DT, result_group.Booking_DT,
       result_group.Accom_Type_ID, result_group.Mkt_Seg_Id, SUM(result_group.Room_Profit) AS Profit INTO #ROOM_PROFIT_GROUP FROM
    (
        SELECT
            gb.Group_Id,gb.Occupancy_DT,gb.Business_Day_End_DT as Booking_DT,
            gb.Accom_Type_ID ,gm.Mkt_seg_Id,
            (gb.Blocks-gb.Pickup)*gb.Rate - (gb.Blocks-gb.Pickup)*
                                            CASE WHEN serviceCost.Full_Turn_Servicing_Cost IS NOT NULL THEN serviceCost.Full_Turn_Servicing_Cost ELSE 0 END
                                                               as Room_Profit
        FROM
            Group_Master gm
                JOIN Pace_Group_Block gb ON gb.Group_ID = gm.Group_ID AND gm.Group_Status_Code = 'DEFINITE' AND gb.blocks - gb.pickup > 0
                JOIN Accom_Type at ON gb.Accom_Type_ID = at.Accom_Type_ID
            JOIN Mkt_Seg_Details msd ON gm.Mkt_Seg_ID =  msd.Mkt_Seg_ID
            LEFT JOIN Servicing_Cost_Cfg serviceCost ON serviceCost.Accom_Class_ID = at.Accom_Class_ID AND msd.Business_Type_ID = serviceCost.Business_Type_ID
            AND serviceCost.rate_code IS NULL
        WHERE
            gb.Occupancy_DT >= @start_date
          AND gb.Occupancy_DT <= @end_Date
    ) result_group
WHERE result_group.Occupancy_DT >= @start_date AND result_group.Occupancy_DT <= @end_date
GROUP BY result_group.Occupancy_DT, result_group.Booking_DT, result_group.Accom_Type_ID, result_group.Mkt_Seg_ID;

-- To Update Non Pace Data
SELECT result_group.Occupancy_DT,
       result_group.Accom_Type_ID, result_group.Mkt_Seg_Id, SUM(result_group.Room_Profit) AS Profit INTO #ROOM_PROFIT_GROUP_Block FROM
    (
        SELECT
            gb.Group_Id,gb.Occupancy_DT,
            gb.Accom_Type_ID ,gm.Mkt_seg_Id,
            (gb.Blocks-gb.Pickup)*gb.Rate - (gb.Blocks-gb.Pickup)*
                                            CASE WHEN serviceCost.Full_Turn_Servicing_Cost IS NOT NULL THEN serviceCost.Full_Turn_Servicing_Cost ELSE 0 END
                as Room_Profit
        FROM
            Group_Master gm
                JOIN Group_Block gb ON gb.Group_ID = gm.Group_ID AND gm.Group_Status_Code = 'DEFINITE' AND gb.blocks - gb.pickup > 0
                JOIN Accom_Type at ON gb.Accom_Type_ID = at.Accom_Type_ID
            JOIN Mkt_Seg_Details msd ON gm.Mkt_Seg_ID =  msd.Mkt_Seg_ID
            LEFT JOIN Servicing_Cost_Cfg serviceCost ON serviceCost.Accom_Class_ID = at.Accom_Class_ID AND msd.Business_Type_ID = serviceCost.Business_Type_ID
            AND serviceCost.rate_code IS NULL
        WHERE
            gb.Occupancy_DT >= @start_date
          AND gb.Occupancy_DT <= @end_Date
    ) result_group
WHERE result_group.Occupancy_DT >= @start_date AND result_group.Occupancy_DT <= @end_date
GROUP BY result_group.Occupancy_DT,  result_group.Accom_Type_ID, result_group.Mkt_Seg_ID;

-- Getting Data From RevenueView (Mostly No Data).
SELECT
    revenueView.Occupancy_DT,
    revenueView.Accom_Type_ID,
    revenueView.Mkt_Seg_ID,
    SUM (revenueView.Estimate_Value + revenueView.Actual_Value - ((revenueView.Estimate_Value + revenueView.Actual_Value) *
                                                                  CASE WHEN rsc.Percentage_Cost IS NOT NULL THEN  rsc.Percentage_Cost/100 ELSE 0 END  )) as Final_Revenue_Profit
INTO #TOTAL_Revenue_Profit
FROM
    Vw_Revenue_Stream_Detail revenueView
        LEFT JOIN revenue_stream_cost rsc ON revenueView.Revenue_Stream_id = rsc.Revenue_Stream_id
        AND revenueView.Occupancy_DT BETWEEN rsc.Start_Date AND rsc.END_DATE
WHERE
        revenueView.OCCUPANCY_DT >= @start_date
  AND revenueView.Occupancy_DT <= @end_date
GROUP BY revenueView.OCCUPANCY_DT, revenueView.Accom_Type_ID, revenueView.Mkt_Seg_ID

-- Combining the Trans And Group table.
SELECT Occupancy_DT,
       Accom_Type_ID,
       Mkt_Seg_ID,
       SUM(Profit) AS Profit
INTO #Total_Group_Trans
FROM (
         SELECT Occupancy_DT, Accom_Type_ID, Mkt_Seg_ID, Profit FROM #ROOM_PROFIT
         UNION ALL
         SELECT Occupancy_DT, Accom_Type_ID, Mkt_Seg_ID, Profit FROM #ROOM_PROFIT_GROUP_Block
     ) AS Combined_Total
GROUP BY Occupancy_DT, Accom_Type_ID, Mkt_Seg_ID

SELECT tgt.Occupancy_DT,
       tgt.Accom_Type_ID,
       tgt.Mkt_Seg_ID,
       tgt.Profit + CASE WHEN trp.Final_Revenue_Profit IS NOT NULL THEN trp.Final_Revenue_Profit ELSE 0 END as Profit
INTO #Non_Pace_Total_Profit
FROM #Total_Group_Trans tgt LEFT JOIN #TOTAL_Revenue_Profit trp ON
            tgt.Occupancy_DT = trp.Occupancy_DT AND tgt.Accom_Type_ID = trp.Accom_Type_ID AND tgt.Mkt_Seg_ID = trp.Mkt_Seg_ID;

SELECT
    OCCUPANCY_DT,
    Booking_DT,
    SUM(profit) OVER (PARTITION BY OCCUPANCY_DT ORDER BY Booking_DT) AS Profit
INTO #TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS
FROM (
         SELECT
             rp.OCCUPANCY_DT,
             rp.Booking_DT,
             SUM(rp.Profit) AS profit
         FROM #ROOM_PROFIT rp
                  JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
         WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
         GROUP BY rp.OCCUPANCY_DT, rp.Booking_DT
     ) AS #Base_TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS;

SELECT
    OCCUPANCY_DT,
    Booking_DT,
    Mkt_Seg_ID,
    SUM(profit) OVER (PARTITION BY Mkt_Seg_ID, OCCUPANCY_DT ORDER BY Booking_DT) AS Profit
INTO #PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS
FROM (
         SELECT
             rp.OCCUPANCY_DT,
             rp.Booking_DT,
             rp.Mkt_Seg_ID,
             SUM(rp.Profit) AS profit
         FROM #ROOM_PROFIT rp
                  JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
         WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
         GROUP BY rp.OCCUPANCY_DT, rp.Booking_DT, rp.Mkt_Seg_ID
     ) AS #Base_PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS;

SELECT
    OCCUPANCY_DT,
    Booking_DT,
    Accom_Type_ID,
    SUM(profit) OVER (PARTITION BY Accom_Type_ID, OCCUPANCY_DT ORDER BY Booking_DT) AS Profit
INTO #Accom_Profit_Excluding_Complimentary_MS
FROM (
         SELECT
             rp.OCCUPANCY_DT,
             rp.Booking_DT,
             rp.Accom_Type_ID,
             SUM(rp.Profit) AS profit
         FROM #ROOM_PROFIT rp
                  JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
         WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
         GROUP BY rp.OCCUPANCY_DT, rp.Booking_DT, rp.Accom_Type_ID
     ) AS #Base_Accom_Profit_Excluding_Complimentary_MS;


INSERT INTO #PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS
SELECT DiffPMA.Occupancy_DT, TRY_CONVERT(DATE,cal.calendar_date) AS Business_Day_end_DT, DiffPMA.Mkt_Seg_ID, profit
FROM
    (
        SELECT Mkt_Seg_ID, Occupancy_DT, Booking_DT,
               CASE WHEN Next_BDE_DT IS NULL THEN DATEADD(DAY,1,Occupancy_DT)
                    ELSE Next_BDE_DT END AS Next_BDE_DT
        FROM (SELECT Mkt_Seg_ID, Occupancy_DT, Booking_DT, LEAD(Booking_DT) OVER(PARTITION BY Mkt_Seg_ID, Occupancy_DT ORDER BY Booking_DT) AS Next_BDE_DT
              FROM #PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS) AS INP ) DiffPMA
        JOIN calendar_dim cal
             ON cal.calendar_date BETWEEN DATEADD(DAY,1,Booking_DT) AND DATEADD(DAY,-1,Next_BDE_DT)
        JOIN #PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS PMA
             ON PMA.Mkt_Seg_ID = DiffPMA.Mkt_Seg_ID
                 AND PMA.Occupancy_DT = DiffPMA.Occupancy_DT
                 AND PMA.Booking_DT = DiffPMA.Booking_DT
ORDER BY PMA.Mkt_Seg_ID, PMA.Occupancy_DT, cal.calendar_date

    INSERT INTO #Accom_Profit_Excluding_Complimentary_MS
SELECT DiffPMA.Occupancy_DT, TRY_CONVERT(DATE,cal.calendar_date) AS Business_Day_end_DT, DiffPMA.Accom_Type_ID, profit
FROM
    (
        SELECT Accom_Type_ID, Occupancy_DT, Booking_DT,
               CASE WHEN Next_BDE_DT IS NULL THEN DATEADD(DAY,1,Occupancy_DT)
                    ELSE Next_BDE_DT END AS Next_BDE_DT
        FROM (SELECT Accom_Type_ID, Occupancy_DT, Booking_DT, LEAD(Booking_DT) OVER(PARTITION BY Accom_Type_ID, Occupancy_DT ORDER BY Booking_DT) AS Next_BDE_DT
              FROM #Accom_Profit_Excluding_Complimentary_MS) AS INP ) DiffPMA
        JOIN calendar_dim cal
             ON cal.calendar_date BETWEEN DATEADD(DAY,1,Booking_DT) AND DATEADD(DAY,-1,Next_BDE_DT)
        JOIN #Accom_Profit_Excluding_Complimentary_MS PMA
             ON PMA.Accom_Type_ID = DiffPMA.Accom_Type_ID
                 AND PMA.Occupancy_DT = DiffPMA.Occupancy_DT
                 AND PMA.Booking_DT = DiffPMA.Booking_DT
ORDER BY PMA.Accom_Type_ID, PMA.Occupancy_DT, cal.calendar_date

    INSERT INTO #TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS
SELECT DiffPMA.Occupancy_DT, TRY_CONVERT(DATE,cal.calendar_date) AS Business_Day_end_DT, profit
FROM
    (
        SELECT Occupancy_DT, Booking_DT,
               CASE WHEN Next_BDE_DT IS NULL THEN DATEADD(DAY,1,Occupancy_DT)
                    ELSE Next_BDE_DT END AS Next_BDE_DT
        FROM (SELECT Occupancy_DT, Booking_DT, LEAD(Booking_DT) OVER(PARTITION BY Occupancy_DT ORDER BY Booking_DT) AS Next_BDE_DT
              FROM #TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS) AS INP ) DiffPMA
        JOIN calendar_dim cal
             ON cal.calendar_date BETWEEN DATEADD(DAY,1,Booking_DT) AND DATEADD(DAY,-1,Next_BDE_DT)
        JOIN #TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS PMA ON
                PMA.Occupancy_DT = DiffPMA.Occupancy_DT
            AND PMA.Booking_DT = DiffPMA.Booking_DT
ORDER BY PMA.Occupancy_DT, cal.calendar_date

SELECT rp.Occupancy_DT, rp.Booking_DT, SUM(rp.Profit) AS Profit	INTO #Total_Group_Pace_Profit FROM #ROOM_PROFIT_GROUP rp JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
GROUP BY rp.Occupancy_DT, rp.Booking_DT;

SELECT rp.Occupancy_DT, rp.Booking_DT, rp.Accom_Type_ID, SUM(rp.Profit) AS Profit INTO #Accom_Group_Pace_Profit FROM #ROOM_PROFIT_GROUP rp JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
GROUP BY rp.Occupancy_DT, rp.Booking_DT, rp.Accom_Type_ID;

SELECT rp.Occupancy_DT, rp.Booking_DT, rp.Mkt_Seg_ID, SUM(rp.Profit) AS Profit INTO #Mkt_Group_Pace_Profit FROM #ROOM_PROFIT_GROUP rp JOIN Mkt_Seg ms ON rp.Mkt_Seg_ID = ms.Mkt_Seg_ID
WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1)
GROUP BY rp.Occupancy_DT, rp.Booking_DT, rp.Mkt_Seg_ID;

SELECT
    Booking_DT,
    Occupancy_DT,
    SUM(Profit) AS Profit INTO #Combined_Group_Trans_Total_Pace
FROM (
         SELECT Booking_DT, Occupancy_DT, Profit FROM #Total_Group_Pace_Profit
         UNION ALL
         SELECT Booking_DT, Occupancy_DT, Profit FROM #TOTAL_PROFIT_EXCLUDING_COMPLIMENTARY_MS
     ) AS #Combined
GROUP BY Booking_DT, Occupancy_DT;

SELECT
    Booking_DT,
    Occupancy_DT,
    Accom_Type_ID,
    SUM(Profit) AS Profit INTO #Combined_Group_Trans_Accom_Pace
FROM (
         SELECT Booking_DT, Occupancy_DT, Accom_Type_ID, Profit FROM #Accom_Group_Pace_Profit
         UNION ALL
         SELECT Booking_DT, Occupancy_DT, Accom_Type_ID, Profit FROM #Accom_Profit_Excluding_Complimentary_MS
     ) AS #Combined_Accom
GROUP BY Booking_DT, Occupancy_DT, Accom_Type_ID

SELECT
    Booking_DT,
    Occupancy_DT,
    Mkt_Seg_ID,
    SUM(Profit) AS Profit INTO #Combined_Group_Trans_Mkt_Seg_Pace
FROM (
         SELECT Booking_DT, Occupancy_DT, Mkt_Seg_ID, Profit FROM #Mkt_Group_Pace_Profit
         UNION ALL
         SELECT Booking_DT, Occupancy_DT, Mkt_Seg_ID, Profit FROM #PACE_MKT_PROFIT_EXCLUDING_COMPLIMENTARY_MS
     ) AS #Combined_Mkt_Seg
GROUP BY Booking_DT, Occupancy_DT, Mkt_Seg_ID

-- Aggregating Revenue Stream Profit.
SELECT trp.Occupancy_DT, SUM(trp.Final_Revenue_Profit) AS Profit INTO #Total_Level_Revenue_Profit FROM #TOTAL_Revenue_Profit trp JOIN Mkt_Seg ms
                                                                                                                                      ON trp.Mkt_Seg_ID = ms.Mkt_Seg_ID WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1) GROUP BY trp.Occupancy_DT;
SELECT trp.Occupancy_DT, trp.Mkt_Seg_ID, SUM(trp.Final_Revenue_Profit) AS Profit INTO #Mkt_Level_Revenue_Profit FROM #TOTAL_Revenue_Profit trp JOIN Mkt_Seg ms
                                                                                                                                                    ON trp.Mkt_Seg_ID = ms.Mkt_Seg_ID WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1) GROUP BY trp.Occupancy_DT, trp.Mkt_Seg_ID;
SELECT trp.Occupancy_DT, trp.Accom_Type_ID, SUM(trp.Final_Revenue_Profit) AS Profit INTO #Accom_Level_Revenue_Profit FROM #TOTAL_Revenue_Profit trp JOIN Mkt_Seg ms
                                                                                                                                                         ON trp.Mkt_Seg_ID = ms.Mkt_Seg_ID WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1) GROUP BY trp.Occupancy_DT, trp.Accom_Type_ID;

SELECT grp_trans_pace.Occupancy_DT, grp_trans_pace.Booking_DT, grp_trans_pace.Profit + ISNULL(tlrp.Profit, 0) AS Profit INTO #Total_Level_Profit_Without_Comp_MS FROM #Combined_Group_Trans_Total_Pace grp_trans_pace LEFT JOIN #Total_Level_Revenue_Profit tlrp ON
        grp_trans_pace.Occupancy_DT = tlrp.Occupancy_DT;

SELECT grp_trans_pace.Occupancy_DT, grp_trans_pace.Booking_DT, grp_trans_pace.Accom_Type_ID, grp_trans_pace.Profit + ISNULL(alrp.Profit, 0) AS Profit INTO #Accom_Level_Profit_Without_Comp_MS FROM #Combined_Group_Trans_Accom_Pace grp_trans_pace LEFT JOIN #Accom_Level_Revenue_Profit alrp ON
            grp_trans_pace.Occupancy_DT = alrp.Occupancy_DT AND grp_trans_pace.Accom_Type_ID = alrp.Accom_Type_ID;

SELECT grp_trans_pace.Occupancy_DT, grp_trans_pace.Booking_DT, grp_trans_pace.Mkt_Seg_ID, grp_trans_pace.Profit + ISNULL(mlrp.Profit, 0) AS Profit INTO #Mkt_Level_Profit_Without_Comp_MS FROM #Combined_Group_Trans_Mkt_Seg_Pace grp_trans_pace LEFT JOIN #Mkt_Level_Revenue_Profit mlrp ON
            grp_trans_pace.Occupancy_DT = mlrp.Occupancy_DT AND grp_trans_pace.Mkt_Seg_ID = mlrp.Mkt_Seg_ID;

-- Non Pace Data
SELECT non_pace_profit.Occupancy_DT, SUM(non_pace_profit.Profit) AS Profit INTO #Combined_Group_Trans_Total_Non_Pace FROM #Non_Pace_Total_Profit non_pace_profit JOIN Mkt_Seg ms ON
        non_pace_profit.Mkt_Seg_ID = ms.Mkt_Seg_ID WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1) GROUP BY non_pace_profit.Occupancy_DT;

SELECT non_pace_profit.Occupancy_DT, non_pace_profit.Accom_Type_ID, SUM(non_pace_profit.Profit) AS Profit INTO #Combined_Group_Trans_Accom_Non_Pace FROM #Non_Pace_Total_Profit non_pace_profit JOIN Mkt_Seg ms ON
        non_pace_profit.Mkt_Seg_ID = ms.Mkt_Seg_ID WHERE ms.complimentary = 0 OR (@excludeComplimentaryRoomRevenueFromProfit = 0 AND ms.complimentary = 1) GROUP BY non_pace_profit.Occupancy_DT, non_pace_profit.Accom_Type_ID;

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE ta   SET ta.TOTAL_PROFIT = ISNULL( profitTable.PROFIT,0)
    FROM Total_Activity ta LEFT  JOIN #Combined_Group_Trans_Total_Non_Pace profitTable ON ta.Occupancy_DT= profitTable.Occupancy_DT
WHERE ta.Property_ID =   @property_id AND ta.Occupancy_dt between @start_date and @end_date

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE maa   SET maa.TOTAL_PROFIT = ISNULL( profitTable.Profit,0)
    FROM Mkt_Accom_Activity maa LEFT JOIN #Non_Pace_Total_Profit profitTable
ON maa.Occupancy_DT= profitTable.Occupancy_DT AND maa.Accom_Type_ID = profitTable.Accom_Type_ID AND maa.Mkt_Seg_ID = profitTable.Mkt_Seg_ID
WHERE maa.Property_ID =   @property_id  AND maa.Occupancy_dt between @start_date and @end_date

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE aa   SET aa.TOTAL_PROFIT = ISNULL( profitTable.PROFIT,0)
    FROM Accom_Activity aa  LEFT JOIN  #Combined_Group_Trans_Accom_Non_Pace profitTable
ON aa.Occupancy_DT= profitTable.Occupancy_DT AND aa.Accom_Type_ID = profitTable.Accom_Type_ID
Where aa.Property_ID =   @property_id  AND aa.Occupancy_dt between @start_date and @end_date

-- no need to reset pace profit as it depends upon business day end date
UPDATE ta   SET ta.TOTAL_PROFIT = profitTable.Profit
    FROM PACE_Total_Activity ta  JOIN  #Total_Level_Profit_Without_Comp_MS profitTable ON ta.Occupancy_DT= profitTable.Occupancy_DT
WHERE  ta.Business_Day_End_DT= profitTable.Booking_DT AND ta.Property_ID =   @property_id

UPDATE maa   SET maa.TOTAL_PROFIT = profitTable.Profit
    FROM PACE_Mkt_Activity maa  JOIN #Mkt_Level_Profit_Without_Comp_MS profitTable
ON maa.Occupancy_DT= profitTable.Occupancy_DT AND  maa.Mkt_Seg_ID = profitTable.Mkt_Seg_ID
WHERE maa.Business_Day_End_DT= profitTable.Booking_DT AND maa.Property_ID =   @property_id

UPDATE aa   SET aa.TOTAL_PROFIT = profitTable.Profit
    FROM PACE_Accom_Activity aa  JOIN #Accom_Level_Profit_Without_Comp_MS profitTable
ON aa.Occupancy_DT= profitTable.Occupancy_DT AND aa.Accom_Type_ID = profitTable.Accom_Type_ID
WHERE aa.Business_Day_End_DT= profitTable.Booking_DT AND aa.Property_ID =   @property_id

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE lota   SET lota.TOTAL_PROFIT = ISNULL( profitTable.PROFIT,0)
    FROM Last_Optimization_Total_Activity lota LEFT JOIN #Combined_Group_Trans_Total_Non_Pace profitTable ON lota.Occupancy_DT= profitTable.Occupancy_DT
WHERE lota.Property_ID =   @property_id AND  lota.Occupancy_dt between @start_date and @end_date

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE lomaa   SET lomaa.TOTAL_PROFIT = ISNULL( profitTable.Profit,0)
    FROM Last_Optimization_Mkt_Accom_Activity lomaa  LEFT JOIN   #Non_Pace_Total_Profit profitTable
ON lomaa.Occupancy_DT= profitTable.Occupancy_DT AND lomaa.Accom_Type_ID = profitTable.Accom_Type_ID AND lomaa.Mkt_Seg_ID = profitTable.Mkt_Seg_ID
WHERE lomaa.Property_ID =   @property_id AND lomaa.Occupancy_dt between @start_date and @end_date

--reset profit values to 0 to handle scenario where activity table have data and reservation tables doesn't
UPDATE loaa   SET loaa.TOTAL_PROFIT = ISNULL( profitTable.PROFIT,0)
    FROM Last_Optimization_Accom_Activity loaa LEFT JOIN #Combined_Group_Trans_Accom_Non_Pace profitTable
ON loaa.Occupancy_DT= profitTable.Occupancy_DT AND loaa.Accom_Type_ID = profitTable.Accom_Type_ID
WHERE  loaa.Property_ID =   @property_id AND  loaa.Occupancy_dt between @start_date and @end_date

END;