if
exists (select * from sys.objects where object_id = object_id(N'[dbo].[usp_tent_grp_fcst_evaluate_alert]'))
drop procedure
[dbo].[usp_tent_grp_fcst_evaluate_alert]
GO
/*************************************************************************************

Procedure Name: usp_tent_grp_fcst_evaluate_alert

Evaluate alert for tentative group forecasting by comparing group master and fs booking data.

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
12/26/2023		Shreyas				Mahajan				Initial Version
***************************************************************************************/

create procedure [dbo].[usp_tent_grp_fcst_evaluate_alert]
(
		@pmsCrsWindow int,
		@aseamWindow int,
		@fsStatusIds nvarchar(max),
        @systemDate date,
        @isPCRS int,
        @isVirtualProperty int,
        @virtualPropertyCodesForPCRS nvarchar(max)
)
as
begin

/* Pre-process Group Master records to remove the prefixes in case of virtual and/or PCRS properties. */
select groupMaster.Start_DT,
       groupMaster.End_DT,
       groupMaster.Group_Name,
       dbo.ufn_tent_grp_fcst_alert_preprocess_grp_code(groupMaster.Master_Group_Code, @isPCRS, @isVirtualProperty, @virtualPropertyCodesForPCRS) as Master_Group_Code,
       dbo.ufn_tent_grp_fcst_alert_preprocess_grp_code(groupMaster.Group_Code, @isPCRS, @isVirtualProperty, @virtualPropertyCodesForPCRS) as Group_Code
into #Group_Master_Filtered
from Group_Master groupMaster
where groupMaster.Group_Status_Code in ('DEFINITE', 'TENT')

/* Pre-process FS Booking records to filter out the zero rooms bookings. */
select fsBooking.Arrival_DT,
       fsBooking.Block_Name,
       fsBooking.Sales_Catering_Identifier,
       fsBooking.Block_Code,
       fsBooking.Departure_DT
into #FS_Booking_Non_Zero_Rooms
from FS_Booking fsBooking
         join FS_Booking_Guest_Room fsBookingGuestRoom
              on fsBooking.FS_Booking_ID = fsBookingGuestRoom.FS_Booking_ID
where (fsBookingGuestRoom.Blocked_Double_Rooms <> 0 or fsBookingGuestRoom.Blocked_Single_Rooms <> 0 or
       fsBookingGuestRoom.Blocked_Quad_Rooms <> 0 or fsBookingGuestRoom.Blocked_Triple_Rooms <> 0 or
       fsBookingGuestRoom.Contracted_Rooms_Total <> 0 or fsBookingGuestRoom.Pickup_Rooms_Total <> 0)
  and fsBooking.FS_Cfg_Status_ID in (select value from STRING_SPLIT(@fsStatusIds, ','))
group by
    fsBooking.Arrival_DT,
    fsBooking.Block_Name,
    fsBooking.Sales_Catering_Identifier,
    fsBooking.Block_Code,
    fsBooking.Departure_DT;

create table #Mismatched_Records
(
    GroupArrivalDate date,
    GroupName nvarchar(max),
    Within_PMS_CRS_Window nvarchar(100),
    PMS_CRS_ID nvarchar(max),
    SnC_ID nvarchar(max),
    PMS_CRS_ID_IN_SnC nvarchar(max),
    Importance nvarchar(10)
)

/* Records present in group master but not in fs booking */
insert into
#Mismatched_Records
select groupMaster.Start_DT as 'GroupArrivalDate',groupMaster.Group_Name as GroupName,
       'Yes' as 'Within_PMS_CRS_Window', groupMaster.Master_Group_Code as PMS_CRS_ID,
       '' as SnC_ID,
       '' as 'PMS_CRS_ID_IN_SnC','1' as 'Importance'
from #Group_Master_Filtered groupMaster
where groupMaster.Start_DT <= DATEADD(day, @pmsCrsWindow, GETDATE())
  and groupMaster.end_dt >= @systemDate
  and not exists(select 1
                 from #FS_Booking_Non_Zero_Rooms fsBooking
                 where (groupMaster.Group_Code = fsBooking.Block_Code OR
                        (groupMaster.Group_Code like '%[_][0-9]%' and
                         groupMaster.Master_Group_Code = fsBooking.Block_Code and
                         ABS(DATEDIFF(day, groupMaster.Start_DT, fsBooking.Arrival_DT)) <= 2))
                   and fsBooking.Arrival_DT <= DATEADD(day, @pmsCrsWindow, GETDATE()))

/* Records present in fs booking but not in group master */
insert
into #Mismatched_Records
select fsBooking.Arrival_DT as 'GroupArrivalDate',fsBooking.Block_Name as GroupName,
       'Yes' as 'Within_PMS_CRS_Window','' as PMS_CRS_ID,
       fsBooking.Sales_Catering_Identifier as SnC_ID,
       fsBooking.Block_Code as 'PMS_CRS_ID_IN_SnC','1' as 'Importance'
from #FS_Booking_Non_Zero_Rooms fsBooking
where fsBooking.Departure_DT >= @systemDate
  and Not fsBooking.Block_Code = ''
  and not exists(select 1
                 from #Group_Master_Filtered groupMaster
                 where (groupMaster.Group_Code = fsBooking.Block_Code OR
                        (groupMaster.Group_Code like '%[_][0-9]%' and
                         groupMaster.Master_Group_Code = fsBooking.Block_Code and
                         ABS(DATEDIFF(day, groupMaster.Start_DT, fsBooking.Arrival_DT)) <= 2)))

/* Records present in fs booking having empty block code but non-empty SnC identifier. */
insert
into #Mismatched_Records
select fsBooking.Arrival_DT as 'GroupArrivalDate',fsBooking.Block_Name as GroupName,
       'Yes' as 'Within_PMS_CRS_Window','' as PMS_CRS_ID,
       fsBooking.Sales_Catering_Identifier as SnC_ID,
       '' as 'PMS_CRS_ID_IN_SnC','1' as 'Importance'
from #FS_Booking_Non_Zero_Rooms fsBooking
where fsBooking.Arrival_DT <= DATEADD(day, @pmsCrsWindow, GETDATE())
  and (fsBooking.Block_Code is NULL or fsBooking.Block_Code = '')
  and fsBooking.Departure_DT >= @systemDate
  and fsBooking.Sales_Catering_Identifier is not NULL

/* Records present in fs booking but not in group master and having arrival date beyond pmsCrsWindow. */
insert
into #Mismatched_Records
select fsBooking.Arrival_DT as 'GroupArrivalDate',fsBooking.Block_Name as GroupName,
       'No' as 'Within_PMS_CRS_Window','' as PMS_CRS_ID,
       fsBooking.Sales_Catering_Identifier as SnC_ID,
       fsBooking.Block_Code as 'PMS_CRS_ID_IN_SnC','2' as 'Importance'
from #FS_Booking_Non_Zero_Rooms fsBooking
where fsBooking.Arrival_DT <= DATEADD(day, @aseamWindow, GETDATE())
  and fsBooking.Arrival_DT > DATEADD(day, @pmsCrsWindow, GETDATE())
  and fsBooking.Departure_DT >= @systemDate
  and Not fsBooking.Block_Code = ''
  and not exists(select 1
                 from #Group_Master_Filtered groupMaster
                 where (groupMaster.Group_Code = fsBooking.Block_Code OR
                        (groupMaster.Group_Code like '%[_][0-9]%' and
                         groupMaster.Master_Group_Code = fsBooking.Block_Code and
                         ABS(DATEDIFF(day, groupMaster.Start_DT, fsBooking.Arrival_DT)) <= 2)))

/* Records present in fs booking having empty block code but non-empty SnC identifier and having arrival date beyond pmsCrsWindow. */
insert
into #Mismatched_Records
select fsBooking.Arrival_DT as 'GroupArrivalDate',fsBooking.Block_Name as GroupName,
       'No' as 'Within_PMS_CRS_Window','' as PMS_CRS_ID,
       fsBooking.Sales_Catering_Identifier as SnC_ID,
       '' as 'PMS_CRS_ID_IN_SnC','3' as 'Importance'
from #FS_Booking_Non_Zero_Rooms fsBooking
where fsBooking.Arrival_DT <= DATEADD(day, @aseamWindow, GETDATE())
  and fsBooking.Arrival_DT > DATEADD(day, @pmsCrsWindow, GETDATE())
  and (fsBooking.Block_Code is NULL or fsBooking.Block_Code = '')
  and fsBooking.Departure_DT >= @systemDate
  and fsBooking.Sales_Catering_Identifier is not NULL

select GroupArrivalDate,
       GroupName,
       Within_PMS_CRS_Window,
       PMS_CRS_ID,
       SnC_ID,
       PMS_CRS_ID_IN_SnC,
       case
           when Importance = '1' then 'High'
           when Importance = '2' then 'Medium'
           when Importance = '3' then 'Low'
           else 'NA' end as Imp
from #Mismatched_Records order by GroupArrivalDate, Importance, GroupName

end
;