IF EXISTS
(
    SELECT * FROM sys.objects WHERE object_id = object_id(N'[usp_hotel_differential_control_report_by_individual_rc]')
)
    DROP PROCEDURE [dbo].[usp_hotel_differential_control_report_by_individual_rc]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_hotel_differential_control_report_by_individual_rc]
(
    @property_id int,
    @roomclass_id varchar(500),
    @comp_id_1 int,
    @comp_id_2 int,
    @comp_id_3 int,
    @comp_id_4 int,
    @comp_id_5 int,
    @comp_id_6 int,
    @comp_id_7 int,
    @comp_id_8 int,
    @comp_id_9 int,
    @comp_id_10 int,
    @comp_id_11 int,
    @comp_id_12 int,
    @comp_id_13 int,
    @comp_id_14 int,
    @comp_id_15 int,
    @record_type_id int,
    @process_status_id int,
    @business_dt date,
    @start_date date,
    @end_date date,
    @isRollingDate int,
    @rolling_business_dt nvarchar(50),
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50),
    @isLV0Closed nvarchar(10),
    @use_physical_capacity int,
    @includeZeroCapacityRT int,
    @isCompactWebratePaceEnabled bit
)
as
begin
    declare @caughtupdate date
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    ) --> extract caughtup date for a property

    if (@isRollingDate = 1)
    begin
        set @business_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_dt, @caughtupdate)
        )
        set @start_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate)
        )
        set @end_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate)
        )
    end

    declare @rc1 int
    declare @rc2 int
    declare @rc3 int
    declare @rc4 int
    declare @rc5 int
    declare @rc6 int
    declare @rc7 int
    declare @rc8 int
    declare @rc9 int
    declare @rc10 int

    declare @temp_rc table
    (
        number int,
        accom_class_id int
    )
    insert into @temp_rc
    select number = ROW_NUMBER() OVER (ORDER BY Accom_Class_id),
           Accom_Class_id
    from Accom_Class
    where Property_ID = @property_id
          and Accom_Class_id in (
                                    SELECT Value FROM varcharToInt(@roomclass_id, ',')
                                )
          and Status_ID = 1
          and System_Default = 0

    set @rc1 =
    (
        Select Accom_Class_id from @temp_rc where number = 1
    )
    set @rc2 =
    (
        Select Accom_Class_id from @temp_rc where number = 2
    )
    set @rc3 =
    (
        Select Accom_Class_id from @temp_rc where number = 3
    )
    set @rc4 =
    (
        Select Accom_Class_id from @temp_rc where number = 4
    )
    set @rc5 =
    (
        Select Accom_Class_id from @temp_rc where number = 5
    )
    set @rc6 =
    (
        Select Accom_Class_id from @temp_rc where number = 6
    )
    set @rc7 =
    (
        Select Accom_Class_id from @temp_rc where number = 7
    )
    set @rc8 =
    (
        Select Accom_Class_id from @temp_rc where number = 8
    )
    set @rc9 =
    (
        Select Accom_Class_id from @temp_rc where number = 9
    )
    set @rc10 =
    (
        Select Accom_Class_id from @temp_rc where number = 10
    )
    --- extract the report metrics
    DECLARE @temp_adr_revpar TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        rooms_sold NUMERIC(18, 0),
        total_accom_capacity NUMERIC(18, 0),
        outoforder NUMERIC(18, 0),
        room_revenue NUMERIC(19, 2),
        adr NUMERIC(19, 2),
        revpar NUMERIC(19, 2)
    );
    DECLARE @temp_propor_propar TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        rooms_sold NUMERIC(18, 0),
        total_accom_capacity NUMERIC(18, 0),
        outoforder NUMERIC(18, 0),
        total_profit NUMERIC(19, 2),
        propor NUMERIC(19, 2),
        propar NUMERIC(19, 2)
    );
    DECLARE @temp_occupancy_forecast TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        ac_occupancy_forecast NUMERIC(8, 2),
        ac_revenue NUMERIC(19, 2),
        ac_adr NUMERIC(19, 2)
    );
    DECLARE @temp_occupancy_forecast_profit TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        ac_occupancy_forecast NUMERIC(8, 2),
        ac_profit NUMERIC(19, 2),
        ac_propor NUMERIC(19, 2)
    );
    DECLARE @temp_adr_revpar_asof TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        rooms_sold NUMERIC(18, 0),
        total_accom_capacity NUMERIC(18, 0),
        outoforder NUMERIC(18, 0),
        room_revenue NUMERIC(19, 2),
        adr NUMERIC(19, 2),
        revpar NUMERIC(19, 2)
    );
    DECLARE @temp_propor_propar_asof TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        rooms_sold NUMERIC(18, 0),
        total_accom_capacity NUMERIC(18, 0),
        outoforder NUMERIC(18, 0),
        profit NUMERIC(19, 2),
        propor NUMERIC(19, 2),
        propar NUMERIC(19, 2)
    );
    DECLARE @temp_occupancy_forecast_asof TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        Accom_Class_ID INT,
        occupancy_nbr_businessstartdate NUMERIC(8, 2),
        revenue_businessstartdate NUMERIC(19, 2),
        adr NUMERIC(19, 2)
    );
    DECLARE @temp_occupancy_forecast_profit_asof TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        Accom_Class_ID INT,
        occupancy_nbr_businessstartdate NUMERIC(8, 2),
        profit_businessstartdate NUMERIC(19, 2),
        propor NUMERIC(19, 2)
    );
    DECLARE @temp_los_barrate TABLE
    (
        property_id INT,
        arrival_dt DATE,
        accom_class_id INT,
        bar_los1 FLOAT,
        bar_los2 FLOAT,
        bar_los3 FLOAT,
        bar_los4 FLOAT,
        bar_los5 FLOAT,
        bar_los6 FLOAT,
        bar_los7 FLOAT,
        bar_los8 FLOAT,
        bar_by_day FLOAT
    );
    DECLARE @temp_rate_code TABLE
    (
        property_id INT,
        arrival_dt DATE,
        accom_class_id INT,
        ratecode_los1 NVARCHAR(50),
        ratecode_los2 NVARCHAR(50),
        ratecode_los3 NVARCHAR(50),
        ratecode_los4 NVARCHAR(50),
        ratecode_los5 NVARCHAR(50),
        ratecode_los6 NVARCHAR(50),
        ratecode_los7 NVARCHAR(50),
        ratecode_los8 NVARCHAR(50),
        ratecode_los_all NVARCHAR(50)
    );
    DECLARE @temp_los_barrate_asof TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_class_id INT,
        bar_los1 FLOAT,
        bar_los2 FLOAT,
        bar_los3 FLOAT,
        bar_los4 FLOAT,
        bar_los5 FLOAT,
        bar_los6 FLOAT,
        bar_los7 FLOAT,
        bar_los8 FLOAT,
        bar_by_day FLOAT
    );
    DECLARE @temp_decision_lrv TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_class_id INT,
        accom_class_code NVARCHAR(50),
        lrv NUMERIC(19, 2)
    );
    DECLARE @temp_lrv_asof TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_class_id INT,
        lrv NUMERIC(19, 2)
    );
    DECLARE @temp_ovrbk_decision TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        overbooking NUMERIC(18, 0)
    );
    DECLARE @temp_ovrbk_decision_asof TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        overbooking NUMERIC(18, 0)
    );
    DECLARE @web_rate_comp_1 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_2 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_3 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_4 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_5 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_6 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_7 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_8 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_9 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_10 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_11 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_12 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_13 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_14 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @web_rate_comp_15 TABLE
    (
        property_id INT,
        accom_class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 2),
        webrate_competitors_name NVARCHAR(150)
    );
    DECLARE @webrate_asof_businessdate_1 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_2 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_3 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_4 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_5 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_6 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_7 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_8 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_9 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_10 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_11 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_12 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_13 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_14 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @webrate_asof_businessdate_15 TABLE
    (
        property_id INT,
        occupancy_dt DATE,
        accom_Class_id int,
        webrate_competitors_name NVARCHAR(150),
        webrate_ratevalue NUMERIC(19, 2),
        generationDate DATE
    );
    DECLARE @temp_groupBlock_groupPickupByRoomClass TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        roomClass_id INT,
        block INT,
        block_pickup INT,
        block_available INT
    );
    DECLARE @temp_hbr_report TABLE
    (
        property_id INT,
        arrival_DT DATE,
        accom_class_id INT,
        LOS1 VARCHAR(2000),
        LOS2 VARCHAR(2000),
        LOS3 VARCHAR(2000),
        LOS4 VARCHAR(2000),
        LOS5 VARCHAR(2000),
        LOS6 VARCHAR(2000),
        LOS7 VARCHAR(2000),
        LOS8 VARCHAR(2000)
    );
    DECLARE @temp_hbr_report_close TABLE
    (
        property_id INT,
        arrival_DT DATE,
        accom_class_id INT,
        LOS1 VARCHAR(2000),
        LOS2 VARCHAR(2000),
        LOS3 VARCHAR(2000),
        LOS4 VARCHAR(2000),
        LOS5 VARCHAR(2000),
        LOS6 VARCHAR(2000),
        LOS7 VARCHAR(2000),
        LOS8 VARCHAR(2000)
    );
    INSERT INTO @temp_adr_revpar
    EXEC [dbo].[usp_get_adr_revpar_by_individual_rc] @property_id,
                                                     @roomclass_id,
                                                     @start_date,
                                                     @end_date,
                                                     @use_physical_capacity,
                                                     @includeZeroCapacityRT
    INSERT INTO @temp_propor_propar
    EXEC [dbo].[usp_get_propor_propar_by_individual_rc] @property_id,
                                                        @roomclass_id,
                                                        @start_date,
                                                        @end_date,
                                                        @use_physical_capacity,
                                                        @includeZeroCapacityRT
    INSERT INTO @temp_occupancy_forecast
    EXEC [dbo].[usp_get_occupancy_forecast_by_individual_rc] @property_id,
                                                             @roomclass_id,
                                                             @record_type_id,
                                                             @process_status_id,
                                                             @start_date,
                                                             @end_date,
                                                             @includeZeroCapacityRT
    INSERT INTO @temp_occupancy_forecast_profit
    EXEC [dbo].[usp_get_occupancy_forecast_profit_by_individual_rc] @property_id,
                                                                    @roomclass_id,
                                                                    @record_type_id,
                                                                    @process_status_id,
                                                                    @start_date,
                                                                    @end_date,
                                                                    @includeZeroCapacityRT,
                                                                    @use_physical_capacity
    INSERT INTO @temp_adr_revpar_asof
    EXEC [dbo].[usp_get_adr_revpar_asof_lastoptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                           @roomclass_id,
                                                                                           @business_dt,
                                                                                           @start_date,
                                                                                           @end_date,
                                                                                           @use_physical_capacity,
                                                                                           @rolling_business_dt,
                                                                                           @includeZeroCapacityRT
    INSERT INTO @temp_propor_propar_asof
    EXEC [dbo].[usp_get_propor_propar_asof_lastoptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                              @roomclass_id,
                                                                                              @business_dt,
                                                                                              @start_date,
                                                                                              @end_date,
                                                                                              @use_physical_capacity,
                                                                                              @rolling_business_dt,
                                                                                              @includeZeroCapacityRT
    INSERT INTO @temp_occupancy_forecast_asof
    EXEC [dbo].[usp_get_occupancy_forecast_asof_lastoptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                                   @roomclass_id,
                                                                                                   @business_dt,
                                                                                                   @start_date,
                                                                                                   @end_date,
                                                                                                   @rolling_business_dt,
                                                                                                   @includeZeroCapacityRT
    INSERT INTO @temp_occupancy_forecast_profit_asof
    EXEC [dbo].[usp_get_occupancy_forecast_profit_asof_lastoptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                                          @roomclass_id,
                                                                                                          @business_dt,
                                                                                                          @start_date,
                                                                                                          @end_date,
                                                                                                          @rolling_business_dt,
                                                                                                          @includeZeroCapacityRT
    INSERT INTO @temp_los_barrate
    EXEC [dbo].[usp_get_los_barrate_by_individual_rc] @property_id,
                                                      @roomclass_id,
                                                      @start_date,
                                                      @end_date,
                                                      @includeZeroCapacityRT
    INSERT INTO @temp_rate_code
    EXEC [dbo].[usp_get_bar_ratecode_by_arrival_by_individual_rc] @property_id,
                                                                  @roomclass_id,
                                                                  @start_date,
                                                                  @end_date
    INSERT INTO @temp_los_barrate_asof
    EXEC [dbo].[usp_get_los_barrate_asof_lastOptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                            @roomclass_id,
                                                                                            @business_dt,
                                                                                            @start_date,
                                                                                            @end_date,
                                                                                            @rolling_business_dt
    INSERT INTO @temp_decision_lrv
    EXEC [dbo].[usp_get_decision_lrv_by_individual_rc] @property_id,
                                                       @roomclass_id,
                                                       @start_date,
                                                       @end_date
    INSERT INTO @temp_lrv_asof
    EXEC [dbo].[usp_get_lrv_asof_lastOptimization_or_businessdate_by_individual_rc] @property_id,
                                                                                    @roomclass_id,
                                                                                    @business_dt,
                                                                                    @start_date,
                                                                                    @end_date,
                                                                                    @rolling_business_dt
    INSERT INTO @temp_ovrbk_decision
    EXEC [dbo].[usp_get_ovrbk_decision_by_individual_rc] @property_id,
                                                         @roomclass_id,
                                                         @start_date,
                                                         @end_date,
                                                         @includeZeroCapacityRT
    INSERT INTO @temp_ovrbk_decision_asof
    EXEC [dbo].[usp_get_ovrbk_decision_asof_businessdate_by_individual_rc] @property_id,
                                                                           @roomclass_id,
                                                                           @business_dt,
                                                                           @start_date,
                                                                           @end_date,
                                                                           @includeZeroCapacityRT
    INSERT INTO @web_rate_comp_1
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_1,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_2
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_2,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_3
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_3,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_4
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_4,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_5
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_5,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_6
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_6,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_7
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_7,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_8
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_8,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_9
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_9,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_10
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_10,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_11
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_11,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_12
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_12,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_13
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_13,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_14
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_14,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @web_rate_comp_15
    EXEC [dbo].[usp_get_web_competitors_rate_by_rc] @property_id,
                                                    @comp_id_15,
                                                    @start_date,
                                                    @end_date,
                                                    @roomclass_id
    INSERT INTO @webrate_asof_businessdate_1
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_1,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_2
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_2,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_3
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_3,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_4
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_4,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_5
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_5,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_6
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_6,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_7
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_7,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_8
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_8,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_9
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_9,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_10
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_10,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_11
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_11,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_12
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_12,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_13
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_13,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_14
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_14,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @webrate_asof_businessdate_15
    EXEC [dbo].[usp_get_webrate_asof_businessdate_based_on_condition_by_individual_rc] @property_id,
                                                                                       @roomclass_id,
                                                                                       @comp_id_15,
                                                                                       @start_date,
                                                                                       @end_date,
                                                                                       @business_dt,
                                                                                       @isCompactWebratePaceEnabled
    INSERT INTO @temp_groupBlock_groupPickupByRoomClass
    EXEC [dbo].[usp_get_groupBlock_groupPickup_by_RoomClass] @property_id,
                                                             @roomclass_id,
                                                             @start_date,
                                                             @end_date,
                                                             0,
                                                             @includeZeroCapacityRT
    INSERT INTO @temp_hbr_report
    EXEC [dbo].[usp_get_AsOnLastOptimization_or_BusinessDate_closeLV0_or_HBR_by_individual_rc] @property_id,
                                                                                               @roomclass_id,
                                                                                               @start_date,
                                                                                               @end_date,
                                                                                               @business_dt,
                                                                                               -- case
                                                                                               --     when upper(@isLV0Closed) = 'TRUE' then
                                                                                               --         1
                                                                                               --     else
                                                                                               --         0
                                                                                               --     end,
                                                                                               1,
                                                                                               @rolling_business_dt
    INSERT INTO @temp_hbr_report_close
    EXEC [dbo].[usp_get_current_closeLV0_or_HBR_by_individual_rc] @property_id,
                                                                  @roomclass_id,
                                                                  @start_date,
                                                                  @end_date,
                                                                  1
    -- case
    --     when upper(@isLV0Closed) = 'TRUE' then
    --         1
    --     else
    --         0
    --     end

    select occupancy_dt as dayofarrival,
           dow,
           property_id,
        --    sum(ooo) as ooo,
           specialevent,
           MAX(rc1_ooo) as rc1_ooo,
           MAX(rc2_ooo) as rc2_ooo,
           MAX(rc3_ooo) as rc3_ooo,
           MAX(rc4_ooo) as rc4_ooo,
           MAX(rc5_ooo) as rc5_ooo,
           MAX(rc6_ooo) as rc6_ooo,
           MAX(rc7_ooo) as rc7_ooo,
           MAX(rc8_ooo) as rc8_ooo,
           MAX(rc9_ooo) as rc9_ooo,
           MAX(rc10_ooo) as rc10_ooo,
           MAX(rc1_rooms_sold_current) as rc1_roomssoldcurrent,
           MAX(rc1_rooms_sold_change) as rc1_roomssoldchange,
           MAX(rc2_rooms_sold_current) as rc2_roomssoldcurrent,
           MAX(rc2_rooms_sold_change) as rc2_roomssoldchange,
           MAX(rc3_rooms_sold_current) as rc3_roomssoldcurrent,
           MAX(rc3_rooms_sold_change) as rc3_roomssoldchange,
           MAX(rc4_rooms_sold_current) as rc4_roomssoldcurrent,
           MAX(rc4_rooms_sold_change) as rc4_roomssoldchange,
           MAX(rc5_rooms_sold_current) as rc5_roomssoldcurrent,
           MAX(rc5_rooms_sold_change) as rc5_roomssoldchange,
           MAX(rc6_rooms_sold_current) as rc6_roomssoldcurrent,
           MAX(rc6_rooms_sold_change) as rc6_roomssoldchange,
           MAX(rc7_rooms_sold_current) as rc7_roomssoldcurrent,
           MAX(rc7_rooms_sold_change) as rc7_roomssoldchange,
           MAX(rc8_rooms_sold_current) as rc8_roomssoldcurrent,
           MAX(rc8_rooms_sold_change) as rc8_roomssoldchange,
           MAX(rc9_rooms_sold_current) as rc9_roomssoldcurrent,
           MAX(rc9_rooms_sold_change) as rc9_roomssoldchange,
           MAX(rc10_rooms_sold_current) as rc10_roomssoldcurrent,
           MAX(rc10_rooms_sold_change) as rc10_roomssoldchange,
           MAX(rc1_occupancyforecastcurrent) as rc1_occupancyforecastcurrent,
           MAX(rc1_occupancyforecastchange) as rc1_occupancyforecastchange,
           MAX(rc2_occupancyforecastcurrent) as rc2_occupancyforecastcurrent,
           MAX(rc2_occupancyforecastchange) as rc2_occupancyforecastchange,
           MAX(rc3_occupancyforecastcurrent) as rc3_occupancyforecastcurrent,
           MAX(rc3_occupancyforecastchange) as rc3_occupancyforecastchange,
           MAX(rc4_occupancyforecastcurrent) as rc4_occupancyforecastcurrent,
           MAX(rc4_occupancyforecastchange) as rc4_occupancyforecastchange,
           MAX(rc5_occupancyforecastcurrent) as rc5_occupancyforecastcurrent,
           MAX(rc5_occupancyforecastchange) as rc5_occupancyforecastchange,
           MAX(rc6_occupancyforecastcurrent) as rc6_occupancyforecastcurrent,
           MAX(rc6_occupancyforecastchange) as rc6_occupancyforecastchange,
           MAX(rc7_occupancyforecastcurrent) as rc7_occupancyforecastcurrent,
           MAX(rc7_occupancyforecastchange) as rc7_occupancyforecastchange,
           MAX(rc8_occupancyforecastcurrent) as rc8_occupancyforecastcurrent,
           MAX(rc8_occupancyforecastchange) as rc8_occupancyforecastchange,
           MAX(rc9_occupancyforecastcurrent) as rc9_occupancyforecastcurrent,
           MAX(rc9_occupancyforecastchange) as rc9_occupancyforecastchange,
           MAX(rc10_occupancyforecastcurrent) as rc10_occupancyforecastcurrent,
           MAX(rc10_occupancyforecastchange) as rc10_occupancyforecastchange,
           MAX(rc1_occupancyforecastpercurrent) as rc1_occupancyforecastpercurrent,
           MAX(rc1_occupancyforecastperchange) as rc1_occupancyforecastperchange,
           MAX(rc2_occupancyforecastpercurrent) as rc2_occupancyforecastpercurrent,
           MAX(rc2_occupancyforecastperchange) as rc2_occupancyforecastperchange,
           MAX(rc3_occupancyforecastpercurrent) as rc3_occupancyforecastpercurrent,
           MAX(rc3_occupancyforecastperchange) as rc3_occupancyforecastperchange,
           MAX(rc4_occupancyforecastpercurrent) as rc4_occupancyforecastpercurrent,
           MAX(rc4_occupancyforecastperchange) as rc4_occupancyforecastperchange,
           MAX(rc5_occupancyforecastpercurrent) as rc5_occupancyforecastpercurrent,
           MAX(rc5_occupancyforecastperchange) as rc5_occupancyforecastperchange,
           MAX(rc6_occupancyforecastpercurrent) as rc6_occupancyforecastpercurrent,
           MAX(rc6_occupancyforecastperchange) as rc6_occupancyforecastperchange,
           MAX(rc7_occupancyforecastpercurrent) as rc7_occupancyforecastpercurrent,
           MAX(rc7_occupancyforecastperchange) as rc7_occupancyforecastperchange,
           MAX(rc8_occupancyforecastpercurrent) as rc8_occupancyforecastpercurrent,
           MAX(rc8_occupancyforecastperchange) as rc8_occupancyforecastperchange,
           MAX(rc9_occupancyforecastpercurrent) as rc9_occupancyforecastpercurrent,
           MAX(rc9_occupancyforecastperchange) as rc9_occupancyforecastperchange,
           MAX(rc10_occupancyforecastpercurrent) as rc10_occupancyforecastpercurrent,
           MAX(rc10_occupancyforecastperchange) as rc10_occupancyforecastperchange,
           MAX(rc1_bookedprofitcurrent) as rc1_bookedprofitcurrent,
           MAX(rc1_bookedprofitchange) as rc1_bookedprofitchange,
           MAX(rc2_bookedprofitcurrent) as rc2_bookedprofitcurrent,
           MAX(rc2_bookedprofitchange) as rc2_bookedprofitchange,
           MAX(rc3_bookedprofitcurrent) as rc3_bookedprofitcurrent,
           MAX(rc3_bookedprofitchange) as rc3_bookedprofitchange,
           MAX(rc4_bookedprofitcurrent) as rc4_bookedprofitcurrent,
           MAX(rc4_bookedprofitchange) as rc4_bookedprofitchange,
           MAX(rc5_bookedprofitcurrent) as rc5_bookedprofitcurrent,
           MAX(rc5_bookedprofitchange) as rc5_bookedprofitchange,
           MAX(rc6_bookedprofitcurrent) as rc6_bookedprofitcurrent,
           MAX(rc6_bookedprofitchange) as rc6_bookedprofitchange,
           MAX(rc7_bookedprofitcurrent) as rc7_bookedprofitcurrent,
           MAX(rc7_bookedprofitchange) as rc7_bookedprofitchange,
           MAX(rc8_bookedprofitcurrent) as rc8_bookedprofitcurrent,
           MAX(rc8_bookedprofitchange) as rc8_bookedprofitchange,
           MAX(rc9_bookedprofitcurrent) as rc9_bookedprofitcurrent,
           MAX(rc9_bookedprofitchange) as rc9_bookedprofitchange,
           MAX(rc10_bookedprofitcurrent) as rc10_bookedprofitcurrent,
           MAX(rc10_bookedprofitchange) as rc10_bookedprofitchange,
           MAX(rc1_fcstedprofitcurrent) as rc1_fcstedprofitcurrent,
           MAX(rc1_fcstedprofitchange) as rc1_fcstedprofitchange,
           MAX(rc2_fcstedprofitcurrent) as rc2_fcstedprofitcurrent,
           MAX(rc2_fcstedprofitchange) as rc2_fcstedprofitchange,
           MAX(rc3_fcstedprofitcurrent) as rc3_fcstedprofitcurrent,
           MAX(rc3_fcstedprofitchange) as rc3_fcstedprofitchange,
           MAX(rc4_fcstedprofitcurrent) as rc4_fcstedprofitcurrent,
           MAX(rc4_fcstedprofitchange) as rc4_fcstedprofitchange,
           MAX(rc5_fcstedprofitcurrent) as rc5_fcstedprofitcurrent,
           MAX(rc5_fcstedprofitchange) as rc5_fcstedprofitchange,
           MAX(rc6_fcstedprofitcurrent) as rc6_fcstedprofitcurrent,
           MAX(rc6_fcstedprofitchange) as rc6_fcstedprofitchange,
           MAX(rc7_fcstedprofitcurrent) as rc7_fcstedprofitcurrent,
           MAX(rc7_fcstedprofitchange) as rc7_fcstedprofitchange,
           MAX(rc8_fcstedprofitcurrent) as rc8_fcstedprofitcurrent,
           MAX(rc8_fcstedprofitchange) as rc8_fcstedprofitchange,
           MAX(rc9_fcstedprofitcurrent) as rc9_fcstedprofitcurrent,
           MAX(rc9_fcstedprofitchange) as rc9_fcstedprofitchange,
           MAX(rc10_fcstedprofitcurrent) as rc10_fcstedprofitcurrent,
           MAX(rc10_fcstedprofitchange) as rc10_fcstedprofitchange,
           MAX(rc1_bookedproporcurrent) as rc1_bookedproporcurrent,
           MAX(rc1_bookedproporchange) as rc1_bookedproporchange,
           MAX(rc2_bookedproporcurrent) as rc2_bookedproporcurrent,
           MAX(rc2_bookedproporchange) as rc2_bookedproporchange,
           MAX(rc3_bookedproporcurrent) as rc3_bookedproporcurrent,
           MAX(rc3_bookedproporchange) as rc3_bookedproporchange,
           MAX(rc4_bookedproporcurrent) as rc4_bookedproporcurrent,
           MAX(rc4_bookedproporchange) as rc4_bookedproporchange,
           MAX(rc5_bookedproporcurrent) as rc5_bookedproporcurrent,
           MAX(rc5_bookedproporchange) as rc5_bookedproporchange,
           MAX(rc6_bookedproporcurrent) as rc6_bookedproporcurrent,
           MAX(rc6_bookedproporchange) as rc6_bookedproporchange,
           MAX(rc7_bookedproporcurrent) as rc7_bookedproporcurrent,
           MAX(rc7_bookedproporchange) as rc7_bookedproporchange,
           MAX(rc8_bookedproporcurrent) as rc8_bookedproporcurrent,
           MAX(rc8_bookedproporchange) as rc8_bookedproporchange,
           MAX(rc9_bookedproporcurrent) as rc9_bookedproporcurrent,
           MAX(rc9_bookedproporchange) as rc9_bookedproporchange,
           MAX(rc10_bookedproporcurrent) as rc10_bookedproporcurrent,
           MAX(rc10_bookedproporchange) as rc10_bookedproporchange,
           MAX(rc1_estimatedproporcurrent) as rc1_estimatedproporcurrent,
           MAX(rc1_estimatedproporchange) as rc1_estimatedproporchange,
           MAX(rc2_estimatedproporcurrent) as rc2_estimatedproporcurrent,
           MAX(rc2_estimatedproporchange) as rc2_estimatedproporchange,
           MAX(rc3_estimatedproporcurrent) as rc3_estimatedproporcurrent,
           MAX(rc3_estimatedproporchange) as rc3_estimatedproporchange,
           MAX(rc4_estimatedproporcurrent) as rc4_estimatedproporcurrent,
           MAX(rc4_estimatedproporchange) as rc4_estimatedproporchange,
           MAX(rc5_estimatedproporcurrent) as rc5_estimatedproporcurrent,
           MAX(rc5_estimatedproporchange) as rc5_estimatedproporchange,
           MAX(rc6_estimatedproporcurrent) as rc6_estimatedproporcurrent,
           MAX(rc6_estimatedproporchange) as rc6_estimatedproporchange,
           MAX(rc7_estimatedproporcurrent) as rc7_estimatedproporcurrent,
           MAX(rc7_estimatedproporchange) as rc7_estimatedproporchange,
           MAX(rc8_estimatedproporcurrent) as rc8_estimatedproporcurrent,
           MAX(rc8_estimatedproporchange) as rc8_estimatedproporchange,
           MAX(rc9_estimatedproporcurrent) as rc9_estimatedproporcurrent,
           MAX(rc9_estimatedproporchange) as rc9_estimatedproporchange,
           MAX(rc10_estimatedproporcurrent) as rc10_estimatedproporcurrent,
           MAX(rc10_estimatedproporchange) as rc10_estimatedproporchange,
           MAX(rc1_bookedproparcurrent) as rc1_bookedproparcurrent,
           MAX(rc1_bookedproparchange) as rc1_bookedproparchange,
           MAX(rc2_bookedproparcurrent) as rc2_bookedproparcurrent,
           MAX(rc2_bookedproparchange) as rc2_bookedproparchange,
           MAX(rc3_bookedproparcurrent) as rc3_bookedproparcurrent,
           MAX(rc3_bookedproparchange) as rc3_bookedproparchange,
           MAX(rc4_bookedproparcurrent) as rc4_bookedproparcurrent,
           MAX(rc4_bookedproparchange) as rc4_bookedproparchange,
           MAX(rc5_bookedproparcurrent) as rc5_bookedproparcurrent,
           MAX(rc5_bookedproparchange) as rc5_bookedproparchange,
           MAX(rc6_bookedproparcurrent) as rc6_bookedproparcurrent,
           MAX(rc6_bookedproparchange) as rc6_bookedproparchange,
           MAX(rc7_bookedproparcurrent) as rc7_bookedproparcurrent,
           MAX(rc7_bookedproparchange) as rc7_bookedproparchange,
           MAX(rc8_bookedproparcurrent) as rc8_bookedproparcurrent,
           MAX(rc8_bookedproparchange) as rc8_bookedproparchange,
           MAX(rc9_bookedproparcurrent) as rc9_bookedproparcurrent,
           MAX(rc9_bookedproparchange) as rc9_bookedproparchange,
           MAX(rc10_bookedproparcurrent) as rc10_bookedproparcurrent,
           MAX(rc10_bookedproparchange) as rc10_bookedproparchange,
           MAX(rc1_estimatedproparcurrent) as rc1_estimatedproparcurrent,
           MAX(rc1_estimatedproparchange) as rc1_estimatedproparchange,
           MAX(rc2_estimatedproparcurrent) as rc2_estimatedproparcurrent,
           MAX(rc2_estimatedproparchange) as rc2_estimatedproparchange,
           MAX(rc3_estimatedproparcurrent) as rc3_estimatedproparcurrent,
           MAX(rc3_estimatedproparchange) as rc3_estimatedproparchange,
           MAX(rc4_estimatedproparcurrent) as rc4_estimatedproparcurrent,
           MAX(rc4_estimatedproparchange) as rc4_estimatedproparchange,
           MAX(rc5_estimatedproparcurrent) as rc5_estimatedproparcurrent,
           MAX(rc5_estimatedproparchange) as rc5_estimatedproparchange,
           MAX(rc6_estimatedproparcurrent) as rc6_estimatedproparcurrent,
           MAX(rc6_estimatedproparchange) as rc6_estimatedproparchange,
           MAX(rc7_estimatedproparcurrent) as rc7_estimatedproparcurrent,
           MAX(rc7_estimatedproparchange) as rc7_estimatedproparchange,
           MAX(rc8_estimatedproparcurrent) as rc8_estimatedproparcurrent,
           MAX(rc8_estimatedproparchange) as rc8_estimatedproparchange,
           MAX(rc9_estimatedproparcurrent) as rc9_estimatedproparcurrent,
           MAX(rc9_estimatedproparchange) as rc9_estimatedproparchange,
           MAX(rc10_estimatedproparcurrent) as rc10_estimatedproparcurrent,
           MAX(rc10_estimatedproparchange) as rc10_estimatedproparchange,
           MAX(rc1_bookedroomrevenuecurrent) as rc1_bookedroomrevenuecurrent,
           MAX(rc1_bookedroomrevenuechange) as rc1_bookedroomrevenuechange,
           MAX(rc2_bookedroomrevenuecurrent) as rc2_bookedroomrevenuecurrent,
           MAX(rc2_bookedroomrevenuechange) as rc2_bookedroomrevenuechange,
           MAX(rc3_bookedroomrevenuecurrent) as rc3_bookedroomrevenuecurrent,
           MAX(rc3_bookedroomrevenuechange) as rc3_bookedroomrevenuechange,
           MAX(rc4_bookedroomrevenuecurrent) as rc4_bookedroomrevenuecurrent,
           MAX(rc4_bookedroomrevenuechange) as rc4_bookedroomrevenuechange,
           MAX(rc5_bookedroomrevenuecurrent) as rc5_bookedroomrevenuecurrent,
           MAX(rc5_bookedroomrevenuechange) as rc5_bookedroomrevenuechange,
           MAX(rc6_bookedroomrevenuecurrent) as rc6_bookedroomrevenuecurrent,
           MAX(rc6_bookedroomrevenuechange) as rc6_bookedroomrevenuechange,
           MAX(rc7_bookedroomrevenuecurrent) as rc7_bookedroomrevenuecurrent,
           MAX(rc7_bookedroomrevenuechange) as rc7_bookedroomrevenuechange,
           MAX(rc8_bookedroomrevenuecurrent) as rc8_bookedroomrevenuecurrent,
           MAX(rc8_bookedroomrevenuechange) as rc8_bookedroomrevenuechange,
           MAX(rc9_bookedroomrevenuecurrent) as rc9_bookedroomrevenuecurrent,
           MAX(rc9_bookedroomrevenuechange) as rc9_bookedroomrevenuechange,
           MAX(rc10_bookedroomrevenuecurrent) as rc10_bookedroomrevenuecurrent,
           MAX(rc10_bookedroomrevenuechange) as rc10_bookedroomrevenuechange,
           MAX(rc1_fcstedroomrevenuecurrent) as rc1_fcstedroomrevenuecurrent,
           MAX(rc1_fcstedroomrevenuechange) as rc1_fcstedroomrevenuechange,
           MAX(rc2_fcstedroomrevenuecurrent) as rc2_fcstedroomrevenuecurrent,
           MAX(rc2_fcstedroomrevenuechange) as rc2_fcstedroomrevenuechange,
           MAX(rc3_fcstedroomrevenuecurrent) as rc3_fcstedroomrevenuecurrent,
           MAX(rc3_fcstedroomrevenuechange) as rc3_fcstedroomrevenuechange,
           MAX(rc4_fcstedroomrevenuecurrent) as rc4_fcstedroomrevenuecurrent,
           MAX(rc4_fcstedroomrevenuechange) as rc4_fcstedroomrevenuechange,
           MAX(rc5_fcstedroomrevenuecurrent) as rc5_fcstedroomrevenuecurrent,
           MAX(rc5_fcstedroomrevenuechange) as rc5_fcstedroomrevenuechange,
           MAX(rc6_fcstedroomrevenuecurrent) as rc6_fcstedroomrevenuecurrent,
           MAX(rc6_fcstedroomrevenuechange) as rc6_fcstedroomrevenuechange,
           MAX(rc7_fcstedroomrevenuecurrent) as rc7_fcstedroomrevenuecurrent,
           MAX(rc7_fcstedroomrevenuechange) as rc7_fcstedroomrevenuechange,
           MAX(rc8_fcstedroomrevenuecurrent) as rc8_fcstedroomrevenuecurrent,
           MAX(rc8_fcstedroomrevenuechange) as rc8_fcstedroomrevenuechange,
           MAX(rc9_fcstedroomrevenuecurrent) as rc9_fcstedroomrevenuecurrent,
           MAX(rc9_fcstedroomrevenuechange) as rc9_fcstedroomrevenuechange,
           MAX(rc10_fcstedroomrevenuecurrent) as rc10_fcstedroomrevenuecurrent,
           MAX(rc10_fcstedroomrevenuechange) as rc10_fcstedroomrevenuechange,
           MAX(rc1_bookedadrcurrent) as rc1_bookedadrcurrent,
           MAX(rc1_bookedadrchange) as rc1_bookedadrchange,
           MAX(rc2_bookedadrcurrent) as rc2_bookedadrcurrent,
           MAX(rc2_bookedadrchange) as rc2_bookedadrchange,
           MAX(rc3_bookedadrcurrent) as rc3_bookedadrcurrent,
           MAX(rc3_bookedadrchange) as rc3_bookedadrchange,
           MAX(rc4_bookedadrcurrent) as rc4_bookedadrcurrent,
           MAX(rc4_bookedadrchange) as rc4_bookedadrchange,
           MAX(rc5_bookedadrcurrent) as rc5_bookedadrcurrent,
           MAX(rc5_bookedadrchange) as rc5_bookedadrchange,
           MAX(rc6_bookedadrcurrent) as rc6_bookedadrcurrent,
           MAX(rc6_bookedadrchange) as rc6_bookedadrchange,
           MAX(rc7_bookedadrcurrent) as rc7_bookedadrcurrent,
           MAX(rc7_bookedadrchange) as rc7_bookedadrchange,
           MAX(rc8_bookedadrcurrent) as rc8_bookedadrcurrent,
           MAX(rc8_bookedadrchange) as rc8_bookedadrchange,
           MAX(rc9_bookedadrcurrent) as rc9_bookedadrcurrent,
           MAX(rc9_bookedadrchange) as rc9_bookedadrchange,
           MAX(rc10_bookedadrcurrent) as rc10_bookedadrcurrent,
           MAX(rc10_bookedadrchange) as rc10_bookedadrchange,
           MAX(rc1_estimatedadrcurrent) as rc1_estimatedadrcurrent,
           MAX(rc1_estimatedadrchange) as rc1_estimatedadrchange,
           MAX(rc2_estimatedadrcurrent) as rc2_estimatedadrcurrent,
           MAX(rc2_estimatedadrchange) as rc2_estimatedadrchange,
           MAX(rc3_estimatedadrcurrent) as rc3_estimatedadrcurrent,
           MAX(rc3_estimatedadrchange) as rc3_estimatedadrchange,
           MAX(rc4_estimatedadrcurrent) as rc4_estimatedadrcurrent,
           MAX(rc4_estimatedadrchange) as rc4_estimatedadrchange,
           MAX(rc5_estimatedadrcurrent) as rc5_estimatedadrcurrent,
           MAX(rc5_estimatedadrchange) as rc5_estimatedadrchange,
           MAX(rc6_estimatedadrcurrent) as rc6_estimatedadrcurrent,
           MAX(rc6_estimatedadrchange) as rc6_estimatedadrchange,
           MAX(rc7_estimatedadrcurrent) as rc7_estimatedadrcurrent,
           MAX(rc7_estimatedadrchange) as rc7_estimatedadrchange,
           MAX(rc8_estimatedadrcurrent) as rc8_estimatedadrcurrent,
           MAX(rc8_estimatedadrchange) as rc8_estimatedadrchange,
           MAX(rc9_estimatedadrcurrent) as rc9_estimatedadrcurrent,
           MAX(rc9_estimatedadrchange) as rc9_estimatedadrchange,
           MAX(rc10_estimatedadrcurrent) as rc10_estimatedadrcurrent,
           MAX(rc10_estimatedadrchange) as rc10_estimatedadrchange,
           MAX(rc1_bookedrevparcurrent) as rc1_bookedrevparcurrent,
           MAX(rc1_bookedrevparchange) as rc1_bookedrevparchange,
           MAX(rc2_bookedrevparcurrent) as rc2_bookedrevparcurrent,
           MAX(rc2_bookedrevparchange) as rc2_bookedrevparchange,
           MAX(rc3_bookedrevparcurrent) as rc3_bookedrevparcurrent,
           MAX(rc3_bookedrevparchange) as rc3_bookedrevparchange,
           MAX(rc4_bookedrevparcurrent) as rc4_bookedrevparcurrent,
           MAX(rc4_bookedrevparchange) as rc4_bookedrevparchange,
           MAX(rc5_bookedrevparcurrent) as rc5_bookedrevparcurrent,
           MAX(rc5_bookedrevparchange) as rc5_bookedrevparchange,
           MAX(rc6_bookedrevparcurrent) as rc6_bookedrevparcurrent,
           MAX(rc6_bookedrevparchange) as rc6_bookedrevparchange,
           MAX(rc7_bookedrevparcurrent) as rc7_bookedrevparcurrent,
           MAX(rc7_bookedrevparchange) as rc7_bookedrevparchange,
           MAX(rc8_bookedrevparcurrent) as rc8_bookedrevparcurrent,
           MAX(rc8_bookedrevparchange) as rc8_bookedrevparchange,
           MAX(rc9_bookedrevparcurrent) as rc9_bookedrevparcurrent,
           MAX(rc9_bookedrevparchange) as rc9_bookedrevparchange,
           MAX(rc10_bookedrevparcurrent) as rc10_bookedrevparcurrent,
           MAX(rc10_bookedrevparchange) as rc10_bookedrevparchange,
           MAX(rc1_estimatedrevparcurrent) as rc1_estimatedrevparcurrent,
           MAX(rc1_estimatedrevparchange) as rc1_estimatedrevparchange,
           MAX(rc2_estimatedrevparcurrent) as rc2_estimatedrevparcurrent,
           MAX(rc2_estimatedrevparchange) as rc2_estimatedrevparchange,
           MAX(rc3_estimatedrevparcurrent) as rc3_estimatedrevparcurrent,
           MAX(rc3_estimatedrevparchange) as rc3_estimatedrevparchange,
           MAX(rc4_estimatedrevparcurrent) as rc4_estimatedrevparcurrent,
           MAX(rc4_estimatedrevparchange) as rc4_estimatedrevparchange,
           MAX(rc5_estimatedrevparcurrent) as rc5_estimatedrevparcurrent,
           MAX(rc5_estimatedrevparchange) as rc5_estimatedrevparchange,
           MAX(rc6_estimatedrevparcurrent) as rc6_estimatedrevparcurrent,
           MAX(rc6_estimatedrevparchange) as rc6_estimatedrevparchange,
           MAX(rc7_estimatedrevparcurrent) as rc7_estimatedrevparcurrent,
           MAX(rc7_estimatedrevparchange) as rc7_estimatedrevparchange,
           MAX(rc8_estimatedrevparcurrent) as rc8_estimatedrevparcurrent,
           MAX(rc8_estimatedrevparchange) as rc8_estimatedrevparchange,
           MAX(rc9_estimatedrevparcurrent) as rc9_estimatedrevparcurrent,
           MAX(rc9_estimatedrevparchange) as rc9_estimatedrevparchange,
           MAX(rc10_estimatedrevparcurrent) as rc10_estimatedrevparcurrent,
           MAX(rc10_estimatedrevparchange) as rc10_estimatedrevparchange,
           MAX(rc1_lrv) as rc1_lrv,
           MAX(rc1_lrv_change) as rc1_lrv_change,
           MAX(rc2_lrv) as rc2_lrv,
           MAX(rc2_lrv_change) as rc2_lrv_change,
           MAX(rc3_lrv) as rc3_lrv,
           MAX(rc3_lrv_change) as rc3_lrv_change,
           MAX(rc4_lrv) as rc4_lrv,
           MAX(rc4_lrv_change) as rc4_lrv_change,
           MAX(rc5_lrv) as rc5_lrv,
           MAX(rc5_lrv_change) as rc5_lrv_change,
           MAX(rc6_lrv) as rc6_lrv,
           MAX(rc6_lrv_change) as rc6_lrv_change,
           MAX(rc7_lrv) as rc7_lrv,
           MAX(rc7_lrv_change) as rc7_lrv_change,
           MAX(rc8_lrv) as rc8_lrv,
           MAX(rc8_lrv_change) as rc8_lrv_change,
           MAX(rc9_lrv) as rc9_lrv,
           MAX(rc9_lrv_change) as rc9_lrv_change,
           MAX(rc10_lrv) as rc10_lrv,
           MAX(rc10_lrv_change) as rc10_lrv_change,
           MAX(rc1_overbookingcurrent) as rc1_overbookingcurrent,
           MAX(rc1_overbookingchange) as rc1_overbookingchange,
           MAX(rc2_overbookingcurrent) as rc2_overbookingcurrent,
           MAX(rc2_overbookingchange) as rc2_overbookingchange,
           MAX(rc3_overbookingcurrent) as rc3_overbookingcurrent,
           MAX(rc3_overbookingchange) as rc3_overbookingchange,
           MAX(rc4_overbookingcurrent) as rc4_overbookingcurrent,
           MAX(rc4_overbookingchange) as rc4_overbookingchange,
           MAX(rc5_overbookingcurrent) as rc5_overbookingcurrent,
           MAX(rc5_overbookingchange) as rc5_overbookingchange,
           MAX(rc6_overbookingcurrent) as rc6_overbookingcurrent,
           MAX(rc6_overbookingchange) as rc6_overbookingchange,
           MAX(rc7_overbookingcurrent) as rc7_overbookingcurrent,
           MAX(rc7_overbookingchange) as rc7_overbookingchange,
           MAX(rc8_overbookingcurrent) as rc8_overbookingcurrent,
           MAX(rc8_overbookingchange) as rc8_overbookingchange,
           MAX(rc9_overbookingcurrent) as rc9_overbookingcurrent,
           MAX(rc9_overbookingchange) as rc9_overbookingchange,
           MAX(rc10_overbookingcurrent) as rc10_overbookingcurrent,
           MAX(rc10_overbookingchange) as rc10_overbookingchange,
           MAX(rc1_master_class_name) as rc1_master_class_name,
           MAX(rc2_master_class_name) as rc2_master_class_name,
           MAX(rc3_master_class_name) as rc3_master_class_name,
           MAX(rc4_master_class_name) as rc4_master_class_name,
           MAX(rc5_master_class_name) as rc5_master_class_name,
           MAX(rc6_master_class_name) as rc6_master_class_name,
           MAX(rc7_master_class_name) as rc7_master_class_name,
           MAX(rc8_master_class_name) as rc8_master_class_name,
           MAX(rc9_master_class_name) as rc9_master_class_name,
           MAX(rc10_master_class_name) as rc10_master_class_name,
           MAX(rc1_bar_los1) as rc1_bar_los1,
           MAX(rc1_bar_los2) as rc1_bar_los2,
           MAX(rc1_bar_los3) as rc1_bar_los3,
           MAX(rc1_bar_los4) as rc1_bar_los4,
           MAX(rc1_bar_los5) as rc1_bar_los5,
           MAX(rc1_bar_los6) as rc1_bar_los6,
           MAX(rc1_bar_los7) as rc1_bar_los7,
           MAX(rc1_bar_los8) as rc1_bar_los8,
           MAX(rc2_bar_los1) as rc2_bar_los1,
           MAX(rc2_bar_los2) as rc2_bar_los2,
           MAX(rc2_bar_los3) as rc2_bar_los3,
           MAX(rc2_bar_los4) as rc2_bar_los4,
           MAX(rc2_bar_los5) as rc2_bar_los5,
           MAX(rc2_bar_los6) as rc2_bar_los6,
           MAX(rc2_bar_los7) as rc2_bar_los7,
           MAX(rc2_bar_los8) as rc2_bar_los8,
           MAX(rc3_bar_los1) as rc3_bar_los1,
           MAX(rc3_bar_los2) as rc3_bar_los2,
           MAX(rc3_bar_los3) as rc3_bar_los3,
           MAX(rc3_bar_los4) as rc3_bar_los4,
           MAX(rc3_bar_los5) as rc3_bar_los5,
           MAX(rc3_bar_los6) as rc3_bar_los6,
           MAX(rc3_bar_los7) as rc3_bar_los7,
           MAX(rc3_bar_los8) as rc3_bar_los8,
           MAX(rc4_bar_los1) as rc4_bar_los1,
           MAX(rc4_bar_los2) as rc4_bar_los2,
           MAX(rc4_bar_los3) as rc4_bar_los3,
           MAX(rc4_bar_los4) as rc4_bar_los4,
           MAX(rc4_bar_los5) as rc4_bar_los5,
           MAX(rc4_bar_los6) as rc4_bar_los6,
           MAX(rc4_bar_los7) as rc4_bar_los7,
           MAX(rc4_bar_los8) as rc4_bar_los8,
           MAX(rc5_bar_los1) as rc5_bar_los1,
           MAX(rc5_bar_los2) as rc5_bar_los2,
           MAX(rc5_bar_los3) as rc5_bar_los3,
           MAX(rc5_bar_los4) as rc5_bar_los4,
           MAX(rc5_bar_los5) as rc5_bar_los5,
           MAX(rc5_bar_los6) as rc5_bar_los6,
           MAX(rc5_bar_los7) as rc5_bar_los7,
           MAX(rc5_bar_los8) as rc5_bar_los8,
           MAX(rc6_bar_los1) as rc6_bar_los1,
           MAX(rc6_bar_los2) as rc6_bar_los2,
           MAX(rc6_bar_los3) as rc6_bar_los3,
           MAX(rc6_bar_los4) as rc6_bar_los4,
           MAX(rc6_bar_los5) as rc6_bar_los5,
           MAX(rc6_bar_los6) as rc6_bar_los6,
           MAX(rc6_bar_los7) as rc6_bar_los7,
           MAX(rc6_bar_los8) as rc6_bar_los8,
           MAX(rc7_bar_los1) as rc7_bar_los1,
           MAX(rc7_bar_los2) as rc7_bar_los2,
           MAX(rc7_bar_los3) as rc7_bar_los3,
           MAX(rc7_bar_los4) as rc7_bar_los4,
           MAX(rc7_bar_los5) as rc7_bar_los5,
           MAX(rc7_bar_los6) as rc7_bar_los6,
           MAX(rc7_bar_los7) as rc7_bar_los7,
           MAX(rc7_bar_los8) as rc7_bar_los8,
           MAX(rc8_bar_los1) as rc8_bar_los1,
           MAX(rc8_bar_los2) as rc8_bar_los2,
           MAX(rc8_bar_los3) as rc8_bar_los3,
           MAX(rc8_bar_los4) as rc8_bar_los4,
           MAX(rc8_bar_los5) as rc8_bar_los5,
           MAX(rc8_bar_los6) as rc8_bar_los6,
           MAX(rc8_bar_los7) as rc8_bar_los7,
           MAX(rc8_bar_los8) as rc8_bar_los8,
           MAX(rc9_bar_los1) as rc9_bar_los1,
           MAX(rc9_bar_los2) as rc9_bar_los2,
           MAX(rc9_bar_los3) as rc9_bar_los3,
           MAX(rc9_bar_los4) as rc9_bar_los4,
           MAX(rc9_bar_los5) as rc9_bar_los5,
           MAX(rc9_bar_los6) as rc9_bar_los6,
           MAX(rc9_bar_los7) as rc9_bar_los7,
           MAX(rc9_bar_los8) as rc9_bar_los8,
           MAX(rc10_bar_los1) as rc10_bar_los1,
           MAX(rc10_bar_los2) as rc10_bar_los2,
           MAX(rc10_bar_los3) as rc10_bar_los3,
           MAX(rc10_bar_los4) as rc10_bar_los4,
           MAX(rc10_bar_los5) as rc10_bar_los5,
           MAX(rc10_bar_los6) as rc10_bar_los6,
           MAX(rc10_bar_los7) as rc10_bar_los7,
           MAX(rc10_bar_los8) as rc10_bar_los8,
           MAX(rc1_bar_by_day) as rc1_bar_by_day,
           MAX(rc2_bar_by_day) as rc2_bar_by_day,
           MAX(rc3_bar_by_day) as rc3_bar_by_day,
           MAX(rc4_bar_by_day) as rc4_bar_by_day,
           MAX(rc5_bar_by_day) as rc5_bar_by_day,
           MAX(rc6_bar_by_day) as rc6_bar_by_day,
           MAX(rc7_bar_by_day) as rc7_bar_by_day,
           MAX(rc8_bar_by_day) as rc8_bar_by_day,
           MAX(rc9_bar_by_day) as rc9_bar_by_day,
           MAX(rc10_bar_by_day) as rc10_bar_by_day,
           MAX(rc1_ratecode_los1) as rc1_ratecode_los1,
           MAX(rc1_ratecode_los2) as rc1_ratecode_los2,
           MAX(rc1_ratecode_los3) as rc1_ratecode_los3,
           MAX(rc1_ratecode_los4) as rc1_ratecode_los4,
           MAX(rc1_ratecode_los5) as rc1_ratecode_los5,
           MAX(rc1_ratecode_los6) as rc1_ratecode_los6,
           MAX(rc1_ratecode_los7) as rc1_ratecode_los7,
           MAX(rc1_ratecode_los8) as rc1_ratecode_los8,
           MAX(rc2_ratecode_los1) as rc2_ratecode_los1,
           MAX(rc2_ratecode_los2) as rc2_ratecode_los2,
           MAX(rc2_ratecode_los3) as rc2_ratecode_los3,
           MAX(rc2_ratecode_los4) as rc2_ratecode_los4,
           MAX(rc2_ratecode_los5) as rc2_ratecode_los5,
           MAX(rc2_ratecode_los6) as rc2_ratecode_los6,
           MAX(rc2_ratecode_los7) as rc2_ratecode_los7,
           MAX(rc2_ratecode_los8) as rc2_ratecode_los8,
           MAX(rc3_ratecode_los1) as rc3_ratecode_los1,
           MAX(rc3_ratecode_los2) as rc3_ratecode_los2,
           MAX(rc3_ratecode_los3) as rc3_ratecode_los3,
           MAX(rc3_ratecode_los4) as rc3_ratecode_los4,
           MAX(rc3_ratecode_los5) as rc3_ratecode_los5,
           MAX(rc3_ratecode_los6) as rc3_ratecode_los6,
           MAX(rc3_ratecode_los7) as rc3_ratecode_los7,
           MAX(rc3_ratecode_los8) as rc3_ratecode_los8,
           MAX(rc4_ratecode_los1) as rc4_ratecode_los1,
           MAX(rc4_ratecode_los2) as rc4_ratecode_los2,
           MAX(rc4_ratecode_los3) as rc4_ratecode_los3,
           MAX(rc4_ratecode_los4) as rc4_ratecode_los4,
           MAX(rc4_ratecode_los5) as rc4_ratecode_los5,
           MAX(rc4_ratecode_los6) as rc4_ratecode_los6,
           MAX(rc4_ratecode_los7) as rc4_ratecode_los7,
           MAX(rc4_ratecode_los8) as rc4_ratecode_los8,
           MAX(rc5_ratecode_los1) as rc5_ratecode_los1,
           MAX(rc5_ratecode_los2) as rc5_ratecode_los2,
           MAX(rc5_ratecode_los3) as rc5_ratecode_los3,
           MAX(rc5_ratecode_los4) as rc5_ratecode_los4,
           MAX(rc5_ratecode_los5) as rc5_ratecode_los5,
           MAX(rc5_ratecode_los6) as rc5_ratecode_los6,
           MAX(rc5_ratecode_los7) as rc5_ratecode_los7,
           MAX(rc5_ratecode_los8) as rc5_ratecode_los8,
           MAX(rc6_ratecode_los1) as rc6_ratecode_los1,
           MAX(rc6_ratecode_los2) as rc6_ratecode_los2,
           MAX(rc6_ratecode_los3) as rc6_ratecode_los3,
           MAX(rc6_ratecode_los4) as rc6_ratecode_los4,
           MAX(rc6_ratecode_los5) as rc6_ratecode_los5,
           MAX(rc6_ratecode_los6) as rc6_ratecode_los6,
           MAX(rc6_ratecode_los7) as rc6_ratecode_los7,
           MAX(rc6_ratecode_los8) as rc6_ratecode_los8,
           MAX(rc7_ratecode_los1) as rc7_ratecode_los1,
           MAX(rc7_ratecode_los2) as rc7_ratecode_los2,
           MAX(rc7_ratecode_los3) as rc7_ratecode_los3,
           MAX(rc7_ratecode_los4) as rc7_ratecode_los4,
           MAX(rc7_ratecode_los5) as rc7_ratecode_los5,
           MAX(rc7_ratecode_los6) as rc7_ratecode_los6,
           MAX(rc7_ratecode_los7) as rc7_ratecode_los7,
           MAX(rc7_ratecode_los8) as rc7_ratecode_los8,
           MAX(rc8_ratecode_los1) as rc8_ratecode_los1,
           MAX(rc8_ratecode_los2) as rc8_ratecode_los2,
           MAX(rc8_ratecode_los3) as rc8_ratecode_los3,
           MAX(rc8_ratecode_los4) as rc8_ratecode_los4,
           MAX(rc8_ratecode_los5) as rc8_ratecode_los5,
           MAX(rc8_ratecode_los6) as rc8_ratecode_los6,
           MAX(rc8_ratecode_los7) as rc8_ratecode_los7,
           MAX(rc8_ratecode_los8) as rc8_ratecode_los8,
           MAX(rc9_ratecode_los1) as rc9_ratecode_los1,
           MAX(rc9_ratecode_los2) as rc9_ratecode_los2,
           MAX(rc9_ratecode_los3) as rc9_ratecode_los3,
           MAX(rc9_ratecode_los4) as rc9_ratecode_los4,
           MAX(rc9_ratecode_los5) as rc9_ratecode_los5,
           MAX(rc9_ratecode_los6) as rc9_ratecode_los6,
           MAX(rc9_ratecode_los7) as rc9_ratecode_los7,
           MAX(rc9_ratecode_los8) as rc9_ratecode_los8,
           MAX(rc10_ratecode_los1) as rc10_ratecode_los1,
           MAX(rc10_ratecode_los2) as rc10_ratecode_los2,
           MAX(rc10_ratecode_los3) as rc10_ratecode_los3,
           MAX(rc10_ratecode_los4) as rc10_ratecode_los4,
           MAX(rc10_ratecode_los5) as rc10_ratecode_los5,
           MAX(rc10_ratecode_los6) as rc10_ratecode_los6,
           MAX(rc10_ratecode_los7) as rc10_ratecode_los7,
           MAX(rc10_ratecode_los8) as rc10_ratecode_los8,
           MAX(rc1_bar_los1_change) as rc1_bar_los1_change,
           MAX(rc1_bar_los2_change) as rc1_bar_los2_change,
           MAX(rc1_bar_los3_change) as rc1_bar_los3_change,
           MAX(rc1_bar_los4_change) as rc1_bar_los4_change,
           MAX(rc1_bar_los5_change) as rc1_bar_los5_change,
           MAX(rc1_bar_los6_change) as rc1_bar_los6_change,
           MAX(rc1_bar_los7_change) as rc1_bar_los7_change,
           MAX(rc1_bar_los8_change) as rc1_bar_los8_change,
           MAX(rc2_bar_los1_change) as rc2_bar_los1_change,
           MAX(rc2_bar_los2_change) as rc2_bar_los2_change,
           MAX(rc2_bar_los3_change) as rc2_bar_los3_change,
           MAX(rc2_bar_los4_change) as rc2_bar_los4_change,
           MAX(rc2_bar_los5_change) as rc2_bar_los5_change,
           MAX(rc2_bar_los6_change) as rc2_bar_los6_change,
           MAX(rc2_bar_los7_change) as rc2_bar_los7_change,
           MAX(rc2_bar_los8_change) as rc2_bar_los8_change,
           MAX(rc3_bar_los1_change) as rc3_bar_los1_change,
           MAX(rc3_bar_los2_change) as rc3_bar_los2_change,
           MAX(rc3_bar_los3_change) as rc3_bar_los3_change,
           MAX(rc3_bar_los4_change) as rc3_bar_los4_change,
           MAX(rc3_bar_los5_change) as rc3_bar_los5_change,
           MAX(rc3_bar_los6_change) as rc3_bar_los6_change,
           MAX(rc3_bar_los7_change) as rc3_bar_los7_change,
           MAX(rc3_bar_los8_change) as rc3_bar_los8_change,
           MAX(rc4_bar_los1_change) as rc4_bar_los1_change,
           MAX(rc4_bar_los2_change) as rc4_bar_los2_change,
           MAX(rc4_bar_los3_change) as rc4_bar_los3_change,
           MAX(rc4_bar_los4_change) as rc4_bar_los4_change,
           MAX(rc4_bar_los5_change) as rc4_bar_los5_change,
           MAX(rc4_bar_los6_change) as rc4_bar_los6_change,
           MAX(rc4_bar_los7_change) as rc4_bar_los7_change,
           MAX(rc4_bar_los8_change) as rc4_bar_los8_change,
           MAX(rc5_bar_los1_change) as rc5_bar_los1_change,
           MAX(rc5_bar_los2_change) as rc5_bar_los2_change,
           MAX(rc5_bar_los3_change) as rc5_bar_los3_change,
           MAX(rc5_bar_los4_change) as rc5_bar_los4_change,
           MAX(rc5_bar_los5_change) as rc5_bar_los5_change,
           MAX(rc5_bar_los6_change) as rc5_bar_los6_change,
           MAX(rc5_bar_los7_change) as rc5_bar_los7_change,
           MAX(rc5_bar_los8_change) as rc5_bar_los8_change,
           MAX(rc6_bar_los1_change) as rc6_bar_los1_change,
           MAX(rc6_bar_los2_change) as rc6_bar_los2_change,
           MAX(rc6_bar_los3_change) as rc6_bar_los3_change,
           MAX(rc6_bar_los4_change) as rc6_bar_los4_change,
           MAX(rc6_bar_los5_change) as rc6_bar_los5_change,
           MAX(rc6_bar_los6_change) as rc6_bar_los6_change,
           MAX(rc6_bar_los7_change) as rc6_bar_los7_change,
           MAX(rc6_bar_los8_change) as rc6_bar_los8_change,
           MAX(rc7_bar_los1_change) as rc7_bar_los1_change,
           MAX(rc7_bar_los2_change) as rc7_bar_los2_change,
           MAX(rc7_bar_los3_change) as rc7_bar_los3_change,
           MAX(rc7_bar_los4_change) as rc7_bar_los4_change,
           MAX(rc7_bar_los5_change) as rc7_bar_los5_change,
           MAX(rc7_bar_los6_change) as rc7_bar_los6_change,
           MAX(rc7_bar_los7_change) as rc7_bar_los7_change,
           MAX(rc7_bar_los8_change) as rc7_bar_los8_change,
           MAX(rc8_bar_los1_change) as rc8_bar_los1_change,
           MAX(rc8_bar_los2_change) as rc8_bar_los2_change,
           MAX(rc8_bar_los3_change) as rc8_bar_los3_change,
           MAX(rc8_bar_los4_change) as rc8_bar_los4_change,
           MAX(rc8_bar_los5_change) as rc8_bar_los5_change,
           MAX(rc8_bar_los6_change) as rc8_bar_los6_change,
           MAX(rc8_bar_los7_change) as rc8_bar_los7_change,
           MAX(rc8_bar_los8_change) as rc8_bar_los8_change,
           MAX(rc9_bar_los1_change) as rc9_bar_los1_change,
           MAX(rc9_bar_los2_change) as rc9_bar_los2_change,
           MAX(rc9_bar_los3_change) as rc9_bar_los3_change,
           MAX(rc9_bar_los4_change) as rc9_bar_los4_change,
           MAX(rc9_bar_los5_change) as rc9_bar_los5_change,
           MAX(rc9_bar_los6_change) as rc9_bar_los6_change,
           MAX(rc9_bar_los7_change) as rc9_bar_los7_change,
           MAX(rc9_bar_los8_change) as rc9_bar_los8_change,
           MAX(rc10_bar_los1_change) as rc10_bar_los1_change,
           MAX(rc10_bar_los2_change) as rc10_bar_los2_change,
           MAX(rc10_bar_los3_change) as rc10_bar_los3_change,
           MAX(rc10_bar_los4_change) as rc10_bar_los4_change,
           MAX(rc10_bar_los5_change) as rc10_bar_los5_change,
           MAX(rc10_bar_los6_change) as rc10_bar_los6_change,
           MAX(rc10_bar_los7_change) as rc10_bar_los7_change,
           MAX(rc10_bar_los8_change) as rc10_bar_los8_change,
           MAX(rc1_ratecode_los_all) as rc1_ratecode_los_all,
           MAX(rc2_ratecode_los_all) as rc2_ratecode_los_all,
           MAX(rc3_ratecode_los_all) as rc3_ratecode_los_all,
           MAX(rc4_ratecode_los_all) as rc4_ratecode_los_all,
           MAX(rc5_ratecode_los_all) as rc5_ratecode_los_all,
           MAX(rc6_ratecode_los_all) as rc6_ratecode_los_all,
           MAX(rc7_ratecode_los_all) as rc7_ratecode_los_all,
           MAX(rc8_ratecode_los_all) as rc8_ratecode_los_all,
           MAX(rc9_ratecode_los_all) as rc9_ratecode_los_all,
           MAX(rc10_ratecode_los_all) as rc10_ratecode_los_all,
           MAX(rc1_bar_by_day_change) as rc1_bar_by_day_change,
           MAX(rc2_bar_by_day_change) as rc2_bar_by_day_change,
           MAX(rc3_bar_by_day_change) as rc3_bar_by_day_change,
           MAX(rc4_bar_by_day_change) as rc4_bar_by_day_change,
           MAX(rc5_bar_by_day_change) as rc5_bar_by_day_change,
           MAX(rc6_bar_by_day_change) as rc6_bar_by_day_change,
           MAX(rc7_bar_by_day_change) as rc7_bar_by_day_change,
           MAX(rc8_bar_by_day_change) as rc8_bar_by_day_change,
           MAX(rc9_bar_by_day_change) as rc9_bar_by_day_change,
           MAX(rc10_bar_by_day_change) as rc10_bar_by_day_change,
           MAX(rc1_comp1_rate) as rc1_comp1_rate,
           MAX(rc1_comp2_rate) as rc1_comp2_rate,
           MAX(rc1_comp3_rate) as rc1_comp3_rate,
           MAX(rc1_comp4_rate) as rc1_comp4_rate,
           MAX(rc1_comp5_rate) as rc1_comp5_rate,
           MAX(rc1_comp6_rate) as rc1_comp6_rate,
           MAX(rc1_comp7_rate) as rc1_comp7_rate,
           MAX(rc1_comp8_rate) as rc1_comp8_rate,
           MAX(rc1_comp9_rate) as rc1_comp9_rate,
           MAX(rc1_comp10_rate) as rc1_comp10_rate,
           MAX(rc1_comp11_rate) as rc1_comp11_rate,
           MAX(rc1_comp12_rate) as rc1_comp12_rate,
           MAX(rc1_comp13_rate) as rc1_comp13_rate,
           MAX(rc1_comp14_rate) as rc1_comp14_rate,
           MAX(rc1_comp15_rate) as rc1_comp15_rate,
           MAX(rc2_comp1_rate) as rc2_comp1_rate,
           MAX(rc2_comp2_rate) as rc2_comp2_rate,
           MAX(rc2_comp3_rate) as rc2_comp3_rate,
           MAX(rc2_comp4_rate) as rc2_comp4_rate,
           MAX(rc2_comp5_rate) as rc2_comp5_rate,
           MAX(rc2_comp6_rate) as rc2_comp6_rate,
           MAX(rc2_comp7_rate) as rc2_comp7_rate,
           MAX(rc2_comp8_rate) as rc2_comp8_rate,
           MAX(rc2_comp9_rate) as rc2_comp9_rate,
           MAX(rc2_comp10_rate) as rc2_comp10_rate,
           MAX(rc2_comp11_rate) as rc2_comp11_rate,
           MAX(rc2_comp12_rate) as rc2_comp12_rate,
           MAX(rc2_comp13_rate) as rc2_comp13_rate,
           MAX(rc2_comp14_rate) as rc2_comp14_rate,
           MAX(rc2_comp15_rate) as rc2_comp15_rate,
           MAX(rc3_comp1_rate) as rc3_comp1_rate,
           MAX(rc3_comp2_rate) as rc3_comp2_rate,
           MAX(rc3_comp3_rate) as rc3_comp3_rate,
           MAX(rc3_comp4_rate) as rc3_comp4_rate,
           MAX(rc3_comp5_rate) as rc3_comp5_rate,
           MAX(rc3_comp6_rate) as rc3_comp6_rate,
           MAX(rc3_comp7_rate) as rc3_comp7_rate,
           MAX(rc3_comp8_rate) as rc3_comp8_rate,
           MAX(rc3_comp9_rate) as rc3_comp9_rate,
           MAX(rc3_comp10_rate) as rc3_comp10_rate,
           MAX(rc3_comp11_rate) as rc3_comp11_rate,
           MAX(rc3_comp12_rate) as rc3_comp12_rate,
           MAX(rc3_comp13_rate) as rc3_comp13_rate,
           MAX(rc3_comp14_rate) as rc3_comp14_rate,
           MAX(rc3_comp15_rate) as rc3_comp15_rate,
           MAX(rc4_comp1_rate) as rc4_comp1_rate,
           MAX(rc4_comp2_rate) as rc4_comp2_rate,
           MAX(rc4_comp3_rate) as rc4_comp3_rate,
           MAX(rc4_comp4_rate) as rc4_comp4_rate,
           MAX(rc4_comp5_rate) as rc4_comp5_rate,
           MAX(rc4_comp6_rate) as rc4_comp6_rate,
           MAX(rc4_comp7_rate) as rc4_comp7_rate,
           MAX(rc4_comp8_rate) as rc4_comp8_rate,
           MAX(rc4_comp9_rate) as rc4_comp9_rate,
           MAX(rc4_comp10_rate) as rc4_comp10_rate,
           MAX(rc4_comp11_rate) as rc4_comp11_rate,
           MAX(rc4_comp12_rate) as rc4_comp12_rate,
           MAX(rc4_comp13_rate) as rc4_comp13_rate,
           MAX(rc4_comp14_rate) as rc4_comp14_rate,
           MAX(rc4_comp15_rate) as rc4_comp15_rate,
           MAX(rc5_comp1_rate) as rc5_comp1_rate,
           MAX(rc5_comp2_rate) as rc5_comp2_rate,
           MAX(rc5_comp3_rate) as rc5_comp3_rate,
           MAX(rc5_comp4_rate) as rc5_comp4_rate,
           MAX(rc5_comp5_rate) as rc5_comp5_rate,
           MAX(rc5_comp6_rate) as rc5_comp6_rate,
           MAX(rc5_comp7_rate) as rc5_comp7_rate,
           MAX(rc5_comp8_rate) as rc5_comp8_rate,
           MAX(rc5_comp9_rate) as rc5_comp9_rate,
           MAX(rc5_comp10_rate) as rc5_comp10_rate,
           MAX(rc5_comp11_rate) as rc5_comp11_rate,
           MAX(rc5_comp12_rate) as rc5_comp12_rate,
           MAX(rc5_comp13_rate) as rc5_comp13_rate,
           MAX(rc5_comp14_rate) as rc5_comp14_rate,
           MAX(rc5_comp15_rate) as rc5_comp15_rate,
           MAX(rc6_comp1_rate) as rc6_comp1_rate,
           MAX(rc6_comp2_rate) as rc6_comp2_rate,
           MAX(rc6_comp3_rate) as rc6_comp3_rate,
           MAX(rc6_comp4_rate) as rc6_comp4_rate,
           MAX(rc6_comp5_rate) as rc6_comp5_rate,
           MAX(rc6_comp6_rate) as rc6_comp6_rate,
           MAX(rc6_comp7_rate) as rc6_comp7_rate,
           MAX(rc6_comp8_rate) as rc6_comp8_rate,
           MAX(rc6_comp9_rate) as rc6_comp9_rate,
           MAX(rc6_comp10_rate) as rc6_comp10_rate,
           MAX(rc6_comp11_rate) as rc6_comp11_rate,
           MAX(rc6_comp12_rate) as rc6_comp12_rate,
           MAX(rc6_comp13_rate) as rc6_comp13_rate,
           MAX(rc6_comp14_rate) as rc6_comp14_rate,
           MAX(rc6_comp15_rate) as rc6_comp15_rate,
           MAX(rc7_comp1_rate) as rc7_comp1_rate,
           MAX(rc7_comp2_rate) as rc7_comp2_rate,
           MAX(rc7_comp3_rate) as rc7_comp3_rate,
           MAX(rc7_comp4_rate) as rc7_comp4_rate,
           MAX(rc7_comp5_rate) as rc7_comp5_rate,
           MAX(rc7_comp6_rate) as rc7_comp6_rate,
           MAX(rc7_comp7_rate) as rc7_comp7_rate,
           MAX(rc7_comp8_rate) as rc7_comp8_rate,
           MAX(rc7_comp9_rate) as rc7_comp9_rate,
           MAX(rc7_comp10_rate) as rc7_comp10_rate,
           MAX(rc7_comp11_rate) as rc7_comp11_rate,
           MAX(rc7_comp12_rate) as rc7_comp12_rate,
           MAX(rc7_comp13_rate) as rc7_comp13_rate,
           MAX(rc7_comp14_rate) as rc7_comp14_rate,
           MAX(rc7_comp15_rate) as rc7_comp15_rate,
           MAX(rc8_comp1_rate) as rc8_comp1_rate,
           MAX(rc8_comp2_rate) as rc8_comp2_rate,
           MAX(rc8_comp3_rate) as rc8_comp3_rate,
           MAX(rc8_comp4_rate) as rc8_comp4_rate,
           MAX(rc8_comp5_rate) as rc8_comp5_rate,
           MAX(rc8_comp6_rate) as rc8_comp6_rate,
           MAX(rc8_comp7_rate) as rc8_comp7_rate,
           MAX(rc8_comp8_rate) as rc8_comp8_rate,
           MAX(rc8_comp9_rate) as rc8_comp9_rate,
           MAX(rc8_comp10_rate) as rc8_comp10_rate,
           MAX(rc8_comp11_rate) as rc8_comp11_rate,
           MAX(rc8_comp12_rate) as rc8_comp12_rate,
           MAX(rc8_comp13_rate) as rc8_comp13_rate,
           MAX(rc8_comp14_rate) as rc8_comp14_rate,
           MAX(rc8_comp15_rate) as rc8_comp15_rate,
           MAX(rc9_comp1_rate) as rc9_comp1_rate,
           MAX(rc9_comp2_rate) as rc9_comp2_rate,
           MAX(rc9_comp3_rate) as rc9_comp3_rate,
           MAX(rc9_comp4_rate) as rc9_comp4_rate,
           MAX(rc9_comp5_rate) as rc9_comp5_rate,
           MAX(rc9_comp6_rate) as rc9_comp6_rate,
           MAX(rc9_comp7_rate) as rc9_comp7_rate,
           MAX(rc9_comp8_rate) as rc9_comp8_rate,
           MAX(rc9_comp9_rate) as rc9_comp9_rate,
           MAX(rc9_comp10_rate) as rc9_comp10_rate,
           MAX(rc9_comp11_rate) as rc9_comp11_rate,
           MAX(rc9_comp12_rate) as rc9_comp12_rate,
           MAX(rc9_comp13_rate) as rc9_comp13_rate,
           MAX(rc9_comp14_rate) as rc9_comp14_rate,
           MAX(rc9_comp15_rate) as rc9_comp15_rate,
           MAX(rc10_comp1_rate) as rc10_comp1_rate,
           MAX(rc10_comp2_rate) as rc10_comp2_rate,
           MAX(rc10_comp3_rate) as rc10_comp3_rate,
           MAX(rc10_comp4_rate) as rc10_comp4_rate,
           MAX(rc10_comp5_rate) as rc10_comp5_rate,
           MAX(rc10_comp6_rate) as rc10_comp6_rate,
           MAX(rc10_comp7_rate) as rc10_comp7_rate,
           MAX(rc10_comp8_rate) as rc10_comp8_rate,
           MAX(rc10_comp9_rate) as rc10_comp9_rate,
           MAX(rc10_comp10_rate) as rc10_comp10_rate,
           MAX(rc10_comp11_rate) as rc10_comp11_rate,
           MAX(rc10_comp12_rate) as rc10_comp12_rate,
           MAX(rc10_comp13_rate) as rc10_comp13_rate,
           MAX(rc10_comp14_rate) as rc10_comp14_rate,
           MAX(rc10_comp15_rate) as rc10_comp15_rate,
           MAX(rc1_comp1_name) as rc1_comp1_name,
           MAX(rc1_comp2_name) as rc1_comp2_name,
           MAX(rc1_comp3_name) as rc1_comp3_name,
           MAX(rc1_comp4_name) as rc1_comp4_name,
           MAX(rc1_comp5_name) as rc1_comp5_name,
           MAX(rc1_comp6_name) as rc1_comp6_name,
           MAX(rc1_comp7_name) as rc1_comp7_name,
           MAX(rc1_comp8_name) as rc1_comp8_name,
           MAX(rc1_comp9_name) as rc1_comp9_name,
           MAX(rc1_comp10_name) as rc1_comp10_name,
           MAX(rc1_comp11_name) as rc1_comp11_name,
           MAX(rc1_comp12_name) as rc1_comp12_name,
           MAX(rc1_comp13_name) as rc1_comp13_name,
           MAX(rc1_comp14_name) as rc1_comp14_name,
           MAX(rc1_comp15_name) as rc1_comp15_name,
           MAX(rc2_comp1_name) as rc2_comp1_name,
           MAX(rc2_comp2_name) as rc2_comp2_name,
           MAX(rc2_comp3_name) as rc2_comp3_name,
           MAX(rc2_comp4_name) as rc2_comp4_name,
           MAX(rc2_comp5_name) as rc2_comp5_name,
           MAX(rc2_comp6_name) as rc2_comp6_name,
           MAX(rc2_comp7_name) as rc2_comp7_name,
           MAX(rc2_comp8_name) as rc2_comp8_name,
           MAX(rc2_comp9_name) as rc2_comp9_name,
           MAX(rc2_comp10_name) as rc2_comp10_name,
           MAX(rc2_comp11_name) as rc2_comp11_name,
           MAX(rc2_comp12_name) as rc2_comp12_name,
           MAX(rc2_comp13_name) as rc2_comp13_name,
           MAX(rc2_comp14_name) as rc2_comp14_name,
           MAX(rc2_comp15_name) as rc2_comp15_name,
           MAX(rc3_comp1_name) as rc3_comp1_name,
           MAX(rc3_comp2_name) as rc3_comp2_name,
           MAX(rc3_comp3_name) as rc3_comp3_name,
           MAX(rc3_comp4_name) as rc3_comp4_name,
           MAX(rc3_comp5_name) as rc3_comp5_name,
           MAX(rc3_comp6_name) as rc3_comp6_name,
           MAX(rc3_comp7_name) as rc3_comp7_name,
           MAX(rc3_comp8_name) as rc3_comp8_name,
           MAX(rc3_comp9_name) as rc3_comp9_name,
           MAX(rc3_comp10_name) as rc3_comp10_name,
           MAX(rc3_comp11_name) as rc3_comp11_name,
           MAX(rc3_comp12_name) as rc3_comp12_name,
           MAX(rc3_comp13_name) as rc3_comp13_name,
           MAX(rc3_comp14_name) as rc3_comp14_name,
           MAX(rc3_comp15_name) as rc3_comp15_name,
           MAX(rc4_comp1_name) as rc4_comp1_name,
           MAX(rc4_comp2_name) as rc4_comp2_name,
           MAX(rc4_comp3_name) as rc4_comp3_name,
           MAX(rc4_comp4_name) as rc4_comp4_name,
           MAX(rc4_comp5_name) as rc4_comp5_name,
           MAX(rc4_comp6_name) as rc4_comp6_name,
           MAX(rc4_comp7_name) as rc4_comp7_name,
           MAX(rc4_comp8_name) as rc4_comp8_name,
           MAX(rc4_comp9_name) as rc4_comp9_name,
           MAX(rc4_comp10_name) as rc4_comp10_name,
           MAX(rc4_comp11_name) as rc4_comp11_name,
           MAX(rc4_comp12_name) as rc4_comp12_name,
           MAX(rc4_comp13_name) as rc4_comp13_name,
           MAX(rc4_comp14_name) as rc4_comp14_name,
           MAX(rc4_comp15_name) as rc4_comp15_name,
           MAX(rc5_comp1_name) as rc5_comp1_name,
           MAX(rc5_comp2_name) as rc5_comp2_name,
           MAX(rc5_comp3_name) as rc5_comp3_name,
           MAX(rc5_comp4_name) as rc5_comp4_name,
           MAX(rc5_comp5_name) as rc5_comp5_name,
           MAX(rc5_comp6_name) as rc5_comp6_name,
           MAX(rc5_comp7_name) as rc5_comp7_name,
           MAX(rc5_comp8_name) as rc5_comp8_name,
           MAX(rc5_comp9_name) as rc5_comp9_name,
           MAX(rc5_comp10_name) as rc5_comp10_name,
           MAX(rc5_comp11_name) as rc5_comp11_name,
           MAX(rc5_comp12_name) as rc5_comp12_name,
           MAX(rc5_comp13_name) as rc5_comp13_name,
           MAX(rc5_comp14_name) as rc5_comp14_name,
           MAX(rc5_comp15_name) as rc5_comp15_name,
           MAX(rc6_comp1_name) as rc6_comp1_name,
           MAX(rc6_comp2_name) as rc6_comp2_name,
           MAX(rc6_comp3_name) as rc6_comp3_name,
           MAX(rc6_comp4_name) as rc6_comp4_name,
           MAX(rc6_comp5_name) as rc6_comp5_name,
           MAX(rc6_comp6_name) as rc6_comp6_name,
           MAX(rc6_comp7_name) as rc6_comp7_name,
           MAX(rc6_comp8_name) as rc6_comp8_name,
           MAX(rc6_comp9_name) as rc6_comp9_name,
           MAX(rc6_comp10_name) as rc6_comp10_name,
           MAX(rc6_comp11_name) as rc6_comp11_name,
           MAX(rc6_comp12_name) as rc6_comp12_name,
           MAX(rc6_comp13_name) as rc6_comp13_name,
           MAX(rc6_comp14_name) as rc6_comp14_name,
           MAX(rc6_comp15_name) as rc6_comp15_name,
           MAX(rc7_comp1_name) as rc7_comp1_name,
           MAX(rc7_comp2_name) as rc7_comp2_name,
           MAX(rc7_comp3_name) as rc7_comp3_name,
           MAX(rc7_comp4_name) as rc7_comp4_name,
           MAX(rc7_comp5_name) as rc7_comp5_name,
           MAX(rc7_comp6_name) as rc7_comp6_name,
           MAX(rc7_comp7_name) as rc7_comp7_name,
           MAX(rc7_comp8_name) as rc7_comp8_name,
           MAX(rc7_comp9_name) as rc7_comp9_name,
           MAX(rc7_comp10_name) as rc7_comp10_name,
           MAX(rc7_comp11_name) as rc7_comp11_name,
           MAX(rc7_comp12_name) as rc7_comp12_name,
           MAX(rc7_comp13_name) as rc7_comp13_name,
           MAX(rc7_comp14_name) as rc7_comp14_name,
           MAX(rc7_comp15_name) as rc7_comp15_name,
           MAX(rc8_comp1_name) as rc8_comp1_name,
           MAX(rc8_comp2_name) as rc8_comp2_name,
           MAX(rc8_comp3_name) as rc8_comp3_name,
           MAX(rc8_comp4_name) as rc8_comp4_name,
           MAX(rc8_comp5_name) as rc8_comp5_name,
           MAX(rc8_comp6_name) as rc8_comp6_name,
           MAX(rc8_comp7_name) as rc8_comp7_name,
           MAX(rc8_comp8_name) as rc8_comp8_name,
           MAX(rc8_comp9_name) as rc8_comp9_name,
           MAX(rc8_comp10_name) as rc8_comp10_name,
           MAX(rc8_comp11_name) as rc8_comp11_name,
           MAX(rc8_comp12_name) as rc8_comp12_name,
           MAX(rc8_comp13_name) as rc8_comp13_name,
           MAX(rc8_comp14_name) as rc8_comp14_name,
           MAX(rc8_comp15_name) as rc8_comp15_name,
           MAX(rc9_comp1_name) as rc9_comp1_name,
           MAX(rc9_comp2_name) as rc9_comp2_name,
           MAX(rc9_comp3_name) as rc9_comp3_name,
           MAX(rc9_comp4_name) as rc9_comp4_name,
           MAX(rc9_comp5_name) as rc9_comp5_name,
           MAX(rc9_comp6_name) as rc9_comp6_name,
           MAX(rc9_comp7_name) as rc9_comp7_name,
           MAX(rc9_comp8_name) as rc9_comp8_name,
           MAX(rc9_comp9_name) as rc9_comp9_name,
           MAX(rc9_comp10_name) as rc9_comp10_name,
           MAX(rc9_comp11_name) as rc9_comp11_name,
           MAX(rc9_comp12_name) as rc9_comp12_name,
           MAX(rc9_comp13_name) as rc9_comp13_name,
           MAX(rc9_comp14_name) as rc9_comp14_name,
           MAX(rc9_comp15_name) as rc9_comp15_name,
           MAX(rc10_comp1_name) as rc10_comp1_name,
           MAX(rc10_comp2_name) as rc10_comp2_name,
           MAX(rc10_comp3_name) as rc10_comp3_name,
           MAX(rc10_comp4_name) as rc10_comp4_name,
           MAX(rc10_comp5_name) as rc10_comp5_name,
           MAX(rc10_comp6_name) as rc10_comp6_name,
           MAX(rc10_comp7_name) as rc10_comp7_name,
           MAX(rc10_comp8_name) as rc10_comp8_name,
           MAX(rc10_comp9_name) as rc10_comp9_name,
           MAX(rc10_comp10_name) as rc10_comp10_name,
           MAX(rc10_comp11_name) as rc10_comp11_name,
           MAX(rc10_comp12_name) as rc10_comp12_name,
           MAX(rc10_comp13_name) as rc10_comp13_name,
           MAX(rc10_comp14_name) as rc10_comp14_name,
           MAX(rc10_comp15_name) as rc10_comp15_name,
           MAX(rc1_comp1_change) as rc1_comp1_change,
           MAX(rc1_comp2_change) as rc1_comp2_change,
           MAX(rc1_comp3_change) as rc1_comp3_change,
           MAX(rc1_comp4_change) as rc1_comp4_change,
           MAX(rc1_comp5_change) as rc1_comp5_change,
           MAX(rc1_comp6_change) as rc1_comp6_change,
           MAX(rc1_comp7_change) as rc1_comp7_change,
           MAX(rc1_comp8_change) as rc1_comp8_change,
           MAX(rc1_comp9_change) as rc1_comp9_change,
           MAX(rc1_comp10_change) as rc1_comp10_change,
           MAX(rc1_comp11_change) as rc1_comp11_change,
           MAX(rc1_comp12_change) as rc1_comp12_change,
           MAX(rc1_comp13_change) as rc1_comp13_change,
           MAX(rc1_comp14_change) as rc1_comp14_change,
           MAX(rc1_comp15_change) as rc1_comp15_change,
           MAX(rc2_comp1_change) as rc2_comp1_change,
           MAX(rc2_comp2_change) as rc2_comp2_change,
           MAX(rc2_comp3_change) as rc2_comp3_change,
           MAX(rc2_comp4_change) as rc2_comp4_change,
           MAX(rc2_comp5_change) as rc2_comp5_change,
           MAX(rc2_comp6_change) as rc2_comp6_change,
           MAX(rc2_comp7_change) as rc2_comp7_change,
           MAX(rc2_comp8_change) as rc2_comp8_change,
           MAX(rc2_comp9_change) as rc2_comp9_change,
           MAX(rc2_comp10_change) as rc2_comp10_change,
           MAX(rc2_comp11_change) as rc2_comp11_change,
           MAX(rc2_comp12_change) as rc2_comp12_change,
           MAX(rc2_comp13_change) as rc2_comp13_change,
           MAX(rc2_comp14_change) as rc2_comp14_change,
           MAX(rc2_comp15_change) as rc2_comp15_change,
           MAX(rc3_comp1_change) as rc3_comp1_change,
           MAX(rc3_comp2_change) as rc3_comp2_change,
           MAX(rc3_comp3_change) as rc3_comp3_change,
           MAX(rc3_comp4_change) as rc3_comp4_change,
           MAX(rc3_comp5_change) as rc3_comp5_change,
           MAX(rc3_comp6_change) as rc3_comp6_change,
           MAX(rc3_comp7_change) as rc3_comp7_change,
           MAX(rc3_comp8_change) as rc3_comp8_change,
           MAX(rc3_comp9_change) as rc3_comp9_change,
           MAX(rc3_comp10_change) as rc3_comp10_change,
           MAX(rc3_comp11_change) as rc3_comp11_change,
           MAX(rc3_comp12_change) as rc3_comp12_change,
           MAX(rc3_comp13_change) as rc3_comp13_change,
           MAX(rc3_comp14_change) as rc3_comp14_change,
           MAX(rc3_comp15_change) as rc3_comp15_change,
           MAX(rc4_comp1_change) as rc4_comp1_change,
           MAX(rc4_comp2_change) as rc4_comp2_change,
           MAX(rc4_comp3_change) as rc4_comp3_change,
           MAX(rc4_comp4_change) as rc4_comp4_change,
           MAX(rc4_comp5_change) as rc4_comp5_change,
           MAX(rc4_comp6_change) as rc4_comp6_change,
           MAX(rc4_comp7_change) as rc4_comp7_change,
           MAX(rc4_comp8_change) as rc4_comp8_change,
           MAX(rc4_comp9_change) as rc4_comp9_change,
           MAX(rc4_comp10_change) as rc4_comp10_change,
           MAX(rc4_comp11_change) as rc4_comp11_change,
           MAX(rc4_comp12_change) as rc4_comp12_change,
           MAX(rc4_comp13_change) as rc4_comp13_change,
           MAX(rc4_comp14_change) as rc4_comp14_change,
           MAX(rc4_comp15_change) as rc4_comp15_change,
           MAX(rc5_comp1_change) as rc5_comp1_change,
           MAX(rc5_comp2_change) as rc5_comp2_change,
           MAX(rc5_comp3_change) as rc5_comp3_change,
           MAX(rc5_comp4_change) as rc5_comp4_change,
           MAX(rc5_comp5_change) as rc5_comp5_change,
           MAX(rc5_comp6_change) as rc5_comp6_change,
           MAX(rc5_comp7_change) as rc5_comp7_change,
           MAX(rc5_comp8_change) as rc5_comp8_change,
           MAX(rc5_comp9_change) as rc5_comp9_change,
           MAX(rc5_comp10_change) as rc5_comp10_change,
           MAX(rc5_comp11_change) as rc5_comp11_change,
           MAX(rc5_comp12_change) as rc5_comp12_change,
           MAX(rc5_comp13_change) as rc5_comp13_change,
           MAX(rc5_comp14_change) as rc5_comp14_change,
           MAX(rc5_comp15_change) as rc5_comp15_change,
           MAX(rc6_comp1_change) as rc6_comp1_change,
           MAX(rc6_comp2_change) as rc6_comp2_change,
           MAX(rc6_comp3_change) as rc6_comp3_change,
           MAX(rc6_comp4_change) as rc6_comp4_change,
           MAX(rc6_comp5_change) as rc6_comp5_change,
           MAX(rc6_comp6_change) as rc6_comp6_change,
           MAX(rc6_comp7_change) as rc6_comp7_change,
           MAX(rc6_comp8_change) as rc6_comp8_change,
           MAX(rc6_comp9_change) as rc6_comp9_change,
           MAX(rc6_comp10_change) as rc6_comp10_change,
           MAX(rc6_comp11_change) as rc6_comp11_change,
           MAX(rc6_comp12_change) as rc6_comp12_change,
           MAX(rc6_comp13_change) as rc6_comp13_change,
           MAX(rc6_comp14_change) as rc6_comp14_change,
           MAX(rc6_comp15_change) as rc6_comp15_change,
           MAX(rc7_comp1_change) as rc7_comp1_change,
           MAX(rc7_comp2_change) as rc7_comp2_change,
           MAX(rc7_comp3_change) as rc7_comp3_change,
           MAX(rc7_comp4_change) as rc7_comp4_change,
           MAX(rc7_comp5_change) as rc7_comp5_change,
           MAX(rc7_comp6_change) as rc7_comp6_change,
           MAX(rc7_comp7_change) as rc7_comp7_change,
           MAX(rc7_comp8_change) as rc7_comp8_change,
           MAX(rc7_comp9_change) as rc7_comp9_change,
           MAX(rc7_comp10_change) as rc7_comp10_change,
           MAX(rc7_comp11_change) as rc7_comp11_change,
           MAX(rc7_comp12_change) as rc7_comp12_change,
           MAX(rc7_comp13_change) as rc7_comp13_change,
           MAX(rc7_comp14_change) as rc7_comp14_change,
           MAX(rc7_comp15_change) as rc7_comp15_change,
           MAX(rc8_comp1_change) as rc8_comp1_change,
           MAX(rc8_comp2_change) as rc8_comp2_change,
           MAX(rc8_comp3_change) as rc8_comp3_change,
           MAX(rc8_comp4_change) as rc8_comp4_change,
           MAX(rc8_comp5_change) as rc8_comp5_change,
           MAX(rc8_comp6_change) as rc8_comp6_change,
           MAX(rc8_comp7_change) as rc8_comp7_change,
           MAX(rc8_comp8_change) as rc8_comp8_change,
           MAX(rc8_comp9_change) as rc8_comp9_change,
           MAX(rc8_comp10_change) as rc8_comp10_change,
           MAX(rc8_comp11_change) as rc8_comp11_change,
           MAX(rc8_comp12_change) as rc8_comp12_change,
           MAX(rc8_comp13_change) as rc8_comp13_change,
           MAX(rc8_comp14_change) as rc8_comp14_change,
           MAX(rc8_comp15_change) as rc8_comp15_change,
           MAX(rc9_comp1_change) as rc9_comp1_change,
           MAX(rc9_comp2_change) as rc9_comp2_change,
           MAX(rc9_comp3_change) as rc9_comp3_change,
           MAX(rc9_comp4_change) as rc9_comp4_change,
           MAX(rc9_comp5_change) as rc9_comp5_change,
           MAX(rc9_comp6_change) as rc9_comp6_change,
           MAX(rc9_comp7_change) as rc9_comp7_change,
           MAX(rc9_comp8_change) as rc9_comp8_change,
           MAX(rc9_comp9_change) as rc9_comp9_change,
           MAX(rc9_comp10_change) as rc9_comp10_change,
           MAX(rc9_comp11_change) as rc9_comp11_change,
           MAX(rc9_comp12_change) as rc9_comp12_change,
           MAX(rc9_comp13_change) as rc9_comp13_change,
           MAX(rc9_comp14_change) as rc9_comp14_change,
           MAX(rc9_comp15_change) as rc9_comp15_change,
           MAX(rc10_comp1_change) as rc10_comp1_change,
           MAX(rc10_comp2_change) as rc10_comp2_change,
           MAX(rc10_comp3_change) as rc10_comp3_change,
           MAX(rc10_comp4_change) as rc10_comp4_change,
           MAX(rc10_comp5_change) as rc10_comp5_change,
           MAX(rc10_comp6_change) as rc10_comp6_change,
           MAX(rc10_comp7_change) as rc10_comp7_change,
           MAX(rc10_comp8_change) as rc10_comp8_change,
           MAX(rc10_comp9_change) as rc10_comp9_change,
           MAX(rc10_comp10_change) as rc10_comp10_change,
           MAX(rc10_comp11_change) as rc10_comp11_change,
           MAX(rc10_comp12_change) as rc10_comp12_change,
           MAX(rc10_comp13_change) as rc10_comp13_change,
           MAX(rc10_comp14_change) as rc10_comp14_change,
           MAX(rc10_comp15_change) as rc10_comp15_change,
           MAX(rc1_block) as rc1_block,
           MAX(rc1_block_available) as rc1_block_available,
           MAX(rc1_block_pickup) as rc1_block_pickup,
           MAX(rc2_block) as rc2_block,
           MAX(rc2_block_available) as rc2_block_available,
           MAX(rc2_block_pickup) as rc2_block_pickup,
           MAX(rc3_block) as rc3_block,
           MAX(rc3_block_available) as rc3_block_available,
           MAX(rc3_block_pickup) as rc3_block_pickup,
           MAX(rc4_block) as rc4_block,
           MAX(rc4_block_available) as rc4_block_available,
           MAX(rc4_block_pickup) as rc4_block_pickup,
           MAX(rc5_block) as rc5_block,
           MAX(rc5_block_available) as rc5_block_available,
           MAX(rc5_block_pickup) as rc5_block_pickup,
           MAX(rc6_block) as rc6_block,
           MAX(rc6_block_available) as rc6_block_available,
           MAX(rc6_block_pickup) as rc6_block_pickup,
           MAX(rc7_block) as rc7_block,
           MAX(rc7_block_available) as rc7_block_available,
           MAX(rc7_block_pickup) as rc7_block_pickup,
           MAX(rc8_block) as rc8_block,
           MAX(rc8_block_available) as rc8_block_available,
           MAX(rc8_block_pickup) as rc8_block_pickup,
           MAX(rc9_block) as rc9_block,
           MAX(rc9_block_available) as rc9_block_available,
           MAX(rc9_block_pickup) as rc9_block_pickup,
           MAX(rc10_block) as rc10_block,
           MAX(rc10_block_available) as rc10_block_available,
           MAX(rc10_block_pickup) as rc10_block_pickup,
           MAX(rc1_OccupancyForecastPerCurrent_without_ooo) as rc1_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc1_occupancyforecastperchange_without_ooo) as rc1_occupancyforecastperchange_without_ooo,
           MAX(rc2_OccupancyForecastPerCurrent_without_ooo) as rc2_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc2_occupancyforecastperchange_without_ooo) as rc2_occupancyforecastperchange_without_ooo,
           MAX(rc3_OccupancyForecastPerCurrent_without_ooo) as rc3_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc3_occupancyforecastperchange_without_ooo) as rc3_occupancyforecastperchange_without_ooo,
           MAX(rc4_OccupancyForecastPerCurrent_without_ooo) as rc4_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc4_occupancyforecastperchange_without_ooo) as rc4_occupancyforecastperchange_without_ooo,
           MAX(rc5_OccupancyForecastPerCurrent_without_ooo) as rc5_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc5_occupancyforecastperchange_without_ooo) as rc5_occupancyforecastperchange_without_ooo,
           MAX(rc6_OccupancyForecastPerCurrent_without_ooo) as rc6_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc6_occupancyforecastperchange_without_ooo) as rc6_occupancyforecastperchange_without_ooo,
           MAX(rc7_OccupancyForecastPerCurrent_without_ooo) as rc7_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc7_occupancyforecastperchange_without_ooo) as rc7_occupancyforecastperchange_without_ooo,
           MAX(rc8_OccupancyForecastPerCurrent_without_ooo) as rc8_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc8_occupancyforecastperchange_without_ooo) as rc8_occupancyforecastperchange_without_ooo,
           MAX(rc9_OccupancyForecastPerCurrent_without_ooo) as rc9_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc9_occupancyforecastperchange_without_ooo) as rc9_occupancyforecastperchange_without_ooo,
           MAX(rc10_OccupancyForecastPerCurrent_without_ooo) as rc10_OccupancyForecastPerCurrent_without_ooo,
           MAX(rc10_occupancyforecastperchange_without_ooo) as rc10_occupancyforecastperchange_without_ooo,
           MAX(rc1_decisionreasontypecurrent) as rc1_decisionreasontypecurrent,
           MAX(rc1_decisionreasontypechange) as rc1_decisionreasontypechange,
           MAX(rc2_decisionreasontypecurrent) as rc2_decisionreasontypecurrent,
           MAX(rc2_decisionreasontypechange) as rc2_decisionreasontypechange,
           MAX(rc3_decisionreasontypecurrent) as rc3_decisionreasontypecurrent,
           MAX(rc3_decisionreasontypechange) as rc3_decisionreasontypechange,
           MAX(rc4_decisionreasontypecurrent) as rc4_decisionreasontypecurrent,
           MAX(rc4_decisionreasontypechange) as rc4_decisionreasontypechange,
           MAX(rc5_decisionreasontypecurrent) as rc5_decisionreasontypecurrent,
           MAX(rc5_decisionreasontypechange) as rc5_decisionreasontypechange,
           MAX(rc6_decisionreasontypecurrent) as rc6_decisionreasontypecurrent,
           MAX(rc6_decisionreasontypechange) as rc6_decisionreasontypechange,
           MAX(rc7_decisionreasontypecurrent) as rc7_decisionreasontypecurrent,
           MAX(rc7_decisionreasontypechange) as rc7_decisionreasontypechange,
           MAX(rc8_decisionreasontypecurrent) as rc8_decisionreasontypecurrent,
           MAX(rc8_decisionreasontypechange) as rc8_decisionreasontypechange,
           MAX(rc9_decisionreasontypecurrent) as rc9_decisionreasontypecurrent,
           MAX(rc9_decisionreasontypechange) as rc9_decisionreasontypechange,
           MAX(rc10_decisionreasontypecurrent) as rc10_decisionreasontypecurrent,
           MAX(rc10_decisionreasontypechange) as rc10_decisionreasontypechange,
           MAX(rc1_decisionreasontypecurrent_los1) as rc1_decisionreasontypecurrent_los1,
           MAX(rc1_decisionreasontypecurrent_los2) as rc1_decisionreasontypecurrent_los2,
           MAX(rc1_decisionreasontypecurrent_los3) as rc1_decisionreasontypecurrent_los3,
           MAX(rc1_decisionreasontypecurrent_los4) as rc1_decisionreasontypecurrent_los4,
           MAX(rc1_decisionreasontypecurrent_los5) as rc1_decisionreasontypecurrent_los5,
           MAX(rc1_decisionreasontypecurrent_los6) as rc1_decisionreasontypecurrent_los6,
           MAX(rc1_decisionreasontypecurrent_los7) as rc1_decisionreasontypecurrent_los7,
           MAX(rc1_decisionreasontypecurrent_los8) as rc1_decisionreasontypecurrent_los8,
           MAX(rc2_decisionreasontypecurrent_los1) as rc2_decisionreasontypecurrent_los1,
           MAX(rc2_decisionreasontypecurrent_los2) as rc2_decisionreasontypecurrent_los2,
           MAX(rc2_decisionreasontypecurrent_los3) as rc2_decisionreasontypecurrent_los3,
           MAX(rc2_decisionreasontypecurrent_los4) as rc2_decisionreasontypecurrent_los4,
           MAX(rc2_decisionreasontypecurrent_los5) as rc2_decisionreasontypecurrent_los5,
           MAX(rc2_decisionreasontypecurrent_los6) as rc2_decisionreasontypecurrent_los6,
           MAX(rc2_decisionreasontypecurrent_los7) as rc2_decisionreasontypecurrent_los7,
           MAX(rc2_decisionreasontypecurrent_los8) as rc2_decisionreasontypecurrent_los8,
           MAX(rc3_decisionreasontypecurrent_los1) as rc3_decisionreasontypecurrent_los1,
           MAX(rc3_decisionreasontypecurrent_los2) as rc3_decisionreasontypecurrent_los2,
           MAX(rc3_decisionreasontypecurrent_los3) as rc3_decisionreasontypecurrent_los3,
           MAX(rc3_decisionreasontypecurrent_los4) as rc3_decisionreasontypecurrent_los4,
           MAX(rc3_decisionreasontypecurrent_los5) as rc3_decisionreasontypecurrent_los5,
           MAX(rc3_decisionreasontypecurrent_los6) as rc3_decisionreasontypecurrent_los6,
           MAX(rc3_decisionreasontypecurrent_los7) as rc3_decisionreasontypecurrent_los7,
           MAX(rc3_decisionreasontypecurrent_los8) as rc3_decisionreasontypecurrent_los8,
           MAX(rc4_decisionreasontypecurrent_los1) as rc4_decisionreasontypecurrent_los1,
           MAX(rc4_decisionreasontypecurrent_los2) as rc4_decisionreasontypecurrent_los2,
           MAX(rc4_decisionreasontypecurrent_los3) as rc4_decisionreasontypecurrent_los3,
           MAX(rc4_decisionreasontypecurrent_los4) as rc4_decisionreasontypecurrent_los4,
           MAX(rc4_decisionreasontypecurrent_los5) as rc4_decisionreasontypecurrent_los5,
           MAX(rc4_decisionreasontypecurrent_los6) as rc4_decisionreasontypecurrent_los6,
           MAX(rc4_decisionreasontypecurrent_los7) as rc4_decisionreasontypecurrent_los7,
           MAX(rc4_decisionreasontypecurrent_los8) as rc4_decisionreasontypecurrent_los8,
           MAX(rc5_decisionreasontypecurrent_los1) as rc5_decisionreasontypecurrent_los1,
           MAX(rc5_decisionreasontypecurrent_los2) as rc5_decisionreasontypecurrent_los2,
           MAX(rc5_decisionreasontypecurrent_los3) as rc5_decisionreasontypecurrent_los3,
           MAX(rc5_decisionreasontypecurrent_los4) as rc5_decisionreasontypecurrent_los4,
           MAX(rc5_decisionreasontypecurrent_los5) as rc5_decisionreasontypecurrent_los5,
           MAX(rc5_decisionreasontypecurrent_los6) as rc5_decisionreasontypecurrent_los6,
           MAX(rc5_decisionreasontypecurrent_los7) as rc5_decisionreasontypecurrent_los7,
           MAX(rc5_decisionreasontypecurrent_los8) as rc5_decisionreasontypecurrent_los8,
           MAX(rc6_decisionreasontypecurrent_los1) as rc6_decisionreasontypecurrent_los1,
           MAX(rc6_decisionreasontypecurrent_los2) as rc6_decisionreasontypecurrent_los2,
           MAX(rc6_decisionreasontypecurrent_los3) as rc6_decisionreasontypecurrent_los3,
           MAX(rc6_decisionreasontypecurrent_los4) as rc6_decisionreasontypecurrent_los4,
           MAX(rc6_decisionreasontypecurrent_los5) as rc6_decisionreasontypecurrent_los5,
           MAX(rc6_decisionreasontypecurrent_los6) as rc6_decisionreasontypecurrent_los6,
           MAX(rc6_decisionreasontypecurrent_los7) as rc6_decisionreasontypecurrent_los7,
           MAX(rc6_decisionreasontypecurrent_los8) as rc6_decisionreasontypecurrent_los8,
           MAX(rc7_decisionreasontypecurrent_los1) as rc7_decisionreasontypecurrent_los1,
           MAX(rc7_decisionreasontypecurrent_los2) as rc7_decisionreasontypecurrent_los2,
           MAX(rc7_decisionreasontypecurrent_los3) as rc7_decisionreasontypecurrent_los3,
           MAX(rc7_decisionreasontypecurrent_los4) as rc7_decisionreasontypecurrent_los4,
           MAX(rc7_decisionreasontypecurrent_los5) as rc7_decisionreasontypecurrent_los5,
           MAX(rc7_decisionreasontypecurrent_los6) as rc7_decisionreasontypecurrent_los6,
           MAX(rc7_decisionreasontypecurrent_los7) as rc7_decisionreasontypecurrent_los7,
           MAX(rc7_decisionreasontypecurrent_los8) as rc7_decisionreasontypecurrent_los8,
           MAX(rc8_decisionreasontypecurrent_los1) as rc8_decisionreasontypecurrent_los1,
           MAX(rc8_decisionreasontypecurrent_los2) as rc8_decisionreasontypecurrent_los2,
           MAX(rc8_decisionreasontypecurrent_los3) as rc8_decisionreasontypecurrent_los3,
           MAX(rc8_decisionreasontypecurrent_los4) as rc8_decisionreasontypecurrent_los4,
           MAX(rc8_decisionreasontypecurrent_los5) as rc8_decisionreasontypecurrent_los5,
           MAX(rc8_decisionreasontypecurrent_los6) as rc8_decisionreasontypecurrent_los6,
           MAX(rc8_decisionreasontypecurrent_los7) as rc8_decisionreasontypecurrent_los7,
           MAX(rc8_decisionreasontypecurrent_los8) as rc8_decisionreasontypecurrent_los8,
           MAX(rc9_decisionreasontypecurrent_los1) as rc9_decisionreasontypecurrent_los1,
           MAX(rc9_decisionreasontypecurrent_los2) as rc9_decisionreasontypecurrent_los2,
           MAX(rc9_decisionreasontypecurrent_los3) as rc9_decisionreasontypecurrent_los3,
           MAX(rc9_decisionreasontypecurrent_los4) as rc9_decisionreasontypecurrent_los4,
           MAX(rc9_decisionreasontypecurrent_los5) as rc9_decisionreasontypecurrent_los5,
           MAX(rc9_decisionreasontypecurrent_los6) as rc9_decisionreasontypecurrent_los6,
           MAX(rc9_decisionreasontypecurrent_los7) as rc9_decisionreasontypecurrent_los7,
           MAX(rc9_decisionreasontypecurrent_los8) as rc9_decisionreasontypecurrent_los8,
           MAX(rc10_decisionreasontypecurrent_los1) as rc10_decisionreasontypecurrent_los1,
           MAX(rc10_decisionreasontypecurrent_los2) as rc10_decisionreasontypecurrent_los2,
           MAX(rc10_decisionreasontypecurrent_los3) as rc10_decisionreasontypecurrent_los3,
           MAX(rc10_decisionreasontypecurrent_los4) as rc10_decisionreasontypecurrent_los4,
           MAX(rc10_decisionreasontypecurrent_los5) as rc10_decisionreasontypecurrent_los5,
           MAX(rc10_decisionreasontypecurrent_los6) as rc10_decisionreasontypecurrent_los6,
           MAX(rc10_decisionreasontypecurrent_los7) as rc10_decisionreasontypecurrent_los7,
           MAX(rc10_decisionreasontypecurrent_los8) as rc10_decisionreasontypecurrent_los8,
           MAX(rc1_decisionreasontypechange_los1) as rc1_decisionreasontypechange_los1,
           MAX(rc1_decisionreasontypechange_los2) as rc1_decisionreasontypechange_los2,
           MAX(rc1_decisionreasontypechange_los3) as rc1_decisionreasontypechange_los3,
           MAX(rc1_decisionreasontypechange_los4) as rc1_decisionreasontypechange_los4,
           MAX(rc1_decisionreasontypechange_los5) as rc1_decisionreasontypechange_los5,
           MAX(rc1_decisionreasontypechange_los6) as rc1_decisionreasontypechange_los6,
           MAX(rc1_decisionreasontypechange_los7) as rc1_decisionreasontypechange_los7,
           MAX(rc1_decisionreasontypechange_los8) as rc1_decisionreasontypechange_los8,
           MAX(rc2_decisionreasontypechange_los1) as rc2_decisionreasontypechange_los1,
           MAX(rc2_decisionreasontypechange_los2) as rc2_decisionreasontypechange_los2,
           MAX(rc2_decisionreasontypechange_los3) as rc2_decisionreasontypechange_los3,
           MAX(rc2_decisionreasontypechange_los4) as rc2_decisionreasontypechange_los4,
           MAX(rc2_decisionreasontypechange_los5) as rc2_decisionreasontypechange_los5,
           MAX(rc2_decisionreasontypechange_los6) as rc2_decisionreasontypechange_los6,
           MAX(rc2_decisionreasontypechange_los7) as rc2_decisionreasontypechange_los7,
           MAX(rc2_decisionreasontypechange_los8) as rc2_decisionreasontypechange_los8,
           MAX(rc3_decisionreasontypechange_los1) as rc3_decisionreasontypechange_los1,
           MAX(rc3_decisionreasontypechange_los2) as rc3_decisionreasontypechange_los2,
           MAX(rc3_decisionreasontypechange_los3) as rc3_decisionreasontypechange_los3,
           MAX(rc3_decisionreasontypechange_los4) as rc3_decisionreasontypechange_los4,
           MAX(rc3_decisionreasontypechange_los5) as rc3_decisionreasontypechange_los5,
           MAX(rc3_decisionreasontypechange_los6) as rc3_decisionreasontypechange_los6,
           MAX(rc3_decisionreasontypechange_los7) as rc3_decisionreasontypechange_los7,
           MAX(rc3_decisionreasontypechange_los8) as rc3_decisionreasontypechange_los8,
           MAX(rc4_decisionreasontypechange_los1) as rc4_decisionreasontypechange_los1,
           MAX(rc4_decisionreasontypechange_los2) as rc4_decisionreasontypechange_los2,
           MAX(rc4_decisionreasontypechange_los3) as rc4_decisionreasontypechange_los3,
           MAX(rc4_decisionreasontypechange_los4) as rc4_decisionreasontypechange_los4,
           MAX(rc4_decisionreasontypechange_los5) as rc4_decisionreasontypechange_los5,
           MAX(rc4_decisionreasontypechange_los6) as rc4_decisionreasontypechange_los6,
           MAX(rc4_decisionreasontypechange_los7) as rc4_decisionreasontypechange_los7,
           MAX(rc4_decisionreasontypechange_los8) as rc4_decisionreasontypechange_los8,
           MAX(rc5_decisionreasontypechange_los1) as rc5_decisionreasontypechange_los1,
           MAX(rc5_decisionreasontypechange_los2) as rc5_decisionreasontypechange_los2,
           MAX(rc5_decisionreasontypechange_los3) as rc5_decisionreasontypechange_los3,
           MAX(rc5_decisionreasontypechange_los4) as rc5_decisionreasontypechange_los4,
           MAX(rc5_decisionreasontypechange_los5) as rc5_decisionreasontypechange_los5,
           MAX(rc5_decisionreasontypechange_los6) as rc5_decisionreasontypechange_los6,
           MAX(rc5_decisionreasontypechange_los7) as rc5_decisionreasontypechange_los7,
           MAX(rc5_decisionreasontypechange_los8) as rc5_decisionreasontypechange_los8,
           MAX(rc6_decisionreasontypechange_los1) as rc6_decisionreasontypechange_los1,
           MAX(rc6_decisionreasontypechange_los2) as rc6_decisionreasontypechange_los2,
           MAX(rc6_decisionreasontypechange_los3) as rc6_decisionreasontypechange_los3,
           MAX(rc6_decisionreasontypechange_los4) as rc6_decisionreasontypechange_los4,
           MAX(rc6_decisionreasontypechange_los5) as rc6_decisionreasontypechange_los5,
           MAX(rc6_decisionreasontypechange_los6) as rc6_decisionreasontypechange_los6,
           MAX(rc6_decisionreasontypechange_los7) as rc6_decisionreasontypechange_los7,
           MAX(rc6_decisionreasontypechange_los8) as rc6_decisionreasontypechange_los8,
           MAX(rc7_decisionreasontypechange_los1) as rc7_decisionreasontypechange_los1,
           MAX(rc7_decisionreasontypechange_los2) as rc7_decisionreasontypechange_los2,
           MAX(rc7_decisionreasontypechange_los3) as rc7_decisionreasontypechange_los3,
           MAX(rc7_decisionreasontypechange_los4) as rc7_decisionreasontypechange_los4,
           MAX(rc7_decisionreasontypechange_los5) as rc7_decisionreasontypechange_los5,
           MAX(rc7_decisionreasontypechange_los6) as rc7_decisionreasontypechange_los6,
           MAX(rc7_decisionreasontypechange_los7) as rc7_decisionreasontypechange_los7,
           MAX(rc7_decisionreasontypechange_los8) as rc7_decisionreasontypechange_los8,
           MAX(rc8_decisionreasontypechange_los1) as rc8_decisionreasontypechange_los1,
           MAX(rc8_decisionreasontypechange_los2) as rc8_decisionreasontypechange_los2,
           MAX(rc8_decisionreasontypechange_los3) as rc8_decisionreasontypechange_los3,
           MAX(rc8_decisionreasontypechange_los4) as rc8_decisionreasontypechange_los4,
           MAX(rc8_decisionreasontypechange_los5) as rc8_decisionreasontypechange_los5,
           MAX(rc8_decisionreasontypechange_los6) as rc8_decisionreasontypechange_los6,
           MAX(rc8_decisionreasontypechange_los7) as rc8_decisionreasontypechange_los7,
           MAX(rc8_decisionreasontypechange_los8) as rc8_decisionreasontypechange_los8,
           MAX(rc9_decisionreasontypechange_los1) as rc9_decisionreasontypechange_los1,
           MAX(rc9_decisionreasontypechange_los2) as rc9_decisionreasontypechange_los2,
           MAX(rc9_decisionreasontypechange_los3) as rc9_decisionreasontypechange_los3,
           MAX(rc9_decisionreasontypechange_los4) as rc9_decisionreasontypechange_los4,
           MAX(rc9_decisionreasontypechange_los5) as rc9_decisionreasontypechange_los5,
           MAX(rc9_decisionreasontypechange_los6) as rc9_decisionreasontypechange_los6,
           MAX(rc9_decisionreasontypechange_los7) as rc9_decisionreasontypechange_los7,
           MAX(rc9_decisionreasontypechange_los8) as rc9_decisionreasontypechange_los8,
           MAX(rc10_decisionreasontypechange_los1) as rc10_decisionreasontypechange_los1,
           MAX(rc10_decisionreasontypechange_los2) as rc10_decisionreasontypechange_los2,
           MAX(rc10_decisionreasontypechange_los3) as rc10_decisionreasontypechange_los3,
           MAX(rc10_decisionreasontypechange_los4) as rc10_decisionreasontypechange_los4,
           MAX(rc10_decisionreasontypechange_los5) as rc10_decisionreasontypechange_los5,
           MAX(rc10_decisionreasontypechange_los6) as rc10_decisionreasontypechange_los6,
           MAX(rc10_decisionreasontypechange_los7) as rc10_decisionreasontypechange_los7,
           MAX(rc10_decisionreasontypechange_los8) as rc10_decisionreasontypechange_los8
    from
    (
        select occupancy_dt,
               dow,
               property_id,
            --    ooo,
               specialevent,
               (case accom_class_id
                    when @rc1 then
                        ooo
                end
               ) as rc1_ooo,
               (case accom_class_id
                    when @rc2 then
                        ooo
                end
               ) as rc2_ooo,
               (case accom_class_id
                    when @rc3 then
                        ooo
                end
               ) as rc3_ooo,
               (case accom_class_id
                    when @rc4 then
                        ooo
                end
               ) as rc4_ooo,
               (case accom_class_id
                    when @rc5 then
                        ooo
                end
               ) as rc5_ooo,
               (case accom_class_id
                    when @rc6 then
                        ooo
                end
               ) as rc6_ooo,
               (case accom_class_id
                    when @rc7 then
                        ooo
                end
               ) as rc7_ooo,
               (case accom_class_id
                    when @rc8 then
                        ooo
                end
               ) as rc8_ooo,
               (case accom_class_id
                    when @rc9 then
                        ooo
                end
               ) as rc9_ooo,
               (case accom_class_id
                    when @rc10 then
                        ooo
                end
               ) as rc10_ooo,
               (case accom_class_id
                    when @rc1 then
                        roomssoldcurrent
                end
               ) as rc1_rooms_sold_current,
               (case accom_class_id
                    when @rc1 then
                        roomssoldchange
                end
               ) as rc1_rooms_sold_change,
               (case accom_class_id
                    when @rc2 then
                        roomssoldcurrent
                end
               ) as rc2_rooms_sold_current,
               (case accom_class_id
                    when @rc2 then
                        roomssoldchange
                end
               ) as rc2_rooms_sold_change,
               (case accom_class_id
                    when @rc3 then
                        roomssoldcurrent
                end
               ) as rc3_rooms_sold_current,
               (case accom_class_id
                    when @rc3 then
                        roomssoldchange
                end
               ) as rc3_rooms_sold_change,
               (case accom_class_id
                    when @rc4 then
                        roomssoldcurrent
                end
               ) as rc4_rooms_sold_current,
               (case accom_class_id
                    when @rc4 then
                        roomssoldchange
                end
               ) as rc4_rooms_sold_change,
               (case accom_class_id
                    when @rc5 then
                        roomssoldcurrent
                end
               ) as rc5_rooms_sold_current,
               (case accom_class_id
                    when @rc5 then
                        roomssoldchange
                end
               ) as rc5_rooms_sold_change,
               (case accom_class_id
                    when @rc6 then
                        roomssoldcurrent
                end
               ) as rc6_rooms_sold_current,
               (case accom_class_id
                    when @rc6 then
                        roomssoldchange
                end
               ) as rc6_rooms_sold_change,
               (case accom_class_id
                    when @rc7 then
                        roomssoldcurrent
                end
               ) as rc7_rooms_sold_current,
               (case accom_class_id
                    when @rc7 then
                        roomssoldchange
                end
               ) as rc7_rooms_sold_change,
               (case accom_class_id
                    when @rc8 then
                        roomssoldcurrent
                end
               ) as rc8_rooms_sold_current,
               (case accom_class_id
                    when @rc8 then
                        roomssoldchange
                end
               ) as rc8_rooms_sold_change,
               (case accom_class_id
                    when @rc9 then
                        roomssoldcurrent
                end
               ) as rc9_rooms_sold_current,
               (case accom_class_id
                    when @rc9 then
                        roomssoldchange
                end
               ) as rc9_rooms_sold_change,
               (case accom_class_id
                    when @rc10 then
                        roomssoldcurrent
                end
               ) as rc10_rooms_sold_current,
               (case accom_class_id
                    when @rc10 then
                        roomssoldchange
                end
               ) as rc10_rooms_sold_change,
               (case accom_class_id
                    when @rc1 then
                        occupancyforecastcurrent
                end
               ) as rc1_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc1 then
                        occupancyforecastchange
                end
               ) as rc1_occupancyforecastchange,
               (case accom_class_id
                    when @rc2 then
                        occupancyforecastcurrent
                end
               ) as rc2_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc2 then
                        occupancyforecastchange
                end
               ) as rc2_occupancyforecastchange,
               (case accom_class_id
                    when @rc3 then
                        occupancyforecastcurrent
                end
               ) as rc3_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc3 then
                        occupancyforecastchange
                end
               ) as rc3_occupancyforecastchange,
               (case accom_class_id
                    when @rc4 then
                        occupancyforecastcurrent
                end
               ) as rc4_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc4 then
                        occupancyforecastchange
                end
               ) as rc4_occupancyforecastchange,
               (case accom_class_id
                    when @rc5 then
                        occupancyforecastcurrent
                end
               ) as rc5_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc5 then
                        occupancyforecastchange
                end
               ) as rc5_occupancyforecastchange,
               (case accom_class_id
                    when @rc6 then
                        occupancyforecastcurrent
                end
               ) as rc6_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc6 then
                        occupancyforecastchange
                end
               ) as rc6_occupancyforecastchange,
               (case accom_class_id
                    when @rc7 then
                        occupancyforecastcurrent
                end
               ) as rc7_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc7 then
                        occupancyforecastchange
                end
               ) as rc7_occupancyforecastchange,
               (case accom_class_id
                    when @rc8 then
                        occupancyforecastcurrent
                end
               ) as rc8_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc8 then
                        occupancyforecastchange
                end
               ) as rc8_occupancyforecastchange,
               (case accom_class_id
                    when @rc9 then
                        occupancyforecastcurrent
                end
               ) as rc9_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc9 then
                        occupancyforecastchange
                end
               ) as rc9_occupancyforecastchange,
               (case accom_class_id
                    when @rc10 then
                        occupancyforecastcurrent
                end
               ) as rc10_occupancyforecastcurrent,
               (case accom_class_id
                    when @rc10 then
                        occupancyforecastchange
                end
               ) as rc10_occupancyforecastchange,
               (case accom_class_id
                    when @rc1 then
                        occupancyforecastpercurrent
                end
               ) as rc1_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc1 then
                        occupancyforecastperchange
                end
               ) as rc1_occupancyforecastperchange,
               (case accom_class_id
                    when @rc2 then
                        occupancyforecastpercurrent
                end
               ) as rc2_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc2 then
                        occupancyforecastperchange
                end
               ) as rc2_occupancyforecastperchange,
               (case accom_class_id
                    when @rc3 then
                        occupancyforecastpercurrent
                end
               ) as rc3_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc3 then
                        occupancyforecastperchange
                end
               ) as rc3_occupancyforecastperchange,
               (case accom_class_id
                    when @rc4 then
                        occupancyforecastpercurrent
                end
               ) as rc4_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc4 then
                        occupancyforecastperchange
                end
               ) as rc4_occupancyforecastperchange,
               (case accom_class_id
                    when @rc5 then
                        occupancyforecastpercurrent
                end
               ) as rc5_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc5 then
                        occupancyforecastperchange
                end
               ) as rc5_occupancyforecastperchange,
               (case accom_class_id
                    when @rc6 then
                        occupancyforecastpercurrent
                end
               ) as rc6_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc6 then
                        occupancyforecastperchange
                end
               ) as rc6_occupancyforecastperchange,
               (case accom_class_id
                    when @rc7 then
                        occupancyforecastpercurrent
                end
               ) as rc7_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc7 then
                        occupancyforecastperchange
                end
               ) as rc7_occupancyforecastperchange,
               (case accom_class_id
                    when @rc8 then
                        occupancyforecastpercurrent
                end
               ) as rc8_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc8 then
                        occupancyforecastperchange
                end
               ) as rc8_occupancyforecastperchange,
               (case accom_class_id
                    when @rc9 then
                        occupancyforecastpercurrent
                end
               ) as rc9_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc9 then
                        occupancyforecastperchange
                end
               ) as rc9_occupancyforecastperchange,
               (case accom_class_id
                    when @rc10 then
                        occupancyforecastpercurrent
                end
               ) as rc10_occupancyforecastpercurrent,
               (case accom_class_id
                    when @rc10 then
                        occupancyforecastperchange
                end
               ) as rc10_occupancyforecastperchange,
               (case accom_class_id
                    when @rc1 then
                        bookedprofitcurrent
                end
               ) as rc1_bookedprofitcurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedprofitchange
                end
               ) as rc1_bookedprofitchange,
               (case accom_class_id
                    when @rc2 then
                        bookedprofitcurrent
                end
               ) as rc2_bookedprofitcurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedprofitchange
                end
               ) as rc2_bookedprofitchange,
               (case accom_class_id
                    when @rc3 then
                        bookedprofitcurrent
                end
               ) as rc3_bookedprofitcurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedprofitchange
                end
               ) as rc3_bookedprofitchange,
               (case accom_class_id
                    when @rc4 then
                        bookedprofitcurrent
                end
               ) as rc4_bookedprofitcurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedprofitchange
                end
               ) as rc4_bookedprofitchange,
               (case accom_class_id
                    when @rc5 then
                        bookedprofitcurrent
                end
               ) as rc5_bookedprofitcurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedprofitchange
                end
               ) as rc5_bookedprofitchange,
               (case accom_class_id
                    when @rc6 then
                        bookedprofitcurrent
                end
               ) as rc6_bookedprofitcurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedprofitchange
                end
               ) as rc6_bookedprofitchange,
               (case accom_class_id
                    when @rc7 then
                        bookedprofitcurrent
                end
               ) as rc7_bookedprofitcurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedprofitchange
                end
               ) as rc7_bookedprofitchange,
               (case accom_class_id
                    when @rc8 then
                        bookedprofitcurrent
                end
               ) as rc8_bookedprofitcurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedprofitchange
                end
               ) as rc8_bookedprofitchange,
               (case accom_class_id
                    when @rc9 then
                        bookedprofitcurrent
                end
               ) as rc9_bookedprofitcurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedprofitchange
                end
               ) as rc9_bookedprofitchange,
               (case accom_class_id
                    when @rc10 then
                        bookedprofitcurrent
                end
               ) as rc10_bookedprofitcurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedprofitchange
                end
               ) as rc10_bookedprofitchange,
               (case accom_class_id
                    when @rc1 then
                        fcstedprofitcurrent
                end
               ) as rc1_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc1 then
                        fcstedprofitchange
                end
               ) as rc1_fcstedprofitchange,
               (case accom_class_id
                    when @rc2 then
                        fcstedprofitcurrent
                end
               ) as rc2_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc2 then
                        fcstedprofitchange
                end
               ) as rc2_fcstedprofitchange,
               (case accom_class_id
                    when @rc3 then
                        fcstedprofitcurrent
                end
               ) as rc3_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc3 then
                        fcstedprofitchange
                end
               ) as rc3_fcstedprofitchange,
               (case accom_class_id
                    when @rc4 then
                        fcstedprofitcurrent
                end
               ) as rc4_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc4 then
                        fcstedprofitchange
                end
               ) as rc4_fcstedprofitchange,
               (case accom_class_id
                    when @rc5 then
                        fcstedprofitcurrent
                end
               ) as rc5_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc5 then
                        fcstedprofitchange
                end
               ) as rc5_fcstedprofitchange,
               (case accom_class_id
                    when @rc6 then
                        fcstedprofitcurrent
                end
               ) as rc6_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc6 then
                        fcstedprofitchange
                end
               ) as rc6_fcstedprofitchange,
               (case accom_class_id
                    when @rc7 then
                        fcstedprofitcurrent
                end
               ) as rc7_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc7 then
                        fcstedprofitchange
                end
               ) as rc7_fcstedprofitchange,
               (case accom_class_id
                    when @rc8 then
                        fcstedprofitcurrent
                end
               ) as rc8_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc8 then
                        fcstedprofitchange
                end
               ) as rc8_fcstedprofitchange,
               (case accom_class_id
                    when @rc9 then
                        fcstedprofitcurrent
                end
               ) as rc9_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc9 then
                        fcstedprofitchange
                end
               ) as rc9_fcstedprofitchange,
               (case accom_class_id
                    when @rc10 then
                        fcstedprofitcurrent
                end
               ) as rc10_fcstedprofitcurrent,
               (case accom_class_id
                    when @rc10 then
                        fcstedprofitchange
                end
               ) as rc10_fcstedprofitchange,
               (case accom_class_id
                    when @rc1 then
                        bookedproporcurrent
                end
               ) as rc1_bookedproporcurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedproporchange
                end
               ) as rc1_bookedproporchange,
               (case accom_class_id
                    when @rc2 then
                        bookedproporcurrent
                end
               ) as rc2_bookedproporcurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedproporchange
                end
               ) as rc2_bookedproporchange,
               (case accom_class_id
                    when @rc3 then
                        bookedproporcurrent
                end
               ) as rc3_bookedproporcurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedproporchange
                end
               ) as rc3_bookedproporchange,
               (case accom_class_id
                    when @rc4 then
                        bookedproporcurrent
                end
               ) as rc4_bookedproporcurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedproporchange
                end
               ) as rc4_bookedproporchange,
               (case accom_class_id
                    when @rc5 then
                        bookedproporcurrent
                end
               ) as rc5_bookedproporcurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedproporchange
                end
               ) as rc5_bookedproporchange,
               (case accom_class_id
                    when @rc6 then
                        bookedproporcurrent
                end
               ) as rc6_bookedproporcurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedproporchange
                end
               ) as rc6_bookedproporchange,
               (case accom_class_id
                    when @rc7 then
                        bookedproporcurrent
                end
               ) as rc7_bookedproporcurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedproporchange
                end
               ) as rc7_bookedproporchange,
               (case accom_class_id
                    when @rc8 then
                        bookedproporcurrent
                end
               ) as rc8_bookedproporcurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedproporchange
                end
               ) as rc8_bookedproporchange,
               (case accom_class_id
                    when @rc9 then
                        bookedproporcurrent
                end
               ) as rc9_bookedproporcurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedproporchange
                end
               ) as rc9_bookedproporchange,
               (case accom_class_id
                    when @rc10 then
                        bookedproporcurrent
                end
               ) as rc10_bookedproporcurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedproporchange
                end
               ) as rc10_bookedproporchange,
               (case accom_class_id
                    when @rc1 then
                        estimatedproporcurrent
                end
               ) as rc1_estimatedproporcurrent,
               (case accom_class_id
                    when @rc1 then
                        estimatedproporchange
                end
               ) as rc1_estimatedproporchange,
               (case accom_class_id
                    when @rc2 then
                        estimatedproporcurrent
                end
               ) as rc2_estimatedproporcurrent,
               (case accom_class_id
                    when @rc2 then
                        estimatedproporchange
                end
               ) as rc2_estimatedproporchange,
               (case accom_class_id
                    when @rc3 then
                        estimatedproporcurrent
                end
               ) as rc3_estimatedproporcurrent,
               (case accom_class_id
                    when @rc3 then
                        estimatedproporchange
                end
               ) as rc3_estimatedproporchange,
               (case accom_class_id
                    when @rc4 then
                        estimatedproporcurrent
                end
               ) as rc4_estimatedproporcurrent,
               (case accom_class_id
                    when @rc4 then
                        estimatedproporchange
                end
               ) as rc4_estimatedproporchange,
               (case accom_class_id
                    when @rc5 then
                        estimatedproporcurrent
                end
               ) as rc5_estimatedproporcurrent,
               (case accom_class_id
                    when @rc5 then
                        estimatedproporchange
                end
               ) as rc5_estimatedproporchange,
               (case accom_class_id
                    when @rc6 then
                        estimatedproporcurrent
                end
               ) as rc6_estimatedproporcurrent,
               (case accom_class_id
                    when @rc6 then
                        estimatedproporchange
                end
               ) as rc6_estimatedproporchange,
               (case accom_class_id
                    when @rc7 then
                        estimatedproporcurrent
                end
               ) as rc7_estimatedproporcurrent,
               (case accom_class_id
                    when @rc7 then
                        estimatedproporchange
                end
               ) as rc7_estimatedproporchange,
               (case accom_class_id
                    when @rc8 then
                        estimatedproporcurrent
                end
               ) as rc8_estimatedproporcurrent,
               (case accom_class_id
                    when @rc8 then
                        estimatedproporchange
                end
               ) as rc8_estimatedproporchange,
               (case accom_class_id
                    when @rc9 then
                        estimatedproporcurrent
                end
               ) as rc9_estimatedproporcurrent,
               (case accom_class_id
                    when @rc9 then
                        estimatedproporchange
                end
               ) as rc9_estimatedproporchange,
               (case accom_class_id
                    when @rc10 then
                        estimatedproporcurrent
                end
               ) as rc10_estimatedproporcurrent,
               (case accom_class_id
                    when @rc10 then
                        estimatedproporchange
                end
               ) as rc10_estimatedproporchange,
               (case accom_class_id
                    when @rc1 then
                        bookedproparcurrent
                end
               ) as rc1_bookedproparcurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedproparchange
                end
               ) as rc1_bookedproparchange,
               (case accom_class_id
                    when @rc2 then
                        bookedproparcurrent
                end
               ) as rc2_bookedproparcurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedproparchange
                end
               ) as rc2_bookedproparchange,
               (case accom_class_id
                    when @rc3 then
                        bookedproparcurrent
                end
               ) as rc3_bookedproparcurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedproparchange
                end
               ) as rc3_bookedproparchange,
               (case accom_class_id
                    when @rc4 then
                        bookedproparcurrent
                end
               ) as rc4_bookedproparcurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedproparchange
                end
               ) as rc4_bookedproparchange,
               (case accom_class_id
                    when @rc5 then
                        bookedproparcurrent
                end
               ) as rc5_bookedproparcurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedproparchange
                end
               ) as rc5_bookedproparchange,
               (case accom_class_id
                    when @rc6 then
                        bookedproparcurrent
                end
               ) as rc6_bookedproparcurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedproparchange
                end
               ) as rc6_bookedproparchange,
               (case accom_class_id
                    when @rc7 then
                        bookedproparcurrent
                end
               ) as rc7_bookedproparcurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedproparchange
                end
               ) as rc7_bookedproparchange,
               (case accom_class_id
                    when @rc8 then
                        bookedproparcurrent
                end
               ) as rc8_bookedproparcurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedproparchange
                end
               ) as rc8_bookedproparchange,
               (case accom_class_id
                    when @rc9 then
                        bookedproparcurrent
                end
               ) as rc9_bookedproparcurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedproparchange
                end
               ) as rc9_bookedproparchange,
               (case accom_class_id
                    when @rc10 then
                        bookedproparcurrent
                end
               ) as rc10_bookedproparcurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedproparchange
                end
               ) as rc10_bookedproparchange,
               (case accom_class_id
                    when @rc1 then
                        estimatedproparcurrent
                end
               ) as rc1_estimatedproparcurrent,
               (case accom_class_id
                    when @rc1 then
                        estimatedproparchange
                end
               ) as rc1_estimatedproparchange,
               (case accom_class_id
                    when @rc2 then
                        estimatedproparcurrent
                end
               ) as rc2_estimatedproparcurrent,
               (case accom_class_id
                    when @rc2 then
                        estimatedproparchange
                end
               ) as rc2_estimatedproparchange,
               (case accom_class_id
                    when @rc3 then
                        estimatedproparcurrent
                end
               ) as rc3_estimatedproparcurrent,
               (case accom_class_id
                    when @rc3 then
                        estimatedproparchange
                end
               ) as rc3_estimatedproparchange,
               (case accom_class_id
                    when @rc4 then
                        estimatedproparcurrent
                end
               ) as rc4_estimatedproparcurrent,
               (case accom_class_id
                    when @rc4 then
                        estimatedproparchange
                end
               ) as rc4_estimatedproparchange,
               (case accom_class_id
                    when @rc5 then
                        estimatedproparcurrent
                end
               ) as rc5_estimatedproparcurrent,
               (case accom_class_id
                    when @rc5 then
                        estimatedproparchange
                end
               ) as rc5_estimatedproparchange,
               (case accom_class_id
                    when @rc6 then
                        estimatedproparcurrent
                end
               ) as rc6_estimatedproparcurrent,
               (case accom_class_id
                    when @rc6 then
                        estimatedproparchange
                end
               ) as rc6_estimatedproparchange,
               (case accom_class_id
                    when @rc7 then
                        estimatedproparcurrent
                end
               ) as rc7_estimatedproparcurrent,
               (case accom_class_id
                    when @rc7 then
                        estimatedproparchange
                end
               ) as rc7_estimatedproparchange,
               (case accom_class_id
                    when @rc8 then
                        estimatedproparcurrent
                end
               ) as rc8_estimatedproparcurrent,
               (case accom_class_id
                    when @rc8 then
                        estimatedproparchange
                end
               ) as rc8_estimatedproparchange,
               (case accom_class_id
                    when @rc9 then
                        estimatedproparcurrent
                end
               ) as rc9_estimatedproparcurrent,
               (case accom_class_id
                    when @rc9 then
                        estimatedproparchange
                end
               ) as rc9_estimatedproparchange,
               (case accom_class_id
                    when @rc10 then
                        estimatedproparcurrent
                end
               ) as rc10_estimatedproparcurrent,
               (case accom_class_id
                    when @rc10 then
                        estimatedproparchange
                end
               ) as rc10_estimatedproparchange,
               (case accom_class_id
                    when @rc1 then
                        bookedroomrevenuecurrent
                end
               ) as rc1_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedroomrevenuechange
                end
               ) as rc1_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc2 then
                        bookedroomrevenuecurrent
                end
               ) as rc2_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedroomrevenuechange
                end
               ) as rc2_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc3 then
                        bookedroomrevenuecurrent
                end
               ) as rc3_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedroomrevenuechange
                end
               ) as rc3_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc4 then
                        bookedroomrevenuecurrent
                end
               ) as rc4_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedroomrevenuechange
                end
               ) as rc4_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc5 then
                        bookedroomrevenuecurrent
                end
               ) as rc5_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedroomrevenuechange
                end
               ) as rc5_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc6 then
                        bookedroomrevenuecurrent
                end
               ) as rc6_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedroomrevenuechange
                end
               ) as rc6_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc7 then
                        bookedroomrevenuecurrent
                end
               ) as rc7_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedroomrevenuechange
                end
               ) as rc7_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc8 then
                        bookedroomrevenuecurrent
                end
               ) as rc8_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedroomrevenuechange
                end
               ) as rc8_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc9 then
                        bookedroomrevenuecurrent
                end
               ) as rc9_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedroomrevenuechange
                end
               ) as rc9_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc10 then
                        bookedroomrevenuecurrent
                end
               ) as rc10_bookedroomrevenuecurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedroomrevenuechange
                end
               ) as rc10_bookedroomrevenuechange,
               (case accom_class_id
                    when @rc1 then
                        fcstedroomrevenuecurrent
                end
               ) as rc1_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc1 then
                        fcstedroomrevenuechange
                end
               ) as rc1_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc2 then
                        fcstedroomrevenuecurrent
                end
               ) as rc2_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc2 then
                        fcstedroomrevenuechange
                end
               ) as rc2_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc3 then
                        fcstedroomrevenuecurrent
                end
               ) as rc3_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc3 then
                        fcstedroomrevenuechange
                end
               ) as rc3_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc4 then
                        fcstedroomrevenuecurrent
                end
               ) as rc4_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc4 then
                        fcstedroomrevenuechange
                end
               ) as rc4_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc5 then
                        fcstedroomrevenuecurrent
                end
               ) as rc5_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc5 then
                        fcstedroomrevenuechange
                end
               ) as rc5_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc6 then
                        fcstedroomrevenuecurrent
                end
               ) as rc6_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc6 then
                        fcstedroomrevenuechange
                end
               ) as rc6_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc7 then
                        fcstedroomrevenuecurrent
                end
               ) as rc7_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc7 then
                        fcstedroomrevenuechange
                end
               ) as rc7_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc8 then
                        fcstedroomrevenuecurrent
                end
               ) as rc8_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc8 then
                        fcstedroomrevenuechange
                end
               ) as rc8_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc9 then
                        fcstedroomrevenuecurrent
                end
               ) as rc9_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc9 then
                        fcstedroomrevenuechange
                end
               ) as rc9_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc10 then
                        fcstedroomrevenuecurrent
                end
               ) as rc10_fcstedroomrevenuecurrent,
               (case accom_class_id
                    when @rc10 then
                        fcstedroomrevenuechange
                end
               ) as rc10_fcstedroomrevenuechange,
               (case accom_class_id
                    when @rc1 then
                        bookedadrcurrent
                end
               ) as rc1_bookedadrcurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedadrchange
                end
               ) as rc1_bookedadrchange,
               (case accom_class_id
                    when @rc2 then
                        bookedadrcurrent
                end
               ) as rc2_bookedadrcurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedadrchange
                end
               ) as rc2_bookedadrchange,
               (case accom_class_id
                    when @rc3 then
                        bookedadrcurrent
                end
               ) as rc3_bookedadrcurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedadrchange
                end
               ) as rc3_bookedadrchange,
               (case accom_class_id
                    when @rc4 then
                        bookedadrcurrent
                end
               ) as rc4_bookedadrcurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedadrchange
                end
               ) as rc4_bookedadrchange,
               (case accom_class_id
                    when @rc5 then
                        bookedadrcurrent
                end
               ) as rc5_bookedadrcurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedadrchange
                end
               ) as rc5_bookedadrchange,
               (case accom_class_id
                    when @rc6 then
                        bookedadrcurrent
                end
               ) as rc6_bookedadrcurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedadrchange
                end
               ) as rc6_bookedadrchange,
               (case accom_class_id
                    when @rc7 then
                        bookedadrcurrent
                end
               ) as rc7_bookedadrcurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedadrchange
                end
               ) as rc7_bookedadrchange,
               (case accom_class_id
                    when @rc8 then
                        bookedadrcurrent
                end
               ) as rc8_bookedadrcurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedadrchange
                end
               ) as rc8_bookedadrchange,
               (case accom_class_id
                    when @rc9 then
                        bookedadrcurrent
                end
               ) as rc9_bookedadrcurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedadrchange
                end
               ) as rc9_bookedadrchange,
               (case accom_class_id
                    when @rc10 then
                        bookedadrcurrent
                end
               ) as rc10_bookedadrcurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedadrchange
                end
               ) as rc10_bookedadrchange,
               (case accom_class_id
                    when @rc1 then
                        estimatedadrcurrent
                end
               ) as rc1_estimatedadrcurrent,
               (case accom_class_id
                    when @rc1 then
                        estimatedadrchange
                end
               ) as rc1_estimatedadrchange,
               (case accom_class_id
                    when @rc2 then
                        estimatedadrcurrent
                end
               ) as rc2_estimatedadrcurrent,
               (case accom_class_id
                    when @rc2 then
                        estimatedadrchange
                end
               ) as rc2_estimatedadrchange,
               (case accom_class_id
                    when @rc3 then
                        estimatedadrcurrent
                end
               ) as rc3_estimatedadrcurrent,
               (case accom_class_id
                    when @rc3 then
                        estimatedadrchange
                end
               ) as rc3_estimatedadrchange,
               (case accom_class_id
                    when @rc4 then
                        estimatedadrcurrent
                end
               ) as rc4_estimatedadrcurrent,
               (case accom_class_id
                    when @rc4 then
                        estimatedadrchange
                end
               ) as rc4_estimatedadrchange,
               (case accom_class_id
                    when @rc5 then
                        estimatedadrcurrent
                end
               ) as rc5_estimatedadrcurrent,
               (case accom_class_id
                    when @rc5 then
                        estimatedadrchange
                end
               ) as rc5_estimatedadrchange,
               (case accom_class_id
                    when @rc6 then
                        estimatedadrcurrent
                end
               ) as rc6_estimatedadrcurrent,
               (case accom_class_id
                    when @rc6 then
                        estimatedadrchange
                end
               ) as rc6_estimatedadrchange,
               (case accom_class_id
                    when @rc7 then
                        estimatedadrcurrent
                end
               ) as rc7_estimatedadrcurrent,
               (case accom_class_id
                    when @rc7 then
                        estimatedadrchange
                end
               ) as rc7_estimatedadrchange,
               (case accom_class_id
                    when @rc8 then
                        estimatedadrcurrent
                end
               ) as rc8_estimatedadrcurrent,
               (case accom_class_id
                    when @rc8 then
                        estimatedadrchange
                end
               ) as rc8_estimatedadrchange,
               (case accom_class_id
                    when @rc9 then
                        estimatedadrcurrent
                end
               ) as rc9_estimatedadrcurrent,
               (case accom_class_id
                    when @rc9 then
                        estimatedadrchange
                end
               ) as rc9_estimatedadrchange,
               (case accom_class_id
                    when @rc10 then
                        estimatedadrcurrent
                end
               ) as rc10_estimatedadrcurrent,
               (case accom_class_id
                    when @rc10 then
                        estimatedadrchange
                end
               ) as rc10_estimatedadrchange,
               (case accom_class_id
                    when @rc1 then
                        bookedrevparcurrent
                end
               ) as rc1_bookedrevparcurrent,
               (case accom_class_id
                    when @rc1 then
                        bookedrevparchange
                end
               ) as rc1_bookedrevparchange,
               (case accom_class_id
                    when @rc2 then
                        bookedrevparcurrent
                end
               ) as rc2_bookedrevparcurrent,
               (case accom_class_id
                    when @rc2 then
                        bookedrevparchange
                end
               ) as rc2_bookedrevparchange,
               (case accom_class_id
                    when @rc3 then
                        bookedrevparcurrent
                end
               ) as rc3_bookedrevparcurrent,
               (case accom_class_id
                    when @rc3 then
                        bookedrevparchange
                end
               ) as rc3_bookedrevparchange,
               (case accom_class_id
                    when @rc4 then
                        bookedrevparcurrent
                end
               ) as rc4_bookedrevparcurrent,
               (case accom_class_id
                    when @rc4 then
                        bookedrevparchange
                end
               ) as rc4_bookedrevparchange,
               (case accom_class_id
                    when @rc5 then
                        bookedrevparcurrent
                end
               ) as rc5_bookedrevparcurrent,
               (case accom_class_id
                    when @rc5 then
                        bookedrevparchange
                end
               ) as rc5_bookedrevparchange,
               (case accom_class_id
                    when @rc6 then
                        bookedrevparcurrent
                end
               ) as rc6_bookedrevparcurrent,
               (case accom_class_id
                    when @rc6 then
                        bookedrevparchange
                end
               ) as rc6_bookedrevparchange,
               (case accom_class_id
                    when @rc7 then
                        bookedrevparcurrent
                end
               ) as rc7_bookedrevparcurrent,
               (case accom_class_id
                    when @rc7 then
                        bookedrevparchange
                end
               ) as rc7_bookedrevparchange,
               (case accom_class_id
                    when @rc8 then
                        bookedrevparcurrent
                end
               ) as rc8_bookedrevparcurrent,
               (case accom_class_id
                    when @rc8 then
                        bookedrevparchange
                end
               ) as rc8_bookedrevparchange,
               (case accom_class_id
                    when @rc9 then
                        bookedrevparcurrent
                end
               ) as rc9_bookedrevparcurrent,
               (case accom_class_id
                    when @rc9 then
                        bookedrevparchange
                end
               ) as rc9_bookedrevparchange,
               (case accom_class_id
                    when @rc10 then
                        bookedrevparcurrent
                end
               ) as rc10_bookedrevparcurrent,
               (case accom_class_id
                    when @rc10 then
                        bookedrevparchange
                end
               ) as rc10_bookedrevparchange,
               (case accom_class_id
                    when @rc1 then
                        estimatedrevparcurrent
                end
               ) as rc1_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc1 then
                        estimatedrevparchange
                end
               ) as rc1_estimatedrevparchange,
               (case accom_class_id
                    when @rc2 then
                        estimatedrevparcurrent
                end
               ) as rc2_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc2 then
                        estimatedrevparchange
                end
               ) as rc2_estimatedrevparchange,
               (case accom_class_id
                    when @rc3 then
                        estimatedrevparcurrent
                end
               ) as rc3_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc3 then
                        estimatedrevparchange
                end
               ) as rc3_estimatedrevparchange,
               (case accom_class_id
                    when @rc4 then
                        estimatedrevparcurrent
                end
               ) as rc4_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc4 then
                        estimatedrevparchange
                end
               ) as rc4_estimatedrevparchange,
               (case accom_class_id
                    when @rc5 then
                        estimatedrevparcurrent
                end
               ) as rc5_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc5 then
                        estimatedrevparchange
                end
               ) as rc5_estimatedrevparchange,
               (case accom_class_id
                    when @rc6 then
                        estimatedrevparcurrent
                end
               ) as rc6_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc6 then
                        estimatedrevparchange
                end
               ) as rc6_estimatedrevparchange,
               (case accom_class_id
                    when @rc7 then
                        estimatedrevparcurrent
                end
               ) as rc7_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc7 then
                        estimatedrevparchange
                end
               ) as rc7_estimatedrevparchange,
               (case accom_class_id
                    when @rc8 then
                        estimatedrevparcurrent
                end
               ) as rc8_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc8 then
                        estimatedrevparchange
                end
               ) as rc8_estimatedrevparchange,
               (case accom_class_id
                    when @rc9 then
                        estimatedrevparcurrent
                end
               ) as rc9_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc9 then
                        estimatedrevparchange
                end
               ) as rc9_estimatedrevparchange,
               (case accom_class_id
                    when @rc10 then
                        estimatedrevparcurrent
                end
               ) as rc10_estimatedrevparcurrent,
               (case accom_class_id
                    when @rc10 then
                        estimatedrevparchange
                end
               ) as rc10_estimatedrevparchange,
               (case accom_class_id
                    when @rc1 then
                        lrv
                end
               ) as rc1_lrv,
               (case accom_class_id
                    when @rc1 then
                        lrv_change
                end
               ) as rc1_lrv_change,
               (case accom_class_id
                    when @rc2 then
                        lrv
                end
               ) as rc2_lrv,
               (case accom_class_id
                    when @rc2 then
                        lrv_change
                end
               ) as rc2_lrv_change,
               (case accom_class_id
                    when @rc3 then
                        lrv
                end
               ) as rc3_lrv,
               (case accom_class_id
                    when @rc3 then
                        lrv_change
                end
               ) as rc3_lrv_change,
               (case accom_class_id
                    when @rc4 then
                        lrv
                end
               ) as rc4_lrv,
               (case accom_class_id
                    when @rc4 then
                        lrv_change
                end
               ) as rc4_lrv_change,
               (case accom_class_id
                    when @rc5 then
                        lrv
                end
               ) as rc5_lrv,
               (case accom_class_id
                    when @rc5 then
                        lrv_change
                end
               ) as rc5_lrv_change,
               (case accom_class_id
                    when @rc6 then
                        lrv
                end
               ) as rc6_lrv,
               (case accom_class_id
                    when @rc6 then
                        lrv_change
                end
               ) as rc6_lrv_change,
               (case accom_class_id
                    when @rc7 then
                        lrv
                end
               ) as rc7_lrv,
               (case accom_class_id
                    when @rc7 then
                        lrv_change
                end
               ) as rc7_lrv_change,
               (case accom_class_id
                    when @rc8 then
                        lrv
                end
               ) as rc8_lrv,
               (case accom_class_id
                    when @rc8 then
                        lrv_change
                end
               ) as rc8_lrv_change,
               (case accom_class_id
                    when @rc9 then
                        lrv
                end
               ) as rc9_lrv,
               (case accom_class_id
                    when @rc9 then
                        lrv_change
                end
               ) as rc9_lrv_change,
               (case accom_class_id
                    when @rc10 then
                        lrv
                end
               ) as rc10_lrv,
               (case accom_class_id
                    when @rc10 then
                        lrv_change
                end
               ) as rc10_lrv_change,
               (case accom_class_id
                    when @rc1 then
                        overbookingcurrent
                end
               ) as rc1_overbookingcurrent,
               (case accom_class_id
                    when @rc1 then
                        overbookingchange
                end
               ) as rc1_overbookingchange,
               (case accom_class_id
                    when @rc2 then
                        overbookingcurrent
                end
               ) as rc2_overbookingcurrent,
               (case accom_class_id
                    when @rc2 then
                        overbookingchange
                end
               ) as rc2_overbookingchange,
               (case accom_class_id
                    when @rc3 then
                        overbookingcurrent
                end
               ) as rc3_overbookingcurrent,
               (case accom_class_id
                    when @rc3 then
                        overbookingchange
                end
               ) as rc3_overbookingchange,
               (case accom_class_id
                    when @rc4 then
                        overbookingcurrent
                end
               ) as rc4_overbookingcurrent,
               (case accom_class_id
                    when @rc4 then
                        overbookingchange
                end
               ) as rc4_overbookingchange,
               (case accom_class_id
                    when @rc5 then
                        overbookingcurrent
                end
               ) as rc5_overbookingcurrent,
               (case accom_class_id
                    when @rc5 then
                        overbookingchange
                end
               ) as rc5_overbookingchange,
               (case accom_class_id
                    when @rc6 then
                        overbookingcurrent
                end
               ) as rc6_overbookingcurrent,
               (case accom_class_id
                    when @rc6 then
                        overbookingchange
                end
               ) as rc6_overbookingchange,
               (case accom_class_id
                    when @rc7 then
                        overbookingcurrent
                end
               ) as rc7_overbookingcurrent,
               (case accom_class_id
                    when @rc7 then
                        overbookingchange
                end
               ) as rc7_overbookingchange,
               (case accom_class_id
                    when @rc8 then
                        overbookingcurrent
                end
               ) as rc8_overbookingcurrent,
               (case accom_class_id
                    when @rc8 then
                        overbookingchange
                end
               ) as rc8_overbookingchange,
               (case accom_class_id
                    when @rc9 then
                        overbookingcurrent
                end
               ) as rc9_overbookingcurrent,
               (case accom_class_id
                    when @rc9 then
                        overbookingchange
                end
               ) as rc9_overbookingchange,
               (case accom_class_id
                    when @rc10 then
                        overbookingcurrent
                end
               ) as rc10_overbookingcurrent,
               (case accom_class_id
                    when @rc10 then
                        overbookingchange
                end
               ) as rc10_overbookingchange,
               (case accom_class_id
                    when @rc1 then
                        master_class_name
                end
               ) as rc1_master_class_name,
               (case accom_class_id
                    when @rc2 then
                        master_class_name
                end
               ) as rc2_master_class_name,
               (case accom_class_id
                    when @rc3 then
                        master_class_name
                end
               ) as rc3_master_class_name,
               (case accom_class_id
                    when @rc4 then
                        master_class_name
                end
               ) as rc4_master_class_name,
               (case accom_class_id
                    when @rc5 then
                        master_class_name
                end
               ) as rc5_master_class_name,
               (case accom_class_id
                    when @rc6 then
                        master_class_name
                end
               ) as rc6_master_class_name,
               (case accom_class_id
                    when @rc7 then
                        master_class_name
                end
               ) as rc7_master_class_name,
               (case accom_class_id
                    when @rc8 then
                        master_class_name
                end
               ) as rc8_master_class_name,
               (case accom_class_id
                    when @rc9 then
                        master_class_name
                end
               ) as rc9_master_class_name,
               (case accom_class_id
                    when @rc10 then
                        master_class_name
                end
               ) as rc10_master_class_name,
               (case accom_class_id
                    when @rc1 then
                        bar_los1
                end
               ) as rc1_bar_los1,
               (case accom_class_id
                    when @rc1 then
                        bar_los2
                end
               ) as rc1_bar_los2,
               (case accom_class_id
                    when @rc1 then
                        bar_los3
                end
               ) as rc1_bar_los3,
               (case accom_class_id
                    when @rc1 then
                        bar_los4
                end
               ) as rc1_bar_los4,
               (case accom_class_id
                    when @rc1 then
                        bar_los5
                end
               ) as rc1_bar_los5,
               (case accom_class_id
                    when @rc1 then
                        bar_los6
                end
               ) as rc1_bar_los6,
               (case accom_class_id
                    when @rc1 then
                        bar_los7
                end
               ) as rc1_bar_los7,
               (case accom_class_id
                    when @rc1 then
                        bar_los8
                end
               ) as rc1_bar_los8,
               (case accom_class_id
                    when @rc2 then
                        bar_los1
                end
               ) as rc2_bar_los1,
               (case accom_class_id
                    when @rc2 then
                        bar_los2
                end
               ) as rc2_bar_los2,
               (case accom_class_id
                    when @rc2 then
                        bar_los3
                end
               ) as rc2_bar_los3,
               (case accom_class_id
                    when @rc2 then
                        bar_los4
                end
               ) as rc2_bar_los4,
               (case accom_class_id
                    when @rc2 then
                        bar_los5
                end
               ) as rc2_bar_los5,
               (case accom_class_id
                    when @rc2 then
                        bar_los6
                end
               ) as rc2_bar_los6,
               (case accom_class_id
                    when @rc2 then
                        bar_los7
                end
               ) as rc2_bar_los7,
               (case accom_class_id
                    when @rc2 then
                        bar_los8
                end
               ) as rc2_bar_los8,
               (case accom_class_id
                    when @rc3 then
                        bar_los1
                end
               ) as rc3_bar_los1,
               (case accom_class_id
                    when @rc3 then
                        bar_los2
                end
               ) as rc3_bar_los2,
               (case accom_class_id
                    when @rc3 then
                        bar_los3
                end
               ) as rc3_bar_los3,
               (case accom_class_id
                    when @rc3 then
                        bar_los4
                end
               ) as rc3_bar_los4,
               (case accom_class_id
                    when @rc3 then
                        bar_los5
                end
               ) as rc3_bar_los5,
               (case accom_class_id
                    when @rc3 then
                        bar_los6
                end
               ) as rc3_bar_los6,
               (case accom_class_id
                    when @rc3 then
                        bar_los7
                end
               ) as rc3_bar_los7,
               (case accom_class_id
                    when @rc3 then
                        bar_los8
                end
               ) as rc3_bar_los8,
               (case accom_class_id
                    when @rc4 then
                        bar_los1
                end
               ) as rc4_bar_los1,
               (case accom_class_id
                    when @rc4 then
                        bar_los2
                end
               ) as rc4_bar_los2,
               (case accom_class_id
                    when @rc4 then
                        bar_los3
                end
               ) as rc4_bar_los3,
               (case accom_class_id
                    when @rc4 then
                        bar_los4
                end
               ) as rc4_bar_los4,
               (case accom_class_id
                    when @rc4 then
                        bar_los5
                end
               ) as rc4_bar_los5,
               (case accom_class_id
                    when @rc4 then
                        bar_los6
                end
               ) as rc4_bar_los6,
               (case accom_class_id
                    when @rc4 then
                        bar_los7
                end
               ) as rc4_bar_los7,
               (case accom_class_id
                    when @rc4 then
                        bar_los8
                end
               ) as rc4_bar_los8,
               (case accom_class_id
                    when @rc5 then
                        bar_los1
                end
               ) as rc5_bar_los1,
               (case accom_class_id
                    when @rc5 then
                        bar_los2
                end
               ) as rc5_bar_los2,
               (case accom_class_id
                    when @rc5 then
                        bar_los3
                end
               ) as rc5_bar_los3,
               (case accom_class_id
                    when @rc5 then
                        bar_los4
                end
               ) as rc5_bar_los4,
               (case accom_class_id
                    when @rc5 then
                        bar_los5
                end
               ) as rc5_bar_los5,
               (case accom_class_id
                    when @rc5 then
                        bar_los6
                end
               ) as rc5_bar_los6,
               (case accom_class_id
                    when @rc5 then
                        bar_los7
                end
               ) as rc5_bar_los7,
               (case accom_class_id
                    when @rc5 then
                        bar_los8
                end
               ) as rc5_bar_los8,
               (case accom_class_id
                    when @rc6 then
                        bar_los1
                end
               ) as rc6_bar_los1,
               (case accom_class_id
                    when @rc6 then
                        bar_los2
                end
               ) as rc6_bar_los2,
               (case accom_class_id
                    when @rc6 then
                        bar_los3
                end
               ) as rc6_bar_los3,
               (case accom_class_id
                    when @rc6 then
                        bar_los4
                end
               ) as rc6_bar_los4,
               (case accom_class_id
                    when @rc6 then
                        bar_los5
                end
               ) as rc6_bar_los5,
               (case accom_class_id
                    when @rc6 then
                        bar_los6
                end
               ) as rc6_bar_los6,
               (case accom_class_id
                    when @rc6 then
                        bar_los7
                end
               ) as rc6_bar_los7,
               (case accom_class_id
                    when @rc6 then
                        bar_los8
                end
               ) as rc6_bar_los8,
               (case accom_class_id
                    when @rc7 then
                        bar_los1
                end
               ) as rc7_bar_los1,
               (case accom_class_id
                    when @rc7 then
                        bar_los2
                end
               ) as rc7_bar_los2,
               (case accom_class_id
                    when @rc7 then
                        bar_los3
                end
               ) as rc7_bar_los3,
               (case accom_class_id
                    when @rc7 then
                        bar_los4
                end
               ) as rc7_bar_los4,
               (case accom_class_id
                    when @rc7 then
                        bar_los5
                end
               ) as rc7_bar_los5,
               (case accom_class_id
                    when @rc7 then
                        bar_los6
                end
               ) as rc7_bar_los6,
               (case accom_class_id
                    when @rc7 then
                        bar_los7
                end
               ) as rc7_bar_los7,
               (case accom_class_id
                    when @rc7 then
                        bar_los8
                end
               ) as rc7_bar_los8,
               (case accom_class_id
                    when @rc8 then
                        bar_los1
                end
               ) as rc8_bar_los1,
               (case accom_class_id
                    when @rc8 then
                        bar_los2
                end
               ) as rc8_bar_los2,
               (case accom_class_id
                    when @rc8 then
                        bar_los3
                end
               ) as rc8_bar_los3,
               (case accom_class_id
                    when @rc8 then
                        bar_los4
                end
               ) as rc8_bar_los4,
               (case accom_class_id
                    when @rc8 then
                        bar_los5
                end
               ) as rc8_bar_los5,
               (case accom_class_id
                    when @rc8 then
                        bar_los6
                end
               ) as rc8_bar_los6,
               (case accom_class_id
                    when @rc8 then
                        bar_los7
                end
               ) as rc8_bar_los7,
               (case accom_class_id
                    when @rc8 then
                        bar_los8
                end
               ) as rc8_bar_los8,
               (case accom_class_id
                    when @rc9 then
                        bar_los1
                end
               ) as rc9_bar_los1,
               (case accom_class_id
                    when @rc9 then
                        bar_los2
                end
               ) as rc9_bar_los2,
               (case accom_class_id
                    when @rc9 then
                        bar_los3
                end
               ) as rc9_bar_los3,
               (case accom_class_id
                    when @rc9 then
                        bar_los4
                end
               ) as rc9_bar_los4,
               (case accom_class_id
                    when @rc9 then
                        bar_los5
                end
               ) as rc9_bar_los5,
               (case accom_class_id
                    when @rc9 then
                        bar_los6
                end
               ) as rc9_bar_los6,
               (case accom_class_id
                    when @rc9 then
                        bar_los7
                end
               ) as rc9_bar_los7,
               (case accom_class_id
                    when @rc9 then
                        bar_los8
                end
               ) as rc9_bar_los8,
               (case accom_class_id
                    when @rc10 then
                        bar_los1
                end
               ) as rc10_bar_los1,
               (case accom_class_id
                    when @rc10 then
                        bar_los2
                end
               ) as rc10_bar_los2,
               (case accom_class_id
                    when @rc10 then
                        bar_los3
                end
               ) as rc10_bar_los3,
               (case accom_class_id
                    when @rc10 then
                        bar_los4
                end
               ) as rc10_bar_los4,
               (case accom_class_id
                    when @rc10 then
                        bar_los5
                end
               ) as rc10_bar_los5,
               (case accom_class_id
                    when @rc10 then
                        bar_los6
                end
               ) as rc10_bar_los6,
               (case accom_class_id
                    when @rc10 then
                        bar_los7
                end
               ) as rc10_bar_los7,
               (case accom_class_id
                    when @rc10 then
                        bar_los8
                end
               ) as rc10_bar_los8,
               (case accom_class_id
                    when @rc1 then
                        bar_by_day
                end
               ) as rc1_bar_by_day,
               (case accom_class_id
                    when @rc2 then
                        bar_by_day
                end
               ) as rc2_bar_by_day,
               (case accom_class_id
                    when @rc3 then
                        bar_by_day
                end
               ) as rc3_bar_by_day,
               (case accom_class_id
                    when @rc4 then
                        bar_by_day
                end
               ) as rc4_bar_by_day,
               (case accom_class_id
                    when @rc5 then
                        bar_by_day
                end
               ) as rc5_bar_by_day,
               (case accom_class_id
                    when @rc6 then
                        bar_by_day
                end
               ) as rc6_bar_by_day,
               (case accom_class_id
                    when @rc7 then
                        bar_by_day
                end
               ) as rc7_bar_by_day,
               (case accom_class_id
                    when @rc8 then
                        bar_by_day
                end
               ) as rc8_bar_by_day,
               (case accom_class_id
                    when @rc9 then
                        bar_by_day
                end
               ) as rc9_bar_by_day,
               (case accom_class_id
                    when @rc10 then
                        bar_by_day
                end
               ) as rc10_bar_by_day,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los1
                end
               ) as rc1_ratecode_los1,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los2
                end
               ) as rc1_ratecode_los2,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los3
                end
               ) as rc1_ratecode_los3,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los4
                end
               ) as rc1_ratecode_los4,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los5
                end
               ) as rc1_ratecode_los5,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los6
                end
               ) as rc1_ratecode_los6,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los7
                end
               ) as rc1_ratecode_los7,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los8
                end
               ) as rc1_ratecode_los8,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los1
                end
               ) as rc2_ratecode_los1,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los2
                end
               ) as rc2_ratecode_los2,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los3
                end
               ) as rc2_ratecode_los3,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los4
                end
               ) as rc2_ratecode_los4,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los5
                end
               ) as rc2_ratecode_los5,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los6
                end
               ) as rc2_ratecode_los6,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los7
                end
               ) as rc2_ratecode_los7,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los8
                end
               ) as rc2_ratecode_los8,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los1
                end
               ) as rc3_ratecode_los1,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los2
                end
               ) as rc3_ratecode_los2,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los3
                end
               ) as rc3_ratecode_los3,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los4
                end
               ) as rc3_ratecode_los4,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los5
                end
               ) as rc3_ratecode_los5,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los6
                end
               ) as rc3_ratecode_los6,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los7
                end
               ) as rc3_ratecode_los7,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los8
                end
               ) as rc3_ratecode_los8,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los1
                end
               ) as rc4_ratecode_los1,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los2
                end
               ) as rc4_ratecode_los2,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los3
                end
               ) as rc4_ratecode_los3,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los4
                end
               ) as rc4_ratecode_los4,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los5
                end
               ) as rc4_ratecode_los5,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los6
                end
               ) as rc4_ratecode_los6,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los7
                end
               ) as rc4_ratecode_los7,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los8
                end
               ) as rc4_ratecode_los8,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los1
                end
               ) as rc5_ratecode_los1,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los2
                end
               ) as rc5_ratecode_los2,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los3
                end
               ) as rc5_ratecode_los3,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los4
                end
               ) as rc5_ratecode_los4,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los5
                end
               ) as rc5_ratecode_los5,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los6
                end
               ) as rc5_ratecode_los6,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los7
                end
               ) as rc5_ratecode_los7,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los8
                end
               ) as rc5_ratecode_los8,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los1
                end
               ) as rc6_ratecode_los1,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los2
                end
               ) as rc6_ratecode_los2,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los3
                end
               ) as rc6_ratecode_los3,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los4
                end
               ) as rc6_ratecode_los4,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los5
                end
               ) as rc6_ratecode_los5,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los6
                end
               ) as rc6_ratecode_los6,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los7
                end
               ) as rc6_ratecode_los7,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los8
                end
               ) as rc6_ratecode_los8,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los1
                end
               ) as rc7_ratecode_los1,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los2
                end
               ) as rc7_ratecode_los2,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los3
                end
               ) as rc7_ratecode_los3,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los4
                end
               ) as rc7_ratecode_los4,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los5
                end
               ) as rc7_ratecode_los5,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los6
                end
               ) as rc7_ratecode_los6,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los7
                end
               ) as rc7_ratecode_los7,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los8
                end
               ) as rc7_ratecode_los8,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los1
                end
               ) as rc8_ratecode_los1,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los2
                end
               ) as rc8_ratecode_los2,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los3
                end
               ) as rc8_ratecode_los3,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los4
                end
               ) as rc8_ratecode_los4,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los5
                end
               ) as rc8_ratecode_los5,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los6
                end
               ) as rc8_ratecode_los6,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los7
                end
               ) as rc8_ratecode_los7,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los8
                end
               ) as rc8_ratecode_los8,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los1
                end
               ) as rc9_ratecode_los1,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los2
                end
               ) as rc9_ratecode_los2,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los3
                end
               ) as rc9_ratecode_los3,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los4
                end
               ) as rc9_ratecode_los4,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los5
                end
               ) as rc9_ratecode_los5,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los6
                end
               ) as rc9_ratecode_los6,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los7
                end
               ) as rc9_ratecode_los7,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los8
                end
               ) as rc9_ratecode_los8,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los1
                end
               ) as rc10_ratecode_los1,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los2
                end
               ) as rc10_ratecode_los2,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los3
                end
               ) as rc10_ratecode_los3,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los4
                end
               ) as rc10_ratecode_los4,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los5
                end
               ) as rc10_ratecode_los5,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los6
                end
               ) as rc10_ratecode_los6,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los7
                end
               ) as rc10_ratecode_los7,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los8
                end
               ) as rc10_ratecode_los8,
               (case accom_class_id
                    when @rc1 then
                        bar_los1_change
                end
               ) as rc1_bar_los1_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los2_change
                end
               ) as rc1_bar_los2_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los3_change
                end
               ) as rc1_bar_los3_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los4_change
                end
               ) as rc1_bar_los4_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los5_change
                end
               ) as rc1_bar_los5_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los6_change
                end
               ) as rc1_bar_los6_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los7_change
                end
               ) as rc1_bar_los7_change,
               (case accom_class_id
                    when @rc1 then
                        bar_los8_change
                end
               ) as rc1_bar_los8_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los1_change
                end
               ) as rc2_bar_los1_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los2_change
                end
               ) as rc2_bar_los2_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los3_change
                end
               ) as rc2_bar_los3_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los4_change
                end
               ) as rc2_bar_los4_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los5_change
                end
               ) as rc2_bar_los5_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los6_change
                end
               ) as rc2_bar_los6_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los7_change
                end
               ) as rc2_bar_los7_change,
               (case accom_class_id
                    when @rc2 then
                        bar_los8_change
                end
               ) as rc2_bar_los8_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los1_change
                end
               ) as rc3_bar_los1_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los2_change
                end
               ) as rc3_bar_los2_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los3_change
                end
               ) as rc3_bar_los3_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los4_change
                end
               ) as rc3_bar_los4_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los5_change
                end
               ) as rc3_bar_los5_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los6_change
                end
               ) as rc3_bar_los6_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los7_change
                end
               ) as rc3_bar_los7_change,
               (case accom_class_id
                    when @rc3 then
                        bar_los8_change
                end
               ) as rc3_bar_los8_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los1_change
                end
               ) as rc4_bar_los1_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los2_change
                end
               ) as rc4_bar_los2_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los3_change
                end
               ) as rc4_bar_los3_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los4_change
                end
               ) as rc4_bar_los4_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los5_change
                end
               ) as rc4_bar_los5_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los6_change
                end
               ) as rc4_bar_los6_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los7_change
                end
               ) as rc4_bar_los7_change,
               (case accom_class_id
                    when @rc4 then
                        bar_los8_change
                end
               ) as rc4_bar_los8_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los1_change
                end
               ) as rc5_bar_los1_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los2_change
                end
               ) as rc5_bar_los2_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los3_change
                end
               ) as rc5_bar_los3_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los4_change
                end
               ) as rc5_bar_los4_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los5_change
                end
               ) as rc5_bar_los5_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los6_change
                end
               ) as rc5_bar_los6_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los7_change
                end
               ) as rc5_bar_los7_change,
               (case accom_class_id
                    when @rc5 then
                        bar_los8_change
                end
               ) as rc5_bar_los8_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los1_change
                end
               ) as rc6_bar_los1_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los2_change
                end
               ) as rc6_bar_los2_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los3_change
                end
               ) as rc6_bar_los3_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los4_change
                end
               ) as rc6_bar_los4_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los5_change
                end
               ) as rc6_bar_los5_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los6_change
                end
               ) as rc6_bar_los6_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los7_change
                end
               ) as rc6_bar_los7_change,
               (case accom_class_id
                    when @rc6 then
                        bar_los8_change
                end
               ) as rc6_bar_los8_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los1_change
                end
               ) as rc7_bar_los1_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los2_change
                end
               ) as rc7_bar_los2_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los3_change
                end
               ) as rc7_bar_los3_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los4_change
                end
               ) as rc7_bar_los4_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los5_change
                end
               ) as rc7_bar_los5_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los6_change
                end
               ) as rc7_bar_los6_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los7_change
                end
               ) as rc7_bar_los7_change,
               (case accom_class_id
                    when @rc7 then
                        bar_los8_change
                end
               ) as rc7_bar_los8_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los1_change
                end
               ) as rc8_bar_los1_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los2_change
                end
               ) as rc8_bar_los2_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los3_change
                end
               ) as rc8_bar_los3_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los4_change
                end
               ) as rc8_bar_los4_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los5_change
                end
               ) as rc8_bar_los5_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los6_change
                end
               ) as rc8_bar_los6_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los7_change
                end
               ) as rc8_bar_los7_change,
               (case accom_class_id
                    when @rc8 then
                        bar_los8_change
                end
               ) as rc8_bar_los8_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los1_change
                end
               ) as rc9_bar_los1_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los2_change
                end
               ) as rc9_bar_los2_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los3_change
                end
               ) as rc9_bar_los3_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los4_change
                end
               ) as rc9_bar_los4_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los5_change
                end
               ) as rc9_bar_los5_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los6_change
                end
               ) as rc9_bar_los6_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los7_change
                end
               ) as rc9_bar_los7_change,
               (case accom_class_id
                    when @rc9 then
                        bar_los8_change
                end
               ) as rc9_bar_los8_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los1_change
                end
               ) as rc10_bar_los1_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los2_change
                end
               ) as rc10_bar_los2_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los3_change
                end
               ) as rc10_bar_los3_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los4_change
                end
               ) as rc10_bar_los4_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los5_change
                end
               ) as rc10_bar_los5_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los6_change
                end
               ) as rc10_bar_los6_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los7_change
                end
               ) as rc10_bar_los7_change,
               (case accom_class_id
                    when @rc10 then
                        bar_los8_change
                end
               ) as rc10_bar_los8_change,
               (case accom_class_id
                    when @rc1 then
                        ratecode_los_all
                end
               ) as rc1_ratecode_los_all,
               (case accom_class_id
                    when @rc2 then
                        ratecode_los_all
                end
               ) as rc2_ratecode_los_all,
               (case accom_class_id
                    when @rc3 then
                        ratecode_los_all
                end
               ) as rc3_ratecode_los_all,
               (case accom_class_id
                    when @rc4 then
                        ratecode_los_all
                end
               ) as rc4_ratecode_los_all,
               (case accom_class_id
                    when @rc5 then
                        ratecode_los_all
                end
               ) as rc5_ratecode_los_all,
               (case accom_class_id
                    when @rc6 then
                        ratecode_los_all
                end
               ) as rc6_ratecode_los_all,
               (case accom_class_id
                    when @rc7 then
                        ratecode_los_all
                end
               ) as rc7_ratecode_los_all,
               (case accom_class_id
                    when @rc8 then
                        ratecode_los_all
                end
               ) as rc8_ratecode_los_all,
               (case accom_class_id
                    when @rc9 then
                        ratecode_los_all
                end
               ) as rc9_ratecode_los_all,
               (case accom_class_id
                    when @rc10 then
                        ratecode_los_all
                end
               ) as rc10_ratecode_los_all,
               (case accom_class_id
                    when @rc1 then
                        bar_by_day_change
                end
               ) as rc1_bar_by_day_change,
               (case accom_class_id
                    when @rc2 then
                        bar_by_day_change
                end
               ) as rc2_bar_by_day_change,
               (case accom_class_id
                    when @rc3 then
                        bar_by_day_change
                end
               ) as rc3_bar_by_day_change,
               (case accom_class_id
                    when @rc4 then
                        bar_by_day_change
                end
               ) as rc4_bar_by_day_change,
               (case accom_class_id
                    when @rc5 then
                        bar_by_day_change
                end
               ) as rc5_bar_by_day_change,
               (case accom_class_id
                    when @rc6 then
                        bar_by_day_change
                end
               ) as rc6_bar_by_day_change,
               (case accom_class_id
                    when @rc7 then
                        bar_by_day_change
                end
               ) as rc7_bar_by_day_change,
               (case accom_class_id
                    when @rc8 then
                        bar_by_day_change
                end
               ) as rc8_bar_by_day_change,
               (case accom_class_id
                    when @rc9 then
                        bar_by_day_change
                end
               ) as rc9_bar_by_day_change,
               (case accom_class_id
                    when @rc10 then
                        bar_by_day_change
                end
               ) as rc10_bar_by_day_change,
               (case accom_class_id
                    when @rc1 then
                        comp1_rate
                end
               ) as rc1_comp1_rate,
               (case accom_class_id
                    when @rc1 then
                        comp2_rate
                end
               ) as rc1_comp2_rate,
               (case accom_class_id
                    when @rc1 then
                        comp3_rate
                end
               ) as rc1_comp3_rate,
               (case accom_class_id
                    when @rc1 then
                        comp4_rate
                end
               ) as rc1_comp4_rate,
               (case accom_class_id
                    when @rc1 then
                        comp5_rate
                end
               ) as rc1_comp5_rate,
               (case accom_class_id
                    when @rc1 then
                        comp6_rate
                end
               ) as rc1_comp6_rate,
               (case accom_class_id
                    when @rc1 then
                        comp7_rate
                end
               ) as rc1_comp7_rate,
               (case accom_class_id
                    when @rc1 then
                        comp8_rate
                end
               ) as rc1_comp8_rate,
               (case accom_class_id
                    when @rc1 then
                        comp9_rate
                end
               ) as rc1_comp9_rate,
               (case accom_class_id
                    when @rc1 then
                        comp10_rate
                end
               ) as rc1_comp10_rate,
               (case accom_class_id
                    when @rc1 then
                        comp11_rate
                end
               ) as rc1_comp11_rate,
               (case accom_class_id
                    when @rc1 then
                        comp12_rate
                end
               ) as rc1_comp12_rate,
               (case accom_class_id
                    when @rc1 then
                        comp13_rate
                end
               ) as rc1_comp13_rate,
               (case accom_class_id
                    when @rc1 then
                        comp14_rate
                end
               ) as rc1_comp14_rate,
               (case accom_class_id
                    when @rc1 then
                        comp15_rate
                end
               ) as rc1_comp15_rate,
               (case accom_class_id
                    when @rc2 then
                        comp1_rate
                end
               ) as rc2_comp1_rate,
               (case accom_class_id
                    when @rc2 then
                        comp2_rate
                end
               ) as rc2_comp2_rate,
               (case accom_class_id
                    when @rc2 then
                        comp3_rate
                end
               ) as rc2_comp3_rate,
               (case accom_class_id
                    when @rc2 then
                        comp4_rate
                end
               ) as rc2_comp4_rate,
               (case accom_class_id
                    when @rc2 then
                        comp5_rate
                end
               ) as rc2_comp5_rate,
               (case accom_class_id
                    when @rc2 then
                        comp6_rate
                end
               ) as rc2_comp6_rate,
               (case accom_class_id
                    when @rc2 then
                        comp7_rate
                end
               ) as rc2_comp7_rate,
               (case accom_class_id
                    when @rc2 then
                        comp8_rate
                end
               ) as rc2_comp8_rate,
               (case accom_class_id
                    when @rc2 then
                        comp9_rate
                end
               ) as rc2_comp9_rate,
               (case accom_class_id
                    when @rc2 then
                        comp10_rate
                end
               ) as rc2_comp10_rate,
               (case accom_class_id
                    when @rc2 then
                        comp11_rate
                end
               ) as rc2_comp11_rate,
               (case accom_class_id
                    when @rc2 then
                        comp12_rate
                end
               ) as rc2_comp12_rate,
               (case accom_class_id
                    when @rc2 then
                        comp13_rate
                end
               ) as rc2_comp13_rate,
               (case accom_class_id
                    when @rc2 then
                        comp14_rate
                end
               ) as rc2_comp14_rate,
               (case accom_class_id
                    when @rc2 then
                        comp15_rate
                end
               ) as rc2_comp15_rate,
               (case accom_class_id
                    when @rc3 then
                        comp1_rate
                end
               ) as rc3_comp1_rate,
               (case accom_class_id
                    when @rc3 then
                        comp2_rate
                end
               ) as rc3_comp2_rate,
               (case accom_class_id
                    when @rc3 then
                        comp3_rate
                end
               ) as rc3_comp3_rate,
               (case accom_class_id
                    when @rc3 then
                        comp4_rate
                end
               ) as rc3_comp4_rate,
               (case accom_class_id
                    when @rc3 then
                        comp5_rate
                end
               ) as rc3_comp5_rate,
               (case accom_class_id
                    when @rc3 then
                        comp6_rate
                end
               ) as rc3_comp6_rate,
               (case accom_class_id
                    when @rc3 then
                        comp7_rate
                end
               ) as rc3_comp7_rate,
               (case accom_class_id
                    when @rc3 then
                        comp8_rate
                end
               ) as rc3_comp8_rate,
               (case accom_class_id
                    when @rc3 then
                        comp9_rate
                end
               ) as rc3_comp9_rate,
               (case accom_class_id
                    when @rc3 then
                        comp10_rate
                end
               ) as rc3_comp10_rate,
               (case accom_class_id
                    when @rc3 then
                        comp11_rate
                end
               ) as rc3_comp11_rate,
               (case accom_class_id
                    when @rc3 then
                        comp12_rate
                end
               ) as rc3_comp12_rate,
               (case accom_class_id
                    when @rc3 then
                        comp13_rate
                end
               ) as rc3_comp13_rate,
               (case accom_class_id
                    when @rc3 then
                        comp14_rate
                end
               ) as rc3_comp14_rate,
               (case accom_class_id
                    when @rc3 then
                        comp15_rate
                end
               ) as rc3_comp15_rate,
               (case accom_class_id
                    when @rc4 then
                        comp1_rate
                end
               ) as rc4_comp1_rate,
               (case accom_class_id
                    when @rc4 then
                        comp2_rate
                end
               ) as rc4_comp2_rate,
               (case accom_class_id
                    when @rc4 then
                        comp3_rate
                end
               ) as rc4_comp3_rate,
               (case accom_class_id
                    when @rc4 then
                        comp4_rate
                end
               ) as rc4_comp4_rate,
               (case accom_class_id
                    when @rc4 then
                        comp5_rate
                end
               ) as rc4_comp5_rate,
               (case accom_class_id
                    when @rc4 then
                        comp6_rate
                end
               ) as rc4_comp6_rate,
               (case accom_class_id
                    when @rc4 then
                        comp7_rate
                end
               ) as rc4_comp7_rate,
               (case accom_class_id
                    when @rc4 then
                        comp8_rate
                end
               ) as rc4_comp8_rate,
               (case accom_class_id
                    when @rc4 then
                        comp9_rate
                end
               ) as rc4_comp9_rate,
               (case accom_class_id
                    when @rc4 then
                        comp10_rate
                end
               ) as rc4_comp10_rate,
               (case accom_class_id
                    when @rc4 then
                        comp11_rate
                end
               ) as rc4_comp11_rate,
               (case accom_class_id
                    when @rc4 then
                        comp12_rate
                end
               ) as rc4_comp12_rate,
               (case accom_class_id
                    when @rc4 then
                        comp13_rate
                end
               ) as rc4_comp13_rate,
               (case accom_class_id
                    when @rc4 then
                        comp14_rate
                end
               ) as rc4_comp14_rate,
               (case accom_class_id
                    when @rc4 then
                        comp15_rate
                end
               ) as rc4_comp15_rate,
               (case accom_class_id
                    when @rc5 then
                        comp1_rate
                end
               ) as rc5_comp1_rate,
               (case accom_class_id
                    when @rc5 then
                        comp2_rate
                end
               ) as rc5_comp2_rate,
               (case accom_class_id
                    when @rc5 then
                        comp3_rate
                end
               ) as rc5_comp3_rate,
               (case accom_class_id
                    when @rc5 then
                        comp4_rate
                end
               ) as rc5_comp4_rate,
               (case accom_class_id
                    when @rc5 then
                        comp5_rate
                end
               ) as rc5_comp5_rate,
               (case accom_class_id
                    when @rc5 then
                        comp6_rate
                end
               ) as rc5_comp6_rate,
               (case accom_class_id
                    when @rc5 then
                        comp7_rate
                end
               ) as rc5_comp7_rate,
               (case accom_class_id
                    when @rc5 then
                        comp8_rate
                end
               ) as rc5_comp8_rate,
               (case accom_class_id
                    when @rc5 then
                        comp9_rate
                end
               ) as rc5_comp9_rate,
               (case accom_class_id
                    when @rc5 then
                        comp10_rate
                end
               ) as rc5_comp10_rate,
               (case accom_class_id
                    when @rc5 then
                        comp11_rate
                end
               ) as rc5_comp11_rate,
               (case accom_class_id
                    when @rc5 then
                        comp12_rate
                end
               ) as rc5_comp12_rate,
               (case accom_class_id
                    when @rc5 then
                        comp13_rate
                end
               ) as rc5_comp13_rate,
               (case accom_class_id
                    when @rc5 then
                        comp14_rate
                end
               ) as rc5_comp14_rate,
               (case accom_class_id
                    when @rc5 then
                        comp15_rate
                end
               ) as rc5_comp15_rate,
               (case accom_class_id
                    when @rc6 then
                        comp1_rate
                end
               ) as rc6_comp1_rate,
               (case accom_class_id
                    when @rc6 then
                        comp2_rate
                end
               ) as rc6_comp2_rate,
               (case accom_class_id
                    when @rc6 then
                        comp3_rate
                end
               ) as rc6_comp3_rate,
               (case accom_class_id
                    when @rc6 then
                        comp4_rate
                end
               ) as rc6_comp4_rate,
               (case accom_class_id
                    when @rc6 then
                        comp5_rate
                end
               ) as rc6_comp5_rate,
               (case accom_class_id
                    when @rc6 then
                        comp6_rate
                end
               ) as rc6_comp6_rate,
               (case accom_class_id
                    when @rc6 then
                        comp7_rate
                end
               ) as rc6_comp7_rate,
               (case accom_class_id
                    when @rc6 then
                        comp8_rate
                end
               ) as rc6_comp8_rate,
               (case accom_class_id
                    when @rc6 then
                        comp9_rate
                end
               ) as rc6_comp9_rate,
               (case accom_class_id
                    when @rc6 then
                        comp10_rate
                end
               ) as rc6_comp10_rate,
               (case accom_class_id
                    when @rc6 then
                        comp11_rate
                end
               ) as rc6_comp11_rate,
               (case accom_class_id
                    when @rc6 then
                        comp12_rate
                end
               ) as rc6_comp12_rate,
               (case accom_class_id
                    when @rc6 then
                        comp13_rate
                end
               ) as rc6_comp13_rate,
               (case accom_class_id
                    when @rc6 then
                        comp14_rate
                end
               ) as rc6_comp14_rate,
               (case accom_class_id
                    when @rc6 then
                        comp15_rate
                end
               ) as rc6_comp15_rate,
               (case accom_class_id
                    when @rc7 then
                        comp1_rate
                end
               ) as rc7_comp1_rate,
               (case accom_class_id
                    when @rc7 then
                        comp2_rate
                end
               ) as rc7_comp2_rate,
               (case accom_class_id
                    when @rc7 then
                        comp3_rate
                end
               ) as rc7_comp3_rate,
               (case accom_class_id
                    when @rc7 then
                        comp4_rate
                end
               ) as rc7_comp4_rate,
               (case accom_class_id
                    when @rc7 then
                        comp5_rate
                end
               ) as rc7_comp5_rate,
               (case accom_class_id
                    when @rc7 then
                        comp6_rate
                end
               ) as rc7_comp6_rate,
               (case accom_class_id
                    when @rc7 then
                        comp7_rate
                end
               ) as rc7_comp7_rate,
               (case accom_class_id
                    when @rc7 then
                        comp8_rate
                end
               ) as rc7_comp8_rate,
               (case accom_class_id
                    when @rc7 then
                        comp9_rate
                end
               ) as rc7_comp9_rate,
               (case accom_class_id
                    when @rc7 then
                        comp10_rate
                end
               ) as rc7_comp10_rate,
               (case accom_class_id
                    when @rc7 then
                        comp11_rate
                end
               ) as rc7_comp11_rate,
               (case accom_class_id
                    when @rc7 then
                        comp12_rate
                end
               ) as rc7_comp12_rate,
               (case accom_class_id
                    when @rc7 then
                        comp13_rate
                end
               ) as rc7_comp13_rate,
               (case accom_class_id
                    when @rc7 then
                        comp14_rate
                end
               ) as rc7_comp14_rate,
               (case accom_class_id
                    when @rc7 then
                        comp15_rate
                end
               ) as rc7_comp15_rate,
               (case accom_class_id
                    when @rc8 then
                        comp1_rate
                end
               ) as rc8_comp1_rate,
               (case accom_class_id
                    when @rc8 then
                        comp2_rate
                end
               ) as rc8_comp2_rate,
               (case accom_class_id
                    when @rc8 then
                        comp3_rate
                end
               ) as rc8_comp3_rate,
               (case accom_class_id
                    when @rc8 then
                        comp4_rate
                end
               ) as rc8_comp4_rate,
               (case accom_class_id
                    when @rc8 then
                        comp5_rate
                end
               ) as rc8_comp5_rate,
               (case accom_class_id
                    when @rc8 then
                        comp6_rate
                end
               ) as rc8_comp6_rate,
               (case accom_class_id
                    when @rc8 then
                        comp7_rate
                end
               ) as rc8_comp7_rate,
               (case accom_class_id
                    when @rc8 then
                        comp8_rate
                end
               ) as rc8_comp8_rate,
               (case accom_class_id
                    when @rc8 then
                        comp9_rate
                end
               ) as rc8_comp9_rate,
               (case accom_class_id
                    when @rc8 then
                        comp10_rate
                end
               ) as rc8_comp10_rate,
               (case accom_class_id
                    when @rc8 then
                        comp11_rate
                end
               ) as rc8_comp11_rate,
               (case accom_class_id
                    when @rc8 then
                        comp12_rate
                end
               ) as rc8_comp12_rate,
               (case accom_class_id
                    when @rc8 then
                        comp13_rate
                end
               ) as rc8_comp13_rate,
               (case accom_class_id
                    when @rc8 then
                        comp14_rate
                end
               ) as rc8_comp14_rate,
               (case accom_class_id
                    when @rc8 then
                        comp15_rate
                end
               ) as rc8_comp15_rate,
               (case accom_class_id
                    when @rc9 then
                        comp1_rate
                end
               ) as rc9_comp1_rate,
               (case accom_class_id
                    when @rc9 then
                        comp2_rate
                end
               ) as rc9_comp2_rate,
               (case accom_class_id
                    when @rc9 then
                        comp3_rate
                end
               ) as rc9_comp3_rate,
               (case accom_class_id
                    when @rc9 then
                        comp4_rate
                end
               ) as rc9_comp4_rate,
               (case accom_class_id
                    when @rc9 then
                        comp5_rate
                end
               ) as rc9_comp5_rate,
               (case accom_class_id
                    when @rc9 then
                        comp6_rate
                end
               ) as rc9_comp6_rate,
               (case accom_class_id
                    when @rc9 then
                        comp7_rate
                end
               ) as rc9_comp7_rate,
               (case accom_class_id
                    when @rc9 then
                        comp8_rate
                end
               ) as rc9_comp8_rate,
               (case accom_class_id
                    when @rc9 then
                        comp9_rate
                end
               ) as rc9_comp9_rate,
               (case accom_class_id
                    when @rc9 then
                        comp10_rate
                end
               ) as rc9_comp10_rate,
               (case accom_class_id
                    when @rc9 then
                        comp11_rate
                end
               ) as rc9_comp11_rate,
               (case accom_class_id
                    when @rc9 then
                        comp12_rate
                end
               ) as rc9_comp12_rate,
               (case accom_class_id
                    when @rc9 then
                        comp13_rate
                end
               ) as rc9_comp13_rate,
               (case accom_class_id
                    when @rc9 then
                        comp14_rate
                end
               ) as rc9_comp14_rate,
               (case accom_class_id
                    when @rc9 then
                        comp15_rate
                end
               ) as rc9_comp15_rate,
               (case accom_class_id
                    when @rc10 then
                        comp1_rate
                end
               ) as rc10_comp1_rate,
               (case accom_class_id
                    when @rc10 then
                        comp2_rate
                end
               ) as rc10_comp2_rate,
               (case accom_class_id
                    when @rc10 then
                        comp3_rate
                end
               ) as rc10_comp3_rate,
               (case accom_class_id
                    when @rc10 then
                        comp4_rate
                end
               ) as rc10_comp4_rate,
               (case accom_class_id
                    when @rc10 then
                        comp5_rate
                end
               ) as rc10_comp5_rate,
               (case accom_class_id
                    when @rc10 then
                        comp6_rate
                end
               ) as rc10_comp6_rate,
               (case accom_class_id
                    when @rc10 then
                        comp7_rate
                end
               ) as rc10_comp7_rate,
               (case accom_class_id
                    when @rc10 then
                        comp8_rate
                end
               ) as rc10_comp8_rate,
               (case accom_class_id
                    when @rc10 then
                        comp9_rate
                end
               ) as rc10_comp9_rate,
               (case accom_class_id
                    when @rc10 then
                        comp10_rate
                end
               ) as rc10_comp10_rate,
               (case accom_class_id
                    when @rc10 then
                        comp11_rate
                end
               ) as rc10_comp11_rate,
               (case accom_class_id
                    when @rc10 then
                        comp12_rate
                end
               ) as rc10_comp12_rate,
               (case accom_class_id
                    when @rc10 then
                        comp13_rate
                end
               ) as rc10_comp13_rate,
               (case accom_class_id
                    when @rc10 then
                        comp14_rate
                end
               ) as rc10_comp14_rate,
               (case accom_class_id
                    when @rc10 then
                        comp15_rate
                end
               ) as rc10_comp15_rate,
               (case accom_class_id
                    when @rc1 then
                        comp1_name
                end
               ) as rc1_comp1_name,
               (case accom_class_id
                    when @rc1 then
                        comp2_name
                end
               ) as rc1_comp2_name,
               (case accom_class_id
                    when @rc1 then
                        comp3_name
                end
               ) as rc1_comp3_name,
               (case accom_class_id
                    when @rc1 then
                        comp4_name
                end
               ) as rc1_comp4_name,
               (case accom_class_id
                    when @rc1 then
                        comp5_name
                end
               ) as rc1_comp5_name,
               (case accom_class_id
                    when @rc1 then
                        comp6_name
                end
               ) as rc1_comp6_name,
               (case accom_class_id
                    when @rc1 then
                        comp7_name
                end
               ) as rc1_comp7_name,
               (case accom_class_id
                    when @rc1 then
                        comp8_name
                end
               ) as rc1_comp8_name,
               (case accom_class_id
                    when @rc1 then
                        comp9_name
                end
               ) as rc1_comp9_name,
               (case accom_class_id
                    when @rc1 then
                        comp10_name
                end
               ) as rc1_comp10_name,
               (case accom_class_id
                    when @rc1 then
                        comp11_name
                end
               ) as rc1_comp11_name,
               (case accom_class_id
                    when @rc1 then
                        comp12_name
                end
               ) as rc1_comp12_name,
               (case accom_class_id
                    when @rc1 then
                        comp13_name
                end
               ) as rc1_comp13_name,
               (case accom_class_id
                    when @rc1 then
                        comp14_name
                end
               ) as rc1_comp14_name,
               (case accom_class_id
                    when @rc1 then
                        comp15_name
                end
               ) as rc1_comp15_name,
               (case accom_class_id
                    when @rc2 then
                        comp1_name
                end
               ) as rc2_comp1_name,
               (case accom_class_id
                    when @rc2 then
                        comp2_name
                end
               ) as rc2_comp2_name,
               (case accom_class_id
                    when @rc2 then
                        comp3_name
                end
               ) as rc2_comp3_name,
               (case accom_class_id
                    when @rc2 then
                        comp4_name
                end
               ) as rc2_comp4_name,
               (case accom_class_id
                    when @rc2 then
                        comp5_name
                end
               ) as rc2_comp5_name,
               (case accom_class_id
                    when @rc2 then
                        comp6_name
                end
               ) as rc2_comp6_name,
               (case accom_class_id
                    when @rc2 then
                        comp7_name
                end
               ) as rc2_comp7_name,
               (case accom_class_id
                    when @rc2 then
                        comp8_name
                end
               ) as rc2_comp8_name,
               (case accom_class_id
                    when @rc2 then
                        comp9_name
                end
               ) as rc2_comp9_name,
               (case accom_class_id
                    when @rc2 then
                        comp10_name
                end
               ) as rc2_comp10_name,
               (case accom_class_id
                    when @rc2 then
                        comp11_name
                end
               ) as rc2_comp11_name,
               (case accom_class_id
                    when @rc2 then
                        comp12_name
                end
               ) as rc2_comp12_name,
               (case accom_class_id
                    when @rc2 then
                        comp13_name
                end
               ) as rc2_comp13_name,
               (case accom_class_id
                    when @rc2 then
                        comp14_name
                end
               ) as rc2_comp14_name,
               (case accom_class_id
                    when @rc2 then
                        comp15_name
                end
               ) as rc2_comp15_name,
               (case accom_class_id
                    when @rc3 then
                        comp1_name
                end
               ) as rc3_comp1_name,
               (case accom_class_id
                    when @rc3 then
                        comp2_name
                end
               ) as rc3_comp2_name,
               (case accom_class_id
                    when @rc3 then
                        comp3_name
                end
               ) as rc3_comp3_name,
               (case accom_class_id
                    when @rc3 then
                        comp4_name
                end
               ) as rc3_comp4_name,
               (case accom_class_id
                    when @rc3 then
                        comp5_name
                end
               ) as rc3_comp5_name,
               (case accom_class_id
                    when @rc3 then
                        comp6_name
                end
               ) as rc3_comp6_name,
               (case accom_class_id
                    when @rc3 then
                        comp7_name
                end
               ) as rc3_comp7_name,
               (case accom_class_id
                    when @rc3 then
                        comp8_name
                end
               ) as rc3_comp8_name,
               (case accom_class_id
                    when @rc3 then
                        comp9_name
                end
               ) as rc3_comp9_name,
               (case accom_class_id
                    when @rc3 then
                        comp10_name
                end
               ) as rc3_comp10_name,
               (case accom_class_id
                    when @rc3 then
                        comp11_name
                end
               ) as rc3_comp11_name,
               (case accom_class_id
                    when @rc3 then
                        comp12_name
                end
               ) as rc3_comp12_name,
               (case accom_class_id
                    when @rc3 then
                        comp13_name
                end
               ) as rc3_comp13_name,
               (case accom_class_id
                    when @rc3 then
                        comp14_name
                end
               ) as rc3_comp14_name,
               (case accom_class_id
                    when @rc3 then
                        comp15_name
                end
               ) as rc3_comp15_name,
               (case accom_class_id
                    when @rc4 then
                        comp1_name
                end
               ) as rc4_comp1_name,
               (case accom_class_id
                    when @rc4 then
                        comp2_name
                end
               ) as rc4_comp2_name,
               (case accom_class_id
                    when @rc4 then
                        comp3_name
                end
               ) as rc4_comp3_name,
               (case accom_class_id
                    when @rc4 then
                        comp4_name
                end
               ) as rc4_comp4_name,
               (case accom_class_id
                    when @rc4 then
                        comp5_name
                end
               ) as rc4_comp5_name,
               (case accom_class_id
                    when @rc4 then
                        comp6_name
                end
               ) as rc4_comp6_name,
               (case accom_class_id
                    when @rc4 then
                        comp7_name
                end
               ) as rc4_comp7_name,
               (case accom_class_id
                    when @rc4 then
                        comp8_name
                end
               ) as rc4_comp8_name,
               (case accom_class_id
                    when @rc4 then
                        comp9_name
                end
               ) as rc4_comp9_name,
               (case accom_class_id
                    when @rc4 then
                        comp10_name
                end
               ) as rc4_comp10_name,
               (case accom_class_id
                    when @rc4 then
                        comp11_name
                end
               ) as rc4_comp11_name,
               (case accom_class_id
                    when @rc4 then
                        comp12_name
                end
               ) as rc4_comp12_name,
               (case accom_class_id
                    when @rc4 then
                        comp13_name
                end
               ) as rc4_comp13_name,
               (case accom_class_id
                    when @rc4 then
                        comp14_name
                end
               ) as rc4_comp14_name,
               (case accom_class_id
                    when @rc4 then
                        comp15_name
                end
               ) as rc4_comp15_name,
               (case accom_class_id
                    when @rc5 then
                        comp1_name
                end
               ) as rc5_comp1_name,
               (case accom_class_id
                    when @rc5 then
                        comp2_name
                end
               ) as rc5_comp2_name,
               (case accom_class_id
                    when @rc5 then
                        comp3_name
                end
               ) as rc5_comp3_name,
               (case accom_class_id
                    when @rc5 then
                        comp4_name
                end
               ) as rc5_comp4_name,
               (case accom_class_id
                    when @rc5 then
                        comp5_name
                end
               ) as rc5_comp5_name,
               (case accom_class_id
                    when @rc5 then
                        comp6_name
                end
               ) as rc5_comp6_name,
               (case accom_class_id
                    when @rc5 then
                        comp7_name
                end
               ) as rc5_comp7_name,
               (case accom_class_id
                    when @rc5 then
                        comp8_name
                end
               ) as rc5_comp8_name,
               (case accom_class_id
                    when @rc5 then
                        comp9_name
                end
               ) as rc5_comp9_name,
               (case accom_class_id
                    when @rc5 then
                        comp10_name
                end
               ) as rc5_comp10_name,
               (case accom_class_id
                    when @rc5 then
                        comp11_name
                end
               ) as rc5_comp11_name,
               (case accom_class_id
                    when @rc5 then
                        comp12_name
                end
               ) as rc5_comp12_name,
               (case accom_class_id
                    when @rc5 then
                        comp13_name
                end
               ) as rc5_comp13_name,
               (case accom_class_id
                    when @rc5 then
                        comp14_name
                end
               ) as rc5_comp14_name,
               (case accom_class_id
                    when @rc5 then
                        comp15_name
                end
               ) as rc5_comp15_name,
               (case accom_class_id
                    when @rc6 then
                        comp1_name
                end
               ) as rc6_comp1_name,
               (case accom_class_id
                    when @rc6 then
                        comp2_name
                end
               ) as rc6_comp2_name,
               (case accom_class_id
                    when @rc6 then
                        comp3_name
                end
               ) as rc6_comp3_name,
               (case accom_class_id
                    when @rc6 then
                        comp4_name
                end
               ) as rc6_comp4_name,
               (case accom_class_id
                    when @rc6 then
                        comp5_name
                end
               ) as rc6_comp5_name,
               (case accom_class_id
                    when @rc6 then
                        comp6_name
                end
               ) as rc6_comp6_name,
               (case accom_class_id
                    when @rc6 then
                        comp7_name
                end
               ) as rc6_comp7_name,
               (case accom_class_id
                    when @rc6 then
                        comp8_name
                end
               ) as rc6_comp8_name,
               (case accom_class_id
                    when @rc6 then
                        comp9_name
                end
               ) as rc6_comp9_name,
               (case accom_class_id
                    when @rc6 then
                        comp10_name
                end
               ) as rc6_comp10_name,
               (case accom_class_id
                    when @rc6 then
                        comp11_name
                end
               ) as rc6_comp11_name,
               (case accom_class_id
                    when @rc6 then
                        comp12_name
                end
               ) as rc6_comp12_name,
               (case accom_class_id
                    when @rc6 then
                        comp13_name
                end
               ) as rc6_comp13_name,
               (case accom_class_id
                    when @rc6 then
                        comp14_name
                end
               ) as rc6_comp14_name,
               (case accom_class_id
                    when @rc6 then
                        comp15_name
                end
               ) as rc6_comp15_name,
               (case accom_class_id
                    when @rc7 then
                        comp1_name
                end
               ) as rc7_comp1_name,
               (case accom_class_id
                    when @rc7 then
                        comp2_name
                end
               ) as rc7_comp2_name,
               (case accom_class_id
                    when @rc7 then
                        comp3_name
                end
               ) as rc7_comp3_name,
               (case accom_class_id
                    when @rc7 then
                        comp4_name
                end
               ) as rc7_comp4_name,
               (case accom_class_id
                    when @rc7 then
                        comp5_name
                end
               ) as rc7_comp5_name,
               (case accom_class_id
                    when @rc7 then
                        comp6_name
                end
               ) as rc7_comp6_name,
               (case accom_class_id
                    when @rc7 then
                        comp7_name
                end
               ) as rc7_comp7_name,
               (case accom_class_id
                    when @rc7 then
                        comp8_name
                end
               ) as rc7_comp8_name,
               (case accom_class_id
                    when @rc7 then
                        comp9_name
                end
               ) as rc7_comp9_name,
               (case accom_class_id
                    when @rc7 then
                        comp10_name
                end
               ) as rc7_comp10_name,
               (case accom_class_id
                    when @rc7 then
                        comp11_name
                end
               ) as rc7_comp11_name,
               (case accom_class_id
                    when @rc7 then
                        comp12_name
                end
               ) as rc7_comp12_name,
               (case accom_class_id
                    when @rc7 then
                        comp13_name
                end
               ) as rc7_comp13_name,
               (case accom_class_id
                    when @rc7 then
                        comp14_name
                end
               ) as rc7_comp14_name,
               (case accom_class_id
                    when @rc7 then
                        comp15_name
                end
               ) as rc7_comp15_name,
               (case accom_class_id
                    when @rc8 then
                        comp1_name
                end
               ) as rc8_comp1_name,
               (case accom_class_id
                    when @rc8 then
                        comp2_name
                end
               ) as rc8_comp2_name,
               (case accom_class_id
                    when @rc8 then
                        comp3_name
                end
               ) as rc8_comp3_name,
               (case accom_class_id
                    when @rc8 then
                        comp4_name
                end
               ) as rc8_comp4_name,
               (case accom_class_id
                    when @rc8 then
                        comp5_name
                end
               ) as rc8_comp5_name,
               (case accom_class_id
                    when @rc8 then
                        comp6_name
                end
               ) as rc8_comp6_name,
               (case accom_class_id
                    when @rc8 then
                        comp7_name
                end
               ) as rc8_comp7_name,
               (case accom_class_id
                    when @rc8 then
                        comp8_name
                end
               ) as rc8_comp8_name,
               (case accom_class_id
                    when @rc8 then
                        comp9_name
                end
               ) as rc8_comp9_name,
               (case accom_class_id
                    when @rc8 then
                        comp10_name
                end
               ) as rc8_comp10_name,
               (case accom_class_id
                    when @rc8 then
                        comp11_name
                end
               ) as rc8_comp11_name,
               (case accom_class_id
                    when @rc8 then
                        comp12_name
                end
               ) as rc8_comp12_name,
               (case accom_class_id
                    when @rc8 then
                        comp13_name
                end
               ) as rc8_comp13_name,
               (case accom_class_id
                    when @rc8 then
                        comp14_name
                end
               ) as rc8_comp14_name,
               (case accom_class_id
                    when @rc8 then
                        comp15_name
                end
               ) as rc8_comp15_name,
               (case accom_class_id
                    when @rc9 then
                        comp1_name
                end
               ) as rc9_comp1_name,
               (case accom_class_id
                    when @rc9 then
                        comp2_name
                end
               ) as rc9_comp2_name,
               (case accom_class_id
                    when @rc9 then
                        comp3_name
                end
               ) as rc9_comp3_name,
               (case accom_class_id
                    when @rc9 then
                        comp4_name
                end
               ) as rc9_comp4_name,
               (case accom_class_id
                    when @rc9 then
                        comp5_name
                end
               ) as rc9_comp5_name,
               (case accom_class_id
                    when @rc9 then
                        comp6_name
                end
               ) as rc9_comp6_name,
               (case accom_class_id
                    when @rc9 then
                        comp7_name
                end
               ) as rc9_comp7_name,
               (case accom_class_id
                    when @rc9 then
                        comp8_name
                end
               ) as rc9_comp8_name,
               (case accom_class_id
                    when @rc9 then
                        comp9_name
                end
               ) as rc9_comp9_name,
               (case accom_class_id
                    when @rc9 then
                        comp10_name
                end
               ) as rc9_comp10_name,
               (case accom_class_id
                    when @rc9 then
                        comp11_name
                end
               ) as rc9_comp11_name,
               (case accom_class_id
                    when @rc9 then
                        comp12_name
                end
               ) as rc9_comp12_name,
               (case accom_class_id
                    when @rc9 then
                        comp13_name
                end
               ) as rc9_comp13_name,
               (case accom_class_id
                    when @rc9 then
                        comp14_name
                end
               ) as rc9_comp14_name,
               (case accom_class_id
                    when @rc9 then
                        comp15_name
                end
               ) as rc9_comp15_name,
               (case accom_class_id
                    when @rc10 then
                        comp1_name
                end
               ) as rc10_comp1_name,
               (case accom_class_id
                    when @rc10 then
                        comp2_name
                end
               ) as rc10_comp2_name,
               (case accom_class_id
                    when @rc10 then
                        comp3_name
                end
               ) as rc10_comp3_name,
               (case accom_class_id
                    when @rc10 then
                        comp4_name
                end
               ) as rc10_comp4_name,
               (case accom_class_id
                    when @rc10 then
                        comp5_name
                end
               ) as rc10_comp5_name,
               (case accom_class_id
                    when @rc10 then
                        comp6_name
                end
               ) as rc10_comp6_name,
               (case accom_class_id
                    when @rc10 then
                        comp7_name
                end
               ) as rc10_comp7_name,
               (case accom_class_id
                    when @rc10 then
                        comp8_name
                end
               ) as rc10_comp8_name,
               (case accom_class_id
                    when @rc10 then
                        comp9_name
                end
               ) as rc10_comp9_name,
               (case accom_class_id
                    when @rc10 then
                        comp10_name
                end
               ) as rc10_comp10_name,
               (case accom_class_id
                    when @rc10 then
                        comp11_name
                end
               ) as rc10_comp11_name,
               (case accom_class_id
                    when @rc10 then
                        comp12_name
                end
               ) as rc10_comp12_name,
               (case accom_class_id
                    when @rc10 then
                        comp13_name
                end
               ) as rc10_comp13_name,
               (case accom_class_id
                    when @rc10 then
                        comp14_name
                end
               ) as rc10_comp14_name,
               (case accom_class_id
                    when @rc10 then
                        comp15_name
                end
               ) as rc10_comp15_name,
               (case accom_class_id
                    when @rc1 then
                        comp1_change
                end
               ) as rc1_comp1_change,
               (case accom_class_id
                    when @rc1 then
                        comp2_change
                end
               ) as rc1_comp2_change,
               (case accom_class_id
                    when @rc1 then
                        comp3_change
                end
               ) as rc1_comp3_change,
               (case accom_class_id
                    when @rc1 then
                        comp4_change
                end
               ) as rc1_comp4_change,
               (case accom_class_id
                    when @rc1 then
                        comp5_change
                end
               ) as rc1_comp5_change,
               (case accom_class_id
                    when @rc1 then
                        comp6_change
                end
               ) as rc1_comp6_change,
               (case accom_class_id
                    when @rc1 then
                        comp7_change
                end
               ) as rc1_comp7_change,
               (case accom_class_id
                    when @rc1 then
                        comp8_change
                end
               ) as rc1_comp8_change,
               (case accom_class_id
                    when @rc1 then
                        comp9_change
                end
               ) as rc1_comp9_change,
               (case accom_class_id
                    when @rc1 then
                        comp10_change
                end
               ) as rc1_comp10_change,
               (case accom_class_id
                    when @rc1 then
                        comp11_change
                end
               ) as rc1_comp11_change,
               (case accom_class_id
                    when @rc1 then
                        comp12_change
                end
               ) as rc1_comp12_change,
               (case accom_class_id
                    when @rc1 then
                        comp13_change
                end
               ) as rc1_comp13_change,
               (case accom_class_id
                    when @rc1 then
                        comp14_change
                end
               ) as rc1_comp14_change,
               (case accom_class_id
                    when @rc1 then
                        comp15_change
                end
               ) as rc1_comp15_change,
               (case accom_class_id
                    when @rc2 then
                        comp1_change
                end
               ) as rc2_comp1_change,
               (case accom_class_id
                    when @rc2 then
                        comp2_change
                end
               ) as rc2_comp2_change,
               (case accom_class_id
                    when @rc2 then
                        comp3_change
                end
               ) as rc2_comp3_change,
               (case accom_class_id
                    when @rc2 then
                        comp4_change
                end
               ) as rc2_comp4_change,
               (case accom_class_id
                    when @rc2 then
                        comp5_change
                end
               ) as rc2_comp5_change,
               (case accom_class_id
                    when @rc2 then
                        comp6_change
                end
               ) as rc2_comp6_change,
               (case accom_class_id
                    when @rc2 then
                        comp7_change
                end
               ) as rc2_comp7_change,
               (case accom_class_id
                    when @rc2 then
                        comp8_change
                end
               ) as rc2_comp8_change,
               (case accom_class_id
                    when @rc2 then
                        comp9_change
                end
               ) as rc2_comp9_change,
               (case accom_class_id
                    when @rc2 then
                        comp10_change
                end
               ) as rc2_comp10_change,
               (case accom_class_id
                    when @rc2 then
                        comp11_change
                end
               ) as rc2_comp11_change,
               (case accom_class_id
                    when @rc2 then
                        comp12_change
                end
               ) as rc2_comp12_change,
               (case accom_class_id
                    when @rc2 then
                        comp13_change
                end
               ) as rc2_comp13_change,
               (case accom_class_id
                    when @rc2 then
                        comp14_change
                end
               ) as rc2_comp14_change,
               (case accom_class_id
                    when @rc2 then
                        comp15_change
                end
               ) as rc2_comp15_change,
               (case accom_class_id
                    when @rc3 then
                        comp1_change
                end
               ) as rc3_comp1_change,
               (case accom_class_id
                    when @rc3 then
                        comp2_change
                end
               ) as rc3_comp2_change,
               (case accom_class_id
                    when @rc3 then
                        comp3_change
                end
               ) as rc3_comp3_change,
               (case accom_class_id
                    when @rc3 then
                        comp4_change
                end
               ) as rc3_comp4_change,
               (case accom_class_id
                    when @rc3 then
                        comp5_change
                end
               ) as rc3_comp5_change,
               (case accom_class_id
                    when @rc3 then
                        comp6_change
                end
               ) as rc3_comp6_change,
               (case accom_class_id
                    when @rc3 then
                        comp7_change
                end
               ) as rc3_comp7_change,
               (case accom_class_id
                    when @rc3 then
                        comp8_change
                end
               ) as rc3_comp8_change,
               (case accom_class_id
                    when @rc3 then
                        comp9_change
                end
               ) as rc3_comp9_change,
               (case accom_class_id
                    when @rc3 then
                        comp10_change
                end
               ) as rc3_comp10_change,
               (case accom_class_id
                    when @rc3 then
                        comp11_change
                end
               ) as rc3_comp11_change,
               (case accom_class_id
                    when @rc3 then
                        comp12_change
                end
               ) as rc3_comp12_change,
               (case accom_class_id
                    when @rc3 then
                        comp13_change
                end
               ) as rc3_comp13_change,
               (case accom_class_id
                    when @rc3 then
                        comp14_change
                end
               ) as rc3_comp14_change,
               (case accom_class_id
                    when @rc3 then
                        comp15_change
                end
               ) as rc3_comp15_change,
               (case accom_class_id
                    when @rc4 then
                        comp1_change
                end
               ) as rc4_comp1_change,
               (case accom_class_id
                    when @rc4 then
                        comp2_change
                end
               ) as rc4_comp2_change,
               (case accom_class_id
                    when @rc4 then
                        comp3_change
                end
               ) as rc4_comp3_change,
               (case accom_class_id
                    when @rc4 then
                        comp4_change
                end
               ) as rc4_comp4_change,
               (case accom_class_id
                    when @rc4 then
                        comp5_change
                end
               ) as rc4_comp5_change,
               (case accom_class_id
                    when @rc4 then
                        comp6_change
                end
               ) as rc4_comp6_change,
               (case accom_class_id
                    when @rc4 then
                        comp7_change
                end
               ) as rc4_comp7_change,
               (case accom_class_id
                    when @rc4 then
                        comp8_change
                end
               ) as rc4_comp8_change,
               (case accom_class_id
                    when @rc4 then
                        comp9_change
                end
               ) as rc4_comp9_change,
               (case accom_class_id
                    when @rc4 then
                        comp10_change
                end
               ) as rc4_comp10_change,
               (case accom_class_id
                    when @rc4 then
                        comp11_change
                end
               ) as rc4_comp11_change,
               (case accom_class_id
                    when @rc4 then
                        comp12_change
                end
               ) as rc4_comp12_change,
               (case accom_class_id
                    when @rc4 then
                        comp13_change
                end
               ) as rc4_comp13_change,
               (case accom_class_id
                    when @rc4 then
                        comp14_change
                end
               ) as rc4_comp14_change,
               (case accom_class_id
                    when @rc4 then
                        comp15_change
                end
               ) as rc4_comp15_change,
               (case accom_class_id
                    when @rc5 then
                        comp1_change
                end
               ) as rc5_comp1_change,
               (case accom_class_id
                    when @rc5 then
                        comp2_change
                end
               ) as rc5_comp2_change,
               (case accom_class_id
                    when @rc5 then
                        comp3_change
                end
               ) as rc5_comp3_change,
               (case accom_class_id
                    when @rc5 then
                        comp4_change
                end
               ) as rc5_comp4_change,
               (case accom_class_id
                    when @rc5 then
                        comp5_change
                end
               ) as rc5_comp5_change,
               (case accom_class_id
                    when @rc5 then
                        comp6_change
                end
               ) as rc5_comp6_change,
               (case accom_class_id
                    when @rc5 then
                        comp7_change
                end
               ) as rc5_comp7_change,
               (case accom_class_id
                    when @rc5 then
                        comp8_change
                end
               ) as rc5_comp8_change,
               (case accom_class_id
                    when @rc5 then
                        comp9_change
                end
               ) as rc5_comp9_change,
               (case accom_class_id
                    when @rc5 then
                        comp10_change
                end
               ) as rc5_comp10_change,
               (case accom_class_id
                    when @rc5 then
                        comp11_change
                end
               ) as rc5_comp11_change,
               (case accom_class_id
                    when @rc5 then
                        comp12_change
                end
               ) as rc5_comp12_change,
               (case accom_class_id
                    when @rc5 then
                        comp13_change
                end
               ) as rc5_comp13_change,
               (case accom_class_id
                    when @rc5 then
                        comp14_change
                end
               ) as rc5_comp14_change,
               (case accom_class_id
                    when @rc5 then
                        comp15_change
                end
               ) as rc5_comp15_change,
               (case accom_class_id
                    when @rc6 then
                        comp1_change
                end
               ) as rc6_comp1_change,
               (case accom_class_id
                    when @rc6 then
                        comp2_change
                end
               ) as rc6_comp2_change,
               (case accom_class_id
                    when @rc6 then
                        comp3_change
                end
               ) as rc6_comp3_change,
               (case accom_class_id
                    when @rc6 then
                        comp4_change
                end
               ) as rc6_comp4_change,
               (case accom_class_id
                    when @rc6 then
                        comp5_change
                end
               ) as rc6_comp5_change,
               (case accom_class_id
                    when @rc6 then
                        comp6_change
                end
               ) as rc6_comp6_change,
               (case accom_class_id
                    when @rc6 then
                        comp7_change
                end
               ) as rc6_comp7_change,
               (case accom_class_id
                    when @rc6 then
                        comp8_change
                end
               ) as rc6_comp8_change,
               (case accom_class_id
                    when @rc6 then
                        comp9_change
                end
               ) as rc6_comp9_change,
               (case accom_class_id
                    when @rc6 then
                        comp10_change
                end
               ) as rc6_comp10_change,
               (case accom_class_id
                    when @rc6 then
                        comp11_change
                end
               ) as rc6_comp11_change,
               (case accom_class_id
                    when @rc6 then
                        comp12_change
                end
               ) as rc6_comp12_change,
               (case accom_class_id
                    when @rc6 then
                        comp13_change
                end
               ) as rc6_comp13_change,
               (case accom_class_id
                    when @rc6 then
                        comp14_change
                end
               ) as rc6_comp14_change,
               (case accom_class_id
                    when @rc6 then
                        comp15_change
                end
               ) as rc6_comp15_change,
               (case accom_class_id
                    when @rc7 then
                        comp1_change
                end
               ) as rc7_comp1_change,
               (case accom_class_id
                    when @rc7 then
                        comp2_change
                end
               ) as rc7_comp2_change,
               (case accom_class_id
                    when @rc7 then
                        comp3_change
                end
               ) as rc7_comp3_change,
               (case accom_class_id
                    when @rc7 then
                        comp4_change
                end
               ) as rc7_comp4_change,
               (case accom_class_id
                    when @rc7 then
                        comp5_change
                end
               ) as rc7_comp5_change,
               (case accom_class_id
                    when @rc7 then
                        comp6_change
                end
               ) as rc7_comp6_change,
               (case accom_class_id
                    when @rc7 then
                        comp7_change
                end
               ) as rc7_comp7_change,
               (case accom_class_id
                    when @rc7 then
                        comp8_change
                end
               ) as rc7_comp8_change,
               (case accom_class_id
                    when @rc7 then
                        comp9_change
                end
               ) as rc7_comp9_change,
               (case accom_class_id
                    when @rc7 then
                        comp10_change
                end
               ) as rc7_comp10_change,
               (case accom_class_id
                    when @rc7 then
                        comp11_change
                end
               ) as rc7_comp11_change,
               (case accom_class_id
                    when @rc7 then
                        comp12_change
                end
               ) as rc7_comp12_change,
               (case accom_class_id
                    when @rc7 then
                        comp13_change
                end
               ) as rc7_comp13_change,
               (case accom_class_id
                    when @rc7 then
                        comp14_change
                end
               ) as rc7_comp14_change,
               (case accom_class_id
                    when @rc7 then
                        comp15_change
                end
               ) as rc7_comp15_change,
               (case accom_class_id
                    when @rc8 then
                        comp1_change
                end
               ) as rc8_comp1_change,
               (case accom_class_id
                    when @rc8 then
                        comp2_change
                end
               ) as rc8_comp2_change,
               (case accom_class_id
                    when @rc8 then
                        comp3_change
                end
               ) as rc8_comp3_change,
               (case accom_class_id
                    when @rc8 then
                        comp4_change
                end
               ) as rc8_comp4_change,
               (case accom_class_id
                    when @rc8 then
                        comp5_change
                end
               ) as rc8_comp5_change,
               (case accom_class_id
                    when @rc8 then
                        comp6_change
                end
               ) as rc8_comp6_change,
               (case accom_class_id
                    when @rc8 then
                        comp7_change
                end
               ) as rc8_comp7_change,
               (case accom_class_id
                    when @rc8 then
                        comp8_change
                end
               ) as rc8_comp8_change,
               (case accom_class_id
                    when @rc8 then
                        comp9_change
                end
               ) as rc8_comp9_change,
               (case accom_class_id
                    when @rc8 then
                        comp10_change
                end
               ) as rc8_comp10_change,
               (case accom_class_id
                    when @rc8 then
                        comp11_change
                end
               ) as rc8_comp11_change,
               (case accom_class_id
                    when @rc8 then
                        comp12_change
                end
               ) as rc8_comp12_change,
               (case accom_class_id
                    when @rc8 then
                        comp13_change
                end
               ) as rc8_comp13_change,
               (case accom_class_id
                    when @rc8 then
                        comp14_change
                end
               ) as rc8_comp14_change,
               (case accom_class_id
                    when @rc8 then
                        comp15_change
                end
               ) as rc8_comp15_change,
               (case accom_class_id
                    when @rc9 then
                        comp1_change
                end
               ) as rc9_comp1_change,
               (case accom_class_id
                    when @rc9 then
                        comp2_change
                end
               ) as rc9_comp2_change,
               (case accom_class_id
                    when @rc9 then
                        comp3_change
                end
               ) as rc9_comp3_change,
               (case accom_class_id
                    when @rc9 then
                        comp4_change
                end
               ) as rc9_comp4_change,
               (case accom_class_id
                    when @rc9 then
                        comp5_change
                end
               ) as rc9_comp5_change,
               (case accom_class_id
                    when @rc9 then
                        comp6_change
                end
               ) as rc9_comp6_change,
               (case accom_class_id
                    when @rc9 then
                        comp7_change
                end
               ) as rc9_comp7_change,
               (case accom_class_id
                    when @rc9 then
                        comp8_change
                end
               ) as rc9_comp8_change,
               (case accom_class_id
                    when @rc9 then
                        comp9_change
                end
               ) as rc9_comp9_change,
               (case accom_class_id
                    when @rc9 then
                        comp10_change
                end
               ) as rc9_comp10_change,
               (case accom_class_id
                    when @rc9 then
                        comp11_change
                end
               ) as rc9_comp11_change,
               (case accom_class_id
                    when @rc9 then
                        comp12_change
                end
               ) as rc9_comp12_change,
               (case accom_class_id
                    when @rc9 then
                        comp13_change
                end
               ) as rc9_comp13_change,
               (case accom_class_id
                    when @rc9 then
                        comp14_change
                end
               ) as rc9_comp14_change,
               (case accom_class_id
                    when @rc9 then
                        comp15_change
                end
               ) as rc9_comp15_change,
               (case accom_class_id
                    when @rc10 then
                        comp1_change
                end
               ) as rc10_comp1_change,
               (case accom_class_id
                    when @rc10 then
                        comp2_change
                end
               ) as rc10_comp2_change,
               (case accom_class_id
                    when @rc10 then
                        comp3_change
                end
               ) as rc10_comp3_change,
               (case accom_class_id
                    when @rc10 then
                        comp4_change
                end
               ) as rc10_comp4_change,
               (case accom_class_id
                    when @rc10 then
                        comp5_change
                end
               ) as rc10_comp5_change,
               (case accom_class_id
                    when @rc10 then
                        comp6_change
                end
               ) as rc10_comp6_change,
               (case accom_class_id
                    when @rc10 then
                        comp7_change
                end
               ) as rc10_comp7_change,
               (case accom_class_id
                    when @rc10 then
                        comp8_change
                end
               ) as rc10_comp8_change,
               (case accom_class_id
                    when @rc10 then
                        comp9_change
                end
               ) as rc10_comp9_change,
               (case accom_class_id
                    when @rc10 then
                        comp10_change
                end
               ) as rc10_comp10_change,
               (case accom_class_id
                    when @rc10 then
                        comp11_change
                end
               ) as rc10_comp11_change,
               (case accom_class_id
                    when @rc10 then
                        comp12_change
                end
               ) as rc10_comp12_change,
               (case accom_class_id
                    when @rc10 then
                        comp13_change
                end
               ) as rc10_comp13_change,
               (case accom_class_id
                    when @rc10 then
                        comp14_change
                end
               ) as rc10_comp14_change,
               (case accom_class_id
                    when @rc10 then
                        comp15_change
                end
               ) as rc10_comp15_change,
               (case accom_class_id
                    when @rc1 then
                        block
                end
               ) as rc1_block,
               (case accom_class_id
                    when @rc1 then
                        block_available
                end
               ) as rc1_block_available,
               (case accom_class_id
                    when @rc1 then
                        block_pickup
                end
               ) as rc1_block_pickup,
               (case accom_class_id
                    when @rc2 then
                        block
                end
               ) as rc2_block,
               (case accom_class_id
                    when @rc2 then
                        block_available
                end
               ) as rc2_block_available,
               (case accom_class_id
                    when @rc2 then
                        block_pickup
                end
               ) as rc2_block_pickup,
               (case accom_class_id
                    when @rc3 then
                        block
                end
               ) as rc3_block,
               (case accom_class_id
                    when @rc3 then
                        block_available
                end
               ) as rc3_block_available,
               (case accom_class_id
                    when @rc3 then
                        block_pickup
                end
               ) as rc3_block_pickup,
               (case accom_class_id
                    when @rc4 then
                        block
                end
               ) as rc4_block,
               (case accom_class_id
                    when @rc4 then
                        block_available
                end
               ) as rc4_block_available,
               (case accom_class_id
                    when @rc4 then
                        block_pickup
                end
               ) as rc4_block_pickup,
               (case accom_class_id
                    when @rc5 then
                        block
                end
               ) as rc5_block,
               (case accom_class_id
                    when @rc5 then
                        block_available
                end
               ) as rc5_block_available,
               (case accom_class_id
                    when @rc5 then
                        block_pickup
                end
               ) as rc5_block_pickup,
               (case accom_class_id
                    when @rc6 then
                        block
                end
               ) as rc6_block,
               (case accom_class_id
                    when @rc6 then
                        block_available
                end
               ) as rc6_block_available,
               (case accom_class_id
                    when @rc6 then
                        block_pickup
                end
               ) as rc6_block_pickup,
               (case accom_class_id
                    when @rc7 then
                        block
                end
               ) as rc7_block,
               (case accom_class_id
                    when @rc7 then
                        block_available
                end
               ) as rc7_block_available,
               (case accom_class_id
                    when @rc7 then
                        block_pickup
                end
               ) as rc7_block_pickup,
               (case accom_class_id
                    when @rc8 then
                        block
                end
               ) as rc8_block,
               (case accom_class_id
                    when @rc8 then
                        block_available
                end
               ) as rc8_block_available,
               (case accom_class_id
                    when @rc8 then
                        block_pickup
                end
               ) as rc8_block_pickup,
               (case accom_class_id
                    when @rc9 then
                        block
                end
               ) as rc9_block,
               (case accom_class_id
                    when @rc9 then
                        block_available
                end
               ) as rc9_block_available,
               (case accom_class_id
                    when @rc9 then
                        block_pickup
                end
               ) as rc9_block_pickup,
               (case accom_class_id
                    when @rc10 then
                        block
                end
               ) as rc10_block,
               (case accom_class_id
                    when @rc10 then
                        block_available
                end
               ) as rc10_block_available,
               (case accom_class_id
                    when @rc10 then
                        block_pickup
                end
               ) as rc10_block_pickup,
               (case accom_class_id
                    when @rc1 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc1_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc1 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc1_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc2 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc2_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc2 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc2_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc3 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc3_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc3 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc3_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc4 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc4_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc4 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc4_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc5 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc5_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc5 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc5_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc6 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc6_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc6 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc6_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc7 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc7_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc7 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc7_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc8 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc8_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc8 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc8_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc9 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc9_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc9 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc9_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc10 then
                        OccupancyForecastPerCurrent_without_ooo
                end
               ) as rc10_OccupancyForecastPerCurrent_without_ooo,
               (case accom_class_id
                    when @rc10 then
                        occupancyforecastperchange_without_ooo
                end
               ) as rc10_occupancyforecastperchange_without_ooo,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent
                end
               ) as rc1_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange
                end
               ) as rc1_decisionreasontypechange,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent
                end
               ) as rc2_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange
                end
               ) as rc2_decisionreasontypechange,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent
                end
               ) as rc3_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange
                end
               ) as rc3_decisionreasontypechange,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent
                end
               ) as rc4_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange
                end
               ) as rc4_decisionreasontypechange,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent
                end
               ) as rc5_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange
                end
               ) as rc5_decisionreasontypechange,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent
                end
               ) as rc6_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange
                end
               ) as rc6_decisionreasontypechange,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent
                end
               ) as rc7_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange
                end
               ) as rc7_decisionreasontypechange,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent
                end
               ) as rc8_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange
                end
               ) as rc8_decisionreasontypechange,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent
                end
               ) as rc9_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange
                end
               ) as rc9_decisionreasontypechange,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent
                end
               ) as rc10_decisionreasontypecurrent,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange
                end
               ) as rc10_decisionreasontypechange,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los1
                end
               ) as rc1_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los2
                end
               ) as rc1_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los3
                end
               ) as rc1_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los4
                end
               ) as rc1_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los5
                end
               ) as rc1_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los6
                end
               ) as rc1_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los7
                end
               ) as rc1_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypecurrent_los8
                end
               ) as rc1_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los1
                end
               ) as rc2_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los2
                end
               ) as rc2_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los3
                end
               ) as rc2_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los4
                end
               ) as rc2_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los5
                end
               ) as rc2_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los6
                end
               ) as rc2_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los7
                end
               ) as rc2_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypecurrent_los8
                end
               ) as rc2_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los1
                end
               ) as rc3_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los2
                end
               ) as rc3_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los3
                end
               ) as rc3_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los4
                end
               ) as rc3_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los5
                end
               ) as rc3_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los6
                end
               ) as rc3_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los7
                end
               ) as rc3_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypecurrent_los8
                end
               ) as rc3_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los1
                end
               ) as rc4_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los2
                end
               ) as rc4_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los3
                end
               ) as rc4_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los4
                end
               ) as rc4_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los5
                end
               ) as rc4_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los6
                end
               ) as rc4_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los7
                end
               ) as rc4_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypecurrent_los8
                end
               ) as rc4_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los1
                end
               ) as rc5_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los2
                end
               ) as rc5_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los3
                end
               ) as rc5_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los4
                end
               ) as rc5_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los5
                end
               ) as rc5_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los6
                end
               ) as rc5_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los7
                end
               ) as rc5_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypecurrent_los8
                end
               ) as rc5_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los1
                end
               ) as rc6_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los2
                end
               ) as rc6_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los3
                end
               ) as rc6_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los4
                end
               ) as rc6_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los5
                end
               ) as rc6_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los6
                end
               ) as rc6_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los7
                end
               ) as rc6_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypecurrent_los8
                end
               ) as rc6_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los1
                end
               ) as rc7_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los2
                end
               ) as rc7_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los3
                end
               ) as rc7_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los4
                end
               ) as rc7_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los5
                end
               ) as rc7_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los6
                end
               ) as rc7_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los7
                end
               ) as rc7_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypecurrent_los8
                end
               ) as rc7_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los1
                end
               ) as rc8_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los2
                end
               ) as rc8_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los3
                end
               ) as rc8_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los4
                end
               ) as rc8_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los5
                end
               ) as rc8_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los6
                end
               ) as rc8_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los7
                end
               ) as rc8_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypecurrent_los8
                end
               ) as rc8_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los1
                end
               ) as rc9_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los2
                end
               ) as rc9_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los3
                end
               ) as rc9_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los4
                end
               ) as rc9_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los5
                end
               ) as rc9_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los6
                end
               ) as rc9_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los7
                end
               ) as rc9_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypecurrent_los8
                end
               ) as rc9_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los1
                end
               ) as rc10_decisionreasontypecurrent_los1,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los2
                end
               ) as rc10_decisionreasontypecurrent_los2,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los3
                end
               ) as rc10_decisionreasontypecurrent_los3,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los4
                end
               ) as rc10_decisionreasontypecurrent_los4,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los5
                end
               ) as rc10_decisionreasontypecurrent_los5,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los6
                end
               ) as rc10_decisionreasontypecurrent_los6,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los7
                end
               ) as rc10_decisionreasontypecurrent_los7,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypecurrent_los8
                end
               ) as rc10_decisionreasontypecurrent_los8,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los1
                end
               ) as rc1_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los2
                end
               ) as rc1_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los3
                end
               ) as rc1_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los4
                end
               ) as rc1_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los5
                end
               ) as rc1_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los6
                end
               ) as rc1_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los7
                end
               ) as rc1_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc1 then
                        decisionreasontypechange_los8
                end
               ) as rc1_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los1
                end
               ) as rc2_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los2
                end
               ) as rc2_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los3
                end
               ) as rc2_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los4
                end
               ) as rc2_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los5
                end
               ) as rc2_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los6
                end
               ) as rc2_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los7
                end
               ) as rc2_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc2 then
                        decisionreasontypechange_los8
                end
               ) as rc2_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los1
                end
               ) as rc3_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los2
                end
               ) as rc3_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los3
                end
               ) as rc3_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los4
                end
               ) as rc3_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los5
                end
               ) as rc3_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los6
                end
               ) as rc3_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los7
                end
               ) as rc3_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc3 then
                        decisionreasontypechange_los8
                end
               ) as rc3_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los1
                end
               ) as rc4_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los2
                end
               ) as rc4_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los3
                end
               ) as rc4_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los4
                end
               ) as rc4_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los5
                end
               ) as rc4_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los6
                end
               ) as rc4_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los7
                end
               ) as rc4_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc4 then
                        decisionreasontypechange_los8
                end
               ) as rc4_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los1
                end
               ) as rc5_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los2
                end
               ) as rc5_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los3
                end
               ) as rc5_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los4
                end
               ) as rc5_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los5
                end
               ) as rc5_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los6
                end
               ) as rc5_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los7
                end
               ) as rc5_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc5 then
                        decisionreasontypechange_los8
                end
               ) as rc5_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los1
                end
               ) as rc6_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los2
                end
               ) as rc6_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los3
                end
               ) as rc6_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los4
                end
               ) as rc6_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los5
                end
               ) as rc6_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los6
                end
               ) as rc6_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los7
                end
               ) as rc6_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc6 then
                        decisionreasontypechange_los8
                end
               ) as rc6_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los1
                end
               ) as rc7_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los2
                end
               ) as rc7_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los3
                end
               ) as rc7_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los4
                end
               ) as rc7_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los5
                end
               ) as rc7_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los6
                end
               ) as rc7_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los7
                end
               ) as rc7_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc7 then
                        decisionreasontypechange_los8
                end
               ) as rc7_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los1
                end
               ) as rc8_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los2
                end
               ) as rc8_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los3
                end
               ) as rc8_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los4
                end
               ) as rc8_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los5
                end
               ) as rc8_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los6
                end
               ) as rc8_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los7
                end
               ) as rc8_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc8 then
                        decisionreasontypechange_los8
                end
               ) as rc8_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los1
                end
               ) as rc9_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los2
                end
               ) as rc9_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los3
                end
               ) as rc9_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los4
                end
               ) as rc9_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los5
                end
               ) as rc9_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los6
                end
               ) as rc9_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los7
                end
               ) as rc9_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc9 then
                        decisionreasontypechange_los8
                end
               ) as rc9_decisionreasontypechange_los8,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los1
                end
               ) as rc10_decisionreasontypechange_los1,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los2
                end
               ) as rc10_decisionreasontypechange_los2,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los3
                end
               ) as rc10_decisionreasontypechange_los3,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los4
                end
               ) as rc10_decisionreasontypechange_los4,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los5
                end
               ) as rc10_decisionreasontypechange_los5,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los6
                end
               ) as rc10_decisionreasontypechange_los6,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los7
                end
               ) as rc10_decisionreasontypechange_los7,
               (case accom_class_id
                    when @rc10 then
                        decisionreasontypechange_los8
                end
               ) as rc10_decisionreasontypechange_los8
        from
        (
            select base.occupancy_dt,
                   datename(dw, a.occupancy_dt) as dow,
                   a.property_id,
                   a.outoforder as ooo,
                   base.accom_class_id,
                   f.special_event_name as specialevent,
                   a.rooms_sold as roomssoldcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           cast(ISNULL(a.rooms_sold, 0.0) - ISNULL(e.rooms_sold, ISNULL(a.rooms_sold, 0.0)) as numeric(19, 2))
                       else
                           cast(ISNULL(a.rooms_sold, 0.0) - ISNULL(e.rooms_sold, 0.0) as numeric(19, 2))
                   end as roomssoldchange,
                   isnull(b.ac_occupancy_forecast, 0.0) as occupancyforecastcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and g.occupancy_nbr_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(b.ac_occupancy_forecast, 0.0)
                                 - ISNULL(g.occupancy_nbr_businessstartdate, isnull(b.ac_occupancy_forecast, 0.0))
                                ) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(b.ac_occupancy_forecast, 0.0) - ISNULL(g.occupancy_nbr_businessstartdate, 0.0)) AS NUMERIC(19, 2))
                   end as occupancyforecastchange,
                   isnull(   case (a.total_accom_capacity - a.outoforder)
                                 when 0 then
                                     0
                                 else
                             (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                             end,
                             0.0
                         ) OccupancyForecastPerCurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and g.occupancy_nbr_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           ISNULL(   (case (a.total_accom_capacity - a.outoforder)
                                          when 0 then
                                              0
                                          else
                                     (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                                      end
                                     ),
                                     (case (a.total_accom_capacity - a.outoforder)
                                          when 0 then
                                              0
                                          else
                                     (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                                      end
                                     )
                                 )
                           - ISNULL(
                                       (case (e.total_accom_capacity - e.outoforder)
                                            when 0 then
                                                0
                                            else
                                       (g.occupancy_nbr_businessstartdate / (e.total_accom_capacity - e.outoforder))
                                       * 100
                                        end
                                       ),
                                       (case (a.total_accom_capacity - a.outoforder)
                                            when 0 then
                                                0
                                            else
                                       (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                                        end
                                       )
                                   )
                       else
                           ISNULL(   (case (a.total_accom_capacity - a.outoforder)
                                          when 0 then
                                              0
                                          else
                                     (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                                      end
                                     ),
                                     (case (a.total_accom_capacity - a.outoforder)
                                          when 0 then
                                              0
                                          else
                                     (b.ac_occupancy_forecast / (a.total_accom_capacity - a.outoforder)) * 100
                                      end
                                     )
                                 )
                           - ISNULL(
                                       (case (e.total_accom_capacity - e.outoforder)
                                            when 0 then
                                                0
                                            else
                                       (isnull(g.occupancy_nbr_businessstartdate, 0.0)
                                        / (e.total_accom_capacity - e.outoforder)
                                       ) * 100
                                        end
                                       ),
                                       0.0
                                   )
                   end as occupancyforecastperchange,
                   aa.total_profit as bookedprofitcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(aa.total_profit, 0.0) - ISNULL(ee.profit, isnull(aa.total_profit, 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(aa.total_profit, 0.0) - ISNULL(ee.profit, 0.0)) AS NUMERIC(19, 2))
                   end as bookedprofitchange,
                   bb.ac_profit as fcstedprofitcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and gg.profit_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(bb.ac_profit, 0.0)
                                 - ISNULL(gg.profit_businessstartdate, isnull(bb.ac_profit, 0.0))
                                ) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(bb.ac_profit, 0.0) - ISNULL(gg.profit_businessstartdate, 0.0)) AS NUMERIC(19, 2))
                   end as fcstedprofitchange,
                   aa.propor as bookedproporcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(aa.propor, 0.0) - ISNULL(ee.propor, isnull(aa.propor, 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(aa.propor, 0.0) - ISNULL(ee.propor, 0.0)) AS NUMERIC(19, 2))
                   end as bookedproporchange,
                   bb.ac_propor as estimatedproporcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and gg.propor is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           ISNULL(bb.ac_propor, 0.0) - ISNULL(gg.propor, ISNULL(bb.ac_propor, 0.0))
                       else
                           ISNULL(bb.ac_propor, 0.0) - ISNULL(gg.propor, 0.0)
                   end as estimatedproporchange,
                   aa.propar as bookedproparcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           isnull(aa.propar, 0.0)
                           - isnull(
                                       (
                                       (
                                           select propar
                                           from dbo.ufn_calculate_propar(
                                                                            ee.profit,
                                                                            ee.total_accom_capacity,
                                                                            ee.outoforder,
                                                                            @use_physical_capacity
                                                                        )
                                       )
                                       ),
                                       isnull(aa.propar, 0.0)
                                   )
                       else
                           isnull(aa.propar, 0.0)
                           - isnull(
                                       (
                                       (
                                           select propar
                                           from dbo.ufn_calculate_propar(
                                                                            ee.profit,
                                                                            ee.total_accom_capacity,
                                                                            ee.outoforder,
                                                                            @use_physical_capacity
                                                                        )
                                       )
                                       ),
                                       0.0
                                   )
                   end as bookedproparchange,
                   (
                       select propar
                       from dbo.ufn_calculate_propar(
                                                        bb.ac_profit,
                                                        aa.total_accom_capacity,
                                                        aa.outoforder,
                                                        @use_physical_capacity
                                                    )
                   ) as estimatedproparcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and gg.profit_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                   (
                       select propar
                       from dbo.ufn_calculate_propar(
                                                        bb.ac_profit,
                                                        aa.total_accom_capacity,
                                                        aa.outoforder,
                                                        @use_physical_capacity
                                                    )
                   )
                   - ISNULL(
                               (
                               (
                                   select propar
                                   from dbo.ufn_calculate_propar(
                                                                    gg.profit_businessstartdate,
                                                                    ee.total_accom_capacity,
                                                                    ee.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               ),
                               (
                               (
                                   select propar
                                   from dbo.ufn_calculate_propar(
                                                                    bb.ac_profit,
                                                                    aa.total_accom_capacity,
                                                                    aa.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               )
                           )
                       else
                   (
                       select propar
                       from dbo.ufn_calculate_propar(
                                                        bb.ac_profit,
                                                        aa.total_accom_capacity,
                                                        aa.outoforder,
                                                        @use_physical_capacity
                                                    )
                   )
                   - ISNULL(
                               (
                               (
                                   select propar
                                   from dbo.ufn_calculate_propar(
                                                                    isnull(gg.profit_businessstartdate, 0.0),
                                                                    ee.total_accom_capacity,
                                                                    ee.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               ),
                               0.0
                           )
                   end as estimatedproparchange,
                   a.room_revenue as bookedroomrevenuecurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(a.room_revenue, 0.0) - ISNULL(e.room_revenue, isnull(a.room_revenue, 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(a.room_revenue, 0.0) - ISNULL(e.room_revenue, 0.0)) AS NUMERIC(19, 2))
                   end as bookedroomrevenuechange,
                   b.ac_revenue as fcstedroomrevenuecurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and g.revenue_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(b.ac_revenue, 0.0)
                                 - ISNULL(g.revenue_businessstartdate, isnull(b.ac_revenue, 0.0))
                                ) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(b.ac_revenue, 0.0) - ISNULL(g.revenue_businessstartdate, 0.0)) AS NUMERIC(19, 2))
                   end as fcstedroomrevenuechange,
                   a.adr bookedadrcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(a.adr, 0.0) - ISNULL(e.adr, isnull(a.adr, 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(a.adr, 0.0) - ISNULL(e.adr, 0.0)) AS NUMERIC(19, 2))
                   end as bookedadrchange,
                   b.ac_adr as estimatedadrcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and g.adr is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           ISNULL(b.ac_adr, 0.0) - ISNULL(g.adr, ISNULL(b.ac_adr, 0.0))
                       else
                           ISNULL(b.ac_adr, 0.0) - ISNULL(g.adr, 0.0)
                   end as estimatedadrchange,
                   a.revpar bookedrevparcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           isnull(a.revpar, 0.0)
                           - isnull(
                                       (
                                       (
                                           select revpar
                                           from dbo.ufn_calculate_revpar(
                                                                            e.room_revenue,
                                                                            e.total_accom_capacity,
                                                                            e.outoforder,
                                                                            @use_physical_capacity
                                                                        )
                                       )
                                       ),
                                       isnull(a.revpar, 0.0)
                                   )
                       else
                           isnull(a.revpar, 0.0)
                           - isnull(
                                       (
                                       (
                                           select revpar
                                           from dbo.ufn_calculate_revpar(
                                                                            e.room_revenue,
                                                                            e.total_accom_capacity,
                                                                            e.outoforder,
                                                                            @use_physical_capacity
                                                                        )
                                       )
                                       ),
                                       0.0
                                   )
                   end as bookedrevparchange,
                   (
                       select revpar
                       from dbo.ufn_calculate_revpar(
                                                        b.ac_revenue,
                                                        a.total_accom_capacity,
                                                        a.outoforder,
                                                        @use_physical_capacity
                                                    )
                   ) as estimatedrevparcurrent,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and g.revenue_businessstartdate is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                   (
                       select revpar
                       from dbo.ufn_calculate_revpar(
                                                        b.ac_revenue,
                                                        a.total_accom_capacity,
                                                        a.outoforder,
                                                        @use_physical_capacity
                                                    )
                   )
                   - ISNULL(
                               (
                               (
                                   select revpar
                                   from dbo.ufn_calculate_revpar(
                                                                    g.revenue_businessstartdate,
                                                                    e.total_accom_capacity,
                                                                    e.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               ),
                               (
                               (
                                   select revpar
                                   from dbo.ufn_calculate_revpar(
                                                                    b.ac_revenue,
                                                                    a.total_accom_capacity,
                                                                    a.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               )
                           )
                       else
                   (
                       select revpar
                       from dbo.ufn_calculate_revpar(
                                                        b.ac_revenue,
                                                        a.total_accom_capacity,
                                                        a.outoforder,
                                                        @use_physical_capacity
                                                    )
                   )
                   - ISNULL(
                               (
                               (
                                   select revpar
                                   from dbo.ufn_calculate_revpar(
                                                                    isnull(g.revenue_businessstartdate, 0.0),
                                                                    e.total_accom_capacity,
                                                                    e.outoforder,
                                                                    @use_physical_capacity
                                                                )
                               )
                               ),
                               0.0
                           )
                   end as estimatedrevparchange,
                   k.lrv,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and l.lrv is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           isnull(k.lrv, 0.0) - isnull(l.lrv, isnull(k.lrv, 0.0))
                       else
                           isnull(k.lrv, 0.0) - isnull(l.lrv, 0.0)
                   end as lrv_change,
                   isnull(m.overbooking, 0.0) as overbookingcurrent,
                   case
                       when a.occupancy_dt <= @business_dt then
                           ISNULL(m.total_overbooking, ISNULL(m.overbooking, 0.0))
                           - ISNULL(m.pace_overbooking, ISNULL(m.overbooking, 0.0))
                       else
                           ISNULL(m.total_overbooking, ISNULL(m.overbooking, 0.0)) - ISNULL(m.pace_overbooking, 0.0)
                   end as overbookingchange,
                   n.accom_class_code as master_class_name,
                   isnull(cast(h.[bar_los1] as float), 0.0) as bar_los1,
                   isnull(cast(h.[bar_los2] as float), 0.0) as bar_los2,
                   isnull(cast(h.[bar_los3] as float), 0.0) as bar_los3,
                   isnull(cast(h.[bar_los4] as float), 0.0) as bar_los4,
                   isnull(cast(h.[bar_los5] as float), 0.0) as bar_los5,
                   isnull(cast(h.[bar_los6] as float), 0.0) as bar_los6,
                   isnull(cast(h.[bar_los7] as float), 0.0) as bar_los7,
                   isnull(cast(h.[bar_los8] as float), 0.0) as bar_los8,
                   isnull(cast(h.[bar_by_day] as float), 0.0) as bar_by_day,
                   i.ratecode_los1,
                   i.ratecode_los2,
                   i.ratecode_los3,
                   i.ratecode_los4,
                   i.ratecode_los5,
                   i.ratecode_los6,
                   i.ratecode_los7,
                   i.ratecode_los8,
                   i.ratecode_los_all,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los1 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los1], 0.0) - ISNULL(j.[bar_los1], isnull(h.[bar_los1], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los1], 0.0) - ISNULL(j.[bar_los1], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los1_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los2 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los2], 0.0) - ISNULL(j.[bar_los2], isnull(h.[bar_los2], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los2], 0.0) - ISNULL(j.[bar_los2], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los2_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los3 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los3], 0.0) - ISNULL(j.[bar_los3], isnull(h.[bar_los3], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los3], 0.0) - ISNULL(j.[bar_los3], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los3_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los4 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los4], 0.0) - ISNULL(j.[bar_los4], isnull(h.[bar_los4], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los4], 0.0) - ISNULL(j.[bar_los4], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los4_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los5 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los5], 0.0) - ISNULL(j.[bar_los5], isnull(h.[bar_los5], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los5], 0.0) - ISNULL(j.[bar_los5], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los5_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los6 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los6], 0.0) - ISNULL(j.[bar_los6], isnull(h.[bar_los6], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los6], 0.0) - ISNULL(j.[bar_los6], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los6_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los7 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los7], 0.0) - ISNULL(j.[bar_los7], isnull(h.[bar_los7], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los7], 0.0) - ISNULL(j.[bar_los7], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los7_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_los8 is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_los8], 0.0) - ISNULL(j.[bar_los8], isnull(h.[bar_los8], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_los8], 0.0) - ISNULL(j.[bar_los8], 0.0)) AS NUMERIC(19, 2))
                   end as bar_los8_change,
                   case
                       when @rolling_business_dt = 'LAST_OPTIMIZATION'
                            and j.bar_by_day is NULL then
                           0
                       when a.occupancy_dt <= @business_dt then
                           CAST((isnull(h.[bar_by_day], 0.0) - ISNULL(j.[bar_by_day], isnull(h.[bar_by_day], 0.0))) AS NUMERIC(19, 2))
                       else
                           CAST((isnull(h.[bar_by_day], 0.0) - ISNULL(j.[bar_by_day], 0.0)) AS NUMERIC(19, 2))
                   end as bar_by_day_change,

                   --	p.Webrate_Competitors_Name,
                   --	p.Webrate_Currency,
                   --	p.Webrate_RateValue

                   comp1.Webrate_RateValue as comp1_rate,
                   comp1.webrate_competitors_name as comp1_name,
                   comp2.webrate_ratevalue as comp2_rate,
                   comp2.webrate_competitors_name as comp2_name,
                   comp3.webrate_ratevalue as comp3_rate,
                   comp3.webrate_competitors_name as comp3_name,
                   comp4.webrate_ratevalue as comp4_rate,
                   comp4.webrate_competitors_name as comp4_name,
                   comp5.webrate_ratevalue as comp5_rate,
                   comp5.webrate_competitors_name as comp5_name,
                   comp6.Webrate_RateValue as comp6_rate,
                   comp6.webrate_competitors_name as comp6_name,
                   comp7.webrate_ratevalue as comp7_rate,
                   comp7.webrate_competitors_name as comp7_name,
                   comp8.webrate_ratevalue as comp8_rate,
                   comp8.webrate_competitors_name as comp8_name,
                   comp9.webrate_ratevalue as comp9_rate,
                   comp9.webrate_competitors_name as comp9_name,
                   comp10.webrate_ratevalue as comp10_rate,
                   comp10.webrate_competitors_name as comp10_name,
                   comp11.Webrate_RateValue as comp11_rate,
                   comp11.webrate_competitors_name as comp11_name,
                   comp12.webrate_ratevalue as comp12_rate,
                   comp12.webrate_competitors_name as comp12_name,
                   comp13.webrate_ratevalue as comp13_rate,
                   comp13.webrate_competitors_name as comp13_name,
                   comp14.webrate_ratevalue as comp14_rate,
                   comp14.webrate_competitors_name as comp14_name,
                   comp15.webrate_ratevalue as comp15_rate,
                   comp15.webrate_competitors_name as comp15_name,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp1.webrate_ratevalue - pace_comp1.Webrate_RateValue
                   END comp1_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp2.webrate_ratevalue - pace_comp2.Webrate_RateValue
                   END comp2_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp3.webrate_ratevalue - pace_comp3.Webrate_RateValue
                   END comp3_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp4.webrate_ratevalue - pace_comp4.Webrate_RateValue
                   END comp4_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp5.webrate_ratevalue - pace_comp5.Webrate_RateValue
                   END comp5_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp6.webrate_ratevalue - pace_comp6.Webrate_RateValue
                   END comp6_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp7.webrate_ratevalue - pace_comp7.Webrate_RateValue
                   END comp7_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp8.webrate_ratevalue - pace_comp8.Webrate_RateValue
                   END comp8_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp9.webrate_ratevalue - pace_comp9.Webrate_RateValue
                   END comp9_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp10.webrate_ratevalue - pace_comp10.Webrate_RateValue
                   END comp10_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp11.webrate_ratevalue - pace_comp11.Webrate_RateValue
                   END comp11_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp12.webrate_ratevalue - pace_comp12.Webrate_RateValue
                   END comp12_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp13.webrate_ratevalue - pace_comp13.Webrate_RateValue
                   END comp13_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp14.webrate_ratevalue - pace_comp14.Webrate_RateValue
                   END comp14_change,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                           NULL
                       ELSE
                           comp15.webrate_ratevalue - pace_comp15.Webrate_RateValue
                   END comp15_change,
                   groupBlock.block as block,
                   groupBlock.block_available as block_available,
                   groupBlock.block_pickup as block_pickup,
                   isnull(   case a.total_accom_capacity
                                 when 0 then
                                     0
                                 else
                             (b.ac_occupancy_forecast / a.total_accom_capacity) * 100
                             end,
                             0.0
                         ) OccupancyForecastPerCurrent_without_ooo,
                   isnull(   ((case a.total_accom_capacity
                                   when 0 then
                                       0
                                   else
                             (b.ac_occupancy_forecast / a.total_accom_capacity) * 100
                               end
                              ) - (case e.total_accom_capacity
                                       when 0 then
                                           0
                                       else
                             (g.occupancy_nbr_businessstartdate / e.total_accom_capacity) * 100
                                   end
                                  )
                             ),
                             0.0
                         ) as occupancyforecastperchange_without_ooo,
                   isNull(highest_bar_current.Decision_Reason_Type, '-') as decisionreasontypecurrent,
                   CASE @rolling_business_dt
                       WHEN 'LAST_OPTIMIZATION' THEN
                       (
                           select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                      highest_bar_current.Decision_Reason_Type,
                                                                                                      highest_bar_change_asOfLastOptimization.Decision_Reason_Type
                                                                                                  )
                       )
                       ELSE
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current.Decision_Reason_Type,
                                                                                                  highest_bar_change.Decision_Reason_Type
                                                                                              )
                   )
                   END as decisionreasontypechange,
                   isNull(highest_bar_current_with_los.LOS1, '-') as decisionreasontypecurrent_los1,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS1,
                                                                                                  highest_bar_change_with_los.LOS1
                                                                                              )
                   ) as decisionreasontypechange_los1,
                   isNull(highest_bar_current_with_los.LOS2, '-') as decisionreasontypecurrent_los2,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS2,
                                                                                                  highest_bar_change_with_los.LOS2
                                                                                              )
                   ) decisionreasontypechange_los2,
                   isNull(highest_bar_current_with_los.LOS3, '-') as decisionreasontypecurrent_los3,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS3,
                                                                                                  highest_bar_change_with_los.LOS3
                                                                                              )
                   ) as decisionreasontypechange_los3,
                   isNull(highest_bar_current_with_los.LOS4, '-') as decisionreasontypecurrent_los4,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS4,
                                                                                                  highest_bar_change_with_los.LOS4
                                                                                              )
                   ) as decisionreasontypechange_los4,
                   isNull(highest_bar_current_with_los.LOS5, '-') as decisionreasontypecurrent_los5,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS5,
                                                                                                  highest_bar_change_with_los.LOS5
                                                                                              )
                   ) as decisionreasontypechange_los5,
                   isNull(highest_bar_current_with_los.LOS6, '-') as decisionreasontypecurrent_los6,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS6,
                                                                                                  highest_bar_change_with_los.LOS6
                                                                                              )
                   ) decisionreasontypechange_los6,
                   isNull(highest_bar_current_with_los.LOS7, '-') as decisionreasontypecurrent_los7,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS7,
                                                                                                  highest_bar_change_with_los.LOS7
                                                                                              )
                   ) as decisionreasontypechange_los7,
                   isNull(highest_bar_current_with_los.LOS8, '-') as decisionreasontypecurrent_los8,
                   (
                       select dbo.ufn_determine_change_by_current_and_past_value_with_closeLV0(
                                                                                                  highest_bar_current_with_los.LOS8,
                                                                                                  highest_bar_change_with_los.LOS8
                                                                                              )
                   ) as decisionreasontypechange_los8
            from
            (
                select @property_id property_id,
                       CAST(calendar_date as date) Occupancy_DT,
                       accom_class_id
                from calendar_dim
                    left join @temp_rc
                        on accom_class_id is not null
                where calendar_date
                between @start_date and @end_date
            ) base
                left join @temp_adr_revpar as a
                    on base.property_id = a.property_id
                       and base.Occupancy_DT = a.occupancy_dt
                       and base.accom_class_id = a.accom_class_id
                left join @temp_propor_propar as aa
                    on base.occupancy_dt = aa.occupancy_dt
                       and base.property_id = aa.property_id
                       and base.accom_class_id = aa.accom_class_id
                left join @temp_occupancy_forecast as b
                    on base.occupancy_dt = b.occupancy_dt
                       and base.property_id = b.property_id
                       and base.accom_class_id = b.accom_class_id
                left join @temp_occupancy_forecast_profit as bb
                    on base.occupancy_dt = bb.occupancy_dt
                       and base.property_id = bb.property_id
                       and base.accom_class_id = bb.accom_class_id
                left join @temp_adr_revpar_asof as e
                    on base.property_id = e.property_id
                       and base.occupancy_dt = e.occupancy_dt
                       and base.accom_class_id = e.accom_class_id
                left join @temp_propor_propar_asof as ee
                    on base.property_id = ee.property_id
                       and base.occupancy_dt = ee.occupancy_dt
                       and base.accom_class_id = ee.accom_class_id
                left join
                (
                    select *
                    from ufn_get_special_events_including_information_only_events_by_property(@property_id, @start_date, @end_date)
                ) as f
                    on a.property_id = f.property_id
                       and a.occupancy_dt = f.event_cal_date
                left join @temp_occupancy_forecast_asof as g
                    on base.property_id = g.property_id
                       and base.occupancy_dt = g.occupancy_dt
                       and base.accom_class_id = g.accom_class_id
                left join @temp_occupancy_forecast_profit_asof as gg
                    on base.property_id = gg.property_id
                       and base.occupancy_dt = gg.occupancy_dt
                       and base.accom_class_id = gg.accom_class_id
                left join @temp_los_barrate as h
                    on base.property_id = h.property_id
                       and base.occupancy_dt = h.arrival_dt
                       and base.accom_class_id = h.accom_class_id
                left join @temp_rate_code as i
                    on base.property_id = i.property_id
                       and base.occupancy_dt = i.arrival_dt
                       and base.accom_class_id = i.accom_class_id
                left join @temp_los_barrate_asof as j
                    on base.property_id = j.property_id
                       and base.occupancy_dt = j.occupancy_dt
                       and base.accom_class_id = j.accom_class_id
                left join @temp_decision_lrv as k
                    on base.property_id = k.property_id
                       and base.occupancy_dt = k.occupancy_dt
                       and base.accom_class_id = k.accom_class_id
                left join @temp_lrv_asof as l
                    on base.property_id = l.property_id
                       and base.occupancy_dt = l.occupancy_dt
                       and base.accom_class_id = l.accom_class_id
                left join
                (
                    select q1.property_id,
                           q1.occupancy_dt,
                           q1.overbooking,
                           q1.overbooking as total_overbooking,
                           q2.overbooking as pace_overbooking,
                           q1.accom_class_id
                    from @temp_ovrbk_decision as q1
                        left join @temp_ovrbk_decision_asof as q2
                            on q1.property_id = q2.property_id
                               and q1.occupancy_dt = q2.occupancy_dt --> overbook decision by occupancy range and as of business date
                               and q1.accom_class_id = q2.accom_class_id
                ) as m
                    on base.property_id = m.property_id
                       and base.occupancy_dt = m.occupancy_dt
                       and base.accom_class_id = m.accom_class_id
                left join
                (
                    select property_id,
                           accom_class_code,
                           accom_class_id
                    from accom_class
                    where accom_class_id in (
                                                select value from varcharToInt(@roomclass_id, ',')
                                            )
                          and property_id = @property_id
                ) as n
                    on base.property_id = n.property_id
                       and base.accom_class_id = n.accom_class_id
                left join @web_rate_comp_1 as comp1
                    on base.Property_ID = comp1.Property_ID
                       and base.Occupancy_DT = comp1.Occupancy_DT
                       and base.accom_Class_id = comp1.accom_Class_id
                left join @web_rate_comp_2 as comp2
                    on base.Occupancy_DT = comp2.Occupancy_DT
                       and base.Property_ID = comp2.Property_ID
                       and base.accom_Class_id = comp2.accom_Class_id
                left join @web_rate_comp_3 as comp3
                    on base.Occupancy_DT = comp3.Occupancy_DT
                       and base.Property_ID = comp3.Property_ID
                       and base.accom_Class_id = comp3.accom_Class_id
                left join @web_rate_comp_4 as comp4
                    on base.Occupancy_DT = comp4.Occupancy_DT
                       and base.Property_ID = comp4.Property_ID
                       and base.accom_Class_id = comp4.accom_Class_id
                left join @web_rate_comp_5 as comp5
                    on base.Occupancy_DT = comp5.Occupancy_DT
                       and base.Property_ID = comp5.Property_ID
                       and base.accom_Class_id = comp5.accom_Class_id
                left join @web_rate_comp_6 as comp6
                    on base.Property_ID = comp6.Property_ID
                       and base.Occupancy_DT = comp6.Occupancy_DT
                       and base.accom_Class_id = comp6.accom_Class_id
                left join @web_rate_comp_7 as comp7
                    on base.Occupancy_DT = comp7.Occupancy_DT
                       and base.Property_ID = comp7.Property_ID
                       and base.accom_Class_id = comp7.accom_Class_id
                left join @web_rate_comp_8 as comp8
                    on base.Occupancy_DT = comp8.Occupancy_DT
                       and base.Property_ID = comp8.Property_ID
                       and base.accom_Class_id = comp8.accom_Class_id
                left join @web_rate_comp_9 as comp9
                    on base.Occupancy_DT = comp9.Occupancy_DT
                       and base.Property_ID = comp9.Property_ID
                       and base.accom_Class_id = comp9.accom_Class_id
                left join @web_rate_comp_10 as comp10
                    on base.Occupancy_DT = comp10.Occupancy_DT
                       and base.Property_ID = comp10.Property_ID
                       and base.accom_Class_id = comp10.accom_Class_id
                left join @web_rate_comp_11 as comp11
                    on base.Property_ID = comp11.Property_ID
                       and base.Occupancy_DT = comp11.Occupancy_DT
                       and base.accom_Class_id = comp11.accom_Class_id
                left join @web_rate_comp_12 as comp12
                    on base.Occupancy_DT = comp12.Occupancy_DT
                       and base.Property_ID = comp12.Property_ID
                       and base.accom_Class_id = comp12.accom_Class_id
                left join @web_rate_comp_13 as comp13
                    on base.Occupancy_DT = comp13.Occupancy_DT
                       and base.Property_ID = comp13.Property_ID
                       and base.accom_Class_id = comp13.accom_Class_id
                left join @web_rate_comp_14 as comp14
                    on base.Occupancy_DT = comp14.Occupancy_DT
                       and base.Property_ID = comp14.Property_ID
                       and base.accom_Class_id = comp14.accom_Class_id
                left join @web_rate_comp_15 as comp15
                    on base.Occupancy_DT = comp15.Occupancy_DT
                       and base.Property_ID = comp15.Property_ID
                       and base.accom_Class_id = comp15.accom_Class_id
                left join @webrate_asof_businessdate_1 as pace_comp1
                    on base.Property_ID = pace_comp1.Property_ID
                       and base.Occupancy_DT = pace_comp1.Occupancy_DT
                       and base.accom_class_id = pace_comp1.accom_class_id
                left join @webrate_asof_businessdate_2 as pace_comp2
                    on base.Property_ID = pace_comp2.Property_ID
                       and base.Occupancy_DT = pace_comp2.Occupancy_DT
                       and base.accom_class_id = pace_comp2.accom_class_id
                left join @webrate_asof_businessdate_3 as pace_comp3
                    on base.Property_ID = pace_comp3.Property_ID
                       and base.Occupancy_DT = pace_comp3.Occupancy_DT
                       and base.accom_class_id = pace_comp3.accom_class_id
                left join @webrate_asof_businessdate_4 as pace_comp4
                    on base.Property_ID = pace_comp4.Property_ID
                       and base.Occupancy_DT = pace_comp4.Occupancy_DT
                       and base.accom_class_id = pace_comp4.accom_class_id
                left join @webrate_asof_businessdate_5 as pace_comp5
                    on base.Property_ID = pace_comp5.Property_ID
                       and base.Occupancy_DT = pace_comp5.Occupancy_DT
                       and base.accom_class_id = pace_comp5.accom_class_id
                left join @webrate_asof_businessdate_6 as pace_comp6
                    on base.Property_ID = pace_comp6.Property_ID
                       and base.Occupancy_DT = pace_comp6.Occupancy_DT
                       and base.accom_class_id = pace_comp6.accom_class_id
                left join @webrate_asof_businessdate_7 as pace_comp7
                    on base.Property_ID = pace_comp7.Property_ID
                       and base.Occupancy_DT = pace_comp7.Occupancy_DT
                       and base.accom_class_id = pace_comp7.accom_class_id
                left join @webrate_asof_businessdate_8 as pace_comp8
                    on base.Property_ID = pace_comp8.Property_ID
                       and base.Occupancy_DT = pace_comp8.Occupancy_DT
                       and base.accom_class_id = pace_comp8.accom_class_id
                left join @webrate_asof_businessdate_9 as pace_comp9
                    on base.Property_ID = pace_comp9.Property_ID
                       and base.Occupancy_DT = pace_comp9.Occupancy_DT
                       and base.accom_class_id = pace_comp9.accom_class_id
                left join @webrate_asof_businessdate_10 as pace_comp10
                    on base.Property_ID = pace_comp10.Property_ID
                       and base.Occupancy_DT = pace_comp10.Occupancy_DT
                       and base.accom_class_id = pace_comp10.accom_class_id
                left join @webrate_asof_businessdate_11 as pace_comp11
                    on base.Property_ID = pace_comp11.Property_ID
                       and base.Occupancy_DT = pace_comp11.Occupancy_DT
                       and base.accom_class_id = pace_comp11.accom_class_id
                left join @webrate_asof_businessdate_12 as pace_comp12
                    on base.Property_ID = pace_comp12.Property_ID
                       and base.Occupancy_DT = pace_comp12.Occupancy_DT
                       and base.accom_class_id = pace_comp12.accom_class_id
                left join @webrate_asof_businessdate_13 as pace_comp13
                    on base.Property_ID = pace_comp13.Property_ID
                       and base.Occupancy_DT = pace_comp13.Occupancy_DT
                       and base.accom_class_id = pace_comp13.accom_class_id
                left join @webrate_asof_businessdate_14 as pace_comp14
                    on base.Property_ID = pace_comp14.Property_ID
                       and base.Occupancy_DT = pace_comp14.Occupancy_DT
                       and base.accom_class_id = pace_comp14.accom_class_id
                left join @webrate_asof_businessdate_15 as pace_comp15
                    on base.Property_ID = pace_comp15.Property_ID
                       and base.Occupancy_DT = pace_comp15.Occupancy_DT
                       and base.accom_class_id = pace_comp15.accom_class_id
                --Archana added group block-Aggregated-START
                left join @temp_groupBlock_groupPickupByRoomClass as groupBlock
                    on base.property_id = groupBlock.property_id
                       and base.occupancy_dt = groupBlock.occupancy_dt
                       and base.accom_class_id = groupBlock.roomClass_id
                --Archana added group block-Aggregated-END
                left join
                (
                    select dbo.Property_id,
                           Arrival_dt,
                           Accom_Class_ID,
                           (case Decision_Reason_Type_id
                                when 2 then
                                    'Y'
                                else
                                    'N'
                            end
                           ) as Decision_Reason_Type
                    from dbo.Decision_Bar_Output dbo
                    where los = -1
                          and Accom_Class_ID in (
                                                    select value from varcharToInt(@roomclass_id, ',')
                                                )
                          and Arrival_DT
                          between @start_date and @end_date
                ) as highest_bar_current
                    on base.property_id = highest_bar_current.Property_ID
                       and base.occupancy_dt = highest_bar_current.Arrival_DT
                       and base.accom_class_id = highest_bar_current.accom_class_id
                left join
                (
                    select pbo.Property_id,
                           Arrival_dt,
                           accom_class_id,
                           (case Decision_Reason_Type_id
                                when 2 then
                                    'Y'
                                else
                                    'N'
                            end
                           ) as Decision_Reason_Type
                    from dbo.PACE_Bar_Output pbo
                        inner join Decision de
                            on pbo.Decision_ID = de.Decision_ID
                               and de.Business_DT = @business_dt
                               and Decision_Type_ID = 1
                    where los = -1
                          and Accom_Class_ID in (
                                                    select value from varcharToInt(@roomclass_id, ',')
                                                )
                          and Arrival_DT
                          between @start_date and @end_date
                ) as highest_bar_change
                    on base.property_id = highest_bar_change.Property_ID
                       and base.occupancy_dt = highest_bar_change.Arrival_DT
                       and base.accom_class_id = highest_bar_change.accom_class_id
                left join
                (
                    select property_id,
                           Arrival_dt,
                           q2.Accom_Class_ID,
                           (case Decision_Reason_Type_id
                                when 2 then
                                    'Y'
                                else
                                    'N'
                            end
                           ) as Decision_Reason_Type
                    from
                    (
                        select pbo.Property_id,
                               Arrival_dt,
                               Decision_ID,
                               Decision_Reason_Type_id,
                               Accom_Class_ID,
                               DENSE_RANK() over (order by Decision_ID desc) rnk
                        from dbo.PACE_Bar_Output_NOTIFICATION pbo
                        where los = -1
                              and Accom_Class_ID in (
                                                        select value from varcharToInt(@roomclass_id, ',')
                                                    )
                              and Arrival_DT
                              between @start_date and @end_date
                    ) q2
                    where rnk = 2
                ) as highest_bar_change_asOfLastOptimization
                    on base.property_id = highest_bar_change_asOfLastOptimization.Property_ID
                       and base.occupancy_dt = highest_bar_change_asOfLastOptimization.Arrival_DT
                       and base.accom_class_id = highest_bar_change_asOfLastOptimization.accom_class_id
                left join @temp_hbr_report_close as highest_bar_current_with_los
                    on base.property_id = highest_bar_current_with_los.Property_ID
                       and base.occupancy_dt = highest_bar_current_with_los.Arrival_DT
                       and base.accom_class_id = highest_bar_current_with_los.accom_class_id
                left join @temp_hbr_report as highest_bar_change_with_los
                    on base.property_id = highest_bar_change_with_los.Property_ID
                       and base.occupancy_dt = highest_bar_change_with_los.Arrival_DT
                       and base.accom_class_id = highest_bar_change_with_los.accom_class_id
        ) data
    ) data2
    group by occupancy_dt,
             dow,
             property_id,
             specialevent
    order by occupancy_dt
end
GO