DROP PROCEDURE IF EXISTS [opera].[usp_opera_sharers_update_primary]
GO

/*************************************************************************************

Procedure Name: usp_opera_sharers_update_primary

Input Parameters : NA
	
Ouput Parameter : NA

***************************************************************************************/

CREATE PROCEDURE [opera].[usp_opera_sharers_update_primary]

AS

BEGIN

	IF OBJECT_ID('tempdb..#tempConfno') IS NOT NULL DROP TABLE #tempConfno
	select ss1.conf_no,ss1.trx_dt into #tempConfno
	from 
	(select full_sorted_sharers, confirmation_number as conf_no,transaction_dt as trx_dt,
	row_number() over (partition by full_sorted_sharers order by confirmation_number,transaction_dt) Rownum
		from opera.Sharers_ShareWithFullSharers 
	) ss1
	where ss1.rownum = 1

	IF OBJECT_ID('tempdb..#tempHash') IS NOT NULL DROP TABLE #tempHash
	select hash_value into #tempHash 
		from opera.Sharers_OnlySharedTrans 
		group by hash_value 
		having count(distinct room_type) = 1

	UPDATE os1 SET [Confirmation_Number] = os2.Confirmation_Number,[Reservation_Status]=os2.Reservation_Status
	, Is_Shared = os2.Is_Shared,[RATE_CODE]= os2.RATE_CODE
	, [MARKET_CODE]=os2.MARKET_CODE, [HOTEL_MARKET_CODE]=os2.HOTEL_MARKET_CODE, [ROOM]=os2.ROOM
	, [ROOM_TYPE]=os2.ROOM_TYPE, [SOURCE_CODE]=os2.SOURCE_CODE,[CHANNEL]=os2.CHANNEL,[BOOKED_ROOM_TYPE]=os2.BOOKED_ROOM_TYPE, [NATIONALITY]=os2.NATIONALITY,[Reservation_Type]=os2.Reservation_Type, [Reservation_Name_ID] =os2.Reservation_Name_ID
	, [Booking_TM]=os2.Booking_TM,[Booking_DT]=os2.Booking_DT,[Rate_Category]=os2.[Rate_Category] 

	FROM opera.Sharers_OnlySharedTrans os1 
	JOIN 
	(		
		select [Confirmation_Number],[Reservation_Status],Is_Shared,[Sharers], [RATE_CODE],[RATE_AMOUNT],[MARKET_CODE],[HOTEL_MARKET_CODE]
		,[ROOM], [ROOM_TYPE],[SOURCE_CODE],[CHANNEL],[BOOKED_ROOM_TYPE],[NATIONALITY],[Reservation_Type]
		,[Reservation_Name_ID],[Booking_TM],[Booking_DT],[Rate_Category],th.[HASH_VALUE] 
		from opera.Sharers_OnlySharedTrans as os3   
		join #tempConfno os4 on os3.Confirmation_Number = os4.conf_no and os3.Transaction_DT = os4.trx_dt 
		join #tempHash th ON th.hash_value = CONVERT(NVARCHAR(32),HashBytes('MD5', os3.sharers),2) 
	) as os2 
	on os1.hash_value = os2.hash_value

    UPDATE OS1
    SET    rate_amount = t.rate_amount
        FROM   opera.sharers_onlysharedtrans OS1
           JOIN (SELECT sharers,
                        transaction_dt,
                        Max(rate_amount) RATE_AMOUNT
                 FROM   opera.sharers_onlysharedtrans OS1
                 GROUP  BY sharers,
                           transaction_dt) t
    ON OS1.sharers = T.sharers
        AND OS1.transaction_dt = T.transaction_dt

    DROP TABLE #tempconfno
    DROP TABLE #temphash

END
;


