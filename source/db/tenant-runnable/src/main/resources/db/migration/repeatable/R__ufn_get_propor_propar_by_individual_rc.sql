if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_propor_propar_by_individual_rc]'))
drop function [ufn_get_propor_propar_by_individual_rc]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE function [dbo].[ufn_get_propor_propar_by_individual_rc]
(
		@property_id int,
		@roomclass_id varchar(500),
		@start_date date,
		@end_date date,
		@use_physical_capacity int,
		@includeZeroCapacityRT int
)		
returns  @propor_propar table
	(	
		occupancy_dt	date,
		property_id	int,
		accom_class_id int,
		rooms_sold	numeric(18,0),
		total_accom_capacity	numeric(18,0),
		outoforder	numeric(18,0),
		total_profit  numeric(19,5),
		propor	numeric(19,5),
		propar	numeric(19,5)
	)
as

begin	
		insert into @propor_propar
			select 
			aa.occupancy_dt,
			aa.property_id,
			ac.accom_class_id,
			sum(aa.rooms_sold) as rooms_sold,
			sum(aa.accom_capacity) as accom_capacity,
			sum((aa.rooms_not_avail_maint+aa.rooms_not_avail_other)) as outoforder,
 			sum(aa.total_profit) as total_profit,
			propor =
			case (sum(aa.rooms_sold))
				when 0 then 0
				else round(sum(aa.total_profit) / sum(aa.rooms_sold), 2)
			end,
      (select propar from dbo.ufn_calculate_propar(sum(aa.total_profit), sum(aa.accom_capacity), sum(aa.rooms_not_avail_maint+aa.rooms_not_avail_other), @use_physical_capacity)) as propar
			from accom_activity aa 
			inner join accom_type at on aa.accom_type_id = at.accom_type_id and aa.property_id = at.property_id and at.status_id=1 and at.System_default=0  and Display_Status_ID
					 in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end )
			inner join accom_class ac on ac.accom_class_id = at.accom_class_id and ac.property_id = at.property_id and ac.status_id=1and ac.System_default=0
			where aa.property_id = @property_id and
				aa.occupancy_dt between @start_date and @end_date	and 
				ac.accom_class_id in (select value from dbo.varcharToInt(@roomclass_id,','))
			group by aa.occupancy_dt,aa.property_id,ac.accom_class_id 
			order by aa.occupancy_dt,aa.property_id,ac.accom_class_id
	return
end


GO

