if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_decision_lrv_by_individual_rc]'))
drop function [ufn_get_decision_lrv_by_individual_rc]

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create function [dbo].[ufn_get_decision_lrv_by_individual_rc]
(
		@property_id int,
		@roomclass_id varchar(500),
		@start_date date,
		@end_date date
)		
returns  @decision_lrv table
	(	
		
		property_id	int,
		occupancy_dt date,
		accom_class_id int,
		accom_class_code nvarchar(50),
		lrv numeric(19,5)
	)
as
begin
		insert into @decision_lrv
		select 
			dl.property_id,
			dl.occupancy_dt,
			ac.accom_class_id,
			ac.accom_class_code,
			dl.lrv 
		from decision_lrv dl, accom_class ac
		where dl.accom_class_id = ac.accom_class_id and ac.accom_class_id in (select value from dbo.varcharToInt(@roomclass_id,','))
		and dl.property_id=@property_id and dl.occupancy_dt between @start_date and @end_date
		order by dl.occupancy_dt
return
end

