drop function if exists [dbo].[ufn_get_cp_pace_los_barrate_diff]
GO

/****** Object:  UserDefinedFunction [dbo].[ufn_get_cp_pace_los_barrate_diff]    Script Date: 5/26/2020 1:47:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create function [dbo].[ufn_get_cp_pace_los_barrate_diff]
(
       @property_id int,
       @occupancy_dt date,
       @start_date date,
       @end_date date
)
returns  @cp_pace_los_barrate table
       (             
              dayOfArrival int,
              property_id   int,
              business_dt date,
              occupancy_dt date,
              BarRate [numeric](19, 5),
              bar_override nvarchar(50)
       )
as
begin
       
       declare @temp_cp_pace_bar_output table 
       (
              Property_ID int,
              Arrival_DT date,
              Decision_ID int,
              BarPrice [numeric](19, 5),
              [bar_override] [nvarchar](50)

       )
    
       declare @pre_final_result table
       (
         Business_dt date,
         Decision_ID int,
		 Final_Bar numeric (19, 5),
		 Override nvarchar (50)
       );

        insert into @pre_final_result
        select
            case when (business_dt < @start_date) then @start_date else business_dt end as business_dt,
            Decision_ID,
            Final_BAR,
            Override
        from (
            select *,
                LEAD(business_dt, 1) over (partition BY rn ORDER BY Business_Dt) as OneLessBDThanStartDt
            from (
                select
                    CPDBO.Business_dt,
                    CPDBO.Decision_ID, CPDBO.Final_BAR, CPDBO.Override,
                    ROW_NUMBER() over(PARTITION BY CPDBO.Business_dt order by cpdbo.Decision_ID desc) as rn
                from
                    CP_Pace_Decision_Bar_Output_Differential  CPDBO
                        inner join Decision d on cpdbo.Decision_ID = d.Decision_ID
                        inner join Accom_Type AT on CPDBO.Accom_Type_ID = AT.Accom_Type_ID
                    inner join Accom_Class AC on AT.Accom_Class_ID = AC.Accom_Class_ID AND AC.master_class = 1 and AC.Status_ID = 1
                    inner join CP_Cfg_AC  CCA on CPDBO.Accom_Type_ID = CCA.Accom_Type_ID AND AT.Accom_Class_ID = CCA.Accom_Class_ID
                where
                    d.Decision_Type_ID = 1 and
                    CPDBO.Arrival_DT = @occupancy_dt
                        and cpdbo.business_dt <= @end_date
                        and CPDBO.Product_ID =1
            ) A where A.rn = 1
        ) B where OneLessBDThanStartDt >= @start_date OR OneLessBDThanStartDt is NULL

        insert into @temp_cp_pace_bar_output
        select
            @property_id as Property_ID,
            calendar_date as Business_Dt,
            Decision_ID,
            Final_Bar,
            Override
        from (
            select *,
                ROW_NUMBER() over(PARTITION BY cd.calendar_date order by pfr.Decision_ID desc) as rn
            from @pre_final_result pfr
                     cross join (select cast(cd.calendar_date as DATE) as calendar_date from calendar_dim cd where cd.calendar_date between @start_date and DATEADD(DAY, -1, @end_date)) as cd
            where
                    cd.calendar_date >= pfr.Business_DT
        ) E where E.rn = 1

       insert into @cp_pace_los_barrate
       select 
       DATEDIFF(day,  base.Business_DT,@occupancy_dt) as daystoArrival,
       @property_id as property_id,
       base.Business_DT businessdate,
       @occupancy_dt  as occupancy_dt,
       a.BarPrice as BarRate,
       a.bar_override as bar_override
       from
       (
              select CAST(calendar_date as date) Business_DT
                from calendar_dim 
               where calendar_date between @start_date and @end_date
       ) base left join
       @temp_cp_pace_bar_output a  on base.Business_DT=a.Arrival_DT

       order by base.Business_DT DESC    
       
       return
end
