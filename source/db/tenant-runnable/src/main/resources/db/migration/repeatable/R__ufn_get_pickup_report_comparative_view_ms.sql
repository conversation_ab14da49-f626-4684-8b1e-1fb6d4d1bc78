IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ufn_get_pickup_report_comparative_view_ms]'))
DROP FUNCTION [dbo].[ufn_get_pickup_report_comparative_view_ms]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Function Name: ufn_get_pickup_report_comparative_view_ms

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@Market_segment_id --> comma seperated list of Market_segment_Ids (MAX 8 supported)
	@business_start_dt --> this is the derived version of caughtup_date(start_date)..for more details please check the data dictionary.
	@business_end_dt --> this is the derived version of caughtup_date(end_date)..for more details please check the data dictionary.
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_business_start_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_business_end_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_start_date  --> used when @isRollingDate is 1, else, this can be blank
	@rolling_end_date  --> used when @isRollingDate is 1, else, this can be blank
	
Ouput Parameter : NA

Execution: this is just an example
	Example : Pickup Report by Market segment in comparative view
	------------------------------------------------
	select * from ufn_get_pickup_report_comparative_view_ms (10016,'109,110,111','2011-06-17','2011-06-30','2011-07-01','2011-07-02')


	
Purpose: The purpose of this procedure is to report Pickup report by Market segment in comparative view

Assumptions : Please make sure to pass Market_segment_Ids as a string ('30').
              When you are passing multiple Market segment ids, please enclose them in single quotes
              seperated by a comma - ('30,31,63'). Please refer to example
		 
Author: Atul 

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/07/2012		Atul				Shendye					Initial Version
03/04/2013		Archana				Mundaye					Added group block, group picked up and group available columns
11/16/2021 		Shilpa 				Shaha					BHASK-1974 : Changed logic not to substract actual data from actual data in case no data present in pace.This will applicable while calculating either pickup/change
12/09/2021 		Shilpa 				Shaha					FEYNMAN-877 Pickup/Change Report: Data Discrepancy for past dates
***************************************************************************************/
CREATE function [dbo].[ufn_get_pickup_report_comparative_view_ms]
(
	@property_id int,
	@Mkt_seg_id varchar(500),
	@business_start_dt date,
	@business_end_dt date,
	@start_date date,
	@end_date date,

	@isRollingDate int,
	@rolling_business_start_dt nvarchar(50),
	@rolling_business_end_dt nvarchar(50),		
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@includeDisconMS smallint
	
)		
returns  @pickup_report_ms_comparative_view table
	(	
		occupancy_dt date
		,dow varchar(10)

		,ms1_name nvarchar(150)
		,ms2_name nvarchar(150)
		,ms3_name nvarchar(150)
		,ms4_name nvarchar(150)
		,ms5_name nvarchar(150)
		,ms6_name nvarchar(150)
		,ms7_name nvarchar(150)
		,ms8_name nvarchar(150)

		,ms1_roomsoldcurrent int
		,ms1_roomssoldpickup int                     
		,ms2_roomsoldcurrent int                     
		,ms2_roomssoldpickup int                     
		,ms3_roomsoldcurrent int                     
		,ms3_roomssoldpickup int                     
		,ms4_roomsoldcurrent int                     
		,ms4_roomssoldpickup int                     
		,ms5_roomsoldcurrent int                     
		,ms5_roomssoldpickup int                     
		,ms6_roomsoldcurrent int                     
		,ms6_roomssoldpickup int                     
		,ms7_roomsoldcurrent int                     
		,ms7_roomssoldpickup int                     
		,ms8_roomsoldcurrent int                     
		,ms8_roomssoldpickup int                     
		
		,ms1_occfcstcurrent numeric(8,2)             
		,ms1_occfcstpickup numeric(8,2)              
		,ms2_occfcstcurrent numeric(8,2)             
		,ms2_occfcstpickup numeric(8,2)              
		,ms3_occfcstcurrent numeric(8,2)             
		,ms3_occfcstpickup numeric(8,2)              
		,ms4_occfcstcurrent numeric(8,2)             
		,ms4_occfcstpickup numeric(8,2)              
		,ms5_occfcstcurrent numeric(8,2)             
		,ms5_occfcstpickup numeric(8,2)              
		,ms6_occfcstcurrent numeric(8,2)             
		,ms6_occfcstpickup numeric(8,2)              
		,ms7_occfcstcurrent numeric(8,2)             
		,ms7_occfcstpickup numeric(8,2)              
		,ms8_occfcstcurrent numeric(8,2)             
		,ms8_occfcstpickup numeric(8,2)              
		
		,ms1_bookedroomrevenuecurrent numeric(19,5)  
		,ms1_bookedroomrevenuepickup numeric(19,5)   
		,ms2_bookedroomrevenuecurrent numeric(19,5)  
		,ms2_bookedroomrevenuepickup numeric(19,5)   
		,ms3_bookedroomrevenuecurrent numeric(19,5)  
		,ms3_bookedroomrevenuepickup numeric(19,5)   
		,ms4_bookedroomrevenuecurrent numeric(19,5)  
		,ms4_bookedroomrevenuepickup numeric(19,5)   
		,ms5_bookedroomrevenuecurrent numeric(19,5)  
		,ms5_bookedroomrevenuepickup numeric(19,5)   
		,ms6_bookedroomrevenuecurrent numeric(19,5)  
		,ms6_bookedroomrevenuepickup numeric(19,5)   
		,ms7_bookedroomrevenuecurrent numeric(19,5)  
		,ms7_bookedroomrevenuepickup numeric(19,5)   
		,ms8_bookedroomrevenuecurrent numeric(19,5)  
		,ms8_bookedroomrevenuepickup numeric(19,5)   
		
		
		,ms1_fcstedroomrevenuecurrent numeric(19,5)  
		,ms1_fcstedroomrevenuepickup numeric(19,5)   
		,ms2_fcstedroomrevenuecurrent numeric(19,5)  
		,ms2_fcstedroomrevenuepickup numeric(19,5)   
		,ms3_fcstedroomrevenuecurrent numeric(19,5)  
		,ms3_fcstedroomrevenuepickup numeric(19,5)   
		,ms4_fcstedroomrevenuecurrent numeric(19,5)  
		,ms4_fcstedroomrevenuepickup numeric(19,5)   
		,ms5_fcstedroomrevenuecurrent numeric(19,5)  
		,ms5_fcstedroomrevenuepickup numeric(19,5)   
		,ms6_fcstedroomrevenuecurrent numeric(19,5)  
		,ms6_fcstedroomrevenuepickup numeric(19,5)   
		,ms7_fcstedroomrevenuecurrent numeric(19,5)  
		,ms7_fcstedroomrevenuepickup numeric(19,5)   
		,ms8_fcstedroomrevenuecurrent numeric(19,5)  
		,ms8_fcstedroomrevenuepickup numeric(19,5)   
		
		,ms1_bookedadrcurrent numeric(19,5)          
		,ms1_bookedadrpickup numeric(19,5)           
		,ms2_bookedadrcurrent numeric(19,5)          
		,ms2_bookedadrpickup numeric(19,5)           
		,ms3_bookedadrcurrent numeric(19,5)          
		,ms3_bookedadrpickup numeric(19,5)           
		,ms4_bookedadrcurrent numeric(19,5)          
		,ms4_bookedadrpickup numeric(19,5)           
		,ms5_bookedadrcurrent numeric(19,5)          
		,ms5_bookedadrpickup numeric(19,5)           
		,ms6_bookedadrcurrent numeric(19,5)          
		,ms6_bookedadrpickup numeric(19,5)           
		,ms7_bookedadrcurrent numeric(19,5)          
		,ms7_bookedadrpickup numeric(19,5)           
		,ms8_bookedadrcurrent numeric(19,5)          
		,ms8_bookedadrpickup numeric(19,5)           
		
		,ms1_fcstedadrcurrent numeric(19,5)          
		,ms1_fcstedadrpickup numeric(19,5)           
		,ms2_fcstedadrcurrent numeric(19,5)          
		,ms2_fcstedadrpickup numeric(19,5)           
		,ms3_fcstedadrcurrent numeric(19,5)          
		,ms3_fcstedadrpickup numeric(19,5)           
		,ms4_fcstedadrcurrent numeric(19,5)          
		,ms4_fcstedadrpickup numeric(19,5)           
		,ms5_fcstedadrcurrent numeric(19,5)          
		,ms5_fcstedadrpickup numeric(19,5)           
		,ms6_fcstedadrcurrent numeric(19,5)          
		,ms6_fcstedadrpickup numeric(19,5)           
		,ms7_fcstedadrcurrent numeric(19,5)          
		,ms7_fcstedadrpickup numeric(19,5)           
		,ms8_fcstedadrcurrent numeric(19,5)          
		,ms8_fcstedadrpickup numeric(19,5) 
		
		,ms1_block int                                                  
		,ms1_block_available int  
		,ms1_block_pickup int  
		,ms2_block int                                                
		,ms2_block_available int  
		,ms2_block_pickup int  
		,ms3_block int                                                
		,ms3_block_available int  
		,ms3_block_pickup int  
		,ms4_block int                                              
		,ms4_block_available int  
		,ms4_block_pickup int  
		,ms5_block int                                                 
		,ms5_block_available int  
		,ms5_block_pickup int  
		,ms6_block int                                                 
		,ms6_block_available int  
		,ms6_block_pickup int  
		,ms7_block int                                                
		,ms7_block_available int  
		,ms7_block_pickup int  
		,ms8_block int                                                
		,ms8_block_available int  
		,ms8_block_pickup int           
	)
as

begin
		declare @caughtupdate date 
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
		begin
			set @business_start_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_start_dt ,@caughtupdate))
			set @business_end_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_end_dt ,@caughtupdate))
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
		end

		declare @MktSegStatus varchar(8)
			set @MktSegStatus = '1'
		IF (@includeDisconMS = 1) 
			set @MktSegStatus = '1,3'

		declare @ms1 int
		declare @ms2 int
		declare @ms3 int
		declare @ms4 int
		declare @ms5 int
		declare @ms6 int
		declare @ms7 int
		declare @ms8 int

		declare @ms1_name nvarchar(150)
		declare @ms2_name nvarchar(150)
		declare @ms3_name nvarchar(150)
		declare @ms4_name nvarchar(150)
		declare @ms5_name nvarchar(150)
		declare @ms6_name nvarchar(150)
		declare @ms7_name nvarchar(150)
		declare @ms8_name nvarchar(150)
	
		declare @tempMS table
		(
			number int,
			mkt_seg_id int,
			mkt_seg_name nvarchar(150)
		)
		insert into @tempMS
		select number = ROW_NUMBER() OVER (ORDER BY mkt_seg_id),mkt_seg_id, mkt_seg_name
			from mkt_seg where Property_ID=@property_id and mkt_seg_id in (SELECT Value FROM varcharToInt(@mkt_seg_id,','))
			and Status_ID=1
		
		set @ms1 = (Select mkt_seg_id from @tempMS where number=1)
		set @ms2 = (Select mkt_seg_id from @tempMS where number=2)
		set @ms3 = (Select mkt_seg_id from @tempMS where number=3)
		set @ms4 = (Select mkt_seg_id from @tempMS where number=4)
		set @ms5 = (Select mkt_seg_id from @tempMS where number=5)
		set @ms6 = (Select mkt_seg_id from @tempMS where number=6)
		set @ms7 = (Select mkt_seg_id from @tempMS where number=7)
		set @ms8 = (Select mkt_seg_id from @tempMS where number=8)

		set @ms1_name = (Select mkt_seg_name from @tempMS where number=1)
		set @ms2_name = (Select mkt_seg_name from @tempMS where number=2)
		set @ms3_name = (Select mkt_seg_name from @tempMS where number=3)
		set @ms4_name = (Select mkt_seg_name from @tempMS where number=4)
		set @ms5_name = (Select mkt_seg_name from @tempMS where number=5)
		set @ms6_name = (Select mkt_seg_name from @tempMS where number=6)
		set @ms7_name = (Select mkt_seg_name from @tempMS where number=7)
		set @ms8_name = (Select mkt_seg_name from @tempMS where number=8)

		--- extract report metrics
		insert into @pickup_report_ms_comparative_view
		select 
		occupancy_dt,dow

		,@ms1_name as ms1_name
		,@ms2_name as ms2_name
		,@ms3_name as ms3_name
		,@ms4_name as ms4_name
		,@ms5_name as ms5_name
		,@ms6_name as ms6_name
		,@ms7_name as ms7_name
		,@ms8_name as ms8_name

		,isnull(MAX(ms1_roomsoldcurrent),0.0) as ms1_roomsoldcurrent,isnull(MAX(ms1_roomssoldpickup),0.0) as ms1_roomssoldpickup
		,isnull(MAX(ms2_roomsoldcurrent),0.0) as ms2_roomsoldcurrent,isnull(MAX(ms2_roomssoldpickup),0.0) as ms2_roomssoldpickup
		,isnull(MAX(ms3_roomsoldcurrent),0.0) as ms3_roomsoldcurrent,isnull(MAX(ms3_roomssoldpickup),0.0) as ms3_roomssoldpickup
		,isnull(MAX(ms4_roomsoldcurrent),0.0) as ms4_roomsoldcurrent,isnull(MAX(ms4_roomssoldpickup),0.0) as ms4_roomssoldpickup
		,isnull(MAX(ms5_roomsoldcurrent),0.0) as ms5_roomsoldcurrent,isnull(MAX(ms5_roomssoldpickup),0.0) as ms5_roomssoldpickup
		,isnull(MAX(ms6_roomsoldcurrent),0.0) as ms6_roomsoldcurrent,isnull(MAX(ms6_roomssoldpickup),0.0) as ms6_roomssoldpickup
		,isnull(MAX(ms7_roomsoldcurrent),0.0) as ms7_roomsoldcurrent,isnull(MAX(ms7_roomssoldpickup),0.0) as ms7_roomssoldpickup
		,isnull(MAX(ms8_roomsoldcurrent),0.0) as ms8_roomsoldcurrent,isnull(MAX(ms8_roomssoldpickup),0.0) as ms8_roomssoldpickup
		
		,isnull(MAX(ms1_occfcstcurrent),0.0) as ms1_occfcstcurrent,isnull(MAX(ms1_occfcstpickup),0.0) as ms1_occfcstpickup
		,isnull(MAX(ms2_occfcstcurrent),0.0) as ms2_occfcstcurrent,isnull(MAX(ms2_occfcstpickup),0.0) as ms2_occfcstpickup
		,isnull(MAX(ms3_occfcstcurrent),0.0) as ms3_occfcstcurrent,isnull(MAX(ms3_occfcstpickup),0.0) as ms3_occfcstpickup
		,isnull(MAX(ms4_occfcstcurrent),0.0) as ms4_occfcstcurrent,isnull(MAX(ms4_occfcstpickup),0.0) as ms4_occfcstpickup
		,isnull(MAX(ms5_occfcstcurrent),0.0) as ms5_occfcstcurrent,isnull(MAX(ms5_occfcstpickup),0.0) as ms5_occfcstpickup
		,isnull(MAX(ms6_occfcstcurrent),0.0) as ms6_occfcstcurrent,isnull(MAX(ms6_occfcstpickup),0.0) as ms6_occfcstpickup
		,isnull(MAX(ms7_occfcstcurrent),0.0) as ms7_occfcstcurrent,isnull(MAX(ms7_occfcstpickup),0.0) as ms7_occfcstpickup
		,isnull(MAX(ms8_occfcstcurrent),0.0) as ms8_occfcstcurrent,isnull(MAX(ms8_occfcstpickup),0.0) as ms8_occfcstpickup
		
		,isnull(MAX(ms1_bookedroomrevenuecurrent),0.0) as ms1_bookedroomrevenuecurrent,isnull(MAX(ms1_bookedroomrevenuepickup),0.0) as ms1_bookedroomrevenuepickup
		,isnull(MAX(ms2_bookedroomrevenuecurrent),0.0) as ms2_bookedroomrevenuecurrent,isnull(MAX(ms2_bookedroomrevenuepickup),0.0) as ms2_bookedroomrevenuepickup
		,isnull(MAX(ms3_bookedroomrevenuecurrent),0.0) as ms3_bookedroomrevenuecurrent,isnull(MAX(ms3_bookedroomrevenuepickup),0.0) as ms3_bookedroomrevenuepickup
		,isnull(MAX(ms4_bookedroomrevenuecurrent),0.0) as ms4_bookedroomrevenuecurrent,isnull(MAX(ms4_bookedroomrevenuepickup),0.0) as ms4_bookedroomrevenuepickup
		,isnull(MAX(ms5_bookedroomrevenuecurrent),0.0) as ms5_bookedroomrevenuecurrent,isnull(MAX(ms5_bookedroomrevenuepickup),0.0) as ms5_bookedroomrevenuepickup
		,isnull(MAX(ms6_bookedroomrevenuecurrent),0.0) as ms6_bookedroomrevenuecurrent,isnull(MAX(ms6_bookedroomrevenuepickup),0.0) as ms6_bookedroomrevenuepickup
		,isnull(MAX(ms7_bookedroomrevenuecurrent),0.0) as ms7_bookedroomrevenuecurrent,isnull(MAX(ms7_bookedroomrevenuepickup),0.0) as ms7_bookedroomrevenuepickup
		,isnull(MAX(ms8_bookedroomrevenuecurrent),0.0) as ms8_bookedroomrevenuecurrent,isnull(MAX(ms8_bookedroomrevenuepickup),0.0) as ms8_bookedroomrevenuepickup
		
		
		,isnull(MAX(ms1_fcstedroomrevenuecurrent),0.0) as ms1_fcstedroomrevenuecurrent,isnull(MAX(ms1_fcstedroomrevenuepickup),0.0) as ms1_fcstedroomrevenuepickup
		,isnull(MAX(ms2_fcstedroomrevenuecurrent),0.0) as ms2_fcstedroomrevenuecurrent,isnull(MAX(ms2_fcstedroomrevenuepickup),0.0) as ms2_fcstedroomrevenuepickup
		,isnull(MAX(ms3_fcstedroomrevenuecurrent),0.0) as ms3_fcstedroomrevenuecurrent,isnull(MAX(ms3_fcstedroomrevenuepickup),0.0) as ms3_fcstedroomrevenuepickup
		,isnull(MAX(ms4_fcstedroomrevenuecurrent),0.0) as ms4_fcstedroomrevenuecurrent,isnull(MAX(ms4_fcstedroomrevenuepickup),0.0) as ms4_fcstedroomrevenuepickup
		,isnull(MAX(ms5_fcstedroomrevenuecurrent),0.0) as ms5_fcstedroomrevenuecurrent,isnull(MAX(ms5_fcstedroomrevenuepickup),0.0) as ms5_fcstedroomrevenuepickup
		,isnull(MAX(ms6_fcstedroomrevenuecurrent),0.0) as ms6_fcstedroomrevenuecurrent,isnull(MAX(ms6_fcstedroomrevenuepickup),0.0) as ms6_fcstedroomrevenuepickup
		,isnull(MAX(ms7_fcstedroomrevenuecurrent),0.0) as ms7_fcstedroomrevenuecurrent,isnull(MAX(ms7_fcstedroomrevenuepickup),0.0) as ms7_fcstedroomrevenuepickup
		,isnull(MAX(ms8_fcstedroomrevenuecurrent),0.0) as ms8_fcstedroomrevenuecurrent,isnull(MAX(ms8_fcstedroomrevenuepickup),0.0) as ms8_fcstedroomrevenuepickup
		
		,isnull(MAX(ms1_bookedadrcurrent),0.0) as ms1_bookedadrcurrent,isnull(MAX(ms1_bookedadrpickup),0.0) as ms1_bookedadrpickup
		,isnull(MAX(ms2_bookedadrcurrent),0.0) as ms2_bookedadrcurrent,isnull(MAX(ms2_bookedadrpickup),0.0) as ms2_bookedadrpickup
		,isnull(MAX(ms3_bookedadrcurrent),0.0) as ms3_bookedadrcurrent,isnull(MAX(ms3_bookedadrpickup),0.0) as ms3_bookedadrpickup
		,isnull(MAX(ms4_bookedadrcurrent),0.0) as ms4_bookedadrcurrent,isnull(MAX(ms4_bookedadrpickup),0.0) as ms4_bookedadrpickup
		,isnull(MAX(ms5_bookedadrcurrent),0.0) as ms5_bookedadrcurrent,isnull(MAX(ms5_bookedadrpickup),0.0) as ms5_bookedadrpickup
		,isnull(MAX(ms6_bookedadrcurrent),0.0) as ms6_bookedadrcurrent,isnull(MAX(ms6_bookedadrpickup),0.0) as ms6_bookedadrpickup
		,isnull(MAX(ms7_bookedadrcurrent),0.0) as ms7_bookedadrcurrent,isnull(MAX(ms7_bookedadrpickup),0.0) as ms7_bookedadrpickup
		,isnull(MAX(ms8_bookedadrcurrent),0.0) as ms8_bookedadrcurrent,isnull(MAX(ms8_bookedadrpickup),0.0) as ms8_bookedadrpickup
		
		,isnull(MAX(ms1_fcstedadrcurrent),0.0) as ms1_fcstedadrcurrent,isnull(MAX(ms1_fcstedadrpickup),0.0) as ms1_fcstedadrpickup
		,isnull(MAX(ms2_fcstedadrcurrent),0.0) as ms2_fcstedadrcurrent,isnull(MAX(ms2_fcstedadrpickup),0.0) as ms2_fcstedadrpickup
		,isnull(MAX(ms3_fcstedadrcurrent),0.0) as ms3_fcstedadrcurrent,isnull(MAX(ms3_fcstedadrpickup),0.0) as ms3_fcstedadrpickup
		,isnull(MAX(ms4_fcstedadrcurrent),0.0) as ms4_fcstedadrcurrent,isnull(MAX(ms4_fcstedadrpickup),0.0) as ms4_fcstedadrpickup
		,isnull(MAX(ms5_fcstedadrcurrent),0.0) as ms5_fcstedadrcurrent,isnull(MAX(ms5_fcstedadrpickup),0.0) as ms5_fcstedadrpickup
		,isnull(MAX(ms6_fcstedadrcurrent),0.0) as ms6_fcstedadrcurrent,isnull(MAX(ms6_fcstedadrpickup),0.0) as ms6_fcstedadrpickup
		,isnull(MAX(ms7_fcstedadrcurrent),0.0) as ms7_fcstedadrcurrent,isnull(MAX(ms7_fcstedadrpickup),0.0) as ms7_fcstedadrpickup
		,isnull(MAX(ms8_fcstedadrcurrent),0.0) as ms8_fcstedadrcurrent,isnull(MAX(ms8_fcstedadrpickup),0.0) as ms8_fcstedadrpickup
		
		--Archana added for group block-, group pickup-Start
		,isnull(MAX(ms1_block),0) as ms1_block,isnull(MAX(ms1_block_available),0) as ms1_block_available,isnull(MAX(ms1_block_pickup),0) as ms1_block_pickup
		,isnull(MAX(ms2_block),0) as ms2_block,isnull(MAX(ms2_block_available),0) as ms2_block_available,isnull(MAX(ms2_block_pickup),0) as ms2_block_pickup
		,isnull(MAX(ms3_block),0) as ms3_block,isnull(MAX(ms3_block_available),0) as ms3_block_available,isnull(MAX(ms3_block_pickup),0) as ms3_block_pickup
		,isnull(MAX(ms4_block),0) as ms4_block,isnull(MAX(ms4_block_available),0) as ms4_block_available,isnull(MAX(ms4_block_pickup),0) as ms4_block_pickup
		,isnull(MAX(ms5_block),0) as ms5_block,isnull(MAX(ms5_block_available),0) as ms5_block_available,isnull(MAX(ms5_block_pickup),0) as ms5_block_pickup
		,isnull(MAX(ms6_block),0) as ms6_block,isnull(MAX(ms6_block_available),0) as ms6_block_available,isnull(MAX(ms6_block_pickup),0) as ms6_block_pickup
		,isnull(MAX(ms7_block),0) as ms7_block,isnull(MAX(ms7_block_available),0) as ms7_block_available,isnull(MAX(ms7_block_pickup),0) as ms7_block_pickup
		,isnull(MAX(ms8_block),0) as ms8_block,isnull(MAX(ms8_block_available),0) as ms8_block_available,isnull(MAX(ms8_block_pickup),0) as ms8_block_pickup
		--Archana added for group block-, group pickup-End
		
		from
		(
			select 
			occupancy_dt,dow
			,(case mkt_seg_id when @ms1 then roomsoldcurrent end) as ms1_roomsoldcurrent
			,(case mkt_seg_id when @ms1 then roomssoldpickup end) as ms1_roomssoldpickup
			,(case mkt_seg_id when @ms2 then roomsoldcurrent end) as ms2_roomsoldcurrent
			,(case mkt_seg_id when @ms2 then roomssoldpickup end) as ms2_roomssoldpickup
			,(case mkt_seg_id when @ms3 then roomsoldcurrent end) as ms3_roomsoldcurrent
			,(case mkt_seg_id when @ms3 then roomssoldpickup end) as ms3_roomssoldpickup
			,(case mkt_seg_id when @ms4 then roomsoldcurrent end) as ms4_roomsoldcurrent
			,(case mkt_seg_id when @ms4 then roomssoldpickup end) as ms4_roomssoldpickup
			,(case mkt_seg_id when @ms5 then roomsoldcurrent end) as ms5_roomsoldcurrent
			,(case mkt_seg_id when @ms5 then roomssoldpickup end) as ms5_roomssoldpickup
			,(case mkt_seg_id when @ms6 then roomsoldcurrent end) as ms6_roomsoldcurrent
			,(case mkt_seg_id when @ms6 then roomssoldpickup end) as ms6_roomssoldpickup
			,(case mkt_seg_id when @ms7 then roomsoldcurrent end) as ms7_roomsoldcurrent
			,(case mkt_seg_id when @ms7 then roomssoldpickup end) as ms7_roomssoldpickup
			,(case mkt_seg_id when @ms8 then roomsoldcurrent end) as ms8_roomsoldcurrent
			,(case mkt_seg_id when @ms8 then roomssoldpickup end) as ms8_roomssoldpickup
		
			,(case mkt_seg_id when @ms1 then occfcstcurrent end) as ms1_occfcstcurrent
			,(case mkt_seg_id when @ms1 then occfcstpickup end) as ms1_occfcstpickup
			,(case mkt_seg_id when @ms2 then occfcstcurrent end) as ms2_occfcstcurrent
			,(case mkt_seg_id when @ms2 then occfcstpickup end) as ms2_occfcstpickup
			,(case mkt_seg_id when @ms3 then occfcstcurrent end) as ms3_occfcstcurrent
			,(case mkt_seg_id when @ms3 then occfcstpickup end) as ms3_occfcstpickup
			,(case mkt_seg_id when @ms4 then occfcstcurrent end) as ms4_occfcstcurrent
			,(case mkt_seg_id when @ms4 then occfcstpickup end) as ms4_occfcstpickup
			,(case mkt_seg_id when @ms5 then occfcstcurrent end) as ms5_occfcstcurrent
			,(case mkt_seg_id when @ms5 then occfcstpickup end) as ms5_occfcstpickup
			,(case mkt_seg_id when @ms6 then occfcstcurrent end) as ms6_occfcstcurrent
			,(case mkt_seg_id when @ms6 then occfcstpickup end) as ms6_occfcstpickup
			,(case mkt_seg_id when @ms7 then occfcstcurrent end) as ms7_occfcstcurrent
			,(case mkt_seg_id when @ms7 then occfcstpickup end) as ms7_occfcstpickup
			,(case mkt_seg_id when @ms8 then occfcstcurrent end) as ms8_occfcstcurrent
			,(case mkt_seg_id when @ms8 then occfcstpickup end) as ms8_occfcstpickup
		
			,(case mkt_seg_id when @ms1 then bookedroomrevenuecurrent end) as ms1_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms1 then bookedroomrevenuepickup end) as ms1_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms2 then bookedroomrevenuecurrent end) as ms2_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms2 then bookedroomrevenuepickup end) as ms2_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms3 then bookedroomrevenuecurrent end) as ms3_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms3 then bookedroomrevenuepickup end) as ms3_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms4 then bookedroomrevenuecurrent end) as ms4_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms4 then bookedroomrevenuepickup end) as ms4_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms5 then bookedroomrevenuecurrent end) as ms5_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms5 then bookedroomrevenuepickup end) as ms5_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then bookedroomrevenuepickup end) as ms6_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then bookedroomrevenuecurrent end) as ms6_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then bookedroomrevenuecurrent end) as ms7_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then bookedroomrevenuepickup end) as ms7_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms8 then bookedroomrevenuecurrent end) as ms8_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms8 then bookedroomrevenuepickup end) as ms8_bookedroomrevenuepickup
		
			,(case mkt_seg_id when @ms1 then fcstedroomrevenuecurrent end) as ms1_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms1 then fcstedroomrevenuepickup end) as ms1_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms2 then fcstedroomrevenuecurrent end) as ms2_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms2 then fcstedroomrevenuepickup end) as ms2_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms3 then fcstedroomrevenuecurrent end) as ms3_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms3 then fcstedroomrevenuepickup end) as ms3_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms4 then fcstedroomrevenuecurrent end) as ms4_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms4 then fcstedroomrevenuepickup end) as ms4_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms5 then fcstedroomrevenuecurrent end) as ms5_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms5 then fcstedroomrevenuepickup end) as ms5_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then fcstedroomrevenuecurrent end) as ms6_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms6 then fcstedroomrevenuepickup end) as ms6_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms7 then fcstedroomrevenuecurrent end) as ms7_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then fcstedroomrevenuepickup end) as ms7_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms8 then fcstedroomrevenuecurrent end) as ms8_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms8 then fcstedroomrevenuepickup end) as ms8_fcstedroomrevenuepickup
		
			,(case mkt_seg_id when @ms1 then bookedadrcurrent end) as ms1_bookedadrcurrent
			,(case mkt_seg_id when @ms1 then bookedadrpickup end) as ms1_bookedadrpickup
			,(case mkt_seg_id when @ms2 then bookedadrcurrent end) as ms2_bookedadrcurrent
			,(case mkt_seg_id when @ms2 then bookedadrpickup end) as ms2_bookedadrpickup
			,(case mkt_seg_id when @ms3 then bookedadrcurrent end) as ms3_bookedadrcurrent
			,(case mkt_seg_id when @ms3 then bookedadrpickup end) as ms3_bookedadrpickup
			,(case mkt_seg_id when @ms4 then bookedadrcurrent end) as ms4_bookedadrcurrent
			,(case mkt_seg_id when @ms4 then bookedadrpickup end) as ms4_bookedadrpickup
			,(case mkt_seg_id when @ms5 then bookedadrcurrent end) as ms5_bookedadrcurrent
			,(case mkt_seg_id when @ms5 then bookedadrpickup end) as ms5_bookedadrpickup
			,(case mkt_seg_id when @ms6 then bookedadrcurrent end) as ms6_bookedadrcurrent
			,(case mkt_seg_id when @ms6 then bookedadrpickup end) as ms6_bookedadrpickup
			,(case mkt_seg_id when @ms7 then bookedadrcurrent end) as ms7_bookedadrcurrent
			,(case mkt_seg_id when @ms7 then bookedadrpickup end) as ms7_bookedadrpickup
			,(case mkt_seg_id when @ms8 then bookedadrcurrent end) as ms8_bookedadrcurrent
			,(case mkt_seg_id when @ms8 then bookedadrpickup end) as ms8_bookedadrpickup
		
			,(case mkt_seg_id when @ms1 then fcstedadrcurrent end) as ms1_fcstedadrcurrent
			,(case mkt_seg_id when @ms1 then fcstedadrpickup end) as ms1_fcstedadrpickup
			,(case mkt_seg_id when @ms2 then fcstedadrcurrent end) as ms2_fcstedadrcurrent
			,(case mkt_seg_id when @ms2 then fcstedadrpickup end) as ms2_fcstedadrpickup
			,(case mkt_seg_id when @ms3 then fcstedadrcurrent end) as ms3_fcstedadrcurrent
			,(case mkt_seg_id when @ms3 then fcstedadrpickup end) as ms3_fcstedadrpickup
			,(case mkt_seg_id when @ms4 then fcstedadrcurrent end) as ms4_fcstedadrcurrent
			,(case mkt_seg_id when @ms4 then fcstedadrpickup end) as ms4_fcstedadrpickup
			,(case mkt_seg_id when @ms5 then fcstedadrcurrent end) as ms5_fcstedadrcurrent
			,(case mkt_seg_id when @ms5 then fcstedadrpickup end) as ms5_fcstedadrpickup
			,(case mkt_seg_id when @ms6 then fcstedadrcurrent end) as ms6_fcstedadrcurrent
			,(case mkt_seg_id when @ms6 then fcstedadrpickup end) as ms6_fcstedadrpickup
			,(case mkt_seg_id when @ms7 then fcstedadrcurrent end) as ms7_fcstedadrcurrent
			,(case mkt_seg_id when @ms7 then fcstedadrpickup end) as ms7_fcstedadrpickup
			,(case mkt_seg_id when @ms8 then fcstedadrcurrent end) as ms8_fcstedadrcurrent
			,(case mkt_seg_id when @ms8 then fcstedadrpickup end) as ms8_fcstedadrpickup
			
			--Archana added for group block and group pickup-Start
			,(case mkt_seg_id when @ms1 then block end) as ms1_block,(case mkt_seg_id when @ms1 then block_pickup end) as ms1_block_pickup,(case mkt_seg_id when @ms1 then block_available end) as ms1_block_available
			,(case mkt_seg_id when @ms2 then block end) as ms2_block,(case mkt_seg_id when @ms2 then block_pickup end) as ms2_block_pickup,(case mkt_seg_id when @ms2 then block_available end) as ms2_block_available
			,(case mkt_seg_id when @ms3 then block end) as ms3_block,(case mkt_seg_id when @ms3 then block_pickup end) as ms3_block_pickup,(case mkt_seg_id when @ms3 then block_available end) as ms3_block_available
			,(case mkt_seg_id when @ms4 then block end) as ms4_block,(case mkt_seg_id when @ms4 then block_pickup end) as ms4_block_pickup,(case mkt_seg_id when @ms4 then block_available end) as ms4_block_available
			,(case mkt_seg_id when @ms5 then block end) as ms5_block,(case mkt_seg_id when @ms5 then block_pickup end) as ms5_block_pickup,(case mkt_seg_id when @ms5 then block_available end) as ms5_block_available
			,(case mkt_seg_id when @ms6 then block end) as ms6_block,(case mkt_seg_id when @ms6 then block_pickup end) as ms6_block_pickup,(case mkt_seg_id when @ms6 then block_available end) as ms6_block_available
			,(case mkt_seg_id when @ms7 then block end) as ms7_block,(case mkt_seg_id when @ms7 then block_pickup end) as ms7_block_pickup,(case mkt_seg_id when @ms7 then block_available end) as ms7_block_available
			,(case mkt_seg_id when @ms8 then block end) as ms8_block,(case mkt_seg_id when @ms8 then block_pickup end) as ms8_block_pickup,(case mkt_seg_id when @ms8 then block_available end) as ms8_block_available
			--Archana added for group block and group pickup-End
		
			from
			(
				select
					base.occupancy_dt,
					base.mkt_seg_id,
					datename(dw,base.occupancy_dt) as dow,
					isnull(a.rooms_sold,0.0) as roomsoldcurrent,
					isnull(a.room_revenue,0.0) as bookedroomrevenuecurrent,
					isnull(a.adr,0.0) as bookedadrcurrent,
					isnull(b.occupancy_forecast_current,0.0) as occfcstcurrent,
					isnull(b.fcsted_room_revenue_current,0.0) as fcstedroomrevenuecurrent,
					isnull(b.estimated_adr_current,0.0) as fcstedadrcurrent,

				    case when a.occupancy_dt <= @business_end_dt then
                        cast(isnull(c.end_date_rooms_sold,isnull(a.rooms_sold,0.0)) - ISNULL(c.start_date_rooms_sold, isnull(a.rooms_sold,0.0)) as numeric (19,2))
				        else
				        cast(isnull(c.end_date_rooms_sold,0.0) - ISNULL(c.start_date_rooms_sold, 0.0) as numeric (19,2))
				    end as roomssoldpickup,

				    case when a.occupancy_dt <= @business_end_dt then
					    cast(isnull(d.end_date_occupancy_nbr,isnull(b.occupancy_forecast_current,0.0)) - ISNULL(d.start_date_occupancy_nbr, isnull(b.occupancy_forecast_current,0.0)) as numeric (19,2))
                        else
					    cast(isnull(d.end_date_occupancy_nbr,0.0) - ISNULL(d.start_date_occupancy_nbr, 0.0) as numeric (19,2))
					end as occfcstpickup,

				    case when a.occupancy_dt <= @business_end_dt then
                        cast(isnull(c.end_date_room_revenue,isnull(a.room_revenue,0.0)) - ISNULL(c.start_date_room_revenue, isnull(a.room_revenue,0.0)) as numeric (19,2))
                        else
					    cast(isnull(c.end_date_room_revenue,0.0) - ISNULL(c.start_date_room_revenue, 0.0) as numeric (19,2))
					end as bookedroomrevenuepickup,

				    case when a.occupancy_dt <= @business_end_dt then
                        cast(isnull(d.end_date_revenue,isnull(b.fcsted_room_revenue_current,0.0)) - ISNULL(d.start_date_revenue, isnull(b.fcsted_room_revenue_current,0.0)) as numeric (19,2))
                        else
					    cast(isnull(d.end_date_revenue,0.0) - ISNULL(d.start_date_revenue, 0.0) as numeric (19,2))
					end as fcstedroomrevenuepickup,

				    case when a.occupancy_dt <= @business_end_dt then
					    cast(isnull(c.end_date_adr,isnull(a.adr,0.0)) - ISNULL(c.start_date_adr, isnull(a.adr,0.0)) as numeric (19,2))
                        else
					    cast(isnull(c.end_date_adr,0.0) - ISNULL(c.start_date_adr, 0.0) as numeric (19,2))
					end as bookedadrpickup,

				    case when a.occupancy_dt <= @business_end_dt then
                        cast(isnull(d.end_date_adr,isnull(b.estimated_adr_current,0.0)) - ISNULL(d.start_date_adr, isnull(b.estimated_adr_current,0.0)) as numeric (19,2))
                        else
					    cast(isnull(d.end_date_adr,0.0) - ISNULL(d.start_date_adr, 0.0) as numeric (19,2))
					end as fcstedadrpickup,

					groupBlock.block,
					groupBlock.block_pickup,
					groupBlock.block_available
				from
				(
					select @property_id property_id,CAST(calendar_date as date) Occupancy_DT,mkt_seg_id  from calendar_dim left join @tempMS on mkt_seg_id is not null
					 where calendar_date between @start_date and @end_date
				)base left join
				(
					select * from ufn_get_activity_by_individual_ms (@property_id,@mkt_seg_id,@start_date,@end_date)
				) as a on base.property_id=a.property_id and base.occupancy_dt=a.occupancy_dt and base.mkt_seg_id=a.mkt_seg_id
				left join
				(
					select * from dbo.ufn_get_occupancy_forecast_by_individual_ms (@property_id,@mkt_seg_id,@start_date,@end_date)
				) as b on base.property_id=b.property_id and base.occupancy_dt=b.occupancy_dt and base.mkt_seg_id=b.mkt_seg_id
				left join
				(
					select 
						q2.property_id, q2.occupancy_dt, q2.mkt_seg_id, q1.rooms_sold as end_date_rooms_sold, q2.rooms_sold as start_date_rooms_sold,
						q1.room_revenue as end_date_room_revenue, q2.room_revenue as start_date_room_revenue, q1.adr as end_date_adr, q2.adr as start_date_adr
					from
					(
						select 
							occupancy_dt,
							property_id,
							mkt_seg_id,
							rooms_sold, 
							room_revenue,
							adr 
						from ufn_get_activity_asof_businessdate_by_individual_ms (@property_id,@mkt_seg_id,@business_end_dt,@start_date,@end_date)	
		  			) q1
		  			right outer join
		  			(
						select 
							occupancy_dt,
							property_id,
							mkt_seg_id,
							rooms_sold, 
							room_revenue,
							adr 
						from ufn_get_activity_asof_businessdate_by_individual_ms (@property_id,@mkt_seg_id,@business_start_dt,@start_date,@end_date)
		  			) q2 on q1.property_id=q2.property_id and q1.occupancy_dt=q2.occupancy_dt and q1.mkt_seg_id=q2.mkt_seg_id
				) as c on base.property_id=c.property_id and base.occupancy_dt=c.occupancy_dt and base.mkt_seg_id=c.mkt_seg_id
				left join
				(
					select 
						q2.property_id, q2.occupancy_dt, q2.mkt_seg_id,q1.occupancy_nbr as end_date_occupancy_nbr, q2.occupancy_nbr as start_date_occupancy_nbr,
						q1.revenue as end_date_revenue, q2.revenue as start_date_revenue, q1.adr as end_date_adr, q2.adr as start_date_adr
					from
					(
						select * from ufn_get_occupancy_forecast_asof_businessdate_by_individual_ms (@property_id,@mkt_seg_id,@business_end_dt,@start_date,@end_date)
					) q1
					right outer join
					(
						select * from ufn_get_occupancy_forecast_asof_businessdate_by_individual_ms (@property_id,@mkt_seg_id,@business_start_dt,@start_date,@end_date)
				
					) q2 on q1.property_id=q2.property_id and q1.occupancy_dt=q2.occupancy_dt and q1.mkt_seg_id=q2.mkt_seg_id
				) as d on base.property_id=d.property_id and base.occupancy_dt=d.occupancy_dt and base.mkt_seg_id=d.mkt_seg_id
				--Archana added group block-Aggregated-START
				left join
				(
					select * from ufn_get_groupBlock_groupPickup_by_MarketSegment (@property_id,@mkt_seg_id,@start_date, @end_date,0, @MktSegStatus)
				) as groupBlock on
				a.property_id=groupBlock.property_id and a.occupancy_dt=groupBlock.occupancy_dt and a.mkt_seg_id=groupBlock.marketSegment_id 
				--Archana added group block-Aggregated-END
			) data
		)data2	
		group by occupancy_dt,dow
		order by occupancy_dt
	return
end