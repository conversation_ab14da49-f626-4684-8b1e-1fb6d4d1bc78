DROP PROCEDURE IF EXISTS [dbo].[usp_get_profit_metrics_at_bt_details]
    GO
    SET ANSI_NULLS ON
    GO
    SET QUOTED_IDENTIFIER ON
    GO
CREATE procedure [dbo].[usp_get_profit_metrics_at_bt_details]
(
    @property_id INT,
    @start_date DATE,
    @end_date DATE,
    @ms_status_ids varchar(16),
    @comp_rooms_filter INT
)

as
begin

declare
@caughtUpDate date
    set @caughtUpDate = (select dbo.ufn_get_caughtup_date_by_property(@property_id,3,13))

declare
@exclude_comp_room varchar(5) = '0,1'
if(@comp_rooms_filter = 1)
    set @exclude_comp_room = '0'

declare
@temp_mkt_seg table
(
Property_ID int,
Mkt_Seg_ID int,
Mkt_Seg_Name nvarchar(100)
)
insert into @temp_mkt_seg
select Property_ID, Mkt_Seg_ID, Mkt_Seg_Name
from Mkt_Seg
where Property_ID = @property_id
  and Status_ID in (select items from Split(@ms_status_ids, ','))
  and Exclude_CompHouse_Data_Display in (select value from varcharToInt(@exclude_comp_room, ','))

Select combined.Business_Type_ID, sum(combined.Profit_On_Books) as Profit_On_Books, sum(combined.Profit_Forcast) as Profit_Forcast
FROM (Select pastData.Business_Type_ID,
             SUM(pastData.Total_Profit) as Profit_On_Books,
             sum(pastData.past_profit)  as Profit_Forcast
      from (select msd.Business_Type_ID,
                   maa.total_profit,
                   case
                       when maa.Occupancy_DT >= @start_date
                           and maa.Occupancy_DT < @caughtUpDate then maa.Total_Profit
                       else 0 end as past_profit
            FROM Mkt_Accom_Activity maa
                     INNER JOIN Mkt_Seg_Forecast_Group msfg ON maa.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID = 1
                     INNER JOIN Mkt_Seg_Details msd on msd.Mkt_Seg_ID = maa.Mkt_Seg_ID
                     INNER JOIN @temp_mkt_seg ms on ms.mkt_seg_id = maa.mkt_seg_id
            WHERE maa.Occupancy_DT between @start_date and @end_date
              and maa.Property_ID = @property_id and maa.total_profit!=0) pastData
      Group By pastData.Business_Type_ID
      union all
      Select msd.Business_Type_ID, 0 as Profit_On_books, sum(occ.Profit) as Profit_Forcast
      from Occupancy_FCST AS occ
               INNER JOIN Mkt_Seg_Forecast_Group msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID = 1
               INNER JOIN Mkt_Seg_Details msd on msd.Mkt_Seg_ID = occ.Mkt_Seg_ID
               INNER JOIN @temp_mkt_seg ms on ms.mkt_seg_id = occ.mkt_seg_id
      where occ.Occupancy_DT between @start_date and @end_date and occ.occupancy_dt >= @caughtUpDate and occ.profit is not null and occ.profit != 0
      Group By msd.Business_Type_ID) combined
group by combined.Business_Type_ID
end