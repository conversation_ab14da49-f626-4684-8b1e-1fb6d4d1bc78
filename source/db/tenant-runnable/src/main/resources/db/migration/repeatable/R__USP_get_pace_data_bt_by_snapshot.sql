DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_data_bt_by_snapshot]
GO
/****** Object:  Stored Procedure [dbo].[usp_get_pace_data_bt_by_snapshot]    Script Date: 1/14/2021 2:40:52 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Stored Procedure Name: usp_get_pace_data_bt_by_snapshot

Input Parameters : 
	@property_id --> property Id Associated with a property (e.g.,'BOSCO' id FROM the property table is 12)
	@start_date --> start date FROM which you need pace ('2011-07-01')
	@end_date --> end date till which you need pace ('2011-07-31')
	@past_start_date --> maximum past date till which we want pace FROM 
	@business_type_id --> business Type for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)

Ouput Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_data_bt_by_snapshot 10027,'2012-09-20', '2012-10-30','2012-07-17','1,2'

Purpose: The purpose of this stored procedure is to extract pace of forecast and occupancy for a given property and given date range. 

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/14/2021		Anil				Borgude					Initial Version
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/

CREATE PROCEDURE [dbo].[usp_get_pace_data_bt_by_snapshot]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@business_type_ids nvarchar(500),
    @exclude_comp_rooms varchar(5)
)		

AS
BEGIN
	SET NOCOUNT ON
/** Getting Rooms Sold count from the business type activity procedure **/
	CREATE table #pace_activity_bt (

		Business_Day_End_DT date,
		business_type_id int,
		Business_Type_Name varchar(10),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2),
        onBooks_ADR numeric(19,5)
	)
	insert into #pace_activity_bt EXECUTE dbo.usp_get_pace_activity_bt_by_snapshot @property_id, @start_date, @end_date, @past_start_date, @business_type_ids, @exclude_comp_rooms

/** Getting Occupancy NBR from the business type occupancy forecast procedure **/
	CREATE table #pace_occupancy_fcst_bt(
		Business_Day_End_DT date,
		business_type_id int,
		Business_Type_Name varchar(10),
		Occupancy_NBR numeric(8,2)
	)

	insert into #pace_occupancy_fcst_bt EXECUTE dbo.usp_get_pace_occupancy_fcst_bt_by_snapshot @property_id, @start_date, @end_date, @past_start_date, @business_type_ids, @exclude_comp_rooms

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	SELECT activity.Business_Day_End_DT AS Business_Day_End_DT, activity.business_type_id AS Business_Type_ID,
	 activity.Business_Type_Name AS Business_Type_Name, activity.Rooms_Sold AS Rooms_Sold, CAST(activity.Room_Revenue as numeric(19, 2)) AS Room_Revenue,
           CASE             
               WHEN (activity.Rooms_Sold IS NULL OR activity.Rooms_Sold = 0) THEN NULL
               WHEN (activity.Room_Revenue IS NULL) THEN NULL
               ELSE CAST(activity.Room_Revenue / activity.Rooms_Sold as numeric(19, 2)) END AS onBooks_ADR,
	 fcst.Occupancy_NBR AS Occupancy_NBR
	 FROM
		#pace_activity_bt activity
	LEFT JOIN
		#pace_occupancy_fcst_bt fcst
	ON activity.Business_Day_End_DT=fcst.Business_Day_End_DT
	AND activity.business_type_id=fcst.business_type_id	      
            ORDER BY activity.Business_Day_End_DT, activity.business_type_id, activity.Business_Type_Name
			DROP TABLE #pace_activity_bt
			DROP TABLE #pace_occupancy_fcst_bt
END
GO