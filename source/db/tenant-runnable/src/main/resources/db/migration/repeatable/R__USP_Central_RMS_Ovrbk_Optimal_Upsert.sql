DROP PROCEDURE IF EXISTS [dbo].[usp_Central_RMS_Overbooking_Optimal_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Central_RMS_Overbooking_Optimal_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Central_RMS_Overbooking_Optimal_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Central_RMS_Overbooking_Optimal_Batch]

CREATE TYPE dbo.Central_RMS_Overbooking_Optimal_Batch AS TABLE (
    [Property_ID]              int,
    [Accom_Type_ID]			   int,
    [Occupancy_DT]             date,
    [Optimal_Value]            int,
    [Revenue]                  numeric(19, 5),
    [Is_Property_Level]		   int
);
GO

CREATE PROCEDURE usp_Central_RMS_Overbooking_Optimal_Upsert @Central_RMS_Overbooking_Optimal_Batch Central_RMS_Overbooking_Optimal_Batch READONLY
AS
BEGIN
MERGE INTO [dbo].[Central_RMS_Ovrbk_Optimal] AS Target
    USING @Central_RMS_Overbooking_Optimal_Batch AS Source
    ON (
    Target.[Property_ID]			= Source.[Property_ID]
    AND Target.[Accom_Type_ID]		= Source.[Accom_Type_ID]
    AND Target.[Occupancy_DT]		= Source.[Occupancy_DT])
    WHEN MATCHED THEN
UPDATE SET
    Target.[Optimal_Value]          = Source.[Optimal_Value],
    Target.[Revenue]				= Source.[Revenue],
    Target.[Is_Property_Level]      = Source.[Is_Property_Level]
    WHEN NOT MATCHED THEN
INSERT ([Property_ID], [Accom_Type_ID], [Occupancy_DT], [Optimal_Value], [Revenue], [Is_Property_Level])
VALUES (Source.[Property_ID], Source.[Accom_Type_ID], Source.[Occupancy_DT], Source.[Optimal_Value], Source.[Revenue], Source.[Is_Property_Level]);
END
GO

CREATE PROCEDURE usp_Central_RMS_Overbooking_Optimal_Update @Central_RMS_Overbooking_Optimal_Batch Central_RMS_Overbooking_Optimal_Batch READONLY
AS
BEGIN
UPDATE [dbo].[Central_RMS_Ovrbk_Optimal] SET
    [Optimal_Value]          = Source.[Optimal_Value],
    [Revenue]				= Source.[Revenue],
    [Is_Property_Level]      = Source.[Is_Property_Level]
FROM @Central_RMS_Overbooking_Optimal_Batch AS Source
WHERE [Central_RMS_Ovrbk_Optimal].[Property_ID]			= Source.[Property_ID]
  AND [Central_RMS_Ovrbk_Optimal].[Accom_Type_ID]		= Source.[Accom_Type_ID]
  AND [Central_RMS_Ovrbk_Optimal].[Occupancy_DT]		= Source.[Occupancy_DT];
END
GO

CREATE PROCEDURE usp_Central_RMS_Overbooking_Optimal_Insert @Central_RMS_Overbooking_Optimal_Batch Central_RMS_Overbooking_Optimal_Batch READONLY
AS
BEGIN
INSERT INTO [dbo].[Central_RMS_Ovrbk_Optimal] (Property_ID, Accom_Type_ID, Occupancy_DT, Optimal_Value, Revenue, Is_Property_Level)
SELECT Property_ID, Accom_Type_ID, Occupancy_DT, Optimal_Value, Revenue, Is_Property_Level
FROM @Central_RMS_Overbooking_Optimal_Batch Central_RMS_Overbooking_Optimal_Batch;
END
GO