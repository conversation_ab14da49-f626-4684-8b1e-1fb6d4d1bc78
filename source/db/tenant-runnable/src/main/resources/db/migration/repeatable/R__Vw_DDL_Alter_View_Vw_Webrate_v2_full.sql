DROP VIEW IF EXISTS [dbo].[vw_webrate_v2_full]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[vw_webrate_v2_full]
AS
-- We are moving away from using function "ufn_add_room_tax_rate" for performance purpose.
-- Before making changes in this view Kindly track if the topTaxRate query is being updated in the original function

    SELECT
    rate.Webrate_ID,
    rate.Webrate_Source_Property_ID,
    rate.Webrate_GenerationDate,
    rate.Webrate_Competitors_ID,
    rate.Webrate_Channel_ID,
    rate.Webrate_Accom_Type_ID,
    rate.Occupancy_DT,
    rate.LOS,
    rate.Webrate_Remark,
    rate.Webrate_Status,
    rate.Webrate_Currency,
    cast((rate.Webrate_RateValue * (1 + TTV.TOPTAXVALUE)) as numeric(19,2)) as Webrate_RateValue,
    cast(rate.Webrate_RateValue_Display AS numeric(19,2)) as Original_Webrate_RateValue,
    rate.Webrate_RateValue as Webrate_RateValue_WOTax,
    rate.Webrate_Page_Number,
    rate.Webrate_Rank,
    rate.Webrate_Rating,
    rate.CreateDate,
    rate.Webrate_RateValue_Max,
    competitors.Webrate_Competitors_Name ,
    competitors.Webrate_Competitors_Alias,
    wacm.Accom_Class_ID,
    wcc.Product_ID,
    wcc.Demand_Enabled,
    wcc.Ranking_Enabled,
    wcc.DTA,
    ISNULL(wtp.Product_ID, -1) AS Rate_Product_ID
FROM
    Webrate rate WITH (NOLOCK)
        LEFT JOIN Rate_Shopping_Adjustment rsa WITH (NOLOCK)
on rsa.Webrate_Competitors_ID = rate.Webrate_Competitors_ID
    INNER JOIN Webrate_Competitors competitors WITH (NOLOCK)
on competitors.Webrate_Competitors_ID = rate.Webrate_Competitors_ID
    INNER JOIN Webrate_Channel channel WITH (NOLOCK)
ON channel.Webrate_Channel_ID = rate.Webrate_Channel_ID
    INNER JOIN Webrate_Accom_Class_Mapping wacm WITH (NOLOCK)
ON wacm.Webrate_Accom_Type_ID = rate.Webrate_Accom_Type_ID
    INNER JOIN Webrate_Competitors_Class wcc WITH (NOLOCK)
ON wcc.Webrate_Competitors_ID = competitors.Webrate_Competitors_ID
    and wcc.Accom_Class_ID = wacm.Accom_Class_ID
    INNER JOIN Product product WITH (NOLOCK)
on product.Product_ID = wcc.Product_ID
    LEFT JOIN Webrate_Type_Product wtp WITH (NOLOCK)
ON wtp.Webrate_Type_ID = rate.Webrate_Type_ID
    AND wtp.LOS = rate.LOS
    CROSS JOIN (
    select top 1 Room_Tax_Rate / 100 AS TOPTAXVALUE from Tax WITH (NOLOCK) where Start_Date is null and End_Date is null
    ) as TTV
WHERE competitors.Status_ID IN (1,3)
  and channel.Status_ID IN (1,3)
  and product.Status_ID = 1
  and wcc.Product_ID = wtp.Product_ID
  and ((product.Rate_Shopping_LOS_Min = -1  and product.Rate_Shopping_LOS_Max = -1)
   or (product.Rate_Shopping_LOS_Min > -1  and rate.LOS >= product.Rate_Shopping_LOS_Min
  and product.Rate_Shopping_LOS_Max > -1  and rate.LOS <= product.Rate_Shopping_LOS_Max))

GO