DROP PROCEDURE IF EXISTS [dbo].[usp_get_pickup_report_comparative_view_fg]
    GO

CREATE PROCEDURE [dbo].[usp_get_pickup_report_comparative_view_fg]
(
    @property_id int,
    @forecast_group_id varchar(500),
    @business_start_dt date,
    @business_end_dt date,
    @start_date date,
    @end_date date,
    @isRollingDate int,
    @rolling_business_start_dt nvarchar(50),
    @rolling_business_end_dt nvarchar(50),
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50)
)
as
begin
    declare @caughtupdate date
    declare @occupancy_dt date
    declare @dow varchar(10)
    declare @fg1_roomsoldcurrent int
    declare @fg1_roomssoldpickup int
    declare @fg2_roomsoldcurrent int
    declare @fg2_roomssoldpickup int
    declare @fg3_roomsoldcurrent int
    declare @fg3_roomssoldpickup int
    declare @fg4_roomsoldcurrent int
    declare @fg4_roomssoldpickup int
    declare @fg5_roomsoldcurrent int
    declare @fg5_roomssoldpickup int
    declare @fg6_roomsoldcurrent int
    declare @fg6_roomssoldpickup int
    declare @fg7_roomsoldcurrent int
    declare @fg7_roomssoldpickup int
    declare @fg8_roomsoldcurrent int
    declare @fg8_roomssoldpickup int
    declare @fg9_roomsoldcurrent int
    declare @fg9_roomssoldpickup int
    declare @fg10_roomsoldcurrent int
    declare @fg10_roomssoldpickup int
    declare @fg11_roomsoldcurrent int
    declare @fg11_roomssoldpickup int
    declare @fg12_roomsoldcurrent int
    declare @fg12_roomssoldpickup int
    declare @fg13_roomsoldcurrent int
    declare @fg13_roomssoldpickup int
    declare @fg14_roomsoldcurrent int
    declare @fg14_roomssoldpickup int
    declare @fg15_roomsoldcurrent int
    declare @fg15_roomssoldpickup int
    declare @fg16_roomsoldcurrent int
    declare @fg16_roomssoldpickup int
    declare @fg17_roomsoldcurrent int
    declare @fg17_roomssoldpickup int
    declare @fg18_roomsoldcurrent int
    declare @fg18_roomssoldpickup int
    declare @fg19_roomsoldcurrent int
    declare @fg19_roomssoldpickup int
    declare @fg20_roomsoldcurrent int
    declare @fg20_roomssoldpickup int
    declare @fg21_roomsoldcurrent int
    declare @fg21_roomssoldpickup int
    declare @fg22_roomsoldcurrent int
    declare @fg22_roomssoldpickup int
    declare @fg23_roomsoldcurrent int
    declare @fg23_roomssoldpickup int
    declare @fg24_roomsoldcurrent int
    declare @fg24_roomssoldpickup int
    declare @fg25_roomsoldcurrent int
    declare @fg25_roomssoldpickup int
    declare @fg26_roomsoldcurrent int
    declare @fg26_roomssoldpickup int
    declare @fg27_roomsoldcurrent int
    declare @fg27_roomssoldpickup int
    declare @fg28_roomsoldcurrent int
    declare @fg28_roomssoldpickup int
    declare @fg29_roomsoldcurrent int
    declare @fg29_roomssoldpickup int
    declare @fg30_roomsoldcurrent int
    declare @fg30_roomssoldpickup int
    declare @fg31_roomsoldcurrent int
    declare @fg31_roomssoldpickup int
    declare @fg32_roomsoldcurrent int
    declare @fg32_roomssoldpickup int
    declare @fg33_roomsoldcurrent int
    declare @fg33_roomssoldpickup int
    declare @fg34_roomsoldcurrent int
    declare @fg34_roomssoldpickup int
    declare @fg35_roomsoldcurrent int
    declare @fg35_roomssoldpickup int
    declare @fg36_roomsoldcurrent int
    declare @fg36_roomssoldpickup int
    declare @fg37_roomsoldcurrent int
    declare @fg37_roomssoldpickup int
    declare @fg38_roomsoldcurrent int
    declare @fg38_roomssoldpickup int
    declare @fg39_roomsoldcurrent int
    declare @fg39_roomssoldpickup int
    declare @fg40_roomsoldcurrent int
    declare @fg40_roomssoldpickup int
    declare @fg41_roomsoldcurrent int
    declare @fg41_roomssoldpickup int
    declare @fg42_roomsoldcurrent int
    declare @fg42_roomssoldpickup int
    declare @fg43_roomsoldcurrent int
    declare @fg43_roomssoldpickup int
    declare @fg44_roomsoldcurrent int
    declare @fg44_roomssoldpickup int
    declare @fg45_roomsoldcurrent int
    declare @fg45_roomssoldpickup int
    declare @fg46_roomsoldcurrent int
    declare @fg46_roomssoldpickup int
    declare @fg47_roomsoldcurrent int
    declare @fg47_roomssoldpickup int
    declare @fg48_roomsoldcurrent int
    declare @fg48_roomssoldpickup int
    declare @fg49_roomsoldcurrent int
    declare @fg49_roomssoldpickup int
    declare @fg50_roomsoldcurrent int
    declare @fg50_roomssoldpickup int
    declare @fg1_occfcstcurrent numeric(8, 2)
    declare @fg1_occfcstpickup numeric(8, 2)
    declare @fg2_occfcstcurrent numeric(8, 2)
    declare @fg2_occfcstpickup numeric(8, 2)
    declare @fg3_occfcstcurrent numeric(8, 2)
    declare @fg3_occfcstpickup numeric(8, 2)
    declare @fg4_occfcstcurrent numeric(8, 2)
    declare @fg4_occfcstpickup numeric(8, 2)
    declare @fg5_occfcstcurrent numeric(8, 2)
    declare @fg5_occfcstpickup numeric(8, 2)
    declare @fg6_occfcstcurrent numeric(8, 2)
    declare @fg6_occfcstpickup numeric(8, 2)
    declare @fg7_occfcstcurrent numeric(8, 2)
    declare @fg7_occfcstpickup numeric(8, 2)
    declare @fg8_occfcstcurrent numeric(8, 2)
    declare @fg8_occfcstpickup numeric(8, 2)
    declare @fg9_occfcstcurrent numeric(8, 2)
    declare @fg9_occfcstpickup numeric(8, 2)
    declare @fg10_occfcstcurrent numeric(8, 2)
    declare @fg10_occfcstpickup numeric(8, 2)
    declare @fg11_occfcstcurrent numeric(8, 2)
    declare @fg11_occfcstpickup numeric(8, 2)
    declare @fg12_occfcstcurrent numeric(8, 2)
    declare @fg12_occfcstpickup numeric(8, 2)
    declare @fg13_occfcstcurrent numeric(8, 2)
    declare @fg13_occfcstpickup numeric(8, 2)
    declare @fg14_occfcstcurrent numeric(8, 2)
    declare @fg14_occfcstpickup numeric(8, 2)
    declare @fg15_occfcstcurrent numeric(8, 2)
    declare @fg15_occfcstpickup numeric(8, 2)
    declare @fg16_occfcstcurrent numeric(8, 2)
    declare @fg16_occfcstpickup numeric(8, 2)
    declare @fg17_occfcstcurrent numeric(8, 2)
    declare @fg17_occfcstpickup numeric(8, 2)
    declare @fg18_occfcstcurrent numeric(8, 2)
    declare @fg18_occfcstpickup numeric(8, 2)
    declare @fg19_occfcstcurrent numeric(8, 2)
    declare @fg19_occfcstpickup numeric(8, 2)
    declare @fg20_occfcstcurrent numeric(8, 2)
    declare @fg20_occfcstpickup numeric(8, 2)
    declare @fg21_occfcstcurrent numeric(8, 2)
    declare @fg21_occfcstpickup numeric(8, 2)
    declare @fg22_occfcstcurrent numeric(8, 2)
    declare @fg22_occfcstpickup numeric(8, 2)
    declare @fg23_occfcstcurrent numeric(8, 2)
    declare @fg23_occfcstpickup numeric(8, 2)
    declare @fg24_occfcstcurrent numeric(8, 2)
    declare @fg24_occfcstpickup numeric(8, 2)
    declare @fg25_occfcstcurrent numeric(8, 2)
    declare @fg25_occfcstpickup numeric(8, 2)
    declare @fg26_occfcstcurrent numeric(8, 2)
    declare @fg26_occfcstpickup numeric(8, 2)
    declare @fg27_occfcstcurrent numeric(8, 2)
    declare @fg27_occfcstpickup numeric(8, 2)
    declare @fg28_occfcstcurrent numeric(8, 2)
    declare @fg28_occfcstpickup numeric(8, 2)
    declare @fg29_occfcstcurrent numeric(8, 2)
    declare @fg29_occfcstpickup numeric(8, 2)
    declare @fg30_occfcstcurrent numeric(8, 2)
    declare @fg30_occfcstpickup numeric(8, 2)
    declare @fg31_occfcstcurrent numeric(8, 2)
    declare @fg31_occfcstpickup numeric(8, 2)
    declare @fg32_occfcstcurrent numeric(8, 2)
    declare @fg32_occfcstpickup numeric(8, 2)
    declare @fg33_occfcstcurrent numeric(8, 2)
    declare @fg33_occfcstpickup numeric(8, 2)
    declare @fg34_occfcstcurrent numeric(8, 2)
    declare @fg34_occfcstpickup numeric(8, 2)
    declare @fg35_occfcstcurrent numeric(8, 2)
    declare @fg35_occfcstpickup numeric(8, 2)
    declare @fg36_occfcstcurrent numeric(8, 2)
    declare @fg36_occfcstpickup numeric(8, 2)
    declare @fg37_occfcstcurrent numeric(8, 2)
    declare @fg37_occfcstpickup numeric(8, 2)
    declare @fg38_occfcstcurrent numeric(8, 2)
    declare @fg38_occfcstpickup numeric(8, 2)
    declare @fg39_occfcstcurrent numeric(8, 2)
    declare @fg39_occfcstpickup numeric(8, 2)
    declare @fg40_occfcstcurrent numeric(8, 2)
    declare @fg40_occfcstpickup numeric(8, 2)
    declare @fg41_occfcstcurrent numeric(8, 2)
    declare @fg41_occfcstpickup numeric(8, 2)
    declare @fg42_occfcstcurrent numeric(8, 2)
    declare @fg42_occfcstpickup numeric(8, 2)
    declare @fg43_occfcstcurrent numeric(8, 2)
    declare @fg43_occfcstpickup numeric(8, 2)
    declare @fg44_occfcstcurrent numeric(8, 2)
    declare @fg44_occfcstpickup numeric(8, 2)
    declare @fg45_occfcstcurrent numeric(8, 2)
    declare @fg45_occfcstpickup numeric(8, 2)
    declare @fg46_occfcstcurrent numeric(8, 2)
    declare @fg46_occfcstpickup numeric(8, 2)
    declare @fg47_occfcstcurrent numeric(8, 2)
    declare @fg47_occfcstpickup numeric(8, 2)
    declare @fg48_occfcstcurrent numeric(8, 2)
    declare @fg48_occfcstpickup numeric(8, 2)
    declare @fg49_occfcstcurrent numeric(8, 2)
    declare @fg49_occfcstpickup numeric(8, 2)
    declare @fg50_occfcstcurrent numeric(8, 2)
    declare @fg50_occfcstpickup numeric(8, 2)
    declare @fg1_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg1_bookedroomrevenuepickup numeric(19, 5)
    declare @fg2_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg2_bookedroomrevenuepickup numeric(19, 5)
    declare @fg3_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg3_bookedroomrevenuepickup numeric(19, 5)
    declare @fg4_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg4_bookedroomrevenuepickup numeric(19, 5)
    declare @fg5_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg5_bookedroomrevenuepickup numeric(19, 5)
    declare @fg6_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg6_bookedroomrevenuepickup numeric(19, 5)
    declare @fg7_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg7_bookedroomrevenuepickup numeric(19, 5)
    declare @fg8_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg8_bookedroomrevenuepickup numeric(19, 5)
    declare @fg9_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg9_bookedroomrevenuepickup numeric(19, 5)
    declare @fg10_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg10_bookedroomrevenuepickup numeric(19, 5)
    declare @fg11_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg11_bookedroomrevenuepickup numeric(19, 5)
    declare @fg12_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg12_bookedroomrevenuepickup numeric(19, 5)
    declare @fg13_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg13_bookedroomrevenuepickup numeric(19, 5)
    declare @fg14_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg14_bookedroomrevenuepickup numeric(19, 5)
    declare @fg15_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg15_bookedroomrevenuepickup numeric(19, 5)
    declare @fg16_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg16_bookedroomrevenuepickup numeric(19, 5)
    declare @fg17_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg17_bookedroomrevenuepickup numeric(19, 5)
    declare @fg18_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg18_bookedroomrevenuepickup numeric(19, 5)
    declare @fg19_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg19_bookedroomrevenuepickup numeric(19, 5)
    declare @fg20_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg20_bookedroomrevenuepickup numeric(19, 5)
    declare @fg21_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg21_bookedroomrevenuepickup numeric(19, 5)
    declare @fg22_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg22_bookedroomrevenuepickup numeric(19, 5)
    declare @fg23_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg23_bookedroomrevenuepickup numeric(19, 5)
    declare @fg24_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg24_bookedroomrevenuepickup numeric(19, 5)
    declare @fg25_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg25_bookedroomrevenuepickup numeric(19, 5)
    declare @fg26_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg26_bookedroomrevenuepickup numeric(19, 5)
    declare @fg27_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg27_bookedroomrevenuepickup numeric(19, 5)
    declare @fg28_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg28_bookedroomrevenuepickup numeric(19, 5)
    declare @fg29_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg29_bookedroomrevenuepickup numeric(19, 5)
    declare @fg30_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg30_bookedroomrevenuepickup numeric(19, 5)
    declare @fg31_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg31_bookedroomrevenuepickup numeric(19, 5)
    declare @fg32_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg32_bookedroomrevenuepickup numeric(19, 5)
    declare @fg33_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg33_bookedroomrevenuepickup numeric(19, 5)
    declare @fg34_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg34_bookedroomrevenuepickup numeric(19, 5)
    declare @fg35_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg35_bookedroomrevenuepickup numeric(19, 5)
    declare @fg36_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg36_bookedroomrevenuepickup numeric(19, 5)
    declare @fg37_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg37_bookedroomrevenuepickup numeric(19, 5)
    declare @fg38_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg38_bookedroomrevenuepickup numeric(19, 5)
    declare @fg39_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg39_bookedroomrevenuepickup numeric(19, 5)
    declare @fg40_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg40_bookedroomrevenuepickup numeric(19, 5)
    declare @fg41_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg41_bookedroomrevenuepickup numeric(19, 5)
    declare @fg42_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg42_bookedroomrevenuepickup numeric(19, 5)
    declare @fg43_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg43_bookedroomrevenuepickup numeric(19, 5)
    declare @fg44_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg44_bookedroomrevenuepickup numeric(19, 5)
    declare @fg45_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg45_bookedroomrevenuepickup numeric(19, 5)
    declare @fg46_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg46_bookedroomrevenuepickup numeric(19, 5)
    declare @fg47_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg47_bookedroomrevenuepickup numeric(19, 5)
    declare @fg48_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg48_bookedroomrevenuepickup numeric(19, 5)
    declare @fg49_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg49_bookedroomrevenuepickup numeric(19, 5)
    declare @fg50_bookedroomrevenuecurrent numeric(19, 5)
    declare @fg50_bookedroomrevenuepickup numeric(19, 5)
    declare @fg1_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg1_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg2_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg2_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg3_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg3_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg4_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg4_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg5_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg5_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg6_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg6_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg7_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg7_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg8_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg8_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg9_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg9_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg10_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg10_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg11_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg11_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg12_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg12_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg13_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg13_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg14_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg14_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg15_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg15_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg16_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg16_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg17_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg17_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg18_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg18_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg19_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg19_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg20_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg20_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg21_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg21_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg22_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg22_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg23_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg23_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg24_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg24_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg25_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg25_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg26_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg26_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg27_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg27_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg28_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg28_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg29_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg29_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg30_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg30_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg31_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg31_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg32_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg32_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg33_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg33_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg34_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg34_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg35_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg35_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg36_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg36_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg37_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg37_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg38_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg38_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg39_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg39_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg40_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg40_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg41_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg41_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg42_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg42_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg43_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg43_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg44_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg44_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg45_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg45_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg46_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg46_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg47_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg47_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg48_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg48_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg49_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg49_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg50_fcstedroomrevenuecurrent numeric(19, 5)
    declare @fg50_fcstedroomrevenuepickup numeric(19, 5)
    declare @fg1_bookedadrcurrent numeric(19, 5)
    declare @fg1_bookedadrpickup numeric(19, 5)
    declare @fg2_bookedadrcurrent numeric(19, 5)
    declare @fg2_bookedadrpickup numeric(19, 5)
    declare @fg3_bookedadrcurrent numeric(19, 5)
    declare @fg3_bookedadrpickup numeric(19, 5)
    declare @fg4_bookedadrcurrent numeric(19, 5)
    declare @fg4_bookedadrpickup numeric(19, 5)
    declare @fg5_bookedadrcurrent numeric(19, 5)
    declare @fg5_bookedadrpickup numeric(19, 5)
    declare @fg6_bookedadrcurrent numeric(19, 5)
    declare @fg6_bookedadrpickup numeric(19, 5)
    declare @fg7_bookedadrcurrent numeric(19, 5)
    declare @fg7_bookedadrpickup numeric(19, 5)
    declare @fg8_bookedadrcurrent numeric(19, 5)
    declare @fg8_bookedadrpickup numeric(19, 5)
    declare @fg9_bookedadrcurrent numeric(19, 5)
    declare @fg9_bookedadrpickup numeric(19, 5)
    declare @fg10_bookedadrcurrent numeric(19, 5)
    declare @fg10_bookedadrpickup numeric(19, 5)
    declare @fg11_bookedadrcurrent numeric(19, 5)
    declare @fg11_bookedadrpickup numeric(19, 5)
    declare @fg12_bookedadrcurrent numeric(19, 5)
    declare @fg12_bookedadrpickup numeric(19, 5)
    declare @fg13_bookedadrcurrent numeric(19, 5)
    declare @fg13_bookedadrpickup numeric(19, 5)
    declare @fg14_bookedadrcurrent numeric(19, 5)
    declare @fg14_bookedadrpickup numeric(19, 5)
    declare @fg15_bookedadrcurrent numeric(19, 5)
    declare @fg15_bookedadrpickup numeric(19, 5)
    declare @fg16_bookedadrcurrent numeric(19, 5)
    declare @fg16_bookedadrpickup numeric(19, 5)
    declare @fg17_bookedadrcurrent numeric(19, 5)
    declare @fg17_bookedadrpickup numeric(19, 5)
    declare @fg18_bookedadrcurrent numeric(19, 5)
    declare @fg18_bookedadrpickup numeric(19, 5)
    declare @fg19_bookedadrcurrent numeric(19, 5)
    declare @fg19_bookedadrpickup numeric(19, 5)
    declare @fg20_bookedadrcurrent numeric(19, 5)
    declare @fg20_bookedadrpickup numeric(19, 5)
    declare @fg21_bookedadrcurrent numeric(19, 5)
    declare @fg21_bookedadrpickup numeric(19, 5)
    declare @fg22_bookedadrcurrent numeric(19, 5)
    declare @fg22_bookedadrpickup numeric(19, 5)
    declare @fg23_bookedadrcurrent numeric(19, 5)
    declare @fg23_bookedadrpickup numeric(19, 5)
    declare @fg24_bookedadrcurrent numeric(19, 5)
    declare @fg24_bookedadrpickup numeric(19, 5)
    declare @fg25_bookedadrcurrent numeric(19, 5)
    declare @fg25_bookedadrpickup numeric(19, 5)
    declare @fg26_bookedadrcurrent numeric(19, 5)
    declare @fg26_bookedadrpickup numeric(19, 5)
    declare @fg27_bookedadrcurrent numeric(19, 5)
    declare @fg27_bookedadrpickup numeric(19, 5)
    declare @fg28_bookedadrcurrent numeric(19, 5)
    declare @fg28_bookedadrpickup numeric(19, 5)
    declare @fg29_bookedadrcurrent numeric(19, 5)
    declare @fg29_bookedadrpickup numeric(19, 5)
    declare @fg30_bookedadrcurrent numeric(19, 5)
    declare @fg30_bookedadrpickup numeric(19, 5)
    declare @fg31_bookedadrcurrent numeric(19, 5)
    declare @fg31_bookedadrpickup numeric(19, 5)
    declare @fg32_bookedadrcurrent numeric(19, 5)
    declare @fg32_bookedadrpickup numeric(19, 5)
    declare @fg33_bookedadrcurrent numeric(19, 5)
    declare @fg33_bookedadrpickup numeric(19, 5)
    declare @fg34_bookedadrcurrent numeric(19, 5)
    declare @fg34_bookedadrpickup numeric(19, 5)
    declare @fg35_bookedadrcurrent numeric(19, 5)
    declare @fg35_bookedadrpickup numeric(19, 5)
    declare @fg36_bookedadrcurrent numeric(19, 5)
    declare @fg36_bookedadrpickup numeric(19, 5)
    declare @fg37_bookedadrcurrent numeric(19, 5)
    declare @fg37_bookedadrpickup numeric(19, 5)
    declare @fg38_bookedadrcurrent numeric(19, 5)
    declare @fg38_bookedadrpickup numeric(19, 5)
    declare @fg39_bookedadrcurrent numeric(19, 5)
    declare @fg39_bookedadrpickup numeric(19, 5)
    declare @fg40_bookedadrcurrent numeric(19, 5)
    declare @fg40_bookedadrpickup numeric(19, 5)
    declare @fg41_bookedadrcurrent numeric(19, 5)
    declare @fg41_bookedadrpickup numeric(19, 5)
    declare @fg42_bookedadrcurrent numeric(19, 5)
    declare @fg42_bookedadrpickup numeric(19, 5)
    declare @fg43_bookedadrcurrent numeric(19, 5)
    declare @fg43_bookedadrpickup numeric(19, 5)
    declare @fg44_bookedadrcurrent numeric(19, 5)
    declare @fg44_bookedadrpickup numeric(19, 5)
    declare @fg45_bookedadrcurrent numeric(19, 5)
    declare @fg45_bookedadrpickup numeric(19, 5)
    declare @fg46_bookedadrcurrent numeric(19, 5)
    declare @fg46_bookedadrpickup numeric(19, 5)
    declare @fg47_bookedadrcurrent numeric(19, 5)
    declare @fg47_bookedadrpickup numeric(19, 5)
    declare @fg48_bookedadrcurrent numeric(19, 5)
    declare @fg48_bookedadrpickup numeric(19, 5)
    declare @fg49_bookedadrcurrent numeric(19, 5)
    declare @fg49_bookedadrpickup numeric(19, 5)
    declare @fg50_bookedadrcurrent numeric(19, 5)
    declare @fg50_bookedadrpickup numeric(19, 5)
    declare @fg1_fcstedadrcurrent numeric(19, 5)
    declare @fg1_fcstedadrpickup numeric(19, 5)
    declare @fg2_fcstedadrcurrent numeric(19, 5)
    declare @fg2_fcstedadrpickup numeric(19, 5)
    declare @fg3_fcstedadrcurrent numeric(19, 5)
    declare @fg3_fcstedadrpickup numeric(19, 5)
    declare @fg4_fcstedadrcurrent numeric(19, 5)
    declare @fg4_fcstedadrpickup numeric(19, 5)
    declare @fg5_fcstedadrcurrent numeric(19, 5)
    declare @fg5_fcstedadrpickup numeric(19, 5)
    declare @fg6_fcstedadrcurrent numeric(19, 5)
    declare @fg6_fcstedadrpickup numeric(19, 5)
    declare @fg7_fcstedadrcurrent numeric(19, 5)
    declare @fg7_fcstedadrpickup numeric(19, 5)
    declare @fg8_fcstedadrcurrent numeric(19, 5)
    declare @fg8_fcstedadrpickup numeric(19, 5)
    declare @fg9_fcstedadrcurrent numeric(19, 5)
    declare @fg9_fcstedadrpickup numeric(19, 5)
    declare @fg10_fcstedadrcurrent numeric(19, 5)
    declare @fg10_fcstedadrpickup numeric(19, 5)
    declare @fg11_fcstedadrcurrent numeric(19, 5)
    declare @fg11_fcstedadrpickup numeric(19, 5)
    declare @fg12_fcstedadrcurrent numeric(19, 5)
    declare @fg12_fcstedadrpickup numeric(19, 5)
    declare @fg13_fcstedadrcurrent numeric(19, 5)
    declare @fg13_fcstedadrpickup numeric(19, 5)
    declare @fg14_fcstedadrcurrent numeric(19, 5)
    declare @fg14_fcstedadrpickup numeric(19, 5)
    declare @fg15_fcstedadrcurrent numeric(19, 5)
    declare @fg15_fcstedadrpickup numeric(19, 5)
    declare @fg16_fcstedadrcurrent numeric(19, 5)
    declare @fg16_fcstedadrpickup numeric(19, 5)
    declare @fg17_fcstedadrcurrent numeric(19, 5)
    declare @fg17_fcstedadrpickup numeric(19, 5)
    declare @fg18_fcstedadrcurrent numeric(19, 5)
    declare @fg18_fcstedadrpickup numeric(19, 5)
    declare @fg19_fcstedadrcurrent numeric(19, 5)
    declare @fg19_fcstedadrpickup numeric(19, 5)
    declare @fg20_fcstedadrcurrent numeric(19, 5)
    declare @fg20_fcstedadrpickup numeric(19, 5)
    declare @fg21_fcstedadrcurrent numeric(19, 5)
    declare @fg21_fcstedadrpickup numeric(19, 5)
    declare @fg22_fcstedadrcurrent numeric(19, 5)
    declare @fg22_fcstedadrpickup numeric(19, 5)
    declare @fg23_fcstedadrcurrent numeric(19, 5)
    declare @fg23_fcstedadrpickup numeric(19, 5)
    declare @fg24_fcstedadrcurrent numeric(19, 5)
    declare @fg24_fcstedadrpickup numeric(19, 5)
    declare @fg25_fcstedadrcurrent numeric(19, 5)
    declare @fg25_fcstedadrpickup numeric(19, 5)
    declare @fg26_fcstedadrcurrent numeric(19, 5)
    declare @fg26_fcstedadrpickup numeric(19, 5)
    declare @fg27_fcstedadrcurrent numeric(19, 5)
    declare @fg27_fcstedadrpickup numeric(19, 5)
    declare @fg28_fcstedadrcurrent numeric(19, 5)
    declare @fg28_fcstedadrpickup numeric(19, 5)
    declare @fg29_fcstedadrcurrent numeric(19, 5)
    declare @fg29_fcstedadrpickup numeric(19, 5)
    declare @fg30_fcstedadrcurrent numeric(19, 5)
    declare @fg30_fcstedadrpickup numeric(19, 5)
    declare @fg31_fcstedadrcurrent numeric(19, 5)
    declare @fg31_fcstedadrpickup numeric(19, 5)
    declare @fg32_fcstedadrcurrent numeric(19, 5)
    declare @fg32_fcstedadrpickup numeric(19, 5)
    declare @fg33_fcstedadrcurrent numeric(19, 5)
    declare @fg33_fcstedadrpickup numeric(19, 5)
    declare @fg34_fcstedadrcurrent numeric(19, 5)
    declare @fg34_fcstedadrpickup numeric(19, 5)
    declare @fg35_fcstedadrcurrent numeric(19, 5)
    declare @fg35_fcstedadrpickup numeric(19, 5)
    declare @fg36_fcstedadrcurrent numeric(19, 5)
    declare @fg36_fcstedadrpickup numeric(19, 5)
    declare @fg37_fcstedadrcurrent numeric(19, 5)
    declare @fg37_fcstedadrpickup numeric(19, 5)
    declare @fg38_fcstedadrcurrent numeric(19, 5)
    declare @fg38_fcstedadrpickup numeric(19, 5)
    declare @fg39_fcstedadrcurrent numeric(19, 5)
    declare @fg39_fcstedadrpickup numeric(19, 5)
    declare @fg40_fcstedadrcurrent numeric(19, 5)
    declare @fg40_fcstedadrpickup numeric(19, 5)
    declare @fg41_fcstedadrcurrent numeric(19, 5)
    declare @fg41_fcstedadrpickup numeric(19, 5)
    declare @fg42_fcstedadrcurrent numeric(19, 5)
    declare @fg42_fcstedadrpickup numeric(19, 5)
    declare @fg43_fcstedadrcurrent numeric(19, 5)
    declare @fg43_fcstedadrpickup numeric(19, 5)
    declare @fg44_fcstedadrcurrent numeric(19, 5)
    declare @fg44_fcstedadrpickup numeric(19, 5)
    declare @fg45_fcstedadrcurrent numeric(19, 5)
    declare @fg45_fcstedadrpickup numeric(19, 5)
    declare @fg46_fcstedadrcurrent numeric(19, 5)
    declare @fg46_fcstedadrpickup numeric(19, 5)
    declare @fg47_fcstedadrcurrent numeric(19, 5)
    declare @fg47_fcstedadrpickup numeric(19, 5)
    declare @fg48_fcstedadrcurrent numeric(19, 5)
    declare @fg48_fcstedadrpickup numeric(19, 5)
    declare @fg49_fcstedadrcurrent numeric(19, 5)
    declare @fg49_fcstedadrpickup numeric(19, 5)
    declare @fg50_fcstedadrcurrent numeric(19, 5)
    declare @fg50_fcstedadrpickup numeric(19, 5)
    declare @fg1_block int
    declare @fg1_block_available int
    declare @fg1_block_pickup int
    declare @fg2_block int
    declare @fg2_block_available int
    declare @fg2_block_pickup int
    declare @fg3_block int
    declare @fg3_block_available int
    declare @fg3_block_pickup int
    declare @fg4_block int
    declare @fg4_block_available int
    declare @fg4_block_pickup int
    declare @fg5_block int
    declare @fg5_block_available int
    declare @fg5_block_pickup int
    declare @fg6_block int
    declare @fg6_block_available int
    declare @fg6_block_pickup int
    declare @fg7_block int
    declare @fg7_block_available int
    declare @fg7_block_pickup int
    declare @fg8_block int
    declare @fg8_block_available int
    declare @fg8_block_pickup int
    declare @fg9_block int
    declare @fg9_block_available int
    declare @fg9_block_pickup int
    declare @fg10_block int
    declare @fg10_block_available int
    declare @fg10_block_pickup int
    declare @fg11_block int
    declare @fg11_block_available int
    declare @fg11_block_pickup int
    declare @fg12_block int
    declare @fg12_block_available int
    declare @fg12_block_pickup int
    declare @fg13_block int
    declare @fg13_block_available int
    declare @fg13_block_pickup int
    declare @fg14_block int
    declare @fg14_block_available int
    declare @fg14_block_pickup int
    declare @fg15_block int
    declare @fg15_block_available int
    declare @fg15_block_pickup int
    declare @fg16_block int
    declare @fg16_block_available int
    declare @fg16_block_pickup int
    declare @fg17_block int
    declare @fg17_block_available int
    declare @fg17_block_pickup int
    declare @fg18_block int
    declare @fg18_block_available int
    declare @fg18_block_pickup int
    declare @fg19_block int
    declare @fg19_block_available int
    declare @fg19_block_pickup int
    declare @fg20_block int
    declare @fg20_block_available int
    declare @fg20_block_pickup int
    declare @fg21_block int
    declare @fg21_block_available int
    declare @fg21_block_pickup int
    declare @fg22_block int
    declare @fg22_block_available int
    declare @fg22_block_pickup int
    declare @fg23_block int
    declare @fg23_block_available int
    declare @fg23_block_pickup int
    declare @fg24_block int
    declare @fg24_block_available int
    declare @fg24_block_pickup int
    declare @fg25_block int
    declare @fg25_block_available int
    declare @fg25_block_pickup int
    declare @fg26_block int
    declare @fg26_block_available int
    declare @fg26_block_pickup int
    declare @fg27_block int
    declare @fg27_block_available int
    declare @fg27_block_pickup int
    declare @fg28_block int
    declare @fg28_block_available int
    declare @fg28_block_pickup int
    declare @fg29_block int
    declare @fg29_block_available int
    declare @fg29_block_pickup int
    declare @fg30_block int
    declare @fg30_block_available int
    declare @fg30_block_pickup int
    declare @fg31_block int
    declare @fg31_block_available int
    declare @fg31_block_pickup int
    declare @fg32_block int
    declare @fg32_block_available int
    declare @fg32_block_pickup int
    declare @fg33_block int
    declare @fg33_block_available int
    declare @fg33_block_pickup int
    declare @fg34_block int
    declare @fg34_block_available int
    declare @fg34_block_pickup int
    declare @fg35_block int
    declare @fg35_block_available int
    declare @fg35_block_pickup int
    declare @fg36_block int
    declare @fg36_block_available int
    declare @fg36_block_pickup int
    declare @fg37_block int
    declare @fg37_block_available int
    declare @fg37_block_pickup int
    declare @fg38_block int
    declare @fg38_block_available int
    declare @fg38_block_pickup int
    declare @fg39_block int
    declare @fg39_block_available int
    declare @fg39_block_pickup int
    declare @fg40_block int
    declare @fg40_block_available int
    declare @fg40_block_pickup int
    declare @fg41_block int
    declare @fg41_block_available int
    declare @fg41_block_pickup int
    declare @fg42_block int
    declare @fg42_block_available int
    declare @fg42_block_pickup int
    declare @fg43_block int
    declare @fg43_block_available int
    declare @fg43_block_pickup int
    declare @fg44_block int
    declare @fg44_block_available int
    declare @fg44_block_pickup int
    declare @fg45_block int
    declare @fg45_block_available int
    declare @fg45_block_pickup int
    declare @fg46_block int
    declare @fg46_block_available int
    declare @fg46_block_pickup int
    declare @fg47_block int
    declare @fg47_block_available int
    declare @fg47_block_pickup int
    declare @fg48_block int
    declare @fg48_block_available int
    declare @fg48_block_pickup int
    declare @fg49_block int
    declare @fg49_block_available int
    declare @fg49_block_pickup int
    declare @fg50_block int
    declare @fg50_block_available int
    declare @fg50_block_pickup int
    declare @fg1_profit_onBooks_current numeric(19, 5)
    declare @fg1_profit_onBooks_pickup numeric(19, 5)
    declare @fg2_profit_onBooks_current numeric(19, 5)
    declare @fg2_profit_onBooks_pickup numeric(19, 5)
    declare @fg3_profit_onBooks_current numeric(19, 5)
    declare @fg3_profit_onBooks_pickup numeric(19, 5)
    declare @fg4_profit_onBooks_current numeric(19, 5)
    declare @fg4_profit_onBooks_pickup numeric(19, 5)
    declare @fg5_profit_onBooks_current numeric(19, 5)
    declare @fg5_profit_onBooks_pickup numeric(19, 5)
    declare @fg6_profit_onBooks_current numeric(19, 5)
    declare @fg6_profit_onBooks_pickup numeric(19, 5)
    declare @fg7_profit_onBooks_current numeric(19, 5)
    declare @fg7_profit_onBooks_pickup numeric(19, 5)
    declare @fg8_profit_onBooks_current numeric(19, 5)
    declare @fg8_profit_onBooks_pickup numeric(19, 5)
    declare @fg9_profit_onBooks_current numeric(19, 5)
    declare @fg9_profit_onBooks_pickup numeric(19, 5)
    declare @fg10_profit_onBooks_current numeric(19, 5)
    declare @fg10_profit_onBooks_pickup numeric(19, 5)
    declare @fg11_profit_onBooks_current numeric(19, 5)
    declare @fg11_profit_onBooks_pickup numeric(19, 5)
    declare @fg12_profit_onBooks_current numeric(19, 5)
    declare @fg12_profit_onBooks_pickup numeric(19, 5)
    declare @fg13_profit_onBooks_current numeric(19, 5)
    declare @fg13_profit_onBooks_pickup numeric(19, 5)
    declare @fg14_profit_onBooks_current numeric(19, 5)
    declare @fg14_profit_onBooks_pickup numeric(19, 5)
    declare @fg15_profit_onBooks_current numeric(19, 5)
    declare @fg15_profit_onBooks_pickup numeric(19, 5)
    declare @fg16_profit_onBooks_current numeric(19, 5)
    declare @fg16_profit_onBooks_pickup numeric(19, 5)
    declare @fg17_profit_onBooks_current numeric(19, 5)
    declare @fg17_profit_onBooks_pickup numeric(19, 5)
    declare @fg18_profit_onBooks_current numeric(19, 5)
    declare @fg18_profit_onBooks_pickup numeric(19, 5)
    declare @fg19_profit_onBooks_current numeric(19, 5)
    declare @fg19_profit_onBooks_pickup numeric(19, 5)
    declare @fg20_profit_onBooks_current numeric(19, 5)
    declare @fg20_profit_onBooks_pickup numeric(19, 5)
    declare @fg21_profit_onBooks_current numeric(19, 5)
    declare @fg21_profit_onBooks_pickup numeric(19, 5)
    declare @fg22_profit_onBooks_current numeric(19, 5)
    declare @fg22_profit_onBooks_pickup numeric(19, 5)
    declare @fg23_profit_onBooks_current numeric(19, 5)
    declare @fg23_profit_onBooks_pickup numeric(19, 5)
    declare @fg24_profit_onBooks_current numeric(19, 5)
    declare @fg24_profit_onBooks_pickup numeric(19, 5)
    declare @fg25_profit_onBooks_current numeric(19, 5)
    declare @fg25_profit_onBooks_pickup numeric(19, 5)
    declare @fg26_profit_onBooks_current numeric(19, 5)
    declare @fg26_profit_onBooks_pickup numeric(19, 5)
    declare @fg27_profit_onBooks_current numeric(19, 5)
    declare @fg27_profit_onBooks_pickup numeric(19, 5)
    declare @fg28_profit_onBooks_current numeric(19, 5)
    declare @fg28_profit_onBooks_pickup numeric(19, 5)
    declare @fg29_profit_onBooks_current numeric(19, 5)
    declare @fg29_profit_onBooks_pickup numeric(19, 5)
    declare @fg30_profit_onBooks_current numeric(19, 5)
    declare @fg30_profit_onBooks_pickup numeric(19, 5)
    declare @fg31_profit_onBooks_current numeric(19, 5)
    declare @fg31_profit_onBooks_pickup numeric(19, 5)
    declare @fg32_profit_onBooks_current numeric(19, 5)
    declare @fg32_profit_onBooks_pickup numeric(19, 5)
    declare @fg33_profit_onBooks_current numeric(19, 5)
    declare @fg33_profit_onBooks_pickup numeric(19, 5)
    declare @fg34_profit_onBooks_current numeric(19, 5)
    declare @fg34_profit_onBooks_pickup numeric(19, 5)
    declare @fg35_profit_onBooks_current numeric(19, 5)
    declare @fg35_profit_onBooks_pickup numeric(19, 5)
    declare @fg36_profit_onBooks_current numeric(19, 5)
    declare @fg36_profit_onBooks_pickup numeric(19, 5)
    declare @fg37_profit_onBooks_current numeric(19, 5)
    declare @fg37_profit_onBooks_pickup numeric(19, 5)
    declare @fg38_profit_onBooks_current numeric(19, 5)
    declare @fg38_profit_onBooks_pickup numeric(19, 5)
    declare @fg39_profit_onBooks_current numeric(19, 5)
    declare @fg39_profit_onBooks_pickup numeric(19, 5)
    declare @fg40_profit_onBooks_current numeric(19, 5)
    declare @fg40_profit_onBooks_pickup numeric(19, 5)
    declare @fg41_profit_onBooks_current numeric(19, 5)
    declare @fg41_profit_onBooks_pickup numeric(19, 5)
    declare @fg42_profit_onBooks_current numeric(19, 5)
    declare @fg42_profit_onBooks_pickup numeric(19, 5)
    declare @fg43_profit_onBooks_current numeric(19, 5)
    declare @fg43_profit_onBooks_pickup numeric(19, 5)
    declare @fg44_profit_onBooks_current numeric(19, 5)
    declare @fg44_profit_onBooks_pickup numeric(19, 5)
    declare @fg45_profit_onBooks_current numeric(19, 5)
    declare @fg45_profit_onBooks_pickup numeric(19, 5)
    declare @fg46_profit_onBooks_current numeric(19, 5)
    declare @fg46_profit_onBooks_pickup numeric(19, 5)
    declare @fg47_profit_onBooks_current numeric(19, 5)
    declare @fg47_profit_onBooks_pickup numeric(19, 5)
    declare @fg48_profit_onBooks_current numeric(19, 5)
    declare @fg48_profit_onBooks_pickup numeric(19, 5)
    declare @fg49_profit_onBooks_current numeric(19, 5)
    declare @fg49_profit_onBooks_pickup numeric(19, 5)
    declare @fg50_profit_onBooks_current numeric(19, 5)
    declare @fg50_profit_onBooks_pickup numeric(19, 5)
    declare @fg1_proPOR_onBooks_current numeric(19, 5)
    declare @fg1_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg2_proPOR_onBooks_current numeric(19, 5)
    declare @fg2_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg3_proPOR_onBooks_current numeric(19, 5)
    declare @fg3_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg4_proPOR_onBooks_current numeric(19, 5)
    declare @fg4_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg5_proPOR_onBooks_current numeric(19, 5)
    declare @fg5_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg6_proPOR_onBooks_current numeric(19, 5)
    declare @fg6_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg7_proPOR_onBooks_current numeric(19, 5)
    declare @fg7_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg8_proPOR_onBooks_current numeric(19, 5)
    declare @fg8_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg9_proPOR_onBooks_current numeric(19, 5)
    declare @fg9_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg10_proPOR_onBooks_current numeric(19, 5)
    declare @fg10_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg11_proPOR_onBooks_current numeric(19, 5)
    declare @fg11_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg12_proPOR_onBooks_current numeric(19, 5)
    declare @fg12_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg13_proPOR_onBooks_current numeric(19, 5)
    declare @fg13_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg14_proPOR_onBooks_current numeric(19, 5)
    declare @fg14_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg15_proPOR_onBooks_current numeric(19, 5)
    declare @fg15_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg16_proPOR_onBooks_current numeric(19, 5)
    declare @fg16_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg17_proPOR_onBooks_current numeric(19, 5)
    declare @fg17_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg18_proPOR_onBooks_current numeric(19, 5)
    declare @fg18_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg19_proPOR_onBooks_current numeric(19, 5)
    declare @fg19_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg20_proPOR_onBooks_current numeric(19, 5)
    declare @fg20_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg21_proPOR_onBooks_current numeric(19, 5)
    declare @fg21_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg22_proPOR_onBooks_current numeric(19, 5)
    declare @fg22_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg23_proPOR_onBooks_current numeric(19, 5)
    declare @fg23_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg24_proPOR_onBooks_current numeric(19, 5)
    declare @fg24_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg25_proPOR_onBooks_current numeric(19, 5)
    declare @fg25_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg26_proPOR_onBooks_current numeric(19, 5)
    declare @fg26_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg27_proPOR_onBooks_current numeric(19, 5)
    declare @fg27_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg28_proPOR_onBooks_current numeric(19, 5)
    declare @fg28_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg29_proPOR_onBooks_current numeric(19, 5)
    declare @fg29_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg30_proPOR_onBooks_current numeric(19, 5)
    declare @fg30_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg31_proPOR_onBooks_current numeric(19, 5)
    declare @fg31_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg32_proPOR_onBooks_current numeric(19, 5)
    declare @fg32_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg33_proPOR_onBooks_current numeric(19, 5)
    declare @fg33_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg34_proPOR_onBooks_current numeric(19, 5)
    declare @fg34_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg35_proPOR_onBooks_current numeric(19, 5)
    declare @fg35_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg36_proPOR_onBooks_current numeric(19, 5)
    declare @fg36_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg37_proPOR_onBooks_current numeric(19, 5)
    declare @fg37_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg38_proPOR_onBooks_current numeric(19, 5)
    declare @fg38_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg39_proPOR_onBooks_current numeric(19, 5)
    declare @fg39_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg40_proPOR_onBooks_current numeric(19, 5)
    declare @fg40_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg41_proPOR_onBooks_current numeric(19, 5)
    declare @fg41_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg42_proPOR_onBooks_current numeric(19, 5)
    declare @fg42_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg43_proPOR_onBooks_current numeric(19, 5)
    declare @fg43_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg44_proPOR_onBooks_current numeric(19, 5)
    declare @fg44_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg45_proPOR_onBooks_current numeric(19, 5)
    declare @fg45_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg46_proPOR_onBooks_current numeric(19, 5)
    declare @fg46_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg47_proPOR_onBooks_current numeric(19, 5)
    declare @fg47_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg48_proPOR_onBooks_current numeric(19, 5)
    declare @fg48_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg49_proPOR_onBooks_current numeric(19, 5)
    declare @fg49_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg50_proPOR_onBooks_current numeric(19, 5)
    declare @fg50_proPOR_onBooks_pickup numeric(19, 5)
    declare @fg1_profit_forecast_current numeric(19, 5)
    declare @fg1_profit_forecast_pickup numeric(19, 5)
    declare @fg2_profit_forecast_current numeric(19, 5)
    declare @fg2_profit_forecast_pickup numeric(19, 5)
    declare @fg3_profit_forecast_current numeric(19, 5)
    declare @fg3_profit_forecast_pickup numeric(19, 5)
    declare @fg4_profit_forecast_current numeric(19, 5)
    declare @fg4_profit_forecast_pickup numeric(19, 5)
    declare @fg5_profit_forecast_current numeric(19, 5)
    declare @fg5_profit_forecast_pickup numeric(19, 5)
    declare @fg6_profit_forecast_current numeric(19, 5)
    declare @fg6_profit_forecast_pickup numeric(19, 5)
    declare @fg7_profit_forecast_current numeric(19, 5)
    declare @fg7_profit_forecast_pickup numeric(19, 5)
    declare @fg8_profit_forecast_current numeric(19, 5)
    declare @fg8_profit_forecast_pickup numeric(19, 5)
    declare @fg9_profit_forecast_current numeric(19, 5)
    declare @fg9_profit_forecast_pickup numeric(19, 5)
    declare @fg10_profit_forecast_current numeric(19, 5)
    declare @fg10_profit_forecast_pickup numeric(19, 5)
    declare @fg11_profit_forecast_current numeric(19, 5)
    declare @fg11_profit_forecast_pickup numeric(19, 5)
    declare @fg12_profit_forecast_current numeric(19, 5)
    declare @fg12_profit_forecast_pickup numeric(19, 5)
    declare @fg13_profit_forecast_current numeric(19, 5)
    declare @fg13_profit_forecast_pickup numeric(19, 5)
    declare @fg14_profit_forecast_current numeric(19, 5)
    declare @fg14_profit_forecast_pickup numeric(19, 5)
    declare @fg15_profit_forecast_current numeric(19, 5)
    declare @fg15_profit_forecast_pickup numeric(19, 5)
    declare @fg16_profit_forecast_current numeric(19, 5)
    declare @fg16_profit_forecast_pickup numeric(19, 5)
    declare @fg17_profit_forecast_current numeric(19, 5)
    declare @fg17_profit_forecast_pickup numeric(19, 5)
    declare @fg18_profit_forecast_current numeric(19, 5)
    declare @fg18_profit_forecast_pickup numeric(19, 5)
    declare @fg19_profit_forecast_current numeric(19, 5)
    declare @fg19_profit_forecast_pickup numeric(19, 5)
    declare @fg20_profit_forecast_current numeric(19, 5)
    declare @fg20_profit_forecast_pickup numeric(19, 5)
    declare @fg21_profit_forecast_current numeric(19, 5)
    declare @fg21_profit_forecast_pickup numeric(19, 5)
    declare @fg22_profit_forecast_current numeric(19, 5)
    declare @fg22_profit_forecast_pickup numeric(19, 5)
    declare @fg23_profit_forecast_current numeric(19, 5)
    declare @fg23_profit_forecast_pickup numeric(19, 5)
    declare @fg24_profit_forecast_current numeric(19, 5)
    declare @fg24_profit_forecast_pickup numeric(19, 5)
    declare @fg25_profit_forecast_current numeric(19, 5)
    declare @fg25_profit_forecast_pickup numeric(19, 5)
    declare @fg26_profit_forecast_current numeric(19, 5)
    declare @fg26_profit_forecast_pickup numeric(19, 5)
    declare @fg27_profit_forecast_current numeric(19, 5)
    declare @fg27_profit_forecast_pickup numeric(19, 5)
    declare @fg28_profit_forecast_current numeric(19, 5)
    declare @fg28_profit_forecast_pickup numeric(19, 5)
    declare @fg29_profit_forecast_current numeric(19, 5)
    declare @fg29_profit_forecast_pickup numeric(19, 5)
    declare @fg30_profit_forecast_current numeric(19, 5)
    declare @fg30_profit_forecast_pickup numeric(19, 5)
    declare @fg31_profit_forecast_current numeric(19, 5)
    declare @fg31_profit_forecast_pickup numeric(19, 5)
    declare @fg32_profit_forecast_current numeric(19, 5)
    declare @fg32_profit_forecast_pickup numeric(19, 5)
    declare @fg33_profit_forecast_current numeric(19, 5)
    declare @fg33_profit_forecast_pickup numeric(19, 5)
    declare @fg34_profit_forecast_current numeric(19, 5)
    declare @fg34_profit_forecast_pickup numeric(19, 5)
    declare @fg35_profit_forecast_current numeric(19, 5)
    declare @fg35_profit_forecast_pickup numeric(19, 5)
    declare @fg36_profit_forecast_current numeric(19, 5)
    declare @fg36_profit_forecast_pickup numeric(19, 5)
    declare @fg37_profit_forecast_current numeric(19, 5)
    declare @fg37_profit_forecast_pickup numeric(19, 5)
    declare @fg38_profit_forecast_current numeric(19, 5)
    declare @fg38_profit_forecast_pickup numeric(19, 5)
    declare @fg39_profit_forecast_current numeric(19, 5)
    declare @fg39_profit_forecast_pickup numeric(19, 5)
    declare @fg40_profit_forecast_current numeric(19, 5)
    declare @fg40_profit_forecast_pickup numeric(19, 5)
    declare @fg41_profit_forecast_current numeric(19, 5)
    declare @fg41_profit_forecast_pickup numeric(19, 5)
    declare @fg42_profit_forecast_current numeric(19, 5)
    declare @fg42_profit_forecast_pickup numeric(19, 5)
    declare @fg43_profit_forecast_current numeric(19, 5)
    declare @fg43_profit_forecast_pickup numeric(19, 5)
    declare @fg44_profit_forecast_current numeric(19, 5)
    declare @fg44_profit_forecast_pickup numeric(19, 5)
    declare @fg45_profit_forecast_current numeric(19, 5)
    declare @fg45_profit_forecast_pickup numeric(19, 5)
    declare @fg46_profit_forecast_current numeric(19, 5)
    declare @fg46_profit_forecast_pickup numeric(19, 5)
    declare @fg47_profit_forecast_current numeric(19, 5)
    declare @fg47_profit_forecast_pickup numeric(19, 5)
    declare @fg48_profit_forecast_current numeric(19, 5)
    declare @fg48_profit_forecast_pickup numeric(19, 5)
    declare @fg49_profit_forecast_current numeric(19, 5)
    declare @fg49_profit_forecast_pickup numeric(19, 5)
    declare @fg50_profit_forecast_current numeric(19, 5)
    declare @fg50_profit_forecast_pickup numeric(19, 5)
    declare @fg1_proPOR_forecast_current numeric(19, 5)
    declare @fg1_proPOR_forecast_pickup numeric(19, 5)
    declare @fg2_proPOR_forecast_current numeric(19, 5)
    declare @fg2_proPOR_forecast_pickup numeric(19, 5)
    declare @fg3_proPOR_forecast_current numeric(19, 5)
    declare @fg3_proPOR_forecast_pickup numeric(19, 5)
    declare @fg4_proPOR_forecast_current numeric(19, 5)
    declare @fg4_proPOR_forecast_pickup numeric(19, 5)
    declare @fg5_proPOR_forecast_current numeric(19, 5)
    declare @fg5_proPOR_forecast_pickup numeric(19, 5)
    declare @fg6_proPOR_forecast_current numeric(19, 5)
    declare @fg6_proPOR_forecast_pickup numeric(19, 5)
    declare @fg7_proPOR_forecast_current numeric(19, 5)
    declare @fg7_proPOR_forecast_pickup numeric(19, 5)
    declare @fg8_proPOR_forecast_current numeric(19, 5)
    declare @fg8_proPOR_forecast_pickup numeric(19, 5)
    declare @fg9_proPOR_forecast_current numeric(19, 5)
    declare @fg9_proPOR_forecast_pickup numeric(19, 5)
    declare @fg10_proPOR_forecast_current numeric(19, 5)
    declare @fg10_proPOR_forecast_pickup numeric(19, 5)
    declare @fg11_proPOR_forecast_current numeric(19, 5)
    declare @fg11_proPOR_forecast_pickup numeric(19, 5)
    declare @fg12_proPOR_forecast_current numeric(19, 5)
    declare @fg12_proPOR_forecast_pickup numeric(19, 5)
    declare @fg13_proPOR_forecast_current numeric(19, 5)
    declare @fg13_proPOR_forecast_pickup numeric(19, 5)
    declare @fg14_proPOR_forecast_current numeric(19, 5)
    declare @fg14_proPOR_forecast_pickup numeric(19, 5)
    declare @fg15_proPOR_forecast_current numeric(19, 5)
    declare @fg15_proPOR_forecast_pickup numeric(19, 5)
    declare @fg16_proPOR_forecast_current numeric(19, 5)
    declare @fg16_proPOR_forecast_pickup numeric(19, 5)
    declare @fg17_proPOR_forecast_current numeric(19, 5)
    declare @fg17_proPOR_forecast_pickup numeric(19, 5)
    declare @fg18_proPOR_forecast_current numeric(19, 5)
    declare @fg18_proPOR_forecast_pickup numeric(19, 5)
    declare @fg19_proPOR_forecast_current numeric(19, 5)
    declare @fg19_proPOR_forecast_pickup numeric(19, 5)
    declare @fg20_proPOR_forecast_current numeric(19, 5)
    declare @fg20_proPOR_forecast_pickup numeric(19, 5)
    declare @fg21_proPOR_forecast_current numeric(19, 5)
    declare @fg21_proPOR_forecast_pickup numeric(19, 5)
    declare @fg22_proPOR_forecast_current numeric(19, 5)
    declare @fg22_proPOR_forecast_pickup numeric(19, 5)
    declare @fg23_proPOR_forecast_current numeric(19, 5)
    declare @fg23_proPOR_forecast_pickup numeric(19, 5)
    declare @fg24_proPOR_forecast_current numeric(19, 5)
    declare @fg24_proPOR_forecast_pickup numeric(19, 5)
    declare @fg25_proPOR_forecast_current numeric(19, 5)
    declare @fg25_proPOR_forecast_pickup numeric(19, 5)
    declare @fg26_proPOR_forecast_current numeric(19, 5)
    declare @fg26_proPOR_forecast_pickup numeric(19, 5)
    declare @fg27_proPOR_forecast_current numeric(19, 5)
    declare @fg27_proPOR_forecast_pickup numeric(19, 5)
    declare @fg28_proPOR_forecast_current numeric(19, 5)
    declare @fg28_proPOR_forecast_pickup numeric(19, 5)
    declare @fg29_proPOR_forecast_current numeric(19, 5)
    declare @fg29_proPOR_forecast_pickup numeric(19, 5)
    declare @fg30_proPOR_forecast_current numeric(19, 5)
    declare @fg30_proPOR_forecast_pickup numeric(19, 5)
    declare @fg31_proPOR_forecast_current numeric(19, 5)
    declare @fg31_proPOR_forecast_pickup numeric(19, 5)
    declare @fg32_proPOR_forecast_current numeric(19, 5)
    declare @fg32_proPOR_forecast_pickup numeric(19, 5)
    declare @fg33_proPOR_forecast_current numeric(19, 5)
    declare @fg33_proPOR_forecast_pickup numeric(19, 5)
    declare @fg34_proPOR_forecast_current numeric(19, 5)
    declare @fg34_proPOR_forecast_pickup numeric(19, 5)
    declare @fg35_proPOR_forecast_current numeric(19, 5)
    declare @fg35_proPOR_forecast_pickup numeric(19, 5)
    declare @fg36_proPOR_forecast_current numeric(19, 5)
    declare @fg36_proPOR_forecast_pickup numeric(19, 5)
    declare @fg37_proPOR_forecast_current numeric(19, 5)
    declare @fg37_proPOR_forecast_pickup numeric(19, 5)
    declare @fg38_proPOR_forecast_current numeric(19, 5)
    declare @fg38_proPOR_forecast_pickup numeric(19, 5)
    declare @fg39_proPOR_forecast_current numeric(19, 5)
    declare @fg39_proPOR_forecast_pickup numeric(19, 5)
    declare @fg40_proPOR_forecast_current numeric(19, 5)
    declare @fg40_proPOR_forecast_pickup numeric(19, 5)
    declare @fg41_proPOR_forecast_current numeric(19, 5)
    declare @fg41_proPOR_forecast_pickup numeric(19, 5)
    declare @fg42_proPOR_forecast_current numeric(19, 5)
    declare @fg42_proPOR_forecast_pickup numeric(19, 5)
    declare @fg43_proPOR_forecast_current numeric(19, 5)
    declare @fg43_proPOR_forecast_pickup numeric(19, 5)
    declare @fg44_proPOR_forecast_current numeric(19, 5)
    declare @fg44_proPOR_forecast_pickup numeric(19, 5)
    declare @fg45_proPOR_forecast_current numeric(19, 5)
    declare @fg45_proPOR_forecast_pickup numeric(19, 5)
    declare @fg46_proPOR_forecast_current numeric(19, 5)
    declare @fg46_proPOR_forecast_pickup numeric(19, 5)
    declare @fg47_proPOR_forecast_current numeric(19, 5)
    declare @fg47_proPOR_forecast_pickup numeric(19, 5)
    declare @fg48_proPOR_forecast_current numeric(19, 5)
    declare @fg48_proPOR_forecast_pickup numeric(19, 5)
    declare @fg49_proPOR_forecast_current numeric(19, 5)
    declare @fg49_proPOR_forecast_pickup numeric(19, 5)
    declare @fg50_proPOR_forecast_current numeric(19, 5)
    declare @fg50_proPOR_forecast_pickup numeric(19, 5)
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    ) --> extract caughtup date for a property

    if (@isRollingDate = 1)
begin
        set @business_start_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_start_dt, @caughtupdate)
        )
        set @business_end_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_end_dt, @caughtupdate)
        )
        set @start_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate)
        )
        set @end_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate)
        )
end

    declare @fg1 int
    declare @fg2 int
    declare @fg3 int
    declare @fg4 int
    declare @fg5 int
    declare @fg6 int
    declare @fg7 int
    declare @fg8 int
    declare @fg9 int
    declare @fg10 int
    declare @fg11 int
    declare @fg12 int
    declare @fg13 int
    declare @fg14 int
    declare @fg15 int
    declare @fg16 int
    declare @fg17 int
    declare @fg18 int
    declare @fg19 int
    declare @fg20 int
    declare @fg21 int
    declare @fg22 int
    declare @fg23 int
    declare @fg24 int
    declare @fg25 int
    declare @fg26 int
    declare @fg27 int
    declare @fg28 int
    declare @fg29 int
    declare @fg30 int
    declare @fg31 int
    declare @fg32 int
    declare @fg33 int
    declare @fg34 int
    declare @fg35 int
    declare @fg36 int
    declare @fg37 int
    declare @fg38 int
    declare @fg39 int
    declare @fg40 int
    declare @fg41 int
    declare @fg42 int
    declare @fg43 int
    declare @fg44 int
    declare @fg45 int
    declare @fg46 int
    declare @fg47 int
    declare @fg48 int
    declare @fg49 int
    declare @fg50 int


    declare @tempFG table
    (
        number int,
        Forecast_Group_id int
    )
    insert into @tempFG
select number = ROW_NUMBER() OVER (ORDER BY Forecast_Group_ID),
        Forecast_Group_id
from Forecast_Group
where Property_ID = @property_id
  and Forecast_Group_ID in (
    SELECT Value FROM varcharToInt(@forecast_group_id, ',')
)
  and Status_ID = 1

    set @fg1 =
    (
        Select Forecast_Group_id from @tempFG where number = 1
    )
set @fg2 =
    (
    Select Forecast_Group_id from @tempFG where number = 2
    )
set @fg3 =
    (
    Select Forecast_Group_id from @tempFG where number = 3
    )
set @fg4 =
    (
    Select Forecast_Group_id from @tempFG where number = 4
    )
set @fg5 =
    (
    Select Forecast_Group_id from @tempFG where number = 5
    )
set @fg6 =
    (
    Select Forecast_Group_id from @tempFG where number = 6
    )
set @fg7 =
    (
    Select Forecast_Group_id from @tempFG where number = 7
    )
set @fg8 =
    (
    Select Forecast_Group_id from @tempFG where number = 8
    )
set @fg9 =
    (
    Select Forecast_Group_id from @tempFG where number = 9
    )
set @fg10 =
    (
    Select Forecast_Group_id from @tempFG where number = 10
    )
set @fg11 =
    (
    Select Forecast_Group_id from @tempFG where number = 11
    )
set @fg12 =
    (
    Select Forecast_Group_id from @tempFG where number = 12
    )
set @fg13 =
    (
    Select Forecast_Group_id from @tempFG where number = 13
    )
set @fg14 =
    (
    Select Forecast_Group_id from @tempFG where number = 14
    )
set @fg15 =
    (
    Select Forecast_Group_id from @tempFG where number = 15
    )
set @fg16 =
    (
    Select Forecast_Group_id from @tempFG where number = 16
    )
set @fg17 =
    (
    Select Forecast_Group_id from @tempFG where number = 17
    )
set @fg18 =
    (
    Select Forecast_Group_id from @tempFG where number = 18
    )
set @fg19 =
    (
    Select Forecast_Group_id from @tempFG where number = 19
    )
set @fg20 =
    (
    Select Forecast_Group_id from @tempFG where number = 20
    )
set @fg21 =
    (
    Select Forecast_Group_id from @tempFG where number = 21
    )
set @fg22 =
    (
    Select Forecast_Group_id from @tempFG where number = 22
    )
set @fg23 =
    (
    Select Forecast_Group_id from @tempFG where number = 23
    )
set @fg24 =
    (
    Select Forecast_Group_id from @tempFG where number = 24
    )
set @fg25 =
    (
    Select Forecast_Group_id from @tempFG where number = 25
    )
set @fg26 =
    (
    Select Forecast_Group_id from @tempFG where number = 26
    )
set @fg27 =
    (
    Select Forecast_Group_id from @tempFG where number = 27
    )
set @fg28 =
    (
    Select Forecast_Group_id from @tempFG where number = 28
    )
set @fg29 =
    (
    Select Forecast_Group_id from @tempFG where number = 29
    )
set @fg30 =
    (
    Select Forecast_Group_id from @tempFG where number = 30
    )
set @fg31 =
    (
    Select Forecast_Group_id from @tempFG where number = 31
    )
set @fg32 =
    (
    Select Forecast_Group_id from @tempFG where number = 32
    )
set @fg33 =
    (
    Select Forecast_Group_id from @tempFG where number = 33
    )
set @fg34 =
    (
    Select Forecast_Group_id from @tempFG where number = 34
    )
set @fg35 =
    (
    Select Forecast_Group_id from @tempFG where number = 35
    )
set @fg36 =
    (
    Select Forecast_Group_id from @tempFG where number = 36
    )
set @fg37 =
    (
    Select Forecast_Group_id from @tempFG where number = 37
    )
set @fg38 =
    (
    Select Forecast_Group_id from @tempFG where number = 38
    )
set @fg39 =
    (
    Select Forecast_Group_id from @tempFG where number = 39
    )
set @fg40 =
    (
    Select Forecast_Group_id from @tempFG where number = 40
    )
set @fg41 =
    (
    Select Forecast_Group_id from @tempFG where number = 41
    )
set @fg42 =
    (
    Select Forecast_Group_id from @tempFG where number = 42
    )
set @fg43 =
    (
    Select Forecast_Group_id from @tempFG where number = 43
    )
set @fg44 =
    (
    Select Forecast_Group_id from @tempFG where number = 44
    )
set @fg45 =
    (
    Select Forecast_Group_id from @tempFG where number = 45
    )
set @fg46 =
    (
    Select Forecast_Group_id from @tempFG where number = 46
    )
set @fg47 =
    (
    Select Forecast_Group_id from @tempFG where number = 47
    )
set @fg48 =
    (
    Select Forecast_Group_id from @tempFG where number = 48
    )
set @fg49 =
    (
    Select Forecast_Group_id from @tempFG where number = 49
    )
set @fg50 =
    (
    Select Forecast_Group_id from @tempFG where number = 50
    )
--- extract report metrics
select occupancy_dt,
       dow,
       isnull(MAX(fg1_roomsoldcurrent), 0.0) as fg1_roomsoldcurrent,
       isnull(MAX(fg1_roomssoldpickup), 0.0) as fg1_roomssoldpickup,
       isnull(MAX(fg2_roomsoldcurrent), 0.0) as fg2_roomsoldcurrent,
       isnull(MAX(fg2_roomssoldpickup), 0.0) as fg2_roomssoldpickup,
       isnull(MAX(fg3_roomsoldcurrent), 0.0) as fg3_roomsoldcurrent,
       isnull(MAX(fg3_roomssoldpickup), 0.0) as fg3_roomssoldpickup,
       isnull(MAX(fg4_roomsoldcurrent), 0.0) as fg4_roomsoldcurrent,
       isnull(MAX(fg4_roomssoldpickup), 0.0) as fg4_roomssoldpickup,
       isnull(MAX(fg5_roomsoldcurrent), 0.0) as fg5_roomsoldcurrent,
       isnull(MAX(fg5_roomssoldpickup), 0.0) as fg5_roomssoldpickup,
       isnull(MAX(fg6_roomsoldcurrent), 0.0) as fg6_roomsoldcurrent,
       isnull(MAX(fg6_roomssoldpickup), 0.0) as fg6_roomssoldpickup,
       isnull(MAX(fg7_roomsoldcurrent), 0.0) as fg7_roomsoldcurrent,
       isnull(MAX(fg7_roomssoldpickup), 0.0) as fg7_roomssoldpickup,
       isnull(MAX(fg8_roomsoldcurrent), 0.0) as fg8_roomsoldcurrent,
       isnull(MAX(fg8_roomssoldpickup), 0.0) as fg8_roomssoldpickup,
       isnull(MAX(fg9_roomsoldcurrent), 0.0) as fg9_roomsoldcurrent,
       isnull(MAX(fg9_roomssoldpickup), 0.0) as fg9_roomssoldpickup,
       isnull(MAX(fg10_roomsoldcurrent), 0.0) as fg10_roomsoldcurrent,
       isnull(MAX(fg10_roomssoldpickup), 0.0) as fg10_roomssoldpickup,
       isnull(MAX(fg11_roomsoldcurrent), 0.0) as fg11_roomsoldcurrent,
       isnull(MAX(fg11_roomssoldpickup), 0.0) as fg11_roomssoldpickup,
       isnull(MAX(fg12_roomsoldcurrent), 0.0) as fg12_roomsoldcurrent,
       isnull(MAX(fg12_roomssoldpickup), 0.0) as fg12_roomssoldpickup,
       isnull(MAX(fg13_roomsoldcurrent), 0.0) as fg13_roomsoldcurrent,
       isnull(MAX(fg13_roomssoldpickup), 0.0) as fg13_roomssoldpickup,
       isnull(MAX(fg14_roomsoldcurrent), 0.0) as fg14_roomsoldcurrent,
       isnull(MAX(fg14_roomssoldpickup), 0.0) as fg14_roomssoldpickup,
       isnull(MAX(fg15_roomsoldcurrent), 0.0) as fg15_roomsoldcurrent,
       isnull(MAX(fg15_roomssoldpickup), 0.0) as fg15_roomssoldpickup,
       isnull(MAX(fg16_roomsoldcurrent), 0.0) as fg16_roomsoldcurrent,
       isnull(MAX(fg16_roomssoldpickup), 0.0) as fg16_roomssoldpickup,
       isnull(MAX(fg17_roomsoldcurrent), 0.0) as fg17_roomsoldcurrent,
       isnull(MAX(fg17_roomssoldpickup), 0.0) as fg17_roomssoldpickup,
       isnull(MAX(fg18_roomsoldcurrent), 0.0) as fg18_roomsoldcurrent,
       isnull(MAX(fg18_roomssoldpickup), 0.0) as fg18_roomssoldpickup,
       isnull(MAX(fg19_roomsoldcurrent), 0.0) as fg19_roomsoldcurrent,
       isnull(MAX(fg19_roomssoldpickup), 0.0) as fg19_roomssoldpickup,
       isnull(MAX(fg20_roomsoldcurrent), 0.0) as fg20_roomsoldcurrent,
       isnull(MAX(fg20_roomssoldpickup), 0.0) as fg20_roomssoldpickup,
       isnull(MAX(fg21_roomsoldcurrent), 0.0) as fg21_roomsoldcurrent,
       isnull(MAX(fg21_roomssoldpickup), 0.0) as fg21_roomssoldpickup,
       isnull(MAX(fg22_roomsoldcurrent), 0.0) as fg22_roomsoldcurrent,
       isnull(MAX(fg22_roomssoldpickup), 0.0) as fg22_roomssoldpickup,
       isnull(MAX(fg23_roomsoldcurrent), 0.0) as fg23_roomsoldcurrent,
       isnull(MAX(fg23_roomssoldpickup), 0.0) as fg23_roomssoldpickup,
       isnull(MAX(fg24_roomsoldcurrent), 0.0) as fg24_roomsoldcurrent,
       isnull(MAX(fg24_roomssoldpickup), 0.0) as fg24_roomssoldpickup,
       isnull(MAX(fg25_roomsoldcurrent), 0.0) as fg25_roomsoldcurrent,
       isnull(MAX(fg25_roomssoldpickup), 0.0) as fg25_roomssoldpickup,
       isnull(MAX(fg26_roomsoldcurrent), 0.0) as fg26_roomsoldcurrent,
       isnull(MAX(fg26_roomssoldpickup), 0.0) as fg26_roomssoldpickup,
       isnull(MAX(fg27_roomsoldcurrent), 0.0) as fg27_roomsoldcurrent,
       isnull(MAX(fg27_roomssoldpickup), 0.0) as fg27_roomssoldpickup,
       isnull(MAX(fg28_roomsoldcurrent), 0.0) as fg28_roomsoldcurrent,
       isnull(MAX(fg28_roomssoldpickup), 0.0) as fg28_roomssoldpickup,
       isnull(MAX(fg29_roomsoldcurrent), 0.0) as fg29_roomsoldcurrent,
       isnull(MAX(fg29_roomssoldpickup), 0.0) as fg29_roomssoldpickup,
       isnull(MAX(fg30_roomsoldcurrent), 0.0) as fg30_roomsoldcurrent,
       isnull(MAX(fg30_roomssoldpickup), 0.0) as fg30_roomssoldpickup,
       isnull(MAX(fg31_roomsoldcurrent), 0.0) as fg31_roomsoldcurrent,
       isnull(MAX(fg31_roomssoldpickup), 0.0) as fg31_roomssoldpickup,
       isnull(MAX(fg32_roomsoldcurrent), 0.0) as fg32_roomsoldcurrent,
       isnull(MAX(fg32_roomssoldpickup), 0.0) as fg32_roomssoldpickup,
       isnull(MAX(fg33_roomsoldcurrent), 0.0) as fg33_roomsoldcurrent,
       isnull(MAX(fg33_roomssoldpickup), 0.0) as fg33_roomssoldpickup,
       isnull(MAX(fg34_roomsoldcurrent), 0.0) as fg34_roomsoldcurrent,
       isnull(MAX(fg34_roomssoldpickup), 0.0) as fg34_roomssoldpickup,
       isnull(MAX(fg35_roomsoldcurrent), 0.0) as fg35_roomsoldcurrent,
       isnull(MAX(fg35_roomssoldpickup), 0.0) as fg35_roomssoldpickup,
       isnull(MAX(fg36_roomsoldcurrent), 0.0) as fg36_roomsoldcurrent,
       isnull(MAX(fg36_roomssoldpickup), 0.0) as fg36_roomssoldpickup,
       isnull(MAX(fg37_roomsoldcurrent), 0.0) as fg37_roomsoldcurrent,
       isnull(MAX(fg37_roomssoldpickup), 0.0) as fg37_roomssoldpickup,
       isnull(MAX(fg38_roomsoldcurrent), 0.0) as fg38_roomsoldcurrent,
       isnull(MAX(fg38_roomssoldpickup), 0.0) as fg38_roomssoldpickup,
       isnull(MAX(fg39_roomsoldcurrent), 0.0) as fg39_roomsoldcurrent,
       isnull(MAX(fg39_roomssoldpickup), 0.0) as fg39_roomssoldpickup,
       isnull(MAX(fg40_roomsoldcurrent), 0.0) as fg40_roomsoldcurrent,
       isnull(MAX(fg40_roomssoldpickup), 0.0) as fg40_roomssoldpickup,
       isnull(MAX(fg41_roomsoldcurrent), 0.0) as fg41_roomsoldcurrent,
       isnull(MAX(fg41_roomssoldpickup), 0.0) as fg41_roomssoldpickup,
       isnull(MAX(fg42_roomsoldcurrent), 0.0) as fg42_roomsoldcurrent,
       isnull(MAX(fg42_roomssoldpickup), 0.0) as fg42_roomssoldpickup,
       isnull(MAX(fg43_roomsoldcurrent), 0.0) as fg43_roomsoldcurrent,
       isnull(MAX(fg43_roomssoldpickup), 0.0) as fg43_roomssoldpickup,
       isnull(MAX(fg44_roomsoldcurrent), 0.0) as fg44_roomsoldcurrent,
       isnull(MAX(fg44_roomssoldpickup), 0.0) as fg44_roomssoldpickup,
       isnull(MAX(fg45_roomsoldcurrent), 0.0) as fg45_roomsoldcurrent,
       isnull(MAX(fg45_roomssoldpickup), 0.0) as fg45_roomssoldpickup,
       isnull(MAX(fg46_roomsoldcurrent), 0.0) as fg46_roomsoldcurrent,
       isnull(MAX(fg46_roomssoldpickup), 0.0) as fg46_roomssoldpickup,
       isnull(MAX(fg47_roomsoldcurrent), 0.0) as fg47_roomsoldcurrent,
       isnull(MAX(fg47_roomssoldpickup), 0.0) as fg47_roomssoldpickup,
       isnull(MAX(fg48_roomsoldcurrent), 0.0) as fg48_roomsoldcurrent,
       isnull(MAX(fg48_roomssoldpickup), 0.0) as fg48_roomssoldpickup,
       isnull(MAX(fg49_roomsoldcurrent), 0.0) as fg49_roomsoldcurrent,
       isnull(MAX(fg49_roomssoldpickup), 0.0) as fg49_roomssoldpickup,
       isnull(MAX(fg50_roomsoldcurrent), 0.0) as fg50_roomsoldcurrent,
       isnull(MAX(fg50_roomssoldpickup), 0.0) as fg50_roomssoldpickup,
       isnull(MAX(fg1_occfcstcurrent), 0.0) as fg1_occfcstcurrent,
       isnull(MAX(fg1_occfcstpickup), 0.0) as fg1_occfcstpickup,
       isnull(MAX(fg2_occfcstcurrent), 0.0) as fg2_occfcstcurrent,
       isnull(MAX(fg2_occfcstpickup), 0.0) as fg2_occfcstpickup,
       isnull(MAX(fg3_occfcstcurrent), 0.0) as fg3_occfcstcurrent,
       isnull(MAX(fg3_occfcstpickup), 0.0) as fg3_occfcstpickup,
       isnull(MAX(fg4_occfcstcurrent), 0.0) as fg4_occfcstcurrent,
       isnull(MAX(fg4_occfcstpickup), 0.0) as fg4_occfcstpickup,
       isnull(MAX(fg5_occfcstcurrent), 0.0) as fg5_occfcstcurrent,
       isnull(MAX(fg5_occfcstpickup), 0.0) as fg5_occfcstpickup,
       isnull(MAX(fg6_occfcstcurrent), 0.0) as fg6_occfcstcurrent,
       isnull(MAX(fg6_occfcstpickup), 0.0) as fg6_occfcstpickup,
       isnull(MAX(fg7_occfcstcurrent), 0.0) as fg7_occfcstcurrent,
       isnull(MAX(fg7_occfcstpickup), 0.0) as fg7_occfcstpickup,
       isnull(MAX(fg8_occfcstcurrent), 0.0) as fg8_occfcstcurrent,
       isnull(MAX(fg8_occfcstpickup), 0.0) as fg8_occfcstpickup,
       isnull(MAX(fg9_occfcstcurrent), 0.0) as fg9_occfcstcurrent,
       isnull(MAX(fg9_occfcstpickup), 0.0) as fg9_occfcstpickup,
       isnull(MAX(fg10_occfcstcurrent), 0.0) as fg10_occfcstcurrent,
       isnull(MAX(fg10_occfcstpickup), 0.0) as fg10_occfcstpickup,
       isnull(MAX(fg11_occfcstcurrent), 0.0) as fg11_occfcstcurrent,
       isnull(MAX(fg11_occfcstpickup), 0.0) as fg11_occfcstpickup,
       isnull(MAX(fg12_occfcstcurrent), 0.0) as fg12_occfcstcurrent,
       isnull(MAX(fg12_occfcstpickup), 0.0) as fg12_occfcstpickup,
       isnull(MAX(fg13_occfcstcurrent), 0.0) as fg13_occfcstcurrent,
       isnull(MAX(fg13_occfcstpickup), 0.0) as fg13_occfcstpickup,
       isnull(MAX(fg14_occfcstcurrent), 0.0) as fg14_occfcstcurrent,
       isnull(MAX(fg14_occfcstpickup), 0.0) as fg14_occfcstpickup,
       isnull(MAX(fg15_occfcstcurrent), 0.0) as fg15_occfcstcurrent,
       isnull(MAX(fg15_occfcstpickup), 0.0) as fg15_occfcstpickup,
       isnull(MAX(fg16_occfcstcurrent), 0.0) as fg16_occfcstcurrent,
       isnull(MAX(fg16_occfcstpickup), 0.0) as fg16_occfcstpickup,
       isnull(MAX(fg17_occfcstcurrent), 0.0) as fg17_occfcstcurrent,
       isnull(MAX(fg17_occfcstpickup), 0.0) as fg17_occfcstpickup,
       isnull(MAX(fg18_occfcstcurrent), 0.0) as fg18_occfcstcurrent,
       isnull(MAX(fg18_occfcstpickup), 0.0) as fg18_occfcstpickup,
       isnull(MAX(fg19_occfcstcurrent), 0.0) as fg19_occfcstcurrent,
       isnull(MAX(fg19_occfcstpickup), 0.0) as fg19_occfcstpickup,
       isnull(MAX(fg20_occfcstcurrent), 0.0) as fg20_occfcstcurrent,
       isnull(MAX(fg20_occfcstpickup), 0.0) as fg20_occfcstpickup,
       isnull(MAX(fg21_occfcstcurrent), 0.0) as fg21_occfcstcurrent,
       isnull(MAX(fg21_occfcstpickup), 0.0) as fg21_occfcstpickup,
       isnull(MAX(fg22_occfcstcurrent), 0.0) as fg22_occfcstcurrent,
       isnull(MAX(fg22_occfcstpickup), 0.0) as fg22_occfcstpickup,
       isnull(MAX(fg23_occfcstcurrent), 0.0) as fg23_occfcstcurrent,
       isnull(MAX(fg23_occfcstpickup), 0.0) as fg23_occfcstpickup,
       isnull(MAX(fg24_occfcstcurrent), 0.0) as fg24_occfcstcurrent,
       isnull(MAX(fg24_occfcstpickup), 0.0) as fg24_occfcstpickup,
       isnull(MAX(fg25_occfcstcurrent), 0.0) as fg25_occfcstcurrent,
       isnull(MAX(fg25_occfcstpickup), 0.0) as fg25_occfcstpickup,
       isnull(MAX(fg26_occfcstcurrent), 0.0) as fg26_occfcstcurrent,
       isnull(MAX(fg26_occfcstpickup), 0.0) as fg26_occfcstpickup,
       isnull(MAX(fg27_occfcstcurrent), 0.0) as fg27_occfcstcurrent,
       isnull(MAX(fg27_occfcstpickup), 0.0) as fg27_occfcstpickup,
       isnull(MAX(fg28_occfcstcurrent), 0.0) as fg28_occfcstcurrent,
       isnull(MAX(fg28_occfcstpickup), 0.0) as fg28_occfcstpickup,
       isnull(MAX(fg29_occfcstcurrent), 0.0) as fg29_occfcstcurrent,
       isnull(MAX(fg29_occfcstpickup), 0.0) as fg29_occfcstpickup,
       isnull(MAX(fg30_occfcstcurrent), 0.0) as fg30_occfcstcurrent,
       isnull(MAX(fg30_occfcstpickup), 0.0) as fg30_occfcstpickup,
       isnull(MAX(fg31_occfcstcurrent), 0.0) as fg31_occfcstcurrent,
       isnull(MAX(fg31_occfcstpickup), 0.0) as fg31_occfcstpickup,
       isnull(MAX(fg32_occfcstcurrent), 0.0) as fg32_occfcstcurrent,
       isnull(MAX(fg32_occfcstpickup), 0.0) as fg32_occfcstpickup,
       isnull(MAX(fg33_occfcstcurrent), 0.0) as fg33_occfcstcurrent,
       isnull(MAX(fg33_occfcstpickup), 0.0) as fg33_occfcstpickup,
       isnull(MAX(fg34_occfcstcurrent), 0.0) as fg34_occfcstcurrent,
       isnull(MAX(fg34_occfcstpickup), 0.0) as fg34_occfcstpickup,
       isnull(MAX(fg35_occfcstcurrent), 0.0) as fg35_occfcstcurrent,
       isnull(MAX(fg35_occfcstpickup), 0.0) as fg35_occfcstpickup,
       isnull(MAX(fg36_occfcstcurrent), 0.0) as fg36_occfcstcurrent,
       isnull(MAX(fg36_occfcstpickup), 0.0) as fg36_occfcstpickup,
       isnull(MAX(fg37_occfcstcurrent), 0.0) as fg37_occfcstcurrent,
       isnull(MAX(fg37_occfcstpickup), 0.0) as fg37_occfcstpickup,
       isnull(MAX(fg38_occfcstcurrent), 0.0) as fg38_occfcstcurrent,
       isnull(MAX(fg38_occfcstpickup), 0.0) as fg38_occfcstpickup,
       isnull(MAX(fg39_occfcstcurrent), 0.0) as fg39_occfcstcurrent,
       isnull(MAX(fg39_occfcstpickup), 0.0) as fg39_occfcstpickup,
       isnull(MAX(fg40_occfcstcurrent), 0.0) as fg40_occfcstcurrent,
       isnull(MAX(fg40_occfcstpickup), 0.0) as fg40_occfcstpickup,
       isnull(MAX(fg41_occfcstcurrent), 0.0) as fg41_occfcstcurrent,
       isnull(MAX(fg41_occfcstpickup), 0.0) as fg41_occfcstpickup,
       isnull(MAX(fg42_occfcstcurrent), 0.0) as fg42_occfcstcurrent,
       isnull(MAX(fg42_occfcstpickup), 0.0) as fg42_occfcstpickup,
       isnull(MAX(fg43_occfcstcurrent), 0.0) as fg43_occfcstcurrent,
       isnull(MAX(fg43_occfcstpickup), 0.0) as fg43_occfcstpickup,
       isnull(MAX(fg44_occfcstcurrent), 0.0) as fg44_occfcstcurrent,
       isnull(MAX(fg44_occfcstpickup), 0.0) as fg44_occfcstpickup,
       isnull(MAX(fg45_occfcstcurrent), 0.0) as fg45_occfcstcurrent,
       isnull(MAX(fg45_occfcstpickup), 0.0) as fg45_occfcstpickup,
       isnull(MAX(fg46_occfcstcurrent), 0.0) as fg46_occfcstcurrent,
       isnull(MAX(fg46_occfcstpickup), 0.0) as fg46_occfcstpickup,
       isnull(MAX(fg47_occfcstcurrent), 0.0) as fg47_occfcstcurrent,
       isnull(MAX(fg47_occfcstpickup), 0.0) as fg47_occfcstpickup,
       isnull(MAX(fg48_occfcstcurrent), 0.0) as fg48_occfcstcurrent,
       isnull(MAX(fg48_occfcstpickup), 0.0) as fg48_occfcstpickup,
       isnull(MAX(fg49_occfcstcurrent), 0.0) as fg49_occfcstcurrent,
       isnull(MAX(fg49_occfcstpickup), 0.0) as fg49_occfcstpickup,
       isnull(MAX(fg50_occfcstcurrent), 0.0) as fg50_occfcstcurrent,
       isnull(MAX(fg50_occfcstpickup), 0.0) as fg50_occfcstpickup,
       isnull(MAX(fg1_bookedroomrevenuecurrent), 0.0) as fg1_bookedroomrevenuecurrent,
       isnull(MAX(fg1_bookedroomrevenuepickup), 0.0) as fg1_bookedroomrevenuepickup,
       isnull(MAX(fg2_bookedroomrevenuecurrent), 0.0) as fg2_bookedroomrevenuecurrent,
       isnull(MAX(fg2_bookedroomrevenuepickup), 0.0) as fg2_bookedroomrevenuepickup,
       isnull(MAX(fg3_bookedroomrevenuecurrent), 0.0) as fg3_bookedroomrevenuecurrent,
       isnull(MAX(fg3_bookedroomrevenuepickup), 0.0) as fg3_bookedroomrevenuepickup,
       isnull(MAX(fg4_bookedroomrevenuecurrent), 0.0) as fg4_bookedroomrevenuecurrent,
       isnull(MAX(fg4_bookedroomrevenuepickup), 0.0) as fg4_bookedroomrevenuepickup,
       isnull(MAX(fg5_bookedroomrevenuecurrent), 0.0) as fg5_bookedroomrevenuecurrent,
       isnull(MAX(fg5_bookedroomrevenuepickup), 0.0) as fg5_bookedroomrevenuepickup,
       isnull(MAX(fg6_bookedroomrevenuecurrent), 0.0) as fg6_bookedroomrevenuecurrent,
       isnull(MAX(fg6_bookedroomrevenuepickup), 0.0) as fg6_bookedroomrevenuepickup,
       isnull(MAX(fg7_bookedroomrevenuecurrent), 0.0) as fg7_bookedroomrevenuecurrent,
       isnull(MAX(fg7_bookedroomrevenuepickup), 0.0) as fg7_bookedroomrevenuepickup,
       isnull(MAX(fg8_bookedroomrevenuecurrent), 0.0) as fg8_bookedroomrevenuecurrent,
       isnull(MAX(fg8_bookedroomrevenuepickup), 0.0) as fg8_bookedroomrevenuepickup,
       isnull(MAX(fg9_bookedroomrevenuecurrent), 0.0) as fg9_bookedroomrevenuecurrent,
       isnull(MAX(fg9_bookedroomrevenuepickup), 0.0) as fg9_bookedroomrevenuepickup,
       isnull(MAX(fg10_bookedroomrevenuecurrent), 0.0) as fg10_bookedroomrevenuecurrent,
       isnull(MAX(fg10_bookedroomrevenuepickup), 0.0) as fg10_bookedroomrevenuepickup,
       isnull(MAX(fg11_bookedroomrevenuecurrent), 0.0) as fg11_bookedroomrevenuecurrent,
       isnull(MAX(fg11_bookedroomrevenuepickup), 0.0) as fg11_bookedroomrevenuepickup,
       isnull(MAX(fg12_bookedroomrevenuecurrent), 0.0) as fg12_bookedroomrevenuecurrent,
       isnull(MAX(fg12_bookedroomrevenuepickup), 0.0) as fg12_bookedroomrevenuepickup,
       isnull(MAX(fg13_bookedroomrevenuecurrent), 0.0) as fg13_bookedroomrevenuecurrent,
       isnull(MAX(fg13_bookedroomrevenuepickup), 0.0) as fg13_bookedroomrevenuepickup,
       isnull(MAX(fg14_bookedroomrevenuecurrent), 0.0) as fg14_bookedroomrevenuecurrent,
       isnull(MAX(fg14_bookedroomrevenuepickup), 0.0) as fg14_bookedroomrevenuepickup,
       isnull(MAX(fg15_bookedroomrevenuecurrent), 0.0) as fg15_bookedroomrevenuecurrent,
       isnull(MAX(fg15_bookedroomrevenuepickup), 0.0) as fg15_bookedroomrevenuepickup,
       isnull(MAX(fg16_bookedroomrevenuecurrent), 0.0) as fg16_bookedroomrevenuecurrent,
       isnull(MAX(fg16_bookedroomrevenuepickup), 0.0) as fg16_bookedroomrevenuepickup,
       isnull(MAX(fg17_bookedroomrevenuecurrent), 0.0) as fg17_bookedroomrevenuecurrent,
       isnull(MAX(fg17_bookedroomrevenuepickup), 0.0) as fg17_bookedroomrevenuepickup,
       isnull(MAX(fg18_bookedroomrevenuecurrent), 0.0) as fg18_bookedroomrevenuecurrent,
       isnull(MAX(fg18_bookedroomrevenuepickup), 0.0) as fg18_bookedroomrevenuepickup,
       isnull(MAX(fg19_bookedroomrevenuecurrent), 0.0) as fg19_bookedroomrevenuecurrent,
       isnull(MAX(fg19_bookedroomrevenuepickup), 0.0) as fg19_bookedroomrevenuepickup,
       isnull(MAX(fg20_bookedroomrevenuecurrent), 0.0) as fg20_bookedroomrevenuecurrent,
       isnull(MAX(fg20_bookedroomrevenuepickup), 0.0) as fg20_bookedroomrevenuepickup,
       isnull(MAX(fg21_bookedroomrevenuecurrent), 0.0) as fg21_bookedroomrevenuecurrent,
       isnull(MAX(fg21_bookedroomrevenuepickup), 0.0) as fg21_bookedroomrevenuepickup,
       isnull(MAX(fg22_bookedroomrevenuecurrent), 0.0) as fg22_bookedroomrevenuecurrent,
       isnull(MAX(fg22_bookedroomrevenuepickup), 0.0) as fg22_bookedroomrevenuepickup,
       isnull(MAX(fg23_bookedroomrevenuecurrent), 0.0) as fg23_bookedroomrevenuecurrent,
       isnull(MAX(fg23_bookedroomrevenuepickup), 0.0) as fg23_bookedroomrevenuepickup,
       isnull(MAX(fg24_bookedroomrevenuecurrent), 0.0) as fg24_bookedroomrevenuecurrent,
       isnull(MAX(fg24_bookedroomrevenuepickup), 0.0) as fg24_bookedroomrevenuepickup,
       isnull(MAX(fg25_bookedroomrevenuecurrent), 0.0) as fg25_bookedroomrevenuecurrent,
       isnull(MAX(fg25_bookedroomrevenuepickup), 0.0) as fg25_bookedroomrevenuepickup,
       isnull(MAX(fg26_bookedroomrevenuecurrent), 0.0) as fg26_bookedroomrevenuecurrent,
       isnull(MAX(fg26_bookedroomrevenuepickup), 0.0) as fg26_bookedroomrevenuepickup,
       isnull(MAX(fg27_bookedroomrevenuecurrent), 0.0) as fg27_bookedroomrevenuecurrent,
       isnull(MAX(fg27_bookedroomrevenuepickup), 0.0) as fg27_bookedroomrevenuepickup,
       isnull(MAX(fg28_bookedroomrevenuecurrent), 0.0) as fg28_bookedroomrevenuecurrent,
       isnull(MAX(fg28_bookedroomrevenuepickup), 0.0) as fg28_bookedroomrevenuepickup,
       isnull(MAX(fg29_bookedroomrevenuecurrent), 0.0) as fg29_bookedroomrevenuecurrent,
       isnull(MAX(fg29_bookedroomrevenuepickup), 0.0) as fg29_bookedroomrevenuepickup,
       isnull(MAX(fg30_bookedroomrevenuecurrent), 0.0) as fg30_bookedroomrevenuecurrent,
       isnull(MAX(fg30_bookedroomrevenuepickup), 0.0) as fg30_bookedroomrevenuepickup,
       isnull(MAX(fg31_bookedroomrevenuecurrent), 0.0) as fg31_bookedroomrevenuecurrent,
       isnull(MAX(fg31_bookedroomrevenuepickup), 0.0) as fg31_bookedroomrevenuepickup,
       isnull(MAX(fg32_bookedroomrevenuecurrent), 0.0) as fg32_bookedroomrevenuecurrent,
       isnull(MAX(fg32_bookedroomrevenuepickup), 0.0) as fg32_bookedroomrevenuepickup,
       isnull(MAX(fg33_bookedroomrevenuecurrent), 0.0) as fg33_bookedroomrevenuecurrent,
       isnull(MAX(fg33_bookedroomrevenuepickup), 0.0) as fg33_bookedroomrevenuepickup,
       isnull(MAX(fg34_bookedroomrevenuecurrent), 0.0) as fg34_bookedroomrevenuecurrent,
       isnull(MAX(fg34_bookedroomrevenuepickup), 0.0) as fg34_bookedroomrevenuepickup,
       isnull(MAX(fg35_bookedroomrevenuecurrent), 0.0) as fg35_bookedroomrevenuecurrent,
       isnull(MAX(fg35_bookedroomrevenuepickup), 0.0) as fg35_bookedroomrevenuepickup,
       isnull(MAX(fg36_bookedroomrevenuecurrent), 0.0) as fg36_bookedroomrevenuecurrent,
       isnull(MAX(fg36_bookedroomrevenuepickup), 0.0) as fg36_bookedroomrevenuepickup,
       isnull(MAX(fg37_bookedroomrevenuecurrent), 0.0) as fg37_bookedroomrevenuecurrent,
       isnull(MAX(fg37_bookedroomrevenuepickup), 0.0) as fg37_bookedroomrevenuepickup,
       isnull(MAX(fg38_bookedroomrevenuecurrent), 0.0) as fg38_bookedroomrevenuecurrent,
       isnull(MAX(fg38_bookedroomrevenuepickup), 0.0) as fg38_bookedroomrevenuepickup,
       isnull(MAX(fg39_bookedroomrevenuecurrent), 0.0) as fg39_bookedroomrevenuecurrent,
       isnull(MAX(fg39_bookedroomrevenuepickup), 0.0) as fg39_bookedroomrevenuepickup,
       isnull(MAX(fg40_bookedroomrevenuecurrent), 0.0) as fg40_bookedroomrevenuecurrent,
       isnull(MAX(fg40_bookedroomrevenuepickup), 0.0) as fg40_bookedroomrevenuepickup,
       isnull(MAX(fg41_bookedroomrevenuecurrent), 0.0) as fg41_bookedroomrevenuecurrent,
       isnull(MAX(fg41_bookedroomrevenuepickup), 0.0) as fg41_bookedroomrevenuepickup,
       isnull(MAX(fg42_bookedroomrevenuecurrent), 0.0) as fg42_bookedroomrevenuecurrent,
       isnull(MAX(fg42_bookedroomrevenuepickup), 0.0) as fg42_bookedroomrevenuepickup,
       isnull(MAX(fg43_bookedroomrevenuecurrent), 0.0) as fg43_bookedroomrevenuecurrent,
       isnull(MAX(fg43_bookedroomrevenuepickup), 0.0) as fg43_bookedroomrevenuepickup,
       isnull(MAX(fg44_bookedroomrevenuecurrent), 0.0) as fg44_bookedroomrevenuecurrent,
       isnull(MAX(fg44_bookedroomrevenuepickup), 0.0) as fg44_bookedroomrevenuepickup,
       isnull(MAX(fg45_bookedroomrevenuecurrent), 0.0) as fg45_bookedroomrevenuecurrent,
       isnull(MAX(fg45_bookedroomrevenuepickup), 0.0) as fg45_bookedroomrevenuepickup,
       isnull(MAX(fg46_bookedroomrevenuecurrent), 0.0) as fg46_bookedroomrevenuecurrent,
       isnull(MAX(fg46_bookedroomrevenuepickup), 0.0) as fg46_bookedroomrevenuepickup,
       isnull(MAX(fg47_bookedroomrevenuecurrent), 0.0) as fg47_bookedroomrevenuecurrent,
       isnull(MAX(fg47_bookedroomrevenuepickup), 0.0) as fg47_bookedroomrevenuepickup,
       isnull(MAX(fg48_bookedroomrevenuecurrent), 0.0) as fg48_bookedroomrevenuecurrent,
       isnull(MAX(fg48_bookedroomrevenuepickup), 0.0) as fg48_bookedroomrevenuepickup,
       isnull(MAX(fg49_bookedroomrevenuecurrent), 0.0) as fg49_bookedroomrevenuecurrent,
       isnull(MAX(fg49_bookedroomrevenuepickup), 0.0) as fg49_bookedroomrevenuepickup,
       isnull(MAX(fg50_bookedroomrevenuecurrent), 0.0) as fg50_bookedroomrevenuecurrent,
       isnull(MAX(fg50_bookedroomrevenuepickup), 0.0) as fg50_bookedroomrevenuepickup,
       isnull(MAX(fg1_fcstedroomrevenuecurrent), 0.0) as fg1_fcstedroomrevenuecurrent,
       isnull(MAX(fg1_fcstedroomrevenuepickup), 0.0) as fg1_fcstedroomrevenuepickup,
       isnull(MAX(fg2_fcstedroomrevenuecurrent), 0.0) as fg2_fcstedroomrevenuecurrent,
       isnull(MAX(fg2_fcstedroomrevenuepickup), 0.0) as fg2_fcstedroomrevenuepickup,
       isnull(MAX(fg3_fcstedroomrevenuecurrent), 0.0) as fg3_fcstedroomrevenuecurrent,
       isnull(MAX(fg3_fcstedroomrevenuepickup), 0.0) as fg3_fcstedroomrevenuepickup,
       isnull(MAX(fg4_fcstedroomrevenuecurrent), 0.0) as fg4_fcstedroomrevenuecurrent,
       isnull(MAX(fg4_fcstedroomrevenuepickup), 0.0) as fg4_fcstedroomrevenuepickup,
       isnull(MAX(fg5_fcstedroomrevenuecurrent), 0.0) as fg5_fcstedroomrevenuecurrent,
       isnull(MAX(fg5_fcstedroomrevenuepickup), 0.0) as fg5_fcstedroomrevenuepickup,
       isnull(MAX(fg6_fcstedroomrevenuecurrent), 0.0) as fg6_fcstedroomrevenuecurrent,
       isnull(MAX(fg6_fcstedroomrevenuepickup), 0.0) as fg6_fcstedroomrevenuepickup,
       isnull(MAX(fg7_fcstedroomrevenuecurrent), 0.0) as fg7_fcstedroomrevenuecurrent,
       isnull(MAX(fg7_fcstedroomrevenuepickup), 0.0) as fg7_fcstedroomrevenuepickup,
       isnull(MAX(fg8_fcstedroomrevenuecurrent), 0.0) as fg8_fcstedroomrevenuecurrent,
       isnull(MAX(fg8_fcstedroomrevenuepickup), 0.0) as fg8_fcstedroomrevenuepickup,
       isnull(MAX(fg9_fcstedroomrevenuecurrent), 0.0) as fg9_fcstedroomrevenuecurrent,
       isnull(MAX(fg9_fcstedroomrevenuepickup), 0.0) as fg9_fcstedroomrevenuepickup,
       isnull(MAX(fg10_fcstedroomrevenuecurrent), 0.0) as fg10_fcstedroomrevenuecurrent,
       isnull(MAX(fg10_fcstedroomrevenuepickup), 0.0) as fg10_fcstedroomrevenuepickup,
       isnull(MAX(fg11_fcstedroomrevenuecurrent), 0.0) as fg11_fcstedroomrevenuecurrent,
       isnull(MAX(fg11_fcstedroomrevenuepickup), 0.0) as fg11_fcstedroomrevenuepickup,
       isnull(MAX(fg12_fcstedroomrevenuecurrent), 0.0) as fg12_fcstedroomrevenuecurrent,
       isnull(MAX(fg12_fcstedroomrevenuepickup), 0.0) as fg12_fcstedroomrevenuepickup,
       isnull(MAX(fg13_fcstedroomrevenuecurrent), 0.0) as fg13_fcstedroomrevenuecurrent,
       isnull(MAX(fg13_fcstedroomrevenuepickup), 0.0) as fg13_fcstedroomrevenuepickup,
       isnull(MAX(fg14_fcstedroomrevenuecurrent), 0.0) as fg14_fcstedroomrevenuecurrent,
       isnull(MAX(fg14_fcstedroomrevenuepickup), 0.0) as fg14_fcstedroomrevenuepickup,
       isnull(MAX(fg15_fcstedroomrevenuecurrent), 0.0) as fg15_fcstedroomrevenuecurrent,
       isnull(MAX(fg15_fcstedroomrevenuepickup), 0.0) as fg15_fcstedroomrevenuepickup,
       isnull(MAX(fg16_fcstedroomrevenuecurrent), 0.0) as fg16_fcstedroomrevenuecurrent,
       isnull(MAX(fg16_fcstedroomrevenuepickup), 0.0) as fg16_fcstedroomrevenuepickup,
       isnull(MAX(fg17_fcstedroomrevenuecurrent), 0.0) as fg17_fcstedroomrevenuecurrent,
       isnull(MAX(fg17_fcstedroomrevenuepickup), 0.0) as fg17_fcstedroomrevenuepickup,
       isnull(MAX(fg18_fcstedroomrevenuecurrent), 0.0) as fg18_fcstedroomrevenuecurrent,
       isnull(MAX(fg18_fcstedroomrevenuepickup), 0.0) as fg18_fcstedroomrevenuepickup,
       isnull(MAX(fg19_fcstedroomrevenuecurrent), 0.0) as fg19_fcstedroomrevenuecurrent,
       isnull(MAX(fg19_fcstedroomrevenuepickup), 0.0) as fg19_fcstedroomrevenuepickup,
       isnull(MAX(fg20_fcstedroomrevenuecurrent), 0.0) as fg20_fcstedroomrevenuecurrent,
       isnull(MAX(fg20_fcstedroomrevenuepickup), 0.0) as fg20_fcstedroomrevenuepickup,
       isnull(MAX(fg21_fcstedroomrevenuecurrent), 0.0) as fg21_fcstedroomrevenuecurrent,
       isnull(MAX(fg21_fcstedroomrevenuepickup), 0.0) as fg21_fcstedroomrevenuepickup,
       isnull(MAX(fg22_fcstedroomrevenuecurrent), 0.0) as fg22_fcstedroomrevenuecurrent,
       isnull(MAX(fg22_fcstedroomrevenuepickup), 0.0) as fg22_fcstedroomrevenuepickup,
       isnull(MAX(fg23_fcstedroomrevenuecurrent), 0.0) as fg23_fcstedroomrevenuecurrent,
       isnull(MAX(fg23_fcstedroomrevenuepickup), 0.0) as fg23_fcstedroomrevenuepickup,
       isnull(MAX(fg24_fcstedroomrevenuecurrent), 0.0) as fg24_fcstedroomrevenuecurrent,
       isnull(MAX(fg24_fcstedroomrevenuepickup), 0.0) as fg24_fcstedroomrevenuepickup,
       isnull(MAX(fg25_fcstedroomrevenuecurrent), 0.0) as fg25_fcstedroomrevenuecurrent,
       isnull(MAX(fg25_fcstedroomrevenuepickup), 0.0) as fg25_fcstedroomrevenuepickup,
       isnull(MAX(fg26_fcstedroomrevenuecurrent), 0.0) as fg26_fcstedroomrevenuecurrent,
       isnull(MAX(fg26_fcstedroomrevenuepickup), 0.0) as fg26_fcstedroomrevenuepickup,
       isnull(MAX(fg27_fcstedroomrevenuecurrent), 0.0) as fg27_fcstedroomrevenuecurrent,
       isnull(MAX(fg27_fcstedroomrevenuepickup), 0.0) as fg27_fcstedroomrevenuepickup,
       isnull(MAX(fg28_fcstedroomrevenuecurrent), 0.0) as fg28_fcstedroomrevenuecurrent,
       isnull(MAX(fg28_fcstedroomrevenuepickup), 0.0) as fg28_fcstedroomrevenuepickup,
       isnull(MAX(fg29_fcstedroomrevenuecurrent), 0.0) as fg29_fcstedroomrevenuecurrent,
       isnull(MAX(fg29_fcstedroomrevenuepickup), 0.0) as fg29_fcstedroomrevenuepickup,
       isnull(MAX(fg30_fcstedroomrevenuecurrent), 0.0) as fg30_fcstedroomrevenuecurrent,
       isnull(MAX(fg30_fcstedroomrevenuepickup), 0.0) as fg30_fcstedroomrevenuepickup,
       isnull(MAX(fg31_fcstedroomrevenuecurrent), 0.0) as fg31_fcstedroomrevenuecurrent,
       isnull(MAX(fg31_fcstedroomrevenuepickup), 0.0) as fg31_fcstedroomrevenuepickup,
       isnull(MAX(fg32_fcstedroomrevenuecurrent), 0.0) as fg32_fcstedroomrevenuecurrent,
       isnull(MAX(fg32_fcstedroomrevenuepickup), 0.0) as fg32_fcstedroomrevenuepickup,
       isnull(MAX(fg33_fcstedroomrevenuecurrent), 0.0) as fg33_fcstedroomrevenuecurrent,
       isnull(MAX(fg33_fcstedroomrevenuepickup), 0.0) as fg33_fcstedroomrevenuepickup,
       isnull(MAX(fg34_fcstedroomrevenuecurrent), 0.0) as fg34_fcstedroomrevenuecurrent,
       isnull(MAX(fg34_fcstedroomrevenuepickup), 0.0) as fg34_fcstedroomrevenuepickup,
       isnull(MAX(fg35_fcstedroomrevenuecurrent), 0.0) as fg35_fcstedroomrevenuecurrent,
       isnull(MAX(fg35_fcstedroomrevenuepickup), 0.0) as fg35_fcstedroomrevenuepickup,
       isnull(MAX(fg36_fcstedroomrevenuecurrent), 0.0) as fg36_fcstedroomrevenuecurrent,
       isnull(MAX(fg36_fcstedroomrevenuepickup), 0.0) as fg36_fcstedroomrevenuepickup,
       isnull(MAX(fg37_fcstedroomrevenuecurrent), 0.0) as fg37_fcstedroomrevenuecurrent,
       isnull(MAX(fg37_fcstedroomrevenuepickup), 0.0) as fg37_fcstedroomrevenuepickup,
       isnull(MAX(fg38_fcstedroomrevenuecurrent), 0.0) as fg38_fcstedroomrevenuecurrent,
       isnull(MAX(fg38_fcstedroomrevenuepickup), 0.0) as fg38_fcstedroomrevenuepickup,
       isnull(MAX(fg39_fcstedroomrevenuecurrent), 0.0) as fg39_fcstedroomrevenuecurrent,
       isnull(MAX(fg39_fcstedroomrevenuepickup), 0.0) as fg39_fcstedroomrevenuepickup,
       isnull(MAX(fg40_fcstedroomrevenuecurrent), 0.0) as fg40_fcstedroomrevenuecurrent,
       isnull(MAX(fg40_fcstedroomrevenuepickup), 0.0) as fg40_fcstedroomrevenuepickup,
       isnull(MAX(fg41_fcstedroomrevenuecurrent), 0.0) as fg41_fcstedroomrevenuecurrent,
       isnull(MAX(fg41_fcstedroomrevenuepickup), 0.0) as fg41_fcstedroomrevenuepickup,
       isnull(MAX(fg42_fcstedroomrevenuecurrent), 0.0) as fg42_fcstedroomrevenuecurrent,
       isnull(MAX(fg42_fcstedroomrevenuepickup), 0.0) as fg42_fcstedroomrevenuepickup,
       isnull(MAX(fg43_fcstedroomrevenuecurrent), 0.0) as fg43_fcstedroomrevenuecurrent,
       isnull(MAX(fg43_fcstedroomrevenuepickup), 0.0) as fg43_fcstedroomrevenuepickup,
       isnull(MAX(fg44_fcstedroomrevenuecurrent), 0.0) as fg44_fcstedroomrevenuecurrent,
       isnull(MAX(fg44_fcstedroomrevenuepickup), 0.0) as fg44_fcstedroomrevenuepickup,
       isnull(MAX(fg45_fcstedroomrevenuecurrent), 0.0) as fg45_fcstedroomrevenuecurrent,
       isnull(MAX(fg45_fcstedroomrevenuepickup), 0.0) as fg45_fcstedroomrevenuepickup,
       isnull(MAX(fg46_fcstedroomrevenuecurrent), 0.0) as fg46_fcstedroomrevenuecurrent,
       isnull(MAX(fg46_fcstedroomrevenuepickup), 0.0) as fg46_fcstedroomrevenuepickup,
       isnull(MAX(fg47_fcstedroomrevenuecurrent), 0.0) as fg47_fcstedroomrevenuecurrent,
       isnull(MAX(fg47_fcstedroomrevenuepickup), 0.0) as fg47_fcstedroomrevenuepickup,
       isnull(MAX(fg48_fcstedroomrevenuecurrent), 0.0) as fg48_fcstedroomrevenuecurrent,
       isnull(MAX(fg48_fcstedroomrevenuepickup), 0.0) as fg48_fcstedroomrevenuepickup,
       isnull(MAX(fg49_fcstedroomrevenuecurrent), 0.0) as fg49_fcstedroomrevenuecurrent,
       isnull(MAX(fg49_fcstedroomrevenuepickup), 0.0) as fg49_fcstedroomrevenuepickup,
       isnull(MAX(fg50_fcstedroomrevenuecurrent), 0.0) as fg50_fcstedroomrevenuecurrent,
       isnull(MAX(fg50_fcstedroomrevenuepickup), 0.0) as fg50_fcstedroomrevenuepickup,
       isnull(MAX(fg1_bookedadrcurrent), 0.0) as fg1_bookedadrcurrent,
       isnull(MAX(fg1_bookedadrpickup), 0.0) as fg1_bookedadrpickup,
       isnull(MAX(fg2_bookedadrcurrent), 0.0) as fg2_bookedadrcurrent,
       isnull(MAX(fg2_bookedadrpickup), 0.0) as fg2_bookedadrpickup,
       isnull(MAX(fg3_bookedadrcurrent), 0.0) as fg3_bookedadrcurrent,
       isnull(MAX(fg3_bookedadrpickup), 0.0) as fg3_bookedadrpickup,
       isnull(MAX(fg4_bookedadrcurrent), 0.0) as fg4_bookedadrcurrent,
       isnull(MAX(fg4_bookedadrpickup), 0.0) as fg4_bookedadrpickup,
       isnull(MAX(fg5_bookedadrcurrent), 0.0) as fg5_bookedadrcurrent,
       isnull(MAX(fg5_bookedadrpickup), 0.0) as fg5_bookedadrpickup,
       isnull(MAX(fg6_bookedadrcurrent), 0.0) as fg6_bookedadrcurrent,
       isnull(MAX(fg6_bookedadrpickup), 0.0) as fg6_bookedadrpickup,
       isnull(MAX(fg7_bookedadrcurrent), 0.0) as fg7_bookedadrcurrent,
       isnull(MAX(fg7_bookedadrpickup), 0.0) as fg7_bookedadrpickup,
       isnull(MAX(fg8_bookedadrcurrent), 0.0) as fg8_bookedadrcurrent,
       isnull(MAX(fg8_bookedadrpickup), 0.0) as fg8_bookedadrpickup,
       isnull(MAX(fg9_bookedadrcurrent), 0.0) as fg9_bookedadrcurrent,
       isnull(MAX(fg9_bookedadrpickup), 0.0) as fg9_bookedadrpickup,
       isnull(MAX(fg10_bookedadrcurrent), 0.0) as fg10_bookedadrcurrent,
       isnull(MAX(fg10_bookedadrpickup), 0.0) as fg10_bookedadrpickup,
       isnull(MAX(fg11_bookedadrcurrent), 0.0) as fg11_bookedadrcurrent,
       isnull(MAX(fg11_bookedadrpickup), 0.0) as fg11_bookedadrpickup,
       isnull(MAX(fg12_bookedadrcurrent), 0.0) as fg12_bookedadrcurrent,
       isnull(MAX(fg12_bookedadrpickup), 0.0) as fg12_bookedadrpickup,
       isnull(MAX(fg13_bookedadrcurrent), 0.0) as fg13_bookedadrcurrent,
       isnull(MAX(fg13_bookedadrpickup), 0.0) as fg13_bookedadrpickup,
       isnull(MAX(fg14_bookedadrcurrent), 0.0) as fg14_bookedadrcurrent,
       isnull(MAX(fg14_bookedadrpickup), 0.0) as fg14_bookedadrpickup,
       isnull(MAX(fg15_bookedadrcurrent), 0.0) as fg15_bookedadrcurrent,
       isnull(MAX(fg15_bookedadrpickup), 0.0) as fg15_bookedadrpickup,
       isnull(MAX(fg16_bookedadrcurrent), 0.0) as fg16_bookedadrcurrent,
       isnull(MAX(fg16_bookedadrpickup), 0.0) as fg16_bookedadrpickup,
       isnull(MAX(fg17_bookedadrcurrent), 0.0) as fg17_bookedadrcurrent,
       isnull(MAX(fg17_bookedadrpickup), 0.0) as fg17_bookedadrpickup,
       isnull(MAX(fg18_bookedadrcurrent), 0.0) as fg18_bookedadrcurrent,
       isnull(MAX(fg18_bookedadrpickup), 0.0) as fg18_bookedadrpickup,
       isnull(MAX(fg19_bookedadrcurrent), 0.0) as fg19_bookedadrcurrent,
       isnull(MAX(fg19_bookedadrpickup), 0.0) as fg19_bookedadrpickup,
       isnull(MAX(fg20_bookedadrcurrent), 0.0) as fg20_bookedadrcurrent,
       isnull(MAX(fg20_bookedadrpickup), 0.0) as fg20_bookedadrpickup,
       isnull(MAX(fg21_bookedadrcurrent), 0.0) as fg21_bookedadrcurrent,
       isnull(MAX(fg21_bookedadrpickup), 0.0) as fg21_bookedadrpickup,
       isnull(MAX(fg22_bookedadrcurrent), 0.0) as fg22_bookedadrcurrent,
       isnull(MAX(fg22_bookedadrpickup), 0.0) as fg22_bookedadrpickup,
       isnull(MAX(fg23_bookedadrcurrent), 0.0) as fg23_bookedadrcurrent,
       isnull(MAX(fg23_bookedadrpickup), 0.0) as fg23_bookedadrpickup,
       isnull(MAX(fg24_bookedadrcurrent), 0.0) as fg24_bookedadrcurrent,
       isnull(MAX(fg24_bookedadrpickup), 0.0) as fg24_bookedadrpickup,
       isnull(MAX(fg25_bookedadrcurrent), 0.0) as fg25_bookedadrcurrent,
       isnull(MAX(fg25_bookedadrpickup), 0.0) as fg25_bookedadrpickup,
       isnull(MAX(fg26_bookedadrcurrent), 0.0) as fg26_bookedadrcurrent,
       isnull(MAX(fg26_bookedadrpickup), 0.0) as fg26_bookedadrpickup,
       isnull(MAX(fg27_bookedadrcurrent), 0.0) as fg27_bookedadrcurrent,
       isnull(MAX(fg27_bookedadrpickup), 0.0) as fg27_bookedadrpickup,
       isnull(MAX(fg28_bookedadrcurrent), 0.0) as fg28_bookedadrcurrent,
       isnull(MAX(fg28_bookedadrpickup), 0.0) as fg28_bookedadrpickup,
       isnull(MAX(fg29_bookedadrcurrent), 0.0) as fg29_bookedadrcurrent,
       isnull(MAX(fg29_bookedadrpickup), 0.0) as fg29_bookedadrpickup,
       isnull(MAX(fg30_bookedadrcurrent), 0.0) as fg30_bookedadrcurrent,
       isnull(MAX(fg30_bookedadrpickup), 0.0) as fg30_bookedadrpickup,
       isnull(MAX(fg31_bookedadrcurrent), 0.0) as fg31_bookedadrcurrent,
       isnull(MAX(fg31_bookedadrpickup), 0.0) as fg31_bookedadrpickup,
       isnull(MAX(fg32_bookedadrcurrent), 0.0) as fg32_bookedadrcurrent,
       isnull(MAX(fg32_bookedadrpickup), 0.0) as fg32_bookedadrpickup,
       isnull(MAX(fg33_bookedadrcurrent), 0.0) as fg33_bookedadrcurrent,
       isnull(MAX(fg33_bookedadrpickup), 0.0) as fg33_bookedadrpickup,
       isnull(MAX(fg34_bookedadrcurrent), 0.0) as fg34_bookedadrcurrent,
       isnull(MAX(fg34_bookedadrpickup), 0.0) as fg34_bookedadrpickup,
       isnull(MAX(fg35_bookedadrcurrent), 0.0) as fg35_bookedadrcurrent,
       isnull(MAX(fg35_bookedadrpickup), 0.0) as fg35_bookedadrpickup,
       isnull(MAX(fg36_bookedadrcurrent), 0.0) as fg36_bookedadrcurrent,
       isnull(MAX(fg36_bookedadrpickup), 0.0) as fg36_bookedadrpickup,
       isnull(MAX(fg37_bookedadrcurrent), 0.0) as fg37_bookedadrcurrent,
       isnull(MAX(fg37_bookedadrpickup), 0.0) as fg37_bookedadrpickup,
       isnull(MAX(fg38_bookedadrcurrent), 0.0) as fg38_bookedadrcurrent,
       isnull(MAX(fg38_bookedadrpickup), 0.0) as fg38_bookedadrpickup,
       isnull(MAX(fg39_bookedadrcurrent), 0.0) as fg39_bookedadrcurrent,
       isnull(MAX(fg39_bookedadrpickup), 0.0) as fg39_bookedadrpickup,
       isnull(MAX(fg40_bookedadrcurrent), 0.0) as fg40_bookedadrcurrent,
       isnull(MAX(fg40_bookedadrpickup), 0.0) as fg40_bookedadrpickup,
       isnull(MAX(fg41_bookedadrcurrent), 0.0) as fg41_bookedadrcurrent,
       isnull(MAX(fg41_bookedadrpickup), 0.0) as fg41_bookedadrpickup,
       isnull(MAX(fg42_bookedadrcurrent), 0.0) as fg42_bookedadrcurrent,
       isnull(MAX(fg42_bookedadrpickup), 0.0) as fg42_bookedadrpickup,
       isnull(MAX(fg43_bookedadrcurrent), 0.0) as fg43_bookedadrcurrent,
       isnull(MAX(fg43_bookedadrpickup), 0.0) as fg43_bookedadrpickup,
       isnull(MAX(fg44_bookedadrcurrent), 0.0) as fg44_bookedadrcurrent,
       isnull(MAX(fg44_bookedadrpickup), 0.0) as fg44_bookedadrpickup,
       isnull(MAX(fg45_bookedadrcurrent), 0.0) as fg45_bookedadrcurrent,
       isnull(MAX(fg45_bookedadrpickup), 0.0) as fg45_bookedadrpickup,
       isnull(MAX(fg46_bookedadrcurrent), 0.0) as fg46_bookedadrcurrent,
       isnull(MAX(fg46_bookedadrpickup), 0.0) as fg46_bookedadrpickup,
       isnull(MAX(fg47_bookedadrcurrent), 0.0) as fg47_bookedadrcurrent,
       isnull(MAX(fg47_bookedadrpickup), 0.0) as fg47_bookedadrpickup,
       isnull(MAX(fg48_bookedadrcurrent), 0.0) as fg48_bookedadrcurrent,
       isnull(MAX(fg48_bookedadrpickup), 0.0) as fg48_bookedadrpickup,
       isnull(MAX(fg49_bookedadrcurrent), 0.0) as fg49_bookedadrcurrent,
       isnull(MAX(fg49_bookedadrpickup), 0.0) as fg49_bookedadrpickup,
       isnull(MAX(fg50_bookedadrcurrent), 0.0) as fg50_bookedadrcurrent,
       isnull(MAX(fg50_bookedadrpickup), 0.0) as fg50_bookedadrpickup,
       isnull(MAX(fg1_fcstedadrcurrent), 0.0) as fg1_fcstedadrcurrent,
       isnull(MAX(fg1_fcstedadrpickup), 0.0) as fg1_fcstedadrpickup,
       isnull(MAX(fg2_fcstedadrcurrent), 0.0) as fg2_fcstedadrcurrent,
       isnull(MAX(fg2_fcstedadrpickup), 0.0) as fg2_fcstedadrpickup,
       isnull(MAX(fg3_fcstedadrcurrent), 0.0) as fg3_fcstedadrcurrent,
       isnull(MAX(fg3_fcstedadrpickup), 0.0) as fg3_fcstedadrpickup,
       isnull(MAX(fg4_fcstedadrcurrent), 0.0) as fg4_fcstedadrcurrent,
       isnull(MAX(fg4_fcstedadrpickup), 0.0) as fg4_fcstedadrpickup,
       isnull(MAX(fg5_fcstedadrcurrent), 0.0) as fg5_fcstedadrcurrent,
       isnull(MAX(fg5_fcstedadrpickup), 0.0) as fg5_fcstedadrpickup,
       isnull(MAX(fg6_fcstedadrcurrent), 0.0) as fg6_fcstedadrcurrent,
       isnull(MAX(fg6_fcstedadrpickup), 0.0) as fg6_fcstedadrpickup,
       isnull(MAX(fg7_fcstedadrcurrent), 0.0) as fg7_fcstedadrcurrent,
       isnull(MAX(fg7_fcstedadrpickup), 0.0) as fg7_fcstedadrpickup,
       isnull(MAX(fg8_fcstedadrcurrent), 0.0) as fg8_fcstedadrcurrent,
       isnull(MAX(fg8_fcstedadrpickup), 0.0) as fg8_fcstedadrpickup,
       isnull(MAX(fg9_fcstedadrcurrent), 0.0) as fg9_fcstedadrcurrent,
       isnull(MAX(fg9_fcstedadrpickup), 0.0) as fg9_fcstedadrpickup,
       isnull(MAX(fg10_fcstedadrcurrent), 0.0) as fg10_fcstedadrcurrent,
       isnull(MAX(fg10_fcstedadrpickup), 0.0) as fg10_fcstedadrpickup,
       isnull(MAX(fg11_fcstedadrcurrent), 0.0) as fg11_fcstedadrcurrent,
       isnull(MAX(fg11_fcstedadrpickup), 0.0) as fg11_fcstedadrpickup,
       isnull(MAX(fg12_fcstedadrcurrent), 0.0) as fg12_fcstedadrcurrent,
       isnull(MAX(fg12_fcstedadrpickup), 0.0) as fg12_fcstedadrpickup,
       isnull(MAX(fg13_fcstedadrcurrent), 0.0) as fg13_fcstedadrcurrent,
       isnull(MAX(fg13_fcstedadrpickup), 0.0) as fg13_fcstedadrpickup,
       isnull(MAX(fg14_fcstedadrcurrent), 0.0) as fg14_fcstedadrcurrent,
       isnull(MAX(fg14_fcstedadrpickup), 0.0) as fg14_fcstedadrpickup,
       isnull(MAX(fg15_fcstedadrcurrent), 0.0) as fg15_fcstedadrcurrent,
       isnull(MAX(fg15_fcstedadrpickup), 0.0) as fg15_fcstedadrpickup,
       isnull(MAX(fg16_fcstedadrcurrent), 0.0) as fg16_fcstedadrcurrent,
       isnull(MAX(fg16_fcstedadrpickup), 0.0) as fg16_fcstedadrpickup,
       isnull(MAX(fg17_fcstedadrcurrent), 0.0) as fg17_fcstedadrcurrent,
       isnull(MAX(fg17_fcstedadrpickup), 0.0) as fg17_fcstedadrpickup,
       isnull(MAX(fg18_fcstedadrcurrent), 0.0) as fg18_fcstedadrcurrent,
       isnull(MAX(fg18_fcstedadrpickup), 0.0) as fg18_fcstedadrpickup,
       isnull(MAX(fg19_fcstedadrcurrent), 0.0) as fg19_fcstedadrcurrent,
       isnull(MAX(fg19_fcstedadrpickup), 0.0) as fg19_fcstedadrpickup,
       isnull(MAX(fg20_fcstedadrcurrent), 0.0) as fg20_fcstedadrcurrent,
       isnull(MAX(fg20_fcstedadrpickup), 0.0) as fg20_fcstedadrpickup,
       isnull(MAX(fg21_fcstedadrcurrent), 0.0) as fg21_fcstedadrcurrent,
       isnull(MAX(fg21_fcstedadrpickup), 0.0) as fg21_fcstedadrpickup,
       isnull(MAX(fg22_fcstedadrcurrent), 0.0) as fg22_fcstedadrcurrent,
       isnull(MAX(fg22_fcstedadrpickup), 0.0) as fg22_fcstedadrpickup,
       isnull(MAX(fg23_fcstedadrcurrent), 0.0) as fg23_fcstedadrcurrent,
       isnull(MAX(fg23_fcstedadrpickup), 0.0) as fg23_fcstedadrpickup,
       isnull(MAX(fg24_fcstedadrcurrent), 0.0) as fg24_fcstedadrcurrent,
       isnull(MAX(fg24_fcstedadrpickup), 0.0) as fg24_fcstedadrpickup,
       isnull(MAX(fg25_fcstedadrcurrent), 0.0) as fg25_fcstedadrcurrent,
       isnull(MAX(fg25_fcstedadrpickup), 0.0) as fg25_fcstedadrpickup,
       isnull(MAX(fg26_fcstedadrcurrent), 0.0) as fg26_fcstedadrcurrent,
       isnull(MAX(fg26_fcstedadrpickup), 0.0) as fg26_fcstedadrpickup,
       isnull(MAX(fg27_fcstedadrcurrent), 0.0) as fg27_fcstedadrcurrent,
       isnull(MAX(fg27_fcstedadrpickup), 0.0) as fg27_fcstedadrpickup,
       isnull(MAX(fg28_fcstedadrcurrent), 0.0) as fg28_fcstedadrcurrent,
       isnull(MAX(fg28_fcstedadrpickup), 0.0) as fg28_fcstedadrpickup,
       isnull(MAX(fg29_fcstedadrcurrent), 0.0) as fg29_fcstedadrcurrent,
       isnull(MAX(fg29_fcstedadrpickup), 0.0) as fg29_fcstedadrpickup,
       isnull(MAX(fg30_fcstedadrcurrent), 0.0) as fg30_fcstedadrcurrent,
       isnull(MAX(fg30_fcstedadrpickup), 0.0) as fg30_fcstedadrpickup,
       isnull(MAX(fg31_fcstedadrcurrent), 0.0) as fg31_fcstedadrcurrent,
       isnull(MAX(fg31_fcstedadrpickup), 0.0) as fg31_fcstedadrpickup,
       isnull(MAX(fg32_fcstedadrcurrent), 0.0) as fg32_fcstedadrcurrent,
       isnull(MAX(fg32_fcstedadrpickup), 0.0) as fg32_fcstedadrpickup,
       isnull(MAX(fg33_fcstedadrcurrent), 0.0) as fg33_fcstedadrcurrent,
       isnull(MAX(fg33_fcstedadrpickup), 0.0) as fg33_fcstedadrpickup,
       isnull(MAX(fg34_fcstedadrcurrent), 0.0) as fg34_fcstedadrcurrent,
       isnull(MAX(fg34_fcstedadrpickup), 0.0) as fg34_fcstedadrpickup,
       isnull(MAX(fg35_fcstedadrcurrent), 0.0) as fg35_fcstedadrcurrent,
       isnull(MAX(fg35_fcstedadrpickup), 0.0) as fg35_fcstedadrpickup,
       isnull(MAX(fg36_fcstedadrcurrent), 0.0) as fg36_fcstedadrcurrent,
       isnull(MAX(fg36_fcstedadrpickup), 0.0) as fg36_fcstedadrpickup,
       isnull(MAX(fg37_fcstedadrcurrent), 0.0) as fg37_fcstedadrcurrent,
       isnull(MAX(fg37_fcstedadrpickup), 0.0) as fg37_fcstedadrpickup,
       isnull(MAX(fg38_fcstedadrcurrent), 0.0) as fg38_fcstedadrcurrent,
       isnull(MAX(fg38_fcstedadrpickup), 0.0) as fg38_fcstedadrpickup,
       isnull(MAX(fg39_fcstedadrcurrent), 0.0) as fg39_fcstedadrcurrent,
       isnull(MAX(fg39_fcstedadrpickup), 0.0) as fg39_fcstedadrpickup,
       isnull(MAX(fg40_fcstedadrcurrent), 0.0) as fg40_fcstedadrcurrent,
       isnull(MAX(fg40_fcstedadrpickup), 0.0) as fg40_fcstedadrpickup,
       isnull(MAX(fg41_fcstedadrcurrent), 0.0) as fg41_fcstedadrcurrent,
       isnull(MAX(fg41_fcstedadrpickup), 0.0) as fg41_fcstedadrpickup,
       isnull(MAX(fg42_fcstedadrcurrent), 0.0) as fg42_fcstedadrcurrent,
       isnull(MAX(fg42_fcstedadrpickup), 0.0) as fg42_fcstedadrpickup,
       isnull(MAX(fg43_fcstedadrcurrent), 0.0) as fg43_fcstedadrcurrent,
       isnull(MAX(fg43_fcstedadrpickup), 0.0) as fg43_fcstedadrpickup,
       isnull(MAX(fg44_fcstedadrcurrent), 0.0) as fg44_fcstedadrcurrent,
       isnull(MAX(fg44_fcstedadrpickup), 0.0) as fg44_fcstedadrpickup,
       isnull(MAX(fg45_fcstedadrcurrent), 0.0) as fg45_fcstedadrcurrent,
       isnull(MAX(fg45_fcstedadrpickup), 0.0) as fg45_fcstedadrpickup,
       isnull(MAX(fg46_fcstedadrcurrent), 0.0) as fg46_fcstedadrcurrent,
       isnull(MAX(fg46_fcstedadrpickup), 0.0) as fg46_fcstedadrpickup,
       isnull(MAX(fg47_fcstedadrcurrent), 0.0) as fg47_fcstedadrcurrent,
       isnull(MAX(fg47_fcstedadrpickup), 0.0) as fg47_fcstedadrpickup,
       isnull(MAX(fg48_fcstedadrcurrent), 0.0) as fg48_fcstedadrcurrent,
       isnull(MAX(fg48_fcstedadrpickup), 0.0) as fg48_fcstedadrpickup,
       isnull(MAX(fg49_fcstedadrcurrent), 0.0) as fg49_fcstedadrcurrent,
       isnull(MAX(fg49_fcstedadrpickup), 0.0) as fg49_fcstedadrpickup,
       isnull(MAX(fg50_fcstedadrcurrent), 0.0) as fg50_fcstedadrcurrent,
       isnull(MAX(fg50_fcstedadrpickup), 0.0) as fg50_fcstedadrpickup,
       isnull(MAX(fg1_block), 0.0) as fg1_block,
       isnull(MAX(fg1_block_available), 0.0) as fg1_block_available,
       isnull(MAX(fg1_block_pickup), 0.0) as fg1_block_pickup,
       isnull(MAX(fg2_block), 0.0) as fg2_block,
       isnull(MAX(fg2_block_available), 0.0) as fg2_block_available,
       isnull(MAX(fg2_block_pickup), 0.0) as fg2_block_pickup,
       isnull(MAX(fg3_block), 0.0) as fg3_block,
       isnull(MAX(fg3_block_available), 0.0) as fg3_block_available,
       isnull(MAX(fg3_block_pickup), 0.0) as fg3_block_pickup,
       isnull(MAX(fg4_block), 0.0) as fg4_block,
       isnull(MAX(fg4_block_available), 0.0) as fg4_block_available,
       isnull(MAX(fg4_block_pickup), 0.0) as fg4_block_pickup,
       isnull(MAX(fg5_block), 0.0) as fg5_block,
       isnull(MAX(fg5_block_available), 0.0) as fg5_block_available,
       isnull(MAX(fg5_block_pickup), 0.0) as fg5_block_pickup,
       isnull(MAX(fg6_block), 0.0) as fg6_block,
       isnull(MAX(fg6_block_available), 0.0) as fg6_block_available,
       isnull(MAX(fg6_block_pickup), 0.0) as fg6_block_pickup,
       isnull(MAX(fg7_block), 0.0) as fg7_block,
       isnull(MAX(fg7_block_available), 0.0) as fg7_block_available,
       isnull(MAX(fg7_block_pickup), 0.0) as fg7_block_pickup,
       isnull(MAX(fg8_block), 0.0) as fg8_block,
       isnull(MAX(fg8_block_available), 0.0) as fg8_block_available,
       isnull(MAX(fg8_block_pickup), 0.0) as fg8_block_pickup,
       isnull(MAX(fg9_block), 0.0) as fg9_block,
       isnull(MAX(fg9_block_available), 0.0) as fg9_block_available,
       isnull(MAX(fg9_block_pickup), 0.0) as fg9_block_pickup,
       isnull(MAX(fg10_block), 0.0) as fg10_block,
       isnull(MAX(fg10_block_available), 0.0) as fg10_block_available,
       isnull(MAX(fg10_block_pickup), 0.0) as fg10_block_pickup,
       isnull(MAX(fg11_block), 0.0) as fg11_block,
       isnull(MAX(fg11_block_available), 0.0) as fg11_block_available,
       isnull(MAX(fg11_block_pickup), 0.0) as fg11_block_pickup,
       isnull(MAX(fg12_block), 0.0) as fg12_block,
       isnull(MAX(fg12_block_available), 0.0) as fg12_block_available,
       isnull(MAX(fg12_block_pickup), 0.0) as fg12_block_pickup,
       isnull(MAX(fg13_block), 0.0) as fg13_block,
       isnull(MAX(fg13_block_available), 0.0) as fg13_block_available,
       isnull(MAX(fg13_block_pickup), 0.0) as fg13_block_pickup,
       isnull(MAX(fg14_block), 0.0) as fg14_block,
       isnull(MAX(fg14_block_available), 0.0) as fg14_block_available,
       isnull(MAX(fg14_block_pickup), 0.0) as fg14_block_pickup,
       isnull(MAX(fg15_block), 0.0) as fg15_block,
       isnull(MAX(fg15_block_available), 0.0) as fg15_block_available,
       isnull(MAX(fg15_block_pickup), 0.0) as fg15_block_pickup,
       isnull(MAX(fg16_block), 0.0) as fg16_block,
       isnull(MAX(fg16_block_available), 0.0) as fg16_block_available,
       isnull(MAX(fg16_block_pickup), 0.0) as fg16_block_pickup,
       isnull(MAX(fg17_block), 0.0) as fg17_block,
       isnull(MAX(fg17_block_available), 0.0) as fg17_block_available,
       isnull(MAX(fg17_block_pickup), 0.0) as fg17_block_pickup,
       isnull(MAX(fg18_block), 0.0) as fg18_block,
       isnull(MAX(fg18_block_available), 0.0) as fg18_block_available,
       isnull(MAX(fg18_block_pickup), 0.0) as fg18_block_pickup,
       isnull(MAX(fg19_block), 0.0) as fg19_block,
       isnull(MAX(fg19_block_available), 0.0) as fg19_block_available,
       isnull(MAX(fg19_block_pickup), 0.0) as fg19_block_pickup,
       isnull(MAX(fg20_block), 0.0) as fg20_block,
       isnull(MAX(fg20_block_available), 0.0) as fg20_block_available,
       isnull(MAX(fg20_block_pickup), 0.0) as fg20_block_pickup,
       isnull(MAX(fg21_block), 0.0) as fg21_block,
       isnull(MAX(fg21_block_available), 0.0) as fg21_block_available,
       isnull(MAX(fg21_block_pickup), 0.0) as fg21_block_pickup,
       isnull(MAX(fg22_block), 0.0) as fg22_block,
       isnull(MAX(fg22_block_available), 0.0) as fg22_block_available,
       isnull(MAX(fg22_block_pickup), 0.0) as fg22_block_pickup,
       isnull(MAX(fg23_block), 0.0) as fg23_block,
       isnull(MAX(fg23_block_available), 0.0) as fg23_block_available,
       isnull(MAX(fg23_block_pickup), 0.0) as fg23_block_pickup,
       isnull(MAX(fg24_block), 0.0) as fg24_block,
       isnull(MAX(fg24_block_available), 0.0) as fg24_block_available,
       isnull(MAX(fg24_block_pickup), 0.0) as fg24_block_pickup,
       isnull(MAX(fg25_block), 0.0) as fg25_block,
       isnull(MAX(fg25_block_available), 0.0) as fg25_block_available,
       isnull(MAX(fg25_block_pickup), 0.0) as fg25_block_pickup,
       isnull(MAX(fg26_block), 0.0) as fg26_block,
       isnull(MAX(fg26_block_available), 0.0) as fg26_block_available,
       isnull(MAX(fg26_block_pickup), 0.0) as fg26_block_pickup,
       isnull(MAX(fg27_block), 0.0) as fg27_block,
       isnull(MAX(fg27_block_available), 0.0) as fg27_block_available,
       isnull(MAX(fg27_block_pickup), 0.0) as fg27_block_pickup,
       isnull(MAX(fg28_block), 0.0) as fg28_block,
       isnull(MAX(fg28_block_available), 0.0) as fg28_block_available,
       isnull(MAX(fg28_block_pickup), 0.0) as fg28_block_pickup,
       isnull(MAX(fg29_block), 0.0) as fg29_block,
       isnull(MAX(fg29_block_available), 0.0) as fg29_block_available,
       isnull(MAX(fg29_block_pickup), 0.0) as fg29_block_pickup,
       isnull(MAX(fg30_block), 0.0) as fg30_block,
       isnull(MAX(fg30_block_available), 0.0) as fg30_block_available,
       isnull(MAX(fg30_block_pickup), 0.0) as fg30_block_pickup,
       isnull(MAX(fg31_block), 0.0) as fg31_block,
       isnull(MAX(fg31_block_available), 0.0) as fg31_block_available,
       isnull(MAX(fg31_block_pickup), 0.0) as fg31_block_pickup,
       isnull(MAX(fg32_block), 0.0) as fg32_block,
       isnull(MAX(fg32_block_available), 0.0) as fg32_block_available,
       isnull(MAX(fg32_block_pickup), 0.0) as fg32_block_pickup,
       isnull(MAX(fg33_block), 0.0) as fg33_block,
       isnull(MAX(fg33_block_available), 0.0) as fg33_block_available,
       isnull(MAX(fg33_block_pickup), 0.0) as fg33_block_pickup,
       isnull(MAX(fg34_block), 0.0) as fg34_block,
       isnull(MAX(fg34_block_available), 0.0) as fg34_block_available,
       isnull(MAX(fg34_block_pickup), 0.0) as fg34_block_pickup,
       isnull(MAX(fg35_block), 0.0) as fg35_block,
       isnull(MAX(fg35_block_available), 0.0) as fg35_block_available,
       isnull(MAX(fg35_block_pickup), 0.0) as fg35_block_pickup,
       isnull(MAX(fg36_block), 0.0) as fg36_block,
       isnull(MAX(fg36_block_available), 0.0) as fg36_block_available,
       isnull(MAX(fg36_block_pickup), 0.0) as fg36_block_pickup,
       isnull(MAX(fg37_block), 0.0) as fg37_block,
       isnull(MAX(fg37_block_available), 0.0) as fg37_block_available,
       isnull(MAX(fg37_block_pickup), 0.0) as fg37_block_pickup,
       isnull(MAX(fg38_block), 0.0) as fg38_block,
       isnull(MAX(fg38_block_available), 0.0) as fg38_block_available,
       isnull(MAX(fg38_block_pickup), 0.0) as fg38_block_pickup,
       isnull(MAX(fg39_block), 0.0) as fg39_block,
       isnull(MAX(fg39_block_available), 0.0) as fg39_block_available,
       isnull(MAX(fg39_block_pickup), 0.0) as fg39_block_pickup,
       isnull(MAX(fg40_block), 0.0) as fg40_block,
       isnull(MAX(fg40_block_available), 0.0) as fg40_block_available,
       isnull(MAX(fg40_block_pickup), 0.0) as fg40_block_pickup,
       isnull(MAX(fg41_block), 0.0) as fg41_block,
       isnull(MAX(fg41_block_available), 0.0) as fg41_block_available,
       isnull(MAX(fg41_block_pickup), 0.0) as fg41_block_pickup,
       isnull(MAX(fg42_block), 0.0) as fg42_block,
       isnull(MAX(fg42_block_available), 0.0) as fg42_block_available,
       isnull(MAX(fg42_block_pickup), 0.0) as fg42_block_pickup,
       isnull(MAX(fg43_block), 0.0) as fg43_block,
       isnull(MAX(fg43_block_available), 0.0) as fg43_block_available,
       isnull(MAX(fg43_block_pickup), 0.0) as fg43_block_pickup,
       isnull(MAX(fg44_block), 0.0) as fg44_block,
       isnull(MAX(fg44_block_available), 0.0) as fg44_block_available,
       isnull(MAX(fg44_block_pickup), 0.0) as fg44_block_pickup,
       isnull(MAX(fg45_block), 0.0) as fg45_block,
       isnull(MAX(fg45_block_available), 0.0) as fg45_block_available,
       isnull(MAX(fg45_block_pickup), 0.0) as fg45_block_pickup,
       isnull(MAX(fg46_block), 0.0) as fg46_block,
       isnull(MAX(fg46_block_available), 0.0) as fg46_block_available,
       isnull(MAX(fg46_block_pickup), 0.0) as fg46_block_pickup,
       isnull(MAX(fg47_block), 0.0) as fg47_block,
       isnull(MAX(fg47_block_available), 0.0) as fg47_block_available,
       isnull(MAX(fg47_block_pickup), 0.0) as fg47_block_pickup,
       isnull(MAX(fg48_block), 0.0) as fg48_block,
       isnull(MAX(fg48_block_available), 0.0) as fg48_block_available,
       isnull(MAX(fg48_block_pickup), 0.0) as fg48_block_pickup,
       isnull(MAX(fg49_block), 0.0) as fg49_block,
       isnull(MAX(fg49_block_available), 0.0) as fg49_block_available,
       isnull(MAX(fg49_block_pickup), 0.0) as fg49_block_pickup,
       isnull(MAX(fg50_block), 0.0) as fg50_block,
       isnull(MAX(fg50_block_available), 0.0) as fg50_block_available,
       isnull(MAX(fg50_block_pickup), 0.0) as fg50_block_pickup,
       isnull(MAX(fg1_profit_onBooks_current), 0.0) as fg1_profit_onBooks_current,
       isnull(MAX(fg1_profit_onBooks_pickup), 0.0) as fg1_profit_onBooks_pickup,
       isnull(MAX(fg2_profit_onBooks_current), 0.0) as fg2_profit_onBooks_current,
       isnull(MAX(fg2_profit_onBooks_pickup), 0.0) as fg2_profit_onBooks_pickup,
       isnull(MAX(fg3_profit_onBooks_current), 0.0) as fg3_profit_onBooks_current,
       isnull(MAX(fg3_profit_onBooks_pickup), 0.0) as fg3_profit_onBooks_pickup,
       isnull(MAX(fg4_profit_onBooks_current), 0.0) as fg4_profit_onBooks_current,
       isnull(MAX(fg4_profit_onBooks_pickup), 0.0) as fg4_profit_onBooks_pickup,
       isnull(MAX(fg5_profit_onBooks_current), 0.0) as fg5_profit_onBooks_current,
       isnull(MAX(fg5_profit_onBooks_pickup), 0.0) as fg5_profit_onBooks_pickup,
       isnull(MAX(fg6_profit_onBooks_current), 0.0) as fg6_profit_onBooks_current,
       isnull(MAX(fg6_profit_onBooks_pickup), 0.0) as fg6_profit_onBooks_pickup,
       isnull(MAX(fg7_profit_onBooks_current), 0.0) as fg7_profit_onBooks_current,
       isnull(MAX(fg7_profit_onBooks_pickup), 0.0) as fg7_profit_onBooks_pickup,
       isnull(MAX(fg8_profit_onBooks_current), 0.0) as fg8_profit_onBooks_current,
       isnull(MAX(fg8_profit_onBooks_pickup), 0.0) as fg8_profit_onBooks_pickup,
       isnull(MAX(fg9_profit_onBooks_current), 0.0) as fg9_profit_onBooks_current,
       isnull(MAX(fg9_profit_onBooks_pickup), 0.0) as fg9_profit_onBooks_pickup,
       isnull(MAX(fg10_profit_onBooks_current), 0.0) as fg10_profit_onBooks_current,
       isnull(MAX(fg10_profit_onBooks_pickup), 0.0) as fg10_profit_onBooks_pickup,
       isnull(MAX(fg11_profit_onBooks_current), 0.0) as fg11_profit_onBooks_current,
       isnull(MAX(fg11_profit_onBooks_pickup), 0.0) as fg11_profit_onBooks_pickup,
       isnull(MAX(fg12_profit_onBooks_current), 0.0) as fg12_profit_onBooks_current,
       isnull(MAX(fg12_profit_onBooks_pickup), 0.0) as fg12_profit_onBooks_pickup,
       isnull(MAX(fg13_profit_onBooks_current), 0.0) as fg13_profit_onBooks_current,
       isnull(MAX(fg13_profit_onBooks_pickup), 0.0) as fg13_profit_onBooks_pickup,
       isnull(MAX(fg14_profit_onBooks_current), 0.0) as fg14_profit_onBooks_current,
       isnull(MAX(fg14_profit_onBooks_pickup), 0.0) as fg14_profit_onBooks_pickup,
       isnull(MAX(fg15_profit_onBooks_current), 0.0) as fg15_profit_onBooks_current,
       isnull(MAX(fg15_profit_onBooks_pickup), 0.0) as fg15_profit_onBooks_pickup,
       isnull(MAX(fg16_profit_onBooks_current), 0.0) as fg16_profit_onBooks_current,
       isnull(MAX(fg16_profit_onBooks_pickup), 0.0) as fg16_profit_onBooks_pickup,
       isnull(MAX(fg17_profit_onBooks_current), 0.0) as fg17_profit_onBooks_current,
       isnull(MAX(fg17_profit_onBooks_pickup), 0.0) as fg17_profit_onBooks_pickup,
       isnull(MAX(fg18_profit_onBooks_current), 0.0) as fg18_profit_onBooks_current,
       isnull(MAX(fg18_profit_onBooks_pickup), 0.0) as fg18_profit_onBooks_pickup,
       isnull(MAX(fg19_profit_onBooks_current), 0.0) as fg19_profit_onBooks_current,
       isnull(MAX(fg19_profit_onBooks_pickup), 0.0) as fg19_profit_onBooks_pickup,
       isnull(MAX(fg20_profit_onBooks_current), 0.0) as fg20_profit_onBooks_current,
       isnull(MAX(fg20_profit_onBooks_pickup), 0.0) as fg20_profit_onBooks_pickup,
       isnull(MAX(fg21_profit_onBooks_current), 0.0) as fg21_profit_onBooks_current,
       isnull(MAX(fg21_profit_onBooks_pickup), 0.0) as fg21_profit_onBooks_pickup,
       isnull(MAX(fg22_profit_onBooks_current), 0.0) as fg22_profit_onBooks_current,
       isnull(MAX(fg22_profit_onBooks_pickup), 0.0) as fg22_profit_onBooks_pickup,
       isnull(MAX(fg23_profit_onBooks_current), 0.0) as fg23_profit_onBooks_current,
       isnull(MAX(fg23_profit_onBooks_pickup), 0.0) as fg23_profit_onBooks_pickup,
       isnull(MAX(fg24_profit_onBooks_current), 0.0) as fg24_profit_onBooks_current,
       isnull(MAX(fg24_profit_onBooks_pickup), 0.0) as fg24_profit_onBooks_pickup,
       isnull(MAX(fg25_profit_onBooks_current), 0.0) as fg25_profit_onBooks_current,
       isnull(MAX(fg25_profit_onBooks_pickup), 0.0) as fg25_profit_onBooks_pickup,
       isnull(MAX(fg26_profit_onBooks_current), 0.0) as fg26_profit_onBooks_current,
       isnull(MAX(fg26_profit_onBooks_pickup), 0.0) as fg26_profit_onBooks_pickup,
       isnull(MAX(fg27_profit_onBooks_current), 0.0) as fg27_profit_onBooks_current,
       isnull(MAX(fg27_profit_onBooks_pickup), 0.0) as fg27_profit_onBooks_pickup,
       isnull(MAX(fg28_profit_onBooks_current), 0.0) as fg28_profit_onBooks_current,
       isnull(MAX(fg28_profit_onBooks_pickup), 0.0) as fg28_profit_onBooks_pickup,
       isnull(MAX(fg29_profit_onBooks_current), 0.0) as fg29_profit_onBooks_current,
       isnull(MAX(fg29_profit_onBooks_pickup), 0.0) as fg29_profit_onBooks_pickup,
       isnull(MAX(fg30_profit_onBooks_current), 0.0) as fg30_profit_onBooks_current,
       isnull(MAX(fg30_profit_onBooks_pickup), 0.0) as fg30_profit_onBooks_pickup,
       isnull(MAX(fg31_profit_onBooks_current), 0.0) as fg31_profit_onBooks_current,
       isnull(MAX(fg31_profit_onBooks_pickup), 0.0) as fg31_profit_onBooks_pickup,
       isnull(MAX(fg32_profit_onBooks_current), 0.0) as fg32_profit_onBooks_current,
       isnull(MAX(fg32_profit_onBooks_pickup), 0.0) as fg32_profit_onBooks_pickup,
       isnull(MAX(fg33_profit_onBooks_current), 0.0) as fg33_profit_onBooks_current,
       isnull(MAX(fg33_profit_onBooks_pickup), 0.0) as fg33_profit_onBooks_pickup,
       isnull(MAX(fg34_profit_onBooks_current), 0.0) as fg34_profit_onBooks_current,
       isnull(MAX(fg34_profit_onBooks_pickup), 0.0) as fg34_profit_onBooks_pickup,
       isnull(MAX(fg35_profit_onBooks_current), 0.0) as fg35_profit_onBooks_current,
       isnull(MAX(fg35_profit_onBooks_pickup), 0.0) as fg35_profit_onBooks_pickup,
       isnull(MAX(fg36_profit_onBooks_current), 0.0) as fg36_profit_onBooks_current,
       isnull(MAX(fg36_profit_onBooks_pickup), 0.0) as fg36_profit_onBooks_pickup,
       isnull(MAX(fg37_profit_onBooks_current), 0.0) as fg37_profit_onBooks_current,
       isnull(MAX(fg37_profit_onBooks_pickup), 0.0) as fg37_profit_onBooks_pickup,
       isnull(MAX(fg38_profit_onBooks_current), 0.0) as fg38_profit_onBooks_current,
       isnull(MAX(fg38_profit_onBooks_pickup), 0.0) as fg38_profit_onBooks_pickup,
       isnull(MAX(fg39_profit_onBooks_current), 0.0) as fg39_profit_onBooks_current,
       isnull(MAX(fg39_profit_onBooks_pickup), 0.0) as fg39_profit_onBooks_pickup,
       isnull(MAX(fg40_profit_onBooks_current), 0.0) as fg40_profit_onBooks_current,
       isnull(MAX(fg40_profit_onBooks_pickup), 0.0) as fg40_profit_onBooks_pickup,
       isnull(MAX(fg41_profit_onBooks_current), 0.0) as fg41_profit_onBooks_current,
       isnull(MAX(fg41_profit_onBooks_pickup), 0.0) as fg41_profit_onBooks_pickup,
       isnull(MAX(fg42_profit_onBooks_current), 0.0) as fg42_profit_onBooks_current,
       isnull(MAX(fg42_profit_onBooks_pickup), 0.0) as fg42_profit_onBooks_pickup,
       isnull(MAX(fg43_profit_onBooks_current), 0.0) as fg43_profit_onBooks_current,
       isnull(MAX(fg43_profit_onBooks_pickup), 0.0) as fg43_profit_onBooks_pickup,
       isnull(MAX(fg44_profit_onBooks_current), 0.0) as fg44_profit_onBooks_current,
       isnull(MAX(fg44_profit_onBooks_pickup), 0.0) as fg44_profit_onBooks_pickup,
       isnull(MAX(fg45_profit_onBooks_current), 0.0) as fg45_profit_onBooks_current,
       isnull(MAX(fg45_profit_onBooks_pickup), 0.0) as fg45_profit_onBooks_pickup,
       isnull(MAX(fg46_profit_onBooks_current), 0.0) as fg46_profit_onBooks_current,
       isnull(MAX(fg46_profit_onBooks_pickup), 0.0) as fg46_profit_onBooks_pickup,
       isnull(MAX(fg47_profit_onBooks_current), 0.0) as fg47_profit_onBooks_current,
       isnull(MAX(fg47_profit_onBooks_pickup), 0.0) as fg47_profit_onBooks_pickup,
       isnull(MAX(fg48_profit_onBooks_current), 0.0) as fg48_profit_onBooks_current,
       isnull(MAX(fg48_profit_onBooks_pickup), 0.0) as fg48_profit_onBooks_pickup,
       isnull(MAX(fg49_profit_onBooks_current), 0.0) as fg49_profit_onBooks_current,
       isnull(MAX(fg49_profit_onBooks_pickup), 0.0) as fg49_profit_onBooks_pickup,
       isnull(MAX(fg50_profit_onBooks_current), 0.0) as fg50_profit_onBooks_current,
       isnull(MAX(fg50_profit_onBooks_pickup), 0.0) as fg50_profit_onBooks_pickup,
       isnull(MAX(fg1_proPOR_onBooks_current), 0.0) as fg1_proPOR_onBooks_current,
       isnull(MAX(fg1_proPOR_onBooks_pickup), 0.0) as fg1_proPOR_onBooks_pickup,
       isnull(MAX(fg2_proPOR_onBooks_current), 0.0) as fg2_proPOR_onBooks_current,
       isnull(MAX(fg2_proPOR_onBooks_pickup), 0.0) as fg2_proPOR_onBooks_pickup,
       isnull(MAX(fg3_proPOR_onBooks_current), 0.0) as fg3_proPOR_onBooks_current,
       isnull(MAX(fg3_proPOR_onBooks_pickup), 0.0) as fg3_proPOR_onBooks_pickup,
       isnull(MAX(fg4_proPOR_onBooks_current), 0.0) as fg4_proPOR_onBooks_current,
       isnull(MAX(fg4_proPOR_onBooks_pickup), 0.0) as fg4_proPOR_onBooks_pickup,
       isnull(MAX(fg5_proPOR_onBooks_current), 0.0) as fg5_proPOR_onBooks_current,
       isnull(MAX(fg5_proPOR_onBooks_pickup), 0.0) as fg5_proPOR_onBooks_pickup,
       isnull(MAX(fg6_proPOR_onBooks_current), 0.0) as fg6_proPOR_onBooks_current,
       isnull(MAX(fg6_proPOR_onBooks_pickup), 0.0) as fg6_proPOR_onBooks_pickup,
       isnull(MAX(fg7_proPOR_onBooks_current), 0.0) as fg7_proPOR_onBooks_current,
       isnull(MAX(fg7_proPOR_onBooks_pickup), 0.0) as fg7_proPOR_onBooks_pickup,
       isnull(MAX(fg8_proPOR_onBooks_current), 0.0) as fg8_proPOR_onBooks_current,
       isnull(MAX(fg8_proPOR_onBooks_pickup), 0.0) as fg8_proPOR_onBooks_pickup,
       isnull(MAX(fg9_proPOR_onBooks_current), 0.0) as fg9_proPOR_onBooks_current,
       isnull(MAX(fg9_proPOR_onBooks_pickup), 0.0) as fg9_proPOR_onBooks_pickup,
       isnull(MAX(fg10_proPOR_onBooks_current), 0.0) as fg10_proPOR_onBooks_current,
       isnull(MAX(fg10_proPOR_onBooks_pickup), 0.0) as fg10_proPOR_onBooks_pickup,
       isnull(MAX(fg11_proPOR_onBooks_current), 0.0) as fg11_proPOR_onBooks_current,
       isnull(MAX(fg11_proPOR_onBooks_pickup), 0.0) as fg11_proPOR_onBooks_pickup,
       isnull(MAX(fg12_proPOR_onBooks_current), 0.0) as fg12_proPOR_onBooks_current,
       isnull(MAX(fg12_proPOR_onBooks_pickup), 0.0) as fg12_proPOR_onBooks_pickup,
       isnull(MAX(fg13_proPOR_onBooks_current), 0.0) as fg13_proPOR_onBooks_current,
       isnull(MAX(fg13_proPOR_onBooks_pickup), 0.0) as fg13_proPOR_onBooks_pickup,
       isnull(MAX(fg14_proPOR_onBooks_current), 0.0) as fg14_proPOR_onBooks_current,
       isnull(MAX(fg14_proPOR_onBooks_pickup), 0.0) as fg14_proPOR_onBooks_pickup,
       isnull(MAX(fg15_proPOR_onBooks_current), 0.0) as fg15_proPOR_onBooks_current,
       isnull(MAX(fg15_proPOR_onBooks_pickup), 0.0) as fg15_proPOR_onBooks_pickup,
       isnull(MAX(fg16_proPOR_onBooks_current), 0.0) as fg16_proPOR_onBooks_current,
       isnull(MAX(fg16_proPOR_onBooks_pickup), 0.0) as fg16_proPOR_onBooks_pickup,
       isnull(MAX(fg17_proPOR_onBooks_current), 0.0) as fg17_proPOR_onBooks_current,
       isnull(MAX(fg17_proPOR_onBooks_pickup), 0.0) as fg17_proPOR_onBooks_pickup,
       isnull(MAX(fg18_proPOR_onBooks_current), 0.0) as fg18_proPOR_onBooks_current,
       isnull(MAX(fg18_proPOR_onBooks_pickup), 0.0) as fg18_proPOR_onBooks_pickup,
       isnull(MAX(fg19_proPOR_onBooks_current), 0.0) as fg19_proPOR_onBooks_current,
       isnull(MAX(fg19_proPOR_onBooks_pickup), 0.0) as fg19_proPOR_onBooks_pickup,
       isnull(MAX(fg20_proPOR_onBooks_current), 0.0) as fg20_proPOR_onBooks_current,
       isnull(MAX(fg20_proPOR_onBooks_pickup), 0.0) as fg20_proPOR_onBooks_pickup,
       isnull(MAX(fg21_proPOR_onBooks_current), 0.0) as fg21_proPOR_onBooks_current,
       isnull(MAX(fg21_proPOR_onBooks_pickup), 0.0) as fg21_proPOR_onBooks_pickup,
       isnull(MAX(fg22_proPOR_onBooks_current), 0.0) as fg22_proPOR_onBooks_current,
       isnull(MAX(fg22_proPOR_onBooks_pickup), 0.0) as fg22_proPOR_onBooks_pickup,
       isnull(MAX(fg23_proPOR_onBooks_current), 0.0) as fg23_proPOR_onBooks_current,
       isnull(MAX(fg23_proPOR_onBooks_pickup), 0.0) as fg23_proPOR_onBooks_pickup,
       isnull(MAX(fg24_proPOR_onBooks_current), 0.0) as fg24_proPOR_onBooks_current,
       isnull(MAX(fg24_proPOR_onBooks_pickup), 0.0) as fg24_proPOR_onBooks_pickup,
       isnull(MAX(fg25_proPOR_onBooks_current), 0.0) as fg25_proPOR_onBooks_current,
       isnull(MAX(fg25_proPOR_onBooks_pickup), 0.0) as fg25_proPOR_onBooks_pickup,
       isnull(MAX(fg26_proPOR_onBooks_current), 0.0) as fg26_proPOR_onBooks_current,
       isnull(MAX(fg26_proPOR_onBooks_pickup), 0.0) as fg26_proPOR_onBooks_pickup,
       isnull(MAX(fg27_proPOR_onBooks_current), 0.0) as fg27_proPOR_onBooks_current,
       isnull(MAX(fg27_proPOR_onBooks_pickup), 0.0) as fg27_proPOR_onBooks_pickup,
       isnull(MAX(fg28_proPOR_onBooks_current), 0.0) as fg28_proPOR_onBooks_current,
       isnull(MAX(fg28_proPOR_onBooks_pickup), 0.0) as fg28_proPOR_onBooks_pickup,
       isnull(MAX(fg29_proPOR_onBooks_current), 0.0) as fg29_proPOR_onBooks_current,
       isnull(MAX(fg29_proPOR_onBooks_pickup), 0.0) as fg29_proPOR_onBooks_pickup,
       isnull(MAX(fg30_proPOR_onBooks_current), 0.0) as fg30_proPOR_onBooks_current,
       isnull(MAX(fg30_proPOR_onBooks_pickup), 0.0) as fg30_proPOR_onBooks_pickup,
       isnull(MAX(fg31_proPOR_onBooks_current), 0.0) as fg31_proPOR_onBooks_current,
       isnull(MAX(fg31_proPOR_onBooks_pickup), 0.0) as fg31_proPOR_onBooks_pickup,
       isnull(MAX(fg32_proPOR_onBooks_current), 0.0) as fg32_proPOR_onBooks_current,
       isnull(MAX(fg32_proPOR_onBooks_pickup), 0.0) as fg32_proPOR_onBooks_pickup,
       isnull(MAX(fg33_proPOR_onBooks_current), 0.0) as fg33_proPOR_onBooks_current,
       isnull(MAX(fg33_proPOR_onBooks_pickup), 0.0) as fg33_proPOR_onBooks_pickup,
       isnull(MAX(fg34_proPOR_onBooks_current), 0.0) as fg34_proPOR_onBooks_current,
       isnull(MAX(fg34_proPOR_onBooks_pickup), 0.0) as fg34_proPOR_onBooks_pickup,
       isnull(MAX(fg35_proPOR_onBooks_current), 0.0) as fg35_proPOR_onBooks_current,
       isnull(MAX(fg35_proPOR_onBooks_pickup), 0.0) as fg35_proPOR_onBooks_pickup,
       isnull(MAX(fg36_proPOR_onBooks_current), 0.0) as fg36_proPOR_onBooks_current,
       isnull(MAX(fg36_proPOR_onBooks_pickup), 0.0) as fg36_proPOR_onBooks_pickup,
       isnull(MAX(fg37_proPOR_onBooks_current), 0.0) as fg37_proPOR_onBooks_current,
       isnull(MAX(fg37_proPOR_onBooks_pickup), 0.0) as fg37_proPOR_onBooks_pickup,
       isnull(MAX(fg38_proPOR_onBooks_current), 0.0) as fg38_proPOR_onBooks_current,
       isnull(MAX(fg38_proPOR_onBooks_pickup), 0.0) as fg38_proPOR_onBooks_pickup,
       isnull(MAX(fg39_proPOR_onBooks_current), 0.0) as fg39_proPOR_onBooks_current,
       isnull(MAX(fg39_proPOR_onBooks_pickup), 0.0) as fg39_proPOR_onBooks_pickup,
       isnull(MAX(fg40_proPOR_onBooks_current), 0.0) as fg40_proPOR_onBooks_current,
       isnull(MAX(fg40_proPOR_onBooks_pickup), 0.0) as fg40_proPOR_onBooks_pickup,
       isnull(MAX(fg41_proPOR_onBooks_current), 0.0) as fg41_proPOR_onBooks_current,
       isnull(MAX(fg41_proPOR_onBooks_pickup), 0.0) as fg41_proPOR_onBooks_pickup,
       isnull(MAX(fg42_proPOR_onBooks_current), 0.0) as fg42_proPOR_onBooks_current,
       isnull(MAX(fg42_proPOR_onBooks_pickup), 0.0) as fg42_proPOR_onBooks_pickup,
       isnull(MAX(fg43_proPOR_onBooks_current), 0.0) as fg43_proPOR_onBooks_current,
       isnull(MAX(fg43_proPOR_onBooks_pickup), 0.0) as fg43_proPOR_onBooks_pickup,
       isnull(MAX(fg44_proPOR_onBooks_current), 0.0) as fg44_proPOR_onBooks_current,
       isnull(MAX(fg44_proPOR_onBooks_pickup), 0.0) as fg44_proPOR_onBooks_pickup,
       isnull(MAX(fg45_proPOR_onBooks_current), 0.0) as fg45_proPOR_onBooks_current,
       isnull(MAX(fg45_proPOR_onBooks_pickup), 0.0) as fg45_proPOR_onBooks_pickup,
       isnull(MAX(fg46_proPOR_onBooks_current), 0.0) as fg46_proPOR_onBooks_current,
       isnull(MAX(fg46_proPOR_onBooks_pickup), 0.0) as fg46_proPOR_onBooks_pickup,
       isnull(MAX(fg47_proPOR_onBooks_current), 0.0) as fg47_proPOR_onBooks_current,
       isnull(MAX(fg47_proPOR_onBooks_pickup), 0.0) as fg47_proPOR_onBooks_pickup,
       isnull(MAX(fg48_proPOR_onBooks_current), 0.0) as fg48_proPOR_onBooks_current,
       isnull(MAX(fg48_proPOR_onBooks_pickup), 0.0) as fg48_proPOR_onBooks_pickup,
       isnull(MAX(fg49_proPOR_onBooks_current), 0.0) as fg49_proPOR_onBooks_current,
       isnull(MAX(fg49_proPOR_onBooks_pickup), 0.0) as fg49_proPOR_onBooks_pickup,
       isnull(MAX(fg50_proPOR_onBooks_current), 0.0) as fg50_proPOR_onBooks_current,
       isnull(MAX(fg50_proPOR_onBooks_pickup), 0.0) as fg50_proPOR_onBooks_pickup,
       isnull(MAX(fg1_profit_forecast_current), 0.0) as fg1_profit_forecast_current,
       isnull(MAX(fg1_profit_forecast_pickup), 0.0) as fg1_profit_forecast_pickup,
       isnull(MAX(fg2_profit_forecast_current), 0.0) as fg2_profit_forecast_current,
       isnull(MAX(fg2_profit_forecast_pickup), 0.0) as fg2_profit_forecast_pickup,
       isnull(MAX(fg3_profit_forecast_current), 0.0) as fg3_profit_forecast_current,
       isnull(MAX(fg3_profit_forecast_pickup), 0.0) as fg3_profit_forecast_pickup,
       isnull(MAX(fg4_profit_forecast_current), 0.0) as fg4_profit_forecast_current,
       isnull(MAX(fg4_profit_forecast_pickup), 0.0) as fg4_profit_forecast_pickup,
       isnull(MAX(fg5_profit_forecast_current), 0.0) as fg5_profit_forecast_current,
       isnull(MAX(fg5_profit_forecast_pickup), 0.0) as fg5_profit_forecast_pickup,
       isnull(MAX(fg6_profit_forecast_current), 0.0) as fg6_profit_forecast_current,
       isnull(MAX(fg6_profit_forecast_pickup), 0.0) as fg6_profit_forecast_pickup,
       isnull(MAX(fg7_profit_forecast_current), 0.0) as fg7_profit_forecast_current,
       isnull(MAX(fg7_profit_forecast_pickup), 0.0) as fg7_profit_forecast_pickup,
       isnull(MAX(fg8_profit_forecast_current), 0.0) as fg8_profit_forecast_current,
       isnull(MAX(fg8_profit_forecast_pickup), 0.0) as fg8_profit_forecast_pickup,
       isnull(MAX(fg9_profit_forecast_current), 0.0) as fg9_profit_forecast_current,
       isnull(MAX(fg9_profit_forecast_pickup), 0.0) as fg9_profit_forecast_pickup,
       isnull(MAX(fg10_profit_forecast_current), 0.0) as fg10_profit_forecast_current,
       isnull(MAX(fg10_profit_forecast_pickup), 0.0) as fg10_profit_forecast_pickup,
       isnull(MAX(fg11_profit_forecast_current), 0.0) as fg11_profit_forecast_current,
       isnull(MAX(fg11_profit_forecast_pickup), 0.0) as fg11_profit_forecast_pickup,
       isnull(MAX(fg12_profit_forecast_current), 0.0) as fg12_profit_forecast_current,
       isnull(MAX(fg12_profit_forecast_pickup), 0.0) as fg12_profit_forecast_pickup,
       isnull(MAX(fg13_profit_forecast_current), 0.0) as fg13_profit_forecast_current,
       isnull(MAX(fg13_profit_forecast_pickup), 0.0) as fg13_profit_forecast_pickup,
       isnull(MAX(fg14_profit_forecast_current), 0.0) as fg14_profit_forecast_current,
       isnull(MAX(fg14_profit_forecast_pickup), 0.0) as fg14_profit_forecast_pickup,
       isnull(MAX(fg15_profit_forecast_current), 0.0) as fg15_profit_forecast_current,
       isnull(MAX(fg15_profit_forecast_pickup), 0.0) as fg15_profit_forecast_pickup,
       isnull(MAX(fg16_profit_forecast_current), 0.0) as fg16_profit_forecast_current,
       isnull(MAX(fg16_profit_forecast_pickup), 0.0) as fg16_profit_forecast_pickup,
       isnull(MAX(fg17_profit_forecast_current), 0.0) as fg17_profit_forecast_current,
       isnull(MAX(fg17_profit_forecast_pickup), 0.0) as fg17_profit_forecast_pickup,
       isnull(MAX(fg18_profit_forecast_current), 0.0) as fg18_profit_forecast_current,
       isnull(MAX(fg18_profit_forecast_pickup), 0.0) as fg18_profit_forecast_pickup,
       isnull(MAX(fg19_profit_forecast_current), 0.0) as fg19_profit_forecast_current,
       isnull(MAX(fg19_profit_forecast_pickup), 0.0) as fg19_profit_forecast_pickup,
       isnull(MAX(fg20_profit_forecast_current), 0.0) as fg20_profit_forecast_current,
       isnull(MAX(fg20_profit_forecast_pickup), 0.0) as fg20_profit_forecast_pickup,
       isnull(MAX(fg21_profit_forecast_current), 0.0) as fg21_profit_forecast_current,
       isnull(MAX(fg21_profit_forecast_pickup), 0.0) as fg21_profit_forecast_pickup,
       isnull(MAX(fg22_profit_forecast_current), 0.0) as fg22_profit_forecast_current,
       isnull(MAX(fg22_profit_forecast_pickup), 0.0) as fg22_profit_forecast_pickup,
       isnull(MAX(fg23_profit_forecast_current), 0.0) as fg23_profit_forecast_current,
       isnull(MAX(fg23_profit_forecast_pickup), 0.0) as fg23_profit_forecast_pickup,
       isnull(MAX(fg24_profit_forecast_current), 0.0) as fg24_profit_forecast_current,
       isnull(MAX(fg24_profit_forecast_pickup), 0.0) as fg24_profit_forecast_pickup,
       isnull(MAX(fg25_profit_forecast_current), 0.0) as fg25_profit_forecast_current,
       isnull(MAX(fg25_profit_forecast_pickup), 0.0) as fg25_profit_forecast_pickup,
       isnull(MAX(fg26_profit_forecast_current), 0.0) as fg26_profit_forecast_current,
       isnull(MAX(fg26_profit_forecast_pickup), 0.0) as fg26_profit_forecast_pickup,
       isnull(MAX(fg27_profit_forecast_current), 0.0) as fg27_profit_forecast_current,
       isnull(MAX(fg27_profit_forecast_pickup), 0.0) as fg27_profit_forecast_pickup,
       isnull(MAX(fg28_profit_forecast_current), 0.0) as fg28_profit_forecast_current,
       isnull(MAX(fg28_profit_forecast_pickup), 0.0) as fg28_profit_forecast_pickup,
       isnull(MAX(fg29_profit_forecast_current), 0.0) as fg29_profit_forecast_current,
       isnull(MAX(fg29_profit_forecast_pickup), 0.0) as fg29_profit_forecast_pickup,
       isnull(MAX(fg30_profit_forecast_current), 0.0) as fg30_profit_forecast_current,
       isnull(MAX(fg30_profit_forecast_pickup), 0.0) as fg30_profit_forecast_pickup,
       isnull(MAX(fg31_profit_forecast_current), 0.0) as fg31_profit_forecast_current,
       isnull(MAX(fg31_profit_forecast_pickup), 0.0) as fg31_profit_forecast_pickup,
       isnull(MAX(fg32_profit_forecast_current), 0.0) as fg32_profit_forecast_current,
       isnull(MAX(fg32_profit_forecast_pickup), 0.0) as fg32_profit_forecast_pickup,
       isnull(MAX(fg33_profit_forecast_current), 0.0) as fg33_profit_forecast_current,
       isnull(MAX(fg33_profit_forecast_pickup), 0.0) as fg33_profit_forecast_pickup,
       isnull(MAX(fg34_profit_forecast_current), 0.0) as fg34_profit_forecast_current,
       isnull(MAX(fg34_profit_forecast_pickup), 0.0) as fg34_profit_forecast_pickup,
       isnull(MAX(fg35_profit_forecast_current), 0.0) as fg35_profit_forecast_current,
       isnull(MAX(fg35_profit_forecast_pickup), 0.0) as fg35_profit_forecast_pickup,
       isnull(MAX(fg36_profit_forecast_current), 0.0) as fg36_profit_forecast_current,
       isnull(MAX(fg36_profit_forecast_pickup), 0.0) as fg36_profit_forecast_pickup,
       isnull(MAX(fg37_profit_forecast_current), 0.0) as fg37_profit_forecast_current,
       isnull(MAX(fg37_profit_forecast_pickup), 0.0) as fg37_profit_forecast_pickup,
       isnull(MAX(fg38_profit_forecast_current), 0.0) as fg38_profit_forecast_current,
       isnull(MAX(fg38_profit_forecast_pickup), 0.0) as fg38_profit_forecast_pickup,
       isnull(MAX(fg39_profit_forecast_current), 0.0) as fg39_profit_forecast_current,
       isnull(MAX(fg39_profit_forecast_pickup), 0.0) as fg39_profit_forecast_pickup,
       isnull(MAX(fg40_profit_forecast_current), 0.0) as fg40_profit_forecast_current,
       isnull(MAX(fg40_profit_forecast_pickup), 0.0) as fg40_profit_forecast_pickup,
       isnull(MAX(fg41_profit_forecast_current), 0.0) as fg41_profit_forecast_current,
       isnull(MAX(fg41_profit_forecast_pickup), 0.0) as fg41_profit_forecast_pickup,
       isnull(MAX(fg42_profit_forecast_current), 0.0) as fg42_profit_forecast_current,
       isnull(MAX(fg42_profit_forecast_pickup), 0.0) as fg42_profit_forecast_pickup,
       isnull(MAX(fg43_profit_forecast_current), 0.0) as fg43_profit_forecast_current,
       isnull(MAX(fg43_profit_forecast_pickup), 0.0) as fg43_profit_forecast_pickup,
       isnull(MAX(fg44_profit_forecast_current), 0.0) as fg44_profit_forecast_current,
       isnull(MAX(fg44_profit_forecast_pickup), 0.0) as fg44_profit_forecast_pickup,
       isnull(MAX(fg45_profit_forecast_current), 0.0) as fg45_profit_forecast_current,
       isnull(MAX(fg45_profit_forecast_pickup), 0.0) as fg45_profit_forecast_pickup,
       isnull(MAX(fg46_profit_forecast_current), 0.0) as fg46_profit_forecast_current,
       isnull(MAX(fg46_profit_forecast_pickup), 0.0) as fg46_profit_forecast_pickup,
       isnull(MAX(fg47_profit_forecast_current), 0.0) as fg47_profit_forecast_current,
       isnull(MAX(fg47_profit_forecast_pickup), 0.0) as fg47_profit_forecast_pickup,
       isnull(MAX(fg48_profit_forecast_current), 0.0) as fg48_profit_forecast_current,
       isnull(MAX(fg48_profit_forecast_pickup), 0.0) as fg48_profit_forecast_pickup,
       isnull(MAX(fg49_profit_forecast_current), 0.0) as fg49_profit_forecast_current,
       isnull(MAX(fg49_profit_forecast_pickup), 0.0) as fg49_profit_forecast_pickup,
       isnull(MAX(fg50_profit_forecast_current), 0.0) as fg50_profit_forecast_current,
       isnull(MAX(fg50_profit_forecast_pickup), 0.0) as fg50_profit_forecast_pickup,
       isnull(MAX(fg1_proPOR_forecast_current), 0.0) as fg1_proPOR_forecast_current,
       isnull(MAX(fg1_proPOR_forecast_pickup), 0.0) as fg1_proPOR_forecast_pickup,
       isnull(MAX(fg2_proPOR_forecast_current), 0.0) as fg2_proPOR_forecast_current,
       isnull(MAX(fg2_proPOR_forecast_pickup), 0.0) as fg2_proPOR_forecast_pickup,
       isnull(MAX(fg3_proPOR_forecast_current), 0.0) as fg3_proPOR_forecast_current,
       isnull(MAX(fg3_proPOR_forecast_pickup), 0.0) as fg3_proPOR_forecast_pickup,
       isnull(MAX(fg4_proPOR_forecast_current), 0.0) as fg4_proPOR_forecast_current,
       isnull(MAX(fg4_proPOR_forecast_pickup), 0.0) as fg4_proPOR_forecast_pickup,
       isnull(MAX(fg5_proPOR_forecast_current), 0.0) as fg5_proPOR_forecast_current,
       isnull(MAX(fg5_proPOR_forecast_pickup), 0.0) as fg5_proPOR_forecast_pickup,
       isnull(MAX(fg6_proPOR_forecast_current), 0.0) as fg6_proPOR_forecast_current,
       isnull(MAX(fg6_proPOR_forecast_pickup), 0.0) as fg6_proPOR_forecast_pickup,
       isnull(MAX(fg7_proPOR_forecast_current), 0.0) as fg7_proPOR_forecast_current,
       isnull(MAX(fg7_proPOR_forecast_pickup), 0.0) as fg7_proPOR_forecast_pickup,
       isnull(MAX(fg8_proPOR_forecast_current), 0.0) as fg8_proPOR_forecast_current,
       isnull(MAX(fg8_proPOR_forecast_pickup), 0.0) as fg8_proPOR_forecast_pickup,
       isnull(MAX(fg9_proPOR_forecast_current), 0.0) as fg9_proPOR_forecast_current,
       isnull(MAX(fg9_proPOR_forecast_pickup), 0.0) as fg9_proPOR_forecast_pickup,
       isnull(MAX(fg10_proPOR_forecast_current), 0.0) as fg10_proPOR_forecast_current,
       isnull(MAX(fg10_proPOR_forecast_pickup), 0.0) as fg10_proPOR_forecast_pickup,
       isnull(MAX(fg11_proPOR_forecast_current), 0.0) as fg11_proPOR_forecast_current,
       isnull(MAX(fg11_proPOR_forecast_pickup), 0.0) as fg11_proPOR_forecast_pickup,
       isnull(MAX(fg12_proPOR_forecast_current), 0.0) as fg12_proPOR_forecast_current,
       isnull(MAX(fg12_proPOR_forecast_pickup), 0.0) as fg12_proPOR_forecast_pickup,
       isnull(MAX(fg13_proPOR_forecast_current), 0.0) as fg13_proPOR_forecast_current,
       isnull(MAX(fg13_proPOR_forecast_pickup), 0.0) as fg13_proPOR_forecast_pickup,
       isnull(MAX(fg14_proPOR_forecast_current), 0.0) as fg14_proPOR_forecast_current,
       isnull(MAX(fg14_proPOR_forecast_pickup), 0.0) as fg14_proPOR_forecast_pickup,
       isnull(MAX(fg15_proPOR_forecast_current), 0.0) as fg15_proPOR_forecast_current,
       isnull(MAX(fg15_proPOR_forecast_pickup), 0.0) as fg15_proPOR_forecast_pickup,
       isnull(MAX(fg16_proPOR_forecast_current), 0.0) as fg16_proPOR_forecast_current,
       isnull(MAX(fg16_proPOR_forecast_pickup), 0.0) as fg16_proPOR_forecast_pickup,
       isnull(MAX(fg17_proPOR_forecast_current), 0.0) as fg17_proPOR_forecast_current,
       isnull(MAX(fg17_proPOR_forecast_pickup), 0.0) as fg17_proPOR_forecast_pickup,
       isnull(MAX(fg18_proPOR_forecast_current), 0.0) as fg18_proPOR_forecast_current,
       isnull(MAX(fg18_proPOR_forecast_pickup), 0.0) as fg18_proPOR_forecast_pickup,
       isnull(MAX(fg19_proPOR_forecast_current), 0.0) as fg19_proPOR_forecast_current,
       isnull(MAX(fg19_proPOR_forecast_pickup), 0.0) as fg19_proPOR_forecast_pickup,
       isnull(MAX(fg20_proPOR_forecast_current), 0.0) as fg20_proPOR_forecast_current,
       isnull(MAX(fg20_proPOR_forecast_pickup), 0.0) as fg20_proPOR_forecast_pickup,
       isnull(MAX(fg21_proPOR_forecast_current), 0.0) as fg21_proPOR_forecast_current,
       isnull(MAX(fg21_proPOR_forecast_pickup), 0.0) as fg21_proPOR_forecast_pickup,
       isnull(MAX(fg22_proPOR_forecast_current), 0.0) as fg22_proPOR_forecast_current,
       isnull(MAX(fg22_proPOR_forecast_pickup), 0.0) as fg22_proPOR_forecast_pickup,
       isnull(MAX(fg23_proPOR_forecast_current), 0.0) as fg23_proPOR_forecast_current,
       isnull(MAX(fg23_proPOR_forecast_pickup), 0.0) as fg23_proPOR_forecast_pickup,
       isnull(MAX(fg24_proPOR_forecast_current), 0.0) as fg24_proPOR_forecast_current,
       isnull(MAX(fg24_proPOR_forecast_pickup), 0.0) as fg24_proPOR_forecast_pickup,
       isnull(MAX(fg25_proPOR_forecast_current), 0.0) as fg25_proPOR_forecast_current,
       isnull(MAX(fg25_proPOR_forecast_pickup), 0.0) as fg25_proPOR_forecast_pickup,
       isnull(MAX(fg26_proPOR_forecast_current), 0.0) as fg26_proPOR_forecast_current,
       isnull(MAX(fg26_proPOR_forecast_pickup), 0.0) as fg26_proPOR_forecast_pickup,
       isnull(MAX(fg27_proPOR_forecast_current), 0.0) as fg27_proPOR_forecast_current,
       isnull(MAX(fg27_proPOR_forecast_pickup), 0.0) as fg27_proPOR_forecast_pickup,
       isnull(MAX(fg28_proPOR_forecast_current), 0.0) as fg28_proPOR_forecast_current,
       isnull(MAX(fg28_proPOR_forecast_pickup), 0.0) as fg28_proPOR_forecast_pickup,
       isnull(MAX(fg29_proPOR_forecast_current), 0.0) as fg29_proPOR_forecast_current,
       isnull(MAX(fg29_proPOR_forecast_pickup), 0.0) as fg29_proPOR_forecast_pickup,
       isnull(MAX(fg30_proPOR_forecast_current), 0.0) as fg30_proPOR_forecast_current,
       isnull(MAX(fg30_proPOR_forecast_pickup), 0.0) as fg30_proPOR_forecast_pickup,
       isnull(MAX(fg31_proPOR_forecast_current), 0.0) as fg31_proPOR_forecast_current,
       isnull(MAX(fg31_proPOR_forecast_pickup), 0.0) as fg31_proPOR_forecast_pickup,
       isnull(MAX(fg32_proPOR_forecast_current), 0.0) as fg32_proPOR_forecast_current,
       isnull(MAX(fg32_proPOR_forecast_pickup), 0.0) as fg32_proPOR_forecast_pickup,
       isnull(MAX(fg33_proPOR_forecast_current), 0.0) as fg33_proPOR_forecast_current,
       isnull(MAX(fg33_proPOR_forecast_pickup), 0.0) as fg33_proPOR_forecast_pickup,
       isnull(MAX(fg34_proPOR_forecast_current), 0.0) as fg34_proPOR_forecast_current,
       isnull(MAX(fg34_proPOR_forecast_pickup), 0.0) as fg34_proPOR_forecast_pickup,
       isnull(MAX(fg35_proPOR_forecast_current), 0.0) as fg35_proPOR_forecast_current,
       isnull(MAX(fg35_proPOR_forecast_pickup), 0.0) as fg35_proPOR_forecast_pickup,
       isnull(MAX(fg36_proPOR_forecast_current), 0.0) as fg36_proPOR_forecast_current,
       isnull(MAX(fg36_proPOR_forecast_pickup), 0.0) as fg36_proPOR_forecast_pickup,
       isnull(MAX(fg37_proPOR_forecast_current), 0.0) as fg37_proPOR_forecast_current,
       isnull(MAX(fg37_proPOR_forecast_pickup), 0.0) as fg37_proPOR_forecast_pickup,
       isnull(MAX(fg38_proPOR_forecast_current), 0.0) as fg38_proPOR_forecast_current,
       isnull(MAX(fg38_proPOR_forecast_pickup), 0.0) as fg38_proPOR_forecast_pickup,
       isnull(MAX(fg39_proPOR_forecast_current), 0.0) as fg39_proPOR_forecast_current,
       isnull(MAX(fg39_proPOR_forecast_pickup), 0.0) as fg39_proPOR_forecast_pickup,
       isnull(MAX(fg40_proPOR_forecast_current), 0.0) as fg40_proPOR_forecast_current,
       isnull(MAX(fg40_proPOR_forecast_pickup), 0.0) as fg40_proPOR_forecast_pickup,
       isnull(MAX(fg41_proPOR_forecast_current), 0.0) as fg41_proPOR_forecast_current,
       isnull(MAX(fg41_proPOR_forecast_pickup), 0.0) as fg41_proPOR_forecast_pickup,
       isnull(MAX(fg42_proPOR_forecast_current), 0.0) as fg42_proPOR_forecast_current,
       isnull(MAX(fg42_proPOR_forecast_pickup), 0.0) as fg42_proPOR_forecast_pickup,
       isnull(MAX(fg43_proPOR_forecast_current), 0.0) as fg43_proPOR_forecast_current,
       isnull(MAX(fg43_proPOR_forecast_pickup), 0.0) as fg43_proPOR_forecast_pickup,
       isnull(MAX(fg44_proPOR_forecast_current), 0.0) as fg44_proPOR_forecast_current,
       isnull(MAX(fg44_proPOR_forecast_pickup), 0.0) as fg44_proPOR_forecast_pickup,
       isnull(MAX(fg45_proPOR_forecast_current), 0.0) as fg45_proPOR_forecast_current,
       isnull(MAX(fg45_proPOR_forecast_pickup), 0.0) as fg45_proPOR_forecast_pickup,
       isnull(MAX(fg46_proPOR_forecast_current), 0.0) as fg46_proPOR_forecast_current,
       isnull(MAX(fg46_proPOR_forecast_pickup), 0.0) as fg46_proPOR_forecast_pickup,
       isnull(MAX(fg47_proPOR_forecast_current), 0.0) as fg47_proPOR_forecast_current,
       isnull(MAX(fg47_proPOR_forecast_pickup), 0.0) as fg47_proPOR_forecast_pickup,
       isnull(MAX(fg48_proPOR_forecast_current), 0.0) as fg48_proPOR_forecast_current,
       isnull(MAX(fg48_proPOR_forecast_pickup), 0.0) as fg48_proPOR_forecast_pickup,
       isnull(MAX(fg49_proPOR_forecast_current), 0.0) as fg49_proPOR_forecast_current,
       isnull(MAX(fg49_proPOR_forecast_pickup), 0.0) as fg49_proPOR_forecast_pickup,
       isnull(MAX(fg50_proPOR_forecast_current), 0.0) as fg50_proPOR_forecast_current,
       isnull(MAX(fg50_proPOR_forecast_pickup), 0.0) as fg50_proPOR_forecast_pickup
from
    (
        select occupancy_dt,
               dow,
               (case forecast_group_id
                    when @fg1 then
                        roomsoldcurrent
                   end
                   ) as fg1_roomsoldcurrent,
               (case forecast_group_id
                    when @fg1 then
                        roomssoldpickup
                   end
                   ) as fg1_roomssoldpickup,
               (case forecast_group_id
                    when @fg2 then
                        roomsoldcurrent
                   end
                   ) as fg2_roomsoldcurrent,
               (case forecast_group_id
                    when @fg2 then
                        roomssoldpickup
                   end
                   ) as fg2_roomssoldpickup,
               (case forecast_group_id
                    when @fg3 then
                        roomsoldcurrent
                   end
                   ) as fg3_roomsoldcurrent,
               (case forecast_group_id
                    when @fg3 then
                        roomssoldpickup
                   end
                   ) as fg3_roomssoldpickup,
               (case forecast_group_id
                    when @fg4 then
                        roomsoldcurrent
                   end
                   ) as fg4_roomsoldcurrent,
               (case forecast_group_id
                    when @fg4 then
                        roomssoldpickup
                   end
                   ) as fg4_roomssoldpickup,
               (case forecast_group_id
                    when @fg5 then
                        roomsoldcurrent
                   end
                   ) as fg5_roomsoldcurrent,
               (case forecast_group_id
                    when @fg5 then
                        roomssoldpickup
                   end
                   ) as fg5_roomssoldpickup,
               (case forecast_group_id
                    when @fg6 then
                        roomsoldcurrent
                   end
                   ) as fg6_roomsoldcurrent,
               (case forecast_group_id
                    when @fg6 then
                        roomssoldpickup
                   end
                   ) as fg6_roomssoldpickup,
               (case forecast_group_id
                    when @fg7 then
                        roomsoldcurrent
                   end
                   ) as fg7_roomsoldcurrent,
               (case forecast_group_id
                    when @fg7 then
                        roomssoldpickup
                   end
                   ) as fg7_roomssoldpickup,
               (case forecast_group_id
                    when @fg8 then
                        roomsoldcurrent
                   end
                   ) as fg8_roomsoldcurrent,
               (case forecast_group_id
                    when @fg8 then
                        roomssoldpickup
                   end
                   ) as fg8_roomssoldpickup,
               (case forecast_group_id
                    when @fg9 then
                        roomsoldcurrent
                   end
                   ) as fg9_roomsoldcurrent,
               (case forecast_group_id
                    when @fg9 then
                        roomssoldpickup
                   end
                   ) as fg9_roomssoldpickup,
               (case forecast_group_id
                    when @fg10 then
                        roomsoldcurrent
                   end
                   ) as fg10_roomsoldcurrent,
               (case forecast_group_id
                    when @fg10 then
                        roomssoldpickup
                   end
                   ) as fg10_roomssoldpickup,
               (case forecast_group_id
                    when @fg11 then
                        roomsoldcurrent
                   end
                   ) as fg11_roomsoldcurrent,
               (case forecast_group_id
                    when @fg11 then
                        roomssoldpickup
                   end
                   ) as fg11_roomssoldpickup,
               (case forecast_group_id
                    when @fg12 then
                        roomsoldcurrent
                   end
                   ) as fg12_roomsoldcurrent,
               (case forecast_group_id
                    when @fg12 then
                        roomssoldpickup
                   end
                   ) as fg12_roomssoldpickup,
               (case forecast_group_id
                    when @fg13 then
                        roomsoldcurrent
                   end
                   ) as fg13_roomsoldcurrent,
               (case forecast_group_id
                    when @fg13 then
                        roomssoldpickup
                   end
                   ) as fg13_roomssoldpickup,
               (case forecast_group_id
                    when @fg14 then
                        roomsoldcurrent
                   end
                   ) as fg14_roomsoldcurrent,
               (case forecast_group_id
                    when @fg14 then
                        roomssoldpickup
                   end
                   ) as fg14_roomssoldpickup,
               (case forecast_group_id
                    when @fg15 then
                        roomsoldcurrent
                   end
                   ) as fg15_roomsoldcurrent,
               (case forecast_group_id
                    when @fg15 then
                        roomssoldpickup
                   end
                   ) as fg15_roomssoldpickup,
               (case forecast_group_id
                    when @fg16 then
                        roomsoldcurrent
                   end
                   ) as fg16_roomsoldcurrent,
               (case forecast_group_id
                    when @fg16 then
                        roomssoldpickup
                   end
                   ) as fg16_roomssoldpickup,
               (case forecast_group_id
                    when @fg17 then
                        roomsoldcurrent
                   end
                   ) as fg17_roomsoldcurrent,
               (case forecast_group_id
                    when @fg17 then
                        roomssoldpickup
                   end
                   ) as fg17_roomssoldpickup,
               (case forecast_group_id
                    when @fg18 then
                        roomsoldcurrent
                   end
                   ) as fg18_roomsoldcurrent,
               (case forecast_group_id
                    when @fg18 then
                        roomssoldpickup
                   end
                   ) as fg18_roomssoldpickup,
               (case forecast_group_id
                    when @fg19 then
                        roomsoldcurrent
                   end
                   ) as fg19_roomsoldcurrent,
               (case forecast_group_id
                    when @fg19 then
                        roomssoldpickup
                   end
                   ) as fg19_roomssoldpickup,
               (case forecast_group_id
                    when @fg20 then
                        roomsoldcurrent
                   end
                   ) as fg20_roomsoldcurrent,
               (case forecast_group_id
                    when @fg20 then
                        roomssoldpickup
                   end
                   ) as fg20_roomssoldpickup,
               (case forecast_group_id
                    when @fg21 then
                        roomsoldcurrent
                   end
                   ) as fg21_roomsoldcurrent,
               (case forecast_group_id
                    when @fg21 then
                        roomssoldpickup
                   end
                   ) as fg21_roomssoldpickup,
               (case forecast_group_id
                    when @fg22 then
                        roomsoldcurrent
                   end
                   ) as fg22_roomsoldcurrent,
               (case forecast_group_id
                    when @fg22 then
                        roomssoldpickup
                   end
                   ) as fg22_roomssoldpickup,
               (case forecast_group_id
                    when @fg23 then
                        roomsoldcurrent
                   end
                   ) as fg23_roomsoldcurrent,
               (case forecast_group_id
                    when @fg23 then
                        roomssoldpickup
                   end
                   ) as fg23_roomssoldpickup,
               (case forecast_group_id
                    when @fg24 then
                        roomsoldcurrent
                   end
                   ) as fg24_roomsoldcurrent,
               (case forecast_group_id
                    when @fg24 then
                        roomssoldpickup
                   end
                   ) as fg24_roomssoldpickup,
               (case forecast_group_id
                    when @fg25 then
                        roomsoldcurrent
                   end
                   ) as fg25_roomsoldcurrent,
               (case forecast_group_id
                    when @fg25 then
                        roomssoldpickup
                   end
                   ) as fg25_roomssoldpickup,
               (case forecast_group_id
                    when @fg26 then
                        roomsoldcurrent
                   end
                   ) as fg26_roomsoldcurrent,
               (case forecast_group_id
                    when @fg26 then
                        roomssoldpickup
                   end
                   ) as fg26_roomssoldpickup,
               (case forecast_group_id
                    when @fg27 then
                        roomsoldcurrent
                   end
                   ) as fg27_roomsoldcurrent,
               (case forecast_group_id
                    when @fg27 then
                        roomssoldpickup
                   end
                   ) as fg27_roomssoldpickup,
               (case forecast_group_id
                    when @fg28 then
                        roomsoldcurrent
                   end
                   ) as fg28_roomsoldcurrent,
               (case forecast_group_id
                    when @fg28 then
                        roomssoldpickup
                   end
                   ) as fg28_roomssoldpickup,
               (case forecast_group_id
                    when @fg29 then
                        roomsoldcurrent
                   end
                   ) as fg29_roomsoldcurrent,
               (case forecast_group_id
                    when @fg29 then
                        roomssoldpickup
                   end
                   ) as fg29_roomssoldpickup,
               (case forecast_group_id
                    when @fg30 then
                        roomsoldcurrent
                   end
                   ) as fg30_roomsoldcurrent,
               (case forecast_group_id
                    when @fg30 then
                        roomssoldpickup
                   end
                   ) as fg30_roomssoldpickup,
               (case forecast_group_id
                    when @fg31 then
                        roomsoldcurrent
                   end
                   ) as fg31_roomsoldcurrent,
               (case forecast_group_id
                    when @fg31 then
                        roomssoldpickup
                   end
                   ) as fg31_roomssoldpickup,
               (case forecast_group_id
                    when @fg32 then
                        roomsoldcurrent
                   end
                   ) as fg32_roomsoldcurrent,
               (case forecast_group_id
                    when @fg32 then
                        roomssoldpickup
                   end
                   ) as fg32_roomssoldpickup,
               (case forecast_group_id
                    when @fg33 then
                        roomsoldcurrent
                   end
                   ) as fg33_roomsoldcurrent,
               (case forecast_group_id
                    when @fg33 then
                        roomssoldpickup
                   end
                   ) as fg33_roomssoldpickup,
               (case forecast_group_id
                    when @fg34 then
                        roomsoldcurrent
                   end
                   ) as fg34_roomsoldcurrent,
               (case forecast_group_id
                    when @fg34 then
                        roomssoldpickup
                   end
                   ) as fg34_roomssoldpickup,
               (case forecast_group_id
                    when @fg35 then
                        roomsoldcurrent
                   end
                   ) as fg35_roomsoldcurrent,
               (case forecast_group_id
                    when @fg35 then
                        roomssoldpickup
                   end
                   ) as fg35_roomssoldpickup,
               (case forecast_group_id
                    when @fg36 then
                        roomsoldcurrent
                   end
                   ) as fg36_roomsoldcurrent,
               (case forecast_group_id
                    when @fg36 then
                        roomssoldpickup
                   end
                   ) as fg36_roomssoldpickup,
               (case forecast_group_id
                    when @fg37 then
                        roomsoldcurrent
                   end
                   ) as fg37_roomsoldcurrent,
               (case forecast_group_id
                    when @fg37 then
                        roomssoldpickup
                   end
                   ) as fg37_roomssoldpickup,
               (case forecast_group_id
                    when @fg38 then
                        roomsoldcurrent
                   end
                   ) as fg38_roomsoldcurrent,
               (case forecast_group_id
                    when @fg38 then
                        roomssoldpickup
                   end
                   ) as fg38_roomssoldpickup,
               (case forecast_group_id
                    when @fg39 then
                        roomsoldcurrent
                   end
                   ) as fg39_roomsoldcurrent,
               (case forecast_group_id
                    when @fg39 then
                        roomssoldpickup
                   end
                   ) as fg39_roomssoldpickup,
               (case forecast_group_id
                    when @fg40 then
                        roomsoldcurrent
                   end
                   ) as fg40_roomsoldcurrent,
               (case forecast_group_id
                    when @fg40 then
                        roomssoldpickup
                   end
                   ) as fg40_roomssoldpickup,
               (case forecast_group_id
                    when @fg41 then
                        roomsoldcurrent
                   end
                   ) as fg41_roomsoldcurrent,
               (case forecast_group_id
                    when @fg41 then
                        roomssoldpickup
                   end
                   ) as fg41_roomssoldpickup,
               (case forecast_group_id
                    when @fg42 then
                        roomsoldcurrent
                   end
                   ) as fg42_roomsoldcurrent,
               (case forecast_group_id
                    when @fg42 then
                        roomssoldpickup
                   end
                   ) as fg42_roomssoldpickup,
               (case forecast_group_id
                    when @fg43 then
                        roomsoldcurrent
                   end
                   ) as fg43_roomsoldcurrent,
               (case forecast_group_id
                    when @fg43 then
                        roomssoldpickup
                   end
                   ) as fg43_roomssoldpickup,
               (case forecast_group_id
                    when @fg44 then
                        roomsoldcurrent
                   end
                   ) as fg44_roomsoldcurrent,
               (case forecast_group_id
                    when @fg44 then
                        roomssoldpickup
                   end
                   ) as fg44_roomssoldpickup,
               (case forecast_group_id
                    when @fg45 then
                        roomsoldcurrent
                   end
                   ) as fg45_roomsoldcurrent,
               (case forecast_group_id
                    when @fg45 then
                        roomssoldpickup
                   end
                   ) as fg45_roomssoldpickup,
               (case forecast_group_id
                    when @fg46 then
                        roomsoldcurrent
                   end
                   ) as fg46_roomsoldcurrent,
               (case forecast_group_id
                    when @fg46 then
                        roomssoldpickup
                   end
                   ) as fg46_roomssoldpickup,
               (case forecast_group_id
                    when @fg47 then
                        roomsoldcurrent
                   end
                   ) as fg47_roomsoldcurrent,
               (case forecast_group_id
                    when @fg47 then
                        roomssoldpickup
                   end
                   ) as fg47_roomssoldpickup,
               (case forecast_group_id
                    when @fg48 then
                        roomsoldcurrent
                   end
                   ) as fg48_roomsoldcurrent,
               (case forecast_group_id
                    when @fg48 then
                        roomssoldpickup
                   end
                   ) as fg48_roomssoldpickup,
               (case forecast_group_id
                    when @fg49 then
                        roomsoldcurrent
                   end
                   ) as fg49_roomsoldcurrent,
               (case forecast_group_id
                    when @fg49 then
                        roomssoldpickup
                   end
                   ) as fg49_roomssoldpickup,
               (case forecast_group_id
                    when @fg50 then
                        roomsoldcurrent
                   end
                   ) as fg50_roomsoldcurrent,
               (case forecast_group_id
                    when @fg50 then
                        roomssoldpickup
                   end
                   ) as fg50_roomssoldpickup,
               (case forecast_group_id
                    when @fg1 then
                        occfcstcurrent
                   end
                   ) as fg1_occfcstcurrent,
               (case forecast_group_id
                    when @fg1 then
                        occfcstpickup
                   end
                   ) as fg1_occfcstpickup,
               (case forecast_group_id
                    when @fg2 then
                        occfcstcurrent
                   end
                   ) as fg2_occfcstcurrent,
               (case forecast_group_id
                    when @fg2 then
                        occfcstpickup
                   end
                   ) as fg2_occfcstpickup,
               (case forecast_group_id
                    when @fg3 then
                        occfcstcurrent
                   end
                   ) as fg3_occfcstcurrent,
               (case forecast_group_id
                    when @fg3 then
                        occfcstpickup
                   end
                   ) as fg3_occfcstpickup,
               (case forecast_group_id
                    when @fg4 then
                        occfcstcurrent
                   end
                   ) as fg4_occfcstcurrent,
               (case forecast_group_id
                    when @fg4 then
                        occfcstpickup
                   end
                   ) as fg4_occfcstpickup,
               (case forecast_group_id
                    when @fg5 then
                        occfcstcurrent
                   end
                   ) as fg5_occfcstcurrent,
               (case forecast_group_id
                    when @fg5 then
                        occfcstpickup
                   end
                   ) as fg5_occfcstpickup,
               (case forecast_group_id
                    when @fg6 then
                        occfcstcurrent
                   end
                   ) as fg6_occfcstcurrent,
               (case forecast_group_id
                    when @fg6 then
                        occfcstpickup
                   end
                   ) as fg6_occfcstpickup,
               (case forecast_group_id
                    when @fg7 then
                        occfcstcurrent
                   end
                   ) as fg7_occfcstcurrent,
               (case forecast_group_id
                    when @fg7 then
                        occfcstpickup
                   end
                   ) as fg7_occfcstpickup,
               (case forecast_group_id
                    when @fg8 then
                        occfcstcurrent
                   end
                   ) as fg8_occfcstcurrent,
               (case forecast_group_id
                    when @fg8 then
                        occfcstpickup
                   end
                   ) as fg8_occfcstpickup,
               (case forecast_group_id
                    when @fg9 then
                        occfcstcurrent
                   end
                   ) as fg9_occfcstcurrent,
               (case forecast_group_id
                    when @fg9 then
                        occfcstpickup
                   end
                   ) as fg9_occfcstpickup,
               (case forecast_group_id
                    when @fg10 then
                        occfcstcurrent
                   end
                   ) as fg10_occfcstcurrent,
               (case forecast_group_id
                    when @fg10 then
                        occfcstpickup
                   end
                   ) as fg10_occfcstpickup,
               (case forecast_group_id
                    when @fg11 then
                        occfcstcurrent
                   end
                   ) as fg11_occfcstcurrent,
               (case forecast_group_id
                    when @fg11 then
                        occfcstpickup
                   end
                   ) as fg11_occfcstpickup,
               (case forecast_group_id
                    when @fg12 then
                        occfcstcurrent
                   end
                   ) as fg12_occfcstcurrent,
               (case forecast_group_id
                    when @fg12 then
                        occfcstpickup
                   end
                   ) as fg12_occfcstpickup,
               (case forecast_group_id
                    when @fg13 then
                        occfcstcurrent
                   end
                   ) as fg13_occfcstcurrent,
               (case forecast_group_id
                    when @fg13 then
                        occfcstpickup
                   end
                   ) as fg13_occfcstpickup,
               (case forecast_group_id
                    when @fg14 then
                        occfcstcurrent
                   end
                   ) as fg14_occfcstcurrent,
               (case forecast_group_id
                    when @fg14 then
                        occfcstpickup
                   end
                   ) as fg14_occfcstpickup,
               (case forecast_group_id
                    when @fg15 then
                        occfcstcurrent
                   end
                   ) as fg15_occfcstcurrent,
               (case forecast_group_id
                    when @fg15 then
                        occfcstpickup
                   end
                   ) as fg15_occfcstpickup,
               (case forecast_group_id
                    when @fg16 then
                        occfcstcurrent
                   end
                   ) as fg16_occfcstcurrent,
               (case forecast_group_id
                    when @fg16 then
                        occfcstpickup
                   end
                   ) as fg16_occfcstpickup,
               (case forecast_group_id
                    when @fg17 then
                        occfcstcurrent
                   end
                   ) as fg17_occfcstcurrent,
               (case forecast_group_id
                    when @fg17 then
                        occfcstpickup
                   end
                   ) as fg17_occfcstpickup,
               (case forecast_group_id
                    when @fg18 then
                        occfcstcurrent
                   end
                   ) as fg18_occfcstcurrent,
               (case forecast_group_id
                    when @fg18 then
                        occfcstpickup
                   end
                   ) as fg18_occfcstpickup,
               (case forecast_group_id
                    when @fg19 then
                        occfcstcurrent
                   end
                   ) as fg19_occfcstcurrent,
               (case forecast_group_id
                    when @fg19 then
                        occfcstpickup
                   end
                   ) as fg19_occfcstpickup,
               (case forecast_group_id
                    when @fg20 then
                        occfcstcurrent
                   end
                   ) as fg20_occfcstcurrent,
               (case forecast_group_id
                    when @fg20 then
                        occfcstpickup
                   end
                   ) as fg20_occfcstpickup,
               (case forecast_group_id
                    when @fg21 then
                        occfcstcurrent
                   end
                   ) as fg21_occfcstcurrent,
               (case forecast_group_id
                    when @fg21 then
                        occfcstpickup
                   end
                   ) as fg21_occfcstpickup,
               (case forecast_group_id
                    when @fg22 then
                        occfcstcurrent
                   end
                   ) as fg22_occfcstcurrent,
               (case forecast_group_id
                    when @fg22 then
                        occfcstpickup
                   end
                   ) as fg22_occfcstpickup,
               (case forecast_group_id
                    when @fg23 then
                        occfcstcurrent
                   end
                   ) as fg23_occfcstcurrent,
               (case forecast_group_id
                    when @fg23 then
                        occfcstpickup
                   end
                   ) as fg23_occfcstpickup,
               (case forecast_group_id
                    when @fg24 then
                        occfcstcurrent
                   end
                   ) as fg24_occfcstcurrent,
               (case forecast_group_id
                    when @fg24 then
                        occfcstpickup
                   end
                   ) as fg24_occfcstpickup,
               (case forecast_group_id
                    when @fg25 then
                        occfcstcurrent
                   end
                   ) as fg25_occfcstcurrent,
               (case forecast_group_id
                    when @fg25 then
                        occfcstpickup
                   end
                   ) as fg25_occfcstpickup,
               (case forecast_group_id
                    when @fg26 then
                        occfcstcurrent
                   end
                   ) as fg26_occfcstcurrent,
               (case forecast_group_id
                    when @fg26 then
                        occfcstpickup
                   end
                   ) as fg26_occfcstpickup,
               (case forecast_group_id
                    when @fg27 then
                        occfcstcurrent
                   end
                   ) as fg27_occfcstcurrent,
               (case forecast_group_id
                    when @fg27 then
                        occfcstpickup
                   end
                   ) as fg27_occfcstpickup,
               (case forecast_group_id
                    when @fg28 then
                        occfcstcurrent
                   end
                   ) as fg28_occfcstcurrent,
               (case forecast_group_id
                    when @fg28 then
                        occfcstpickup
                   end
                   ) as fg28_occfcstpickup,
               (case forecast_group_id
                    when @fg29 then
                        occfcstcurrent
                   end
                   ) as fg29_occfcstcurrent,
               (case forecast_group_id
                    when @fg29 then
                        occfcstpickup
                   end
                   ) as fg29_occfcstpickup,
               (case forecast_group_id
                    when @fg30 then
                        occfcstcurrent
                   end
                   ) as fg30_occfcstcurrent,
               (case forecast_group_id
                    when @fg30 then
                        occfcstpickup
                   end
                   ) as fg30_occfcstpickup,
               (case forecast_group_id
                    when @fg31 then
                        occfcstcurrent
                   end
                   ) as fg31_occfcstcurrent,
               (case forecast_group_id
                    when @fg31 then
                        occfcstpickup
                   end
                   ) as fg31_occfcstpickup,
               (case forecast_group_id
                    when @fg32 then
                        occfcstcurrent
                   end
                   ) as fg32_occfcstcurrent,
               (case forecast_group_id
                    when @fg32 then
                        occfcstpickup
                   end
                   ) as fg32_occfcstpickup,
               (case forecast_group_id
                    when @fg33 then
                        occfcstcurrent
                   end
                   ) as fg33_occfcstcurrent,
               (case forecast_group_id
                    when @fg33 then
                        occfcstpickup
                   end
                   ) as fg33_occfcstpickup,
               (case forecast_group_id
                    when @fg34 then
                        occfcstcurrent
                   end
                   ) as fg34_occfcstcurrent,
               (case forecast_group_id
                    when @fg34 then
                        occfcstpickup
                   end
                   ) as fg34_occfcstpickup,
               (case forecast_group_id
                    when @fg35 then
                        occfcstcurrent
                   end
                   ) as fg35_occfcstcurrent,
               (case forecast_group_id
                    when @fg35 then
                        occfcstpickup
                   end
                   ) as fg35_occfcstpickup,
               (case forecast_group_id
                    when @fg36 then
                        occfcstcurrent
                   end
                   ) as fg36_occfcstcurrent,
               (case forecast_group_id
                    when @fg36 then
                        occfcstpickup
                   end
                   ) as fg36_occfcstpickup,
               (case forecast_group_id
                    when @fg37 then
                        occfcstcurrent
                   end
                   ) as fg37_occfcstcurrent,
               (case forecast_group_id
                    when @fg37 then
                        occfcstpickup
                   end
                   ) as fg37_occfcstpickup,
               (case forecast_group_id
                    when @fg38 then
                        occfcstcurrent
                   end
                   ) as fg38_occfcstcurrent,
               (case forecast_group_id
                    when @fg38 then
                        occfcstpickup
                   end
                   ) as fg38_occfcstpickup,
               (case forecast_group_id
                    when @fg39 then
                        occfcstcurrent
                   end
                   ) as fg39_occfcstcurrent,
               (case forecast_group_id
                    when @fg39 then
                        occfcstpickup
                   end
                   ) as fg39_occfcstpickup,
               (case forecast_group_id
                    when @fg40 then
                        occfcstcurrent
                   end
                   ) as fg40_occfcstcurrent,
               (case forecast_group_id
                    when @fg40 then
                        occfcstpickup
                   end
                   ) as fg40_occfcstpickup,
               (case forecast_group_id
                    when @fg41 then
                        occfcstcurrent
                   end
                   ) as fg41_occfcstcurrent,
               (case forecast_group_id
                    when @fg41 then
                        occfcstpickup
                   end
                   ) as fg41_occfcstpickup,
               (case forecast_group_id
                    when @fg42 then
                        occfcstcurrent
                   end
                   ) as fg42_occfcstcurrent,
               (case forecast_group_id
                    when @fg42 then
                        occfcstpickup
                   end
                   ) as fg42_occfcstpickup,
               (case forecast_group_id
                    when @fg43 then
                        occfcstcurrent
                   end
                   ) as fg43_occfcstcurrent,
               (case forecast_group_id
                    when @fg43 then
                        occfcstpickup
                   end
                   ) as fg43_occfcstpickup,
               (case forecast_group_id
                    when @fg44 then
                        occfcstcurrent
                   end
                   ) as fg44_occfcstcurrent,
               (case forecast_group_id
                    when @fg44 then
                        occfcstpickup
                   end
                   ) as fg44_occfcstpickup,
               (case forecast_group_id
                    when @fg45 then
                        occfcstcurrent
                   end
                   ) as fg45_occfcstcurrent,
               (case forecast_group_id
                    when @fg45 then
                        occfcstpickup
                   end
                   ) as fg45_occfcstpickup,
               (case forecast_group_id
                    when @fg46 then
                        occfcstcurrent
                   end
                   ) as fg46_occfcstcurrent,
               (case forecast_group_id
                    when @fg46 then
                        occfcstpickup
                   end
                   ) as fg46_occfcstpickup,
               (case forecast_group_id
                    when @fg47 then
                        occfcstcurrent
                   end
                   ) as fg47_occfcstcurrent,
               (case forecast_group_id
                    when @fg47 then
                        occfcstpickup
                   end
                   ) as fg47_occfcstpickup,
               (case forecast_group_id
                    when @fg48 then
                        occfcstcurrent
                   end
                   ) as fg48_occfcstcurrent,
               (case forecast_group_id
                    when @fg48 then
                        occfcstpickup
                   end
                   ) as fg48_occfcstpickup,
               (case forecast_group_id
                    when @fg49 then
                        occfcstcurrent
                   end
                   ) as fg49_occfcstcurrent,
               (case forecast_group_id
                    when @fg49 then
                        occfcstpickup
                   end
                   ) as fg49_occfcstpickup,
               (case forecast_group_id
                    when @fg50 then
                        occfcstcurrent
                   end
                   ) as fg50_occfcstcurrent,
               (case forecast_group_id
                    when @fg50 then
                        occfcstpickup
                   end
                   ) as fg50_occfcstpickup,
               (case forecast_group_id
                    when @fg1 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg1_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg1 then
                        bookedroomrevenuepickup
                   end
                   ) as fg1_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg2 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg2_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg2 then
                        bookedroomrevenuepickup
                   end
                   ) as fg2_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg3 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg3_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg3 then
                        bookedroomrevenuepickup
                   end
                   ) as fg3_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg4 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg4_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg4 then
                        bookedroomrevenuepickup
                   end
                   ) as fg4_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg5 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg5_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg5 then
                        bookedroomrevenuepickup
                   end
                   ) as fg5_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg6 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg6_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg6 then
                        bookedroomrevenuepickup
                   end
                   ) as fg6_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg7 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg7_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg7 then
                        bookedroomrevenuepickup
                   end
                   ) as fg7_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg8 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg8_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg8 then
                        bookedroomrevenuepickup
                   end
                   ) as fg8_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg9 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg9_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg9 then
                        bookedroomrevenuepickup
                   end
                   ) as fg9_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg10 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg10_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg10 then
                        bookedroomrevenuepickup
                   end
                   ) as fg10_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg11 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg11_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg11 then
                        bookedroomrevenuepickup
                   end
                   ) as fg11_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg12 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg12_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg12 then
                        bookedroomrevenuepickup
                   end
                   ) as fg12_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg13 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg13_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg13 then
                        bookedroomrevenuepickup
                   end
                   ) as fg13_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg14 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg14_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg14 then
                        bookedroomrevenuepickup
                   end
                   ) as fg14_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg15 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg15_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg15 then
                        bookedroomrevenuepickup
                   end
                   ) as fg15_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg16 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg16_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg16 then
                        bookedroomrevenuepickup
                   end
                   ) as fg16_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg17 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg17_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg17 then
                        bookedroomrevenuepickup
                   end
                   ) as fg17_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg18 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg18_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg18 then
                        bookedroomrevenuepickup
                   end
                   ) as fg18_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg19 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg19_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg19 then
                        bookedroomrevenuepickup
                   end
                   ) as fg19_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg20 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg20_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg20 then
                        bookedroomrevenuepickup
                   end
                   ) as fg20_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg21 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg21_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg21 then
                        bookedroomrevenuepickup
                   end
                   ) as fg21_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg22 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg22_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg22 then
                        bookedroomrevenuepickup
                   end
                   ) as fg22_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg23 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg23_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg23 then
                        bookedroomrevenuepickup
                   end
                   ) as fg23_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg24 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg24_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg24 then
                        bookedroomrevenuepickup
                   end
                   ) as fg24_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg25 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg25_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg25 then
                        bookedroomrevenuepickup
                   end
                   ) as fg25_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg26 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg26_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg26 then
                        bookedroomrevenuepickup
                   end
                   ) as fg26_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg27 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg27_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg27 then
                        bookedroomrevenuepickup
                   end
                   ) as fg27_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg28 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg28_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg28 then
                        bookedroomrevenuepickup
                   end
                   ) as fg28_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg29 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg29_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg29 then
                        bookedroomrevenuepickup
                   end
                   ) as fg29_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg30 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg30_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg30 then
                        bookedroomrevenuepickup
                   end
                   ) as fg30_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg31 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg31_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg31 then
                        bookedroomrevenuepickup
                   end
                   ) as fg31_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg32 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg32_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg32 then
                        bookedroomrevenuepickup
                   end
                   ) as fg32_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg33 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg33_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg33 then
                        bookedroomrevenuepickup
                   end
                   ) as fg33_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg34 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg34_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg34 then
                        bookedroomrevenuepickup
                   end
                   ) as fg34_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg35 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg35_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg35 then
                        bookedroomrevenuepickup
                   end
                   ) as fg35_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg36 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg36_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg36 then
                        bookedroomrevenuepickup
                   end
                   ) as fg36_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg37 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg37_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg37 then
                        bookedroomrevenuepickup
                   end
                   ) as fg37_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg38 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg38_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg38 then
                        bookedroomrevenuepickup
                   end
                   ) as fg38_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg39 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg39_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg39 then
                        bookedroomrevenuepickup
                   end
                   ) as fg39_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg40 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg40_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg40 then
                        bookedroomrevenuepickup
                   end
                   ) as fg40_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg41 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg41_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg41 then
                        bookedroomrevenuepickup
                   end
                   ) as fg41_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg42 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg42_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg42 then
                        bookedroomrevenuepickup
                   end
                   ) as fg42_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg43 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg43_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg43 then
                        bookedroomrevenuepickup
                   end
                   ) as fg43_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg44 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg44_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg44 then
                        bookedroomrevenuepickup
                   end
                   ) as fg44_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg45 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg45_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg45 then
                        bookedroomrevenuepickup
                   end
                   ) as fg45_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg46 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg46_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg46 then
                        bookedroomrevenuepickup
                   end
                   ) as fg46_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg47 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg47_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg47 then
                        bookedroomrevenuepickup
                   end
                   ) as fg47_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg48 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg48_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg48 then
                        bookedroomrevenuepickup
                   end
                   ) as fg48_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg49 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg49_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg49 then
                        bookedroomrevenuepickup
                   end
                   ) as fg49_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg50 then
                        bookedroomrevenuecurrent
                   end
                   ) as fg50_bookedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg50 then
                        bookedroomrevenuepickup
                   end
                   ) as fg50_bookedroomrevenuepickup,
               (case forecast_group_id
                    when @fg1 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg1_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg1 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg1_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg2 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg2_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg2 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg2_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg3 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg3_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg3 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg3_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg4 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg4_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg4 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg4_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg5 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg5_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg5 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg5_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg6 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg6_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg6 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg6_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg7 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg7_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg7 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg7_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg8 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg8_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg8 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg8_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg9 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg9_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg9 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg9_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg10 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg10_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg10 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg10_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg11 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg11_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg11 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg11_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg12 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg12_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg12 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg12_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg13 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg13_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg13 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg13_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg14 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg14_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg14 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg14_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg15 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg15_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg15 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg15_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg16 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg16_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg16 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg16_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg17 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg17_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg17 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg17_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg18 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg18_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg18 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg18_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg19 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg19_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg19 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg19_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg20 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg20_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg20 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg20_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg21 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg21_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg21 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg21_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg22 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg22_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg22 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg22_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg23 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg23_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg23 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg23_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg24 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg24_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg24 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg24_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg25 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg25_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg25 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg25_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg26 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg26_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg26 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg26_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg27 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg27_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg27 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg27_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg28 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg28_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg28 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg28_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg29 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg29_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg29 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg29_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg30 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg30_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg30 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg30_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg31 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg31_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg31 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg31_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg32 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg32_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg32 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg32_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg33 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg33_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg33 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg33_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg34 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg34_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg34 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg34_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg35 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg35_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg35 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg35_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg36 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg36_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg36 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg36_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg37 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg37_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg37 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg37_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg38 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg38_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg38 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg38_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg39 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg39_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg39 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg39_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg40 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg40_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg40 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg40_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg41 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg41_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg41 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg41_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg42 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg42_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg42 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg42_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg43 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg43_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg43 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg43_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg44 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg44_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg44 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg44_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg45 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg45_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg45 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg45_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg46 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg46_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg46 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg46_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg47 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg47_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg47 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg47_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg48 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg48_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg48 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg48_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg49 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg49_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg49 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg49_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg50 then
                        fcstedroomrevenuecurrent
                   end
                   ) as fg50_fcstedroomrevenuecurrent,
               (case forecast_group_id
                    when @fg50 then
                        fcstedroomrevenuepickup
                   end
                   ) as fg50_fcstedroomrevenuepickup,
               (case forecast_group_id
                    when @fg1 then
                        bookedadrcurrent
                   end
                   ) as fg1_bookedadrcurrent,
               (case forecast_group_id
                    when @fg1 then
                        bookedadrpickup
                   end
                   ) as fg1_bookedadrpickup,
               (case forecast_group_id
                    when @fg2 then
                        bookedadrcurrent
                   end
                   ) as fg2_bookedadrcurrent,
               (case forecast_group_id
                    when @fg2 then
                        bookedadrpickup
                   end
                   ) as fg2_bookedadrpickup,
               (case forecast_group_id
                    when @fg3 then
                        bookedadrcurrent
                   end
                   ) as fg3_bookedadrcurrent,
               (case forecast_group_id
                    when @fg3 then
                        bookedadrpickup
                   end
                   ) as fg3_bookedadrpickup,
               (case forecast_group_id
                    when @fg4 then
                        bookedadrcurrent
                   end
                   ) as fg4_bookedadrcurrent,
               (case forecast_group_id
                    when @fg4 then
                        bookedadrpickup
                   end
                   ) as fg4_bookedadrpickup,
               (case forecast_group_id
                    when @fg5 then
                        bookedadrcurrent
                   end
                   ) as fg5_bookedadrcurrent,
               (case forecast_group_id
                    when @fg5 then
                        bookedadrpickup
                   end
                   ) as fg5_bookedadrpickup,
               (case forecast_group_id
                    when @fg6 then
                        bookedadrcurrent
                   end
                   ) as fg6_bookedadrcurrent,
               (case forecast_group_id
                    when @fg6 then
                        bookedadrpickup
                   end
                   ) as fg6_bookedadrpickup,
               (case forecast_group_id
                    when @fg7 then
                        bookedadrcurrent
                   end
                   ) as fg7_bookedadrcurrent,
               (case forecast_group_id
                    when @fg7 then
                        bookedadrpickup
                   end
                   ) as fg7_bookedadrpickup,
               (case forecast_group_id
                    when @fg8 then
                        bookedadrcurrent
                   end
                   ) as fg8_bookedadrcurrent,
               (case forecast_group_id
                    when @fg8 then
                        bookedadrpickup
                   end
                   ) as fg8_bookedadrpickup,
               (case forecast_group_id
                    when @fg9 then
                        bookedadrcurrent
                   end
                   ) as fg9_bookedadrcurrent,
               (case forecast_group_id
                    when @fg9 then
                        bookedadrpickup
                   end
                   ) as fg9_bookedadrpickup,
               (case forecast_group_id
                    when @fg10 then
                        bookedadrcurrent
                   end
                   ) as fg10_bookedadrcurrent,
               (case forecast_group_id
                    when @fg10 then
                        bookedadrpickup
                   end
                   ) as fg10_bookedadrpickup,
               (case forecast_group_id
                    when @fg11 then
                        bookedadrcurrent
                   end
                   ) as fg11_bookedadrcurrent,
               (case forecast_group_id
                    when @fg11 then
                        bookedadrpickup
                   end
                   ) as fg11_bookedadrpickup,
               (case forecast_group_id
                    when @fg12 then
                        bookedadrcurrent
                   end
                   ) as fg12_bookedadrcurrent,
               (case forecast_group_id
                    when @fg12 then
                        bookedadrpickup
                   end
                   ) as fg12_bookedadrpickup,
               (case forecast_group_id
                    when @fg13 then
                        bookedadrcurrent
                   end
                   ) as fg13_bookedadrcurrent,
               (case forecast_group_id
                    when @fg13 then
                        bookedadrpickup
                   end
                   ) as fg13_bookedadrpickup,
               (case forecast_group_id
                    when @fg14 then
                        bookedadrcurrent
                   end
                   ) as fg14_bookedadrcurrent,
               (case forecast_group_id
                    when @fg14 then
                        bookedadrpickup
                   end
                   ) as fg14_bookedadrpickup,
               (case forecast_group_id
                    when @fg15 then
                        bookedadrcurrent
                   end
                   ) as fg15_bookedadrcurrent,
               (case forecast_group_id
                    when @fg15 then
                        bookedadrpickup
                   end
                   ) as fg15_bookedadrpickup,
               (case forecast_group_id
                    when @fg16 then
                        bookedadrcurrent
                   end
                   ) as fg16_bookedadrcurrent,
               (case forecast_group_id
                    when @fg16 then
                        bookedadrpickup
                   end
                   ) as fg16_bookedadrpickup,
               (case forecast_group_id
                    when @fg17 then
                        bookedadrcurrent
                   end
                   ) as fg17_bookedadrcurrent,
               (case forecast_group_id
                    when @fg17 then
                        bookedadrpickup
                   end
                   ) as fg17_bookedadrpickup,
               (case forecast_group_id
                    when @fg18 then
                        bookedadrcurrent
                   end
                   ) as fg18_bookedadrcurrent,
               (case forecast_group_id
                    when @fg18 then
                        bookedadrpickup
                   end
                   ) as fg18_bookedadrpickup,
               (case forecast_group_id
                    when @fg19 then
                        bookedadrcurrent
                   end
                   ) as fg19_bookedadrcurrent,
               (case forecast_group_id
                    when @fg19 then
                        bookedadrpickup
                   end
                   ) as fg19_bookedadrpickup,
               (case forecast_group_id
                    when @fg20 then
                        bookedadrcurrent
                   end
                   ) as fg20_bookedadrcurrent,
               (case forecast_group_id
                    when @fg20 then
                        bookedadrpickup
                   end
                   ) as fg20_bookedadrpickup,
               (case forecast_group_id
                    when @fg21 then
                        bookedadrcurrent
                   end
                   ) as fg21_bookedadrcurrent,
               (case forecast_group_id
                    when @fg21 then
                        bookedadrpickup
                   end
                   ) as fg21_bookedadrpickup,
               (case forecast_group_id
                    when @fg22 then
                        bookedadrcurrent
                   end
                   ) as fg22_bookedadrcurrent,
               (case forecast_group_id
                    when @fg22 then
                        bookedadrpickup
                   end
                   ) as fg22_bookedadrpickup,
               (case forecast_group_id
                    when @fg23 then
                        bookedadrcurrent
                   end
                   ) as fg23_bookedadrcurrent,
               (case forecast_group_id
                    when @fg23 then
                        bookedadrpickup
                   end
                   ) as fg23_bookedadrpickup,
               (case forecast_group_id
                    when @fg24 then
                        bookedadrcurrent
                   end
                   ) as fg24_bookedadrcurrent,
               (case forecast_group_id
                    when @fg24 then
                        bookedadrpickup
                   end
                   ) as fg24_bookedadrpickup,
               (case forecast_group_id
                    when @fg25 then
                        bookedadrcurrent
                   end
                   ) as fg25_bookedadrcurrent,
               (case forecast_group_id
                    when @fg25 then
                        bookedadrpickup
                   end
                   ) as fg25_bookedadrpickup,
               (case forecast_group_id
                    when @fg26 then
                        bookedadrcurrent
                   end
                   ) as fg26_bookedadrcurrent,
               (case forecast_group_id
                    when @fg26 then
                        bookedadrpickup
                   end
                   ) as fg26_bookedadrpickup,
               (case forecast_group_id
                    when @fg27 then
                        bookedadrcurrent
                   end
                   ) as fg27_bookedadrcurrent,
               (case forecast_group_id
                    when @fg27 then
                        bookedadrpickup
                   end
                   ) as fg27_bookedadrpickup,
               (case forecast_group_id
                    when @fg28 then
                        bookedadrcurrent
                   end
                   ) as fg28_bookedadrcurrent,
               (case forecast_group_id
                    when @fg28 then
                        bookedadrpickup
                   end
                   ) as fg28_bookedadrpickup,
               (case forecast_group_id
                    when @fg29 then
                        bookedadrcurrent
                   end
                   ) as fg29_bookedadrcurrent,
               (case forecast_group_id
                    when @fg29 then
                        bookedadrpickup
                   end
                   ) as fg29_bookedadrpickup,
               (case forecast_group_id
                    when @fg30 then
                        bookedadrcurrent
                   end
                   ) as fg30_bookedadrcurrent,
               (case forecast_group_id
                    when @fg30 then
                        bookedadrpickup
                   end
                   ) as fg30_bookedadrpickup,
               (case forecast_group_id
                    when @fg31 then
                        bookedadrcurrent
                   end
                   ) as fg31_bookedadrcurrent,
               (case forecast_group_id
                    when @fg31 then
                        bookedadrpickup
                   end
                   ) as fg31_bookedadrpickup,
               (case forecast_group_id
                    when @fg32 then
                        bookedadrcurrent
                   end
                   ) as fg32_bookedadrcurrent,
               (case forecast_group_id
                    when @fg32 then
                        bookedadrpickup
                   end
                   ) as fg32_bookedadrpickup,
               (case forecast_group_id
                    when @fg33 then
                        bookedadrcurrent
                   end
                   ) as fg33_bookedadrcurrent,
               (case forecast_group_id
                    when @fg33 then
                        bookedadrpickup
                   end
                   ) as fg33_bookedadrpickup,
               (case forecast_group_id
                    when @fg34 then
                        bookedadrcurrent
                   end
                   ) as fg34_bookedadrcurrent,
               (case forecast_group_id
                    when @fg34 then
                        bookedadrpickup
                   end
                   ) as fg34_bookedadrpickup,
               (case forecast_group_id
                    when @fg35 then
                        bookedadrcurrent
                   end
                   ) as fg35_bookedadrcurrent,
               (case forecast_group_id
                    when @fg35 then
                        bookedadrpickup
                   end
                   ) as fg35_bookedadrpickup,
               (case forecast_group_id
                    when @fg36 then
                        bookedadrcurrent
                   end
                   ) as fg36_bookedadrcurrent,
               (case forecast_group_id
                    when @fg36 then
                        bookedadrpickup
                   end
                   ) as fg36_bookedadrpickup,
               (case forecast_group_id
                    when @fg37 then
                        bookedadrcurrent
                   end
                   ) as fg37_bookedadrcurrent,
               (case forecast_group_id
                    when @fg37 then
                        bookedadrpickup
                   end
                   ) as fg37_bookedadrpickup,
               (case forecast_group_id
                    when @fg38 then
                        bookedadrcurrent
                   end
                   ) as fg38_bookedadrcurrent,
               (case forecast_group_id
                    when @fg38 then
                        bookedadrpickup
                   end
                   ) as fg38_bookedadrpickup,
               (case forecast_group_id
                    when @fg39 then
                        bookedadrcurrent
                   end
                   ) as fg39_bookedadrcurrent,
               (case forecast_group_id
                    when @fg39 then
                        bookedadrpickup
                   end
                   ) as fg39_bookedadrpickup,
               (case forecast_group_id
                    when @fg40 then
                        bookedadrcurrent
                   end
                   ) as fg40_bookedadrcurrent,
               (case forecast_group_id
                    when @fg40 then
                        bookedadrpickup
                   end
                   ) as fg40_bookedadrpickup,
               (case forecast_group_id
                    when @fg41 then
                        bookedadrcurrent
                   end
                   ) as fg41_bookedadrcurrent,
               (case forecast_group_id
                    when @fg41 then
                        bookedadrpickup
                   end
                   ) as fg41_bookedadrpickup,
               (case forecast_group_id
                    when @fg42 then
                        bookedadrcurrent
                   end
                   ) as fg42_bookedadrcurrent,
               (case forecast_group_id
                    when @fg42 then
                        bookedadrpickup
                   end
                   ) as fg42_bookedadrpickup,
               (case forecast_group_id
                    when @fg43 then
                        bookedadrcurrent
                   end
                   ) as fg43_bookedadrcurrent,
               (case forecast_group_id
                    when @fg43 then
                        bookedadrpickup
                   end
                   ) as fg43_bookedadrpickup,
               (case forecast_group_id
                    when @fg44 then
                        bookedadrcurrent
                   end
                   ) as fg44_bookedadrcurrent,
               (case forecast_group_id
                    when @fg44 then
                        bookedadrpickup
                   end
                   ) as fg44_bookedadrpickup,
               (case forecast_group_id
                    when @fg45 then
                        bookedadrcurrent
                   end
                   ) as fg45_bookedadrcurrent,
               (case forecast_group_id
                    when @fg45 then
                        bookedadrpickup
                   end
                   ) as fg45_bookedadrpickup,
               (case forecast_group_id
                    when @fg46 then
                        bookedadrcurrent
                   end
                   ) as fg46_bookedadrcurrent,
               (case forecast_group_id
                    when @fg46 then
                        bookedadrpickup
                   end
                   ) as fg46_bookedadrpickup,
               (case forecast_group_id
                    when @fg47 then
                        bookedadrcurrent
                   end
                   ) as fg47_bookedadrcurrent,
               (case forecast_group_id
                    when @fg47 then
                        bookedadrpickup
                   end
                   ) as fg47_bookedadrpickup,
               (case forecast_group_id
                    when @fg48 then
                        bookedadrcurrent
                   end
                   ) as fg48_bookedadrcurrent,
               (case forecast_group_id
                    when @fg48 then
                        bookedadrpickup
                   end
                   ) as fg48_bookedadrpickup,
               (case forecast_group_id
                    when @fg49 then
                        bookedadrcurrent
                   end
                   ) as fg49_bookedadrcurrent,
               (case forecast_group_id
                    when @fg49 then
                        bookedadrpickup
                   end
                   ) as fg49_bookedadrpickup,
               (case forecast_group_id
                    when @fg50 then
                        bookedadrcurrent
                   end
                   ) as fg50_bookedadrcurrent,
               (case forecast_group_id
                    when @fg50 then
                        bookedadrpickup
                   end
                   ) as fg50_bookedadrpickup,
               (case forecast_group_id
                    when @fg1 then
                        fcstedadrcurrent
                   end
                   ) as fg1_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg1 then
                        fcstedadrpickup
                   end
                   ) as fg1_fcstedadrpickup,
               (case forecast_group_id
                    when @fg2 then
                        fcstedadrcurrent
                   end
                   ) as fg2_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg2 then
                        fcstedadrpickup
                   end
                   ) as fg2_fcstedadrpickup,
               (case forecast_group_id
                    when @fg3 then
                        fcstedadrcurrent
                   end
                   ) as fg3_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg3 then
                        fcstedadrpickup
                   end
                   ) as fg3_fcstedadrpickup,
               (case forecast_group_id
                    when @fg4 then
                        fcstedadrcurrent
                   end
                   ) as fg4_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg4 then
                        fcstedadrpickup
                   end
                   ) as fg4_fcstedadrpickup,
               (case forecast_group_id
                    when @fg5 then
                        fcstedadrcurrent
                   end
                   ) as fg5_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg5 then
                        fcstedadrpickup
                   end
                   ) as fg5_fcstedadrpickup,
               (case forecast_group_id
                    when @fg6 then
                        fcstedadrcurrent
                   end
                   ) as fg6_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg6 then
                        fcstedadrpickup
                   end
                   ) as fg6_fcstedadrpickup,
               (case forecast_group_id
                    when @fg7 then
                        fcstedadrcurrent
                   end
                   ) as fg7_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg7 then
                        fcstedadrpickup
                   end
                   ) as fg7_fcstedadrpickup,
               (case forecast_group_id
                    when @fg8 then
                        fcstedadrcurrent
                   end
                   ) as fg8_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg8 then
                        fcstedadrpickup
                   end
                   ) as fg8_fcstedadrpickup,
               (case forecast_group_id
                    when @fg9 then
                        fcstedadrcurrent
                   end
                   ) as fg9_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg9 then
                        fcstedadrpickup
                   end
                   ) as fg9_fcstedadrpickup,
               (case forecast_group_id
                    when @fg10 then
                        fcstedadrcurrent
                   end
                   ) as fg10_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg10 then
                        fcstedadrpickup
                   end
                   ) as fg10_fcstedadrpickup,
               (case forecast_group_id
                    when @fg11 then
                        fcstedadrcurrent
                   end
                   ) as fg11_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg11 then
                        fcstedadrpickup
                   end
                   ) as fg11_fcstedadrpickup,
               (case forecast_group_id
                    when @fg12 then
                        fcstedadrcurrent
                   end
                   ) as fg12_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg12 then
                        fcstedadrpickup
                   end
                   ) as fg12_fcstedadrpickup,
               (case forecast_group_id
                    when @fg13 then
                        fcstedadrcurrent
                   end
                   ) as fg13_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg13 then
                        fcstedadrpickup
                   end
                   ) as fg13_fcstedadrpickup,
               (case forecast_group_id
                    when @fg14 then
                        fcstedadrcurrent
                   end
                   ) as fg14_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg14 then
                        fcstedadrpickup
                   end
                   ) as fg14_fcstedadrpickup,
               (case forecast_group_id
                    when @fg15 then
                        fcstedadrcurrent
                   end
                   ) as fg15_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg15 then
                        fcstedadrpickup
                   end
                   ) as fg15_fcstedadrpickup,
               (case forecast_group_id
                    when @fg16 then
                        fcstedadrcurrent
                   end
                   ) as fg16_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg16 then
                        fcstedadrpickup
                   end
                   ) as fg16_fcstedadrpickup,
               (case forecast_group_id
                    when @fg17 then
                        fcstedadrcurrent
                   end
                   ) as fg17_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg17 then
                        fcstedadrpickup
                   end
                   ) as fg17_fcstedadrpickup,
               (case forecast_group_id
                    when @fg18 then
                        fcstedadrcurrent
                   end
                   ) as fg18_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg18 then
                        fcstedadrpickup
                   end
                   ) as fg18_fcstedadrpickup,
               (case forecast_group_id
                    when @fg19 then
                        fcstedadrcurrent
                   end
                   ) as fg19_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg19 then
                        fcstedadrpickup
                   end
                   ) as fg19_fcstedadrpickup,
               (case forecast_group_id
                    when @fg20 then
                        fcstedadrcurrent
                   end
                   ) as fg20_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg20 then
                        fcstedadrpickup
                   end
                   ) as fg20_fcstedadrpickup,
               (case forecast_group_id
                    when @fg21 then
                        fcstedadrcurrent
                   end
                   ) as fg21_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg21 then
                        fcstedadrpickup
                   end
                   ) as fg21_fcstedadrpickup,
               (case forecast_group_id
                    when @fg22 then
                        fcstedadrcurrent
                   end
                   ) as fg22_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg22 then
                        fcstedadrpickup
                   end
                   ) as fg22_fcstedadrpickup,
               (case forecast_group_id
                    when @fg23 then
                        fcstedadrcurrent
                   end
                   ) as fg23_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg23 then
                        fcstedadrpickup
                   end
                   ) as fg23_fcstedadrpickup,
               (case forecast_group_id
                    when @fg24 then
                        fcstedadrcurrent
                   end
                   ) as fg24_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg24 then
                        fcstedadrpickup
                   end
                   ) as fg24_fcstedadrpickup,
               (case forecast_group_id
                    when @fg25 then
                        fcstedadrcurrent
                   end
                   ) as fg25_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg25 then
                        fcstedadrpickup
                   end
                   ) as fg25_fcstedadrpickup,
               (case forecast_group_id
                    when @fg26 then
                        fcstedadrcurrent
                   end
                   ) as fg26_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg26 then
                        fcstedadrpickup
                   end
                   ) as fg26_fcstedadrpickup,
               (case forecast_group_id
                    when @fg27 then
                        fcstedadrcurrent
                   end
                   ) as fg27_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg27 then
                        fcstedadrpickup
                   end
                   ) as fg27_fcstedadrpickup,
               (case forecast_group_id
                    when @fg28 then
                        fcstedadrcurrent
                   end
                   ) as fg28_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg28 then
                        fcstedadrpickup
                   end
                   ) as fg28_fcstedadrpickup,
               (case forecast_group_id
                    when @fg29 then
                        fcstedadrcurrent
                   end
                   ) as fg29_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg29 then
                        fcstedadrpickup
                   end
                   ) as fg29_fcstedadrpickup,
               (case forecast_group_id
                    when @fg30 then
                        fcstedadrcurrent
                   end
                   ) as fg30_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg30 then
                        fcstedadrpickup
                   end
                   ) as fg30_fcstedadrpickup,
               (case forecast_group_id
                    when @fg31 then
                        fcstedadrcurrent
                   end
                   ) as fg31_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg31 then
                        fcstedadrpickup
                   end
                   ) as fg31_fcstedadrpickup,
               (case forecast_group_id
                    when @fg32 then
                        fcstedadrcurrent
                   end
                   ) as fg32_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg32 then
                        fcstedadrpickup
                   end
                   ) as fg32_fcstedadrpickup,
               (case forecast_group_id
                    when @fg33 then
                        fcstedadrcurrent
                   end
                   ) as fg33_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg33 then
                        fcstedadrpickup
                   end
                   ) as fg33_fcstedadrpickup,
               (case forecast_group_id
                    when @fg34 then
                        fcstedadrcurrent
                   end
                   ) as fg34_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg34 then
                        fcstedadrpickup
                   end
                   ) as fg34_fcstedadrpickup,
               (case forecast_group_id
                    when @fg35 then
                        fcstedadrcurrent
                   end
                   ) as fg35_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg35 then
                        fcstedadrpickup
                   end
                   ) as fg35_fcstedadrpickup,
               (case forecast_group_id
                    when @fg36 then
                        fcstedadrcurrent
                   end
                   ) as fg36_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg36 then
                        fcstedadrpickup
                   end
                   ) as fg36_fcstedadrpickup,
               (case forecast_group_id
                    when @fg37 then
                        fcstedadrcurrent
                   end
                   ) as fg37_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg37 then
                        fcstedadrpickup
                   end
                   ) as fg37_fcstedadrpickup,
               (case forecast_group_id
                    when @fg38 then
                        fcstedadrcurrent
                   end
                   ) as fg38_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg38 then
                        fcstedadrpickup
                   end
                   ) as fg38_fcstedadrpickup,
               (case forecast_group_id
                    when @fg39 then
                        fcstedadrcurrent
                   end
                   ) as fg39_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg39 then
                        fcstedadrpickup
                   end
                   ) as fg39_fcstedadrpickup,
               (case forecast_group_id
                    when @fg40 then
                        fcstedadrcurrent
                   end
                   ) as fg40_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg40 then
                        fcstedadrpickup
                   end
                   ) as fg40_fcstedadrpickup,
               (case forecast_group_id
                    when @fg41 then
                        fcstedadrcurrent
                   end
                   ) as fg41_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg41 then
                        fcstedadrpickup
                   end
                   ) as fg41_fcstedadrpickup,
               (case forecast_group_id
                    when @fg42 then
                        fcstedadrcurrent
                   end
                   ) as fg42_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg42 then
                        fcstedadrpickup
                   end
                   ) as fg42_fcstedadrpickup,
               (case forecast_group_id
                    when @fg43 then
                        fcstedadrcurrent
                   end
                   ) as fg43_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg43 then
                        fcstedadrpickup
                   end
                   ) as fg43_fcstedadrpickup,
               (case forecast_group_id
                    when @fg44 then
                        fcstedadrcurrent
                   end
                   ) as fg44_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg44 then
                        fcstedadrpickup
                   end
                   ) as fg44_fcstedadrpickup,
               (case forecast_group_id
                    when @fg45 then
                        fcstedadrcurrent
                   end
                   ) as fg45_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg45 then
                        fcstedadrpickup
                   end
                   ) as fg45_fcstedadrpickup,
               (case forecast_group_id
                    when @fg46 then
                        fcstedadrcurrent
                   end
                   ) as fg46_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg46 then
                        fcstedadrpickup
                   end
                   ) as fg46_fcstedadrpickup,
               (case forecast_group_id
                    when @fg47 then
                        fcstedadrcurrent
                   end
                   ) as fg47_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg47 then
                        fcstedadrpickup
                   end
                   ) as fg47_fcstedadrpickup,
               (case forecast_group_id
                    when @fg48 then
                        fcstedadrcurrent
                   end
                   ) as fg48_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg48 then
                        fcstedadrpickup
                   end
                   ) as fg48_fcstedadrpickup,
               (case forecast_group_id
                    when @fg49 then
                        fcstedadrcurrent
                   end
                   ) as fg49_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg49 then
                        fcstedadrpickup
                   end
                   ) as fg49_fcstedadrpickup,
               (case forecast_group_id
                    when @fg50 then
                        fcstedadrcurrent
                   end
                   ) as fg50_fcstedadrcurrent,
               (case forecast_group_id
                    when @fg50 then
                        fcstedadrpickup
                   end
                   ) as fg50_fcstedadrpickup,
               (case forecast_group_id
                    when @fg1 then
                        block
                   end
                   ) as fg1_block,
               (case forecast_group_id
                    when @fg1 then
                        block_pickup
                   end
                   ) as fg1_block_pickup,
               (case forecast_group_id
                    when @fg1 then
                        block_available
                   end
                   ) as fg1_block_available,
               (case forecast_group_id
                    when @fg2 then
                        block
                   end
                   ) as fg2_block,
               (case forecast_group_id
                    when @fg2 then
                        block_pickup
                   end
                   ) as fg2_block_pickup,
               (case forecast_group_id
                    when @fg2 then
                        block_available
                   end
                   ) as fg2_block_available,
               (case forecast_group_id
                    when @fg3 then
                        block
                   end
                   ) as fg3_block,
               (case forecast_group_id
                    when @fg3 then
                        block_pickup
                   end
                   ) as fg3_block_pickup,
               (case forecast_group_id
                    when @fg3 then
                        block_available
                   end
                   ) as fg3_block_available,
               (case forecast_group_id
                    when @fg4 then
                        block
                   end
                   ) as fg4_block,
               (case forecast_group_id
                    when @fg4 then
                        block_pickup
                   end
                   ) as fg4_block_pickup,
               (case forecast_group_id
                    when @fg4 then
                        block_available
                   end
                   ) as fg4_block_available,
               (case forecast_group_id
                    when @fg5 then
                        block
                   end
                   ) as fg5_block,
               (case forecast_group_id
                    when @fg5 then
                        block_pickup
                   end
                   ) as fg5_block_pickup,
               (case forecast_group_id
                    when @fg5 then
                        block_available
                   end
                   ) as fg5_block_available,
               (case forecast_group_id
                    when @fg6 then
                        block
                   end
                   ) as fg6_block,
               (case forecast_group_id
                    when @fg6 then
                        block_pickup
                   end
                   ) as fg6_block_pickup,
               (case forecast_group_id
                    when @fg6 then
                        block_available
                   end
                   ) as fg6_block_available,
               (case forecast_group_id
                    when @fg7 then
                        block
                   end
                   ) as fg7_block,
               (case forecast_group_id
                    when @fg7 then
                        block_pickup
                   end
                   ) as fg7_block_pickup,
               (case forecast_group_id
                    when @fg7 then
                        block_available
                   end
                   ) as fg7_block_available,
               (case forecast_group_id
                    when @fg8 then
                        block
                   end
                   ) as fg8_block,
               (case forecast_group_id
                    when @fg8 then
                        block_pickup
                   end
                   ) as fg8_block_pickup,
               (case forecast_group_id
                    when @fg8 then
                        block_available
                   end
                   ) as fg8_block_available,
               (case forecast_group_id
                    when @fg9 then
                        block
                   end
                   ) as fg9_block,
               (case forecast_group_id
                    when @fg9 then
                        block_pickup
                   end
                   ) as fg9_block_pickup,
               (case forecast_group_id
                    when @fg9 then
                        block_available
                   end
                   ) as fg9_block_available,
               (case forecast_group_id
                    when @fg10 then
                        block
                   end
                   ) as fg10_block,
               (case forecast_group_id
                    when @fg10 then
                        block_pickup
                   end
                   ) as fg10_block_pickup,
               (case forecast_group_id
                    when @fg10 then
                        block_available
                   end
                   ) as fg10_block_available,
               (case forecast_group_id
                    when @fg11 then
                        block
                   end
                   ) as fg11_block,
               (case forecast_group_id
                    when @fg11 then
                        block_pickup
                   end
                   ) as fg11_block_pickup,
               (case forecast_group_id
                    when @fg11 then
                        block_available
                   end
                   ) as fg11_block_available,
               (case forecast_group_id
                    when @fg12 then
                        block
                   end
                   ) as fg12_block,
               (case forecast_group_id
                    when @fg12 then
                        block_pickup
                   end
                   ) as fg12_block_pickup,
               (case forecast_group_id
                    when @fg12 then
                        block_available
                   end
                   ) as fg12_block_available,
               (case forecast_group_id
                    when @fg13 then
                        block
                   end
                   ) as fg13_block,
               (case forecast_group_id
                    when @fg13 then
                        block_pickup
                   end
                   ) as fg13_block_pickup,
               (case forecast_group_id
                    when @fg13 then
                        block_available
                   end
                   ) as fg13_block_available,
               (case forecast_group_id
                    when @fg14 then
                        block
                   end
                   ) as fg14_block,
               (case forecast_group_id
                    when @fg14 then
                        block_pickup
                   end
                   ) as fg14_block_pickup,
               (case forecast_group_id
                    when @fg14 then
                        block_available
                   end
                   ) as fg14_block_available,
               (case forecast_group_id
                    when @fg15 then
                        block
                   end
                   ) as fg15_block,
               (case forecast_group_id
                    when @fg15 then
                        block_pickup
                   end
                   ) as fg15_block_pickup,
               (case forecast_group_id
                    when @fg15 then
                        block_available
                   end
                   ) as fg15_block_available,
               (case forecast_group_id
                    when @fg16 then
                        block
                   end
                   ) as fg16_block,
               (case forecast_group_id
                    when @fg16 then
                        block_pickup
                   end
                   ) as fg16_block_pickup,
               (case forecast_group_id
                    when @fg16 then
                        block_available
                   end
                   ) as fg16_block_available,
               (case forecast_group_id
                    when @fg17 then
                        block
                   end
                   ) as fg17_block,
               (case forecast_group_id
                    when @fg17 then
                        block_pickup
                   end
                   ) as fg17_block_pickup,
               (case forecast_group_id
                    when @fg17 then
                        block_available
                   end
                   ) as fg17_block_available,
               (case forecast_group_id
                    when @fg18 then
                        block
                   end
                   ) as fg18_block,
               (case forecast_group_id
                    when @fg18 then
                        block_pickup
                   end
                   ) as fg18_block_pickup,
               (case forecast_group_id
                    when @fg18 then
                        block_available
                   end
                   ) as fg18_block_available,
               (case forecast_group_id
                    when @fg19 then
                        block
                   end
                   ) as fg19_block,
               (case forecast_group_id
                    when @fg19 then
                        block_pickup
                   end
                   ) as fg19_block_pickup,
               (case forecast_group_id
                    when @fg19 then
                        block_available
                   end
                   ) as fg19_block_available,
               (case forecast_group_id
                    when @fg20 then
                        block
                   end
                   ) as fg20_block,
               (case forecast_group_id
                    when @fg20 then
                        block_pickup
                   end
                   ) as fg20_block_pickup,
               (case forecast_group_id
                    when @fg20 then
                        block_available
                   end
                   ) as fg20_block_available,
               (case forecast_group_id
                    when @fg21 then
                        block
                   end
                   ) as fg21_block,
               (case forecast_group_id
                    when @fg21 then
                        block_pickup
                   end
                   ) as fg21_block_pickup,
               (case forecast_group_id
                    when @fg21 then
                        block_available
                   end
                   ) as fg21_block_available,
               (case forecast_group_id
                    when @fg22 then
                        block
                   end
                   ) as fg22_block,
               (case forecast_group_id
                    when @fg22 then
                        block_pickup
                   end
                   ) as fg22_block_pickup,
               (case forecast_group_id
                    when @fg22 then
                        block_available
                   end
                   ) as fg22_block_available,
               (case forecast_group_id
                    when @fg23 then
                        block
                   end
                   ) as fg23_block,
               (case forecast_group_id
                    when @fg23 then
                        block_pickup
                   end
                   ) as fg23_block_pickup,
               (case forecast_group_id
                    when @fg23 then
                        block_available
                   end
                   ) as fg23_block_available,
               (case forecast_group_id
                    when @fg24 then
                        block
                   end
                   ) as fg24_block,
               (case forecast_group_id
                    when @fg24 then
                        block_pickup
                   end
                   ) as fg24_block_pickup,
               (case forecast_group_id
                    when @fg24 then
                        block_available
                   end
                   ) as fg24_block_available,
               (case forecast_group_id
                    when @fg25 then
                        block
                   end
                   ) as fg25_block,
               (case forecast_group_id
                    when @fg25 then
                        block_pickup
                   end
                   ) as fg25_block_pickup,
               (case forecast_group_id
                    when @fg25 then
                        block_available
                   end
                   ) as fg25_block_available,
               (case forecast_group_id
                    when @fg26 then
                        block
                   end
                   ) as fg26_block,
               (case forecast_group_id
                    when @fg26 then
                        block_pickup
                   end
                   ) as fg26_block_pickup,
               (case forecast_group_id
                    when @fg26 then
                        block_available
                   end
                   ) as fg26_block_available,
               (case forecast_group_id
                    when @fg27 then
                        block
                   end
                   ) as fg27_block,
               (case forecast_group_id
                    when @fg27 then
                        block_pickup
                   end
                   ) as fg27_block_pickup,
               (case forecast_group_id
                    when @fg27 then
                        block_available
                   end
                   ) as fg27_block_available,
               (case forecast_group_id
                    when @fg28 then
                        block
                   end
                   ) as fg28_block,
               (case forecast_group_id
                    when @fg28 then
                        block_pickup
                   end
                   ) as fg28_block_pickup,
               (case forecast_group_id
                    when @fg28 then
                        block_available
                   end
                   ) as fg28_block_available,
               (case forecast_group_id
                    when @fg29 then
                        block
                   end
                   ) as fg29_block,
               (case forecast_group_id
                    when @fg29 then
                        block_pickup
                   end
                   ) as fg29_block_pickup,
               (case forecast_group_id
                    when @fg29 then
                        block_available
                   end
                   ) as fg29_block_available,
               (case forecast_group_id
                    when @fg30 then
                        block
                   end
                   ) as fg30_block,
               (case forecast_group_id
                    when @fg30 then
                        block_pickup
                   end
                   ) as fg30_block_pickup,
               (case forecast_group_id
                    when @fg30 then
                        block_available
                   end
                   ) as fg30_block_available,
               (case forecast_group_id
                    when @fg31 then
                        block
                   end
                   ) as fg31_block,
               (case forecast_group_id
                    when @fg31 then
                        block_pickup
                   end
                   ) as fg31_block_pickup,
               (case forecast_group_id
                    when @fg31 then
                        block_available
                   end
                   ) as fg31_block_available,
               (case forecast_group_id
                    when @fg32 then
                        block
                   end
                   ) as fg32_block,
               (case forecast_group_id
                    when @fg32 then
                        block_pickup
                   end
                   ) as fg32_block_pickup,
               (case forecast_group_id
                    when @fg32 then
                        block_available
                   end
                   ) as fg32_block_available,
               (case forecast_group_id
                    when @fg33 then
                        block
                   end
                   ) as fg33_block,
               (case forecast_group_id
                    when @fg33 then
                        block_pickup
                   end
                   ) as fg33_block_pickup,
               (case forecast_group_id
                    when @fg33 then
                        block_available
                   end
                   ) as fg33_block_available,
               (case forecast_group_id
                    when @fg34 then
                        block
                   end
                   ) as fg34_block,
               (case forecast_group_id
                    when @fg34 then
                        block_pickup
                   end
                   ) as fg34_block_pickup,
               (case forecast_group_id
                    when @fg34 then
                        block_available
                   end
                   ) as fg34_block_available,
               (case forecast_group_id
                    when @fg35 then
                        block
                   end
                   ) as fg35_block,
               (case forecast_group_id
                    when @fg35 then
                        block_pickup
                   end
                   ) as fg35_block_pickup,
               (case forecast_group_id
                    when @fg35 then
                        block_available
                   end
                   ) as fg35_block_available,
               (case forecast_group_id
                    when @fg36 then
                        block
                   end
                   ) as fg36_block,
               (case forecast_group_id
                    when @fg36 then
                        block_pickup
                   end
                   ) as fg36_block_pickup,
               (case forecast_group_id
                    when @fg36 then
                        block_available
                   end
                   ) as fg36_block_available,
               (case forecast_group_id
                    when @fg37 then
                        block
                   end
                   ) as fg37_block,
               (case forecast_group_id
                    when @fg37 then
                        block_pickup
                   end
                   ) as fg37_block_pickup,
               (case forecast_group_id
                    when @fg37 then
                        block_available
                   end
                   ) as fg37_block_available,
               (case forecast_group_id
                    when @fg38 then
                        block
                   end
                   ) as fg38_block,
               (case forecast_group_id
                    when @fg38 then
                        block_pickup
                   end
                   ) as fg38_block_pickup,
               (case forecast_group_id
                    when @fg38 then
                        block_available
                   end
                   ) as fg38_block_available,
               (case forecast_group_id
                    when @fg39 then
                        block
                   end
                   ) as fg39_block,
               (case forecast_group_id
                    when @fg39 then
                        block_pickup
                   end
                   ) as fg39_block_pickup,
               (case forecast_group_id
                    when @fg39 then
                        block_available
                   end
                   ) as fg39_block_available,
               (case forecast_group_id
                    when @fg40 then
                        block
                   end
                   ) as fg40_block,
               (case forecast_group_id
                    when @fg40 then
                        block_pickup
                   end
                   ) as fg40_block_pickup,
               (case forecast_group_id
                    when @fg40 then
                        block_available
                   end
                   ) as fg40_block_available,
               (case forecast_group_id
                    when @fg41 then
                        block
                   end
                   ) as fg41_block,
               (case forecast_group_id
                    when @fg41 then
                        block_pickup
                   end
                   ) as fg41_block_pickup,
               (case forecast_group_id
                    when @fg41 then
                        block_available
                   end
                   ) as fg41_block_available,
               (case forecast_group_id
                    when @fg42 then
                        block
                   end
                   ) as fg42_block,
               (case forecast_group_id
                    when @fg42 then
                        block_pickup
                   end
                   ) as fg42_block_pickup,
               (case forecast_group_id
                    when @fg42 then
                        block_available
                   end
                   ) as fg42_block_available,
               (case forecast_group_id
                    when @fg43 then
                        block
                   end
                   ) as fg43_block,
               (case forecast_group_id
                    when @fg43 then
                        block_pickup
                   end
                   ) as fg43_block_pickup,
               (case forecast_group_id
                    when @fg43 then
                        block_available
                   end
                   ) as fg43_block_available,
               (case forecast_group_id
                    when @fg44 then
                        block
                   end
                   ) as fg44_block,
               (case forecast_group_id
                    when @fg44 then
                        block_pickup
                   end
                   ) as fg44_block_pickup,
               (case forecast_group_id
                    when @fg44 then
                        block_available
                   end
                   ) as fg44_block_available,
               (case forecast_group_id
                    when @fg45 then
                        block
                   end
                   ) as fg45_block,
               (case forecast_group_id
                    when @fg45 then
                        block_pickup
                   end
                   ) as fg45_block_pickup,
               (case forecast_group_id
                    when @fg45 then
                        block_available
                   end
                   ) as fg45_block_available,
               (case forecast_group_id
                    when @fg46 then
                        block
                   end
                   ) as fg46_block,
               (case forecast_group_id
                    when @fg46 then
                        block_pickup
                   end
                   ) as fg46_block_pickup,
               (case forecast_group_id
                    when @fg46 then
                        block_available
                   end
                   ) as fg46_block_available,
               (case forecast_group_id
                    when @fg47 then
                        block
                   end
                   ) as fg47_block,
               (case forecast_group_id
                    when @fg47 then
                        block_pickup
                   end
                   ) as fg47_block_pickup,
               (case forecast_group_id
                    when @fg47 then
                        block_available
                   end
                   ) as fg47_block_available,
               (case forecast_group_id
                    when @fg48 then
                        block
                   end
                   ) as fg48_block,
               (case forecast_group_id
                    when @fg48 then
                        block_pickup
                   end
                   ) as fg48_block_pickup,
               (case forecast_group_id
                    when @fg48 then
                        block_available
                   end
                   ) as fg48_block_available,
               (case forecast_group_id
                    when @fg49 then
                        block
                   end
                   ) as fg49_block,
               (case forecast_group_id
                    when @fg49 then
                        block_pickup
                   end
                   ) as fg49_block_pickup,
               (case forecast_group_id
                    when @fg49 then
                        block_available
                   end
                   ) as fg49_block_available,
               (case forecast_group_id
                    when @fg50 then
                        block
                   end
                   ) as fg50_block,
               (case forecast_group_id
                    when @fg50 then
                        block_pickup
                   end
                   ) as fg50_block_pickup,
               (case forecast_group_id
                    when @fg50 then
                        block_available
                   end
                   ) as fg50_block_available,
               (case forecast_group_id
                    when @fg1 then
                        profit_onBooks_current
                   end
                   ) as fg1_profit_onBooks_current,
               (case forecast_group_id
                    when @fg1 then
                        profit_onBooks_pickup
                   end
                   ) as fg1_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg2 then
                        profit_onBooks_current
                   end
                   ) as fg2_profit_onBooks_current,
               (case forecast_group_id
                    when @fg2 then
                        profit_onBooks_pickup
                   end
                   ) as fg2_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg3 then
                        profit_onBooks_current
                   end
                   ) as fg3_profit_onBooks_current,
               (case forecast_group_id
                    when @fg3 then
                        profit_onBooks_pickup
                   end
                   ) as fg3_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg4 then
                        profit_onBooks_current
                   end
                   ) as fg4_profit_onBooks_current,
               (case forecast_group_id
                    when @fg4 then
                        profit_onBooks_pickup
                   end
                   ) as fg4_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg5 then
                        profit_onBooks_current
                   end
                   ) as fg5_profit_onBooks_current,
               (case forecast_group_id
                    when @fg5 then
                        profit_onBooks_pickup
                   end
                   ) as fg5_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg6 then
                        profit_onBooks_current
                   end
                   ) as fg6_profit_onBooks_current,
               (case forecast_group_id
                    when @fg6 then
                        profit_onBooks_pickup
                   end
                   ) as fg6_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg7 then
                        profit_onBooks_current
                   end
                   ) as fg7_profit_onBooks_current,
               (case forecast_group_id
                    when @fg7 then
                        profit_onBooks_pickup
                   end
                   ) as fg7_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg8 then
                        profit_onBooks_current
                   end
                   ) as fg8_profit_onBooks_current,
               (case forecast_group_id
                    when @fg8 then
                        profit_onBooks_pickup
                   end
                   ) as fg8_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg9 then
                        profit_onBooks_current
                   end
                   ) as fg9_profit_onBooks_current,
               (case forecast_group_id
                    when @fg9 then
                        profit_onBooks_pickup
                   end
                   ) as fg9_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg10 then
                        profit_onBooks_current
                   end
                   ) as fg10_profit_onBooks_current,
               (case forecast_group_id
                    when @fg10 then
                        profit_onBooks_pickup
                   end
                   ) as fg10_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg11 then
                        profit_onBooks_current
                   end
                   ) as fg11_profit_onBooks_current,
               (case forecast_group_id
                    when @fg11 then
                        profit_onBooks_pickup
                   end
                   ) as fg11_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg12 then
                        profit_onBooks_current
                   end
                   ) as fg12_profit_onBooks_current,
               (case forecast_group_id
                    when @fg12 then
                        profit_onBooks_pickup
                   end
                   ) as fg12_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg13 then
                        profit_onBooks_current
                   end
                   ) as fg13_profit_onBooks_current,
               (case forecast_group_id
                    when @fg13 then
                        profit_onBooks_pickup
                   end
                   ) as fg13_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg14 then
                        profit_onBooks_current
                   end
                   ) as fg14_profit_onBooks_current,
               (case forecast_group_id
                    when @fg14 then
                        profit_onBooks_pickup
                   end
                   ) as fg14_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg15 then
                        profit_onBooks_current
                   end
                   ) as fg15_profit_onBooks_current,
               (case forecast_group_id
                    when @fg15 then
                        profit_onBooks_pickup
                   end
                   ) as fg15_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg16 then
                        profit_onBooks_current
                   end
                   ) as fg16_profit_onBooks_current,
               (case forecast_group_id
                    when @fg16 then
                        profit_onBooks_pickup
                   end
                   ) as fg16_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg17 then
                        profit_onBooks_current
                   end
                   ) as fg17_profit_onBooks_current,
               (case forecast_group_id
                    when @fg17 then
                        profit_onBooks_pickup
                   end
                   ) as fg17_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg18 then
                        profit_onBooks_current
                   end
                   ) as fg18_profit_onBooks_current,
               (case forecast_group_id
                    when @fg18 then
                        profit_onBooks_pickup
                   end
                   ) as fg18_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg19 then
                        profit_onBooks_current
                   end
                   ) as fg19_profit_onBooks_current,
               (case forecast_group_id
                    when @fg19 then
                        profit_onBooks_pickup
                   end
                   ) as fg19_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg20 then
                        profit_onBooks_current
                   end
                   ) as fg20_profit_onBooks_current,
               (case forecast_group_id
                    when @fg20 then
                        profit_onBooks_pickup
                   end
                   ) as fg20_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg21 then
                        profit_onBooks_current
                   end
                   ) as fg21_profit_onBooks_current,
               (case forecast_group_id
                    when @fg21 then
                        profit_onBooks_pickup
                   end
                   ) as fg21_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg22 then
                        profit_onBooks_current
                   end
                   ) as fg22_profit_onBooks_current,
               (case forecast_group_id
                    when @fg22 then
                        profit_onBooks_pickup
                   end
                   ) as fg22_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg23 then
                        profit_onBooks_current
                   end
                   ) as fg23_profit_onBooks_current,
               (case forecast_group_id
                    when @fg23 then
                        profit_onBooks_pickup
                   end
                   ) as fg23_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg24 then
                        profit_onBooks_current
                   end
                   ) as fg24_profit_onBooks_current,
               (case forecast_group_id
                    when @fg24 then
                        profit_onBooks_pickup
                   end
                   ) as fg24_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg25 then
                        profit_onBooks_current
                   end
                   ) as fg25_profit_onBooks_current,
               (case forecast_group_id
                    when @fg25 then
                        profit_onBooks_pickup
                   end
                   ) as fg25_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg26 then
                        profit_onBooks_current
                   end
                   ) as fg26_profit_onBooks_current,
               (case forecast_group_id
                    when @fg26 then
                        profit_onBooks_pickup
                   end
                   ) as fg26_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg27 then
                        profit_onBooks_current
                   end
                   ) as fg27_profit_onBooks_current,
               (case forecast_group_id
                    when @fg27 then
                        profit_onBooks_pickup
                   end
                   ) as fg27_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg28 then
                        profit_onBooks_current
                   end
                   ) as fg28_profit_onBooks_current,
               (case forecast_group_id
                    when @fg28 then
                        profit_onBooks_pickup
                   end
                   ) as fg28_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg29 then
                        profit_onBooks_current
                   end
                   ) as fg29_profit_onBooks_current,
               (case forecast_group_id
                    when @fg29 then
                        profit_onBooks_pickup
                   end
                   ) as fg29_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg30 then
                        profit_onBooks_current
                   end
                   ) as fg30_profit_onBooks_current,
               (case forecast_group_id
                    when @fg30 then
                        profit_onBooks_pickup
                   end
                   ) as fg30_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg31 then
                        profit_onBooks_current
                   end
                   ) as fg31_profit_onBooks_current,
               (case forecast_group_id
                    when @fg31 then
                        profit_onBooks_pickup
                   end
                   ) as fg31_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg32 then
                        profit_onBooks_current
                   end
                   ) as fg32_profit_onBooks_current,
               (case forecast_group_id
                    when @fg32 then
                        profit_onBooks_pickup
                   end
                   ) as fg32_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg33 then
                        profit_onBooks_current
                   end
                   ) as fg33_profit_onBooks_current,
               (case forecast_group_id
                    when @fg33 then
                        profit_onBooks_pickup
                   end
                   ) as fg33_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg34 then
                        profit_onBooks_current
                   end
                   ) as fg34_profit_onBooks_current,
               (case forecast_group_id
                    when @fg34 then
                        profit_onBooks_pickup
                   end
                   ) as fg34_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg35 then
                        profit_onBooks_current
                   end
                   ) as fg35_profit_onBooks_current,
               (case forecast_group_id
                    when @fg35 then
                        profit_onBooks_pickup
                   end
                   ) as fg35_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg36 then
                        profit_onBooks_current
                   end
                   ) as fg36_profit_onBooks_current,
               (case forecast_group_id
                    when @fg36 then
                        profit_onBooks_pickup
                   end
                   ) as fg36_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg37 then
                        profit_onBooks_current
                   end
                   ) as fg37_profit_onBooks_current,
               (case forecast_group_id
                    when @fg37 then
                        profit_onBooks_pickup
                   end
                   ) as fg37_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg38 then
                        profit_onBooks_current
                   end
                   ) as fg38_profit_onBooks_current,
               (case forecast_group_id
                    when @fg38 then
                        profit_onBooks_pickup
                   end
                   ) as fg38_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg39 then
                        profit_onBooks_current
                   end
                   ) as fg39_profit_onBooks_current,
               (case forecast_group_id
                    when @fg39 then
                        profit_onBooks_pickup
                   end
                   ) as fg39_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg40 then
                        profit_onBooks_current
                   end
                   ) as fg40_profit_onBooks_current,
               (case forecast_group_id
                    when @fg40 then
                        profit_onBooks_pickup
                   end
                   ) as fg40_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg41 then
                        profit_onBooks_current
                   end
                   ) as fg41_profit_onBooks_current,
               (case forecast_group_id
                    when @fg41 then
                        profit_onBooks_pickup
                   end
                   ) as fg41_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg42 then
                        profit_onBooks_current
                   end
                   ) as fg42_profit_onBooks_current,
               (case forecast_group_id
                    when @fg42 then
                        profit_onBooks_pickup
                   end
                   ) as fg42_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg43 then
                        profit_onBooks_current
                   end
                   ) as fg43_profit_onBooks_current,
               (case forecast_group_id
                    when @fg43 then
                        profit_onBooks_pickup
                   end
                   ) as fg43_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg44 then
                        profit_onBooks_current
                   end
                   ) as fg44_profit_onBooks_current,
               (case forecast_group_id
                    when @fg44 then
                        profit_onBooks_pickup
                   end
                   ) as fg44_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg45 then
                        profit_onBooks_current
                   end
                   ) as fg45_profit_onBooks_current,
               (case forecast_group_id
                    when @fg45 then
                        profit_onBooks_pickup
                   end
                   ) as fg45_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg46 then
                        profit_onBooks_current
                   end
                   ) as fg46_profit_onBooks_current,
               (case forecast_group_id
                    when @fg46 then
                        profit_onBooks_pickup
                   end
                   ) as fg46_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg47 then
                        profit_onBooks_current
                   end
                   ) as fg47_profit_onBooks_current,
               (case forecast_group_id
                    when @fg47 then
                        profit_onBooks_pickup
                   end
                   ) as fg47_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg48 then
                        profit_onBooks_current
                   end
                   ) as fg48_profit_onBooks_current,
               (case forecast_group_id
                    when @fg48 then
                        profit_onBooks_pickup
                   end
                   ) as fg48_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg49 then
                        profit_onBooks_current
                   end
                   ) as fg49_profit_onBooks_current,
               (case forecast_group_id
                    when @fg49 then
                        profit_onBooks_pickup
                   end
                   ) as fg49_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg50 then
                        profit_onBooks_current
                   end
                   ) as fg50_profit_onBooks_current,
               (case forecast_group_id
                    when @fg50 then
                        profit_onBooks_pickup
                   end
                   ) as fg50_profit_onBooks_pickup,
               (case forecast_group_id
                    when @fg1 then
                        proPOR_onBooks_current
                   end
                   ) as fg1_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg1 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg1_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg2 then
                        proPOR_onBooks_current
                   end
                   ) as fg2_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg2 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg2_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg3 then
                        proPOR_onBooks_current
                   end
                   ) as fg3_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg3 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg3_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg4 then
                        proPOR_onBooks_current
                   end
                   ) as fg4_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg4 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg4_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg5 then
                        proPOR_onBooks_current
                   end
                   ) as fg5_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg5 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg5_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg6 then
                        proPOR_onBooks_current
                   end
                   ) as fg6_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg6 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg6_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg7 then
                        proPOR_onBooks_current
                   end
                   ) as fg7_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg7 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg7_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg8 then
                        proPOR_onBooks_current
                   end
                   ) as fg8_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg8 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg8_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg9 then
                        proPOR_onBooks_current
                   end
                   ) as fg9_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg9 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg9_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg10 then
                        proPOR_onBooks_current
                   end
                   ) as fg10_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg10 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg10_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg11 then
                        proPOR_onBooks_current
                   end
                   ) as fg11_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg11 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg11_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg12 then
                        proPOR_onBooks_current
                   end
                   ) as fg12_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg12 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg12_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg13 then
                        proPOR_onBooks_current
                   end
                   ) as fg13_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg13 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg13_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg14 then
                        proPOR_onBooks_current
                   end
                   ) as fg14_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg14 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg14_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg15 then
                        proPOR_onBooks_current
                   end
                   ) as fg15_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg15 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg15_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg16 then
                        proPOR_onBooks_current
                   end
                   ) as fg16_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg16 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg16_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg17 then
                        proPOR_onBooks_current
                   end
                   ) as fg17_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg17 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg17_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg18 then
                        proPOR_onBooks_current
                   end
                   ) as fg18_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg18 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg18_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg19 then
                        proPOR_onBooks_current
                   end
                   ) as fg19_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg19 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg19_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg20 then
                        proPOR_onBooks_current
                   end
                   ) as fg20_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg20 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg20_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg21 then
                        proPOR_onBooks_current
                   end
                   ) as fg21_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg21 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg21_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg22 then
                        proPOR_onBooks_current
                   end
                   ) as fg22_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg22 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg22_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg23 then
                        proPOR_onBooks_current
                   end
                   ) as fg23_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg23 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg23_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg24 then
                        proPOR_onBooks_current
                   end
                   ) as fg24_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg24 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg24_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg25 then
                        proPOR_onBooks_current
                   end
                   ) as fg25_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg25 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg25_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg26 then
                        proPOR_onBooks_current
                   end
                   ) as fg26_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg26 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg26_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg27 then
                        proPOR_onBooks_current
                   end
                   ) as fg27_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg27 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg27_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg28 then
                        proPOR_onBooks_current
                   end
                   ) as fg28_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg28 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg28_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg29 then
                        proPOR_onBooks_current
                   end
                   ) as fg29_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg29 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg29_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg30 then
                        proPOR_onBooks_current
                   end
                   ) as fg30_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg30 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg30_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg31 then
                        proPOR_onBooks_current
                   end
                   ) as fg31_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg31 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg31_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg32 then
                        proPOR_onBooks_current
                   end
                   ) as fg32_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg32 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg32_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg33 then
                        proPOR_onBooks_current
                   end
                   ) as fg33_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg33 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg33_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg34 then
                        proPOR_onBooks_current
                   end
                   ) as fg34_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg34 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg34_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg35 then
                        proPOR_onBooks_current
                   end
                   ) as fg35_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg35 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg35_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg36 then
                        proPOR_onBooks_current
                   end
                   ) as fg36_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg36 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg36_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg37 then
                        proPOR_onBooks_current
                   end
                   ) as fg37_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg37 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg37_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg38 then
                        proPOR_onBooks_current
                   end
                   ) as fg38_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg38 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg38_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg39 then
                        proPOR_onBooks_current
                   end
                   ) as fg39_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg39 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg39_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg40 then
                        proPOR_onBooks_current
                   end
                   ) as fg40_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg40 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg40_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg41 then
                        proPOR_onBooks_current
                   end
                   ) as fg41_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg41 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg41_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg42 then
                        proPOR_onBooks_current
                   end
                   ) as fg42_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg42 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg42_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg43 then
                        proPOR_onBooks_current
                   end
                   ) as fg43_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg43 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg43_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg44 then
                        proPOR_onBooks_current
                   end
                   ) as fg44_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg44 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg44_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg45 then
                        proPOR_onBooks_current
                   end
                   ) as fg45_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg45 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg45_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg46 then
                        proPOR_onBooks_current
                   end
                   ) as fg46_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg46 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg46_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg47 then
                        proPOR_onBooks_current
                   end
                   ) as fg47_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg47 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg47_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg48 then
                        proPOR_onBooks_current
                   end
                   ) as fg48_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg48 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg48_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg49 then
                        proPOR_onBooks_current
                   end
                   ) as fg49_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg49 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg49_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg50 then
                        proPOR_onBooks_current
                   end
                   ) as fg50_proPOR_onBooks_current,
               (case forecast_group_id
                    when @fg50 then
                        proPOR_onBooks_pickup
                   end
                   ) as fg50_proPOR_onBooks_pickup,
               (case forecast_group_id
                    when @fg1 then
                        profit_forecast_current
                   end
                   ) as fg1_profit_forecast_current,
               (case forecast_group_id
                    when @fg1 then
                        profit_forecast_pickup
                   end
                   ) as fg1_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg2 then
                        profit_forecast_current
                   end
                   ) as fg2_profit_forecast_current,
               (case forecast_group_id
                    when @fg2 then
                        profit_forecast_pickup
                   end
                   ) as fg2_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg3 then
                        profit_forecast_current
                   end
                   ) as fg3_profit_forecast_current,
               (case forecast_group_id
                    when @fg3 then
                        profit_forecast_pickup
                   end
                   ) as fg3_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg4 then
                        profit_forecast_current
                   end
                   ) as fg4_profit_forecast_current,
               (case forecast_group_id
                    when @fg4 then
                        profit_forecast_pickup
                   end
                   ) as fg4_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg5 then
                        profit_forecast_current
                   end
                   ) as fg5_profit_forecast_current,
               (case forecast_group_id
                    when @fg5 then
                        profit_forecast_pickup
                   end
                   ) as fg5_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg6 then
                        profit_forecast_current
                   end
                   ) as fg6_profit_forecast_current,
               (case forecast_group_id
                    when @fg6 then
                        profit_forecast_pickup
                   end
                   ) as fg6_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg7 then
                        profit_forecast_current
                   end
                   ) as fg7_profit_forecast_current,
               (case forecast_group_id
                    when @fg7 then
                        profit_forecast_pickup
                   end
                   ) as fg7_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg8 then
                        profit_forecast_current
                   end
                   ) as fg8_profit_forecast_current,
               (case forecast_group_id
                    when @fg8 then
                        profit_forecast_pickup
                   end
                   ) as fg8_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg9 then
                        profit_forecast_current
                   end
                   ) as fg9_profit_forecast_current,
               (case forecast_group_id
                    when @fg9 then
                        profit_forecast_pickup
                   end
                   ) as fg9_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg10 then
                        profit_forecast_current
                   end
                   ) as fg10_profit_forecast_current,
               (case forecast_group_id
                    when @fg10 then
                        profit_forecast_pickup
                   end
                   ) as fg10_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg11 then
                        profit_forecast_current
                   end
                   ) as fg11_profit_forecast_current,
               (case forecast_group_id
                    when @fg11 then
                        profit_forecast_pickup
                   end
                   ) as fg11_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg12 then
                        profit_forecast_current
                   end
                   ) as fg12_profit_forecast_current,
               (case forecast_group_id
                    when @fg12 then
                        profit_forecast_pickup
                   end
                   ) as fg12_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg13 then
                        profit_forecast_current
                   end
                   ) as fg13_profit_forecast_current,
               (case forecast_group_id
                    when @fg13 then
                        profit_forecast_pickup
                   end
                   ) as fg13_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg14 then
                        profit_forecast_current
                   end
                   ) as fg14_profit_forecast_current,
               (case forecast_group_id
                    when @fg14 then
                        profit_forecast_pickup
                   end
                   ) as fg14_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg15 then
                        profit_forecast_current
                   end
                   ) as fg15_profit_forecast_current,
               (case forecast_group_id
                    when @fg15 then
                        profit_forecast_pickup
                   end
                   ) as fg15_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg16 then
                        profit_forecast_current
                   end
                   ) as fg16_profit_forecast_current,
               (case forecast_group_id
                    when @fg16 then
                        profit_forecast_pickup
                   end
                   ) as fg16_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg17 then
                        profit_forecast_current
                   end
                   ) as fg17_profit_forecast_current,
               (case forecast_group_id
                    when @fg17 then
                        profit_forecast_pickup
                   end
                   ) as fg17_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg18 then
                        profit_forecast_current
                   end
                   ) as fg18_profit_forecast_current,
               (case forecast_group_id
                    when @fg18 then
                        profit_forecast_pickup
                   end
                   ) as fg18_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg19 then
                        profit_forecast_current
                   end
                   ) as fg19_profit_forecast_current,
               (case forecast_group_id
                    when @fg19 then
                        profit_forecast_pickup
                   end
                   ) as fg19_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg20 then
                        profit_forecast_current
                   end
                   ) as fg20_profit_forecast_current,
               (case forecast_group_id
                    when @fg20 then
                        profit_forecast_pickup
                   end
                   ) as fg20_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg21 then
                        profit_forecast_current
                   end
                   ) as fg21_profit_forecast_current,
               (case forecast_group_id
                    when @fg21 then
                        profit_forecast_pickup
                   end
                   ) as fg21_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg22 then
                        profit_forecast_current
                   end
                   ) as fg22_profit_forecast_current,
               (case forecast_group_id
                    when @fg22 then
                        profit_forecast_pickup
                   end
                   ) as fg22_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg23 then
                        profit_forecast_current
                   end
                   ) as fg23_profit_forecast_current,
               (case forecast_group_id
                    when @fg23 then
                        profit_forecast_pickup
                   end
                   ) as fg23_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg24 then
                        profit_forecast_current
                   end
                   ) as fg24_profit_forecast_current,
               (case forecast_group_id
                    when @fg24 then
                        profit_forecast_pickup
                   end
                   ) as fg24_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg25 then
                        profit_forecast_current
                   end
                   ) as fg25_profit_forecast_current,
               (case forecast_group_id
                    when @fg25 then
                        profit_forecast_pickup
                   end
                   ) as fg25_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg26 then
                        profit_forecast_current
                   end
                   ) as fg26_profit_forecast_current,
               (case forecast_group_id
                    when @fg26 then
                        profit_forecast_pickup
                   end
                   ) as fg26_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg27 then
                        profit_forecast_current
                   end
                   ) as fg27_profit_forecast_current,
               (case forecast_group_id
                    when @fg27 then
                        profit_forecast_pickup
                   end
                   ) as fg27_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg28 then
                        profit_forecast_current
                   end
                   ) as fg28_profit_forecast_current,
               (case forecast_group_id
                    when @fg28 then
                        profit_forecast_pickup
                   end
                   ) as fg28_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg29 then
                        profit_forecast_current
                   end
                   ) as fg29_profit_forecast_current,
               (case forecast_group_id
                    when @fg29 then
                        profit_forecast_pickup
                   end
                   ) as fg29_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg30 then
                        profit_forecast_current
                   end
                   ) as fg30_profit_forecast_current,
               (case forecast_group_id
                    when @fg30 then
                        profit_forecast_pickup
                   end
                   ) as fg30_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg31 then
                        profit_forecast_current
                   end
                   ) as fg31_profit_forecast_current,
               (case forecast_group_id
                    when @fg31 then
                        profit_forecast_pickup
                   end
                   ) as fg31_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg32 then
                        profit_forecast_current
                   end
                   ) as fg32_profit_forecast_current,
               (case forecast_group_id
                    when @fg32 then
                        profit_forecast_pickup
                   end
                   ) as fg32_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg33 then
                        profit_forecast_current
                   end
                   ) as fg33_profit_forecast_current,
               (case forecast_group_id
                    when @fg33 then
                        profit_forecast_pickup
                   end
                   ) as fg33_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg34 then
                        profit_forecast_current
                   end
                   ) as fg34_profit_forecast_current,
               (case forecast_group_id
                    when @fg34 then
                        profit_forecast_pickup
                   end
                   ) as fg34_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg35 then
                        profit_forecast_current
                   end
                   ) as fg35_profit_forecast_current,
               (case forecast_group_id
                    when @fg35 then
                        profit_forecast_pickup
                   end
                   ) as fg35_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg36 then
                        profit_forecast_current
                   end
                   ) as fg36_profit_forecast_current,
               (case forecast_group_id
                    when @fg36 then
                        profit_forecast_pickup
                   end
                   ) as fg36_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg37 then
                        profit_forecast_current
                   end
                   ) as fg37_profit_forecast_current,
               (case forecast_group_id
                    when @fg37 then
                        profit_forecast_pickup
                   end
                   ) as fg37_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg38 then
                        profit_forecast_current
                   end
                   ) as fg38_profit_forecast_current,
               (case forecast_group_id
                    when @fg38 then
                        profit_forecast_pickup
                   end
                   ) as fg38_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg39 then
                        profit_forecast_current
                   end
                   ) as fg39_profit_forecast_current,
               (case forecast_group_id
                    when @fg39 then
                        profit_forecast_pickup
                   end
                   ) as fg39_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg40 then
                        profit_forecast_current
                   end
                   ) as fg40_profit_forecast_current,
               (case forecast_group_id
                    when @fg40 then
                        profit_forecast_pickup
                   end
                   ) as fg40_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg41 then
                        profit_forecast_current
                   end
                   ) as fg41_profit_forecast_current,
               (case forecast_group_id
                    when @fg41 then
                        profit_forecast_pickup
                   end
                   ) as fg41_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg42 then
                        profit_forecast_current
                   end
                   ) as fg42_profit_forecast_current,
               (case forecast_group_id
                    when @fg42 then
                        profit_forecast_pickup
                   end
                   ) as fg42_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg43 then
                        profit_forecast_current
                   end
                   ) as fg43_profit_forecast_current,
               (case forecast_group_id
                    when @fg43 then
                        profit_forecast_pickup
                   end
                   ) as fg43_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg44 then
                        profit_forecast_current
                   end
                   ) as fg44_profit_forecast_current,
               (case forecast_group_id
                    when @fg44 then
                        profit_forecast_pickup
                   end
                   ) as fg44_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg45 then
                        profit_forecast_current
                   end
                   ) as fg45_profit_forecast_current,
               (case forecast_group_id
                    when @fg45 then
                        profit_forecast_pickup
                   end
                   ) as fg45_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg46 then
                        profit_forecast_current
                   end
                   ) as fg46_profit_forecast_current,
               (case forecast_group_id
                    when @fg46 then
                        profit_forecast_pickup
                   end
                   ) as fg46_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg47 then
                        profit_forecast_current
                   end
                   ) as fg47_profit_forecast_current,
               (case forecast_group_id
                    when @fg47 then
                        profit_forecast_pickup
                   end
                   ) as fg47_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg48 then
                        profit_forecast_current
                   end
                   ) as fg48_profit_forecast_current,
               (case forecast_group_id
                    when @fg48 then
                        profit_forecast_pickup
                   end
                   ) as fg48_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg49 then
                        profit_forecast_current
                   end
                   ) as fg49_profit_forecast_current,
               (case forecast_group_id
                    when @fg49 then
                        profit_forecast_pickup
                   end
                   ) as fg49_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg50 then
                        profit_forecast_current
                   end
                   ) as fg50_profit_forecast_current,
               (case forecast_group_id
                    when @fg50 then
                        profit_forecast_pickup
                   end
                   ) as fg50_profit_forecast_pickup,
               (case forecast_group_id
                    when @fg1 then
                        proPOR_forecast_current
                   end
                   ) as fg1_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg1 then
                        proPOR_forecast_pickup
                   end
                   ) as fg1_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg2 then
                        proPOR_forecast_current
                   end
                   ) as fg2_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg2 then
                        proPOR_forecast_pickup
                   end
                   ) as fg2_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg3 then
                        proPOR_forecast_current
                   end
                   ) as fg3_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg3 then
                        proPOR_forecast_pickup
                   end
                   ) as fg3_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg4 then
                        proPOR_forecast_current
                   end
                   ) as fg4_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg4 then
                        proPOR_forecast_pickup
                   end
                   ) as fg4_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg5 then
                        proPOR_forecast_current
                   end
                   ) as fg5_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg5 then
                        proPOR_forecast_pickup
                   end
                   ) as fg5_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg6 then
                        proPOR_forecast_current
                   end
                   ) as fg6_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg6 then
                        proPOR_forecast_pickup
                   end
                   ) as fg6_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg7 then
                        proPOR_forecast_current
                   end
                   ) as fg7_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg7 then
                        proPOR_forecast_pickup
                   end
                   ) as fg7_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg8 then
                        proPOR_forecast_current
                   end
                   ) as fg8_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg8 then
                        proPOR_forecast_pickup
                   end
                   ) as fg8_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg9 then
                        proPOR_forecast_current
                   end
                   ) as fg9_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg9 then
                        proPOR_forecast_pickup
                   end
                   ) as fg9_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg10 then
                        proPOR_forecast_current
                   end
                   ) as fg10_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg10 then
                        proPOR_forecast_pickup
                   end
                   ) as fg10_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg11 then
                        proPOR_forecast_current
                   end
                   ) as fg11_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg11 then
                        proPOR_forecast_pickup
                   end
                   ) as fg11_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg12 then
                        proPOR_forecast_current
                   end
                   ) as fg12_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg12 then
                        proPOR_forecast_pickup
                   end
                   ) as fg12_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg13 then
                        proPOR_forecast_current
                   end
                   ) as fg13_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg13 then
                        proPOR_forecast_pickup
                   end
                   ) as fg13_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg14 then
                        proPOR_forecast_current
                   end
                   ) as fg14_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg14 then
                        proPOR_forecast_pickup
                   end
                   ) as fg14_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg15 then
                        proPOR_forecast_current
                   end
                   ) as fg15_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg15 then
                        proPOR_forecast_pickup
                   end
                   ) as fg15_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg16 then
                        proPOR_forecast_current
                   end
                   ) as fg16_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg16 then
                        proPOR_forecast_pickup
                   end
                   ) as fg16_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg17 then
                        proPOR_forecast_current
                   end
                   ) as fg17_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg17 then
                        proPOR_forecast_pickup
                   end
                   ) as fg17_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg18 then
                        proPOR_forecast_current
                   end
                   ) as fg18_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg18 then
                        proPOR_forecast_pickup
                   end
                   ) as fg18_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg19 then
                        proPOR_forecast_current
                   end
                   ) as fg19_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg19 then
                        proPOR_forecast_pickup
                   end
                   ) as fg19_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg20 then
                        proPOR_forecast_current
                   end
                   ) as fg20_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg20 then
                        proPOR_forecast_pickup
                   end
                   ) as fg20_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg21 then
                        proPOR_forecast_current
                   end
                   ) as fg21_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg21 then
                        proPOR_forecast_pickup
                   end
                   ) as fg21_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg22 then
                        proPOR_forecast_current
                   end
                   ) as fg22_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg22 then
                        proPOR_forecast_pickup
                   end
                   ) as fg22_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg23 then
                        proPOR_forecast_current
                   end
                   ) as fg23_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg23 then
                        proPOR_forecast_pickup
                   end
                   ) as fg23_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg24 then
                        proPOR_forecast_current
                   end
                   ) as fg24_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg24 then
                        proPOR_forecast_pickup
                   end
                   ) as fg24_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg25 then
                        proPOR_forecast_current
                   end
                   ) as fg25_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg25 then
                        proPOR_forecast_pickup
                   end
                   ) as fg25_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg26 then
                        proPOR_forecast_current
                   end
                   ) as fg26_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg26 then
                        proPOR_forecast_pickup
                   end
                   ) as fg26_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg27 then
                        proPOR_forecast_current
                   end
                   ) as fg27_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg27 then
                        proPOR_forecast_pickup
                   end
                   ) as fg27_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg28 then
                        proPOR_forecast_current
                   end
                   ) as fg28_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg28 then
                        proPOR_forecast_pickup
                   end
                   ) as fg28_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg29 then
                        proPOR_forecast_current
                   end
                   ) as fg29_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg29 then
                        proPOR_forecast_pickup
                   end
                   ) as fg29_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg30 then
                        proPOR_forecast_current
                   end
                   ) as fg30_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg30 then
                        proPOR_forecast_pickup
                   end
                   ) as fg30_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg31 then
                        proPOR_forecast_current
                   end
                   ) as fg31_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg31 then
                        proPOR_forecast_pickup
                   end
                   ) as fg31_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg32 then
                        proPOR_forecast_current
                   end
                   ) as fg32_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg32 then
                        proPOR_forecast_pickup
                   end
                   ) as fg32_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg33 then
                        proPOR_forecast_current
                   end
                   ) as fg33_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg33 then
                        proPOR_forecast_pickup
                   end
                   ) as fg33_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg34 then
                        proPOR_forecast_current
                   end
                   ) as fg34_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg34 then
                        proPOR_forecast_pickup
                   end
                   ) as fg34_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg35 then
                        proPOR_forecast_current
                   end
                   ) as fg35_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg35 then
                        proPOR_forecast_pickup
                   end
                   ) as fg35_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg36 then
                        proPOR_forecast_current
                   end
                   ) as fg36_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg36 then
                        proPOR_forecast_pickup
                   end
                   ) as fg36_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg37 then
                        proPOR_forecast_current
                   end
                   ) as fg37_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg37 then
                        proPOR_forecast_pickup
                   end
                   ) as fg37_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg38 then
                        proPOR_forecast_current
                   end
                   ) as fg38_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg38 then
                        proPOR_forecast_pickup
                   end
                   ) as fg38_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg39 then
                        proPOR_forecast_current
                   end
                   ) as fg39_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg39 then
                        proPOR_forecast_pickup
                   end
                   ) as fg39_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg40 then
                        proPOR_forecast_current
                   end
                   ) as fg40_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg40 then
                        proPOR_forecast_pickup
                   end
                   ) as fg40_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg41 then
                        proPOR_forecast_current
                   end
                   ) as fg41_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg41 then
                        proPOR_forecast_pickup
                   end
                   ) as fg41_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg42 then
                        proPOR_forecast_current
                   end
                   ) as fg42_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg42 then
                        proPOR_forecast_pickup
                   end
                   ) as fg42_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg43 then
                        proPOR_forecast_current
                   end
                   ) as fg43_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg43 then
                        proPOR_forecast_pickup
                   end
                   ) as fg43_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg44 then
                        proPOR_forecast_current
                   end
                   ) as fg44_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg44 then
                        proPOR_forecast_pickup
                   end
                   ) as fg44_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg45 then
                        proPOR_forecast_current
                   end
                   ) as fg45_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg45 then
                        proPOR_forecast_pickup
                   end
                   ) as fg45_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg46 then
                        proPOR_forecast_current
                   end
                   ) as fg46_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg46 then
                        proPOR_forecast_pickup
                   end
                   ) as fg46_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg47 then
                        proPOR_forecast_current
                   end
                   ) as fg47_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg47 then
                        proPOR_forecast_pickup
                   end
                   ) as fg47_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg48 then
                        proPOR_forecast_current
                   end
                   ) as fg48_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg48 then
                        proPOR_forecast_pickup
                   end
                   ) as fg48_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg49 then
                        proPOR_forecast_current
                   end
                   ) as fg49_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg49 then
                        proPOR_forecast_pickup
                   end
                   ) as fg49_proPOR_forecast_pickup,
               (case forecast_group_id
                    when @fg50 then
                        proPOR_forecast_current
                   end
                   ) as fg50_proPOR_forecast_current,
               (case forecast_group_id
                    when @fg50 then
                        proPOR_forecast_pickup
                   end
                   ) as fg50_proPOR_forecast_pickup
        from
            (
                select base.occupancy_dt,
                       base.forecast_group_id,
                       datename(dw, base.occupancy_dt) as dow,
                       isnull(a.rooms_sold, 0.0) as roomsoldcurrent,
                       isnull(a.room_revenue, 0.0) as bookedroomrevenuecurrent,
                       isnull(a.adr, 0.0) as bookedadrcurrent,
                       isnull(b.occupancy_forecast_current, 0.0) as occfcstcurrent,
                       isnull(b.fcsted_room_revenue_current, 0.0) as fcstedroomrevenuecurrent,
                       isnull(b.fcsted_adr_current, 0.0) as fcstedadrcurrent,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_rooms_sold, isnull(a.rooms_sold, 0.0))
                                   - ISNULL(c.start_date_rooms_sold, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.rooms_sold, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_rooms_sold, 0.0) - ISNULL(c.start_date_rooms_sold, 0.0) as numeric(19, 2))
                           end as roomssoldpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_date_occupancy_nbr, isnull(b.occupancy_forecast_current, 0.0))
                                   - ISNULL(d.start_date_occupancy_nbr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.occupancy_forecast_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_date_occupancy_nbr, 0.0) - ISNULL(d.start_date_occupancy_nbr, 0.0) as numeric(19, 2))
                           end as occfcstpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_room_revenue, isnull(a.room_revenue, 0.0))
                                   - ISNULL(c.start_date_room_revenue, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.room_revenue, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_room_revenue, 0.0) - ISNULL(c.start_date_room_revenue, 0.0) as numeric(19, 2))
                           end as bookedroomrevenuepickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_date_revenue, isnull(b.fcsted_room_revenue_current, 0.0))
                                   - ISNULL(d.start_date_revenue, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.fcsted_room_revenue_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_date_revenue, 0.0) - ISNULL(d.start_date_revenue, 0.0) as numeric(19, 2))
                           end as fcstedroomrevenuepickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_adr, isnull(a.adr, 0.0))
                                   - ISNULL(c.start_date_adr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.adr, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_adr, 0.0) - ISNULL(c.start_date_adr, 0.0) as numeric(19, 2))
                           end as bookedadrpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_date_adr, isnull(b.fcsted_adr_current, 0.0))
                                   - ISNULL(d.start_date_adr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.fcsted_adr_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_date_adr, 0.0) - ISNULL(d.start_date_adr, 0.0) as numeric(19, 2))
                           end as fcstedadrpickup,
                       groupBlock.block,
                       groupBlock.block_pickup,
                       groupBlock.block_available,
                       isnull(onBooksProfitCurrent.profit, 0.0) as profit_onBooks_current,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(onBooksProfitPickup.end_date_profit, isnull(onBooksProfitCurrent.profit, 0.0))
                                   - ISNULL(
                                            onBooksProfitPickup.start_date_profit,
                                            IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(onBooksProfitCurrent.profit, 0.0))
                                        ) as numeric(19, 2))
                           else
                               cast(isnull(onBooksProfitPickup.end_date_profit, 0.0)
                                   - ISNULL(onBooksProfitPickup.start_date_profit, 0.0) as numeric(19, 2))
                           end as profit_onBooks_pickup,
                       isnull(onBooksProfitCurrent.proPOR, 0.0) as proPOR_onBooks_current,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(onBooksProfitPickup.end_date_proPOR, isnull(onBooksProfitCurrent.proPOR, 0.0))
                                   - ISNULL(
                                            onBooksProfitPickup.start_date_proPOR,
                                            IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(onBooksProfitCurrent.proPOR, 0.0))
                                        ) as numeric(19, 2))
                           else
                               cast(isnull(onBooksProfitPickup.end_date_proPOR, 0.0)
                                   - ISNULL(onBooksProfitPickup.start_date_proPOR, 0.0) as numeric(19, 2))
                           end as proPOR_onBooks_pickup,
                       isnull(forecastProfitCurrent.profit, 0.0) as profit_forecast_current,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(forecastProfitPickup.end_date_profit, isnull(forecastProfitCurrent.profit, 0.0))
                                   - ISNULL(
                                            forecastProfitPickup.start_date_profit,
                                            IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(forecastProfitCurrent.profit, 0.0))
                                        ) as numeric(19, 2))
                           else
                               cast(isnull(forecastProfitPickup.end_date_profit, 0.0)
                                   - ISNULL(forecastProfitPickup.start_date_profit, 0.0) as numeric(19, 2))
                           end as profit_forecast_pickup,
                       isnull(forecastProfitCurrent.proPOR, 0.0) as proPOR_forecast_current,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(forecastProfitPickup.end_date_proPOR, isnull(forecastProfitCurrent.proPOR, 0.0))
                                   - ISNULL(
                                            forecastProfitPickup.start_date_proPOR,
                                            IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(forecastProfitCurrent.proPOR, 0.0))
                                        ) as numeric(19, 2))
                           else
                               cast(isnull(forecastProfitPickup.end_date_proPOR, 0.0)
                                   - ISNULL(forecastProfitPickup.start_date_proPOR, 0.0) as numeric(19, 2))
                           end as proPOR_forecast_pickup
                from
                    (
                        select @property_id property_id,
                               CAST(calendar_date as date) Occupancy_DT,
                               Forecast_Group_id
                        from calendar_dim
                                 left join @tempFG
                                           on Forecast_Group_ID is not null
                        where calendar_date
                                  between @start_date and @end_date
                    ) base
                        left join
                    (
                        select *
                        from dbo.ufn_get_adr_roomrev_by_individual_fg(
                                @property_id,
                                @forecast_group_id,
                                @start_date,
                                @end_date
                            )
                    ) as a
                    on base.property_id = a.property_id
                        and base.occupancy_dt = a.occupancy_dt
                        and base.forecast_group_id = a.forecast_group_id
                        left join
                    (
                        select *
                        from dbo.ufn_get_occupancy_forecast_by_individual_fg(
                                @property_id,
                                3,
                                13,
                                @forecast_group_id,
                                @start_date,
                                @end_date
                            )
                    ) as b
                    on base.property_id = b.property_id
                        and base.occupancy_dt = b.occupancy_dt
                        and base.forecast_group_id = b.forecast_group_id
                        left join
                    (
                        select q2.property_id,
                               q2.occupancy_dt,
                               q2.forecast_group_id,
                               q1.rooms_sold as end_date_rooms_sold,
                               q2.rooms_sold as start_date_rooms_sold,
                               q1.room_revenue as end_date_room_revenue,
                               q2.room_revenue as start_date_room_revenue,
                               q1.adr as end_date_adr,
                               q2.adr as start_date_adr
                        from
                            (
                                select occupancy_dt,
                                       property_id,
                                       Forecast_Group_Id,
                                       rooms_sold,
                                       room_revenue,
                                       adr
                                from dbo.ufn_get_adr_roomrev_asof_businessdate_by_individual_fg_for_pickup(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q1
                                right outer join
                            (
                                select occupancy_dt,
                                       property_id,
                                       Forecast_Group_Id,
                                       rooms_sold,
                                       room_revenue,
                                       adr
                                from dbo.ufn_get_adr_roomrev_asof_businessdate_by_individual_fg_for_pickup(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q2
                            on q1.property_id = q2.property_id
                                and q1.occupancy_dt = q2.occupancy_dt
                                and q1.forecast_group_id = q2.forecast_group_id
                    ) as c
                    on base.property_id = c.property_id
                        and base.occupancy_dt = c.occupancy_dt
                        and base.forecast_group_id = c.forecast_group_id
                        left join
                    (
                        select q2.property_id,
                               q2.occupancy_dt,
                               q2.forecast_group_id,
                               q1.occupancy_nbr as end_date_occupancy_nbr,
                               q2.occupancy_nbr as start_date_occupancy_nbr,
                               q1.revenue as end_date_revenue,
                               q2.revenue as start_date_revenue,
                               q1.adr as end_date_adr,
                               q2.adr as start_date_adr
                        from
                            (
                                select *
                                from dbo.ufn_get_occupancy_forecast_asof_businessdate_by_individual_fg(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q1
                                right outer join
                            (
                                select *
                                from dbo.ufn_get_occupancy_forecast_asof_businessdate_by_individual_fg(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q2
                            on q1.property_id = q2.property_id
                                and q1.occupancy_dt = q2.occupancy_dt
                                and q1.forecast_group_id = q2.forecast_group_id
                    ) as d
                    on base.property_id = d.property_id
                        and base.occupancy_dt = d.occupancy_dt
                        and base.forecast_group_id = d.forecast_group_id
                        left join
                    (
                        select *
                        from dbo.ufn_get_onBooks_profit_metrics_by_individual_fg(
                                @property_id,
                                @forecast_group_id,
                                @start_date,
                                @end_date
                            )
                    ) as onBooksProfitCurrent
                    on base.property_id = onBooksProfitCurrent.property_id
                        and base.occupancy_dt = onBooksProfitCurrent.occupancy_dt
                        and base.forecast_group_id = onBooksProfitCurrent.forecast_group_id
                        left join
                    (
                        select startData.property_id,
                               startData.occupancy_dt,
                               startData.forecast_group_id,
                               endData.profit as end_date_profit,
                               startData.profit as start_date_profit,
                               endData.proPOR as end_date_proPOR,
                               startData.proPOR as start_date_proPOR
                        from
                            (
                                select *
                                from dbo.ufn_get_onBooks_profit_metrics_asof_businessdate_by_individual_fg_for_pickup(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) endData
                                right outer join
                            (
                                select *
                                from dbo.ufn_get_onBooks_profit_metrics_asof_businessdate_by_individual_fg_for_pickup(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) startData
                            on endData.property_id = startData.property_id
                                and endData.occupancy_dt = startData.occupancy_dt
                                and endData.forecast_group_id = startData.forecast_group_id
                    ) as onBooksProfitPickup
                    on base.property_id = onBooksProfitPickup.property_id
                        and base.occupancy_dt = onBooksProfitPickup.occupancy_dt
                        and base.forecast_group_id = onBooksProfitPickup.forecast_group_id
                        left join
                    (
                        select *
                        from dbo.ufn_get_forecast_profit_metrics_by_individual_fg(
                                @property_id,
                                3,
                                13,
                                @forecast_group_id,
                                @start_date,
                                @end_date
                            )
                    ) as forecastProfitCurrent
                    on base.property_id = forecastProfitCurrent.property_id
                        and base.occupancy_dt = forecastProfitCurrent.occupancy_dt
                        and base.forecast_group_id = forecastProfitCurrent.forecast_group_id
                        left join
                    (
                        select startData.property_id,
                               startData.occupancy_dt,
                               startData.forecast_group_id,
                               endData.profit as end_date_profit,
                               startData.profit as start_date_profit,
                               endData.proPOR as end_date_proPOR,
                               startData.proPOR as start_date_proPOR
                        from
                            (
                                select *
                                from dbo.ufn_get_forecast_profit_metrics_asof_businessdate_by_individual_fg(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) endData
                                right outer join
                            (
                                select *
                                from dbo.ufn_get_forecast_profit_metrics_asof_businessdate_by_individual_fg(
                                        @property_id,
                                        @forecast_group_id,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) startData
                            on endData.property_id = startData.property_id
                                and endData.occupancy_dt = startData.occupancy_dt
                                and endData.forecast_group_id = startData.forecast_group_id
                    ) as forecastProfitPickup
                    on base.property_id = forecastProfitPickup.property_id
                        and base.occupancy_dt = forecastProfitPickup.occupancy_dt
                        and base.forecast_group_id = forecastProfitPickup.forecast_group_id

                        --Archana added group block-Aggregated-START
                        left join
                    (
                        select *
                        from ufn_get_groupblock_grouppickup_by_ForecastGroup(
                                @property_id,
                                @forecast_group_id,
                                @start_date,
                                @end_date,
                                0
                            )
                    ) as groupBlock
                    on a.property_id = groupBlock.property_id
                        and a.occupancy_dt = groupBlock.occupancy_dt
                        and a.forecast_group_id = groupBlock.forecast_group_Id
                --Archana added group block-Aggregated-END
            ) data
    ) data2
group by occupancy_dt,
         dow
order by occupancy_dt

    return
end