if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_pricing_pace_barbyday_report]'))
drop function [ufn_get_pricing_pace_barbyday_report]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE function [dbo].[ufn_get_pricing_pace_barbyday_report]
(
		@property_id int,
		@start_date date,
		@end_date date,
		@accom_class_id int,
		@RoomTypes nvarchar(100),
		@comp_id_1 int,
		@comp_id_2 int,
		@comp_id_3 int,
		@comp_id_4 int,
		@comp_id_5 int,
		@comp_id_6 int,
		@comp_id_7 int,
		@comp_id_8 int,
		@comp_id_9 int,
		@comp_id_10 int,
		@comp_id_11 int,
		@comp_id_12 int,
		@comp_id_13 int,
		@comp_id_14 int,
		@comp_id_15 int,
		@Use_Compact_Webrate_Pace bit,
		@DisplayLRVandOVBKforDecisionPaceReport bit
)
returns  @pricing_pace_report table
(
	Caught_up_DTTM datetime,
	businessdate date,
	dow varchar (10),
	override nvarchar(50),
	LOS int ,
	Accom_name nvarchar(150),
	Rate_Code_Name nvarchar(50),
	price numeric(19,5),
	Rooms_Sold numeric(18,0),
	Property_Rooms_Sold numeric(18,0),
	Property_Forecast numeric(8,2),
	Accom_Forecast numeric(8,2),
	AccomTypeName nvarchar(150),
	overbooking numeric(8),
	lrv numeric(8,2),
	PropertyOccFcst numeric(8,2),
	OccFcst numeric(8,2),
	webrate1 numeric(8,2),comp1_name nvarchar(150),
	webrate2 numeric(8,2),comp2_name nvarchar(150),
	webrate3 numeric(8,2),comp3_name nvarchar(150),
	webrate4 numeric(8,2),comp4_name nvarchar(150),
	webrate5 numeric(8,2),comp5_name nvarchar(150),
	webrate6 numeric(8,2),comp6_name nvarchar(150),
	webrate7 numeric(8,2),comp7_name nvarchar(150),
	webrate8 numeric(8,2),comp8_name nvarchar(150),
	webrate9 numeric(8,2),comp9_name nvarchar(150),
	webrate10 numeric(8,2),comp10_name nvarchar(150),
	webrate11 numeric(8,2),comp11_name nvarchar(150),
	webrate12 numeric(8,2),comp12_name nvarchar(150),
	webrate13 numeric(8,2),comp13_name nvarchar(150),
	webrate14 numeric(8,2),comp14_name nvarchar(150),
	webrate15 numeric(8,2),comp15_name nvarchar(150)
)
as
begin
	declare @arrival_date date
select @arrival_date = DATEADD(DAY, 1, cast(@end_date as Date))

declare @Businessdate date
	set @Businessdate = DATEADD(DAY, -1, cast((select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) as Date)) --> extract caughtup date for a property

	if(@arrival_date > @Businessdate )
begin
		-- date is in future. calculate pace days from business date
		declare @num_days int
		set @num_days =  DATEDIFF ( day , @start_date , @end_date )
		set @start_date = DATEADD(DAY, -@num_days, cast(@Businessdate as Date))
		set @end_date = @Businessdate
end

	declare @roomTypesDataAvailable bit = 1
	IF NULLIF(@RoomTypes, '') IS NULL
begin
		set @roomTypesDataAvailable = 0
end

    declare @temp_decision table
	(
		Decision_ID int,
		Business_DT date
	)

	insert into @temp_decision
select  max(Decision_ID) as Decision_ID, Business_DT as business_dt
from Decision d
where  Business_DT between @start_date and @end_date
  and Decision_Type_ID=1
  and property_ID = @property_ID
group by business_dt

declare @temp_decision_CaughtUpDTTM table
	(
		Decision_ID int,
		Business_DT date,
		Decision_Type_ID int,
		Caught_up_DTTM date,
		property_ID int,
		UNIQUE CLUSTERED (property_id,Business_DT,Decision_ID)
	)

	insert into @temp_decision_CaughtUpDTTM
select  max(Decision_ID) as Decision_ID, Business_DT as business_dt, Decision_Type_ID, max(Caught_up_DTTM), property_ID  from Decision d
where  Business_DT between @start_date and @end_date
  and Decision_Type_ID=1
  and property_ID = @property_ID
group by property_id, business_dt,Decision_Type_ID

    insert into @pricing_pace_report
select a.Caught_up_DTTM,
       CONVERT(date, a.businessdate) as businessdate,
       DATENAME(dw,a.Caught_up_DTTM) as dow,
       override,
       a.LOS,
       Accom_name,
       Rate_Code_Name,
       Price,
       Rooms_Sold,
       Property_Rooms_Sold,
       Property_Forecast,
       Accom_Forecast,
       accom_type.Accom_Type_Name as AccomTypeName,
       Accom_overbooking.Overbooking_Decision as overbooking,
       Accom_lrv.LRV as lrv,
       PropertyOccFcst =
       case (available_Capacity)
           when 0 then 0
           else
               (Property_Forecast*100/available_Capacity)
           end,
       OccFcst =
       case (Avail_Accom_Capacity)
           when 0 then 0
           else
                       Accom_Forecast*100/Avail_Accom_Capacity
           end,
       pace_comp1.webrate,comp1_name.webrate_competitors_name,
       pace_comp2.webrate,comp2_name.webrate_competitors_name,
       pace_comp3.webrate,comp3_name.webrate_competitors_name,
       pace_comp4.webrate,comp4_name.webrate_competitors_name,
       pace_comp5.webrate,comp5_name.webrate_competitors_name,
       pace_comp6.webrate,comp6_name.webrate_competitors_name,
       pace_comp7.webrate,comp7_name.webrate_competitors_name,
       pace_comp8.webrate,comp8_name.webrate_competitors_name,
       pace_comp9.webrate,comp9_name.webrate_competitors_name,
       pace_comp10.webrate,comp10_name.webrate_competitors_name,
       pace_comp11.webrate,comp11_name.webrate_competitors_name,
       pace_comp12.webrate,comp12_name.webrate_competitors_name,
       pace_comp13.webrate,comp13_name.webrate_competitors_name,
       pace_comp14.webrate,comp14_name.webrate_competitors_name,
       pace_comp15.webrate,comp15_name.webrate_competitors_name
from
    (
        select Business_DT,cast(Decision.Caught_up_DTTM as DaTE) as Caught_up_DTTM,Decision.Business_DT as businessdate,LOS,
               Rate_Unqualified.Rate_Code_Name,override,Accom_class.Accom_Class_Name Accom_name,Derived_Unqualified_Value as Price,PACE_Bar_Output.Decision_ID from
            PACE_Bar_Output inner join @temp_decision_CaughtUpDTTM Decision on PACE_Bar_Output.Decision_ID=Decision.Decision_ID
                            inner join Rate_Unqualified on PACE_Bar_Output.Rate_Unqualified_ID=Rate_Unqualified.Rate_Unqualified_ID
                            inner join Accom_Class on PACE_Bar_Output.Accom_Class_ID=Accom_Class.Accom_Class_ID
        where
                PACE_Bar_Output.Property_ID = @property_id AND
                PACE_Bar_Output.Arrival_DT=@arrival_date
          and
                PACE_Bar_Output.Accom_Class_ID=@accom_class_id



    ) as a left join
    (
        select Business_Day_End_DT,cast(PACE_Accom_Activity.SnapShot_DTTM as DATE) as Caught_up_DTTM,Accom_Class.Accom_Class_Name,SUM(Rooms_Sold) as Rooms_Sold,
               (SUM(Accom_Capacity)-SUM(Rooms_Not_Avail_Maint)-SUM(Rooms_Not_Avail_Other)) as Avail_Accom_Capacity from
            PACE_Accom_Activity inner join Accom_Type on PACE_Accom_Activity.Accom_Type_ID=Accom_Type.Accom_Type_ID
                                inner join Accom_Class on Accom_Type.Accom_Class_ID=Accom_Class.Accom_Class_ID
        where
                Accom_Type.Accom_Class_ID=@accom_class_id
          and
                PACE_Accom_Activity.Property_ID=@property_id
          and
                PACE_Accom_Activity.Occupancy_DT=@arrival_date
          and
            PACE_Accom_Activity.Business_Day_End_DT  between   @start_date and @end_date
        group by PACE_Accom_Activity.Business_Day_End_DT,PACE_Accom_Activity.SnapShot_DTTM,Accom_Class.Accom_Class_Name
    ) as b on  a.Accom_name=b.Accom_Class_Name
        and
               a.Business_DT=b.Business_Day_End_DT
           left join
    (
        select Business_Day_End_DT,cast(PACE_Total_Activity.SnapShot_DTTM as DATE) as Caught_up_DTTM,Rooms_Sold as Property_Rooms_Sold,
               (Total_Accom_Capacity-Rooms_Not_Avail_Maint-Rooms_Not_Avail_Other) as available_Capacity from PACE_Total_Activity
        where
                PACE_Total_Activity.Property_ID=@property_id
          and
                PACE_Total_Activity.Occupancy_DT=@arrival_date
          and
            PACE_Total_Activity.Business_Day_End_DT  between @start_date and  @end_date
    ) as c on a.Business_DT=c.Business_Day_End_DT left join
    (
        select Business_DT,cast(Decision.Caught_up_DTTM as DATE) as Caught_up_DTTM,sum(Occupancy_NBR) as Property_Forecast from
            PACE_Accom_Occupancy_FCST inner join @temp_decision_CaughtUpDTTM Decision on PACE_Accom_Occupancy_FCST.Decision_ID=Decision.Decision_ID
                                      INNER JOIN Decision_Type on Decision.Decision_Type_ID=Decision_Type.Decision_Type_ID
                                      inner join Accom_Type at on PACE_Accom_Occupancy_FCST.accom_type_id = at.accom_type_id and at.isComponentRoom ='N'
        where
            PACE_Accom_Occupancy_FCST.Property_ID=@property_id
          and
            PACE_Accom_Occupancy_FCST.Occupancy_DT=@arrival_date
        group by Decision.Caught_up_DTTM,Business_DT
    ) as d on a.Business_DT=d.Business_DT left join
    (
        select Business_DT,cast(Decision.Caught_up_DTTM as DATE) as Caught_up_DTTM,sum(Occupancy_NBR) as Accom_Forecast from
            PACE_Accom_Occupancy_FCST inner join @temp_decision_CaughtUpDTTM Decision on PACE_Accom_Occupancy_FCST.Decision_ID=Decision.Decision_ID
                                      INNER JOIN Decision_Type on Decision.Decision_Type_ID=Decision_Type.Decision_Type_ID
                                      inner join Accom_Type on PACE_Accom_Occupancy_FCST.Accom_Type_ID=Accom_Type.Accom_Type_ID
        where
                PACE_Accom_Occupancy_FCST.Property_ID=@property_id
          and
                PACE_Accom_Occupancy_FCST.Occupancy_DT=@arrival_date
          and
                Accom_Type.Accom_Class_ID=@accom_class_id
        group by Decision.Business_DT,Caught_up_DTTM
    ) as e on a.Business_DT=e.Business_DT
           LEFT JOIN (
        select at.Accom_Type_Name,
               ac.Accom_Class_Name,
               at.Accom_Type_ID

        from Accom_Type at
				INNER JOIN Accom_Class ac on at.Accom_Class_ID=ac.Accom_Class_ID
        where @DisplayLRVandOVBKforDecisionPaceReport=1
          and @roomTypesDataAvailable=0

        UNION

        select at.Accom_Type_Name,
            ac.Accom_Class_Name,
            at.Accom_Type_ID

        FROM Accom_Type at
            INNER JOIN Accom_Class ac on at.Accom_Class_ID=ac.Accom_Class_ID
        WHERE at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))
          AND @DisplayLRVandOVBKforDecisionPaceReport=1
          and @roomTypesDataAvailable=1

    ) as accom_type on a.Accom_name=accom_type.Accom_Class_Name
           LEFT JOIN (
        SELECT poa.Overbooking_Decision,
               poa.Decision_ID as Decision_ID,
               poa.Accom_Type_ID
        FROM PACE_Ovrbk_Accom	poa
                 INNER JOIN @temp_decision d ON poa.Decision_ID = d.Decision_ID
        WHERE poa.Property_ID = @property_id AND
                poa.Occupancy_DT=@arrival_date
          and @DisplayLRVandOVBKforDecisionPaceReport=1

    ) as Accom_overbooking on a.Decision_ID=Accom_overbooking.Decision_ID AND accom_type.Accom_Type_ID=Accom_overbooking.Accom_Type_ID
           LEFT JOIN (
        SELECT
            plrv.LRV,
            plrv.Decision_ID as Decision_ID,
            ac.Accom_Class_Name as Accom_Class_Name
        FROM PACE_LRV	plrv
                 INNER JOIN Accom_Class ac ON plrv.Accom_Class_ID=ac.Accom_Class_ID
                 INNER JOIN @temp_decision d ON plrv.Decision_ID = d.Decision_ID
        WHERE plrv.Property_ID = @property_id AND
                plrv.Occupancy_DT=@arrival_date
          and @DisplayLRVandOVBKforDecisionPaceReport=1


    ) as Accom_lrv on a.Decision_ID=Accom_lrv.Decision_ID AND a.Accom_name=Accom_lrv.Accom_Class_Name
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_1,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_1>0
    )pace_comp1 on a.Business_DT=pace_comp1.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_2,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_2>0
    )pace_comp2 on a.Business_DT=pace_comp2.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_3,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_3>0
    )pace_comp3 on a.Business_DT=pace_comp3.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_4,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_4>0
    )pace_comp4 on a.Business_DT=pace_comp4.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_5,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_5>0
    )pace_comp5 on a.Business_DT=pace_comp5.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_6,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_6>0
    )pace_comp6 on a.Business_DT=pace_comp6.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_7,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_7>0
    )pace_comp7 on a.Business_DT=pace_comp7.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_8,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_8>0
    )pace_comp8 on a.Business_DT=pace_comp8.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_9,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_9>0
    )pace_comp9 on a.Business_DT=pace_comp9.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_10,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_10>0
    )pace_comp10 on a.Business_DT=pace_comp10.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_11,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_11>0
    )pace_comp11 on a.Business_DT=pace_comp11.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_12,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_12>0
    )pace_comp12 on a.Business_DT=pace_comp12.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_13,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_13>0
    )pace_comp13 on a.Business_DT=pace_comp13.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_14,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_14>0
    )pace_comp14 on a.Business_DT=pace_comp14.Business_DT
           left join
    (
        select * from dbo.ufn_get_pace_web_competitors (@property_id,@comp_id_15,@accom_class_id,@arrival_date, @start_date,@end_date,@Use_Compact_Webrate_Pace) where @comp_id_15>0
    )pace_comp15 on a.Business_DT=pace_comp15.Business_DT
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_1 and Status_ID in (1,3) and @comp_id_1>0
    ) comp1_name on comp1_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_2 and Status_ID in (1,3) and @comp_id_2>0
    ) comp2_name on comp2_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_3 and Status_ID in (1,3) and @comp_id_3>0
    ) comp3_name on comp3_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_4 and Status_ID in (1,3) and @comp_id_4>0
    ) comp4_name on comp4_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_5 and Status_ID in (1,3) and @comp_id_5>0
    ) comp5_name on comp5_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_6 and Status_ID in (1,3) and @comp_id_6>0
    ) comp6_name on comp6_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_7 and Status_ID in (1,3) and @comp_id_7>0
    ) comp7_name on comp7_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_8 and Status_ID in (1,3) and @comp_id_8>0
    ) comp8_name on comp8_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_9 and Status_ID in (1,3) and @comp_id_9>0
    ) comp9_name on comp9_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_10 and Status_ID in (1,3) and @comp_id_10>0
    ) comp10_name on comp10_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_11 and Status_ID in (1,3) and @comp_id_11>0
    ) comp11_name on comp11_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_12 and Status_ID in (1,3) and @comp_id_12>0
    ) comp12_name on comp12_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_13 and Status_ID in (1,3) and @comp_id_13>0
    ) comp13_name on comp13_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_14 and Status_ID in (1,3) and @comp_id_14>0
    ) comp14_name on comp14_name.Property_ID=@property_id
           left join
    (
        select Property_ID,webrate_competitors_name from Webrate_Competitors where Property_ID=@property_id and Webrate_Competitors_ID =@comp_id_15 and Status_ID in (1,3) and @comp_id_15>0
    ) comp15_name on comp15_name.Property_ID=@property_id
order by businessdate DESC

    return
end
