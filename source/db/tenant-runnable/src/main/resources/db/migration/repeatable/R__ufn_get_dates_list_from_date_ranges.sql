DROP FUNCTION IF EXISTS [dbo].[ufn_get_dates_list_from_date_ranges]
GO

CREATE FUNCTION [dbo].[ufn_get_dates_list_from_date_ranges]
(
	@occupancy_start_date DATE,
	@occupancy_end_date DATE,
	@st19_start_date DATE
)
RETURNS @st19_dates TABLE (
    idx INT,
    occupancy_date DATE,
    st19_date DATE
)
AS
BEGIN
	DECLARE @idx int = 1;

	WHILE (@occupancy_start_date <= @occupancy_end_date)
		BEGIN
			INSERT INTO @st19_dates VALUES (@idx, @occupancy_start_date, @st19_start_date);

			SET @idx = @idx + 1;
			SET @occupancy_start_date = DATEADD(DAY, 1, @occupancy_start_date);
			SET @st19_start_date = DATEADD(DAY, 1, @st19_start_date);
		END

	RETURN
END
GO
