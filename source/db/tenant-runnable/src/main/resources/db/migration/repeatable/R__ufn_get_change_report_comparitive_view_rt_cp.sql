if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_change_report_comparative_view_rt_cp]'))
drop function [ufn_get_change_report_comparative_view_rt_cp]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Function Name: ufn_get_change_report_comparative_view_rt_cp

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@roomtype_id --> Room types in comma seperated format (Max 8 supported)
	@business_dt --> this is the derived version of caughtup_date(start_date)..for more details please check the data dictionary.
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_business_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_start_date --> used when @isRollingDate is 1, else, this can be blank
	@rolling_end_date --> used when @isRollingDate is 1, else, this can be blank
	@use_physical_capacity --> whether or not to use physical capacity in revpar calculations
	@useCpPaceDifferentialTableEnabled --> 1-refer to newly introduced table CP_Pace_Decision_Bar_Output_Differential 0-refer to original table CP_Pace_Decision_Bar_Output
		
Ouput Parameter : NA

Execution: this is just an example
	Example : change Report by room Type in comparative view
	------------------------------------------------
	select * from ufn_get_change_report_comparative_view_rt_cp (10016,'39,40,41,42,43,44,45,46','2011-06-18','2011-07-01','2011-07-31',1, 0)


	
Purpose: The purpose of this procedure is to report change metrics for Room type in comparative view

Assumptions : Please make sure to pass room type Level ids as a string ('81').
              When you are passing multiple accomodation type ids, please enclose them in single quotes
              separated by a comma - ('81,82,83'). Please refer to example #2.
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/29/2012		Atul				Shendye					Initial Version
02/26/2013		Archana				Mundaye					Added group block,group pickup
03/10/2017    Sam         Naden           Adding in param use_physical_capacity
09/13/2017		Ketan				Chitale					Last optimization in Rolling Dates
11/16/2021 		Shilpa 				Shaha					BHASK-1974 : Changed logic not to substract actual data from actual data in case no data present in pace.This will applicable while calculating either pickup/change
12/09/2021 		Shilpa 				Shaha					FEYNMAN-877 Pickup/Change Report: Data Discrepancy for past date
05/25/2022      Vikas               Shivankar               FEYNMAN-1447 Change Report- Data Discrepancies at Property level.
07/20/2022      Shilpa              Shaha                   FEYNMAN-1669 UI-Change Report- CP_Pace_Decision_Bar_Output to CP_Pace_Decision_Bar_Output_differtial migration
***************************************************************************************/

CREATE function [dbo].[ufn_get_change_report_comparative_view_rt_cp]
(
	@property_id int,
	@roomtype_id varchar(500),
	@business_dt date,
	@start_date date,
	@end_date date,
	@isRollingDate int,
	@rolling_business_dt nvarchar(50),
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@use_physical_capacity int,
	@useCpPaceDifferentialTableEnabled int
)		
returns  @change_report_rt_comparative_view table
	(	
		occupancy_dt date
		,dow varchar(10)
		,special_event nvarchar(500)
		,outoforder numeric(18,0)   
		,rt1_rooms_sold int                                                           
		,rt1_rooms_solds_change int                                                   
		,rt2_rooms_sold int                                                           
		,rt2_rooms_solds_change int                                                   
		,rt3_rooms_sold int                                                           
		,rt3_rooms_solds_change int                                                   
		,rt4_rooms_sold int                                                           
		,rt4_rooms_solds_change int                                                   
		,rt5_rooms_sold int                                                           
		,rt5_rooms_solds_change int                                                   
		,rt6_rooms_sold int                                                           
		,rt6_rooms_solds_change int                                                   
		,rt7_rooms_sold int                                                           
		,rt7_rooms_solds_change int                                                   
		,rt8_rooms_sold int                                                           
		,rt8_rooms_solds_change int                                                   
		
		,rt1_occupancy_nbr numeric(8,2)                                               
		,rt1_occupancy_change numeric(8,2)                                            
		,rt2_occupancy_nbr numeric(8,2)                                               
		,rt2_occupancy_change numeric(8,2)                                            
		,rt3_occupancy_nbr numeric(8,2)                                               
		,rt3_occupancy_change numeric(8,2)                                            
		,rt4_occupancy_nbr numeric(8,2)                                               
		,rt4_occupancy_change numeric(8,2)                                            
		,rt5_occupancy_nbr numeric(8,2)                                               
		,rt5_occupancy_change numeric(8,2)                                            
		,rt6_occupancy_nbr numeric(8,2)                                               
		,rt6_occupancy_change numeric(8,2)                                            
		,rt7_occupancy_nbr numeric(8,2)                                               
		,rt7_occupancy_change numeric(8,2)                                            
		,rt8_occupancy_nbr numeric(8,2)                                               
		,rt8_occupancy_change numeric(8,2)                                            
		
		,rt1_occupancy_percent numeric(8,2)                                           
		,rt1_occupancy_perc_change numeric(8,2)                                       
		,rt2_occupancy_percent numeric(8,2)                                           
		,rt2_occupancy_perc_change numeric(8,2)                                       
		,rt3_occupancy_percent numeric(8,2)                                           
		,rt3_occupancy_perc_change numeric(8,2)                                       
		,rt4_occupancy_percent numeric(8,2)                                           
		,rt4_occupancy_perc_change numeric(8,2)                                       
		,rt5_occupancy_percent numeric(8,2)                                           
		,rt5_occupancy_perc_change numeric(8,2)                                       
		,rt6_occupancy_percent numeric(8,2)                                           
		,rt6_occupancy_perc_change numeric(8,2)                                       
		,rt7_occupancy_percent numeric(8,2)                                           
		,rt7_occupancy_perc_change numeric(8,2)                                       
		,rt8_occupancy_percent numeric(8,2)                                           
		,rt8_occupancy_perc_change numeric(8,2)                                       
		
		,rt1_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt1_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt2_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt2_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt3_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt3_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt4_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt4_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt5_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt5_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt6_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt6_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt7_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt7_occupancy_perc_change_without_ooo numeric(8,2)                                       
		,rt8_occupancy_percent_without_ooo numeric(8,2)                                           
		,rt8_occupancy_perc_change_without_ooo numeric(8,2)          
		
		,rt1_booked_revenue numeric(19,5)                                             
		,rt1_booked_revenue_change numeric(19,5)                                      
		,rt2_booked_revenue numeric(19,5)                                             
		,rt2_booked_revenue_change numeric(19,5)                                      
		,rt3_booked_revenue numeric(19,5)                                             
		,rt3_booked_revenue_change numeric(19,5)                                      
		,rt4_booked_revenue numeric(19,5)                                             
		,rt4_booked_revenue_change numeric(19,5)                                      
		,rt5_booked_revenue numeric(19,5)                                             
		,rt5_booked_revenue_change numeric(19,5)                                      
		,rt6_booked_revenue numeric(19,5)                                             
		,rt6_booked_revenue_change numeric(19,5)                                      
		,rt7_booked_revenue numeric(19,5)                                             
		,rt7_booked_revenue_change numeric(19,5)                                      
		,rt8_booked_revenue numeric(19,5)                                             
		,rt8_booked_revenue_change numeric(19,5)                                      
		
		,rt1_revenue numeric(19,5)                                                    
		,rt1_revenue_change numeric(19,5)                                             
		,rt2_revenue numeric(19,5)                                                    
		,rt2_revenue_change numeric(19,5)                                             
		,rt3_revenue numeric(19,5)                                                    
		,rt3_revenue_change numeric(19,5)                                             
		,rt4_revenue numeric(19,5)                                                    
		,rt4_revenue_change numeric(19,5)                                             
		,rt5_revenue numeric(19,5)                                                    
		,rt5_revenue_change numeric(19,5)                                             
		,rt6_revenue numeric(19,5)                                                    
		,rt6_revenue_change numeric(19,5)                                             
		,rt7_revenue numeric(19,5)                                                    
		,rt7_revenue_change numeric(19,5)                                             
		,rt8_revenue numeric(19,5)                                                    
		,rt8_revenue_change numeric(19,5)                                             
		
		,rt1_booked_adr numeric(19,5)                                                 
		,rt1_booked_adr_change numeric(19,5)                                          
		,rt2_booked_adr numeric(19,5)                                                 
		,rt2_booked_adr_change numeric(19,5)                                          
		,rt3_booked_adr numeric(19,5)                                                 
		,rt3_booked_adr_change numeric(19,5)                                          
		,rt4_booked_adr numeric(19,5)                                                 
		,rt4_booked_adr_change numeric(19,5)                                          
		,rt5_booked_adr numeric(19,5)                                                 
		,rt5_booked_adr_change numeric(19,5)                                          
		,rt6_booked_adr numeric(19,5)                                                 
		,rt6_booked_adr_change numeric(19,5)                                          
		,rt7_booked_adr numeric(19,5)                                                 
		,rt7_booked_adr_change numeric(19,5)                                          
		,rt8_booked_adr numeric(19,5)                                                 
		,rt8_booked_adr_change numeric(19,5)                                          

		,rt1_adr numeric(19,5)                                                        
		,rt1_adr_change numeric(19,5)                                                 
		,rt2_adr numeric(19,5)                                                        
		,rt2_adr_change numeric(19,5)                                                 
		,rt3_adr numeric(19,5)                                                        
		,rt3_adr_change numeric(19,5)                                                 
		,rt4_adr numeric(19,5)                                                        
		,rt4_adr_change numeric(19,5)                                                 
		,rt5_adr numeric(19,5)                                                        
		,rt5_adr_change numeric(19,5)                                                 
		,rt6_adr numeric(19,5)                                                        
		,rt6_adr_change numeric(19,5)                                                 
		,rt7_adr numeric(19,5)                                                        
		,rt7_adr_change numeric(19,5)                                                 
		,rt8_adr numeric(19,5)                                                        
		,rt8_adr_change numeric(19,5)                                                 
		
		,rt1_booked_revpar numeric(19,5)                                              
		,rt1_booked_revpar_change numeric(19,5)                                       
		,rt2_booked_revpar numeric(19,5)                                              
		,rt2_booked_revpar_change numeric(19,5)                                       
		,rt3_booked_revpar numeric(19,5)                                              
		,rt3_booked_revpar_change numeric(19,5)                                       
		,rt4_booked_revpar numeric(19,5)                                              
		,rt4_booked_revpar_change numeric(19,5)                                       
		,rt5_booked_revpar numeric(19,5)                                              
		,rt5_booked_revpar_change numeric(19,5)                                       
		,rt6_booked_revpar numeric(19,5)                                              
		,rt6_booked_revpar_change numeric(19,5)                                       
		,rt7_booked_revpar numeric(19,5)                                              
		,rt7_booked_revpar_change numeric(19,5)                                       
		,rt8_booked_revpar numeric(19,5)                                              
		,rt8_booked_revpar_change numeric(19,5)                                       
		
		,rt1_revpar numeric(19,5)                                                     
		,rt1_revpar_change numeric(19,5)                                              
		,rt2_revpar numeric(19,5)                                                     
		,rt2_revpar_change numeric(19,5)                                              
		,rt3_revpar numeric(19,5)                                                     
		,rt3_revpar_change numeric(19,5)                                              
		,rt4_revpar numeric(19,5)                                                     
		,rt4_revpar_change numeric(19,5)                                              
		,rt5_revpar numeric(19,5)                                                     
		,rt5_revpar_change numeric(19,5)                                              
		,rt6_revpar numeric(19,5)                                                     
		,rt6_revpar_change numeric(19,5)                                              
		,rt7_revpar numeric(19,5)                                                     
		,rt7_revpar_change numeric(19,5)                                              
		,rt8_revpar numeric(19,5)                                                     
		,rt8_revpar_change numeric(19,5)                                              
 
		,rt1_overbookingcurrent int                                                           
		,rt1_overbookingchange int                                                   
		,rt2_overbookingcurrent int                                                           
		,rt2_overbookingchange int                                                   
		,rt3_overbookingcurrent int                                                           
		,rt3_overbookingchange int                                                   
		,rt4_overbookingcurrent int                                                           
		,rt4_overbookingchange int                                                   
		,rt5_overbookingcurrent int                                                           
		,rt5_overbookingchange int                                                   
		,rt6_overbookingcurrent int                                                           
		,rt6_overbookingchange int                                                   
		,rt7_overbookingcurrent int                                                           
		,rt7_overbookingchange int                                                   
		,rt8_overbookingcurrent int                                                           
		,rt8_overbookingchange int  
		
		,rt1_block int                                                  
		,rt1_block_available int  
		,rt1_block_change int  
		,rt2_block int                                                
		,rt2_block_available int  
		,rt2_block_change int  
		,rt3_block int                                                
		,rt3_block_available int  
		,rt3_block_change int  
		,rt4_block int                                              
		,rt4_block_available int  
		,rt4_block_change int  
		,rt5_block int                                                 
		,rt5_block_available int  
		,rt5_block_change int  
		,rt6_block int                                                 
		,rt6_block_available int  
		,rt6_block_change int  
		,rt7_block int                                                
		,rt7_block_available int  
		,rt7_block_change int  
		,rt8_block int                                                
		,rt8_block_available int  
		,rt8_block_change int     
		
		,rt1_barcurrent numeric(19,5)
		,rt1_barchange numeric(19,5)
		,rt2_barcurrent numeric(19,5)
		,rt2_barchange numeric(19,5)
		,rt3_barcurrent numeric(19,5)
		,rt3_barchange numeric(19,5)
		,rt4_barcurrent numeric(19,5)
		,rt4_barchange numeric(19,5)
		,rt5_barcurrent numeric(19,5)
		,rt5_barchange numeric(19,5)
		,rt6_barcurrent numeric(19,5)
		,rt6_barchange numeric(19,5)
		,rt7_barcurrent numeric(19,5)
		,rt7_barchange numeric(19,5)
		,rt8_barcurrent numeric(19,5)
		,rt8_barchange numeric(19,5)
		
		,rt1_decisionreasontypecurrent nvarchar(1)
		,rt1_decisionreasontypechange nvarchar(1)	       
		,rt2_decisionreasontypecurrent nvarchar(1)
		,rt2_decisionreasontypechange nvarchar(1)	       
		,rt3_decisionreasontypecurrent nvarchar(1)
		,rt3_decisionreasontypechange nvarchar(1)	       
		,rt4_decisionreasontypecurrent nvarchar(1)
		,rt4_decisionreasontypechange nvarchar(1)	       
		,rt5_decisionreasontypecurrent nvarchar(1)
		,rt5_decisionreasontypechange nvarchar(1)	       
		,rt6_decisionreasontypecurrent nvarchar(1)
		,rt6_decisionreasontypechange nvarchar(1)	       
		,rt7_decisionreasontypecurrent nvarchar(1)
		,rt7_decisionreasontypechange nvarchar(1)	       
		,rt8_decisionreasontypecurrent nvarchar(1)
		,rt8_decisionreasontypechange nvarchar(1)	       
	)
as

begin
		declare @caughtupdate date 
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
		begin
			set @business_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_dt ,@caughtupdate))
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
		end

	declare @rt1 int
	declare @rt2 int
	declare @rt3 int
	declare @rt4 int
	declare @rt5 int
	declare @rt6 int
	declare @rt7 int
	declare @rt8 int
	
	declare @tempRT table
	(
		number int,
		accom_type_id int
	)
	insert into @tempRT
	select number = ROW_NUMBER() OVER (ORDER BY Accom_Type_id),Accom_Type_id 
		from Accom_Type where Property_ID=@property_id and Accom_Type_id in (SELECT Value FROM varcharToInt(@roomtype_id,','))
		and Status_ID=1 and System_Default=0
			
	set @rt1 = (Select Accom_Type_id from @tempRT where number=1)
	set @rt2 = (Select Accom_Type_id from @tempRT where number=2)
	set @rt3 = (Select Accom_Type_id from @tempRT where number=3)
	set @rt4 = (Select Accom_Type_id from @tempRT where number=4)
	set @rt5 = (Select Accom_Type_id from @tempRT where number=5)
	set @rt6 = (Select Accom_Type_id from @tempRT where number=6)
	set @rt7 = (Select Accom_Type_id from @tempRT where number=7)
	set @rt8 = (Select Accom_Type_id from @tempRT where number=8)
			
		--- extract report metrics
	insert into @change_report_rt_comparative_view
	select 
	occupancy_dt,dow,special_event,sum(outoforder)outoforder
	,isnull(MAX(rt1_rooms_sold),0.0) as rt1_rooms_sold,isnull(MAX(rt1_rooms_solds_change),0.0) as rt1_rooms_solds_change
	,isnull(MAX(rt2_rooms_sold),0.0) as rt2_rooms_sold,isnull(MAX(rt2_rooms_solds_change),0.0) as rt2_rooms_solds_change
	,isnull(MAX(rt3_rooms_sold),0.0) as rt3_rooms_sold,isnull(MAX(rt3_rooms_solds_change),0.0) as rt3_rooms_solds_change
	,isnull(MAX(rt4_rooms_sold),0.0) as rt4_rooms_sold,isnull(MAX(rt4_rooms_solds_change),0.0) as rt4_rooms_solds_change
	,isnull(MAX(rt5_rooms_sold),0.0) as rt5_rooms_sold,isnull(MAX(rt5_rooms_solds_change),0.0) as rt5_rooms_solds_change
	,isnull(MAX(rt6_rooms_sold),0.0) as rt6_rooms_sold,isnull(MAX(rt6_rooms_solds_change),0.0) as rt6_rooms_solds_change
	,isnull(MAX(rt7_rooms_sold),0.0) as rt7_rooms_sold,isnull(MAX(rt7_rooms_solds_change),0.0) as rt7_rooms_solds_change
	,isnull(MAX(rt8_rooms_sold),0.0) as rt8_rooms_sold,isnull(MAX(rt8_rooms_solds_change),0.0) as rt8_rooms_solds_change
	
	,isnull(MAX(rt1_occupancy_nbr),0.0) as rt1_occupancy_nbr,isnull(MAX(rt1_occupancy_change),0.0) as rt1_occupancy_change
	,isnull(MAX(rt2_occupancy_nbr),0.0) as rt2_occupancy_nbr,isnull(MAX(rt2_occupancy_change),0.0) as rt2_occupancy_change
	,isnull(MAX(rt3_occupancy_nbr),0.0) as rt3_occupancy_nbr,isnull(MAX(rt3_occupancy_change),0.0) as rt3_occupancy_change
	,isnull(MAX(rt4_occupancy_nbr),0.0) as rt4_occupancy_nbr,isnull(MAX(rt4_occupancy_change),0.0) as rt4_occupancy_change
	,isnull(MAX(rt5_occupancy_nbr),0.0) as rt5_occupancy_nbr,isnull(MAX(rt5_occupancy_change),0.0) as rt5_occupancy_change
	,isnull(MAX(rt6_occupancy_nbr),0.0) as rt6_occupancy_nbr,isnull(MAX(rt6_occupancy_change),0.0) as rt6_occupancy_change
	,isnull(MAX(rt7_occupancy_nbr),0.0) as rt7_occupancy_nbr,isnull(MAX(rt7_occupancy_change),0.0) as rt7_occupancy_change
	,isnull(MAX(rt8_occupancy_nbr),0.0) as rt8_occupancy_nbr,isnull(MAX(rt8_occupancy_change),0.0) as rt8_occupancy_change
	
	,isnull(MAX(rt1_occupancy_percent),0.0) as rt1_occupancy_percent,isnull(MAX(rt1_occupancy_perc_change),0.0) as rt1_occupancy_perc_change
	,isnull(MAX(rt2_occupancy_percent),0.0) as rt2_occupancy_percent,isnull(MAX(rt2_occupancy_perc_change),0.0) as rt2_occupancy_perc_change
	,isnull(MAX(rt3_occupancy_percent),0.0) as rt3_occupancy_percent,isnull(MAX(rt3_occupancy_perc_change),0.0) as rt3_occupancy_perc_change
	,isnull(MAX(rt4_occupancy_percent),0.0) as rt4_occupancy_percent,isnull(MAX(rt4_occupancy_perc_change),0.0) as rt4_occupancy_perc_change
	,isnull(MAX(rt5_occupancy_percent),0.0) as rt5_occupancy_percent,isnull(MAX(rt5_occupancy_perc_change),0.0) as rt5_occupancy_perc_change
	,isnull(MAX(rt6_occupancy_percent),0.0) as rt6_occupancy_percent,isnull(MAX(rt6_occupancy_perc_change),0.0) as rt6_occupancy_perc_change
	,isnull(MAX(rt7_occupancy_percent),0.0) as rt7_occupancy_percent,isnull(MAX(rt7_occupancy_perc_change),0.0) as rt7_occupancy_perc_change
	,isnull(MAX(rt8_occupancy_percent),0.0) as rt8_occupancy_percent,isnull(MAX(rt8_occupancy_perc_change),0.0) as rt8_occupancy_perc_change
	
	,isnull(MAX(rt1_occupancy_percent_without_ooo),0.0) as rt1_occupancy_percent_without_ooo,isnull(MAX(rt1_occupancy_perc_change_without_ooo),0.0) as rt1_occupancy_perc_change_without_ooo
	,isnull(MAX(rt2_occupancy_percent_without_ooo),0.0) as rt2_occupancy_percent_without_ooo,isnull(MAX(rt2_occupancy_perc_change_without_ooo),0.0) as rt2_occupancy_perc_change_without_ooo
	,isnull(MAX(rt3_occupancy_percent_without_ooo),0.0) as rt3_occupancy_percent_without_ooo,isnull(MAX(rt3_occupancy_perc_change_without_ooo),0.0) as rt3_occupancy_perc_change_without_ooo
	,isnull(MAX(rt4_occupancy_percent_without_ooo),0.0) as rt4_occupancy_percent_without_ooo,isnull(MAX(rt4_occupancy_perc_change_without_ooo),0.0) as rt4_occupancy_perc_change_without_ooo
	,isnull(MAX(rt5_occupancy_percent_without_ooo),0.0) as rt5_occupancy_percent_without_ooo,isnull(MAX(rt5_occupancy_perc_change_without_ooo),0.0) as rt5_occupancy_perc_change_without_ooo
	,isnull(MAX(rt6_occupancy_percent_without_ooo),0.0) as rt6_occupancy_percent_without_ooo,isnull(MAX(rt6_occupancy_perc_change_without_ooo),0.0) as rt6_occupancy_perc_change_without_ooo
	,isnull(MAX(rt7_occupancy_percent_without_ooo),0.0) as rt7_occupancy_percent_without_ooo,isnull(MAX(rt7_occupancy_perc_change_without_ooo),0.0) as rt7_occupancy_perc_change_without_ooo
	,isnull(MAX(rt8_occupancy_percent_without_ooo),0.0) as rt8_occupancy_percent_without_ooo,isnull(MAX(rt8_occupancy_perc_change_without_ooo),0.0) as rt8_occupancy_perc_change_without_ooo
	
	,isnull(MAX(rt1_booked_revenue),0.0) as rt1_booked_revenue,isnull(MAX(rt1_booked_revenue_change),0.0) as rt1_booked_revenue_change
	,isnull(MAX(rt2_booked_revenue),0.0) as rt2_booked_revenue,isnull(MAX(rt2_booked_revenue_change),0.0) as rt2_booked_revenue_change
	,isnull(MAX(rt3_booked_revenue),0.0) as rt3_booked_revenue,isnull(MAX(rt3_booked_revenue_change),0.0) as rt3_booked_revenue_change
	,isnull(MAX(rt4_booked_revenue),0.0) as rt4_booked_revenue,isnull(MAX(rt4_booked_revenue_change),0.0) as rt4_booked_revenue_change
	,isnull(MAX(rt5_booked_revenue),0.0) as rt5_booked_revenue,isnull(MAX(rt5_booked_revenue_change),0.0) as rt5_booked_revenue_change
	,isnull(MAX(rt6_booked_revenue),0.0) as rt6_booked_revenue,isnull(MAX(rt6_booked_revenue_change),0.0) as rt6_booked_revenue_change
	,isnull(MAX(rt7_booked_revenue),0.0) as rt7_booked_revenue,isnull(MAX(rt7_booked_revenue_change),0.0) as rt7_booked_revenue_change
	,isnull(MAX(rt8_booked_revenue),0.0) as rt8_booked_revenue,isnull(MAX(rt8_booked_revenue_change),0.0) as rt8_booked_revenue_change
	
	,isnull(MAX(rt1_revenue),0.0) as rt1_revenue,isnull(MAX(rt1_revenue_change),0.0) as rt1_revenue_change
	,isnull(MAX(rt2_revenue),0.0) as rt2_revenue,isnull(MAX(rt2_revenue_change),0.0) as rt2_revenue_change
	,isnull(MAX(rt3_revenue),0.0) as rt3_revenue,isnull(MAX(rt3_revenue_change),0.0) as rt3_revenue_change
	,isnull(MAX(rt4_revenue),0.0) as rt4_revenue,isnull(MAX(rt4_revenue_change),0.0) as rt4_revenue_change
	,isnull(MAX(rt5_revenue),0.0) as rt5_revenue,isnull(MAX(rt5_revenue_change),0.0) as rt5_revenue_change
	,isnull(MAX(rt6_revenue),0.0) as rt6_revenue,isnull(MAX(rt6_revenue_change),0.0) as rt6_revenue_change
	,isnull(MAX(rt7_revenue),0.0) as rt7_revenue,isnull(MAX(rt7_revenue_change),0.0) as rt7_revenue_change
	,isnull(MAX(rt8_revenue),0.0) as rt8_revenue,isnull(MAX(rt8_revenue_change),0.0) as rt8_revenue_change
	
	,isnull(MAX(rt1_booked_adr),0.0) as rt1_booked_adr,isnull(MAX(rt1_booked_adr_change),0.0) as rt1_booked_adr_change
	,isnull(MAX(rt2_booked_adr),0.0) as rt2_booked_adr,isnull(MAX(rt2_booked_adr_change),0.0) as rt2_booked_adr_change
	,isnull(MAX(rt3_booked_adr),0.0) as rt3_booked_adr,isnull(MAX(rt3_booked_adr_change),0.0) as rt3_booked_adr_change
	,isnull(MAX(rt4_booked_adr),0.0) as rt4_booked_adr,isnull(MAX(rt4_booked_adr_change),0.0) as rt4_booked_adr_change
	,isnull(MAX(rt5_booked_adr),0.0) as rt5_booked_adr,isnull(MAX(rt5_booked_adr_change),0.0) as rt5_booked_adr_change
	,isnull(MAX(rt6_booked_adr),0.0) as rt6_booked_adr,isnull(MAX(rt6_booked_adr_change),0.0) as rt6_booked_adr_change
	,isnull(MAX(rt7_booked_adr),0.0) as rt7_booked_adr,isnull(MAX(rt7_booked_adr_change),0.0) as rt7_booked_adr_change
	,isnull(MAX(rt8_booked_adr),0.0) as rt8_booked_adr,isnull(MAX(rt8_booked_adr_change),0.0) as rt8_booked_adr_change
	
	,isnull(MAX(rt1_adr),0.0) as rt1_adr,isnull(MAX(rt1_adr_change),0.0) as rt1_adr_change
	,isnull(MAX(rt2_adr),0.0) as rt2_adr,isnull(MAX(rt2_adr_change),0.0) as rt2_adr_change
	,isnull(MAX(rt3_adr),0.0) as rt3_adr,isnull(MAX(rt3_adr_change),0.0) as rt3_adr_change
	,isnull(MAX(rt4_adr),0.0) as rt4_adr,isnull(MAX(rt4_adr_change),0.0) as rt4_adr_change
	,isnull(MAX(rt5_adr),0.0) as rt5_adr,isnull(MAX(rt5_adr_change),0.0) as rt5_adr_change
	,isnull(MAX(rt6_adr),0.0) as rt6_adr,isnull(MAX(rt6_adr_change),0.0) as rt6_adr_change
	,isnull(MAX(rt7_adr),0.0) as rt7_adr,isnull(MAX(rt7_adr_change),0.0) as rt7_adr_change
	,isnull(MAX(rt8_adr),0.0) as rt8_adr,isnull(MAX(rt8_adr_change),0.0) as rt8_adr_change
	
	,isnull(MAX(rt1_booked_revpar),0.0) as rt1_booked_revpar,isnull(MAX(rt1_booked_revpar_change),0.0) as rt1_booked_revpar_change
	,isnull(MAX(rt2_booked_revpar),0.0) as rt2_booked_revpar,isnull(MAX(rt2_booked_revpar_change),0.0) as rt2_booked_revpar_change
	,isnull(MAX(rt3_booked_revpar),0.0) as rt3_booked_revpar,isnull(MAX(rt3_booked_revpar_change),0.0) as rt3_booked_revpar_change
	,isnull(MAX(rt4_booked_revpar),0.0) as rt4_booked_revpar,isnull(MAX(rt4_booked_revpar_change),0.0) as rt4_booked_revpar_change
	,isnull(MAX(rt5_booked_revpar),0.0) as rt5_booked_revpar,isnull(MAX(rt5_booked_revpar_change),0.0) as rt5_booked_revpar_change
	,isnull(MAX(rt6_booked_revpar),0.0) as rt6_booked_revpar,isnull(MAX(rt6_booked_revpar_change),0.0) as rt6_booked_revpar_change
	,isnull(MAX(rt7_booked_revpar),0.0) as rt7_booked_revpar,isnull(MAX(rt7_booked_revpar_change),0.0) as rt7_booked_revpar_change
	,isnull(MAX(rt8_booked_revpar),0.0) as rt8_booked_revpar,isnull(MAX(rt8_booked_revpar_change),0.0) as rt8_booked_revpar_change
	
	,isnull(MAX(rt1_revpar),0.0) as rt1_revpar,isnull(MAX(rt1_revpar_change),0.0) as rt1_revpar_change
	,isnull(MAX(rt2_revpar),0.0) as rt2_revpar,isnull(MAX(rt2_revpar_change),0.0) as rt2_revpar_change
	,isnull(MAX(rt3_revpar),0.0) as rt3_revpar,isnull(MAX(rt3_revpar_change),0.0) as rt3_revpar_change
	,isnull(MAX(rt4_revpar),0.0) as rt4_revpar,isnull(MAX(rt4_revpar_change),0.0) as rt4_revpar_change
	,isnull(MAX(rt5_revpar),0.0) as rt5_revpar,isnull(MAX(rt5_revpar_change),0.0) as rt5_revpar_change
	,isnull(MAX(rt6_revpar),0.0) as rt6_revpar,isnull(MAX(rt6_revpar_change),0.0) as rt6_revpar_change
	,isnull(MAX(rt7_revpar),0.0) as rt7_revpar,isnull(MAX(rt7_revpar_change),0.0) as rt7_revpar_change
	,isnull(MAX(rt8_revpar),0.0) as rt8_revpar,isnull(MAX(rt8_revpar_change),0.0) as rt8_revpar_change
	
	,isnull(MAX(rt1_overbookingcurrent),0.0) as rt1_overbookingcurrent, isnull(MAX(rt1_overbookingchange),0.0) as rt1_overbookingchange
	,isnull(MAX(rt2_overbookingcurrent),0.0) as rt2_overbookingcurrent, isnull(MAX(rt2_overbookingchange),0.0) as rt2_overbookingchange
	,isnull(MAX(rt3_overbookingcurrent),0.0) as rt3_overbookingcurrent, isnull(MAX(rt3_overbookingchange),0.0) as rt3_overbookingchange
	,isnull(MAX(rt4_overbookingcurrent),0.0) as rt4_overbookingcurrent, isnull(MAX(rt4_overbookingchange),0.0) as rt4_overbookingchange
	,isnull(MAX(rt5_overbookingcurrent),0.0) as rt5_overbookingcurrent, isnull(MAX(rt5_overbookingchange),0.0) as rt5_overbookingchange
	,isnull(MAX(rt6_overbookingcurrent),0.0) as rt6_overbookingcurrent, isnull(MAX(rt6_overbookingchange),0.0) as rt6_overbookingchange
	,isnull(MAX(rt7_overbookingcurrent),0.0) as rt7_overbookingcurrent, isnull(MAX(rt7_overbookingchange),0.0) as rt7_overbookingchange
	,isnull(MAX(rt8_overbookingcurrent),0.0) as rt8_overbookingcurrent, isnull(MAX(rt8_overbookingchange),0.0) as rt8_overbookingchange
	
	--Archana added for group block-, group pickup-Start
	,isnull(MAX(rt1_Blocks),0.0) as rt1_block,isnull(MAX(rt1_Blocks_Available),0.0) as rt1_block_available,isnull(MAX(rt1_Blocks_change),0.0) as rt1_block_change
	,isnull(MAX(rt2_Blocks),0.0) as rt2_block,isnull(MAX(rt2_Blocks_Available),0.0) as rt2_block_available,isnull(MAX(rt2_Blocks_change),0.0) as rt2_block_change
	,isnull(MAX(rt3_Blocks),0.0) as rt3_block,isnull(MAX(rt3_Blocks_Available),0.0) as rt3_block_available,isnull(MAX(rt3_Blocks_change),0.0) as rt3_block_change
	,isnull(MAX(rt4_Blocks),0.0) as rt4_block,isnull(MAX(rt4_Blocks_Available),0.0) as rt4_block_available,isnull(MAX(rt4_Blocks_change),0.0) as rt4_block_change
	,isnull(MAX(rt5_Blocks),0.0) as rt5_block,isnull(MAX(rt5_Blocks_Available),0.0) as rt5_block_available,isnull(MAX(rt5_Blocks_change),0.0) as rt5_block_change
	,isnull(MAX(rt6_Blocks),0.0) as rt6_block,isnull(MAX(rt6_Blocks_Available),0.0) as rt6_block_available,isnull(MAX(rt6_Blocks_change),0.0) as rt6_block_change
	,isnull(MAX(rt7_Blocks),0.0) as rt7_block,isnull(MAX(rt7_Blocks_Available),0.0) as rt7_block_available,isnull(MAX(rt7_Blocks_change),0.0) as rt7_block_change
	,isnull(MAX(rt8_Blocks),0.0) as rt8_block,isnull(MAX(rt8_Blocks_Available),0.0) as rt8_block_available,isnull(MAX(rt8_Blocks_change),0.0) as rt8_block_change
	--Archana added for group block-, group pickup-End

	,isnull(MAX(rt1_barcurrent),0.0) as rt1_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt1_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt1_barchange),0.0) END as rt1_barchange
	,isnull(MAX(rt2_barcurrent),0.0) as rt2_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt2_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt2_barchange),0.0) END as rt2_barchange
	,isnull(MAX(rt3_barcurrent),0.0) as rt3_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt3_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt3_barchange),0.0) END as rt3_barchange
	,isnull(MAX(rt4_barcurrent),0.0) as rt4_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt4_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt4_barchange),0.0) END as rt4_barchange
	,isnull(MAX(rt5_barcurrent),0.0) as rt5_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt5_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt5_barchange),0.0) END as rt5_barchange
	,isnull(MAX(rt6_barcurrent),0.0) as rt6_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt6_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt6_barchange),0.0) END as rt6_barchange
	,isnull(MAX(rt7_barcurrent),0.0) as rt7_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt7_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt7_barchange),0.0) END as rt7_barchange
	,isnull(MAX(rt8_barcurrent),0.0) as rt8_barcurrent,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN isnull(MAX(rt8_barchangeLastOptimize),0.0) ELSE isnull(MAX(rt8_barchange),0.0) END as rt8_barchange


	,isNull(MAX(rt1_decisionreasontypecurrent),'-') as rt1_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt1_decisionreasontypecurrent), MAX(rt1_decisionreasontypechange))) END as rt1_decisionreasontypechange
	,isNull(MAX(rt2_decisionreasontypecurrent),'-') as rt2_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt2_decisionreasontypecurrent), MAX(rt2_decisionreasontypechange))) END as rt2_decisionreasontypechange
	,isNull(MAX(rt3_decisionreasontypecurrent),'-') as rt3_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt3_decisionreasontypecurrent), MAX(rt3_decisionreasontypechange))) END as rt3_decisionreasontypechange
	,isNull(MAX(rt4_decisionreasontypecurrent),'-') as rt4_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt4_decisionreasontypecurrent), MAX(rt4_decisionreasontypechange))) END as rt4_decisionreasontypechange
	,isNull(MAX(rt5_decisionreasontypecurrent),'-') as rt5_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt5_decisionreasontypecurrent), MAX(rt5_decisionreasontypechange))) END as rt5_decisionreasontypechange
	,isNull(MAX(rt6_decisionreasontypecurrent),'-') as rt6_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt6_decisionreasontypecurrent), MAX(rt6_decisionreasontypechange))) END as rt6_decisionreasontypechange
	,isNull(MAX(rt7_decisionreasontypecurrent),'-') as rt7_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt7_decisionreasontypecurrent), MAX(rt7_decisionreasontypechange))) END as rt7_decisionreasontypechange
	,isNull(MAX(rt8_decisionreasontypecurrent),'-') as rt8_decisionreasontypecurrent
	,CASE @rolling_business_dt WHEN 'LAST_OPTIMIZATION' THEN NULL ELSE (select dbo.ufn_determine_change_by_current_and_past_value (MAX(rt8_decisionreasontypecurrent), MAX(rt8_decisionreasontypechange))) END as rt8_decisionreasontypechange

	from
	(

	select 
		occupancy_dt,dow,special_event,outoforder
		,(case accom_type_id when @rt1 then rooms_sold end) as rt1_rooms_sold,(case accom_type_id when @rt1 then rooms_solds_change end) as rt1_rooms_solds_change
		,(case accom_type_id when @rt2 then rooms_sold end) as rt2_rooms_sold,(case accom_type_id when @rt2 then rooms_solds_change end) as rt2_rooms_solds_change
		,(case accom_type_id when @rt3 then rooms_sold end) as rt3_rooms_sold,(case accom_type_id when @rt3 then rooms_solds_change end) as rt3_rooms_solds_change
		,(case accom_type_id when @rt4 then rooms_sold end) as rt4_rooms_sold,(case accom_type_id when @rt4 then rooms_solds_change end) as rt4_rooms_solds_change
		,(case accom_type_id when @rt5 then rooms_sold end) as rt5_rooms_sold,(case accom_type_id when @rt5 then rooms_solds_change end) as rt5_rooms_solds_change
		,(case accom_type_id when @rt6 then rooms_sold end) as rt6_rooms_sold,(case accom_type_id when @rt6 then rooms_solds_change end) as rt6_rooms_solds_change
		,(case accom_type_id when @rt7 then rooms_sold end) as rt7_rooms_sold,(case accom_type_id when @rt7 then rooms_solds_change end) as rt7_rooms_solds_change
		,(case accom_type_id when @rt8 then rooms_sold end) as rt8_rooms_sold,(case accom_type_id when @rt8 then rooms_solds_change end) as rt8_rooms_solds_change
	
		,(case accom_type_id when @rt1 then occupancy_nbr end) as rt1_occupancy_nbr,(case accom_type_id when @rt1 then occupancy_change end) as rt1_occupancy_change
		,(case accom_type_id when @rt2 then occupancy_nbr end) as rt2_occupancy_nbr,(case accom_type_id when @rt2 then occupancy_change end) as rt2_occupancy_change
		,(case accom_type_id when @rt3 then occupancy_nbr end) as rt3_occupancy_nbr,(case accom_type_id when @rt3 then occupancy_change end) as rt3_occupancy_change
		,(case accom_type_id when @rt4 then occupancy_nbr end) as rt4_occupancy_nbr,(case accom_type_id when @rt4 then occupancy_change end) as rt4_occupancy_change
		,(case accom_type_id when @rt5 then occupancy_nbr end) as rt5_occupancy_nbr,(case accom_type_id when @rt5 then occupancy_change end) as rt5_occupancy_change
		,(case accom_type_id when @rt6 then occupancy_nbr end) as rt6_occupancy_nbr,(case accom_type_id when @rt6 then occupancy_change end) as rt6_occupancy_change
		,(case accom_type_id when @rt7 then occupancy_nbr end) as rt7_occupancy_nbr,(case accom_type_id when @rt7 then occupancy_change end) as rt7_occupancy_change
		,(case accom_type_id when @rt8 then occupancy_nbr end) as rt8_occupancy_nbr,(case accom_type_id when @rt8 then occupancy_change end) as rt8_occupancy_change
	
		,(case accom_type_id when @rt1 then occupancy_percent end) as rt1_occupancy_percent,(case accom_type_id when @rt1 then occupancy_perc_change end) as rt1_occupancy_perc_change
		,(case accom_type_id when @rt2 then occupancy_percent end) as rt2_occupancy_percent,(case accom_type_id when @rt2 then occupancy_perc_change end) as rt2_occupancy_perc_change
		,(case accom_type_id when @rt3 then occupancy_percent end) as rt3_occupancy_percent,(case accom_type_id when @rt3 then occupancy_perc_change end) as rt3_occupancy_perc_change
		,(case accom_type_id when @rt4 then occupancy_percent end) as rt4_occupancy_percent,(case accom_type_id when @rt4 then occupancy_perc_change end) as rt4_occupancy_perc_change
		,(case accom_type_id when @rt5 then occupancy_percent end) as rt5_occupancy_percent,(case accom_type_id when @rt5 then occupancy_perc_change end) as rt5_occupancy_perc_change
		,(case accom_type_id when @rt6 then occupancy_percent end) as rt6_occupancy_percent,(case accom_type_id when @rt6 then occupancy_perc_change end) as rt6_occupancy_perc_change
		,(case accom_type_id when @rt7 then occupancy_percent end) as rt7_occupancy_percent,(case accom_type_id when @rt7 then occupancy_perc_change end) as rt7_occupancy_perc_change
		,(case accom_type_id when @rt8 then occupancy_percent end) as rt8_occupancy_percent,(case accom_type_id when @rt8 then occupancy_perc_change end) as rt8_occupancy_perc_change
	
		,(case accom_type_id when @rt1 then occupancy_percent_without_ooo end) as rt1_occupancy_percent_without_ooo,(case accom_type_id when @rt1 then occupancy_perc_change_without_ooo end) as rt1_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt2 then occupancy_percent_without_ooo end) as rt2_occupancy_percent_without_ooo,(case accom_type_id when @rt2 then occupancy_perc_change_without_ooo end) as rt2_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt3 then occupancy_percent_without_ooo end) as rt3_occupancy_percent_without_ooo,(case accom_type_id when @rt3 then occupancy_perc_change_without_ooo end) as rt3_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt4 then occupancy_percent_without_ooo end) as rt4_occupancy_percent_without_ooo,(case accom_type_id when @rt4 then occupancy_perc_change_without_ooo end) as rt4_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt5 then occupancy_percent_without_ooo end) as rt5_occupancy_percent_without_ooo,(case accom_type_id when @rt5 then occupancy_perc_change_without_ooo end) as rt5_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt6 then occupancy_percent_without_ooo end) as rt6_occupancy_percent_without_ooo,(case accom_type_id when @rt6 then occupancy_perc_change_without_ooo end) as rt6_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt7 then occupancy_percent_without_ooo end) as rt7_occupancy_percent_without_ooo,(case accom_type_id when @rt7 then occupancy_perc_change_without_ooo end) as rt7_occupancy_perc_change_without_ooo
		,(case accom_type_id when @rt8 then occupancy_percent_without_ooo end) as rt8_occupancy_percent_without_ooo,(case accom_type_id when @rt8 then occupancy_perc_change_without_ooo end) as rt8_occupancy_perc_change_without_ooo
		
		,(case accom_type_id when @rt1 then booked_revenue end) as rt1_booked_revenue,(case accom_type_id when @rt1 then booked_revenue_change end) as rt1_booked_revenue_change
		,(case accom_type_id when @rt2 then booked_revenue end) as rt2_booked_revenue,(case accom_type_id when @rt2 then booked_revenue_change end) as rt2_booked_revenue_change
		,(case accom_type_id when @rt3 then booked_revenue end) as rt3_booked_revenue,(case accom_type_id when @rt3 then booked_revenue_change end) as rt3_booked_revenue_change
		,(case accom_type_id when @rt4 then booked_revenue end) as rt4_booked_revenue,(case accom_type_id when @rt4 then booked_revenue_change end) as rt4_booked_revenue_change
		,(case accom_type_id when @rt5 then booked_revenue end) as rt5_booked_revenue,(case accom_type_id when @rt5 then booked_revenue_change end) as rt5_booked_revenue_change
		,(case accom_type_id when @rt6 then booked_revenue end) as rt6_booked_revenue,(case accom_type_id when @rt6 then booked_revenue_change end) as rt6_booked_revenue_change
		,(case accom_type_id when @rt7 then booked_revenue end) as rt7_booked_revenue,(case accom_type_id when @rt7 then booked_revenue_change end) as rt7_booked_revenue_change
		,(case accom_type_id when @rt8 then booked_revenue end) as rt8_booked_revenue,(case accom_type_id when @rt8 then booked_revenue_change end) as rt8_booked_revenue_change
	
		,(case accom_type_id when @rt1 then revenue end) as rt1_revenue,(case accom_type_id when @rt1 then revenue_change end) as rt1_revenue_change
		,(case accom_type_id when @rt2 then revenue end) as rt2_revenue,(case accom_type_id when @rt2 then revenue_change end) as rt2_revenue_change
		,(case accom_type_id when @rt3 then revenue end) as rt3_revenue,(case accom_type_id when @rt3 then revenue_change end) as rt3_revenue_change
		,(case accom_type_id when @rt4 then revenue end) as rt4_revenue,(case accom_type_id when @rt4 then revenue_change end) as rt4_revenue_change
		,(case accom_type_id when @rt5 then revenue end) as rt5_revenue,(case accom_type_id when @rt5 then revenue_change end) as rt5_revenue_change
		,(case accom_type_id when @rt6 then revenue end) as rt6_revenue,(case accom_type_id when @rt6 then revenue_change end) as rt6_revenue_change
		,(case accom_type_id when @rt7 then revenue end) as rt7_revenue,(case accom_type_id when @rt7 then revenue_change end) as rt7_revenue_change
		,(case accom_type_id when @rt8 then revenue end) as rt8_revenue,(case accom_type_id when @rt8 then revenue_change end) as rt8_revenue_change
	
		,(case accom_type_id when @rt1 then booked_adr end) as rt1_booked_adr,(case accom_type_id when @rt1 then booked_adr_change end) as rt1_booked_adr_change
		,(case accom_type_id when @rt2 then booked_adr end) as rt2_booked_adr,(case accom_type_id when @rt2 then booked_adr_change end) as rt2_booked_adr_change
		,(case accom_type_id when @rt3 then booked_adr end) as rt3_booked_adr,(case accom_type_id when @rt3 then booked_adr_change end) as rt3_booked_adr_change
		,(case accom_type_id when @rt4 then booked_adr end) as rt4_booked_adr,(case accom_type_id when @rt4 then booked_adr_change end) as rt4_booked_adr_change
		,(case accom_type_id when @rt5 then booked_adr end) as rt5_booked_adr,(case accom_type_id when @rt5 then booked_adr_change end) as rt5_booked_adr_change
		,(case accom_type_id when @rt6 then booked_adr end) as rt6_booked_adr,(case accom_type_id when @rt6 then booked_adr_change end) as rt6_booked_adr_change
		,(case accom_type_id when @rt7 then booked_adr end) as rt7_booked_adr,(case accom_type_id when @rt7 then booked_adr_change end) as rt7_booked_adr_change
		,(case accom_type_id when @rt8 then booked_adr end) as rt8_booked_adr,(case accom_type_id when @rt8 then booked_adr_change end) as rt8_booked_adr_change
	
		,(case accom_type_id when @rt1 then adr end) as rt1_adr,(case accom_type_id when @rt1 then adr_change end) as rt1_adr_change
		,(case accom_type_id when @rt2 then adr end) as rt2_adr,(case accom_type_id when @rt2 then adr_change end) as rt2_adr_change
		,(case accom_type_id when @rt3 then adr end) as rt3_adr,(case accom_type_id when @rt3 then adr_change end) as rt3_adr_change
		,(case accom_type_id when @rt4 then adr end) as rt4_adr,(case accom_type_id when @rt4 then adr_change end) as rt4_adr_change
		,(case accom_type_id when @rt5 then adr end) as rt5_adr,(case accom_type_id when @rt5 then adr_change end) as rt5_adr_change
		,(case accom_type_id when @rt6 then adr end) as rt6_adr,(case accom_type_id when @rt6 then adr_change end) as rt6_adr_change
		,(case accom_type_id when @rt7 then adr end) as rt7_adr,(case accom_type_id when @rt7 then adr_change end) as rt7_adr_change
		,(case accom_type_id when @rt8 then adr end) as rt8_adr,(case accom_type_id when @rt8 then adr_change end) as rt8_adr_change
	
		,(case accom_type_id when @rt1 then booked_revpar end) as rt1_booked_revpar,(case accom_type_id when @rt1 then booked_revpar_change end) as rt1_booked_revpar_change
		,(case accom_type_id when @rt2 then booked_revpar end) as rt2_booked_revpar,(case accom_type_id when @rt2 then booked_revpar_change end) as rt2_booked_revpar_change
		,(case accom_type_id when @rt3 then booked_revpar end) as rt3_booked_revpar,(case accom_type_id when @rt3 then booked_revpar_change end) as rt3_booked_revpar_change
		,(case accom_type_id when @rt4 then booked_revpar end) as rt4_booked_revpar,(case accom_type_id when @rt4 then booked_revpar_change end) as rt4_booked_revpar_change
		,(case accom_type_id when @rt5 then booked_revpar end) as rt5_booked_revpar,(case accom_type_id when @rt5 then booked_revpar_change end) as rt5_booked_revpar_change
		,(case accom_type_id when @rt6 then booked_revpar end) as rt6_booked_revpar,(case accom_type_id when @rt6 then booked_revpar_change end) as rt6_booked_revpar_change
		,(case accom_type_id when @rt7 then booked_revpar end) as rt7_booked_revpar,(case accom_type_id when @rt7 then booked_revpar_change end) as rt7_booked_revpar_change
		,(case accom_type_id when @rt8 then booked_revpar end) as rt8_booked_revpar,(case accom_type_id when @rt8 then booked_revpar_change end) as rt8_booked_revpar_change
	
		,(case accom_type_id when @rt1 then revpar end) as rt1_revpar,(case accom_type_id when @rt1 then revpar_change end) as rt1_revpar_change
		,(case accom_type_id when @rt2 then revpar end) as rt2_revpar,(case accom_type_id when @rt2 then revpar_change end) as rt2_revpar_change
		,(case accom_type_id when @rt3 then revpar end) as rt3_revpar,(case accom_type_id when @rt3 then revpar_change end) as rt3_revpar_change
		,(case accom_type_id when @rt4 then revpar end) as rt4_revpar,(case accom_type_id when @rt4 then revpar_change end) as rt4_revpar_change
		,(case accom_type_id when @rt5 then revpar end) as rt5_revpar,(case accom_type_id when @rt5 then revpar_change end) as rt5_revpar_change
		,(case accom_type_id when @rt6 then revpar end) as rt6_revpar,(case accom_type_id when @rt6 then revpar_change end) as rt6_revpar_change
		,(case accom_type_id when @rt7 then revpar end) as rt7_revpar,(case accom_type_id when @rt7 then revpar_change end) as rt7_revpar_change
		,(case accom_type_id when @rt8 then revpar end) as rt8_revpar,(case accom_type_id when @rt8 then revpar_change end) as rt8_revpar_change

		,(case accom_type_id when @rt1 then overbookingcurrent end) as rt1_overbookingcurrent,(case accom_type_id when @rt1 then overbookingchange end) as rt1_overbookingchange
		,(case accom_type_id when @rt2 then overbookingcurrent end) as rt2_overbookingcurrent,(case accom_type_id when @rt2 then overbookingchange end) as rt2_overbookingchange
		,(case accom_type_id when @rt3 then overbookingcurrent end) as rt3_overbookingcurrent,(case accom_type_id when @rt3 then overbookingchange end) as rt3_overbookingchange
		,(case accom_type_id when @rt4 then overbookingcurrent end) as rt4_overbookingcurrent,(case accom_type_id when @rt4 then overbookingchange end) as rt4_overbookingchange
		,(case accom_type_id when @rt5 then overbookingcurrent end) as rt5_overbookingcurrent,(case accom_type_id when @rt5 then overbookingchange end) as rt5_overbookingchange
		,(case accom_type_id when @rt6 then overbookingcurrent end) as rt6_overbookingcurrent,(case accom_type_id when @rt6 then overbookingchange end) as rt6_overbookingchange
		,(case accom_type_id when @rt7 then overbookingcurrent end) as rt7_overbookingcurrent,(case accom_type_id when @rt7 then overbookingchange end) as rt7_overbookingchange
		,(case accom_type_id when @rt8 then overbookingcurrent end) as rt8_overbookingcurrent,(case accom_type_id when @rt8 then overbookingchange end) as rt8_overbookingchange
		
		--Archana added for group block and group pickup-Start
		,(case accom_type_id when @rt1 then Blocks end) as rt1_Blocks,(case accom_type_id when @rt1 then Blocks_change end) as rt1_Blocks_change,(case accom_type_id when @rt1 then Blocks_Available end) as rt1_Blocks_Available
		,(case accom_type_id when @rt2 then Blocks end) as rt2_Blocks,(case accom_type_id when @rt2 then Blocks_change end) as rt2_Blocks_change,(case accom_type_id when @rt2 then Blocks_Available end) as rt2_Blocks_Available
		,(case accom_type_id when @rt3 then Blocks end) as rt3_Blocks,(case accom_type_id when @rt3 then Blocks_change end) as rt3_Blocks_change,(case accom_type_id when @rt3 then Blocks_Available end) as rt3_Blocks_Available
		,(case accom_type_id when @rt4 then Blocks end) as rt4_Blocks,(case accom_type_id when @rt4 then Blocks_change end) as rt4_Blocks_change,(case accom_type_id when @rt4 then Blocks_Available end) as rt4_Blocks_Available
		,(case accom_type_id when @rt5 then Blocks end) as rt5_Blocks,(case accom_type_id when @rt5 then Blocks_change end) as rt5_Blocks_change,(case accom_type_id when @rt5 then Blocks_Available end) as rt5_Blocks_Available
		,(case accom_type_id when @rt6 then Blocks end) as rt6_Blocks,(case accom_type_id when @rt6 then Blocks_change end) as rt6_Blocks_change,(case accom_type_id when @rt6 then Blocks_Available end) as rt6_Blocks_Available
		,(case accom_type_id when @rt7 then Blocks end) as rt7_Blocks,(case accom_type_id when @rt7 then Blocks_change end) as rt7_Blocks_change,(case accom_type_id when @rt7 then Blocks_Available end) as rt7_Blocks_Available
		,(case accom_type_id when @rt8 then Blocks end) as rt8_Blocks,(case accom_type_id when @rt8 then Blocks_change end) as rt8_Blocks_change,(case accom_type_id when @rt8 then Blocks_Available end) as rt8_Blocks_Available
		--Archana added for group block and group pickup-End

		,(case accom_type_id when @rt1 then barcurrent end) as rt1_barcurrent,(case accom_type_id when @rt1 then barchange end) as rt1_barchange
		,(case accom_type_id when @rt2 then barcurrent end) as rt2_barcurrent,(case accom_type_id when @rt2 then barchange end) as rt2_barchange
		,(case accom_type_id when @rt3 then barcurrent end) as rt3_barcurrent,(case accom_type_id when @rt3 then barchange end) as rt3_barchange
		,(case accom_type_id when @rt4 then barcurrent end) as rt4_barcurrent,(case accom_type_id when @rt4 then barchange end) as rt4_barchange
		,(case accom_type_id when @rt5 then barcurrent end) as rt5_barcurrent,(case accom_type_id when @rt5 then barchange end) as rt5_barchange
		,(case accom_type_id when @rt6 then barcurrent end) as rt6_barcurrent,(case accom_type_id when @rt6 then barchange end) as rt6_barchange
		,(case accom_type_id when @rt7 then barcurrent end) as rt7_barcurrent,(case accom_type_id when @rt7 then barchange end) as rt7_barchange
		,(case accom_type_id when @rt8 then barcurrent end) as rt8_barcurrent,(case accom_type_id when @rt8 then barchange end) as rt8_barchange

		,(case accom_type_id when @rt1 then barchangeLastOptimize end) as rt1_barchangeLastOptimize
		,(case accom_type_id when @rt2 then barchangeLastOptimize end) as rt2_barchangeLastOptimize
		,(case accom_type_id when @rt3 then barchangeLastOptimize end) as rt3_barchangeLastOptimize
		,(case accom_type_id when @rt4 then barchangeLastOptimize end) as rt4_barchangeLastOptimize
		,(case accom_type_id when @rt5 then barchangeLastOptimize end) as rt5_barchangeLastOptimize
		,(case accom_type_id when @rt6 then barchangeLastOptimize end) as rt6_barchangeLastOptimize
		,(case accom_type_id when @rt7 then barchangeLastOptimize end) as rt7_barchangeLastOptimize
		,(case accom_type_id when @rt8 then barchangeLastOptimize end) as rt8_barchangeLastOptimize

		,(case accom_type_id when @rt1 then decisionreasontypecurrent end) as rt1_decisionreasontypecurrent,(case accom_type_id when @rt1 then decisionreasontypechange end) as rt1_decisionreasontypechange
		,(case accom_type_id when @rt2 then decisionreasontypecurrent end) as rt2_decisionreasontypecurrent,(case accom_type_id when @rt2 then decisionreasontypechange end) as rt2_decisionreasontypechange
		,(case accom_type_id when @rt3 then decisionreasontypecurrent end) as rt3_decisionreasontypecurrent,(case accom_type_id when @rt3 then decisionreasontypechange end) as rt3_decisionreasontypechange
		,(case accom_type_id when @rt4 then decisionreasontypecurrent end) as rt4_decisionreasontypecurrent,(case accom_type_id when @rt4 then decisionreasontypechange end) as rt4_decisionreasontypechange
		,(case accom_type_id when @rt5 then decisionreasontypecurrent end) as rt5_decisionreasontypecurrent,(case accom_type_id when @rt5 then decisionreasontypechange end) as rt5_decisionreasontypechange
		,(case accom_type_id when @rt6 then decisionreasontypecurrent end) as rt6_decisionreasontypecurrent,(case accom_type_id when @rt6 then decisionreasontypechange end) as rt6_decisionreasontypechange
		,(case accom_type_id when @rt7 then decisionreasontypecurrent end) as rt7_decisionreasontypecurrent,(case accom_type_id when @rt7 then decisionreasontypechange end) as rt7_decisionreasontypechange
		,(case accom_type_id when @rt8 then decisionreasontypecurrent end) as rt8_decisionreasontypecurrent,(case accom_type_id when @rt8 then decisionreasontypechange end) as rt8_decisionreasontypechange
		
		from 
		(
			select
				base.occupancy_dt,
				datename(dw,base.occupancy_dt) as dow,
				base.property_id,
				base.accom_type_id,
				isnull(a.rooms_sold,0)rooms_sold,
				isnull(a.outoforder,0.0)outoforder,
				f.special_event_name  as special_event,

                case when base.occupancy_dt <= @business_dt then
                    cast((isnull(a.rooms_sold,0.0) - isnull(e.rooms_sold,isnull(a.rooms_sold,0.0))) as numeric(19,2))
				else
				    cast((isnull(a.rooms_sold,0.0) - isnull(e.rooms_sold,0.0)) as numeric(19,2))
                end as rooms_solds_change,

				isnull(b.occupancyforecast,0.0) as occupancy_nbr,
				isnull(
					(
					case (a.capacity-a.outoforder)
						when 0 then 0
					else
						(b.occupancyforecast / (a.capacity-a.outoforder)) *100
					end
					),0.0)occupancy_percent,
				isnull(
					(case a.capacity
						when 0 then 0
					else
						(b.occupancyforecast / a.capacity) *100
					end),0.0)occupancy_percent_without_ooo,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.occupancy_nbr is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    isnull(b.occupancyforecast,0.0) - isnull(g.occupancy_nbr,isnull(b.occupancyforecast,0.0))
                else
                    isnull(b.occupancyforecast,0.0) - isnull(g.occupancy_nbr,0.0)
                end as occupancy_change,

			    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.occupancy_nbr is NULL then 0
			        when base.occupancy_dt <= @business_dt then
                    isnull((case (a.capacity-a.outoforder)
                                when 0 then 0
                                else
                                        (b.occupancyforecast / (a.capacity-a.outoforder)) *100
                        end),(case (a.capacity-a.outoforder)
                                  when 0 then 0
                                  else
                                          (b.occupancyforecast / (a.capacity-a.outoforder)) *100
                        end))
                        - isnull((case (e.accom_capacity_businessenddate-e.OOO_BusinessendDate)
                                      when 0 then 0
                                      else
                                              (g.occupancy_nbr/(e.accom_capacity_businessenddate-e.OOO_BusinessendDate)) *100
                        end),(case (a.capacity-a.outoforder)
                                  when 0 then 0
                                  else
                                          (b.occupancyforecast / (a.capacity-a.outoforder)) *100
                        end))
			    else
                    isnull((case (a.capacity-a.outoforder)
                            when 0 then 0
                        else
                            (b.occupancyforecast / (a.capacity-a.outoforder)) *100
                        end),(case (a.capacity-a.outoforder)
                            when 0 then 0
                        else
                            (b.occupancyforecast / (a.capacity-a.outoforder)) *100
                        end))
                    - isnull((case (e.accom_capacity_businessenddate-e.OOO_BusinessendDate)
                            when 0 then 0
                        else
                            (isnull(g.occupancy_nbr,0.0)/(e.accom_capacity_businessenddate-e.OOO_BusinessendDate)) *100
                        end),0.0)
                end as occupancy_perc_change,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.occupancy_nbr is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    isnull((case (a.capacity)
                            when 0 then 0
                            else
                                    (b.occupancyforecast / a.capacity) *100
                    end),(case a.capacity
                              when 0 then 0
                              else
                                      (b.occupancyforecast / a.capacity) *100
                    end))
                    - isnull((case (e.accom_capacity_businessenddate)
                                  when 0 then 0
                                  else
                                          (g.occupancy_nbr/e.accom_capacity_businessenddate) *100
                    end),(case a.capacity
                              when 0 then 0
                              else
                                      (b.occupancyforecast / a.capacity) *100
                    end))
                else
                    isnull((case (a.capacity)
                            when 0 then 0
                        else
                            (b.occupancyforecast / a.capacity) *100
                        end),(case a.capacity
                            when 0 then 0
                        else
                            (b.occupancyforecast / a.capacity) *100
                        end))
                    - isnull((case (e.accom_capacity_businessenddate)
                            when 0 then 0
                        else
                            (isnull(g.occupancy_nbr,0.0)/e.accom_capacity_businessenddate) *100
                        end),0.0)
				end as occupancy_perc_change_without_ooo,
				isnull(b.revenue,0.0)revenue,
				isnull(b.adr,0.0)adr,
				isnull(((select revpar from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity))),0.0)revpar,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.revenue is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                     (select revpar from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity))
                     - isnull(
                             (
                                 (select revpar from dbo.ufn_calculate_revpar(g.revenue, e.accom_capacity_businessenddate, e.ooo_businessenddate, @use_physical_capacity))
                             ),
                             (
                                 (select revpar from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity))
                             )
                         )
                else
                    (select revpar from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity))
                    - isnull(
                    (
                        (select revpar from dbo.ufn_calculate_revpar(isnull(g.revenue,0.0), e.accom_capacity_businessenddate, e.ooo_businessenddate, @use_physical_capacity))
                        ),
                        0.0
                    )
				end as revpar_change,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.revenue is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    cast((isnull(b.revenue,0.0) - isnull(g.revenue,isnull(b.revenue,0.0))) as numeric(19,2))
                else
				    cast((isnull(b.revenue,0.0) - isnull(g.revenue,0.0)) as numeric(19,2))
                end as revenue_change,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and g.adr is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    cast((isnull(b.adr,0.0) - isnull(g.adr,isnull(b.adr,0.0))) as numeric(19,2))
                else
			        cast((isnull(b.adr,0.0) - isnull(g.adr,0.0)) as numeric(19,2))
                end as adr_change,

			    isnull(a.booked_room_revenue,0.0) booked_revenue,
				isnull(a.booked_adr,0.0)booked_adr,
				isnull(a.booked_revpar ,0.0)booked_revpar,

			    case when base.occupancy_dt <= @business_dt then
                    cast((isnull(a.booked_adr,0.0) - isnull(e.adr,isnull(a.booked_adr,0.0))) as numeric(19,2))
                else
			        cast((isnull(a.booked_adr,0.0) - isnull(e.adr,0.0)) as numeric(19,2))
			    end as booked_adr_change,

                case when base.occupancy_dt <= @business_dt then
                    cast((isnull(a.booked_revpar,0.0) - isnull(e.revpar,isnull(a.booked_revpar,0.0))) as numeric(19,2))
				else
				    cast((isnull(a.booked_revpar,0.0) - isnull(e.revpar,0.0)) as numeric(19,2))
				end as booked_revpar_change,

                case when base.occupancy_dt <= @business_dt then
                    cast((isnull(a.booked_room_revenue,0.0) - isnull(e.room_revenue,isnull(a.booked_room_revenue,0.0))) as numeric(19,2))
				else
				    cast((isnull(a.booked_room_revenue,0.0) - isnull(e.room_revenue,0.0)) as numeric(19,2))
				end as booked_revenue_change,

			    isnull(h.overbooking,0.0) as overbookingcurrent,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and i.overbooking is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    cast((isnull(h.overbooking,0.0) - isnull(i.overbooking,isnull(h.overbooking,0.0))) as numeric(19,2))
                else
                    cast((isnull(h.overbooking,0.0) - isnull(i.overbooking,0.0)) as numeric(19,2))
                end as overbookingchange,

				isnull(groupBlock.Blocks,0.0) as Blocks,
				isnull(groupBlock.Blocks_Pickup,0.0) as Blocks_change,
				isnull(groupBlock.Blocks_Available,0.0)as Blocks_Available,
				isnull(bar.Final_BAR,0.0) as barcurrent,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and barForBusinessDate.Final_BAR is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    cast((isnull(bar.Final_BAR,0.0) - isnull(barForBusinessDate.Final_BAR,isnull(bar.Final_BAR,0.0))) as numeric(19,2))
                else
				    cast((isnull(bar.Final_BAR,0.0) - isnull(barForBusinessDate.Final_BAR,0.0)) as numeric(19,2))
                end as barchange,

                case when @rolling_business_dt = 'LAST_OPTIMIZATION' and barForLastOptimization.Final_BAR is NULL then 0
                    when base.occupancy_dt <= @business_dt then
                    cast((isnull(bar.Final_BAR,0.0) - isnull(barForLastOptimization.Final_BAR,isnull(bar.Final_BAR,0.0))) as numeric(19,2))
                else
                    cast((isnull(bar.Final_BAR,0.0) - isnull(barForLastOptimization.Final_BAR,0.0)) as numeric(19,2))
                end as barchangeLastOptimize,
				isNull(highest_bar_current.Decision_Reason_Type,'-') as decisionreasontypecurrent,
				(select dbo.ufn_determine_change_by_current_and_past_value (highest_bar_current.Decision_Reason_Type, highest_bar_change.Decision_Reason_Type)) as decisionreasontypechange
			from
			(
				select @property_id property_id,CAST(calendar_date as date) Occupancy_DT,accom_type_id  
					from calendar_dim left join @tempRT on accom_type_id is not null
				 where calendar_date between @start_date and @end_date
			)base left join	
			(
				select * from ufn_get_activity_by_individual_rt (@property_id,@roomtype_id,@start_date, @end_date, @use_physical_capacity)
			) as a on base.property_id=a.property_id and base.Occupancy_DT=a.occupancy_dt and base.accom_type_id=a.accom_type_id 
			left join
			(
				select 
					occupancy_dt,
					property_id,
					accom_type_id,
					at_occupancy_forecast as occupancyforecast, 
					at_revenue as revenue,
					at_adr as adr
				from dbo.ufn_get_occupancy_forecast_by_individual_rt (@property_id,@roomtype_id,3,13,@start_date, @end_date)
			) as b on base.occupancy_dt=b.occupancy_dt and base.property_id=b.property_id and base.accom_type_id=b.accom_type_id
			left join
			(
				select 
					occupancy_dt,
					property_id,
					accom_type_id,
					rooms_sold,
					accom_capacity_businessstartdate as accom_capacity_businessenddate,
					ooo_businessstartdate as ooo_businessenddate,
					room_revenue,
					adr,
					revpar
				from ufn_get_activity_asof_lastoptimization_or_businessdate_by_individual_rt (@property_id,@roomtype_id,@business_dt,@start_date, @end_date, @use_physical_capacity, @rolling_business_dt)
			) as e on base.property_id=e.property_id and base.occupancy_dt=e.occupancy_dt and base.accom_type_id=e.accom_type_id
			left join
			(
				select * from ufn_get_special_event_by_property (@property_id,@start_date, @end_date)
			) as f on base.property_id=f.property_id and base.occupancy_dt = f.event_cal_date
			left join
			(
				select 
					occupancy_dt,
					property_id,
					accom_type_id,
					occupancy_nbr_businessstartdate as occupancy_nbr,
					revenue_businessstartdate as revenue,
					adr
				from dbo.ufn_get_occupancy_forecast_asof_lastoptimization_or_businessdate_by_individual_rt (@property_id,@roomtype_id,@business_dt,@start_date, @end_date,@rolling_business_dt)
			) as g on base.property_id=g.property_id and base.occupancy_dt=g.occupancy_dt and base.accom_type_id=g.accom_type_id
			left join
			(
				select * from ufn_get_ovrbk_decision_by_individual_rt (@property_id,@roomtype_id,@start_date, @end_date)
			) as h on base.property_id=h.property_id and base.occupancy_dt=h.occupancy_dt and base.accom_type_id=h.accom_type_id
			left join
			(
				select * from ufn_get_ovrbk_decision_asof_lastOptimization_or_businessdate_by_individual_rt (@property_id,@roomtype_id,@business_dt,@start_date, @end_date, @rolling_business_dt)
			) as i on base.property_id=i.property_id and base.occupancy_dt=i.occupancy_dt and base.accom_type_id=i.accom_type_id
			--Archana added group block-START
			left join
			(
				select * from ufn_get_groupBlock_groupPickup_by_RoomType (@property_id,@roomtype_id,@start_date, @end_date,0)
			) as groupBlock on base.property_id=groupBlock.property_id and base.Occupancy_DT=groupBlock.occupancy_dt and base.accom_type_id=groupBlock.Accom_type_Id
			--Archana added group block-END
			left join
			(
				select cdbo.Property_ID, cdbo.Arrival_DT, cdbo.Accom_Type_ID, cdbo.Final_BAR from dbo.CP_Decision_Bar_Output cdbo
				where cdbo.Property_ID = @property_id and cdbo.Arrival_DT between @start_date and @end_date and cdbo.Product_ID = 1
				and cdbo.Accom_Type_ID in (SELECT Value FROM varcharToInt(@roomtype_id,','))
			) as bar on base.property_id = bar.property_id and base.Occupancy_DT = bar.Arrival_DT and base.accom_type_id = bar.Accom_Type_ID
			left join
			(
				select pcdbo.Property_ID, pcdbo.Arrival_DT, pcdbo.Accom_Type_ID, pcdbo.Final_BAR from dbo.CP_Pace_Decision_Bar_Output pcdbo INNER JOIN Decision d ON pcdbo.Decision_ID = d.Decision_ID
				where pcdbo.Property_ID = @property_id and pcdbo.Product_ID = 1 and pcdbo.Arrival_DT between @start_date and @end_date and pcdbo.Product_ID = 1
				and pcdbo.Accom_Type_ID in (SELECT Value FROM varcharToInt(@roomtype_id,',')) and d.Business_DT = @business_dt and d.Decision_Type_ID = 1
			) as barForBusinessDate on base.property_id = barForBusinessDate.property_id and base.Occupancy_DT = barForBusinessDate.Arrival_DT and base.accom_type_id = barForBusinessDate.Accom_Type_ID
			left join
			(
				select pcdbo.Property_ID, pcdbo.Arrival_DT, pcdbo.Accom_Type_ID, pcdbo.Final_BAR from dbo.CP_Pace_Decision_Bar_Output pcdbo INNER JOIN Decision d ON pcdbo.Decision_ID = d.Decision_ID
				where pcdbo.Property_ID = @property_id 
				and pcdbo.Product_ID = 1 
				and pcdbo.Arrival_DT between @start_date and @end_date 
				--and pcdbo.Product_ID = 1
				and pcdbo.Accom_Type_ID in (SELECT Value FROM varcharToInt(@roomtype_id,',')) 
				and d.Decision_ID = (SELECT TOP 1 x.Decision_ID From (select Top 2 * from Decision d where d.Decision_Type_ID IN (1,2) ORDER BY Decision_ID DESC) x ORDER BY Decision_ID DESC)
			) as barForLastOptimization on base.property_id = barForLastOptimization.property_id and base.Occupancy_DT = barForLastOptimization.Arrival_DT and base.accom_type_id = barForLastOptimization.Accom_Type_ID
			left join
			(
				select cdbo.Property_id, cdbo.Arrival_dt, cdbo.Accom_Type_ID, (case cdbo.Decision_Reason_Type_ID when 2 then 'Y' else 'N' end) as Decision_Reason_Type
				from dbo.CP_Decision_Bar_Output cdbo where cdbo.Property_ID = @property_id and cdbo.los = -1 and cdbo.Accom_Type_ID in (SELECT Value FROM varcharToInt(@roomtype_id,','))
				and cdbo.Arrival_DT between @start_date and @end_date and cdbo.Product_ID = 1
			)as highest_bar_current on base.property_id=highest_bar_current.Property_ID and base.occupancy_dt=highest_bar_current.Arrival_DT and base.accom_type_id = highest_bar_current.Accom_Type_ID
			left join
			(
				select pbo.Property_id, Arrival_dt, Accom_Type_ID, (case Decision_Reason_Type_id when 2 then 'Y' else 'N' end) as Decision_Reason_Type
				from dbo.CP_Pace_Decision_Bar_Output pbo inner join Decision de on pbo.Decision_ID=de.Decision_ID and de.Business_DT = @business_dt and Decision_Type_ID = 1
				where pbo.Property_ID = @property_id and pbo.los = -1 and pbo.Accom_Type_ID in (SELECT Value FROM varcharToInt(@roomtype_id,','))
				and pbo.Arrival_DT between @start_date and @end_date and pbo.Product_ID = 1
			)as highest_bar_change on base.property_id=highest_bar_change.Property_ID and base.occupancy_dt=highest_bar_change.Arrival_DT and base.accom_type_id = highest_bar_change.Accom_Type_ID
		)data
		)data2
	group by occupancy_dt,dow,special_event
	order by occupancy_dt
	
	return
end
