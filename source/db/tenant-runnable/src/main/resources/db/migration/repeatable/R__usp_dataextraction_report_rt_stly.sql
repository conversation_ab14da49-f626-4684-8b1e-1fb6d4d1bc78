if exists (select * from sys.objects where object_id = object_id(N'[dbo].[usp_dataextraction_report_rt_stly]'))
drop procedure [dbo].[usp_dataextraction_report_rt_stly]

GO

/****** Object:  StoredProcedure [dbo].[usp_dataextraction_report_rt_stly]    Script Date: 7/27/2020 12:53:48 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[usp_dataextraction_report_rt_stly]
(
    @property_id int,
	@ly_start_date date,
	@ly_end_date date,
	@ly_businessdate date,
	@includeZeroCapacityRT int,
	@includePseudoRT int,
	@is_stly_or_2y int
      
)         
as
begin

		select
            (case when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT) else DateAdd(WEEK, 104, pace.Occupancy_DT) end) as Occupancy_DT,
			Accom_Type_Name,
			accom.Accom_Class_Name,
			rooms_sold,
			room_revenue
		from PACE_Accom_Activity pace
			join Accom_Type acct 
				on acct.Accom_Type_ID = pace.Accom_Type_ID	
				and acct.property_Id = pace.property_Id
				and acct.status_id in (select 1 union select case when @includePseudoRT = 1 then 6 else 1 end )
				and acct.display_status_id in (select 1 union 
												select case when @includeZeroCapacityRT = 1 then 2 else 1 end union
												select case when @includePseudoRT = 1 then 4 else 1 end)
			join Accom_Class accom
				on accom.property_Id = acct.property_Id
				and accom.Accom_Class_ID = acct.Accom_Class_ID
				and accom.status_id = 1 
		where
			  pace.Occupancy_DT between @ly_start_date and @ly_end_date 
			  and pace.Property_ID=@property_id
			  and pace.Business_Day_End_DT = @ly_businessdate
		order by Occupancy_DT, Accom_Type_Name		
                  
end
GO
