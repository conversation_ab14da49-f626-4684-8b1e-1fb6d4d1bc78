DROP VIEW IF EXISTS [dbo].[vw_Pace_Webrate_Differ_v2_full]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[vw_Pace_Webrate_Differ_v2_full]
AS

SELECT
    pwr.PACE_Webrate_ID,
    pwr.Webrate_GenerationDate,
    pwr.Occupancy_DT,
    pwr.First_Webrate_GenerationDate,
    case when pwr.Webrate_Status = 'A' then 1 else 2 end as Webrate_Status, --UPDATE TO 1, 2
    wcc.Product_ID,
    pwr.Webrate_Competitors_ID,
    pwr.Webrate_Channel_ID,
    pwr.Webrate_Accom_Type_ID,
    pwr.LOS,
    pwr.Webrate_RateValue,
    pwr.Webrate_count,
    ISNULL(wtp.Product_ID, -1) AS Rate_Product_ID,
    pwr.Webrate_Source_Property_ID,
    pwr.Webrate_Type_ID,
    wacm.Accom_Class_ID
FROM [dbo].[PACE_Webrate_Differential] AS pwr WITH (NOLOCK)
    JOIN [dbo].Webrate_Accom_Class_Mapping AS wacm WITH (NOLOCK)
ON pwr.Webrate_Accom_Type_ID = wacm.Webrate_Accom_Type_ID
    JOIN [dbo].[Webrate_Competitors_Class] AS wcc WITH (NOLOCK)
ON pwr.Webrate_Competitors_ID = wcc.Webrate_Competitors_ID
    And wcc.Accom_Class_ID = wacm.Accom_Class_ID
    JOIN [dbo].[Product] as p WITH (NOLOCK)
ON wcc.Product_ID = p.Product_ID
    LEFT JOIN Webrate_Type_Product wtp WITH (NOLOCK)
ON wtp.Webrate_Type_ID = pwr.Webrate_Type_ID
    AND wtp.LOS = pwr.LOS
WHERE pwr.Webrate_Status in ('A', 'C')
  and p.Status_ID = 1
  and wcc.Product_ID = wtp.Product_ID
  and ((p.Rate_Shopping_LOS_Min = -1  and p.Rate_Shopping_LOS_Max = -1)
   or (p.Rate_Shopping_LOS_Min > -1  and pwr.LOS >= p.Rate_Shopping_LOS_Min
  and p.Rate_Shopping_LOS_Max > -1  and pwr.LOS <= p.Rate_Shopping_LOS_Max))
GO