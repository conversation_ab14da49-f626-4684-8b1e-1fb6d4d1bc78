drop procedure if exists [dbo].[usp_insert_new_reservation_change_records]
GO
CREATE PROCEDURE [dbo].[usp_insert_new_reservation_change_records]
(
    @file_metadata_id INT
)
AS
BEGIN
	select DISTINCT Persistent_Key into #keys FROM Reservation_Night_Change;
	insert into Reservation_Night_Change (
        Persistent_Key, Reservation_Identifier, Occupancy_DT, File_Metadata_ID, Individual_Status, Arrival_DT, Departure_DT,
        Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID,
        Room_Revenue, Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue,
        Total_Revenue, Source_Booking, Nationality, Rate_Code, Rate_Value, Room_Number,
        Booking_type, Number_Children, Number_Adults, Confirmation_No, Channel, Booking_TM,
        Change_Type, Change_DTTM, Inv_Block_Code, Market_Code)
	select
        Persistent_Key, Reservation_Identifier, Occupancy_DT, File_MetaData_ID, Individual_Status, Arrival_DT, Departure_DT,
        Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID,
        Room_Revenue, Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue,
        Total_Revenue, Source_Booking, Nationality, Rate_Code, Rate_Value, Room_Number,
        Booking_type, Number_Children, Number_Adults, Confirmation_No, Channel, Booking_TM,
        1, getdate(), Inv_Block_Code, Market_Code
    from Reservation_Night where File_Metadata_ID = @file_metadata_id and
        Persistent_Key not in (select Persistent_Key from #keys);
END
GO

DROP PROCEDURE IF EXISTS [dbo].[usp_insert_change_reservation_for_deleted_reservations]
GO
CREATE PROCEDURE [dbo].[usp_insert_change_reservation_for_deleted_reservations]
	@file_metadata_id int,
	@window_start_date date,
	@window_end_date date
AS
BEGIN
	select Persistent_Key into #keys from Reservation_Night_Change where Change_Type = 3;
	insert into #keys select Persistent_Key From Reservation_Night where file_metadata_id = @file_metadata_id;
	insert into
		Reservation_Night_Change(Reservation_Identifier, Occupancy_DT, Persistent_Key, File_Metadata_ID, Change_Type, Change_DTTM)
    select
        distinct(Reservation_Identifier), Occupancy_DT, Reservation_Identifier+ ',' +convert(nvarchar, Occupancy_DT, 23), @file_metadata_id, 3, getdate()
    from
		Reservation_Night_Change rnc
    where
         Arrival_DT >= @window_start_date and Arrival_DT <= @window_end_date and
         Persistent_Key not in (select Persistent_Key from #keys);
END
GO

DROP PROCEDURE IF EXISTS [dbo].[usp_insert_change_reservation_for_deleted_reservations_New]
GO
CREATE PROCEDURE [dbo].[usp_insert_change_reservation_for_deleted_reservations_New]
	@file_metadata_id int,
	@window_start_date date,
	@window_end_date date
AS
BEGIN

	select Persistent_Key into #keys from Reservation_Night_Change where Change_Type = 3;  
	insert into #keys select rn.Persistent_Key From Reservation_Night rn left join #keys k on k.Persistent_Key = rn.Persistent_Key  
	where rn.file_metadata_id = @file_metadata_id and k.Persistent_Key is null

	insert into
		Reservation_Night_Change(Reservation_Identifier, Occupancy_DT, Persistent_Key, File_Metadata_ID, Change_Type, Change_DTTM)
    select   
        distinct(Reservation_Identifier)  Reservation_Identifier  
		, Occupancy_DT
		, Reservation_Identifier+ ',' +convert(nvarchar, Occupancy_DT, 23) Persistent_Key
		, @file_metadata_id file_metadata_id
		, 3 Change_Type
		, getdate() Change_DTTM
    from
		Reservation_Night_Change rnc
		left join #keys k on k.Persistent_Key = rnc.Persistent_Key 
    where
         rnc.Arrival_DT >= @window_start_date and rnc.Arrival_DT <= @window_end_date 
		 and k.Persistent_Key is null

END
GO

DROP PROCEDURE IF EXISTS [dbo].[usp_insert_change_reservation_for_deleted_reservations_diff]
    go
CREATE PROCEDURE [dbo].[Usp_insert_change_reservation_for_deleted_reservations_diff]
  @file_metadata_id  INT,
  @property_id       INT,
  @window_start_date DATE,
  @window_end_date   DATE
AS
BEGIN
    SELECT persistent_key INTO #keys
    FROM
        (SELECT persistent_key
         FROM reservation_night
         WHERE property_id = @property_id
           AND arrival_dt >= @window_start_date
         UNION SELECT persistent_key
         FROM reservation_night_change
         WHERE change_type = 3 ) A ;


    INSERT INTO reservation_night_change (reservation_identifier, occupancy_dt, persistent_key, file_metadata_id, change_type, change_dttm)
    SELECT DISTINCT(reservation_identifier),
                   occupancy_dt,
                   reservation_identifier+','+CONVERT(NVARCHAR, occupancy_dt, 23),
                   @file_metadata_id,
                   3,
                   Getdate()
    FROM reservation_night_change rnc
             LEFT JOIN #keys k ON rnc.persistent_key = k.persistent_key
    WHERE arrival_dt BETWEEN @window_start_date AND @window_end_date
      AND k.persistent_key IS NULL
END
go


DROP PROCEDURE IF EXISTS [dbo].[usp_insert_change_reservation_records_for_updated_reservations]
GO
CREATE PROCEDURE [dbo].[usp_insert_change_reservation_records_for_updated_reservations]
	@file_metadata_id int
AS
BEGIN
    insert into Reservation_Night_Change (
         Persistent_Key, Reservation_Identifier, Occupancy_DT, File_Metadata_ID,Individual_Status,Arrival_DT, Departure_DT,
         Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID,
         Room_Revenue, Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue,
         Total_Revenue,Source_Booking,Nationality,Rate_Code,Rate_Value,Room_Number,
         Booking_type,Number_Children,Number_Adults,Confirmation_No,Channel,Booking_TM,
         Change_Type,Change_DTTM, Inv_Block_Code, Market_Code)
    select
         new.Reservation_Identifier + ',' + convert(nvarchar, new.Occupancy_DT, 23), new.Reservation_Identifier,
         new.Occupancy_DT,new.File_MetaData_ID,new.Individual_Status,new.Arrival_DT,new.Departure_DT,
         new.Booking_DT,new.Cancellation_DT,new.Booked_Accom_Type_Code,new.Accom_Type_ID,new.Mkt_Seg_ID,
         new.Room_Revenue,new.Food_Revenue,new.Beverage_Revenue,new.Telecom_Revenue,new.Other_Revenue,
         new.Total_Revenue,new.Source_Booking,new.Nationality,new.Rate_Code,new.Rate_Value,new.Room_Number,
         new.Booking_type,new.Number_Children,new.Number_Adults,new.Confirmation_No,new.Channel,new.Booking_TM,
         2, getdate(), new.Inv_Block_Code, new.Market_Code
    from Reservation_Night new
    inner join Reservation_Night_Change old
    on new.Persistent_Key = old.Persistent_Key
    where
        new.File_Metadata_ID = @file_metadata_id
        and old.Change_DTTM = (select max(Change_DTTM) from  Reservation_Night_Change where
        Persistent_Key = new.Persistent_Key)
        and not
        (
			new.Individual_Status = coalesce(old.Individual_Status,'') and
            new.Arrival_Dt = coalesce(old.Arrival_DT,'1800-01-01') and
            new.Departure_DT = coalesce(old.Departure_DT,'1800-01-01') and
            new.Booking_DT = coalesce(old.Booking_DT,'1800-01-01') and
            coalesce(new.Cancellation_DT,'1800-01-01') = coalesce(old.Cancellation_DT,'1800-01-01') and
            coalesce(new.Booked_Accom_Type_Code,'') = coalesce(old.Booked_Accom_Type_Code,'') and
            new.Accom_Type_Id = coalesce(old.Accom_Type_Id,0) and
            new.Mkt_Seg_ID = coalesce(old.Mkt_Seg_ID,0) and
            new.Room_Revenue = coalesce(old.Room_Revenue,0) and
            coalesce(new.Food_Revenue,0) = coalesce(old.Food_Revenue,0) and
            coalesce(new.Beverage_Revenue,0) = coalesce(old.Beverage_Revenue,0) and
            coalesce(new.Telecom_Revenue,0) = coalesce(old.Telecom_Revenue,0) and
            coalesce(new.Other_Revenue,0) = coalesce(old.Other_Revenue,0) and
            new.Total_Revenue = coalesce(old.Total_Revenue,0) and
            coalesce(new.Source_Booking,'') = coalesce(old.Source_Booking,'') and
            coalesce(new.Nationality,'') = coalesce(old.Nationality,'') and
            coalesce(new.Rate_Code,'') = coalesce(old.Rate_Code,'') and
            coalesce(new.Rate_Value,0) = coalesce(old.Rate_Value,0) and
            coalesce(new.Room_Number,'') = coalesce(old.Room_Number,'') and
            coalesce(new.Booking_type,'') = coalesce(old.Booking_type,'') and
            coalesce(new.Number_Children,0) = coalesce(old.Number_Children,0) and
            coalesce(new.Number_Adults,0) = coalesce(old.Number_Adults,0) and
            coalesce(new.Confirmation_No,'') = coalesce(old.Confirmation_No,'') and
            coalesce(new.Channel,'') = coalesce(old.Channel,'') and
            coalesce(new.Inv_Block_Code,'') = coalesce(old.Inv_Block_Code,'') and
            coalesce(new.Market_Code,'') = coalesce(old.Market_Code,'')
        )
END
GO

DROP PROCEDURE IF EXISTS [dbo].[usp_insert_change_reservation_records_for_updated_reservations_new]
GO

CREATE PROCEDURE [dbo].[usp_insert_change_reservation_records_for_updated_reservations_new]
	@file_metadata_id int
AS
BEGIN

	drop table if exists #rnc_keys

	select Persistent_Key, Reservation_Night_Change_ID into #rnc_keys
	from (select old.Persistent_Key, old.Reservation_Night_Change_ID,
	Row_Number() over (partition by old.Persistent_Key order by old.Change_DTTM desc) RowNbr
	from Reservation_Night_Change old join Reservation_Night new
		on new.Persistent_Key = old.Persistent_Key
		where new.file_metadata_id = @file_metadata_id
		) a
	where RowNbr = 1


    insert into Reservation_Night_Change (
         Persistent_Key, Reservation_Identifier, Occupancy_DT, File_Metadata_ID,Individual_Status,Arrival_DT, Departure_DT,
         Booking_DT, Cancellation_DT, Booked_Accom_Type_Code, Accom_Type_ID, Mkt_Seg_ID,
         Room_Revenue, Food_Revenue, Beverage_Revenue, Telecom_Revenue, Other_Revenue,
         Total_Revenue,Source_Booking,Nationality,Rate_Code,Rate_Value,Room_Number,
         Booking_type,Number_Children,Number_Adults,Confirmation_No,Channel,Booking_TM,
         Change_Type,Change_DTTM, Inv_Block_Code, Market_Code)
    select
         new.Reservation_Identifier + ',' + convert(nvarchar, new.Occupancy_DT, 23), new.Reservation_Identifier,
         new.Occupancy_DT,new.File_MetaData_ID,new.Individual_Status,new.Arrival_DT,new.Departure_DT,
         new.Booking_DT,new.Cancellation_DT,new.Booked_Accom_Type_Code,new.Accom_Type_ID,new.Mkt_Seg_ID,
         new.Room_Revenue,new.Food_Revenue,new.Beverage_Revenue,new.Telecom_Revenue,new.Other_Revenue,
         new.Total_Revenue,new.Source_Booking,new.Nationality,new.Rate_Code,new.Rate_Value,new.Room_Number,
         new.Booking_type,new.Number_Children,new.Number_Adults,new.Confirmation_No,new.Channel,new.Booking_TM,
         2, getdate(), new.Inv_Block_Code, new.Market_Code
    from #rnc_keys keys
	inner join Reservation_Night new on new.Persistent_Key = keys.Persistent_Key
    inner join Reservation_Night_Change old on old.Persistent_Key = keys.Persistent_Key and old.Reservation_Night_Change_ID = keys.Reservation_Night_Change_ID
    where not
        (
			new.Individual_Status = coalesce(old.Individual_Status,'') and
            new.Arrival_Dt = coalesce(old.Arrival_DT,'1800-01-01') and
            new.Departure_DT = coalesce(old.Departure_DT,'1800-01-01') and
            new.Booking_DT = coalesce(old.Booking_DT,'1800-01-01') and
            coalesce(new.Cancellation_DT,'1800-01-01') = coalesce(old.Cancellation_DT,'1800-01-01') and
            coalesce(new.Booked_Accom_Type_Code,'') = coalesce(old.Booked_Accom_Type_Code,'') and
            new.Accom_Type_Id = coalesce(old.Accom_Type_Id,0) and
            new.Mkt_Seg_ID = coalesce(old.Mkt_Seg_ID,0) and
            new.Room_Revenue = coalesce(old.Room_Revenue,0) and
            coalesce(new.Food_Revenue,0) = coalesce(old.Food_Revenue,0) and
            coalesce(new.Beverage_Revenue,0) = coalesce(old.Beverage_Revenue,0) and
            coalesce(new.Telecom_Revenue,0) = coalesce(old.Telecom_Revenue,0) and
            coalesce(new.Other_Revenue,0) = coalesce(old.Other_Revenue,0) and
            new.Total_Revenue = coalesce(old.Total_Revenue,0) and
            coalesce(new.Source_Booking,'') = coalesce(old.Source_Booking,'') and
            coalesce(new.Nationality,'') = coalesce(old.Nationality,'') and
            coalesce(new.Rate_Code,'') = coalesce(old.Rate_Code,'') and
            coalesce(new.Rate_Value,0) = coalesce(old.Rate_Value,0) and
            coalesce(new.Room_Number,'') = coalesce(old.Room_Number,'') and
            coalesce(new.Booking_type,'') = coalesce(old.Booking_type,'') and
            coalesce(new.Number_Children,0) = coalesce(old.Number_Children,0) and
            coalesce(new.Number_Adults,0) = coalesce(old.Number_Adults,0) and
            coalesce(new.Confirmation_No,'') = coalesce(old.Confirmation_No,'') and
            coalesce(new.Channel,'') = coalesce(old.Channel,'') and
            coalesce(new.Inv_Block_Code,'') = coalesce(old.Inv_Block_Code,'') and
            coalesce(new.Market_Code,'') = coalesce(old.Market_Code,'')
        )
END
GO