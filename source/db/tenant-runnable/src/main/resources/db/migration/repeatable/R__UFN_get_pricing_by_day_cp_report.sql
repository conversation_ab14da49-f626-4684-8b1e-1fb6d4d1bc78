if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_pricing_by_day_cp_report]'))
drop function [ufn_get_pricing_by_day_cp_report]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Function Name: ufn_get_pricing_by_day_cp_report

Input Parameters : 
       @property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
       @start_date --> occupancy_start_date ('2011-07-01')
       @end_date --> occupancy_end_date ('2011-07-31')

       @isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
       @rolling_start_date --> used when @isRollingDate is 1, else, this can be blank
       @rolling_end_date --> used when @isRollingDate is 1, else, this can be blank

Ouput Parameter : NA

Execution: this is just an example
       Example 1: ufn_get_pricing_by_day_report
       -----------------------------------------
       select * from dbo.ufn_get_pricing_by_day_cp_report (10014,'2011-07-29','2011-08-28',1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,2,0,'','')
       select * from dbo.ufn_get_pricing_by_day_cp_report (10016,'2013-07-20','2013-07-20',-1,-1,-1,-1,-1,2,0,'','')
              select * from dbo.ufn_get_pricing_by_day_cp_report (10016,'2013-07-20','2013-07-20',-1,-1,-1,-1,-1,2,0,'','')
       
Purpose: The purpose of this function is to get pricing_by_day_cp_report
              
Author: Atul

Release Update:
Release_Dt           First_Name                 Last_Name                         Release Comments
----------    ----------------     -------------------        -------------------------------
06/18/2012           Atul                       Shendye                                  Initial Version
4/3/2020            Kyle                        Vierkant              updated to add @isAdjustRSSForTaxEnabled parameter
8/21/2020            Kyle                        Vierkant              Removed @isAdjustRSSForTaxEnabled parameter

***************************************************************************************/

CREATE function [dbo].[ufn_get_pricing_by_day_cp_report]
(
       @property_id int,
       @start_date date,
       @end_date date,
       @comp_id_1 int,
       @comp_id_2 int,
       @comp_id_3 int,
       @comp_id_4 int,
       @comp_id_5 int,
       @comp_id_6 int,
       @comp_id_7 int,
       @comp_id_8 int,
       @comp_id_9 int,
       @comp_id_10 int,
       @comp_id_11 int,
       @comp_id_12 int,
       @comp_id_13 int,
       @comp_id_14 int,
       @comp_id_15 int,
       @RoomTypes nvarchar(200),
       @isRollingDate int,
       @rolling_start_date nvarchar(50),
       @rolling_end_date nvarchar(50),
       @products nvarchar(500),    
	     @filterDtsWhenMatchesGFO int
)             
returns  @pricing_by_day_report table
(      
       Arrival_DT date,
       Override nvarchar(50),
       DOW varchar (10),
       Rooms_Sold numeric(18,0),
       Out_of_Order numeric(18,0),
       Occupancy_FCST numeric(18,2),
       Occupancy_FCST_Per numeric(18,2),
       property_Occupancy_NBR numeric(18,2),
       property_Occupancy_Percent numeric(8,2),
       Rate_Code_Name nvarchar(50),
       BarRate numeric(19,5),
       Accom_Class_Name nvarchar(150),
       Notes nvarchar(max),
       comp1_rate numeric(19,5),
       comp1_name nvarchar(150),
       comp2_rate numeric(19,5),
       comp2_name nvarchar(150),
       comp3_rate numeric(19,5),
       comp3_name nvarchar(150),
       comp4_rate numeric(19,5),
       comp4_name nvarchar(150),
       comp5_rate numeric(19,5),
       comp5_name nvarchar(150),
       comp6_rate numeric(19,5),
       comp6_name nvarchar(150),
       comp7_rate numeric(19,5),
       comp7_name nvarchar(150),
       comp8_rate numeric(19,5),
       comp8_name nvarchar(150),
       comp9_rate numeric(19,5),
       comp9_name nvarchar(150),
       comp10_rate numeric(19,5),
       comp10_name nvarchar(150),
       comp11_rate numeric(19,5),
       comp11_name nvarchar(150),
       comp12_rate numeric(19,5),
       comp12_name nvarchar(150),
       comp13_rate numeric(19,5),
       comp13_name nvarchar(150),
       comp14_rate numeric(19,5),
       comp14_name nvarchar(150),
       comp15_rate numeric(19,5),
       comp15_name nvarchar(150),
       LRV numeric(19,5),
       Total_Property_Rooms numeric(8,0),
       User_ID int,
       CreateDate_DTTM datetime,
       Username nvarchar(50),
       Occupancy_FCST_PhyCap_Per numeric(18,2),
       Property_Occupancy_PhyCap_Percent numeric(8,2),
       Accom_Type_Name nvarchar(150),
       Product_Name nvarchar(500)
)
as
begin

              declare @caughtupdate date 
              declare @RoomClasses nvarchar(100)
              declare @tempAccomClassId table(ID int, Accom_Class_ID int) 
              set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property
              
              insert into @tempAccomClassId select 1, Accom_Class_ID as Accom_Class_ID 
              FROM (SELECT value Accom_Type_ID FROM varcharToInt(@RoomTypes,',')
              )a inner join Accom_Type b on b.Accom_Type_ID = a.Accom_Type_ID and b.property_id = @property_id group by Accom_Class_ID
              SELECT @RoomClasses = STUFF((SELECT ', ' + CAST(Accom_Class_ID AS VARCHAR(10)) [text()]
                                         FROM @tempAccomClassId 
                                          WHERE ID = t.ID
                                         FOR XML PATH(''), TYPE)
                                         .value('.','NVARCHAR(MAX)'),1,2,' ') 
              FROM @tempAccomClassId t group by ID
              
              if(@RoomTypes = '-1')
              begin
                     SELECT @RoomClasses = STUFF((SELECT ', ' + CAST(Accom_Class_ID AS VARCHAR(10)) [text()]
                                         FROM Accom_Class 
                                          WHERE property_id = t.property_id
                                         FOR XML PATH(''), TYPE)
                                         .value('.','NVARCHAR(MAX)'),1,2,' ') 
                           FROM Accom_Class t where property_id = @property_id group by property_id
              end
                           
              if(@isRollingDate=1)
              begin
                     set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
                     set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
              end

              ------------------------------create temp table to keep room types------------------------------
              
              declare @selectedRoomTypesTable table (Accom_Type_ID int)

              if(@RoomTypes = '-1')
                     insert into @selectedRoomTypesTable select Accom_Type_ID from Accom_Type where Status_ID = 1
              else 
                     insert into @selectedRoomTypesTable select value as Accom_Type_ID from varcharToInt(@RoomTypes,',')
       
              ------------------------------create temp table to keep product, room types and date combination------------------------------

              declare @selectedProductsTable table (Property_ID int, Product_ID int, Product_Name nvarchar(100), Arrival_DT date, Accom_Type_ID int)
       
              insert into @selectedProductsTable  
              select p.Property_ID, p.Product_ID, p.Name as Product_Name, selectedDate.date as Arrival_DT, rt.Accom_Type_ID from 
              (
                     SELECT Value as Product_ID, p.Name, @property_id as Property_ID 
                     FROM 
                     varcharToInt(@products,',')
                     inner join 
                     Product as p
                     on p.Product_ID = value
              ) as p
              cross join 
              (
                     select Accom_Type_ID from @selectedRoomTypesTable
              
              ) as rt
              cross join
              (
                     select cast(calendar_date as date) date, @property_id as Property_ID from calendar_dim where calendar_date BETWEEN @start_date AND @end_date
              ) as selectedDate
              
              -----------------------------------------------Main query starts from here-------------------------------------------------------------
              
              INSERT INTO @pricing_by_day_report
              SELECT 
                     main.Occupancy_DT AS Arrival_DT,
                     bar.Override as Override,
                     DATENAME(dw,main.Occupancy_DT) AS DOW,
                     accomClassActivity.Room_Type_Rooms_Sold AS Rooms_Sold,
                     accomClassActivity.Room_Type_Outoforder AS Out_of_Order,
                     accomClassOccupancyForecast.OccupancyForecast AS Occupancy_FCST,
                     Occupancy_FCST_Per =
                             CASE (accomClassActivity.Room_Type_Capacity-accomClassActivity.Room_Type_Outoforder)
                                         WHEN 0 THEN 0
                             ELSE
                                         (accomClassOccupancyForecast.OccupancyForecast / (accomClassActivity.Room_Type_Capacity-accomClassActivity.Room_Type_Outoforder)) * 100
                             END,
                     propertyOccupancyForecast.Property_Occupancy_NBR,
                     propertyOccupancyForecast.Property_Occupancy_Percent,
                     NULL AS Rate_Code_Name,
                     bar.Final_BAR AS BarRate,
                     main.Accom_Class_Name,
                     notes.Notes,
                     comp1.webrate_ratevalue AS comp1_rate,comp1.webrate_competitors_name AS comp1_name,
                     comp2.webrate_ratevalue AS comp2_rate,comp2.webrate_competitors_name AS comp2_name,
                     comp3.webrate_ratevalue AS comp3_rate,comp3.webrate_competitors_name AS comp3_name,
                     comp4.webrate_ratevalue AS comp4_rate,comp4.webrate_competitors_name AS comp4_name,
                     comp5.webrate_ratevalue AS comp5_rate,comp5.webrate_competitors_name AS comp5_name,
                     comp6.webrate_ratevalue AS comp6_rate,comp6.webrate_competitors_name AS comp6_name,
                     comp7.webrate_ratevalue AS comp7_rate,comp7.webrate_competitors_name AS comp7_name,
                     comp8.webrate_ratevalue AS comp8_rate,comp8.webrate_competitors_name AS comp8_name,
                     comp9.webrate_ratevalue AS comp9_rate,comp9.webrate_competitors_name AS comp9_name,
                     comp10.webrate_ratevalue AS comp10_rate,comp10.webrate_competitors_name AS comp10_name,
                     comp11.webrate_ratevalue AS comp11_rate,comp11.webrate_competitors_name AS comp11_name,
                     comp12.webrate_ratevalue AS comp12_rate,comp12.webrate_competitors_name AS comp12_name,
                     comp13.webrate_ratevalue AS comp13_rate,comp13.webrate_competitors_name AS comp13_name,
                     comp14.webrate_ratevalue AS comp14_rate,comp14.webrate_competitors_name AS comp14_name,
                     comp15.webrate_ratevalue AS comp15_rate,comp15.webrate_competitors_name AS comp15_name,
                     lrv.LRV AS lrv,
                     totalActivity.Property_Rooms_Sold AS Total_Property_Rooms,
                     bar.User_ID,
                     bar.CreateDate_DTTM,
                     bar.User_Name AS Username,
                     Occupancy_FCST_PhyCap_Per =
                           CASE (accomClassActivity.Room_Type_Capacity)
                          WHEN 0 THEN 0
                           ELSE
                          (accomClassOccupancyForecast.OccupancyForecast / (accomClassActivity.Room_Type_Capacity)) * 100
                           END,
                     Property_Occupancy_PhyCap_Percent =
                           CASE (propertyOccupancyForecast.Total_Accom_Capacity)
                          WHEN 0 THEN 0
                           ELSE
                          (propertyOccupancyForecast.property_Occupancy_NBR / (propertyOccupancyForecast.Total_Accom_Capacity)) * 100
                           END,
                     main.Accom_Type_Name AS Accom_Type_Name,
                     bar.Product_Name
              FROM
              (
                     SELECT aa.Property_ID, p.Property_Name, aa.Occupancy_DT, at.Accom_Class_ID, ac.Accom_Class_Name, at.Accom_Type_ID, at.Accom_Type_Name
                     FROM Accom_Activity aa INNER JOIN Property p ON aa.Property_ID = p.Property_ID INNER JOIN Accom_Type at ON aa.Accom_Type_ID = at.Accom_Type_ID INNER JOIN Accom_Class ac on at.Accom_Class_ID = ac.Accom_Class_ID
                     WHERE aa.Property_ID = @property_id AND aa.Occupancy_DT BETWEEN @start_date AND @end_date
                     AND at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
              ) main
              LEFT JOIN (
                     SELECT 
                           Property_ID,
                           Occupancy_DT,
                           Rooms_Sold as Property_Rooms_Sold,
                           Total_Accom_Capacity as Property_capacity,
                           (Rooms_Not_Avail_Maint+Rooms_Not_Avail_Other) Property_Outoforder
                     FROM Total_Activity Total_Activity
                     WHERE Total_Activity.Property_ID=@property_id AND Occupancy_DT BETWEEN @start_date AND @end_date
              ) AS totalActivity on main.Property_ID = totalActivity.Property_ID AND main.Occupancy_DT = totalActivity.Occupancy_DT
              LEFT JOIN
              (
                     SELECT
                           AA.Property_ID,
                           AA.Occupancy_DT,
                           AT.Accom_Class_ID,
                           SUM(Rooms_Sold) AS Room_Type_Rooms_Sold,
                           SUM(AA.Accom_capacity) AS Room_Type_Capacity,
                           (SUM(AA.Rooms_Not_Avail_Maint) + SUM(AA.Rooms_Not_Avail_Other)) AS Room_Type_Outoforder
                     FROM Accom_Activity aa INNER JOIN Accom_Type at on aa.Accom_Type_ID = at.Accom_Type_ID
                     WHERE aa.Property_ID = @property_id AND aa.Occupancy_DT BETWEEN @start_date AND @end_date
                     AND at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                     GROUP BY AA.Property_ID,AA.Occupancy_DT,AT.Accom_Class_ID
              ) AS accomClassActivity ON main.Property_ID=accomClassActivity.Property_ID AND main.Occupancy_DT=accomClassActivity.Occupancy_DT AND main.Accom_Class_ID = accomClassActivity.Accom_Class_ID
              LEFT JOIN
              (
                    SELECT DL.Property_ID, DL.Occupancy_DT, DL.Accom_Class_ID, DL.LRV 
                    FROM Decision_LRV DL
                       WHERE DL.Property_ID=@property_id AND DL.Occupancy_DT BETWEEN @start_date AND @end_date
                       AND DL.Accom_Class_ID IN (SELECT DISTINCT at.Accom_Class_ID FROM Accom_Type at WHERE at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))))
              ) AS lrv ON main.Property_ID=lrv.Property_ID AND main.Occupancy_DT=lrv.Occupancy_DT AND main.Accom_Class_ID=lrv.Accom_Class_ID
              LEFT JOIN
              (
                    SELECT Property_ID, Occupancy_DT, Occupancy_Percent AS Property_Occupancy_Percent, Occupancy_NBR AS Property_Occupancy_NBR, Total_Accom_Capacity 
                    FROM dbo.FN_Occupancy_Forecast(@caughtupdate, 0)
                    WHERE Property_ID=@property_id AND Occupancy_DT BETWEEN @start_date AND @end_date
              ) AS propertyOccupancyForecast ON main.Property_ID=propertyOccupancyForecast.Property_ID AND main.Occupancy_DT=propertyOccupancyForecast.Occupancy_DT
              LEFT JOIN
              (
                    SELECT Property_ID, Occupancy_DT, Accom_class_ID, SUM(Occupancy_NBR) as OccupancyForecast
                       FROM dbo.FN_AT_MS_Occupancy(@caughtupdate, 0)
                    WHERE Property_ID=@property_id and Occupancy_DT between @start_date AND @end_date
                    AND Accom_Class_ID IN (SELECT DISTINCT at.Accom_Class_ID FROM Accom_Type at WHERE at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))))
                       GROUP BY Occupancy_DT, Property_ID, Accom_Class_ID
              ) AS accomClassOccupancyForecast ON main.Occupancy_DT=accomClassOccupancyForecast.Occupancy_DT AND main.Property_ID=accomClassOccupancyForecast.Property_ID AND main.Accom_Class_ID=accomClassOccupancyForecast.Accom_Class_ID
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_1,@start_date,@end_date,@RoomClasses) WHERE @comp_id_1>0
              ) AS comp1 ON main.Occupancy_DT=comp1.Occupancy_DT AND main.Property_ID=comp1.Property_ID and main.Accom_Class_ID = comp1.accom_Class_id
              LEFT JOIN
              (
                       SELECT * FROM ufn_get_notes_by_multiple_module (@property_id,@start_date,@end_date,'Overbooking','Continuous Pricing','Demand And Wash','')
              ) AS notes ON main.Occupancy_DT = notes.Arrival_DT and main.Property_ID=notes.Property_ID
              LEFT JOIN
              (
                     SELECT 
                           bar_output.Property_ID, 
                           bar_output.Arrival_DT,
                           bar_output.Accom_Type_ID,
                           bar_output.LOS,
                           bar_output.Final_BAR,
                           bar_output.Override,
                           bar_output_ovr.User_ID,
                           bar_output_ovr.User_Name,
                           bar_output_ovr.CreateDate_DTTM,
                           bar_output.Product_Name as Product_Name
                     FROM
                     (
                           select pr.Property_ID, pr.Arrival_DT, pr.Accom_Type_ID, rate.LOS, rate.Final_BAR, rate.Override, pr.Product_Name from 
                           (
                                  SELECT * from @selectedProductsTable
                           ) as pr
                           left join 
                           (
                                  SELECT cdbo.Property_ID, cdbo.Arrival_DT, cdbo.Accom_Type_ID, cdbo.LOS, cdbo.Final_BAR, cdbo.Override, cdbo.Product_ID--, p.Name, p.Product_ID
                                  FROM 
                                  CP_Decision_Bar_Output cdbo
                                  WHERE cdbo.Property_ID = @property_id AND cdbo.Arrival_DT BETWEEN @start_date AND @end_date AND cdbo.LOS = -1
                                  AND (@RoomTypes = '-1' OR cdbo.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                           ) as rate
                           on pr.Product_ID = rate.Product_ID and pr.Accom_Type_ID = rate.Accom_Type_ID and pr.Arrival_DT = rate.Arrival_DT and pr.Property_ID = rate.Property_ID

                     ) AS bar_output LEFT JOIN
                     (
                           SELECT cdboo.Property_ID, cdboo.Arrival_DT, cdboo.Accom_Type_ID, u.User_ID, u.User_Name, max(cdboo.CreateDate) as CreateDate_DTTM
                           FROM CP_Decision_Bar_Output_OVR cdboo
                           INNER JOIN Users U ON cdboo.User_ID=U.User_ID
                           LEFT JOIN Product as p
                           ON p.Product_ID = cdboo.Product_ID
                           WHERE cdboo.Property_ID=@property_id AND Arrival_DT BETWEEN @start_date AND @end_date AND LOS=-1
                           AND (@RoomTypes = '-1' OR cdboo.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                           AND cdboo.Product_ID IN (SELECT Value FROM varcharToInt(@products,','))
                           GROUP BY Property_ID, Arrival_DT, Accom_Type_ID, u.User_ID ,User_Name

                     ) AS bar_output_ovr ON bar_output.Property_ID = bar_output_ovr.Property_ID AND bar_output.Arrival_DT=bar_output_ovr.Arrival_DT AND bar_output.Accom_Type_ID=bar_output_ovr.Accom_Type_ID
              ) AS bar ON main.Property_ID=bar.Property_ID AND main.Occupancy_DT=bar.Arrival_DT AND main.Accom_Type_ID=bar.Accom_Type_ID
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_2,@start_date,@end_date,@RoomClasses) WHERE @comp_id_2>0
              ) AS comp2 ON main.Occupancy_DT=comp2.Occupancy_DT and main.Property_ID=comp2.Property_ID and main.Accom_Class_ID = comp2.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_3,@start_date,@end_date,@RoomClasses) WHERE @comp_id_3>0
              ) AS comp3 ON main.Occupancy_DT=comp3.Occupancy_DT and main.Property_ID=comp3.Property_ID and main.Accom_Class_ID = comp3.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_4,@start_date,@end_date,@RoomClasses) WHERE @comp_id_4>0
              ) AS comp4 ON main.Occupancy_DT=comp4.Occupancy_DT and main.Property_ID=comp4.Property_ID and main.Accom_Class_ID = comp4.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_5,@start_date,@end_date,@RoomClasses) WHERE @comp_id_5>0
              ) AS comp5 ON main.Occupancy_DT=comp5.Occupancy_DT and main.Property_ID=comp5.Property_ID and main.Accom_Class_ID = comp5.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_6,@start_date,@end_date,@RoomClasses) WHERE @comp_id_6>0
              ) AS comp6 ON main.Occupancy_DT = comp6.Occupancy_DT AND main.Property_ID = comp6.Property_ID and main.Accom_Class_ID = comp6.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_7,@start_date,@end_date,@RoomClasses) where @comp_id_7>0
              ) AS comp7 ON main.Occupancy_DT = comp7.Occupancy_DT and main.Property_ID = comp7.Property_ID and main.Accom_Class_ID = comp7.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_8,@start_date,@end_date,@RoomClasses) WHERE @comp_id_8>0
              ) AS comp8 ON main.Occupancy_DT = comp8.Occupancy_DT and main.Property_ID = comp8.Property_ID and main.Accom_Class_ID = comp8.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_9,@start_date,@end_date,@RoomClasses) WHERE @comp_id_9>0
              ) AS comp9 ON main.Occupancy_DT = comp9.Occupancy_DT and main.Property_ID = comp9.Property_ID and main.Accom_Class_ID = comp9.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_10,@start_date,@end_date,@RoomClasses) WHERE @comp_id_10>0
              ) AS comp10 ON main.Occupancy_DT = comp10.Occupancy_DT and main.Property_ID = comp10.Property_ID and main.Accom_Class_ID = comp10.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_11,@start_date,@end_date,@RoomClasses) WHERE @comp_id_11>0
              ) AS comp11 ON main.Occupancy_DT = comp11.Occupancy_DT and main.Property_ID = comp11.Property_ID and main.Accom_Class_ID = comp11.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_12,@start_date,@end_date,@RoomClasses) WHERE @comp_id_12>0
              ) as comp12 on main.Occupancy_DT = comp12.Occupancy_DT and main.Property_ID = comp12.Property_ID and main.Accom_Class_ID = comp12.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_13,@start_date,@end_date,@RoomClasses) WHERE @comp_id_13>0
              ) as comp13 on main.Occupancy_DT = comp13.Occupancy_DT and main.Property_ID = comp13.Property_ID and main.Accom_Class_ID = comp13.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_14,@start_date,@end_date,@RoomClasses) WHERE @comp_id_14>0
              ) AS comp14 ON main.Occupancy_DT = comp14.Occupancy_DT AND main.Property_ID = comp14.Property_ID and main.Accom_Class_ID = comp14.accom_Class_id
              LEFT JOIN
              (
                    SELECT * FROM ufn_get_web_competitors_rate_by_rc(@property_id,@comp_id_15,@start_date,@end_date,@RoomClasses) WHERE @comp_id_15>0
              ) AS comp15 ON main.Occupancy_DT = comp15.Occupancy_DT AND main.Property_ID = comp15.Property_ID and main.Accom_Class_ID = comp15.accom_Class_id
			  LEFT JOIN
			  Group_Floor_OVR  AS GFO
			  on bar.Accom_Type_ID = GFO.Base_Accom_Type_ID and bar.Arrival_DT = GFO.Occupancy_DT
			  where @filterDtsWhenMatchesGFO = 0 or (GFO.IsGFOApplied=1 AND GFO.Pretty_Group_Rate = bar.Final_BAR)
	
              ORDER BY main.Occupancy_DT
       return
end
GO