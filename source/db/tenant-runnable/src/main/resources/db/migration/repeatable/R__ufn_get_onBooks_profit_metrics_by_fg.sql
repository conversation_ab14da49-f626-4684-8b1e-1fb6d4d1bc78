if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_onBooks_profit_metrics_by_fg]'))
drop function [ufn_get_onBooks_profit_metrics_by_fg]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create function [dbo].[ufn_get_onBooks_profit_metrics_by_fg]
(
		@property_id int,
		@forecast_group_id varchar(500),
		@start_date date,
		@end_date date
)		
returns  @profit_metrics table
	(	
		occupancy_dt	date,
		property_id	int,
		profit numeric(19,5),
		proPOR	numeric(19,5)
	)
as
begin
		insert into @profit_metrics
		select  
			mca.occupancy_dt,
			mca.property_id,
			sum(mca.total_profit) as profit,
			proPOR =
				case (sum(mca.rooms_sold))
					when 0 then 0
				else
					sum(mca.total_profit)/sum(mca.rooms_sold)
				end
		from mkt_accom_activity mca inner join Accom_Type at on mca.Accom_Type_Id = at.Accom_Type_ID and at.isComponentRoom = 'N'
			inner join mkt_seg msg on mca.mkt_seg_id = msg.mkt_seg_id and mca.property_id = msg.property_id and msg.Status_ID in (1,3)
			inner join mkt_seg_forecast_group msfg on msg.mkt_seg_id = msfg.mkt_seg_id and msfg.Status_ID=1
		where mca.property_id = @property_id
			and mca.occupancy_dt between @start_date and @end_date
			and forecast_group_id in (select value from varchartoint(@forecast_group_id,',')) --> parse forcast_group_id string to integers
		group by mca.property_id,mca.occupancy_dt
		order by mca.occupancy_dt
	return
end

GO