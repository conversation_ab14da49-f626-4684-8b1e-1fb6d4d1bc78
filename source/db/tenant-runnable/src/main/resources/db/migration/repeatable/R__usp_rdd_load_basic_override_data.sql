drop procedure if exists [dbo].[usp_rdd_load_basic_override_data]
    GO
/****** Object:  StoredProcedure [dbo].[usp_rdd_load_basic_override_data]    Script Date@ 3/21/2023 7:55:39 PM ******/
    SET ANSI_NULLS ON
    GO

    SET QUOTED_IDENTIFIER ON
    GO

CREATE PROCEDURE [dbo].[usp_rdd_load_basic_override_data] (
	 @propertyId INT
	,@startDate DATE
	,@endDate DATE
	,@hiltonSpecificEnabled int
	)
AS
BEGIN

With ValidAccomClasses as (
    select Accom_Class_Id, accom_class_Name, View_Order from Accom_Class
    where status_id = 1 and System_Default = 0
),
     ValidAccomTypes as (
         select Accom_Type_Id, Accom_Type_Name, Accom_Class_ID from Accom_Type
         where Status_ID=1 and System_Default=0 and Display_Status_ID = 1 and accom_type_capacity > 0
     ),
     ValidDecisionBarOutput as (
         select Arrival_DT, vac.Accom_Class_ID, vac.Accom_Class_Name, dbo.Override override,
                dbo.Rate_Unqualified_ID as User_Specified_Rate,
                dbo.Ceil_Rate_Unqualified_ID as Ceil_Rate,
                dbo.Floor_Rate_Unqualified_ID as Floor_Rate,
                vac.View_Order
         from Decision_Bar_Output dbo
                  inner join ValidAccomClasses vac on dbo.Accom_Class_ID=vac.Accom_Class_ID
         where dbo.Property_ID=@propertyId and Arrival_DT between @startDate and @endDate and dbo.LOS in (1,-1)
     ),
     SpecifiedRateUnqualifiedData as (
         select vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name,
                vdbo.Override,
                rate =
                case (datename(dw,vdbo.Arrival_DT))
                    when 'monday' then rud.monday
                    when 'tuesday' then rud.tuesday
                    when 'wednesday' then rud.wednesday
                    when 'thursday' then rud.thursday
                    when 'friday' then rud.friday
                    when 'saturday' then rud.saturday
                    when 'sunday' then rud.sunday
                    end,
                ROW_NUMBER() OVER ( PARTITION BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name,vdbo.Override,
                    ru.Rate_Unqualified_ID ORDER BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name,vdbo.Override,
                    ru.Rate_Unqualified_ID) row_num
         from ValidDecisionBarOutput vdbo
                  inner join Rate_Unqualified ru on ru.Rate_Unqualified_ID=vdbo.User_Specified_Rate and ru.Property_ID = @propertyId and ru.Status_ID = 1 AND ru.System_Default = 0
                  inner join Rate_Unqualified_Details rud on rud.Rate_Unqualified_ID = ru.Rate_Unqualified_ID  and vdbo.Arrival_DT between rud.Start_Date_DT and rud.End_Date_DT
                  inner join ValidAccomTypes at on rud.accom_type_id=at.accom_type_id and at.accom_class_id = vdbo.Accom_Class_ID
where vdbo.Override like '%USER%'
order by vdbo.View_Order offset 0 rows
    ),
    FloorRateUnqualifiedData as (
select vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name, vdbo.Override,
    rate =
    case (datename(dw,vdbo.Arrival_DT))
    when 'monday' then rud.monday
    when 'tuesday' then rud.tuesday
    when 'wednesday' then rud.wednesday
    when 'thursday' then rud.thursday
    when 'friday' then rud.friday
    when 'saturday' then rud.saturday
    when 'sunday' then rud.sunday
    end,
    ROW_NUMBER() OVER ( PARTITION BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name, vdbo.Override,
    ru.Rate_Unqualified_ID ORDER BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name, vdbo.Override,
    ru.Rate_Unqualified_ID) row_num
from ValidDecisionBarOutput vdbo
    inner join Rate_Unqualified ru on ru.Rate_Unqualified_ID=vdbo.Floor_Rate and ru.Property_ID = @propertyId
    inner join Rate_Unqualified_Details rud on rud.Rate_Unqualified_ID = ru.Rate_Unqualified_ID  and vdbo.Arrival_DT between rud.Start_Date_DT and rud.End_Date_DT
    inner join ValidAccomTypes at on rud.accom_type_id=at.accom_type_id and at.accom_class_id = vdbo.Accom_Class_ID
where vdbo.Override like '%Floor%'
order by vdbo.View_Order offset 0 rows
    ),
    CeilRateUnqualifiedData as (
select vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name, vdbo.Override,
    rate =
    case (datename(dw,vdbo.Arrival_DT))
    when 'monday' then rud.monday
    when 'tuesday' then rud.tuesday
    when 'wednesday' then rud.wednesday
    when 'thursday' then rud.thursday
    when 'friday' then rud.friday
    when 'saturday' then rud.saturday
    when 'sunday' then rud.sunday
    end,
    ROW_NUMBER() OVER ( PARTITION BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name,vdbo.Override,
    ru.Rate_Unqualified_ID ORDER BY vdbo.Arrival_DT, vdbo.Accom_Class_ID, vdbo.Accom_Class_Name,vdbo.Override,
    ru.Rate_Unqualified_ID) row_num
from ValidDecisionBarOutput vdbo
    inner join Rate_Unqualified ru on ru.Rate_Unqualified_ID=vdbo.Ceil_Rate and ru.Property_ID = @propertyId
    inner join Rate_Unqualified_Details rud on rud.Rate_Unqualified_ID = ru.Rate_Unqualified_ID  and vdbo.Arrival_DT between rud.Start_Date_DT and rud.End_Date_DT
    inner join ValidAccomTypes at on rud.accom_type_id=at.accom_type_id and at.accom_class_id = vdbo.Accom_Class_ID
where vdbo.Override like '%CEIL%'
order by vdbo.View_Order offset 0 rows
    )
select Occupancy_DT,
       barRestricted.accomClasses as LRV_Greater_AccomClasses,
       userOvr.accomClasses as User_Override_AC_With_Rate,
       floorOvr.accomClasses as Floor_Override_AC_With_Rate,
       ceilOvr.accomClasses as Ceil_Override_AC_With_Rate
From
    (
        select CAST(calendar_date as date) Occupancy_DT
        from calendar_dim where calendar_date between @startDate and @endDate
    ) base
        left join
    (
        select bar.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),bar.Accom_Class_Name), CHAR(10)) accomClasses
        from
            (select distinct dbo.Arrival_DT, dbo.Accom_Class_Id, ac.Accom_CLass_Name, dbo.Decision_Reason_Type_ID, ac.View_Order from Decision_Bar_Output dbo
                                                                                                                                          inner join Rate_Unqualified ru on dbo.Rate_Unqualified_ID = ru.Rate_Unqualified_ID inner join Accom_Class ac on ac.Accom_Class_Id = dbo.Accom_Class_Id
             where dbo.property_id = @propertyId and dbo.Arrival_DT between @startDate and @endDate and dbo.Decision_Reason_Type_ID = 2 and not (@hiltonSpecificEnabled = 1 and not ru.Rate_Code_Name like 'LV0')
             order by ac.View_Order offset 0 Rows) bar group by bar.Arrival_DT
    ) barRestricted on base.Occupancy_DT=barRestricted.Arrival_DT
        left join
    (
        select srud.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max), concat(srud.Accom_Class_Name, '-', cast(srud.rate as numeric(19,2)))), CHAR(10)) accomClasses
        from SpecifiedRateUnqualifiedData srud where row_num=1
        group by srud.Arrival_DT
    ) userovr on base.Occupancy_DT=userOvr.Arrival_DT
        left join
    (
        select frud.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),concat(frud.Accom_Class_Name, '-', cast(frud.rate as numeric(19,2)))), CHAR(10)) accomClasses
        from FloorRateUnqualifiedData frud where row_num=1
        group by frud.Arrival_DT
    ) floorovr on base.Occupancy_DT=floorOvr.Arrival_DT
        left join
    (
        select crud.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),concat(crud.Accom_Class_Name, '-', cast(crud.rate as numeric(19,2)))), CHAR(10)) accomClasses
        from CeilRateUnqualifiedData crud where row_num=1
        group by crud.Arrival_DT
    ) ceilovr on base.Occupancy_DT=ceilOvr.Arrival_DT
END
