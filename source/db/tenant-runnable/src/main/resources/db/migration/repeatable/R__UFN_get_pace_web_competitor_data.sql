if exists(select * from sys.objects where object_id = object_id(N'[ufn_get_pace_web_competitor_data]'))
  drop function [ufn_get_pace_web_competitor_data]
    GO


  SET ANSI_NULLS ON
    GO
  SET QUOTED_IDENTIFIER ON
   GO

CREATE FUNCTION [dbo].[ufn_get_pace_web_competitor_data]
(
        @gen_end_dt date ,
        @gen_start_dt date ,
        @webrate_Source_property_ID int ,
        @Occupancy_DT date ,
        @accom_class_id int ,
        @property_id int ,
        @Webrate_Competitors_ID int ,
        @Use_Compact_Webrate_Pace bit
 )
 returns @pace_web_rate_comp table
 (
   PACE_Webrate_ID int,
   Webrate_GenerationDate date,
   Occupancy_DT date,
   generationDate datetime,
   Webrate_RateValue numeric(19,5),
   LOS int,
   Webrate_Competitors_ID int,
   Webrate_Channel_ID int
 ) AS
  BEGIN
	declare @webrateTypeID int;
	set @webrateTypeID = (select top 1 wtp.Webrate_Type_ID from Webrate_Type_Product wtp where wtp.Product_ID=1);

	IF @Use_Compact_Webrate_Pace = 1
      BEGIN
        insert into @pace_web_rate_comp(PACE_Webrate_ID , Webrate_GenerationDate , Occupancy_DT , generationDate , Webrate_RateValue , LOS , Webrate_Competitors_ID , Webrate_Channel_ID
          )

        select info.PACE_Webrate_ID,info.First_Webrate_GenerationDate, info.Occupancy_DT,
               info.generationDate, info.Webrate_RateValue_Display, info.LOS, comp.Webrate_Competitors_ID,info.Webrate_Channel_ID
        from
            (select Webrate_Competitors_ID from Webrate_Competitors where Webrate_Competitors_ID in(@Webrate_Competitors_ID) and  Status_ID in (1,3) ) as comp
                left join
            (
        Select max(PACE_Webrate_ID) PACE_Webrate_ID -- to satisfy group by clause
          ,First_Webrate_GenerationDate, PWD.Occupancy_DT , REPLACE(CONVERT(VARCHAR(10), First_Webrate_GenerationDate, 111), '/', '-') AS generationDate , min(Webrate_RateValue_Display) AS Webrate_RateValue_Display , LOS , Webrate_Competitors_ID , PWD.Webrate_Channel_ID
        from PACE_Webrate_Differential PWD
          inner join
            (
              select * from Webrate_Channel
              where
                Property_ID=@property_id
                and Status_ID in (1, 3)
            )channel
            on
              PWD.Webrate_Channel_ID=channel.Webrate_Channel_ID
          inner join
            (
              select * from dbo.vw_webrate_channel
              where
                occupancy_dt   = @Occupancy_DT
                and property_id=@property_id
            )default_channel
            on
              PWD.Occupancy_DT = default_channel.Occupancy_DT
              and PWD.Webrate_Channel_ID = default_channel.Channel_ID
        where
          PWD.Webrate_Source_Property_ID = @webrate_Source_property_ID
          and PWD.Webrate_Competitors_ID=@Webrate_Competitors_ID
          and PWD.Webrate_Accom_Type_ID in
          (
            select webT.webrate_Accom_Type_ID
            from webrate_accom_type webT
              inner join
                Webrate_Accom_Class_Mapping webM
                on
                  webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID
                  and webM.Accom_Class_ID = @accom_class_id
                  and property_ID = @property_id
          )
          and PWD.los = 1
          and PWD.Webrate_status = 'A'
          and PWD.Occupancy_DT   = @occupancy_dt
		  AND (@webrateTypeID is null or PWD.Webrate_Type_ID = @webrateTypeID)
        group by
          PWD.Webrate_Source_Property_ID , PWD.First_Webrate_GenerationDate , PWD.Webrate_Competitors_ID , PWD.Webrate_Channel_ID , PWD.Webrate_Type_ID , PWD.Occupancy_DT , PWD.LOS)as info
        ON comp.Webrate_Competitors_ID = info.Webrate_Competitors_ID
        ;
      END

      ELSE
      BEGIN
      insert into @pace_web_rate_comp(PACE_Webrate_ID , Webrate_GenerationDate , Occupancy_DT , generationDate , Webrate_RateValue , LOS , Webrate_Competitors_ID , Webrate_Channel_ID
          )
      select info.PACE_Webrate_ID,info.Webrate_GenerationDate, info.Occupancy_DT,
             info.generationDate, info.Webrate_RateValue, info.LOS, comp.Webrate_Competitors_ID,info.Webrate_Channel_ID
      from
          (select Webrate_Competitors_ID from Webrate_Competitors where Webrate_Competitors_ID in(@Webrate_Competitors_ID) and  Status_ID in (1,3) ) as comp
              left join
          (
        Select max(PACE_Webrate_ID) PACE_Webrate_ID -- to satisfy group by clause
          , Webrate_GenerationDate , PW.Occupancy_DT , REPLACE(CONVERT(VARCHAR(10), Webrate_GenerationDate, 111), '/', '-') AS generationDate , min(Webrate_RateValue) Webrate_RateValue , LOS , Webrate_Competitors_ID , PW.Webrate_Channel_ID
        from  PACE_Webrate PW
          inner join
            (
              select * from Webrate_Channel
              where
                Property_ID=@property_id
                and Status_ID in (1, 3)
            )channel
            on
              PW.Webrate_Channel_ID=channel.Webrate_Channel_ID
          inner join
            (
              select * from dbo.vw_webrate_channel
              where
                occupancy_dt   = @Occupancy_DT
                and property_id=@property_id
            )default_channel
            on
              PW.Occupancy_DT = default_channel.Occupancy_DT
              and PW.Webrate_Channel_ID = default_channel.Channel_ID
        where
          pw.Webrate_Source_Property_ID = @webrate_Source_property_ID
          and PW.Webrate_Competitors_ID=@Webrate_Competitors_ID
          and pw.Webrate_Accom_Type_ID in
          (
            select webT.webrate_Accom_Type_ID
            from webrate_accom_type webT
              inner join
                Webrate_Accom_Class_Mapping webM
                on
                  webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID
                  and webM.Accom_Class_ID    = @accom_class_id
                  and property_ID            = @property_id
          )
          and PW.los = 1
          and PW.Webrate_status = 'A'
          and PW.Occupancy_DT   = @occupancy_dt
		  AND (@webrateTypeID is null or PW.Webrate_Type_ID = @webrateTypeID)
        group by
          PW.Webrate_Source_Property_ID , PW.Webrate_GenerationDate , PW.Webrate_Competitors_ID , PW.Webrate_Channel_ID , PW.Webrate_Type_ID , PW.Occupancy_DT , PW.LOS)as info
          ON comp.Webrate_Competitors_ID = info.Webrate_Competitors_ID
        ;

      END

      RETURN
    END

GO