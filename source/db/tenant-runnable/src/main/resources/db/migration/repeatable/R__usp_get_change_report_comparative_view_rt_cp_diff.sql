IF EXISTS
(
    SELECT *
    FROM sys.objects
    WHERE object_id = OBJECT_ID(N'[dbo].[usp_get_change_report_comparative_view_rt_cp_diff]')
)
    DROP PROCEDURE [dbo].[usp_get_change_report_comparative_view_rt_cp_diff]
GO

/****** Object:  StoredProcedure [dbo].[usp_get_change_report_comparative_view_rt_cp_diff]    Script Date: 8/9/2023 4:51:06 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_get_change_report_comparative_view_rt_cp_diff]
(
    @property_id int,
    @roomtype_id varchar(500),
    @business_dt date,
    @start_date date,
    @end_date date,
    @isRollingDate int,
    @rolling_business_dt nvarchar(50),
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50),
    @use_physical_capacity int,
    @useCpPaceDifferentialTableEnabled int,
    @product_ids varchar(500)
)
as
begin
    declare @caughtupdate date
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    ) --> extract caughtup date for a property

    if (@isRollingDate = 1)
begin
        set @business_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_dt, @caughtupdate)
        )
        set @start_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate)
        )
        set @end_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate)
        )
end
	begin

	select d.business_DT, maxDecisionIdOfBDE, caught_up_DTTM
		into #Temp_BDE_decision_Ids
		from decision d inner join (
			select business_DT, max(decision_id) maxDecisionIdOfBDE
			from decision
			where business_DT =  @business_dt
				and decision_type_id = 1 and process_status_id = 13
			group by business_dt ) as tempBDE on d.decision_id = maxDecisionIdOfBDE and d.business_DT = tempBDE.business_DT

	select d.business_DT, maxDecisionIdOfBdeCdp, caught_up_DTTM
		into #Temp_BDE_CDP_decision_Ids
        from decision d inner join (
        select max(business_DT) business_DT, max(decision_id) maxDecisionIdOfBdeCdp
        from decision
        where decision_type_id in (1,2) and process_status_id = 13
    ) as tempBdeCdp on d.decision_id = maxDecisionIdOfBdeCdp and d.business_DT = tempBdeCdp.business_DT

	end


    declare @rt1 int
    declare @rt2 int
    declare @rt3 int
    declare @rt4 int
    declare @rt5 int
    declare @rt6 int
    declare @rt7 int
    declare @rt8 int
    declare @rt9 int
    declare @rt10 int
    declare @rt11 int
    declare @rt12 int
    declare @rt13 int
    declare @rt14 int
    declare @rt15 int
    declare @rt16 int
    declare @rt17 int
    declare @rt18 int
    declare @rt19 int
    declare @rt20 int
    declare @rt21 int
    declare @rt22 int
    declare @rt23 int
    declare @rt24 int
    declare @rt25 int
    declare @rt26 int
    declare @rt27 int
    declare @rt28 int
    declare @rt29 int
    declare @rt30 int
    declare @rt31 int
    declare @rt32 int
    declare @rt33 int
    declare @rt34 int
    declare @rt35 int
    declare @rt36 int
    declare @rt37 int
    declare @rt38 int
    declare @rt39 int
    declare @rt40 int
    declare @rt41 int
    declare @rt42 int
    declare @rt43 int
    declare @rt44 int
    declare @rt45 int
    declare @rt46 int
    declare @rt47 int
    declare @rt48 int
    declare @rt49 int
    declare @rt50 int
    declare @product_id int
    declare @product_code nvarchar(100)

select number = ROW_NUMBER() OVER (ORDER BY Accom_Type_id),
        Accom_Type_id
INTO #tempRT
from Accom_Type
where Property_ID = @property_id
  and Accom_Type_id in (
    SELECT Value FROM varcharToInt(@roomtype_id, ',')
)
  and Status_ID = 1
  and System_Default = 0

declare @tempProducts table
    (
        number int,
        product_id int,
        product_code nvarchar(100)
    )
insert into @tempProducts
select number = ROW_NUMBER() OVER (ORDER BY Product_ID), Product_ID, Code
from Product where Product_ID in (SELECT Value FROM varcharToInt(@product_ids, ','))
               and Status_ID = 1

    set @rt1 =
    (
        Select Accom_Type_id from #tempRT where number = 1
    )
set @rt2 =
    (
    Select Accom_Type_id from #tempRT where number = 2
    )
set @rt3 =
    (
    Select Accom_Type_id from #tempRT where number = 3
    )
set @rt4 =
    (
    Select Accom_Type_id from #tempRT where number = 4
    )
set @rt5 =
    (
    Select Accom_Type_id from #tempRT where number = 5
    )
set @rt6 =
    (
    Select Accom_Type_id from #tempRT where number = 6
    )
set @rt7 =
    (
    Select Accom_Type_id from #tempRT where number = 7
    )
set @rt8 =
    (
    Select Accom_Type_id from #tempRT where number = 8
    )
set @rt9 =
    (
    Select Accom_Type_id from #tempRT where number = 9
    )
set @rt10 =
    (
    Select Accom_Type_id from #tempRT where number = 10
    )
set @rt11 =
    (
    Select Accom_Type_id from #tempRT where number = 11
    )
set @rt12 =
    (
    Select Accom_Type_id from #tempRT where number = 12
    )
set @rt13 =
    (
    Select Accom_Type_id from #tempRT where number = 13
    )
set @rt14 =
    (
    Select Accom_Type_id from #tempRT where number = 14
    )
set @rt15 =
    (
    Select Accom_Type_id from #tempRT where number = 15
    )
set @rt16 =
    (
    Select Accom_Type_id from #tempRT where number = 16
    )
set @rt17 =
    (
    Select Accom_Type_id from #tempRT where number = 17
    )
set @rt18 =
    (
    Select Accom_Type_id from #tempRT where number = 18
    )
set @rt19 =
    (
    Select Accom_Type_id from #tempRT where number = 19
    )
set @rt20 =
    (
    Select Accom_Type_id from #tempRT where number = 20
    )
set @rt21 =
    (
    Select Accom_Type_id from #tempRT where number = 21
    )
set @rt22 =
    (
    Select Accom_Type_id from #tempRT where number = 22
    )
set @rt23 =
    (
    Select Accom_Type_id from #tempRT where number = 23
    )
set @rt24 =
    (
    Select Accom_Type_id from #tempRT where number = 24
    )
set @rt25 =
    (
    Select Accom_Type_id from #tempRT where number = 25
    )
set @rt26 =
    (
    Select Accom_Type_id from #tempRT where number = 26
    )
set @rt27 =
    (
    Select Accom_Type_id from #tempRT where number = 27
    )
set @rt28 =
    (
    Select Accom_Type_id from #tempRT where number = 28
    )
set @rt29 =
    (
    Select Accom_Type_id from #tempRT where number = 29
    )
set @rt30 =
    (
    Select Accom_Type_id from #tempRT where number = 30
    )
set @rt31 =
    (
    Select Accom_Type_id from #tempRT where number = 31
    )
set @rt32 =
    (
    Select Accom_Type_id from #tempRT where number = 32
    )
set @rt33 =
    (
    Select Accom_Type_id from #tempRT where number = 33
    )
set @rt34 =
    (
    Select Accom_Type_id from #tempRT where number = 34
    )
set @rt35 =
    (
    Select Accom_Type_id from #tempRT where number = 35
    )
set @rt36 =
    (
    Select Accom_Type_id from #tempRT where number = 36
    )
set @rt37 =
    (
    Select Accom_Type_id from #tempRT where number = 37
    )
set @rt38 =
    (
    Select Accom_Type_id from #tempRT where number = 38
    )
set @rt39 =
    (
    Select Accom_Type_id from #tempRT where number = 39
    )
set @rt40 =
    (
    Select Accom_Type_id from #tempRT where number = 40
    )
set @rt41 =
    (
    Select Accom_Type_id from #tempRT where number = 41
    )
set @rt42 =
    (
    Select Accom_Type_id from #tempRT where number = 42
    )
set @rt43 =
    (
    Select Accom_Type_id from #tempRT where number = 43
    )
set @rt44 =
    (
    Select Accom_Type_id from #tempRT where number = 44
    )
set @rt45 =
    (
    Select Accom_Type_id from #tempRT where number = 45
    )
set @rt46 =
    (
    Select Accom_Type_id from #tempRT where number = 46
    )
set @rt47 =
    (
    Select Accom_Type_id from #tempRT where number = 47
    )
set @rt48 =
    (
    Select Accom_Type_id from #tempRT where number = 48
    )
set @rt49 =
    (
    Select Accom_Type_id from #tempRT where number = 49
    )
set @rt50 =
    (
    Select Accom_Type_id from #tempRT where number = 50
    )

set @product_id =
    (
    Select product_id from @tempProducts where number = 1
    )
set @product_code =
    (
    Select product_code from @tempProducts where number = 1
    )

	select *
	INTO #GroupBlock
	from ufn_get_groupBlock_groupPickup_by_RoomType(
			@property_id,
			@roomtype_id,
			@start_date,
			@end_date,
			0
		)


	select *
	INTO #overbookingasofdate
	from ufn_get_ovrbk_decision_asof_lastOptimization_or_businessdate_by_individual_rt(
			@property_id,
			@roomtype_id,
			@business_dt,
			@start_date,
			@end_date,
			@rolling_business_dt
		)
		
--- extract report metrics
select occupancy_dt,
       dow,
       special_event,
       sum(outoforder) outoforder,
       isnull(MAX(rt1_rooms_sold), 0.0) as rt1_rooms_sold,
       isnull(MAX(rt1_rooms_solds_change), 0.0) as rt1_rooms_solds_change,
       isnull(MAX(rt2_rooms_sold), 0.0) as rt2_rooms_sold,
       isnull(MAX(rt2_rooms_solds_change), 0.0) as rt2_rooms_solds_change,
       isnull(MAX(rt3_rooms_sold), 0.0) as rt3_rooms_sold,
       isnull(MAX(rt3_rooms_solds_change), 0.0) as rt3_rooms_solds_change,
       isnull(MAX(rt4_rooms_sold), 0.0) as rt4_rooms_sold,
       isnull(MAX(rt4_rooms_solds_change), 0.0) as rt4_rooms_solds_change,
       isnull(MAX(rt5_rooms_sold), 0.0) as rt5_rooms_sold,
       isnull(MAX(rt5_rooms_solds_change), 0.0) as rt5_rooms_solds_change,
       isnull(MAX(rt6_rooms_sold), 0.0) as rt6_rooms_sold,
       isnull(MAX(rt6_rooms_solds_change), 0.0) as rt6_rooms_solds_change,
       isnull(MAX(rt7_rooms_sold), 0.0) as rt7_rooms_sold,
       isnull(MAX(rt7_rooms_solds_change), 0.0) as rt7_rooms_solds_change,
       isnull(MAX(rt8_rooms_sold), 0.0) as rt8_rooms_sold,
       isnull(MAX(rt8_rooms_solds_change), 0.0) as rt8_rooms_solds_change,
       isnull(MAX(rt9_rooms_sold), 0.0) as rt9_rooms_sold,
       isnull(MAX(rt9_rooms_solds_change), 0.0) as rt9_rooms_solds_change,
       isnull(MAX(rt10_rooms_sold), 0.0) as rt10_rooms_sold,
       isnull(MAX(rt10_rooms_solds_change), 0.0) as rt10_rooms_solds_change,
       isnull(MAX(rt11_rooms_sold), 0.0) as rt11_rooms_sold,
       isnull(MAX(rt11_rooms_solds_change), 0.0) as rt11_rooms_solds_change,
       isnull(MAX(rt12_rooms_sold), 0.0) as rt12_rooms_sold,
       isnull(MAX(rt12_rooms_solds_change), 0.0) as rt12_rooms_solds_change,
       isnull(MAX(rt13_rooms_sold), 0.0) as rt13_rooms_sold,
       isnull(MAX(rt13_rooms_solds_change), 0.0) as rt13_rooms_solds_change,
       isnull(MAX(rt14_rooms_sold), 0.0) as rt14_rooms_sold,
       isnull(MAX(rt14_rooms_solds_change), 0.0) as rt14_rooms_solds_change,
       isnull(MAX(rt15_rooms_sold), 0.0) as rt15_rooms_sold,
       isnull(MAX(rt15_rooms_solds_change), 0.0) as rt15_rooms_solds_change,
       isnull(MAX(rt16_rooms_sold), 0.0) as rt16_rooms_sold,
       isnull(MAX(rt16_rooms_solds_change), 0.0) as rt16_rooms_solds_change,
       isnull(MAX(rt17_rooms_sold), 0.0) as rt17_rooms_sold,
       isnull(MAX(rt17_rooms_solds_change), 0.0) as rt17_rooms_solds_change,
       isnull(MAX(rt18_rooms_sold), 0.0) as rt18_rooms_sold,
       isnull(MAX(rt18_rooms_solds_change), 0.0) as rt18_rooms_solds_change,
       isnull(MAX(rt19_rooms_sold), 0.0) as rt19_rooms_sold,
       isnull(MAX(rt19_rooms_solds_change), 0.0) as rt19_rooms_solds_change,
       isnull(MAX(rt20_rooms_sold), 0.0) as rt20_rooms_sold,
       isnull(MAX(rt20_rooms_solds_change), 0.0) as rt20_rooms_solds_change,
       isnull(MAX(rt21_rooms_sold), 0.0) as rt21_rooms_sold,
       isnull(MAX(rt21_rooms_solds_change), 0.0) as rt21_rooms_solds_change,
       isnull(MAX(rt22_rooms_sold), 0.0) as rt22_rooms_sold,
       isnull(MAX(rt22_rooms_solds_change), 0.0) as rt22_rooms_solds_change,
       isnull(MAX(rt23_rooms_sold), 0.0) as rt23_rooms_sold,
       isnull(MAX(rt23_rooms_solds_change), 0.0) as rt23_rooms_solds_change,
       isnull(MAX(rt24_rooms_sold), 0.0) as rt24_rooms_sold,
       isnull(MAX(rt24_rooms_solds_change), 0.0) as rt24_rooms_solds_change,
       isnull(MAX(rt25_rooms_sold), 0.0) as rt25_rooms_sold,
       isnull(MAX(rt25_rooms_solds_change), 0.0) as rt25_rooms_solds_change,
       isnull(MAX(rt26_rooms_sold), 0.0) as rt26_rooms_sold,
       isnull(MAX(rt26_rooms_solds_change), 0.0) as rt26_rooms_solds_change,
       isnull(MAX(rt27_rooms_sold), 0.0) as rt27_rooms_sold,
       isnull(MAX(rt27_rooms_solds_change), 0.0) as rt27_rooms_solds_change,
       isnull(MAX(rt28_rooms_sold), 0.0) as rt28_rooms_sold,
       isnull(MAX(rt28_rooms_solds_change), 0.0) as rt28_rooms_solds_change,
       isnull(MAX(rt29_rooms_sold), 0.0) as rt29_rooms_sold,
       isnull(MAX(rt29_rooms_solds_change), 0.0) as rt29_rooms_solds_change,
       isnull(MAX(rt30_rooms_sold), 0.0) as rt30_rooms_sold,
       isnull(MAX(rt30_rooms_solds_change), 0.0) as rt30_rooms_solds_change,
       isnull(MAX(rt31_rooms_sold), 0.0) as rt31_rooms_sold,
       isnull(MAX(rt31_rooms_solds_change), 0.0) as rt31_rooms_solds_change,
       isnull(MAX(rt32_rooms_sold), 0.0) as rt32_rooms_sold,
       isnull(MAX(rt32_rooms_solds_change), 0.0) as rt32_rooms_solds_change,
       isnull(MAX(rt33_rooms_sold), 0.0) as rt33_rooms_sold,
       isnull(MAX(rt33_rooms_solds_change), 0.0) as rt33_rooms_solds_change,
       isnull(MAX(rt34_rooms_sold), 0.0) as rt34_rooms_sold,
       isnull(MAX(rt34_rooms_solds_change), 0.0) as rt34_rooms_solds_change,
       isnull(MAX(rt35_rooms_sold), 0.0) as rt35_rooms_sold,
       isnull(MAX(rt35_rooms_solds_change), 0.0) as rt35_rooms_solds_change,
       isnull(MAX(rt36_rooms_sold), 0.0) as rt36_rooms_sold,
       isnull(MAX(rt36_rooms_solds_change), 0.0) as rt36_rooms_solds_change,
       isnull(MAX(rt37_rooms_sold), 0.0) as rt37_rooms_sold,
       isnull(MAX(rt37_rooms_solds_change), 0.0) as rt37_rooms_solds_change,
       isnull(MAX(rt38_rooms_sold), 0.0) as rt38_rooms_sold,
       isnull(MAX(rt38_rooms_solds_change), 0.0) as rt38_rooms_solds_change,
       isnull(MAX(rt39_rooms_sold), 0.0) as rt39_rooms_sold,
       isnull(MAX(rt39_rooms_solds_change), 0.0) as rt39_rooms_solds_change,
       isnull(MAX(rt40_rooms_sold), 0.0) as rt40_rooms_sold,
       isnull(MAX(rt40_rooms_solds_change), 0.0) as rt40_rooms_solds_change,
       isnull(MAX(rt41_rooms_sold), 0.0) as rt41_rooms_sold,
       isnull(MAX(rt41_rooms_solds_change), 0.0) as rt41_rooms_solds_change,
       isnull(MAX(rt42_rooms_sold), 0.0) as rt42_rooms_sold,
       isnull(MAX(rt42_rooms_solds_change), 0.0) as rt42_rooms_solds_change,
       isnull(MAX(rt43_rooms_sold), 0.0) as rt43_rooms_sold,
       isnull(MAX(rt43_rooms_solds_change), 0.0) as rt43_rooms_solds_change,
       isnull(MAX(rt44_rooms_sold), 0.0) as rt44_rooms_sold,
       isnull(MAX(rt44_rooms_solds_change), 0.0) as rt44_rooms_solds_change,
       isnull(MAX(rt45_rooms_sold), 0.0) as rt45_rooms_sold,
       isnull(MAX(rt45_rooms_solds_change), 0.0) as rt45_rooms_solds_change,
       isnull(MAX(rt46_rooms_sold), 0.0) as rt46_rooms_sold,
       isnull(MAX(rt46_rooms_solds_change), 0.0) as rt46_rooms_solds_change,
       isnull(MAX(rt47_rooms_sold), 0.0) as rt47_rooms_sold,
       isnull(MAX(rt47_rooms_solds_change), 0.0) as rt47_rooms_solds_change,
       isnull(MAX(rt48_rooms_sold), 0.0) as rt48_rooms_sold,
       isnull(MAX(rt48_rooms_solds_change), 0.0) as rt48_rooms_solds_change,
       isnull(MAX(rt49_rooms_sold), 0.0) as rt49_rooms_sold,
       isnull(MAX(rt49_rooms_solds_change), 0.0) as rt49_rooms_solds_change,
       isnull(MAX(rt50_rooms_sold), 0.0) as rt50_rooms_sold,
       isnull(MAX(rt50_rooms_solds_change), 0.0) as rt50_rooms_solds_change,
       isnull(MAX(cast(rt1_occupancy_nbr as numeric(8, 2))), 0.0) as rt1_occupancy_nbr,
       isnull(MAX(cast(rt1_occupancy_change as numeric(8, 2))), 0.0) as rt1_occupancy_change,
       isnull(MAX(cast(rt2_occupancy_nbr as numeric(8, 2))), 0.0) as rt2_occupancy_nbr,
       isnull(MAX(cast(rt2_occupancy_change as numeric(8, 2))), 0.0) as rt2_occupancy_change,
       isnull(MAX(cast(rt3_occupancy_nbr as numeric(8, 2))), 0.0) as rt3_occupancy_nbr,
       isnull(MAX(cast(rt3_occupancy_change as numeric(8, 2))), 0.0) as rt3_occupancy_change,
       isnull(MAX(cast(rt4_occupancy_nbr as numeric(8, 2))), 0.0) as rt4_occupancy_nbr,
       isnull(MAX(cast(rt4_occupancy_change as numeric(8, 2))), 0.0) as rt4_occupancy_change,
       isnull(MAX(cast(rt5_occupancy_nbr as numeric(8, 2))), 0.0) as rt5_occupancy_nbr,
       isnull(MAX(cast(rt5_occupancy_change as numeric(8, 2))), 0.0) as rt5_occupancy_change,
       isnull(MAX(cast(rt6_occupancy_nbr as numeric(8, 2))), 0.0) as rt6_occupancy_nbr,
       isnull(MAX(cast(rt6_occupancy_change as numeric(8, 2))), 0.0) as rt6_occupancy_change,
       isnull(MAX(cast(rt7_occupancy_nbr as numeric(8, 2))), 0.0) as rt7_occupancy_nbr,
       isnull(MAX(cast(rt7_occupancy_change as numeric(8, 2))), 0.0) as rt7_occupancy_change,
       isnull(MAX(cast(rt8_occupancy_nbr as numeric(8, 2))), 0.0) as rt8_occupancy_nbr,
       isnull(MAX(cast(rt8_occupancy_change as numeric(8, 2))), 0.0) as rt8_occupancy_change,
       isnull(MAX(cast(rt9_occupancy_nbr as numeric(8, 2))), 0.0) as rt9_occupancy_nbr,
       isnull(MAX(cast(rt9_occupancy_change as numeric(8, 2))), 0.0) as rt9_occupancy_change,
       isnull(MAX(cast(rt10_occupancy_nbr as numeric(8, 2))), 0.0) as rt10_occupancy_nbr,
       isnull(MAX(cast(rt10_occupancy_change as numeric(8, 2))), 0.0) as rt10_occupancy_change,
       isnull(MAX(cast(rt11_occupancy_nbr as numeric(8, 2))), 0.0) as rt11_occupancy_nbr,
       isnull(MAX(cast(rt11_occupancy_change as numeric(8, 2))), 0.0) as rt11_occupancy_change,
       isnull(MAX(cast(rt12_occupancy_nbr as numeric(8, 2))), 0.0) as rt12_occupancy_nbr,
       isnull(MAX(cast(rt12_occupancy_change as numeric(8, 2))), 0.0) as rt12_occupancy_change,
       isnull(MAX(cast(rt13_occupancy_nbr as numeric(8, 2))), 0.0) as rt13_occupancy_nbr,
       isnull(MAX(cast(rt13_occupancy_change as numeric(8, 2))), 0.0) as rt13_occupancy_change,
       isnull(MAX(cast(rt14_occupancy_nbr as numeric(8, 2))), 0.0) as rt14_occupancy_nbr,
       isnull(MAX(cast(rt14_occupancy_change as numeric(8, 2))), 0.0) as rt14_occupancy_change,
       isnull(MAX(cast(rt15_occupancy_nbr as numeric(8, 2))), 0.0) as rt15_occupancy_nbr,
       isnull(MAX(cast(rt15_occupancy_change as numeric(8, 2))), 0.0) as rt15_occupancy_change,
       isnull(MAX(cast(rt16_occupancy_nbr as numeric(8, 2))), 0.0) as rt16_occupancy_nbr,
       isnull(MAX(cast(rt16_occupancy_change as numeric(8, 2))), 0.0) as rt16_occupancy_change,
       isnull(MAX(cast(rt17_occupancy_nbr as numeric(8, 2))), 0.0) as rt17_occupancy_nbr,
       isnull(MAX(cast(rt17_occupancy_change as numeric(8, 2))), 0.0) as rt17_occupancy_change,
       isnull(MAX(cast(rt18_occupancy_nbr as numeric(8, 2))), 0.0) as rt18_occupancy_nbr,
       isnull(MAX(cast(rt18_occupancy_change as numeric(8, 2))), 0.0) as rt18_occupancy_change,
       isnull(MAX(cast(rt19_occupancy_nbr as numeric(8, 2))), 0.0) as rt19_occupancy_nbr,
       isnull(MAX(cast(rt19_occupancy_change as numeric(8, 2))), 0.0) as rt19_occupancy_change,
       isnull(MAX(cast(rt20_occupancy_nbr as numeric(8, 2))), 0.0) as rt20_occupancy_nbr,
       isnull(MAX(cast(rt20_occupancy_change as numeric(8, 2))), 0.0) as rt20_occupancy_change,
       isnull(MAX(cast(rt21_occupancy_nbr as numeric(8, 2))), 0.0) as rt21_occupancy_nbr,
       isnull(MAX(cast(rt21_occupancy_change as numeric(8, 2))), 0.0) as rt21_occupancy_change,
       isnull(MAX(cast(rt22_occupancy_nbr as numeric(8, 2))), 0.0) as rt22_occupancy_nbr,
       isnull(MAX(cast(rt22_occupancy_change as numeric(8, 2))), 0.0) as rt22_occupancy_change,
       isnull(MAX(cast(rt23_occupancy_nbr as numeric(8, 2))), 0.0) as rt23_occupancy_nbr,
       isnull(MAX(cast(rt23_occupancy_change as numeric(8, 2))), 0.0) as rt23_occupancy_change,
       isnull(MAX(cast(rt24_occupancy_nbr as numeric(8, 2))), 0.0) as rt24_occupancy_nbr,
       isnull(MAX(cast(rt24_occupancy_change as numeric(8, 2))), 0.0) as rt24_occupancy_change,
       isnull(MAX(cast(rt25_occupancy_nbr as numeric(8, 2))), 0.0) as rt25_occupancy_nbr,
       isnull(MAX(cast(rt25_occupancy_change as numeric(8, 2))), 0.0) as rt25_occupancy_change,
       isnull(MAX(cast(rt26_occupancy_nbr as numeric(8, 2))), 0.0) as rt26_occupancy_nbr,
       isnull(MAX(cast(rt26_occupancy_change as numeric(8, 2))), 0.0) as rt26_occupancy_change,
       isnull(MAX(cast(rt27_occupancy_nbr as numeric(8, 2))), 0.0) as rt27_occupancy_nbr,
       isnull(MAX(cast(rt27_occupancy_change as numeric(8, 2))), 0.0) as rt27_occupancy_change,
       isnull(MAX(cast(rt28_occupancy_nbr as numeric(8, 2))), 0.0) as rt28_occupancy_nbr,
       isnull(MAX(cast(rt28_occupancy_change as numeric(8, 2))), 0.0) as rt28_occupancy_change,
       isnull(MAX(cast(rt29_occupancy_nbr as numeric(8, 2))), 0.0) as rt29_occupancy_nbr,
       isnull(MAX(cast(rt29_occupancy_change as numeric(8, 2))), 0.0) as rt29_occupancy_change,
       isnull(MAX(cast(rt30_occupancy_nbr as numeric(8, 2))), 0.0) as rt30_occupancy_nbr,
       isnull(MAX(cast(rt30_occupancy_change as numeric(8, 2))), 0.0) as rt30_occupancy_change,
       isnull(MAX(cast(rt31_occupancy_nbr as numeric(8, 2))), 0.0) as rt31_occupancy_nbr,
       isnull(MAX(cast(rt31_occupancy_change as numeric(8, 2))), 0.0) as rt31_occupancy_change,
       isnull(MAX(cast(rt32_occupancy_nbr as numeric(8, 2))), 0.0) as rt32_occupancy_nbr,
       isnull(MAX(cast(rt32_occupancy_change as numeric(8, 2))), 0.0) as rt32_occupancy_change,
       isnull(MAX(cast(rt33_occupancy_nbr as numeric(8, 2))), 0.0) as rt33_occupancy_nbr,
       isnull(MAX(cast(rt33_occupancy_change as numeric(8, 2))), 0.0) as rt33_occupancy_change,
       isnull(MAX(cast(rt34_occupancy_nbr as numeric(8, 2))), 0.0) as rt34_occupancy_nbr,
       isnull(MAX(cast(rt34_occupancy_change as numeric(8, 2))), 0.0) as rt34_occupancy_change,
       isnull(MAX(cast(rt35_occupancy_nbr as numeric(8, 2))), 0.0) as rt35_occupancy_nbr,
       isnull(MAX(cast(rt35_occupancy_change as numeric(8, 2))), 0.0) as rt35_occupancy_change,
       isnull(MAX(cast(rt36_occupancy_nbr as numeric(8, 2))), 0.0) as rt36_occupancy_nbr,
       isnull(MAX(cast(rt36_occupancy_change as numeric(8, 2))), 0.0) as rt36_occupancy_change,
       isnull(MAX(cast(rt37_occupancy_nbr as numeric(8, 2))), 0.0) as rt37_occupancy_nbr,
       isnull(MAX(cast(rt37_occupancy_change as numeric(8, 2))), 0.0) as rt37_occupancy_change,
       isnull(MAX(cast(rt38_occupancy_nbr as numeric(8, 2))), 0.0) as rt38_occupancy_nbr,
       isnull(MAX(cast(rt38_occupancy_change as numeric(8, 2))), 0.0) as rt38_occupancy_change,
       isnull(MAX(cast(rt39_occupancy_nbr as numeric(8, 2))), 0.0) as rt39_occupancy_nbr,
       isnull(MAX(cast(rt39_occupancy_change as numeric(8, 2))), 0.0) as rt39_occupancy_change,
       isnull(MAX(cast(rt40_occupancy_nbr as numeric(8, 2))), 0.0) as rt40_occupancy_nbr,
       isnull(MAX(cast(rt40_occupancy_change as numeric(8, 2))), 0.0) as rt40_occupancy_change,
       isnull(MAX(cast(rt41_occupancy_nbr as numeric(8, 2))), 0.0) as rt41_occupancy_nbr,
       isnull(MAX(cast(rt41_occupancy_change as numeric(8, 2))), 0.0) as rt41_occupancy_change,
       isnull(MAX(cast(rt42_occupancy_nbr as numeric(8, 2))), 0.0) as rt42_occupancy_nbr,
       isnull(MAX(cast(rt42_occupancy_change as numeric(8, 2))), 0.0) as rt42_occupancy_change,
       isnull(MAX(cast(rt43_occupancy_nbr as numeric(8, 2))), 0.0) as rt43_occupancy_nbr,
       isnull(MAX(cast(rt43_occupancy_change as numeric(8, 2))), 0.0) as rt43_occupancy_change,
       isnull(MAX(cast(rt44_occupancy_nbr as numeric(8, 2))), 0.0) as rt44_occupancy_nbr,
       isnull(MAX(cast(rt44_occupancy_change as numeric(8, 2))), 0.0) as rt44_occupancy_change,
       isnull(MAX(cast(rt45_occupancy_nbr as numeric(8, 2))), 0.0) as rt45_occupancy_nbr,
       isnull(MAX(cast(rt45_occupancy_change as numeric(8, 2))), 0.0) as rt45_occupancy_change,
       isnull(MAX(cast(rt46_occupancy_nbr as numeric(8, 2))), 0.0) as rt46_occupancy_nbr,
       isnull(MAX(cast(rt46_occupancy_change as numeric(8, 2))), 0.0) as rt46_occupancy_change,
       isnull(MAX(cast(rt47_occupancy_nbr as numeric(8, 2))), 0.0) as rt47_occupancy_nbr,
       isnull(MAX(cast(rt47_occupancy_change as numeric(8, 2))), 0.0) as rt47_occupancy_change,
       isnull(MAX(cast(rt48_occupancy_nbr as numeric(8, 2))), 0.0) as rt48_occupancy_nbr,
       isnull(MAX(cast(rt48_occupancy_change as numeric(8, 2))), 0.0) as rt48_occupancy_change,
       isnull(MAX(cast(rt49_occupancy_nbr as numeric(8, 2))), 0.0) as rt49_occupancy_nbr,
       isnull(MAX(cast(rt49_occupancy_change as numeric(8, 2))), 0.0) as rt49_occupancy_change,
       isnull(MAX(cast(rt50_occupancy_nbr as numeric(8, 2))), 0.0) as rt50_occupancy_nbr,
       isnull(MAX(cast(rt50_occupancy_change as numeric(8, 2))), 0.0) as rt50_occupancy_change,
       isnull(MAX(cast(rt1_occupancy_percent as numeric(8, 2))), 0.0) as rt1_occupancy_percent,
       isnull(MAX(cast(rt1_occupancy_perc_change as numeric(8, 2))), 0.0) as rt1_occupancy_perc_change,
       isnull(MAX(cast(rt2_occupancy_percent as numeric(8, 2))), 0.0) as rt2_occupancy_percent,
       isnull(MAX(cast(rt2_occupancy_perc_change as numeric(8, 2))), 0.0) as rt2_occupancy_perc_change,
       isnull(MAX(cast(rt3_occupancy_percent as numeric(8, 2))), 0.0) as rt3_occupancy_percent,
       isnull(MAX(cast(rt3_occupancy_perc_change as numeric(8, 2))), 0.0) as rt3_occupancy_perc_change,
       isnull(MAX(cast(rt4_occupancy_percent as numeric(8, 2))), 0.0) as rt4_occupancy_percent,
       isnull(MAX(cast(rt4_occupancy_perc_change as numeric(8, 2))), 0.0) as rt4_occupancy_perc_change,
       isnull(MAX(cast(rt5_occupancy_percent as numeric(8, 2))), 0.0) as rt5_occupancy_percent,
       isnull(MAX(cast(rt5_occupancy_perc_change as numeric(8, 2))), 0.0) as rt5_occupancy_perc_change,
       isnull(MAX(cast(rt6_occupancy_percent as numeric(8, 2))), 0.0) as rt6_occupancy_percent,
       isnull(MAX(cast(rt6_occupancy_perc_change as numeric(8, 2))), 0.0) as rt6_occupancy_perc_change,
       isnull(MAX(cast(rt7_occupancy_percent as numeric(8, 2))), 0.0) as rt7_occupancy_percent,
       isnull(MAX(cast(rt7_occupancy_perc_change as numeric(8, 2))), 0.0) as rt7_occupancy_perc_change,
       isnull(MAX(cast(rt8_occupancy_percent as numeric(8, 2))), 0.0) as rt8_occupancy_percent,
       isnull(MAX(cast(rt8_occupancy_perc_change as numeric(8, 2))), 0.0) as rt8_occupancy_perc_change,
       isnull(MAX(cast(rt9_occupancy_percent as numeric(8, 2))), 0.0) as rt9_occupancy_percent,
       isnull(MAX(cast(rt9_occupancy_perc_change as numeric(8, 2))), 0.0) as rt9_occupancy_perc_change,
       isnull(MAX(cast(rt10_occupancy_percent as numeric(8, 2))), 0.0) as rt10_occupancy_percent,
       isnull(MAX(cast(rt10_occupancy_perc_change as numeric(8, 2))), 0.0) as rt10_occupancy_perc_change,
       isnull(MAX(cast(rt11_occupancy_percent as numeric(8, 2))), 0.0) as rt11_occupancy_percent,
       isnull(MAX(cast(rt11_occupancy_perc_change as numeric(8, 2))), 0.0) as rt11_occupancy_perc_change,
       isnull(MAX(cast(rt12_occupancy_percent as numeric(8, 2))), 0.0) as rt12_occupancy_percent,
       isnull(MAX(cast(rt12_occupancy_perc_change as numeric(8, 2))), 0.0) as rt12_occupancy_perc_change,
       isnull(MAX(cast(rt13_occupancy_percent as numeric(8, 2))), 0.0) as rt13_occupancy_percent,
       isnull(MAX(cast(rt13_occupancy_perc_change as numeric(8, 2))), 0.0) as rt13_occupancy_perc_change,
       isnull(MAX(cast(rt14_occupancy_percent as numeric(8, 2))), 0.0) as rt14_occupancy_percent,
       isnull(MAX(cast(rt14_occupancy_perc_change as numeric(8, 2))), 0.0) as rt14_occupancy_perc_change,
       isnull(MAX(cast(rt15_occupancy_percent as numeric(8, 2))), 0.0) as rt15_occupancy_percent,
       isnull(MAX(cast(rt15_occupancy_perc_change as numeric(8, 2))), 0.0) as rt15_occupancy_perc_change,
       isnull(MAX(cast(rt16_occupancy_percent as numeric(8, 2))), 0.0) as rt16_occupancy_percent,
       isnull(MAX(cast(rt16_occupancy_perc_change as numeric(8, 2))), 0.0) as rt16_occupancy_perc_change,
       isnull(MAX(cast(rt17_occupancy_percent as numeric(8, 2))), 0.0) as rt17_occupancy_percent,
       isnull(MAX(cast(rt17_occupancy_perc_change as numeric(8, 2))), 0.0) as rt17_occupancy_perc_change,
       isnull(MAX(cast(rt18_occupancy_percent as numeric(8, 2))), 0.0) as rt18_occupancy_percent,
       isnull(MAX(cast(rt18_occupancy_perc_change as numeric(8, 2))), 0.0) as rt18_occupancy_perc_change,
       isnull(MAX(cast(rt19_occupancy_percent as numeric(8, 2))), 0.0) as rt19_occupancy_percent,
       isnull(MAX(cast(rt19_occupancy_perc_change as numeric(8, 2))), 0.0) as rt19_occupancy_perc_change,
       isnull(MAX(cast(rt20_occupancy_percent as numeric(8, 2))), 0.0) as rt20_occupancy_percent,
       isnull(MAX(cast(rt20_occupancy_perc_change as numeric(8, 2))), 0.0) as rt20_occupancy_perc_change,
       isnull(MAX(cast(rt21_occupancy_percent as numeric(8, 2))), 0.0) as rt21_occupancy_percent,
       isnull(MAX(cast(rt21_occupancy_perc_change as numeric(8, 2))), 0.0) as rt21_occupancy_perc_change,
       isnull(MAX(cast(rt22_occupancy_percent as numeric(8, 2))), 0.0) as rt22_occupancy_percent,
       isnull(MAX(cast(rt22_occupancy_perc_change as numeric(8, 2))), 0.0) as rt22_occupancy_perc_change,
       isnull(MAX(cast(rt23_occupancy_percent as numeric(8, 2))), 0.0) as rt23_occupancy_percent,
       isnull(MAX(cast(rt23_occupancy_perc_change as numeric(8, 2))), 0.0) as rt23_occupancy_perc_change,
       isnull(MAX(cast(rt24_occupancy_percent as numeric(8, 2))), 0.0) as rt24_occupancy_percent,
       isnull(MAX(cast(rt24_occupancy_perc_change as numeric(8, 2))), 0.0) as rt24_occupancy_perc_change,
       isnull(MAX(cast(rt25_occupancy_percent as numeric(8, 2))), 0.0) as rt25_occupancy_percent,
       isnull(MAX(cast(rt25_occupancy_perc_change as numeric(8, 2))), 0.0) as rt25_occupancy_perc_change,
       isnull(MAX(cast(rt26_occupancy_percent as numeric(8, 2))), 0.0) as rt26_occupancy_percent,
       isnull(MAX(cast(rt26_occupancy_perc_change as numeric(8, 2))), 0.0) as rt26_occupancy_perc_change,
       isnull(MAX(cast(rt27_occupancy_percent as numeric(8, 2))), 0.0) as rt27_occupancy_percent,
       isnull(MAX(cast(rt27_occupancy_perc_change as numeric(8, 2))), 0.0) as rt27_occupancy_perc_change,
       isnull(MAX(cast(rt28_occupancy_percent as numeric(8, 2))), 0.0) as rt28_occupancy_percent,
       isnull(MAX(cast(rt28_occupancy_perc_change as numeric(8, 2))), 0.0) as rt28_occupancy_perc_change,
       isnull(MAX(cast(rt29_occupancy_percent as numeric(8, 2))), 0.0) as rt29_occupancy_percent,
       isnull(MAX(cast(rt29_occupancy_perc_change as numeric(8, 2))), 0.0) as rt29_occupancy_perc_change,
       isnull(MAX(cast(rt30_occupancy_percent as numeric(8, 2))), 0.0) as rt30_occupancy_percent,
       isnull(MAX(cast(rt30_occupancy_perc_change as numeric(8, 2))), 0.0) as rt30_occupancy_perc_change,
       isnull(MAX(cast(rt31_occupancy_percent as numeric(8, 2))), 0.0) as rt31_occupancy_percent,
       isnull(MAX(cast(rt31_occupancy_perc_change as numeric(8, 2))), 0.0) as rt31_occupancy_perc_change,
       isnull(MAX(cast(rt32_occupancy_percent as numeric(8, 2))), 0.0) as rt32_occupancy_percent,
       isnull(MAX(cast(rt32_occupancy_perc_change as numeric(8, 2))), 0.0) as rt32_occupancy_perc_change,
       isnull(MAX(cast(rt33_occupancy_percent as numeric(8, 2))), 0.0) as rt33_occupancy_percent,
       isnull(MAX(cast(rt33_occupancy_perc_change as numeric(8, 2))), 0.0) as rt33_occupancy_perc_change,
       isnull(MAX(cast(rt34_occupancy_percent as numeric(8, 2))), 0.0) as rt34_occupancy_percent,
       isnull(MAX(cast(rt34_occupancy_perc_change as numeric(8, 2))), 0.0) as rt34_occupancy_perc_change,
       isnull(MAX(cast(rt35_occupancy_percent as numeric(8, 2))), 0.0) as rt35_occupancy_percent,
       isnull(MAX(cast(rt35_occupancy_perc_change as numeric(8, 2))), 0.0) as rt35_occupancy_perc_change,
       isnull(MAX(cast(rt36_occupancy_percent as numeric(8, 2))), 0.0) as rt36_occupancy_percent,
       isnull(MAX(cast(rt36_occupancy_perc_change as numeric(8, 2))), 0.0) as rt36_occupancy_perc_change,
       isnull(MAX(cast(rt37_occupancy_percent as numeric(8, 2))), 0.0) as rt37_occupancy_percent,
       isnull(MAX(cast(rt37_occupancy_perc_change as numeric(8, 2))), 0.0) as rt37_occupancy_perc_change,
       isnull(MAX(cast(rt38_occupancy_percent as numeric(8, 2))), 0.0) as rt38_occupancy_percent,
       isnull(MAX(cast(rt38_occupancy_perc_change as numeric(8, 2))), 0.0) as rt38_occupancy_perc_change,
       isnull(MAX(cast(rt39_occupancy_percent as numeric(8, 2))), 0.0) as rt39_occupancy_percent,
       isnull(MAX(cast(rt39_occupancy_perc_change as numeric(8, 2))), 0.0) as rt39_occupancy_perc_change,
       isnull(MAX(cast(rt40_occupancy_percent as numeric(8, 2))), 0.0) as rt40_occupancy_percent,
       isnull(MAX(cast(rt40_occupancy_perc_change as numeric(8, 2))), 0.0) as rt40_occupancy_perc_change,
       isnull(MAX(cast(rt41_occupancy_percent as numeric(8, 2))), 0.0) as rt41_occupancy_percent,
       isnull(MAX(cast(rt41_occupancy_perc_change as numeric(8, 2))), 0.0) as rt41_occupancy_perc_change,
       isnull(MAX(cast(rt42_occupancy_percent as numeric(8, 2))), 0.0) as rt42_occupancy_percent,
       isnull(MAX(cast(rt42_occupancy_perc_change as numeric(8, 2))), 0.0) as rt42_occupancy_perc_change,
       isnull(MAX(cast(rt43_occupancy_percent as numeric(8, 2))), 0.0) as rt43_occupancy_percent,
       isnull(MAX(cast(rt43_occupancy_perc_change as numeric(8, 2))), 0.0) as rt43_occupancy_perc_change,
       isnull(MAX(cast(rt44_occupancy_percent as numeric(8, 2))), 0.0) as rt44_occupancy_percent,
       isnull(MAX(cast(rt44_occupancy_perc_change as numeric(8, 2))), 0.0) as rt44_occupancy_perc_change,
       isnull(MAX(cast(rt45_occupancy_percent as numeric(8, 2))), 0.0) as rt45_occupancy_percent,
       isnull(MAX(cast(rt45_occupancy_perc_change as numeric(8, 2))), 0.0) as rt45_occupancy_perc_change,
       isnull(MAX(cast(rt46_occupancy_percent as numeric(8, 2))), 0.0) as rt46_occupancy_percent,
       isnull(MAX(cast(rt46_occupancy_perc_change as numeric(8, 2))), 0.0) as rt46_occupancy_perc_change,
       isnull(MAX(cast(rt47_occupancy_percent as numeric(8, 2))), 0.0) as rt47_occupancy_percent,
       isnull(MAX(cast(rt47_occupancy_perc_change as numeric(8, 2))), 0.0) as rt47_occupancy_perc_change,
       isnull(MAX(cast(rt48_occupancy_percent as numeric(8, 2))), 0.0) as rt48_occupancy_percent,
       isnull(MAX(cast(rt48_occupancy_perc_change as numeric(8, 2))), 0.0) as rt48_occupancy_perc_change,
       isnull(MAX(cast(rt49_occupancy_percent as numeric(8, 2))), 0.0) as rt49_occupancy_percent,
       isnull(MAX(cast(rt49_occupancy_perc_change as numeric(8, 2))), 0.0) as rt49_occupancy_perc_change,
       isnull(MAX(cast(rt50_occupancy_percent as numeric(8, 2))), 0.0) as rt50_occupancy_percent,
       isnull(MAX(cast(rt50_occupancy_perc_change as numeric(8, 2))), 0.0) as rt50_occupancy_perc_change,
       isnull(MAX(cast(rt1_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt1_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt1_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt1_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt2_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt2_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt2_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt2_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt3_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt3_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt3_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt3_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt4_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt4_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt4_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt4_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt5_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt5_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt5_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt5_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt6_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt6_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt6_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt6_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt7_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt7_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt7_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt7_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt8_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt8_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt8_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt8_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt9_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt9_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt9_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt9_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt10_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt10_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt10_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt10_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt11_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt11_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt11_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt11_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt12_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt12_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt12_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt12_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt13_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt13_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt13_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt13_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt14_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt14_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt14_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt14_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt15_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt15_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt15_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt15_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt16_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt16_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt16_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt16_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt17_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt17_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt17_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt17_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt18_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt18_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt18_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt18_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt19_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt19_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt19_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt19_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt20_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt20_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt20_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt20_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt21_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt21_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt21_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt21_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt22_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt22_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt22_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt22_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt23_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt23_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt23_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt23_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt24_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt24_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt24_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt24_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt25_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt25_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt25_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt25_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt26_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt26_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt26_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt26_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt27_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt27_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt27_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt27_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt28_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt28_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt28_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt28_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt29_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt29_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt29_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt29_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt30_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt30_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt30_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt30_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt31_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt31_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt31_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt31_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt32_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt32_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt32_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt32_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt33_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt33_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt33_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt33_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt34_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt34_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt34_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt34_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt35_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt35_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt35_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt35_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt36_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt36_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt36_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt36_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt37_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt37_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt37_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt37_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt38_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt38_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt38_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt38_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt39_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt39_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt39_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt39_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt40_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt40_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt40_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt40_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt41_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt41_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt41_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt41_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt42_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt42_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt42_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt42_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt43_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt43_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt43_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt43_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt44_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt44_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt44_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt44_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt45_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt45_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt45_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt45_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt46_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt46_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt46_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt46_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt47_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt47_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt47_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt47_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt48_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt48_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt48_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt48_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt49_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt49_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt49_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt49_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt50_occupancy_percent_without_ooo as numeric(8, 2))), 0.0) as rt50_occupancy_percent_without_ooo,
       isnull(MAX(cast(rt50_occupancy_perc_change_without_ooo as numeric(8, 2))), 0.0) as rt50_occupancy_perc_change_without_ooo,
       isnull(MAX(cast(rt1_booked_revenue as numeric(19, 5))), 0.0) as rt1_booked_revenue,
       isnull(MAX(cast(rt1_booked_revenue_change as numeric(19, 5))), 0.0) as rt1_booked_revenue_change,
       isnull(MAX(cast(rt2_booked_revenue as numeric(19, 5))), 0.0) as rt2_booked_revenue,
       isnull(MAX(cast(rt2_booked_revenue_change as numeric(19, 5))), 0.0) as rt2_booked_revenue_change,
       isnull(MAX(cast(rt3_booked_revenue as numeric(19, 5))), 0.0) as rt3_booked_revenue,
       isnull(MAX(cast(rt3_booked_revenue_change as numeric(19, 5))), 0.0) as rt3_booked_revenue_change,
       isnull(MAX(cast(rt4_booked_revenue as numeric(19, 5))), 0.0) as rt4_booked_revenue,
       isnull(MAX(cast(rt4_booked_revenue_change as numeric(19, 5))), 0.0) as rt4_booked_revenue_change,
       isnull(MAX(cast(rt5_booked_revenue as numeric(19, 5))), 0.0) as rt5_booked_revenue,
       isnull(MAX(cast(rt5_booked_revenue_change as numeric(19, 5))), 0.0) as rt5_booked_revenue_change,
       isnull(MAX(cast(rt6_booked_revenue as numeric(19, 5))), 0.0) as rt6_booked_revenue,
       isnull(MAX(cast(rt6_booked_revenue_change as numeric(19, 5))), 0.0) as rt6_booked_revenue_change,
       isnull(MAX(cast(rt7_booked_revenue as numeric(19, 5))), 0.0) as rt7_booked_revenue,
       isnull(MAX(cast(rt7_booked_revenue_change as numeric(19, 5))), 0.0) as rt7_booked_revenue_change,
       isnull(MAX(cast(rt8_booked_revenue as numeric(19, 5))), 0.0) as rt8_booked_revenue,
       isnull(MAX(cast(rt8_booked_revenue_change as numeric(19, 5))), 0.0) as rt8_booked_revenue_change,
       isnull(MAX(cast(rt9_booked_revenue as numeric(19, 5))), 0.0) as rt9_booked_revenue,
       isnull(MAX(cast(rt9_booked_revenue_change as numeric(19, 5))), 0.0) as rt9_booked_revenue_change,
       isnull(MAX(cast(rt10_booked_revenue as numeric(19, 5))), 0.0) as rt10_booked_revenue,
       isnull(MAX(cast(rt10_booked_revenue_change as numeric(19, 5))), 0.0) as rt10_booked_revenue_change,
       isnull(MAX(cast(rt11_booked_revenue as numeric(19, 5))), 0.0) as rt11_booked_revenue,
       isnull(MAX(cast(rt11_booked_revenue_change as numeric(19, 5))), 0.0) as rt11_booked_revenue_change,
       isnull(MAX(cast(rt12_booked_revenue as numeric(19, 5))), 0.0) as rt12_booked_revenue,
       isnull(MAX(cast(rt12_booked_revenue_change as numeric(19, 5))), 0.0) as rt12_booked_revenue_change,
       isnull(MAX(cast(rt13_booked_revenue as numeric(19, 5))), 0.0) as rt13_booked_revenue,
       isnull(MAX(cast(rt13_booked_revenue_change as numeric(19, 5))), 0.0) as rt13_booked_revenue_change,
       isnull(MAX(cast(rt14_booked_revenue as numeric(19, 5))), 0.0) as rt14_booked_revenue,
       isnull(MAX(cast(rt14_booked_revenue_change as numeric(19, 5))), 0.0) as rt14_booked_revenue_change,
       isnull(MAX(cast(rt15_booked_revenue as numeric(19, 5))), 0.0) as rt15_booked_revenue,
       isnull(MAX(cast(rt15_booked_revenue_change as numeric(19, 5))), 0.0) as rt15_booked_revenue_change,
       isnull(MAX(cast(rt16_booked_revenue as numeric(19, 5))), 0.0) as rt16_booked_revenue,
       isnull(MAX(cast(rt16_booked_revenue_change as numeric(19, 5))), 0.0) as rt16_booked_revenue_change,
       isnull(MAX(cast(rt17_booked_revenue as numeric(19, 5))), 0.0) as rt17_booked_revenue,
       isnull(MAX(cast(rt17_booked_revenue_change as numeric(19, 5))), 0.0) as rt17_booked_revenue_change,
       isnull(MAX(cast(rt18_booked_revenue as numeric(19, 5))), 0.0) as rt18_booked_revenue,
       isnull(MAX(cast(rt18_booked_revenue_change as numeric(19, 5))), 0.0) as rt18_booked_revenue_change,
       isnull(MAX(cast(rt19_booked_revenue as numeric(19, 5))), 0.0) as rt19_booked_revenue,
       isnull(MAX(cast(rt19_booked_revenue_change as numeric(19, 5))), 0.0) as rt19_booked_revenue_change,
       isnull(MAX(cast(rt20_booked_revenue as numeric(19, 5))), 0.0) as rt20_booked_revenue,
       isnull(MAX(cast(rt20_booked_revenue_change as numeric(19, 5))), 0.0) as rt20_booked_revenue_change,
       isnull(MAX(cast(rt21_booked_revenue as numeric(19, 5))), 0.0) as rt21_booked_revenue,
       isnull(MAX(cast(rt21_booked_revenue_change as numeric(19, 5))), 0.0) as rt21_booked_revenue_change,
       isnull(MAX(cast(rt22_booked_revenue as numeric(19, 5))), 0.0) as rt22_booked_revenue,
       isnull(MAX(cast(rt22_booked_revenue_change as numeric(19, 5))), 0.0) as rt22_booked_revenue_change,
       isnull(MAX(cast(rt23_booked_revenue as numeric(19, 5))), 0.0) as rt23_booked_revenue,
       isnull(MAX(cast(rt23_booked_revenue_change as numeric(19, 5))), 0.0) as rt23_booked_revenue_change,
       isnull(MAX(cast(rt24_booked_revenue as numeric(19, 5))), 0.0) as rt24_booked_revenue,
       isnull(MAX(cast(rt24_booked_revenue_change as numeric(19, 5))), 0.0) as rt24_booked_revenue_change,
       isnull(MAX(cast(rt25_booked_revenue as numeric(19, 5))), 0.0) as rt25_booked_revenue,
       isnull(MAX(cast(rt25_booked_revenue_change as numeric(19, 5))), 0.0) as rt25_booked_revenue_change,
       isnull(MAX(cast(rt26_booked_revenue as numeric(19, 5))), 0.0) as rt26_booked_revenue,
       isnull(MAX(cast(rt26_booked_revenue_change as numeric(19, 5))), 0.0) as rt26_booked_revenue_change,
       isnull(MAX(cast(rt27_booked_revenue as numeric(19, 5))), 0.0) as rt27_booked_revenue,
       isnull(MAX(cast(rt27_booked_revenue_change as numeric(19, 5))), 0.0) as rt27_booked_revenue_change,
       isnull(MAX(cast(rt28_booked_revenue as numeric(19, 5))), 0.0) as rt28_booked_revenue,
       isnull(MAX(cast(rt28_booked_revenue_change as numeric(19, 5))), 0.0) as rt28_booked_revenue_change,
       isnull(MAX(cast(rt29_booked_revenue as numeric(19, 5))), 0.0) as rt29_booked_revenue,
       isnull(MAX(cast(rt29_booked_revenue_change as numeric(19, 5))), 0.0) as rt29_booked_revenue_change,
       isnull(MAX(cast(rt30_booked_revenue as numeric(19, 5))), 0.0) as rt30_booked_revenue,
       isnull(MAX(cast(rt30_booked_revenue_change as numeric(19, 5))), 0.0) as rt30_booked_revenue_change,
       isnull(MAX(cast(rt31_booked_revenue as numeric(19, 5))), 0.0) as rt31_booked_revenue,
       isnull(MAX(cast(rt31_booked_revenue_change as numeric(19, 5))), 0.0) as rt31_booked_revenue_change,
       isnull(MAX(cast(rt32_booked_revenue as numeric(19, 5))), 0.0) as rt32_booked_revenue,
       isnull(MAX(cast(rt32_booked_revenue_change as numeric(19, 5))), 0.0) as rt32_booked_revenue_change,
       isnull(MAX(cast(rt33_booked_revenue as numeric(19, 5))), 0.0) as rt33_booked_revenue,
       isnull(MAX(cast(rt33_booked_revenue_change as numeric(19, 5))), 0.0) as rt33_booked_revenue_change,
       isnull(MAX(cast(rt34_booked_revenue as numeric(19, 5))), 0.0) as rt34_booked_revenue,
       isnull(MAX(cast(rt34_booked_revenue_change as numeric(19, 5))), 0.0) as rt34_booked_revenue_change,
       isnull(MAX(cast(rt35_booked_revenue as numeric(19, 5))), 0.0) as rt35_booked_revenue,
       isnull(MAX(cast(rt35_booked_revenue_change as numeric(19, 5))), 0.0) as rt35_booked_revenue_change,
       isnull(MAX(cast(rt36_booked_revenue as numeric(19, 5))), 0.0) as rt36_booked_revenue,
       isnull(MAX(cast(rt36_booked_revenue_change as numeric(19, 5))), 0.0) as rt36_booked_revenue_change,
       isnull(MAX(cast(rt37_booked_revenue as numeric(19, 5))), 0.0) as rt37_booked_revenue,
       isnull(MAX(cast(rt37_booked_revenue_change as numeric(19, 5))), 0.0) as rt37_booked_revenue_change,
       isnull(MAX(cast(rt38_booked_revenue as numeric(19, 5))), 0.0) as rt38_booked_revenue,
       isnull(MAX(cast(rt38_booked_revenue_change as numeric(19, 5))), 0.0) as rt38_booked_revenue_change,
       isnull(MAX(cast(rt39_booked_revenue as numeric(19, 5))), 0.0) as rt39_booked_revenue,
       isnull(MAX(cast(rt39_booked_revenue_change as numeric(19, 5))), 0.0) as rt39_booked_revenue_change,
       isnull(MAX(cast(rt40_booked_revenue as numeric(19, 5))), 0.0) as rt40_booked_revenue,
       isnull(MAX(cast(rt40_booked_revenue_change as numeric(19, 5))), 0.0) as rt40_booked_revenue_change,
       isnull(MAX(cast(rt41_booked_revenue as numeric(19, 5))), 0.0) as rt41_booked_revenue,
       isnull(MAX(cast(rt41_booked_revenue_change as numeric(19, 5))), 0.0) as rt41_booked_revenue_change,
       isnull(MAX(cast(rt42_booked_revenue as numeric(19, 5))), 0.0) as rt42_booked_revenue,
       isnull(MAX(cast(rt42_booked_revenue_change as numeric(19, 5))), 0.0) as rt42_booked_revenue_change,
       isnull(MAX(cast(rt43_booked_revenue as numeric(19, 5))), 0.0) as rt43_booked_revenue,
       isnull(MAX(cast(rt43_booked_revenue_change as numeric(19, 5))), 0.0) as rt43_booked_revenue_change,
       isnull(MAX(cast(rt44_booked_revenue as numeric(19, 5))), 0.0) as rt44_booked_revenue,
       isnull(MAX(cast(rt44_booked_revenue_change as numeric(19, 5))), 0.0) as rt44_booked_revenue_change,
       isnull(MAX(cast(rt45_booked_revenue as numeric(19, 5))), 0.0) as rt45_booked_revenue,
       isnull(MAX(cast(rt45_booked_revenue_change as numeric(19, 5))), 0.0) as rt45_booked_revenue_change,
       isnull(MAX(cast(rt46_booked_revenue as numeric(19, 5))), 0.0) as rt46_booked_revenue,
       isnull(MAX(cast(rt46_booked_revenue_change as numeric(19, 5))), 0.0) as rt46_booked_revenue_change,
       isnull(MAX(cast(rt47_booked_revenue as numeric(19, 5))), 0.0) as rt47_booked_revenue,
       isnull(MAX(cast(rt47_booked_revenue_change as numeric(19, 5))), 0.0) as rt47_booked_revenue_change,
       isnull(MAX(cast(rt48_booked_revenue as numeric(19, 5))), 0.0) as rt48_booked_revenue,
       isnull(MAX(cast(rt48_booked_revenue_change as numeric(19, 5))), 0.0) as rt48_booked_revenue_change,
       isnull(MAX(cast(rt49_booked_revenue as numeric(19, 5))), 0.0) as rt49_booked_revenue,
       isnull(MAX(cast(rt49_booked_revenue_change as numeric(19, 5))), 0.0) as rt49_booked_revenue_change,
       isnull(MAX(cast(rt50_booked_revenue as numeric(19, 5))), 0.0) as rt50_booked_revenue,
       isnull(MAX(cast(rt50_booked_revenue_change as numeric(19, 5))), 0.0) as rt50_booked_revenue_change,
       isnull(MAX(cast(rt1_revenue as numeric(19, 5))), 0.0) as rt1_revenue,
       isnull(MAX(cast(rt1_revenue_change as numeric(19, 5))), 0.0) as rt1_revenue_change,
       isnull(MAX(cast(rt2_revenue as numeric(19, 5))), 0.0) as rt2_revenue,
       isnull(MAX(cast(rt2_revenue_change as numeric(19, 5))), 0.0) as rt2_revenue_change,
       isnull(MAX(cast(rt3_revenue as numeric(19, 5))), 0.0) as rt3_revenue,
       isnull(MAX(cast(rt3_revenue_change as numeric(19, 5))), 0.0) as rt3_revenue_change,
       isnull(MAX(cast(rt4_revenue as numeric(19, 5))), 0.0) as rt4_revenue,
       isnull(MAX(cast(rt4_revenue_change as numeric(19, 5))), 0.0) as rt4_revenue_change,
       isnull(MAX(cast(rt5_revenue as numeric(19, 5))), 0.0) as rt5_revenue,
       isnull(MAX(cast(rt5_revenue_change as numeric(19, 5))), 0.0) as rt5_revenue_change,
       isnull(MAX(cast(rt6_revenue as numeric(19, 5))), 0.0) as rt6_revenue,
       isnull(MAX(cast(rt6_revenue_change as numeric(19, 5))), 0.0) as rt6_revenue_change,
       isnull(MAX(cast(rt7_revenue as numeric(19, 5))), 0.0) as rt7_revenue,
       isnull(MAX(cast(rt7_revenue_change as numeric(19, 5))), 0.0) as rt7_revenue_change,
       isnull(MAX(cast(rt8_revenue as numeric(19, 5))), 0.0) as rt8_revenue,
       isnull(MAX(cast(rt8_revenue_change as numeric(19, 5))), 0.0) as rt8_revenue_change,
       isnull(MAX(cast(rt9_revenue as numeric(19, 5))), 0.0) as rt9_revenue,
       isnull(MAX(cast(rt9_revenue_change as numeric(19, 5))), 0.0) as rt9_revenue_change,
       isnull(MAX(cast(rt10_revenue as numeric(19, 5))), 0.0) as rt10_revenue,
       isnull(MAX(cast(rt10_revenue_change as numeric(19, 5))), 0.0) as rt10_revenue_change,
       isnull(MAX(cast(rt11_revenue as numeric(19, 5))), 0.0) as rt11_revenue,
       isnull(MAX(cast(rt11_revenue_change as numeric(19, 5))), 0.0) as rt11_revenue_change,
       isnull(MAX(cast(rt12_revenue as numeric(19, 5))), 0.0) as rt12_revenue,
       isnull(MAX(cast(rt12_revenue_change as numeric(19, 5))), 0.0) as rt12_revenue_change,
       isnull(MAX(cast(rt13_revenue as numeric(19, 5))), 0.0) as rt13_revenue,
       isnull(MAX(cast(rt13_revenue_change as numeric(19, 5))), 0.0) as rt13_revenue_change,
       isnull(MAX(cast(rt14_revenue as numeric(19, 5))), 0.0) as rt14_revenue,
       isnull(MAX(cast(rt14_revenue_change as numeric(19, 5))), 0.0) as rt14_revenue_change,
       isnull(MAX(cast(rt15_revenue as numeric(19, 5))), 0.0) as rt15_revenue,
       isnull(MAX(cast(rt15_revenue_change as numeric(19, 5))), 0.0) as rt15_revenue_change,
       isnull(MAX(cast(rt16_revenue as numeric(19, 5))), 0.0) as rt16_revenue,
       isnull(MAX(cast(rt16_revenue_change as numeric(19, 5))), 0.0) as rt16_revenue_change,
       isnull(MAX(cast(rt17_revenue as numeric(19, 5))), 0.0) as rt17_revenue,
       isnull(MAX(cast(rt17_revenue_change as numeric(19, 5))), 0.0) as rt17_revenue_change,
       isnull(MAX(cast(rt18_revenue as numeric(19, 5))), 0.0) as rt18_revenue,
       isnull(MAX(cast(rt18_revenue_change as numeric(19, 5))), 0.0) as rt18_revenue_change,
       isnull(MAX(cast(rt19_revenue as numeric(19, 5))), 0.0) as rt19_revenue,
       isnull(MAX(cast(rt19_revenue_change as numeric(19, 5))), 0.0) as rt19_revenue_change,
       isnull(MAX(cast(rt20_revenue as numeric(19, 5))), 0.0) as rt20_revenue,
       isnull(MAX(cast(rt20_revenue_change as numeric(19, 5))), 0.0) as rt20_revenue_change,
       isnull(MAX(cast(rt21_revenue as numeric(19, 5))), 0.0) as rt21_revenue,
       isnull(MAX(cast(rt21_revenue_change as numeric(19, 5))), 0.0) as rt21_revenue_change,
       isnull(MAX(cast(rt22_revenue as numeric(19, 5))), 0.0) as rt22_revenue,
       isnull(MAX(cast(rt22_revenue_change as numeric(19, 5))), 0.0) as rt22_revenue_change,
       isnull(MAX(cast(rt23_revenue as numeric(19, 5))), 0.0) as rt23_revenue,
       isnull(MAX(cast(rt23_revenue_change as numeric(19, 5))), 0.0) as rt23_revenue_change,
       isnull(MAX(cast(rt24_revenue as numeric(19, 5))), 0.0) as rt24_revenue,
       isnull(MAX(cast(rt24_revenue_change as numeric(19, 5))), 0.0) as rt24_revenue_change,
       isnull(MAX(cast(rt25_revenue as numeric(19, 5))), 0.0) as rt25_revenue,
       isnull(MAX(cast(rt25_revenue_change as numeric(19, 5))), 0.0) as rt25_revenue_change,
       isnull(MAX(cast(rt26_revenue as numeric(19, 5))), 0.0) as rt26_revenue,
       isnull(MAX(cast(rt26_revenue_change as numeric(19, 5))), 0.0) as rt26_revenue_change,
       isnull(MAX(cast(rt27_revenue as numeric(19, 5))), 0.0) as rt27_revenue,
       isnull(MAX(cast(rt27_revenue_change as numeric(19, 5))), 0.0) as rt27_revenue_change,
       isnull(MAX(cast(rt28_revenue as numeric(19, 5))), 0.0) as rt28_revenue,
       isnull(MAX(cast(rt28_revenue_change as numeric(19, 5))), 0.0) as rt28_revenue_change,
       isnull(MAX(cast(rt29_revenue as numeric(19, 5))), 0.0) as rt29_revenue,
       isnull(MAX(cast(rt29_revenue_change as numeric(19, 5))), 0.0) as rt29_revenue_change,
       isnull(MAX(cast(rt30_revenue as numeric(19, 5))), 0.0) as rt30_revenue,
       isnull(MAX(cast(rt30_revenue_change as numeric(19, 5))), 0.0) as rt30_revenue_change,
       isnull(MAX(cast(rt31_revenue as numeric(19, 5))), 0.0) as rt31_revenue,
       isnull(MAX(cast(rt31_revenue_change as numeric(19, 5))), 0.0) as rt31_revenue_change,
       isnull(MAX(cast(rt32_revenue as numeric(19, 5))), 0.0) as rt32_revenue,
       isnull(MAX(cast(rt32_revenue_change as numeric(19, 5))), 0.0) as rt32_revenue_change,
       isnull(MAX(cast(rt33_revenue as numeric(19, 5))), 0.0) as rt33_revenue,
       isnull(MAX(cast(rt33_revenue_change as numeric(19, 5))), 0.0) as rt33_revenue_change,
       isnull(MAX(cast(rt34_revenue as numeric(19, 5))), 0.0) as rt34_revenue,
       isnull(MAX(cast(rt34_revenue_change as numeric(19, 5))), 0.0) as rt34_revenue_change,
       isnull(MAX(cast(rt35_revenue as numeric(19, 5))), 0.0) as rt35_revenue,
       isnull(MAX(cast(rt35_revenue_change as numeric(19, 5))), 0.0) as rt35_revenue_change,
       isnull(MAX(cast(rt36_revenue as numeric(19, 5))), 0.0) as rt36_revenue,
       isnull(MAX(cast(rt36_revenue_change as numeric(19, 5))), 0.0) as rt36_revenue_change,
       isnull(MAX(cast(rt37_revenue as numeric(19, 5))), 0.0) as rt37_revenue,
       isnull(MAX(cast(rt37_revenue_change as numeric(19, 5))), 0.0) as rt37_revenue_change,
       isnull(MAX(cast(rt38_revenue as numeric(19, 5))), 0.0) as rt38_revenue,
       isnull(MAX(cast(rt38_revenue_change as numeric(19, 5))), 0.0) as rt38_revenue_change,
       isnull(MAX(cast(rt39_revenue as numeric(19, 5))), 0.0) as rt39_revenue,
       isnull(MAX(cast(rt39_revenue_change as numeric(19, 5))), 0.0) as rt39_revenue_change,
       isnull(MAX(cast(rt40_revenue as numeric(19, 5))), 0.0) as rt40_revenue,
       isnull(MAX(cast(rt40_revenue_change as numeric(19, 5))), 0.0) as rt40_revenue_change,
       isnull(MAX(cast(rt41_revenue as numeric(19, 5))), 0.0) as rt41_revenue,
       isnull(MAX(cast(rt41_revenue_change as numeric(19, 5))), 0.0) as rt41_revenue_change,
       isnull(MAX(cast(rt42_revenue as numeric(19, 5))), 0.0) as rt42_revenue,
       isnull(MAX(cast(rt42_revenue_change as numeric(19, 5))), 0.0) as rt42_revenue_change,
       isnull(MAX(cast(rt43_revenue as numeric(19, 5))), 0.0) as rt43_revenue,
       isnull(MAX(cast(rt43_revenue_change as numeric(19, 5))), 0.0) as rt43_revenue_change,
       isnull(MAX(cast(rt44_revenue as numeric(19, 5))), 0.0) as rt44_revenue,
       isnull(MAX(cast(rt44_revenue_change as numeric(19, 5))), 0.0) as rt44_revenue_change,
       isnull(MAX(cast(rt45_revenue as numeric(19, 5))), 0.0) as rt45_revenue,
       isnull(MAX(cast(rt45_revenue_change as numeric(19, 5))), 0.0) as rt45_revenue_change,
       isnull(MAX(cast(rt46_revenue as numeric(19, 5))), 0.0) as rt46_revenue,
       isnull(MAX(cast(rt46_revenue_change as numeric(19, 5))), 0.0) as rt46_revenue_change,
       isnull(MAX(cast(rt47_revenue as numeric(19, 5))), 0.0) as rt47_revenue,
       isnull(MAX(cast(rt47_revenue_change as numeric(19, 5))), 0.0) as rt47_revenue_change,
       isnull(MAX(cast(rt48_revenue as numeric(19, 5))), 0.0) as rt48_revenue,
       isnull(MAX(cast(rt48_revenue_change as numeric(19, 5))), 0.0) as rt48_revenue_change,
       isnull(MAX(cast(rt49_revenue as numeric(19, 5))), 0.0) as rt49_revenue,
       isnull(MAX(cast(rt49_revenue_change as numeric(19, 5))), 0.0) as rt49_revenue_change,
       isnull(MAX(cast(rt50_revenue as numeric(19, 5))), 0.0) as rt50_revenue,
       isnull(MAX(cast(rt50_revenue_change as numeric(19, 5))), 0.0) as rt50_revenue_change,
       isnull(MAX(cast(rt1_booked_adr as numeric(19, 5))), 0.0) as rt1_booked_adr,
       isnull(MAX(cast(rt1_booked_adr_change as numeric(19, 5))), 0.0) as rt1_booked_adr_change,
       isnull(MAX(cast(rt2_booked_adr as numeric(19, 5))), 0.0) as rt2_booked_adr,
       isnull(MAX(cast(rt2_booked_adr_change as numeric(19, 5))), 0.0) as rt2_booked_adr_change,
       isnull(MAX(cast(rt3_booked_adr as numeric(19, 5))), 0.0) as rt3_booked_adr,
       isnull(MAX(cast(rt3_booked_adr_change as numeric(19, 5))), 0.0) as rt3_booked_adr_change,
       isnull(MAX(cast(rt4_booked_adr as numeric(19, 5))), 0.0) as rt4_booked_adr,
       isnull(MAX(cast(rt4_booked_adr_change as numeric(19, 5))), 0.0) as rt4_booked_adr_change,
       isnull(MAX(cast(rt5_booked_adr as numeric(19, 5))), 0.0) as rt5_booked_adr,
       isnull(MAX(cast(rt5_booked_adr_change as numeric(19, 5))), 0.0) as rt5_booked_adr_change,
       isnull(MAX(cast(rt6_booked_adr as numeric(19, 5))), 0.0) as rt6_booked_adr,
       isnull(MAX(cast(rt6_booked_adr_change as numeric(19, 5))), 0.0) as rt6_booked_adr_change,
       isnull(MAX(cast(rt7_booked_adr as numeric(19, 5))), 0.0) as rt7_booked_adr,
       isnull(MAX(cast(rt7_booked_adr_change as numeric(19, 5))), 0.0) as rt7_booked_adr_change,
       isnull(MAX(cast(rt8_booked_adr as numeric(19, 5))), 0.0) as rt8_booked_adr,
       isnull(MAX(cast(rt8_booked_adr_change as numeric(19, 5))), 0.0) as rt8_booked_adr_change,
       isnull(MAX(cast(rt9_booked_adr as numeric(19, 5))), 0.0) as rt9_booked_adr,
       isnull(MAX(cast(rt9_booked_adr_change as numeric(19, 5))), 0.0) as rt9_booked_adr_change,
       isnull(MAX(cast(rt10_booked_adr as numeric(19, 5))), 0.0) as rt10_booked_adr,
       isnull(MAX(cast(rt10_booked_adr_change as numeric(19, 5))), 0.0) as rt10_booked_adr_change,
       isnull(MAX(cast(rt11_booked_adr as numeric(19, 5))), 0.0) as rt11_booked_adr,
       isnull(MAX(cast(rt11_booked_adr_change as numeric(19, 5))), 0.0) as rt11_booked_adr_change,
       isnull(MAX(cast(rt12_booked_adr as numeric(19, 5))), 0.0) as rt12_booked_adr,
       isnull(MAX(cast(rt12_booked_adr_change as numeric(19, 5))), 0.0) as rt12_booked_adr_change,
       isnull(MAX(cast(rt13_booked_adr as numeric(19, 5))), 0.0) as rt13_booked_adr,
       isnull(MAX(cast(rt13_booked_adr_change as numeric(19, 5))), 0.0) as rt13_booked_adr_change,
       isnull(MAX(cast(rt14_booked_adr as numeric(19, 5))), 0.0) as rt14_booked_adr,
       isnull(MAX(cast(rt14_booked_adr_change as numeric(19, 5))), 0.0) as rt14_booked_adr_change,
       isnull(MAX(cast(rt15_booked_adr as numeric(19, 5))), 0.0) as rt15_booked_adr,
       isnull(MAX(cast(rt15_booked_adr_change as numeric(19, 5))), 0.0) as rt15_booked_adr_change,
       isnull(MAX(cast(rt16_booked_adr as numeric(19, 5))), 0.0) as rt16_booked_adr,
       isnull(MAX(cast(rt16_booked_adr_change as numeric(19, 5))), 0.0) as rt16_booked_adr_change,
       isnull(MAX(cast(rt17_booked_adr as numeric(19, 5))), 0.0) as rt17_booked_adr,
       isnull(MAX(cast(rt17_booked_adr_change as numeric(19, 5))), 0.0) as rt17_booked_adr_change,
       isnull(MAX(cast(rt18_booked_adr as numeric(19, 5))), 0.0) as rt18_booked_adr,
       isnull(MAX(cast(rt18_booked_adr_change as numeric(19, 5))), 0.0) as rt18_booked_adr_change,
       isnull(MAX(cast(rt19_booked_adr as numeric(19, 5))), 0.0) as rt19_booked_adr,
       isnull(MAX(cast(rt19_booked_adr_change as numeric(19, 5))), 0.0) as rt19_booked_adr_change,
       isnull(MAX(cast(rt20_booked_adr as numeric(19, 5))), 0.0) as rt20_booked_adr,
       isnull(MAX(cast(rt20_booked_adr_change as numeric(19, 5))), 0.0) as rt20_booked_adr_change,
       isnull(MAX(cast(rt21_booked_adr as numeric(19, 5))), 0.0) as rt21_booked_adr,
       isnull(MAX(cast(rt21_booked_adr_change as numeric(19, 5))), 0.0) as rt21_booked_adr_change,
       isnull(MAX(cast(rt22_booked_adr as numeric(19, 5))), 0.0) as rt22_booked_adr,
       isnull(MAX(cast(rt22_booked_adr_change as numeric(19, 5))), 0.0) as rt22_booked_adr_change,
       isnull(MAX(cast(rt23_booked_adr as numeric(19, 5))), 0.0) as rt23_booked_adr,
       isnull(MAX(cast(rt23_booked_adr_change as numeric(19, 5))), 0.0) as rt23_booked_adr_change,
       isnull(MAX(cast(rt24_booked_adr as numeric(19, 5))), 0.0) as rt24_booked_adr,
       isnull(MAX(cast(rt24_booked_adr_change as numeric(19, 5))), 0.0) as rt24_booked_adr_change,
       isnull(MAX(cast(rt25_booked_adr as numeric(19, 5))), 0.0) as rt25_booked_adr,
       isnull(MAX(cast(rt25_booked_adr_change as numeric(19, 5))), 0.0) as rt25_booked_adr_change,
       isnull(MAX(cast(rt26_booked_adr as numeric(19, 5))), 0.0) as rt26_booked_adr,
       isnull(MAX(cast(rt26_booked_adr_change as numeric(19, 5))), 0.0) as rt26_booked_adr_change,
       isnull(MAX(cast(rt27_booked_adr as numeric(19, 5))), 0.0) as rt27_booked_adr,
       isnull(MAX(cast(rt27_booked_adr_change as numeric(19, 5))), 0.0) as rt27_booked_adr_change,
       isnull(MAX(cast(rt28_booked_adr as numeric(19, 5))), 0.0) as rt28_booked_adr,
       isnull(MAX(cast(rt28_booked_adr_change as numeric(19, 5))), 0.0) as rt28_booked_adr_change,
       isnull(MAX(cast(rt29_booked_adr as numeric(19, 5))), 0.0) as rt29_booked_adr,
       isnull(MAX(cast(rt29_booked_adr_change as numeric(19, 5))), 0.0) as rt29_booked_adr_change,
       isnull(MAX(cast(rt30_booked_adr as numeric(19, 5))), 0.0) as rt30_booked_adr,
       isnull(MAX(cast(rt30_booked_adr_change as numeric(19, 5))), 0.0) as rt30_booked_adr_change,
       isnull(MAX(cast(rt31_booked_adr as numeric(19, 5))), 0.0) as rt31_booked_adr,
       isnull(MAX(cast(rt31_booked_adr_change as numeric(19, 5))), 0.0) as rt31_booked_adr_change,
       isnull(MAX(cast(rt32_booked_adr as numeric(19, 5))), 0.0) as rt32_booked_adr,
       isnull(MAX(cast(rt32_booked_adr_change as numeric(19, 5))), 0.0) as rt32_booked_adr_change,
       isnull(MAX(cast(rt33_booked_adr as numeric(19, 5))), 0.0) as rt33_booked_adr,
       isnull(MAX(cast(rt33_booked_adr_change as numeric(19, 5))), 0.0) as rt33_booked_adr_change,
       isnull(MAX(cast(rt34_booked_adr as numeric(19, 5))), 0.0) as rt34_booked_adr,
       isnull(MAX(cast(rt34_booked_adr_change as numeric(19, 5))), 0.0) as rt34_booked_adr_change,
       isnull(MAX(cast(rt35_booked_adr as numeric(19, 5))), 0.0) as rt35_booked_adr,
       isnull(MAX(cast(rt35_booked_adr_change as numeric(19, 5))), 0.0) as rt35_booked_adr_change,
       isnull(MAX(cast(rt36_booked_adr as numeric(19, 5))), 0.0) as rt36_booked_adr,
       isnull(MAX(cast(rt36_booked_adr_change as numeric(19, 5))), 0.0) as rt36_booked_adr_change,
       isnull(MAX(cast(rt37_booked_adr as numeric(19, 5))), 0.0) as rt37_booked_adr,
       isnull(MAX(cast(rt37_booked_adr_change as numeric(19, 5))), 0.0) as rt37_booked_adr_change,
       isnull(MAX(cast(rt38_booked_adr as numeric(19, 5))), 0.0) as rt38_booked_adr,
       isnull(MAX(cast(rt38_booked_adr_change as numeric(19, 5))), 0.0) as rt38_booked_adr_change,
       isnull(MAX(cast(rt39_booked_adr as numeric(19, 5))), 0.0) as rt39_booked_adr,
       isnull(MAX(cast(rt39_booked_adr_change as numeric(19, 5))), 0.0) as rt39_booked_adr_change,
       isnull(MAX(cast(rt40_booked_adr as numeric(19, 5))), 0.0) as rt40_booked_adr,
       isnull(MAX(cast(rt40_booked_adr_change as numeric(19, 5))), 0.0) as rt40_booked_adr_change,
       isnull(MAX(cast(rt41_booked_adr as numeric(19, 5))), 0.0) as rt41_booked_adr,
       isnull(MAX(cast(rt41_booked_adr_change as numeric(19, 5))), 0.0) as rt41_booked_adr_change,
       isnull(MAX(cast(rt42_booked_adr as numeric(19, 5))), 0.0) as rt42_booked_adr,
       isnull(MAX(cast(rt42_booked_adr_change as numeric(19, 5))), 0.0) as rt42_booked_adr_change,
       isnull(MAX(cast(rt43_booked_adr as numeric(19, 5))), 0.0) as rt43_booked_adr,
       isnull(MAX(cast(rt43_booked_adr_change as numeric(19, 5))), 0.0) as rt43_booked_adr_change,
       isnull(MAX(cast(rt44_booked_adr as numeric(19, 5))), 0.0) as rt44_booked_adr,
       isnull(MAX(cast(rt44_booked_adr_change as numeric(19, 5))), 0.0) as rt44_booked_adr_change,
       isnull(MAX(cast(rt45_booked_adr as numeric(19, 5))), 0.0) as rt45_booked_adr,
       isnull(MAX(cast(rt45_booked_adr_change as numeric(19, 5))), 0.0) as rt45_booked_adr_change,
       isnull(MAX(cast(rt46_booked_adr as numeric(19, 5))), 0.0) as rt46_booked_adr,
       isnull(MAX(cast(rt46_booked_adr_change as numeric(19, 5))), 0.0) as rt46_booked_adr_change,
       isnull(MAX(cast(rt47_booked_adr as numeric(19, 5))), 0.0) as rt47_booked_adr,
       isnull(MAX(cast(rt47_booked_adr_change as numeric(19, 5))), 0.0) as rt47_booked_adr_change,
       isnull(MAX(cast(rt48_booked_adr as numeric(19, 5))), 0.0) as rt48_booked_adr,
       isnull(MAX(cast(rt48_booked_adr_change as numeric(19, 5))), 0.0) as rt48_booked_adr_change,
       isnull(MAX(cast(rt49_booked_adr as numeric(19, 5))), 0.0) as rt49_booked_adr,
       isnull(MAX(cast(rt49_booked_adr_change as numeric(19, 5))), 0.0) as rt49_booked_adr_change,
       isnull(MAX(cast(rt50_booked_adr as numeric(19, 5))), 0.0) as rt50_booked_adr,
       isnull(MAX(cast(rt50_booked_adr_change as numeric(19, 5))), 0.0) as rt50_booked_adr_change,
       isnull(MAX(cast(rt1_adr as numeric(19, 5))), 0.0) as rt1_adr,
       isnull(MAX(cast(rt1_adr_change as numeric(19, 5))), 0.0) as rt1_adr_change,
       isnull(MAX(cast(rt2_adr as numeric(19, 5))), 0.0) as rt2_adr,
       isnull(MAX(cast(rt2_adr_change as numeric(19, 5))), 0.0) as rt2_adr_change,
       isnull(MAX(cast(rt3_adr as numeric(19, 5))), 0.0) as rt3_adr,
       isnull(MAX(cast(rt3_adr_change as numeric(19, 5))), 0.0) as rt3_adr_change,
       isnull(MAX(cast(rt4_adr as numeric(19, 5))), 0.0) as rt4_adr,
       isnull(MAX(cast(rt4_adr_change as numeric(19, 5))), 0.0) as rt4_adr_change,
       isnull(MAX(cast(rt5_adr as numeric(19, 5))), 0.0) as rt5_adr,
       isnull(MAX(cast(rt5_adr_change as numeric(19, 5))), 0.0) as rt5_adr_change,
       isnull(MAX(cast(rt6_adr as numeric(19, 5))), 0.0) as rt6_adr,
       isnull(MAX(cast(rt6_adr_change as numeric(19, 5))), 0.0) as rt6_adr_change,
       isnull(MAX(cast(rt7_adr as numeric(19, 5))), 0.0) as rt7_adr,
       isnull(MAX(cast(rt7_adr_change as numeric(19, 5))), 0.0) as rt7_adr_change,
       isnull(MAX(cast(rt8_adr as numeric(19, 5))), 0.0) as rt8_adr,
       isnull(MAX(cast(rt8_adr_change as numeric(19, 5))), 0.0) as rt8_adr_change,
       isnull(MAX(cast(rt9_adr as numeric(19, 5))), 0.0) as rt9_adr,
       isnull(MAX(cast(rt9_adr_change as numeric(19, 5))), 0.0) as rt9_adr_change,
       isnull(MAX(cast(rt10_adr as numeric(19, 5))), 0.0) as rt10_adr,
       isnull(MAX(cast(rt10_adr_change as numeric(19, 5))), 0.0) as rt10_adr_change,
       isnull(MAX(cast(rt11_adr as numeric(19, 5))), 0.0) as rt11_adr,
       isnull(MAX(cast(rt11_adr_change as numeric(19, 5))), 0.0) as rt11_adr_change,
       isnull(MAX(cast(rt12_adr as numeric(19, 5))), 0.0) as rt12_adr,
       isnull(MAX(cast(rt12_adr_change as numeric(19, 5))), 0.0) as rt12_adr_change,
       isnull(MAX(cast(rt13_adr as numeric(19, 5))), 0.0) as rt13_adr,
       isnull(MAX(cast(rt13_adr_change as numeric(19, 5))), 0.0) as rt13_adr_change,
       isnull(MAX(cast(rt14_adr as numeric(19, 5))), 0.0) as rt14_adr,
       isnull(MAX(cast(rt14_adr_change as numeric(19, 5))), 0.0) as rt14_adr_change,
       isnull(MAX(cast(rt15_adr as numeric(19, 5))), 0.0) as rt15_adr,
       isnull(MAX(cast(rt15_adr_change as numeric(19, 5))), 0.0) as rt15_adr_change,
       isnull(MAX(cast(rt16_adr as numeric(19, 5))), 0.0) as rt16_adr,
       isnull(MAX(cast(rt16_adr_change as numeric(19, 5))), 0.0) as rt16_adr_change,
       isnull(MAX(cast(rt17_adr as numeric(19, 5))), 0.0) as rt17_adr,
       isnull(MAX(cast(rt17_adr_change as numeric(19, 5))), 0.0) as rt17_adr_change,
       isnull(MAX(cast(rt18_adr as numeric(19, 5))), 0.0) as rt18_adr,
       isnull(MAX(cast(rt18_adr_change as numeric(19, 5))), 0.0) as rt18_adr_change,
       isnull(MAX(cast(rt19_adr as numeric(19, 5))), 0.0) as rt19_adr,
       isnull(MAX(cast(rt19_adr_change as numeric(19, 5))), 0.0) as rt19_adr_change,
       isnull(MAX(cast(rt20_adr as numeric(19, 5))), 0.0) as rt20_adr,
       isnull(MAX(cast(rt20_adr_change as numeric(19, 5))), 0.0) as rt20_adr_change,
       isnull(MAX(cast(rt21_adr as numeric(19, 5))), 0.0) as rt21_adr,
       isnull(MAX(cast(rt21_adr_change as numeric(19, 5))), 0.0) as rt21_adr_change,
       isnull(MAX(cast(rt22_adr as numeric(19, 5))), 0.0) as rt22_adr,
       isnull(MAX(cast(rt22_adr_change as numeric(19, 5))), 0.0) as rt22_adr_change,
       isnull(MAX(cast(rt23_adr as numeric(19, 5))), 0.0) as rt23_adr,
       isnull(MAX(cast(rt23_adr_change as numeric(19, 5))), 0.0) as rt23_adr_change,
       isnull(MAX(cast(rt24_adr as numeric(19, 5))), 0.0) as rt24_adr,
       isnull(MAX(cast(rt24_adr_change as numeric(19, 5))), 0.0) as rt24_adr_change,
       isnull(MAX(cast(rt25_adr as numeric(19, 5))), 0.0) as rt25_adr,
       isnull(MAX(cast(rt25_adr_change as numeric(19, 5))), 0.0) as rt25_adr_change,
       isnull(MAX(cast(rt26_adr as numeric(19, 5))), 0.0) as rt26_adr,
       isnull(MAX(cast(rt26_adr_change as numeric(19, 5))), 0.0) as rt26_adr_change,
       isnull(MAX(cast(rt27_adr as numeric(19, 5))), 0.0) as rt27_adr,
       isnull(MAX(cast(rt27_adr_change as numeric(19, 5))), 0.0) as rt27_adr_change,
       isnull(MAX(cast(rt28_adr as numeric(19, 5))), 0.0) as rt28_adr,
       isnull(MAX(cast(rt28_adr_change as numeric(19, 5))), 0.0) as rt28_adr_change,
       isnull(MAX(cast(rt29_adr as numeric(19, 5))), 0.0) as rt29_adr,
       isnull(MAX(cast(rt29_adr_change as numeric(19, 5))), 0.0) as rt29_adr_change,
       isnull(MAX(cast(rt30_adr as numeric(19, 5))), 0.0) as rt30_adr,
       isnull(MAX(cast(rt30_adr_change as numeric(19, 5))), 0.0) as rt30_adr_change,
       isnull(MAX(cast(rt31_adr as numeric(19, 5))), 0.0) as rt31_adr,
       isnull(MAX(cast(rt31_adr_change as numeric(19, 5))), 0.0) as rt31_adr_change,
       isnull(MAX(cast(rt32_adr as numeric(19, 5))), 0.0) as rt32_adr,
       isnull(MAX(cast(rt32_adr_change as numeric(19, 5))), 0.0) as rt32_adr_change,
       isnull(MAX(cast(rt33_adr as numeric(19, 5))), 0.0) as rt33_adr,
       isnull(MAX(cast(rt33_adr_change as numeric(19, 5))), 0.0) as rt33_adr_change,
       isnull(MAX(cast(rt34_adr as numeric(19, 5))), 0.0) as rt34_adr,
       isnull(MAX(cast(rt34_adr_change as numeric(19, 5))), 0.0) as rt34_adr_change,
       isnull(MAX(cast(rt35_adr as numeric(19, 5))), 0.0) as rt35_adr,
       isnull(MAX(cast(rt35_adr_change as numeric(19, 5))), 0.0) as rt35_adr_change,
       isnull(MAX(cast(rt36_adr as numeric(19, 5))), 0.0) as rt36_adr,
       isnull(MAX(cast(rt36_adr_change as numeric(19, 5))), 0.0) as rt36_adr_change,
       isnull(MAX(cast(rt37_adr as numeric(19, 5))), 0.0) as rt37_adr,
       isnull(MAX(cast(rt37_adr_change as numeric(19, 5))), 0.0) as rt37_adr_change,
       isnull(MAX(cast(rt38_adr as numeric(19, 5))), 0.0) as rt38_adr,
       isnull(MAX(cast(rt38_adr_change as numeric(19, 5))), 0.0) as rt38_adr_change,
       isnull(MAX(cast(rt39_adr as numeric(19, 5))), 0.0) as rt39_adr,
       isnull(MAX(cast(rt39_adr_change as numeric(19, 5))), 0.0) as rt39_adr_change,
       isnull(MAX(cast(rt40_adr as numeric(19, 5))), 0.0) as rt40_adr,
       isnull(MAX(cast(rt40_adr_change as numeric(19, 5))), 0.0) as rt40_adr_change,
       isnull(MAX(cast(rt41_adr as numeric(19, 5))), 0.0) as rt41_adr,
       isnull(MAX(cast(rt41_adr_change as numeric(19, 5))), 0.0) as rt41_adr_change,
       isnull(MAX(cast(rt42_adr as numeric(19, 5))), 0.0) as rt42_adr,
       isnull(MAX(cast(rt42_adr_change as numeric(19, 5))), 0.0) as rt42_adr_change,
       isnull(MAX(cast(rt43_adr as numeric(19, 5))), 0.0) as rt43_adr,
       isnull(MAX(cast(rt43_adr_change as numeric(19, 5))), 0.0) as rt43_adr_change,
       isnull(MAX(cast(rt44_adr as numeric(19, 5))), 0.0) as rt44_adr,
       isnull(MAX(cast(rt44_adr_change as numeric(19, 5))), 0.0) as rt44_adr_change,
       isnull(MAX(cast(rt45_adr as numeric(19, 5))), 0.0) as rt45_adr,
       isnull(MAX(cast(rt45_adr_change as numeric(19, 5))), 0.0) as rt45_adr_change,
       isnull(MAX(cast(rt46_adr as numeric(19, 5))), 0.0) as rt46_adr,
       isnull(MAX(cast(rt46_adr_change as numeric(19, 5))), 0.0) as rt46_adr_change,
       isnull(MAX(cast(rt47_adr as numeric(19, 5))), 0.0) as rt47_adr,
       isnull(MAX(cast(rt47_adr_change as numeric(19, 5))), 0.0) as rt47_adr_change,
       isnull(MAX(cast(rt48_adr as numeric(19, 5))), 0.0) as rt48_adr,
       isnull(MAX(cast(rt48_adr_change as numeric(19, 5))), 0.0) as rt48_adr_change,
       isnull(MAX(cast(rt49_adr as numeric(19, 5))), 0.0) as rt49_adr,
       isnull(MAX(cast(rt49_adr_change as numeric(19, 5))), 0.0) as rt49_adr_change,
       isnull(MAX(cast(rt50_adr as numeric(19, 5))), 0.0) as rt50_adr,
       isnull(MAX(cast(rt50_adr_change as numeric(19, 5))), 0.0) as rt50_adr_change,
       isnull(MAX(cast(rt1_booked_revpar as numeric(19, 5))), 0.0) as rt1_booked_revpar,
       isnull(MAX(cast(rt1_booked_revpar_change as numeric(19, 5))), 0.0) as rt1_booked_revpar_change,
       isnull(MAX(cast(rt2_booked_revpar as numeric(19, 5))), 0.0) as rt2_booked_revpar,
       isnull(MAX(cast(rt2_booked_revpar_change as numeric(19, 5))), 0.0) as rt2_booked_revpar_change,
       isnull(MAX(cast(rt3_booked_revpar as numeric(19, 5))), 0.0) as rt3_booked_revpar,
       isnull(MAX(cast(rt3_booked_revpar_change as numeric(19, 5))), 0.0) as rt3_booked_revpar_change,
       isnull(MAX(cast(rt4_booked_revpar as numeric(19, 5))), 0.0) as rt4_booked_revpar,
       isnull(MAX(cast(rt4_booked_revpar_change as numeric(19, 5))), 0.0) as rt4_booked_revpar_change,
       isnull(MAX(cast(rt5_booked_revpar as numeric(19, 5))), 0.0) as rt5_booked_revpar,
       isnull(MAX(cast(rt5_booked_revpar_change as numeric(19, 5))), 0.0) as rt5_booked_revpar_change,
       isnull(MAX(cast(rt6_booked_revpar as numeric(19, 5))), 0.0) as rt6_booked_revpar,
       isnull(MAX(cast(rt6_booked_revpar_change as numeric(19, 5))), 0.0) as rt6_booked_revpar_change,
       isnull(MAX(cast(rt7_booked_revpar as numeric(19, 5))), 0.0) as rt7_booked_revpar,
       isnull(MAX(cast(rt7_booked_revpar_change as numeric(19, 5))), 0.0) as rt7_booked_revpar_change,
       isnull(MAX(cast(rt8_booked_revpar as numeric(19, 5))), 0.0) as rt8_booked_revpar,
       isnull(MAX(cast(rt8_booked_revpar_change as numeric(19, 5))), 0.0) as rt8_booked_revpar_change,
       isnull(MAX(cast(rt9_booked_revpar as numeric(19, 5))), 0.0) as rt9_booked_revpar,
       isnull(MAX(cast(rt9_booked_revpar_change as numeric(19, 5))), 0.0) as rt9_booked_revpar_change,
       isnull(MAX(cast(rt10_booked_revpar as numeric(19, 5))), 0.0) as rt10_booked_revpar,
       isnull(MAX(cast(rt10_booked_revpar_change as numeric(19, 5))), 0.0) as rt10_booked_revpar_change,
       isnull(MAX(cast(rt11_booked_revpar as numeric(19, 5))), 0.0) as rt11_booked_revpar,
       isnull(MAX(cast(rt11_booked_revpar_change as numeric(19, 5))), 0.0) as rt11_booked_revpar_change,
       isnull(MAX(cast(rt12_booked_revpar as numeric(19, 5))), 0.0) as rt12_booked_revpar,
       isnull(MAX(cast(rt12_booked_revpar_change as numeric(19, 5))), 0.0) as rt12_booked_revpar_change,
       isnull(MAX(cast(rt13_booked_revpar as numeric(19, 5))), 0.0) as rt13_booked_revpar,
       isnull(MAX(cast(rt13_booked_revpar_change as numeric(19, 5))), 0.0) as rt13_booked_revpar_change,
       isnull(MAX(cast(rt14_booked_revpar as numeric(19, 5))), 0.0) as rt14_booked_revpar,
       isnull(MAX(cast(rt14_booked_revpar_change as numeric(19, 5))), 0.0) as rt14_booked_revpar_change,
       isnull(MAX(cast(rt15_booked_revpar as numeric(19, 5))), 0.0) as rt15_booked_revpar,
       isnull(MAX(cast(rt15_booked_revpar_change as numeric(19, 5))), 0.0) as rt15_booked_revpar_change,
       isnull(MAX(cast(rt16_booked_revpar as numeric(19, 5))), 0.0) as rt16_booked_revpar,
       isnull(MAX(cast(rt16_booked_revpar_change as numeric(19, 5))), 0.0) as rt16_booked_revpar_change,
       isnull(MAX(cast(rt17_booked_revpar as numeric(19, 5))), 0.0) as rt17_booked_revpar,
       isnull(MAX(cast(rt17_booked_revpar_change as numeric(19, 5))), 0.0) as rt17_booked_revpar_change,
       isnull(MAX(cast(rt18_booked_revpar as numeric(19, 5))), 0.0) as rt18_booked_revpar,
       isnull(MAX(cast(rt18_booked_revpar_change as numeric(19, 5))), 0.0) as rt18_booked_revpar_change,
       isnull(MAX(cast(rt19_booked_revpar as numeric(19, 5))), 0.0) as rt19_booked_revpar,
       isnull(MAX(cast(rt19_booked_revpar_change as numeric(19, 5))), 0.0) as rt19_booked_revpar_change,
       isnull(MAX(cast(rt20_booked_revpar as numeric(19, 5))), 0.0) as rt20_booked_revpar,
       isnull(MAX(cast(rt20_booked_revpar_change as numeric(19, 5))), 0.0) as rt20_booked_revpar_change,
       isnull(MAX(cast(rt21_booked_revpar as numeric(19, 5))), 0.0) as rt21_booked_revpar,
       isnull(MAX(cast(rt21_booked_revpar_change as numeric(19, 5))), 0.0) as rt21_booked_revpar_change,
       isnull(MAX(cast(rt22_booked_revpar as numeric(19, 5))), 0.0) as rt22_booked_revpar,
       isnull(MAX(cast(rt22_booked_revpar_change as numeric(19, 5))), 0.0) as rt22_booked_revpar_change,
       isnull(MAX(cast(rt23_booked_revpar as numeric(19, 5))), 0.0) as rt23_booked_revpar,
       isnull(MAX(cast(rt23_booked_revpar_change as numeric(19, 5))), 0.0) as rt23_booked_revpar_change,
       isnull(MAX(cast(rt24_booked_revpar as numeric(19, 5))), 0.0) as rt24_booked_revpar,
       isnull(MAX(cast(rt24_booked_revpar_change as numeric(19, 5))), 0.0) as rt24_booked_revpar_change,
       isnull(MAX(cast(rt25_booked_revpar as numeric(19, 5))), 0.0) as rt25_booked_revpar,
       isnull(MAX(cast(rt25_booked_revpar_change as numeric(19, 5))), 0.0) as rt25_booked_revpar_change,
       isnull(MAX(cast(rt26_booked_revpar as numeric(19, 5))), 0.0) as rt26_booked_revpar,
       isnull(MAX(cast(rt26_booked_revpar_change as numeric(19, 5))), 0.0) as rt26_booked_revpar_change,
       isnull(MAX(cast(rt27_booked_revpar as numeric(19, 5))), 0.0) as rt27_booked_revpar,
       isnull(MAX(cast(rt27_booked_revpar_change as numeric(19, 5))), 0.0) as rt27_booked_revpar_change,
       isnull(MAX(cast(rt28_booked_revpar as numeric(19, 5))), 0.0) as rt28_booked_revpar,
       isnull(MAX(cast(rt28_booked_revpar_change as numeric(19, 5))), 0.0) as rt28_booked_revpar_change,
       isnull(MAX(cast(rt29_booked_revpar as numeric(19, 5))), 0.0) as rt29_booked_revpar,
       isnull(MAX(cast(rt29_booked_revpar_change as numeric(19, 5))), 0.0) as rt29_booked_revpar_change,
       isnull(MAX(cast(rt30_booked_revpar as numeric(19, 5))), 0.0) as rt30_booked_revpar,
       isnull(MAX(cast(rt30_booked_revpar_change as numeric(19, 5))), 0.0) as rt30_booked_revpar_change,
       isnull(MAX(cast(rt31_booked_revpar as numeric(19, 5))), 0.0) as rt31_booked_revpar,
       isnull(MAX(cast(rt31_booked_revpar_change as numeric(19, 5))), 0.0) as rt31_booked_revpar_change,
       isnull(MAX(cast(rt32_booked_revpar as numeric(19, 5))), 0.0) as rt32_booked_revpar,
       isnull(MAX(cast(rt32_booked_revpar_change as numeric(19, 5))), 0.0) as rt32_booked_revpar_change,
       isnull(MAX(cast(rt33_booked_revpar as numeric(19, 5))), 0.0) as rt33_booked_revpar,
       isnull(MAX(cast(rt33_booked_revpar_change as numeric(19, 5))), 0.0) as rt33_booked_revpar_change,
       isnull(MAX(cast(rt34_booked_revpar as numeric(19, 5))), 0.0) as rt34_booked_revpar,
       isnull(MAX(cast(rt34_booked_revpar_change as numeric(19, 5))), 0.0) as rt34_booked_revpar_change,
       isnull(MAX(cast(rt35_booked_revpar as numeric(19, 5))), 0.0) as rt35_booked_revpar,
       isnull(MAX(cast(rt35_booked_revpar_change as numeric(19, 5))), 0.0) as rt35_booked_revpar_change,
       isnull(MAX(cast(rt36_booked_revpar as numeric(19, 5))), 0.0) as rt36_booked_revpar,
       isnull(MAX(cast(rt36_booked_revpar_change as numeric(19, 5))), 0.0) as rt36_booked_revpar_change,
       isnull(MAX(cast(rt37_booked_revpar as numeric(19, 5))), 0.0) as rt37_booked_revpar,
       isnull(MAX(cast(rt37_booked_revpar_change as numeric(19, 5))), 0.0) as rt37_booked_revpar_change,
       isnull(MAX(cast(rt38_booked_revpar as numeric(19, 5))), 0.0) as rt38_booked_revpar,
       isnull(MAX(cast(rt38_booked_revpar_change as numeric(19, 5))), 0.0) as rt38_booked_revpar_change,
       isnull(MAX(cast(rt39_booked_revpar as numeric(19, 5))), 0.0) as rt39_booked_revpar,
       isnull(MAX(cast(rt39_booked_revpar_change as numeric(19, 5))), 0.0) as rt39_booked_revpar_change,
       isnull(MAX(cast(rt40_booked_revpar as numeric(19, 5))), 0.0) as rt40_booked_revpar,
       isnull(MAX(cast(rt40_booked_revpar_change as numeric(19, 5))), 0.0) as rt40_booked_revpar_change,
       isnull(MAX(cast(rt41_booked_revpar as numeric(19, 5))), 0.0) as rt41_booked_revpar,
       isnull(MAX(cast(rt41_booked_revpar_change as numeric(19, 5))), 0.0) as rt41_booked_revpar_change,
       isnull(MAX(cast(rt42_booked_revpar as numeric(19, 5))), 0.0) as rt42_booked_revpar,
       isnull(MAX(cast(rt42_booked_revpar_change as numeric(19, 5))), 0.0) as rt42_booked_revpar_change,
       isnull(MAX(cast(rt43_booked_revpar as numeric(19, 5))), 0.0) as rt43_booked_revpar,
       isnull(MAX(cast(rt43_booked_revpar_change as numeric(19, 5))), 0.0) as rt43_booked_revpar_change,
       isnull(MAX(cast(rt44_booked_revpar as numeric(19, 5))), 0.0) as rt44_booked_revpar,
       isnull(MAX(cast(rt44_booked_revpar_change as numeric(19, 5))), 0.0) as rt44_booked_revpar_change,
       isnull(MAX(cast(rt45_booked_revpar as numeric(19, 5))), 0.0) as rt45_booked_revpar,
       isnull(MAX(cast(rt45_booked_revpar_change as numeric(19, 5))), 0.0) as rt45_booked_revpar_change,
       isnull(MAX(cast(rt46_booked_revpar as numeric(19, 5))), 0.0) as rt46_booked_revpar,
       isnull(MAX(cast(rt46_booked_revpar_change as numeric(19, 5))), 0.0) as rt46_booked_revpar_change,
       isnull(MAX(cast(rt47_booked_revpar as numeric(19, 5))), 0.0) as rt47_booked_revpar,
       isnull(MAX(cast(rt47_booked_revpar_change as numeric(19, 5))), 0.0) as rt47_booked_revpar_change,
       isnull(MAX(cast(rt48_booked_revpar as numeric(19, 5))), 0.0) as rt48_booked_revpar,
       isnull(MAX(cast(rt48_booked_revpar_change as numeric(19, 5))), 0.0) as rt48_booked_revpar_change,
       isnull(MAX(cast(rt49_booked_revpar as numeric(19, 5))), 0.0) as rt49_booked_revpar,
       isnull(MAX(cast(rt49_booked_revpar_change as numeric(19, 5))), 0.0) as rt49_booked_revpar_change,
       isnull(MAX(cast(rt50_booked_revpar as numeric(19, 5))), 0.0) as rt50_booked_revpar,
       isnull(MAX(cast(rt50_booked_revpar_change as numeric(19, 5))), 0.0) as rt50_booked_revpar_change,
       isnull(MAX(cast(rt1_revpar as numeric(19, 5))), 0.0) as rt1_revpar,
       isnull(MAX(cast(rt1_revpar_change as numeric(19, 5))), 0.0) as rt1_revpar_change,
       isnull(MAX(cast(rt2_revpar as numeric(19, 5))), 0.0) as rt2_revpar,
       isnull(MAX(cast(rt2_revpar_change as numeric(19, 5))), 0.0) as rt2_revpar_change,
       isnull(MAX(cast(rt3_revpar as numeric(19, 5))), 0.0) as rt3_revpar,
       isnull(MAX(cast(rt3_revpar_change as numeric(19, 5))), 0.0) as rt3_revpar_change,
       isnull(MAX(cast(rt4_revpar as numeric(19, 5))), 0.0) as rt4_revpar,
       isnull(MAX(cast(rt4_revpar_change as numeric(19, 5))), 0.0) as rt4_revpar_change,
       isnull(MAX(cast(rt5_revpar as numeric(19, 5))), 0.0) as rt5_revpar,
       isnull(MAX(cast(rt5_revpar_change as numeric(19, 5))), 0.0) as rt5_revpar_change,
       isnull(MAX(cast(rt6_revpar as numeric(19, 5))), 0.0) as rt6_revpar,
       isnull(MAX(cast(rt6_revpar_change as numeric(19, 5))), 0.0) as rt6_revpar_change,
       isnull(MAX(cast(rt7_revpar as numeric(19, 5))), 0.0) as rt7_revpar,
       isnull(MAX(cast(rt7_revpar_change as numeric(19, 5))), 0.0) as rt7_revpar_change,
       isnull(MAX(cast(rt8_revpar as numeric(19, 5))), 0.0) as rt8_revpar,
       isnull(MAX(cast(rt8_revpar_change as numeric(19, 5))), 0.0) as rt8_revpar_change,
       isnull(MAX(cast(rt9_revpar as numeric(19, 5))), 0.0) as rt9_revpar,
       isnull(MAX(cast(rt9_revpar_change as numeric(19, 5))), 0.0) as rt9_revpar_change,
       isnull(MAX(cast(rt10_revpar as numeric(19, 5))), 0.0) as rt10_revpar,
       isnull(MAX(cast(rt10_revpar_change as numeric(19, 5))), 0.0) as rt10_revpar_change,
       isnull(MAX(cast(rt11_revpar as numeric(19, 5))), 0.0) as rt11_revpar,
       isnull(MAX(cast(rt11_revpar_change as numeric(19, 5))), 0.0) as rt11_revpar_change,
       isnull(MAX(cast(rt12_revpar as numeric(19, 5))), 0.0) as rt12_revpar,
       isnull(MAX(cast(rt12_revpar_change as numeric(19, 5))), 0.0) as rt12_revpar_change,
       isnull(MAX(cast(rt13_revpar as numeric(19, 5))), 0.0) as rt13_revpar,
       isnull(MAX(cast(rt13_revpar_change as numeric(19, 5))), 0.0) as rt13_revpar_change,
       isnull(MAX(cast(rt14_revpar as numeric(19, 5))), 0.0) as rt14_revpar,
       isnull(MAX(cast(rt14_revpar_change as numeric(19, 5))), 0.0) as rt14_revpar_change,
       isnull(MAX(cast(rt15_revpar as numeric(19, 5))), 0.0) as rt15_revpar,
       isnull(MAX(cast(rt15_revpar_change as numeric(19, 5))), 0.0) as rt15_revpar_change,
       isnull(MAX(cast(rt16_revpar as numeric(19, 5))), 0.0) as rt16_revpar,
       isnull(MAX(cast(rt16_revpar_change as numeric(19, 5))), 0.0) as rt16_revpar_change,
       isnull(MAX(cast(rt17_revpar as numeric(19, 5))), 0.0) as rt17_revpar,
       isnull(MAX(cast(rt17_revpar_change as numeric(19, 5))), 0.0) as rt17_revpar_change,
       isnull(MAX(cast(rt18_revpar as numeric(19, 5))), 0.0) as rt18_revpar,
       isnull(MAX(cast(rt18_revpar_change as numeric(19, 5))), 0.0) as rt18_revpar_change,
       isnull(MAX(cast(rt19_revpar as numeric(19, 5))), 0.0) as rt19_revpar,
       isnull(MAX(cast(rt19_revpar_change as numeric(19, 5))), 0.0) as rt19_revpar_change,
       isnull(MAX(cast(rt20_revpar as numeric(19, 5))), 0.0) as rt20_revpar,
       isnull(MAX(cast(rt20_revpar_change as numeric(19, 5))), 0.0) as rt20_revpar_change,
       isnull(MAX(cast(rt21_revpar as numeric(19, 5))), 0.0) as rt21_revpar,
       isnull(MAX(cast(rt21_revpar_change as numeric(19, 5))), 0.0) as rt21_revpar_change,
       isnull(MAX(cast(rt22_revpar as numeric(19, 5))), 0.0) as rt22_revpar,
       isnull(MAX(cast(rt22_revpar_change as numeric(19, 5))), 0.0) as rt22_revpar_change,
       isnull(MAX(cast(rt23_revpar as numeric(19, 5))), 0.0) as rt23_revpar,
       isnull(MAX(cast(rt23_revpar_change as numeric(19, 5))), 0.0) as rt23_revpar_change,
       isnull(MAX(cast(rt24_revpar as numeric(19, 5))), 0.0) as rt24_revpar,
       isnull(MAX(cast(rt24_revpar_change as numeric(19, 5))), 0.0) as rt24_revpar_change,
       isnull(MAX(cast(rt25_revpar as numeric(19, 5))), 0.0) as rt25_revpar,
       isnull(MAX(cast(rt25_revpar_change as numeric(19, 5))), 0.0) as rt25_revpar_change,
       isnull(MAX(cast(rt26_revpar as numeric(19, 5))), 0.0) as rt26_revpar,
       isnull(MAX(cast(rt26_revpar_change as numeric(19, 5))), 0.0) as rt26_revpar_change,
       isnull(MAX(cast(rt27_revpar as numeric(19, 5))), 0.0) as rt27_revpar,
       isnull(MAX(cast(rt27_revpar_change as numeric(19, 5))), 0.0) as rt27_revpar_change,
       isnull(MAX(cast(rt28_revpar as numeric(19, 5))), 0.0) as rt28_revpar,
       isnull(MAX(cast(rt28_revpar_change as numeric(19, 5))), 0.0) as rt28_revpar_change,
       isnull(MAX(cast(rt29_revpar as numeric(19, 5))), 0.0) as rt29_revpar,
       isnull(MAX(cast(rt29_revpar_change as numeric(19, 5))), 0.0) as rt29_revpar_change,
       isnull(MAX(cast(rt30_revpar as numeric(19, 5))), 0.0) as rt30_revpar,
       isnull(MAX(cast(rt30_revpar_change as numeric(19, 5))), 0.0) as rt30_revpar_change,
       isnull(MAX(cast(rt31_revpar as numeric(19, 5))), 0.0) as rt31_revpar,
       isnull(MAX(cast(rt31_revpar_change as numeric(19, 5))), 0.0) as rt31_revpar_change,
       isnull(MAX(cast(rt32_revpar as numeric(19, 5))), 0.0) as rt32_revpar,
       isnull(MAX(cast(rt32_revpar_change as numeric(19, 5))), 0.0) as rt32_revpar_change,
       isnull(MAX(cast(rt33_revpar as numeric(19, 5))), 0.0) as rt33_revpar,
       isnull(MAX(cast(rt33_revpar_change as numeric(19, 5))), 0.0) as rt33_revpar_change,
       isnull(MAX(cast(rt34_revpar as numeric(19, 5))), 0.0) as rt34_revpar,
       isnull(MAX(cast(rt34_revpar_change as numeric(19, 5))), 0.0) as rt34_revpar_change,
       isnull(MAX(cast(rt35_revpar as numeric(19, 5))), 0.0) as rt35_revpar,
       isnull(MAX(cast(rt35_revpar_change as numeric(19, 5))), 0.0) as rt35_revpar_change,
       isnull(MAX(cast(rt36_revpar as numeric(19, 5))), 0.0) as rt36_revpar,
       isnull(MAX(cast(rt36_revpar_change as numeric(19, 5))), 0.0) as rt36_revpar_change,
       isnull(MAX(cast(rt37_revpar as numeric(19, 5))), 0.0) as rt37_revpar,
       isnull(MAX(cast(rt37_revpar_change as numeric(19, 5))), 0.0) as rt37_revpar_change,
       isnull(MAX(cast(rt38_revpar as numeric(19, 5))), 0.0) as rt38_revpar,
       isnull(MAX(cast(rt38_revpar_change as numeric(19, 5))), 0.0) as rt38_revpar_change,
       isnull(MAX(cast(rt39_revpar as numeric(19, 5))), 0.0) as rt39_revpar,
       isnull(MAX(cast(rt39_revpar_change as numeric(19, 5))), 0.0) as rt39_revpar_change,
       isnull(MAX(cast(rt40_revpar as numeric(19, 5))), 0.0) as rt40_revpar,
       isnull(MAX(cast(rt40_revpar_change as numeric(19, 5))), 0.0) as rt40_revpar_change,
       isnull(MAX(cast(rt41_revpar as numeric(19, 5))), 0.0) as rt41_revpar,
       isnull(MAX(cast(rt41_revpar_change as numeric(19, 5))), 0.0) as rt41_revpar_change,
       isnull(MAX(cast(rt42_revpar as numeric(19, 5))), 0.0) as rt42_revpar,
       isnull(MAX(cast(rt42_revpar_change as numeric(19, 5))), 0.0) as rt42_revpar_change,
       isnull(MAX(cast(rt43_revpar as numeric(19, 5))), 0.0) as rt43_revpar,
       isnull(MAX(cast(rt43_revpar_change as numeric(19, 5))), 0.0) as rt43_revpar_change,
       isnull(MAX(cast(rt44_revpar as numeric(19, 5))), 0.0) as rt44_revpar,
       isnull(MAX(cast(rt44_revpar_change as numeric(19, 5))), 0.0) as rt44_revpar_change,
       isnull(MAX(cast(rt45_revpar as numeric(19, 5))), 0.0) as rt45_revpar,
       isnull(MAX(cast(rt45_revpar_change as numeric(19, 5))), 0.0) as rt45_revpar_change,
       isnull(MAX(cast(rt46_revpar as numeric(19, 5))), 0.0) as rt46_revpar,
       isnull(MAX(cast(rt46_revpar_change as numeric(19, 5))), 0.0) as rt46_revpar_change,
       isnull(MAX(cast(rt47_revpar as numeric(19, 5))), 0.0) as rt47_revpar,
       isnull(MAX(cast(rt47_revpar_change as numeric(19, 5))), 0.0) as rt47_revpar_change,
       isnull(MAX(cast(rt48_revpar as numeric(19, 5))), 0.0) as rt48_revpar,
       isnull(MAX(cast(rt48_revpar_change as numeric(19, 5))), 0.0) as rt48_revpar_change,
       isnull(MAX(cast(rt49_revpar as numeric(19, 5))), 0.0) as rt49_revpar,
       isnull(MAX(cast(rt49_revpar_change as numeric(19, 5))), 0.0) as rt49_revpar_change,
       isnull(MAX(cast(rt50_revpar as numeric(19, 5))), 0.0) as rt50_revpar,
       isnull(MAX(cast(rt50_revpar_change as numeric(19, 5))), 0.0) as rt50_revpar_change,
       isnull(MAX(rt1_overbookingcurrent), 0.0) as rt1_overbookingcurrent,
       isnull(MAX(rt1_overbookingchange), 0.0) as rt1_overbookingchange,
       isnull(MAX(rt2_overbookingcurrent), 0.0) as rt2_overbookingcurrent,
       isnull(MAX(rt2_overbookingchange), 0.0) as rt2_overbookingchange,
       isnull(MAX(rt3_overbookingcurrent), 0.0) as rt3_overbookingcurrent,
       isnull(MAX(rt3_overbookingchange), 0.0) as rt3_overbookingchange,
       isnull(MAX(rt4_overbookingcurrent), 0.0) as rt4_overbookingcurrent,
       isnull(MAX(rt4_overbookingchange), 0.0) as rt4_overbookingchange,
       isnull(MAX(rt5_overbookingcurrent), 0.0) as rt5_overbookingcurrent,
       isnull(MAX(rt5_overbookingchange), 0.0) as rt5_overbookingchange,
       isnull(MAX(rt6_overbookingcurrent), 0.0) as rt6_overbookingcurrent,
       isnull(MAX(rt6_overbookingchange), 0.0) as rt6_overbookingchange,
       isnull(MAX(rt7_overbookingcurrent), 0.0) as rt7_overbookingcurrent,
       isnull(MAX(rt7_overbookingchange), 0.0) as rt7_overbookingchange,
       isnull(MAX(rt8_overbookingcurrent), 0.0) as rt8_overbookingcurrent,
       isnull(MAX(rt8_overbookingchange), 0.0) as rt8_overbookingchange,
       isnull(MAX(rt9_overbookingcurrent), 0.0) as rt9_overbookingcurrent,
       isnull(MAX(rt9_overbookingchange), 0.0) as rt9_overbookingchange,
       isnull(MAX(rt10_overbookingcurrent), 0.0) as rt10_overbookingcurrent,
       isnull(MAX(rt10_overbookingchange), 0.0) as rt10_overbookingchange,
       isnull(MAX(rt11_overbookingcurrent), 0.0) as rt11_overbookingcurrent,
       isnull(MAX(rt11_overbookingchange), 0.0) as rt11_overbookingchange,
       isnull(MAX(rt12_overbookingcurrent), 0.0) as rt12_overbookingcurrent,
       isnull(MAX(rt12_overbookingchange), 0.0) as rt12_overbookingchange,
       isnull(MAX(rt13_overbookingcurrent), 0.0) as rt13_overbookingcurrent,
       isnull(MAX(rt13_overbookingchange), 0.0) as rt13_overbookingchange,
       isnull(MAX(rt14_overbookingcurrent), 0.0) as rt14_overbookingcurrent,
       isnull(MAX(rt14_overbookingchange), 0.0) as rt14_overbookingchange,
       isnull(MAX(rt15_overbookingcurrent), 0.0) as rt15_overbookingcurrent,
       isnull(MAX(rt15_overbookingchange), 0.0) as rt15_overbookingchange,
       isnull(MAX(rt16_overbookingcurrent), 0.0) as rt16_overbookingcurrent,
       isnull(MAX(rt16_overbookingchange), 0.0) as rt16_overbookingchange,
       isnull(MAX(rt17_overbookingcurrent), 0.0) as rt17_overbookingcurrent,
       isnull(MAX(rt17_overbookingchange), 0.0) as rt17_overbookingchange,
       isnull(MAX(rt18_overbookingcurrent), 0.0) as rt18_overbookingcurrent,
       isnull(MAX(rt18_overbookingchange), 0.0) as rt18_overbookingchange,
       isnull(MAX(rt19_overbookingcurrent), 0.0) as rt19_overbookingcurrent,
       isnull(MAX(rt19_overbookingchange), 0.0) as rt19_overbookingchange,
       isnull(MAX(rt20_overbookingcurrent), 0.0) as rt20_overbookingcurrent,
       isnull(MAX(rt20_overbookingchange), 0.0) as rt20_overbookingchange,
       isnull(MAX(rt21_overbookingcurrent), 0.0) as rt21_overbookingcurrent,
       isnull(MAX(rt21_overbookingchange), 0.0) as rt21_overbookingchange,
       isnull(MAX(rt22_overbookingcurrent), 0.0) as rt22_overbookingcurrent,
       isnull(MAX(rt22_overbookingchange), 0.0) as rt22_overbookingchange,
       isnull(MAX(rt23_overbookingcurrent), 0.0) as rt23_overbookingcurrent,
       isnull(MAX(rt23_overbookingchange), 0.0) as rt23_overbookingchange,
       isnull(MAX(rt24_overbookingcurrent), 0.0) as rt24_overbookingcurrent,
       isnull(MAX(rt24_overbookingchange), 0.0) as rt24_overbookingchange,
       isnull(MAX(rt25_overbookingcurrent), 0.0) as rt25_overbookingcurrent,
       isnull(MAX(rt25_overbookingchange), 0.0) as rt25_overbookingchange,
       isnull(MAX(rt26_overbookingcurrent), 0.0) as rt26_overbookingcurrent,
       isnull(MAX(rt26_overbookingchange), 0.0) as rt26_overbookingchange,
       isnull(MAX(rt27_overbookingcurrent), 0.0) as rt27_overbookingcurrent,
       isnull(MAX(rt27_overbookingchange), 0.0) as rt27_overbookingchange,
       isnull(MAX(rt28_overbookingcurrent), 0.0) as rt28_overbookingcurrent,
       isnull(MAX(rt28_overbookingchange), 0.0) as rt28_overbookingchange,
       isnull(MAX(rt29_overbookingcurrent), 0.0) as rt29_overbookingcurrent,
       isnull(MAX(rt29_overbookingchange), 0.0) as rt29_overbookingchange,
       isnull(MAX(rt30_overbookingcurrent), 0.0) as rt30_overbookingcurrent,
       isnull(MAX(rt30_overbookingchange), 0.0) as rt30_overbookingchange,
       isnull(MAX(rt31_overbookingcurrent), 0.0) as rt31_overbookingcurrent,
       isnull(MAX(rt31_overbookingchange), 0.0) as rt31_overbookingchange,
       isnull(MAX(rt32_overbookingcurrent), 0.0) as rt32_overbookingcurrent,
       isnull(MAX(rt32_overbookingchange), 0.0) as rt32_overbookingchange,
       isnull(MAX(rt33_overbookingcurrent), 0.0) as rt33_overbookingcurrent,
       isnull(MAX(rt33_overbookingchange), 0.0) as rt33_overbookingchange,
       isnull(MAX(rt34_overbookingcurrent), 0.0) as rt34_overbookingcurrent,
       isnull(MAX(rt34_overbookingchange), 0.0) as rt34_overbookingchange,
       isnull(MAX(rt35_overbookingcurrent), 0.0) as rt35_overbookingcurrent,
       isnull(MAX(rt35_overbookingchange), 0.0) as rt35_overbookingchange,
       isnull(MAX(rt36_overbookingcurrent), 0.0) as rt36_overbookingcurrent,
       isnull(MAX(rt36_overbookingchange), 0.0) as rt36_overbookingchange,
       isnull(MAX(rt37_overbookingcurrent), 0.0) as rt37_overbookingcurrent,
       isnull(MAX(rt37_overbookingchange), 0.0) as rt37_overbookingchange,
       isnull(MAX(rt38_overbookingcurrent), 0.0) as rt38_overbookingcurrent,
       isnull(MAX(rt38_overbookingchange), 0.0) as rt38_overbookingchange,
       isnull(MAX(rt39_overbookingcurrent), 0.0) as rt39_overbookingcurrent,
       isnull(MAX(rt39_overbookingchange), 0.0) as rt39_overbookingchange,
       isnull(MAX(rt40_overbookingcurrent), 0.0) as rt40_overbookingcurrent,
       isnull(MAX(rt40_overbookingchange), 0.0) as rt40_overbookingchange,
       isnull(MAX(rt41_overbookingcurrent), 0.0) as rt41_overbookingcurrent,
       isnull(MAX(rt41_overbookingchange), 0.0) as rt41_overbookingchange,
       isnull(MAX(rt42_overbookingcurrent), 0.0) as rt42_overbookingcurrent,
       isnull(MAX(rt42_overbookingchange), 0.0) as rt42_overbookingchange,
       isnull(MAX(rt43_overbookingcurrent), 0.0) as rt43_overbookingcurrent,
       isnull(MAX(rt43_overbookingchange), 0.0) as rt43_overbookingchange,
       isnull(MAX(rt44_overbookingcurrent), 0.0) as rt44_overbookingcurrent,
       isnull(MAX(rt44_overbookingchange), 0.0) as rt44_overbookingchange,
       isnull(MAX(rt45_overbookingcurrent), 0.0) as rt45_overbookingcurrent,
       isnull(MAX(rt45_overbookingchange), 0.0) as rt45_overbookingchange,
       isnull(MAX(rt46_overbookingcurrent), 0.0) as rt46_overbookingcurrent,
       isnull(MAX(rt46_overbookingchange), 0.0) as rt46_overbookingchange,
       isnull(MAX(rt47_overbookingcurrent), 0.0) as rt47_overbookingcurrent,
       isnull(MAX(rt47_overbookingchange), 0.0) as rt47_overbookingchange,
       isnull(MAX(rt48_overbookingcurrent), 0.0) as rt48_overbookingcurrent,
       isnull(MAX(rt48_overbookingchange), 0.0) as rt48_overbookingchange,
       isnull(MAX(rt49_overbookingcurrent), 0.0) as rt49_overbookingcurrent,
       isnull(MAX(rt49_overbookingchange), 0.0) as rt49_overbookingchange,
       isnull(MAX(rt50_overbookingcurrent), 0.0) as rt50_overbookingcurrent,
       isnull(MAX(rt50_overbookingchange), 0.0) as rt50_overbookingchange,
       --Archana added for group block-, group pickup-Start
       isnull(MAX(rt1_Blocks), 0.0) as rt1_block,
       isnull(MAX(rt1_Blocks_Available), 0.0) as rt1_block_available,
       isnull(MAX(rt1_Blocks_change), 0.0) as rt1_block_change,
       isnull(MAX(rt2_Blocks), 0.0) as rt2_block,
       isnull(MAX(rt2_Blocks_Available), 0.0) as rt2_block_available,
       isnull(MAX(rt2_Blocks_change), 0.0) as rt2_block_change,
       isnull(MAX(rt3_Blocks), 0.0) as rt3_block,
       isnull(MAX(rt3_Blocks_Available), 0.0) as rt3_block_available,
       isnull(MAX(rt3_Blocks_change), 0.0) as rt3_block_change,
       isnull(MAX(rt4_Blocks), 0.0) as rt4_block,
       isnull(MAX(rt4_Blocks_Available), 0.0) as rt4_block_available,
       isnull(MAX(rt4_Blocks_change), 0.0) as rt4_block_change,
       isnull(MAX(rt5_Blocks), 0.0) as rt5_block,
       isnull(MAX(rt5_Blocks_Available), 0.0) as rt5_block_available,
       isnull(MAX(rt5_Blocks_change), 0.0) as rt5_block_change,
       isnull(MAX(rt6_Blocks), 0.0) as rt6_block,
       isnull(MAX(rt6_Blocks_Available), 0.0) as rt6_block_available,
       isnull(MAX(rt6_Blocks_change), 0.0) as rt6_block_change,
       isnull(MAX(rt7_Blocks), 0.0) as rt7_block,
       isnull(MAX(rt7_Blocks_Available), 0.0) as rt7_block_available,
       isnull(MAX(rt7_Blocks_change), 0.0) as rt7_block_change,
       isnull(MAX(rt8_Blocks), 0.0) as rt8_block,
       isnull(MAX(rt8_Blocks_Available), 0.0) as rt8_block_available,
       isnull(MAX(rt8_Blocks_change), 0.0) as rt8_block_change,
       isnull(MAX(rt9_Blocks), 0.0) as rt9_block,
       isnull(MAX(rt9_Blocks_Available), 0.0) as rt9_block_available,
       isnull(MAX(rt9_Blocks_change), 0.0) as rt9_block_change,
       isnull(MAX(rt10_Blocks), 0.0) as rt10_block,
       isnull(MAX(rt10_Blocks_Available), 0.0) as rt10_block_available,
       isnull(MAX(rt10_Blocks_change), 0.0) as rt10_block_change,
       isnull(MAX(rt11_Blocks), 0.0) as rt11_block,
       isnull(MAX(rt11_Blocks_Available), 0.0) as rt11_block_available,
       isnull(MAX(rt11_Blocks_change), 0.0) as rt11_block_change,
       isnull(MAX(rt12_Blocks), 0.0) as rt12_block,
       isnull(MAX(rt12_Blocks_Available), 0.0) as rt12_block_available,
       isnull(MAX(rt12_Blocks_change), 0.0) as rt12_block_change,
       isnull(MAX(rt13_Blocks), 0.0) as rt13_block,
       isnull(MAX(rt13_Blocks_Available), 0.0) as rt13_block_available,
       isnull(MAX(rt13_Blocks_change), 0.0) as rt13_block_change,
       isnull(MAX(rt14_Blocks), 0.0) as rt14_block,
       isnull(MAX(rt14_Blocks_Available), 0.0) as rt14_block_available,
       isnull(MAX(rt14_Blocks_change), 0.0) as rt14_block_change,
       isnull(MAX(rt15_Blocks), 0.0) as rt15_block,
       isnull(MAX(rt15_Blocks_Available), 0.0) as rt15_block_available,
       isnull(MAX(rt15_Blocks_change), 0.0) as rt15_block_change,
       isnull(MAX(rt16_Blocks), 0.0) as rt16_block,
       isnull(MAX(rt16_Blocks_Available), 0.0) as rt16_block_available,
       isnull(MAX(rt16_Blocks_change), 0.0) as rt16_block_change,
       isnull(MAX(rt17_Blocks), 0.0) as rt17_block,
       isnull(MAX(rt17_Blocks_Available), 0.0) as rt17_block_available,
       isnull(MAX(rt17_Blocks_change), 0.0) as rt17_block_change,
       isnull(MAX(rt18_Blocks), 0.0) as rt18_block,
       isnull(MAX(rt18_Blocks_Available), 0.0) as rt18_block_available,
       isnull(MAX(rt18_Blocks_change), 0.0) as rt18_block_change,
       isnull(MAX(rt19_Blocks), 0.0) as rt19_block,
       isnull(MAX(rt19_Blocks_Available), 0.0) as rt19_block_available,
       isnull(MAX(rt19_Blocks_change), 0.0) as rt19_block_change,
       isnull(MAX(rt20_Blocks), 0.0) as rt20_block,
       isnull(MAX(rt20_Blocks_Available), 0.0) as rt20_block_available,
       isnull(MAX(rt20_Blocks_change), 0.0) as rt20_block_change,
       isnull(MAX(rt21_Blocks), 0.0) as rt21_block,
       isnull(MAX(rt21_Blocks_Available), 0.0) as rt21_block_available,
       isnull(MAX(rt21_Blocks_change), 0.0) as rt21_block_change,
       isnull(MAX(rt22_Blocks), 0.0) as rt22_block,
       isnull(MAX(rt22_Blocks_Available), 0.0) as rt22_block_available,
       isnull(MAX(rt22_Blocks_change), 0.0) as rt22_block_change,
       isnull(MAX(rt23_Blocks), 0.0) as rt23_block,
       isnull(MAX(rt23_Blocks_Available), 0.0) as rt23_block_available,
       isnull(MAX(rt23_Blocks_change), 0.0) as rt23_block_change,
       isnull(MAX(rt24_Blocks), 0.0) as rt24_block,
       isnull(MAX(rt24_Blocks_Available), 0.0) as rt24_block_available,
       isnull(MAX(rt24_Blocks_change), 0.0) as rt24_block_change,
       isnull(MAX(rt25_Blocks), 0.0) as rt25_block,
       isnull(MAX(rt25_Blocks_Available), 0.0) as rt25_block_available,
       isnull(MAX(rt25_Blocks_change), 0.0) as rt25_block_change,
       isnull(MAX(rt26_Blocks), 0.0) as rt26_block,
       isnull(MAX(rt26_Blocks_Available), 0.0) as rt26_block_available,
       isnull(MAX(rt26_Blocks_change), 0.0) as rt26_block_change,
       isnull(MAX(rt27_Blocks), 0.0) as rt27_block,
       isnull(MAX(rt27_Blocks_Available), 0.0) as rt27_block_available,
       isnull(MAX(rt27_Blocks_change), 0.0) as rt27_block_change,
       isnull(MAX(rt28_Blocks), 0.0) as rt28_block,
       isnull(MAX(rt28_Blocks_Available), 0.0) as rt28_block_available,
       isnull(MAX(rt28_Blocks_change), 0.0) as rt28_block_change,
       isnull(MAX(rt29_Blocks), 0.0) as rt29_block,
       isnull(MAX(rt29_Blocks_Available), 0.0) as rt29_block_available,
       isnull(MAX(rt29_Blocks_change), 0.0) as rt29_block_change,
       isnull(MAX(rt30_Blocks), 0.0) as rt30_block,
       isnull(MAX(rt30_Blocks_Available), 0.0) as rt30_block_available,
       isnull(MAX(rt30_Blocks_change), 0.0) as rt30_block_change,
       isnull(MAX(rt31_Blocks), 0.0) as rt31_block,
       isnull(MAX(rt31_Blocks_Available), 0.0) as rt31_block_available,
       isnull(MAX(rt31_Blocks_change), 0.0) as rt31_block_change,
       isnull(MAX(rt32_Blocks), 0.0) as rt32_block,
       isnull(MAX(rt32_Blocks_Available), 0.0) as rt32_block_available,
       isnull(MAX(rt32_Blocks_change), 0.0) as rt32_block_change,
       isnull(MAX(rt33_Blocks), 0.0) as rt33_block,
       isnull(MAX(rt33_Blocks_Available), 0.0) as rt33_block_available,
       isnull(MAX(rt33_Blocks_change), 0.0) as rt33_block_change,
       isnull(MAX(rt34_Blocks), 0.0) as rt34_block,
       isnull(MAX(rt34_Blocks_Available), 0.0) as rt34_block_available,
       isnull(MAX(rt34_Blocks_change), 0.0) as rt34_block_change,
       isnull(MAX(rt35_Blocks), 0.0) as rt35_block,
       isnull(MAX(rt35_Blocks_Available), 0.0) as rt35_block_available,
       isnull(MAX(rt35_Blocks_change), 0.0) as rt35_block_change,
       isnull(MAX(rt36_Blocks), 0.0) as rt36_block,
       isnull(MAX(rt36_Blocks_Available), 0.0) as rt36_block_available,
       isnull(MAX(rt36_Blocks_change), 0.0) as rt36_block_change,
       isnull(MAX(rt37_Blocks), 0.0) as rt37_block,
       isnull(MAX(rt37_Blocks_Available), 0.0) as rt37_block_available,
       isnull(MAX(rt37_Blocks_change), 0.0) as rt37_block_change,
       isnull(MAX(rt38_Blocks), 0.0) as rt38_block,
       isnull(MAX(rt38_Blocks_Available), 0.0) as rt38_block_available,
       isnull(MAX(rt38_Blocks_change), 0.0) as rt38_block_change,
       isnull(MAX(rt39_Blocks), 0.0) as rt39_block,
       isnull(MAX(rt39_Blocks_Available), 0.0) as rt39_block_available,
       isnull(MAX(rt39_Blocks_change), 0.0) as rt39_block_change,
       isnull(MAX(rt40_Blocks), 0.0) as rt40_block,
       isnull(MAX(rt40_Blocks_Available), 0.0) as rt40_block_available,
       isnull(MAX(rt40_Blocks_change), 0.0) as rt40_block_change,
       isnull(MAX(rt41_Blocks), 0.0) as rt41_block,
       isnull(MAX(rt41_Blocks_Available), 0.0) as rt41_block_available,
       isnull(MAX(rt41_Blocks_change), 0.0) as rt41_block_change,
       isnull(MAX(rt42_Blocks), 0.0) as rt42_block,
       isnull(MAX(rt42_Blocks_Available), 0.0) as rt42_block_available,
       isnull(MAX(rt42_Blocks_change), 0.0) as rt42_block_change,
       isnull(MAX(rt43_Blocks), 0.0) as rt43_block,
       isnull(MAX(rt43_Blocks_Available), 0.0) as rt43_block_available,
       isnull(MAX(rt43_Blocks_change), 0.0) as rt43_block_change,
       isnull(MAX(rt44_Blocks), 0.0) as rt44_block,
       isnull(MAX(rt44_Blocks_Available), 0.0) as rt44_block_available,
       isnull(MAX(rt44_Blocks_change), 0.0) as rt44_block_change,
       isnull(MAX(rt45_Blocks), 0.0) as rt45_block,
       isnull(MAX(rt45_Blocks_Available), 0.0) as rt45_block_available,
       isnull(MAX(rt45_Blocks_change), 0.0) as rt45_block_change,
       isnull(MAX(rt46_Blocks), 0.0) as rt46_block,
       isnull(MAX(rt46_Blocks_Available), 0.0) as rt46_block_available,
       isnull(MAX(rt46_Blocks_change), 0.0) as rt46_block_change,
       isnull(MAX(rt47_Blocks), 0.0) as rt47_block,
       isnull(MAX(rt47_Blocks_Available), 0.0) as rt47_block_available,
       isnull(MAX(rt47_Blocks_change), 0.0) as rt47_block_change,
       isnull(MAX(rt48_Blocks), 0.0) as rt48_block,
       isnull(MAX(rt48_Blocks_Available), 0.0) as rt48_block_available,
       isnull(MAX(rt48_Blocks_change), 0.0) as rt48_block_change,
       isnull(MAX(rt49_Blocks), 0.0) as rt49_block,
       isnull(MAX(rt49_Blocks_Available), 0.0) as rt49_block_available,
       isnull(MAX(rt49_Blocks_change), 0.0) as rt49_block_change,
       isnull(MAX(rt50_Blocks), 0.0) as rt50_block,
       isnull(MAX(rt50_Blocks_Available), 0.0) as rt50_block_available,
       isnull(MAX(rt50_Blocks_change), 0.0) as rt50_block_change,
       --Archana added for group block-, group pickup-End
       isnull(MAX(cast(rt1_barcurrent as numeric(19, 5))), 0.0) as rt1_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt1_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt1_barchange as numeric(19, 5))), 0.0)
           END as rt1_barchange,
       isnull(MAX(cast(rt2_barcurrent as numeric(19, 5))), 0.0) as rt2_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt2_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt2_barchange as numeric(19, 5))), 0.0)
           END as rt2_barchange,
       isnull(MAX(cast(rt3_barcurrent as numeric(19, 5))), 0.0) as rt3_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt3_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt3_barchange as numeric(19, 5))), 0.0)
           END as rt3_barchange,
       isnull(MAX(cast(rt4_barcurrent as numeric(19, 5))), 0.0) as rt4_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt4_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt4_barchange as numeric(19, 5))), 0.0)
           END as rt4_barchange,
       isnull(MAX(cast(rt5_barcurrent as numeric(19, 5))), 0.0) as rt5_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt5_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt5_barchange as numeric(19, 5))), 0.0)
           END as rt5_barchange,
       isnull(MAX(cast(rt6_barcurrent as numeric(19, 5))), 0.0) as rt6_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt6_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt6_barchange as numeric(19, 5))), 0.0)
           END as rt6_barchange,
       isnull(MAX(cast(rt7_barcurrent as numeric(19, 5))), 0.0) as rt7_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt7_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt7_barchange as numeric(19, 5))), 0.0)
           END as rt7_barchange,
       isnull(MAX(cast(rt8_barcurrent as numeric(19, 5))), 0.0) as rt8_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt8_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt8_barchange as numeric(19, 5))), 0.0)
           END as rt8_barchange,
       isnull(MAX(cast(rt9_barcurrent as numeric(19, 5))), 0.0) as rt9_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt9_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt9_barchange as numeric(19, 5))), 0.0)
           END as rt9_barchange,
       isnull(MAX(cast(rt10_barcurrent as numeric(19, 5))), 0.0) as rt10_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt10_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt10_barchange as numeric(19, 5))), 0.0)
           END as rt10_barchange,
       isnull(MAX(cast(rt11_barcurrent as numeric(19, 5))), 0.0) as rt11_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt11_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt11_barchange as numeric(19, 5))), 0.0)
           END as rt11_barchange,
       isnull(MAX(cast(rt12_barcurrent as numeric(19, 5))), 0.0) as rt12_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt12_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt12_barchange as numeric(19, 5))), 0.0)
           END as rt12_barchange,
       isnull(MAX(cast(rt13_barcurrent as numeric(19, 5))), 0.0) as rt13_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt13_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt13_barchange as numeric(19, 5))), 0.0)
           END as rt13_barchange,
       isnull(MAX(cast(rt14_barcurrent as numeric(19, 5))), 0.0) as rt14_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt14_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt14_barchange as numeric(19, 5))), 0.0)
           END as rt14_barchange,
       isnull(MAX(cast(rt15_barcurrent as numeric(19, 5))), 0.0) as rt15_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt15_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt15_barchange as numeric(19, 5))), 0.0)
           END as rt15_barchange,
       isnull(MAX(cast(rt16_barcurrent as numeric(19, 5))), 0.0) as rt16_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt16_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt16_barchange as numeric(19, 5))), 0.0)
           END as rt16_barchange,
       isnull(MAX(cast(rt17_barcurrent as numeric(19, 5))), 0.0) as rt17_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt17_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt17_barchange as numeric(19, 5))), 0.0)
           END as rt17_barchange,
       isnull(MAX(cast(rt18_barcurrent as numeric(19, 5))), 0.0) as rt18_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt18_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt18_barchange as numeric(19, 5))), 0.0)
           END as rt18_barchange,
       isnull(MAX(cast(rt19_barcurrent as numeric(19, 5))), 0.0) as rt19_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt19_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt19_barchange as numeric(19, 5))), 0.0)
           END as rt19_barchange,
       isnull(MAX(cast(rt20_barcurrent as numeric(19, 5))), 0.0) as rt20_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt20_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt20_barchange as numeric(19, 5))), 0.0)
           END as rt20_barchange,
       isnull(MAX(cast(rt21_barcurrent as numeric(19, 5))), 0.0) as rt21_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt21_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt21_barchange as numeric(19, 5))), 0.0)
           END as rt21_barchange,
       isnull(MAX(cast(rt22_barcurrent as numeric(19, 5))), 0.0) as rt22_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt22_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt22_barchange as numeric(19, 5))), 0.0)
           END as rt22_barchange,
       isnull(MAX(cast(rt23_barcurrent as numeric(19, 5))), 0.0) as rt23_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt23_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt23_barchange as numeric(19, 5))), 0.0)
           END as rt23_barchange,
       isnull(MAX(cast(rt24_barcurrent as numeric(19, 5))), 0.0) as rt24_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt24_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt24_barchange as numeric(19, 5))), 0.0)
           END as rt24_barchange,
       isnull(MAX(cast(rt25_barcurrent as numeric(19, 5))), 0.0) as rt25_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt25_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt25_barchange as numeric(19, 5))), 0.0)
           END as rt25_barchange,
       isnull(MAX(cast(rt26_barcurrent as numeric(19, 5))), 0.0) as rt26_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt26_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt26_barchange as numeric(19, 5))), 0.0)
           END as rt26_barchange,
       isnull(MAX(cast(rt27_barcurrent as numeric(19, 5))), 0.0) as rt27_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt27_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt27_barchange as numeric(19, 5))), 0.0)
           END as rt27_barchange,
       isnull(MAX(cast(rt28_barcurrent as numeric(19, 5))), 0.0) as rt28_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt28_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt28_barchange as numeric(19, 5))), 0.0)
           END as rt28_barchange,
       isnull(MAX(cast(rt29_barcurrent as numeric(19, 5))), 0.0) as rt29_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt29_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt29_barchange as numeric(19, 5))), 0.0)
           END as rt29_barchange,
       isnull(MAX(cast(rt30_barcurrent as numeric(19, 5))), 0.0) as rt30_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt30_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt30_barchange as numeric(19, 5))), 0.0)
           END as rt30_barchange,
       isnull(MAX(cast(rt31_barcurrent as numeric(19, 5))), 0.0) as rt31_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt31_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt31_barchange as numeric(19, 5))), 0.0)
           END as rt31_barchange,
       isnull(MAX(cast(rt32_barcurrent as numeric(19, 5))), 0.0) as rt32_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt32_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt32_barchange as numeric(19, 5))), 0.0)
           END as rt32_barchange,
       isnull(MAX(cast(rt33_barcurrent as numeric(19, 5))), 0.0) as rt33_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt33_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt33_barchange as numeric(19, 5))), 0.0)
           END as rt33_barchange,
       isnull(MAX(cast(rt34_barcurrent as numeric(19, 5))), 0.0) as rt34_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt34_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt34_barchange as numeric(19, 5))), 0.0)
           END as rt34_barchange,
       isnull(MAX(cast(rt35_barcurrent as numeric(19, 5))), 0.0) as rt35_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt35_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt35_barchange as numeric(19, 5))), 0.0)
           END as rt35_barchange,
       isnull(MAX(cast(rt36_barcurrent as numeric(19, 5))), 0.0) as rt36_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt36_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt36_barchange as numeric(19, 5))), 0.0)
           END as rt36_barchange,
       isnull(MAX(cast(rt37_barcurrent as numeric(19, 5))), 0.0) as rt37_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt37_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt37_barchange as numeric(19, 5))), 0.0)
           END as rt37_barchange,
       isnull(MAX(cast(rt38_barcurrent as numeric(19, 5))), 0.0) as rt38_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt38_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt38_barchange as numeric(19, 5))), 0.0)
           END as rt38_barchange,
       isnull(MAX(cast(rt39_barcurrent as numeric(19, 5))), 0.0) as rt39_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt39_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt39_barchange as numeric(19, 5))), 0.0)
           END as rt39_barchange,
       isnull(MAX(cast(rt40_barcurrent as numeric(19, 5))), 0.0) as rt40_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt40_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt40_barchange as numeric(19, 5))), 0.0)
           END as rt40_barchange,
       isnull(MAX(cast(rt41_barcurrent as numeric(19, 5))), 0.0) as rt41_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt41_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt41_barchange as numeric(19, 5))), 0.0)
           END as rt41_barchange,
       isnull(MAX(cast(rt42_barcurrent as numeric(19, 5))), 0.0) as rt42_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt42_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt42_barchange as numeric(19, 5))), 0.0)
           END as rt42_barchange,
       isnull(MAX(cast(rt43_barcurrent as numeric(19, 5))), 0.0) as rt43_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt43_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt43_barchange as numeric(19, 5))), 0.0)
           END as rt43_barchange,
       isnull(MAX(cast(rt44_barcurrent as numeric(19, 5))), 0.0) as rt44_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt44_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt44_barchange as numeric(19, 5))), 0.0)
           END as rt44_barchange,
       isnull(MAX(cast(rt45_barcurrent as numeric(19, 5))), 0.0) as rt45_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt45_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt45_barchange as numeric(19, 5))), 0.0)
           END as rt45_barchange,
       isnull(MAX(cast(rt46_barcurrent as numeric(19, 5))), 0.0) as rt46_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt46_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt46_barchange as numeric(19, 5))), 0.0)
           END as rt46_barchange,
       isnull(MAX(cast(rt47_barcurrent as numeric(19, 5))), 0.0) as rt47_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt47_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt47_barchange as numeric(19, 5))), 0.0)
           END as rt47_barchange,
       isnull(MAX(cast(rt48_barcurrent as numeric(19, 5))), 0.0) as rt48_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt48_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt48_barchange as numeric(19, 5))), 0.0)
           END as rt48_barchange,
       isnull(MAX(cast(rt49_barcurrent as numeric(19, 5))), 0.0) as rt49_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt49_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt49_barchange as numeric(19, 5))), 0.0)
           END as rt49_barchange,
       isnull(MAX(cast(rt50_barcurrent as numeric(19, 5))), 0.0) as rt50_barcurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               isnull(MAX(cast(rt50_barchangeLastOptimize as numeric(19, 5))), 0.0)
           ELSE
               isnull(MAX(cast(rt50_barchange as numeric(19, 5))), 0.0)
           END as rt50_barchange,
       isNull(MAX(rt1_decisionreasontypecurrent), '-') as rt1_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt1_decisionreasontypecurrent),
                                  MAX(rt1_decisionreasontypechange)
                              )
               )
           END as rt1_decisionreasontypechange,
       isNull(MAX(rt2_decisionreasontypecurrent), '-') as rt2_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt2_decisionreasontypecurrent),
                                  MAX(rt2_decisionreasontypechange)
                              )
               )
           END as rt2_decisionreasontypechange,
       isNull(MAX(rt3_decisionreasontypecurrent), '-') as rt3_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt3_decisionreasontypecurrent),
                                  MAX(rt3_decisionreasontypechange)
                              )
               )
           END as rt3_decisionreasontypechange,
       isNull(MAX(rt4_decisionreasontypecurrent), '-') as rt4_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt4_decisionreasontypecurrent),
                                  MAX(rt4_decisionreasontypechange)
                              )
               )
           END as rt4_decisionreasontypechange,
       isNull(MAX(rt5_decisionreasontypecurrent), '-') as rt5_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt5_decisionreasontypecurrent),
                                  MAX(rt5_decisionreasontypechange)
                              )
               )
           END as rt5_decisionreasontypechange,
       isNull(MAX(rt6_decisionreasontypecurrent), '-') as rt6_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt6_decisionreasontypecurrent),
                                  MAX(rt6_decisionreasontypechange)
                              )
               )
           END as rt6_decisionreasontypechange,
       isNull(MAX(rt7_decisionreasontypecurrent), '-') as rt7_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt7_decisionreasontypecurrent),
                                  MAX(rt7_decisionreasontypechange)
                              )
               )
           END as rt7_decisionreasontypechange,
       isNull(MAX(rt8_decisionreasontypecurrent), '-') as rt8_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt8_decisionreasontypecurrent),
                                  MAX(rt8_decisionreasontypechange)
                              )
               )
           END as rt8_decisionreasontypechange,
       isNull(MAX(rt9_decisionreasontypecurrent), '-') as rt9_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt9_decisionreasontypecurrent),
                                  MAX(rt9_decisionreasontypechange)
                              )
               )
           END as rt9_decisionreasontypechange,
       isNull(MAX(rt10_decisionreasontypecurrent), '-') as rt10_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt10_decisionreasontypecurrent),
                                  MAX(rt10_decisionreasontypechange)
                              )
               )
           END as rt10_decisionreasontypechange,
       isNull(MAX(rt11_decisionreasontypecurrent), '-') as rt11_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt11_decisionreasontypecurrent),
                                  MAX(rt11_decisionreasontypechange)
                              )
               )
           END as rt11_decisionreasontypechange,
       isNull(MAX(rt12_decisionreasontypecurrent), '-') as rt12_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt12_decisionreasontypecurrent),
                                  MAX(rt12_decisionreasontypechange)
                              )
               )
           END as rt12_decisionreasontypechange,
       isNull(MAX(rt13_decisionreasontypecurrent), '-') as rt13_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt13_decisionreasontypecurrent),
                                  MAX(rt13_decisionreasontypechange)
                              )
               )
           END as rt13_decisionreasontypechange,
       isNull(MAX(rt14_decisionreasontypecurrent), '-') as rt14_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt14_decisionreasontypecurrent),
                                  MAX(rt14_decisionreasontypechange)
                              )
               )
           END as rt14_decisionreasontypechange,
       isNull(MAX(rt15_decisionreasontypecurrent), '-') as rt15_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt15_decisionreasontypecurrent),
                                  MAX(rt15_decisionreasontypechange)
                              )
               )
           END as rt15_decisionreasontypechange,
       isNull(MAX(rt16_decisionreasontypecurrent), '-') as rt16_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt16_decisionreasontypecurrent),
                                  MAX(rt16_decisionreasontypechange)
                              )
               )
           END as rt16_decisionreasontypechange,
       isNull(MAX(rt17_decisionreasontypecurrent), '-') as rt17_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt17_decisionreasontypecurrent),
                                  MAX(rt17_decisionreasontypechange)
                              )
               )
           END as rt17_decisionreasontypechange,
       isNull(MAX(rt18_decisionreasontypecurrent), '-') as rt18_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt18_decisionreasontypecurrent),
                                  MAX(rt18_decisionreasontypechange)
                              )
               )
           END as rt18_decisionreasontypechange,
       isNull(MAX(rt19_decisionreasontypecurrent), '-') as rt19_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt19_decisionreasontypecurrent),
                                  MAX(rt19_decisionreasontypechange)
                              )
               )
           END as rt19_decisionreasontypechange,
       isNull(MAX(rt20_decisionreasontypecurrent), '-') as rt20_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt20_decisionreasontypecurrent),
                                  MAX(rt20_decisionreasontypechange)
                              )
               )
           END as rt20_decisionreasontypechange,
       isNull(MAX(rt21_decisionreasontypecurrent), '-') as rt21_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt21_decisionreasontypecurrent),
                                  MAX(rt21_decisionreasontypechange)
                              )
               )
           END as rt21_decisionreasontypechange,
       isNull(MAX(rt22_decisionreasontypecurrent), '-') as rt22_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt22_decisionreasontypecurrent),
                                  MAX(rt22_decisionreasontypechange)
                              )
               )
           END as rt22_decisionreasontypechange,
       isNull(MAX(rt23_decisionreasontypecurrent), '-') as rt23_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt23_decisionreasontypecurrent),
                                  MAX(rt23_decisionreasontypechange)
                              )
               )
           END as rt23_decisionreasontypechange,
       isNull(MAX(rt24_decisionreasontypecurrent), '-') as rt24_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt24_decisionreasontypecurrent),
                                  MAX(rt24_decisionreasontypechange)
                              )
               )
           END as rt24_decisionreasontypechange,
       isNull(MAX(rt25_decisionreasontypecurrent), '-') as rt25_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt25_decisionreasontypecurrent),
                                  MAX(rt25_decisionreasontypechange)
                              )
               )
           END as rt25_decisionreasontypechange,
       isNull(MAX(rt26_decisionreasontypecurrent), '-') as rt26_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt26_decisionreasontypecurrent),
                                  MAX(rt26_decisionreasontypechange)
                              )
               )
           END as rt26_decisionreasontypechange,
       isNull(MAX(rt27_decisionreasontypecurrent), '-') as rt27_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt27_decisionreasontypecurrent),
                                  MAX(rt27_decisionreasontypechange)
                              )
               )
           END as rt27_decisionreasontypechange,
       isNull(MAX(rt28_decisionreasontypecurrent), '-') as rt28_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt28_decisionreasontypecurrent),
                                  MAX(rt28_decisionreasontypechange)
                              )
               )
           END as rt28_decisionreasontypechange,
       isNull(MAX(rt29_decisionreasontypecurrent), '-') as rt29_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt29_decisionreasontypecurrent),
                                  MAX(rt29_decisionreasontypechange)
                              )
               )
           END as rt29_decisionreasontypechange,
       isNull(MAX(rt30_decisionreasontypecurrent), '-') as rt30_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt30_decisionreasontypecurrent),
                                  MAX(rt30_decisionreasontypechange)
                              )
               )
           END as rt30_decisionreasontypechange,
       isNull(MAX(rt31_decisionreasontypecurrent), '-') as rt31_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt31_decisionreasontypecurrent),
                                  MAX(rt31_decisionreasontypechange)
                              )
               )
           END as rt31_decisionreasontypechange,
       isNull(MAX(rt32_decisionreasontypecurrent), '-') as rt32_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt32_decisionreasontypecurrent),
                                  MAX(rt32_decisionreasontypechange)
                              )
               )
           END as rt32_decisionreasontypechange,
       isNull(MAX(rt33_decisionreasontypecurrent), '-') as rt33_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt33_decisionreasontypecurrent),
                                  MAX(rt33_decisionreasontypechange)
                              )
               )
           END as rt33_decisionreasontypechange,
       isNull(MAX(rt34_decisionreasontypecurrent), '-') as rt34_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt34_decisionreasontypecurrent),
                                  MAX(rt34_decisionreasontypechange)
                              )
               )
           END as rt34_decisionreasontypechange,
       isNull(MAX(rt35_decisionreasontypecurrent), '-') as rt35_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt35_decisionreasontypecurrent),
                                  MAX(rt35_decisionreasontypechange)
                              )
               )
           END as rt35_decisionreasontypechange,
       isNull(MAX(rt36_decisionreasontypecurrent), '-') as rt36_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt36_decisionreasontypecurrent),
                                  MAX(rt36_decisionreasontypechange)
                              )
               )
           END as rt36_decisionreasontypechange,
       isNull(MAX(rt37_decisionreasontypecurrent), '-') as rt37_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt37_decisionreasontypecurrent),
                                  MAX(rt37_decisionreasontypechange)
                              )
               )
           END as rt37_decisionreasontypechange,
       isNull(MAX(rt38_decisionreasontypecurrent), '-') as rt38_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt38_decisionreasontypecurrent),
                                  MAX(rt38_decisionreasontypechange)
                              )
               )
           END as rt38_decisionreasontypechange,
       isNull(MAX(rt39_decisionreasontypecurrent), '-') as rt39_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt39_decisionreasontypecurrent),
                                  MAX(rt39_decisionreasontypechange)
                              )
               )
           END as rt39_decisionreasontypechange,
       isNull(MAX(rt40_decisionreasontypecurrent), '-') as rt40_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt40_decisionreasontypecurrent),
                                  MAX(rt40_decisionreasontypechange)
                              )
               )
           END as rt40_decisionreasontypechange,
       isNull(MAX(rt41_decisionreasontypecurrent), '-') as rt41_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt41_decisionreasontypecurrent),
                                  MAX(rt41_decisionreasontypechange)
                              )
               )
           END as rt41_decisionreasontypechange,
       isNull(MAX(rt42_decisionreasontypecurrent), '-') as rt42_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt42_decisionreasontypecurrent),
                                  MAX(rt42_decisionreasontypechange)
                              )
               )
           END as rt42_decisionreasontypechange,
       isNull(MAX(rt43_decisionreasontypecurrent), '-') as rt43_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt43_decisionreasontypecurrent),
                                  MAX(rt43_decisionreasontypechange)
                              )
               )
           END as rt43_decisionreasontypechange,
       isNull(MAX(rt44_decisionreasontypecurrent), '-') as rt44_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt44_decisionreasontypecurrent),
                                  MAX(rt44_decisionreasontypechange)
                              )
               )
           END as rt44_decisionreasontypechange,
       isNull(MAX(rt45_decisionreasontypecurrent), '-') as rt45_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt45_decisionreasontypecurrent),
                                  MAX(rt45_decisionreasontypechange)
                              )
               )
           END as rt45_decisionreasontypechange,
       isNull(MAX(rt46_decisionreasontypecurrent), '-') as rt46_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt46_decisionreasontypecurrent),
                                  MAX(rt46_decisionreasontypechange)
                              )
               )
           END as rt46_decisionreasontypechange,
       isNull(MAX(rt47_decisionreasontypecurrent), '-') as rt47_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt47_decisionreasontypecurrent),
                                  MAX(rt47_decisionreasontypechange)
                              )
               )
           END as rt47_decisionreasontypechange,
       isNull(MAX(rt48_decisionreasontypecurrent), '-') as rt48_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt48_decisionreasontypecurrent),
                                  MAX(rt48_decisionreasontypechange)
                              )
               )
           END as rt48_decisionreasontypechange,
       isNull(MAX(rt49_decisionreasontypecurrent), '-') as rt49_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt49_decisionreasontypecurrent),
                                  MAX(rt49_decisionreasontypechange)
                              )
               )
           END as rt49_decisionreasontypechange,
       isNull(MAX(rt50_decisionreasontypecurrent), '-') as rt50_decisionreasontypecurrent,
       CASE @rolling_business_dt
           WHEN 'LAST_OPTIMIZATION' THEN
               NULL
           ELSE
               (
                   select dbo.ufn_determine_change_by_current_and_past_value(
                                  MAX(rt50_decisionreasontypecurrent),
                                  MAX(rt50_decisionreasontypechange)
                              )
               )
           END as rt50_decisionreasontypechange
from
    (
        select occupancy_dt,
               dow,
               special_event,
               outoforder,
               (case accom_type_id
                    when @rt1 then
                        rooms_sold
                   end
                   ) as rt1_rooms_sold,
               (case accom_type_id
                    when @rt1 then
                        rooms_solds_change
                   end
                   ) as rt1_rooms_solds_change,
               (case accom_type_id
                    when @rt2 then
                        rooms_sold
                   end
                   ) as rt2_rooms_sold,
               (case accom_type_id
                    when @rt2 then
                        rooms_solds_change
                   end
                   ) as rt2_rooms_solds_change,
               (case accom_type_id
                    when @rt3 then
                        rooms_sold
                   end
                   ) as rt3_rooms_sold,
               (case accom_type_id
                    when @rt3 then
                        rooms_solds_change
                   end
                   ) as rt3_rooms_solds_change,
               (case accom_type_id
                    when @rt4 then
                        rooms_sold
                   end
                   ) as rt4_rooms_sold,
               (case accom_type_id
                    when @rt4 then
                        rooms_solds_change
                   end
                   ) as rt4_rooms_solds_change,
               (case accom_type_id
                    when @rt5 then
                        rooms_sold
                   end
                   ) as rt5_rooms_sold,
               (case accom_type_id
                    when @rt5 then
                        rooms_solds_change
                   end
                   ) as rt5_rooms_solds_change,
               (case accom_type_id
                    when @rt6 then
                        rooms_sold
                   end
                   ) as rt6_rooms_sold,
               (case accom_type_id
                    when @rt6 then
                        rooms_solds_change
                   end
                   ) as rt6_rooms_solds_change,
               (case accom_type_id
                    when @rt7 then
                        rooms_sold
                   end
                   ) as rt7_rooms_sold,
               (case accom_type_id
                    when @rt7 then
                        rooms_solds_change
                   end
                   ) as rt7_rooms_solds_change,
               (case accom_type_id
                    when @rt8 then
                        rooms_sold
                   end
                   ) as rt8_rooms_sold,
               (case accom_type_id
                    when @rt8 then
                        rooms_solds_change
                   end
                   ) as rt8_rooms_solds_change,
               (case accom_type_id
                    when @rt9 then
                        rooms_sold
                   end
                   ) as rt9_rooms_sold,
               (case accom_type_id
                    when @rt9 then
                        rooms_solds_change
                   end
                   ) as rt9_rooms_solds_change,
               (case accom_type_id
                    when @rt10 then
                        rooms_sold
                   end
                   ) as rt10_rooms_sold,
               (case accom_type_id
                    when @rt10 then
                        rooms_solds_change
                   end
                   ) as rt10_rooms_solds_change,
               (case accom_type_id
                    when @rt11 then
                        rooms_sold
                   end
                   ) as rt11_rooms_sold,
               (case accom_type_id
                    when @rt11 then
                        rooms_solds_change
                   end
                   ) as rt11_rooms_solds_change,
               (case accom_type_id
                    when @rt12 then
                        rooms_sold
                   end
                   ) as rt12_rooms_sold,
               (case accom_type_id
                    when @rt12 then
                        rooms_solds_change
                   end
                   ) as rt12_rooms_solds_change,
               (case accom_type_id
                    when @rt13 then
                        rooms_sold
                   end
                   ) as rt13_rooms_sold,
               (case accom_type_id
                    when @rt13 then
                        rooms_solds_change
                   end
                   ) as rt13_rooms_solds_change,
               (case accom_type_id
                    when @rt14 then
                        rooms_sold
                   end
                   ) as rt14_rooms_sold,
               (case accom_type_id
                    when @rt14 then
                        rooms_solds_change
                   end
                   ) as rt14_rooms_solds_change,
               (case accom_type_id
                    when @rt15 then
                        rooms_sold
                   end
                   ) as rt15_rooms_sold,
               (case accom_type_id
                    when @rt15 then
                        rooms_solds_change
                   end
                   ) as rt15_rooms_solds_change,
               (case accom_type_id
                    when @rt16 then
                        rooms_sold
                   end
                   ) as rt16_rooms_sold,
               (case accom_type_id
                    when @rt16 then
                        rooms_solds_change
                   end
                   ) as rt16_rooms_solds_change,
               (case accom_type_id
                    when @rt17 then
                        rooms_sold
                   end
                   ) as rt17_rooms_sold,
               (case accom_type_id
                    when @rt17 then
                        rooms_solds_change
                   end
                   ) as rt17_rooms_solds_change,
               (case accom_type_id
                    when @rt18 then
                        rooms_sold
                   end
                   ) as rt18_rooms_sold,
               (case accom_type_id
                    when @rt18 then
                        rooms_solds_change
                   end
                   ) as rt18_rooms_solds_change,
               (case accom_type_id
                    when @rt19 then
                        rooms_sold
                   end
                   ) as rt19_rooms_sold,
               (case accom_type_id
                    when @rt19 then
                        rooms_solds_change
                   end
                   ) as rt19_rooms_solds_change,
               (case accom_type_id
                    when @rt20 then
                        rooms_sold
                   end
                   ) as rt20_rooms_sold,
               (case accom_type_id
                    when @rt20 then
                        rooms_solds_change
                   end
                   ) as rt20_rooms_solds_change,
               (case accom_type_id
                    when @rt21 then
                        rooms_sold
                   end
                   ) as rt21_rooms_sold,
               (case accom_type_id
                    when @rt21 then
                        rooms_solds_change
                   end
                   ) as rt21_rooms_solds_change,
               (case accom_type_id
                    when @rt22 then
                        rooms_sold
                   end
                   ) as rt22_rooms_sold,
               (case accom_type_id
                    when @rt22 then
                        rooms_solds_change
                   end
                   ) as rt22_rooms_solds_change,
               (case accom_type_id
                    when @rt23 then
                        rooms_sold
                   end
                   ) as rt23_rooms_sold,
               (case accom_type_id
                    when @rt23 then
                        rooms_solds_change
                   end
                   ) as rt23_rooms_solds_change,
               (case accom_type_id
                    when @rt24 then
                        rooms_sold
                   end
                   ) as rt24_rooms_sold,
               (case accom_type_id
                    when @rt24 then
                        rooms_solds_change
                   end
                   ) as rt24_rooms_solds_change,
               (case accom_type_id
                    when @rt25 then
                        rooms_sold
                   end
                   ) as rt25_rooms_sold,
               (case accom_type_id
                    when @rt25 then
                        rooms_solds_change
                   end
                   ) as rt25_rooms_solds_change,
               (case accom_type_id
                    when @rt26 then
                        rooms_sold
                   end
                   ) as rt26_rooms_sold,
               (case accom_type_id
                    when @rt26 then
                        rooms_solds_change
                   end
                   ) as rt26_rooms_solds_change,
               (case accom_type_id
                    when @rt27 then
                        rooms_sold
                   end
                   ) as rt27_rooms_sold,
               (case accom_type_id
                    when @rt27 then
                        rooms_solds_change
                   end
                   ) as rt27_rooms_solds_change,
               (case accom_type_id
                    when @rt28 then
                        rooms_sold
                   end
                   ) as rt28_rooms_sold,
               (case accom_type_id
                    when @rt28 then
                        rooms_solds_change
                   end
                   ) as rt28_rooms_solds_change,
               (case accom_type_id
                    when @rt29 then
                        rooms_sold
                   end
                   ) as rt29_rooms_sold,
               (case accom_type_id
                    when @rt29 then
                        rooms_solds_change
                   end
                   ) as rt29_rooms_solds_change,
               (case accom_type_id
                    when @rt30 then
                        rooms_sold
                   end
                   ) as rt30_rooms_sold,
               (case accom_type_id
                    when @rt30 then
                        rooms_solds_change
                   end
                   ) as rt30_rooms_solds_change,
               (case accom_type_id
                    when @rt31 then
                        rooms_sold
                   end
                   ) as rt31_rooms_sold,
               (case accom_type_id
                    when @rt31 then
                        rooms_solds_change
                   end
                   ) as rt31_rooms_solds_change,
               (case accom_type_id
                    when @rt32 then
                        rooms_sold
                   end
                   ) as rt32_rooms_sold,
               (case accom_type_id
                    when @rt32 then
                        rooms_solds_change
                   end
                   ) as rt32_rooms_solds_change,
               (case accom_type_id
                    when @rt33 then
                        rooms_sold
                   end
                   ) as rt33_rooms_sold,
               (case accom_type_id
                    when @rt33 then
                        rooms_solds_change
                   end
                   ) as rt33_rooms_solds_change,
               (case accom_type_id
                    when @rt34 then
                        rooms_sold
                   end
                   ) as rt34_rooms_sold,
               (case accom_type_id
                    when @rt34 then
                        rooms_solds_change
                   end
                   ) as rt34_rooms_solds_change,
               (case accom_type_id
                    when @rt35 then
                        rooms_sold
                   end
                   ) as rt35_rooms_sold,
               (case accom_type_id
                    when @rt35 then
                        rooms_solds_change
                   end
                   ) as rt35_rooms_solds_change,
               (case accom_type_id
                    when @rt36 then
                        rooms_sold
                   end
                   ) as rt36_rooms_sold,
               (case accom_type_id
                    when @rt36 then
                        rooms_solds_change
                   end
                   ) as rt36_rooms_solds_change,
               (case accom_type_id
                    when @rt37 then
                        rooms_sold
                   end
                   ) as rt37_rooms_sold,
               (case accom_type_id
                    when @rt37 then
                        rooms_solds_change
                   end
                   ) as rt37_rooms_solds_change,
               (case accom_type_id
                    when @rt38 then
                        rooms_sold
                   end
                   ) as rt38_rooms_sold,
               (case accom_type_id
                    when @rt38 then
                        rooms_solds_change
                   end
                   ) as rt38_rooms_solds_change,
               (case accom_type_id
                    when @rt39 then
                        rooms_sold
                   end
                   ) as rt39_rooms_sold,
               (case accom_type_id
                    when @rt39 then
                        rooms_solds_change
                   end
                   ) as rt39_rooms_solds_change,
               (case accom_type_id
                    when @rt40 then
                        rooms_sold
                   end
                   ) as rt40_rooms_sold,
               (case accom_type_id
                    when @rt40 then
                        rooms_solds_change
                   end
                   ) as rt40_rooms_solds_change,
               (case accom_type_id
                    when @rt41 then
                        rooms_sold
                   end
                   ) as rt41_rooms_sold,
               (case accom_type_id
                    when @rt41 then
                        rooms_solds_change
                   end
                   ) as rt41_rooms_solds_change,
               (case accom_type_id
                    when @rt42 then
                        rooms_sold
                   end
                   ) as rt42_rooms_sold,
               (case accom_type_id
                    when @rt42 then
                        rooms_solds_change
                   end
                   ) as rt42_rooms_solds_change,
               (case accom_type_id
                    when @rt43 then
                        rooms_sold
                   end
                   ) as rt43_rooms_sold,
               (case accom_type_id
                    when @rt43 then
                        rooms_solds_change
                   end
                   ) as rt43_rooms_solds_change,
               (case accom_type_id
                    when @rt44 then
                        rooms_sold
                   end
                   ) as rt44_rooms_sold,
               (case accom_type_id
                    when @rt44 then
                        rooms_solds_change
                   end
                   ) as rt44_rooms_solds_change,
               (case accom_type_id
                    when @rt45 then
                        rooms_sold
                   end
                   ) as rt45_rooms_sold,
               (case accom_type_id
                    when @rt45 then
                        rooms_solds_change
                   end
                   ) as rt45_rooms_solds_change,
               (case accom_type_id
                    when @rt46 then
                        rooms_sold
                   end
                   ) as rt46_rooms_sold,
               (case accom_type_id
                    when @rt46 then
                        rooms_solds_change
                   end
                   ) as rt46_rooms_solds_change,
               (case accom_type_id
                    when @rt47 then
                        rooms_sold
                   end
                   ) as rt47_rooms_sold,
               (case accom_type_id
                    when @rt47 then
                        rooms_solds_change
                   end
                   ) as rt47_rooms_solds_change,
               (case accom_type_id
                    when @rt48 then
                        rooms_sold
                   end
                   ) as rt48_rooms_sold,
               (case accom_type_id
                    when @rt48 then
                        rooms_solds_change
                   end
                   ) as rt48_rooms_solds_change,
               (case accom_type_id
                    when @rt49 then
                        rooms_sold
                   end
                   ) as rt49_rooms_sold,
               (case accom_type_id
                    when @rt49 then
                        rooms_solds_change
                   end
                   ) as rt49_rooms_solds_change,
               (case accom_type_id
                    when @rt50 then
                        rooms_sold
                   end
                   ) as rt50_rooms_sold,
               (case accom_type_id
                    when @rt50 then
                        rooms_solds_change
                   end
                   ) as rt50_rooms_solds_change,
               (case accom_type_id
                    when @rt1 then
                        occupancy_nbr
                   end
                   ) as rt1_occupancy_nbr,
               (case accom_type_id
                    when @rt1 then
                        occupancy_change
                   end
                   ) as rt1_occupancy_change,
               (case accom_type_id
                    when @rt2 then
                        occupancy_nbr
                   end
                   ) as rt2_occupancy_nbr,
               (case accom_type_id
                    when @rt2 then
                        occupancy_change
                   end
                   ) as rt2_occupancy_change,
               (case accom_type_id
                    when @rt3 then
                        occupancy_nbr
                   end
                   ) as rt3_occupancy_nbr,
               (case accom_type_id
                    when @rt3 then
                        occupancy_change
                   end
                   ) as rt3_occupancy_change,
               (case accom_type_id
                    when @rt4 then
                        occupancy_nbr
                   end
                   ) as rt4_occupancy_nbr,
               (case accom_type_id
                    when @rt4 then
                        occupancy_change
                   end
                   ) as rt4_occupancy_change,
               (case accom_type_id
                    when @rt5 then
                        occupancy_nbr
                   end
                   ) as rt5_occupancy_nbr,
               (case accom_type_id
                    when @rt5 then
                        occupancy_change
                   end
                   ) as rt5_occupancy_change,
               (case accom_type_id
                    when @rt6 then
                        occupancy_nbr
                   end
                   ) as rt6_occupancy_nbr,
               (case accom_type_id
                    when @rt6 then
                        occupancy_change
                   end
                   ) as rt6_occupancy_change,
               (case accom_type_id
                    when @rt7 then
                        occupancy_nbr
                   end
                   ) as rt7_occupancy_nbr,
               (case accom_type_id
                    when @rt7 then
                        occupancy_change
                   end
                   ) as rt7_occupancy_change,
               (case accom_type_id
                    when @rt8 then
                        occupancy_nbr
                   end
                   ) as rt8_occupancy_nbr,
               (case accom_type_id
                    when @rt8 then
                        occupancy_change
                   end
                   ) as rt8_occupancy_change,
               (case accom_type_id
                    when @rt9 then
                        occupancy_nbr
                   end
                   ) as rt9_occupancy_nbr,
               (case accom_type_id
                    when @rt9 then
                        occupancy_change
                   end
                   ) as rt9_occupancy_change,
               (case accom_type_id
                    when @rt10 then
                        occupancy_nbr
                   end
                   ) as rt10_occupancy_nbr,
               (case accom_type_id
                    when @rt10 then
                        occupancy_change
                   end
                   ) as rt10_occupancy_change,
               (case accom_type_id
                    when @rt11 then
                        occupancy_nbr
                   end
                   ) as rt11_occupancy_nbr,
               (case accom_type_id
                    when @rt11 then
                        occupancy_change
                   end
                   ) as rt11_occupancy_change,
               (case accom_type_id
                    when @rt12 then
                        occupancy_nbr
                   end
                   ) as rt12_occupancy_nbr,
               (case accom_type_id
                    when @rt12 then
                        occupancy_change
                   end
                   ) as rt12_occupancy_change,
               (case accom_type_id
                    when @rt13 then
                        occupancy_nbr
                   end
                   ) as rt13_occupancy_nbr,
               (case accom_type_id
                    when @rt13 then
                        occupancy_change
                   end
                   ) as rt13_occupancy_change,
               (case accom_type_id
                    when @rt14 then
                        occupancy_nbr
                   end
                   ) as rt14_occupancy_nbr,
               (case accom_type_id
                    when @rt14 then
                        occupancy_change
                   end
                   ) as rt14_occupancy_change,
               (case accom_type_id
                    when @rt15 then
                        occupancy_nbr
                   end
                   ) as rt15_occupancy_nbr,
               (case accom_type_id
                    when @rt15 then
                        occupancy_change
                   end
                   ) as rt15_occupancy_change,
               (case accom_type_id
                    when @rt16 then
                        occupancy_nbr
                   end
                   ) as rt16_occupancy_nbr,
               (case accom_type_id
                    when @rt16 then
                        occupancy_change
                   end
                   ) as rt16_occupancy_change,
               (case accom_type_id
                    when @rt17 then
                        occupancy_nbr
                   end
                   ) as rt17_occupancy_nbr,
               (case accom_type_id
                    when @rt17 then
                        occupancy_change
                   end
                   ) as rt17_occupancy_change,
               (case accom_type_id
                    when @rt18 then
                        occupancy_nbr
                   end
                   ) as rt18_occupancy_nbr,
               (case accom_type_id
                    when @rt18 then
                        occupancy_change
                   end
                   ) as rt18_occupancy_change,
               (case accom_type_id
                    when @rt19 then
                        occupancy_nbr
                   end
                   ) as rt19_occupancy_nbr,
               (case accom_type_id
                    when @rt19 then
                        occupancy_change
                   end
                   ) as rt19_occupancy_change,
               (case accom_type_id
                    when @rt20 then
                        occupancy_nbr
                   end
                   ) as rt20_occupancy_nbr,
               (case accom_type_id
                    when @rt20 then
                        occupancy_change
                   end
                   ) as rt20_occupancy_change,
               (case accom_type_id
                    when @rt21 then
                        occupancy_nbr
                   end
                   ) as rt21_occupancy_nbr,
               (case accom_type_id
                    when @rt21 then
                        occupancy_change
                   end
                   ) as rt21_occupancy_change,
               (case accom_type_id
                    when @rt22 then
                        occupancy_nbr
                   end
                   ) as rt22_occupancy_nbr,
               (case accom_type_id
                    when @rt22 then
                        occupancy_change
                   end
                   ) as rt22_occupancy_change,
               (case accom_type_id
                    when @rt23 then
                        occupancy_nbr
                   end
                   ) as rt23_occupancy_nbr,
               (case accom_type_id
                    when @rt23 then
                        occupancy_change
                   end
                   ) as rt23_occupancy_change,
               (case accom_type_id
                    when @rt24 then
                        occupancy_nbr
                   end
                   ) as rt24_occupancy_nbr,
               (case accom_type_id
                    when @rt24 then
                        occupancy_change
                   end
                   ) as rt24_occupancy_change,
               (case accom_type_id
                    when @rt25 then
                        occupancy_nbr
                   end
                   ) as rt25_occupancy_nbr,
               (case accom_type_id
                    when @rt25 then
                        occupancy_change
                   end
                   ) as rt25_occupancy_change,
               (case accom_type_id
                    when @rt26 then
                        occupancy_nbr
                   end
                   ) as rt26_occupancy_nbr,
               (case accom_type_id
                    when @rt26 then
                        occupancy_change
                   end
                   ) as rt26_occupancy_change,
               (case accom_type_id
                    when @rt27 then
                        occupancy_nbr
                   end
                   ) as rt27_occupancy_nbr,
               (case accom_type_id
                    when @rt27 then
                        occupancy_change
                   end
                   ) as rt27_occupancy_change,
               (case accom_type_id
                    when @rt28 then
                        occupancy_nbr
                   end
                   ) as rt28_occupancy_nbr,
               (case accom_type_id
                    when @rt28 then
                        occupancy_change
                   end
                   ) as rt28_occupancy_change,
               (case accom_type_id
                    when @rt29 then
                        occupancy_nbr
                   end
                   ) as rt29_occupancy_nbr,
               (case accom_type_id
                    when @rt29 then
                        occupancy_change
                   end
                   ) as rt29_occupancy_change,
               (case accom_type_id
                    when @rt30 then
                        occupancy_nbr
                   end
                   ) as rt30_occupancy_nbr,
               (case accom_type_id
                    when @rt30 then
                        occupancy_change
                   end
                   ) as rt30_occupancy_change,
               (case accom_type_id
                    when @rt31 then
                        occupancy_nbr
                   end
                   ) as rt31_occupancy_nbr,
               (case accom_type_id
                    when @rt31 then
                        occupancy_change
                   end
                   ) as rt31_occupancy_change,
               (case accom_type_id
                    when @rt32 then
                        occupancy_nbr
                   end
                   ) as rt32_occupancy_nbr,
               (case accom_type_id
                    when @rt32 then
                        occupancy_change
                   end
                   ) as rt32_occupancy_change,
               (case accom_type_id
                    when @rt33 then
                        occupancy_nbr
                   end
                   ) as rt33_occupancy_nbr,
               (case accom_type_id
                    when @rt33 then
                        occupancy_change
                   end
                   ) as rt33_occupancy_change,
               (case accom_type_id
                    when @rt34 then
                        occupancy_nbr
                   end
                   ) as rt34_occupancy_nbr,
               (case accom_type_id
                    when @rt34 then
                        occupancy_change
                   end
                   ) as rt34_occupancy_change,
               (case accom_type_id
                    when @rt35 then
                        occupancy_nbr
                   end
                   ) as rt35_occupancy_nbr,
               (case accom_type_id
                    when @rt35 then
                        occupancy_change
                   end
                   ) as rt35_occupancy_change,
               (case accom_type_id
                    when @rt36 then
                        occupancy_nbr
                   end
                   ) as rt36_occupancy_nbr,
               (case accom_type_id
                    when @rt36 then
                        occupancy_change
                   end
                   ) as rt36_occupancy_change,
               (case accom_type_id
                    when @rt37 then
                        occupancy_nbr
                   end
                   ) as rt37_occupancy_nbr,
               (case accom_type_id
                    when @rt37 then
                        occupancy_change
                   end
                   ) as rt37_occupancy_change,
               (case accom_type_id
                    when @rt38 then
                        occupancy_nbr
                   end
                   ) as rt38_occupancy_nbr,
               (case accom_type_id
                    when @rt38 then
                        occupancy_change
                   end
                   ) as rt38_occupancy_change,
               (case accom_type_id
                    when @rt39 then
                        occupancy_nbr
                   end
                   ) as rt39_occupancy_nbr,
               (case accom_type_id
                    when @rt39 then
                        occupancy_change
                   end
                   ) as rt39_occupancy_change,
               (case accom_type_id
                    when @rt40 then
                        occupancy_nbr
                   end
                   ) as rt40_occupancy_nbr,
               (case accom_type_id
                    when @rt40 then
                        occupancy_change
                   end
                   ) as rt40_occupancy_change,
               (case accom_type_id
                    when @rt41 then
                        occupancy_nbr
                   end
                   ) as rt41_occupancy_nbr,
               (case accom_type_id
                    when @rt41 then
                        occupancy_change
                   end
                   ) as rt41_occupancy_change,
               (case accom_type_id
                    when @rt42 then
                        occupancy_nbr
                   end
                   ) as rt42_occupancy_nbr,
               (case accom_type_id
                    when @rt42 then
                        occupancy_change
                   end
                   ) as rt42_occupancy_change,
               (case accom_type_id
                    when @rt43 then
                        occupancy_nbr
                   end
                   ) as rt43_occupancy_nbr,
               (case accom_type_id
                    when @rt43 then
                        occupancy_change
                   end
                   ) as rt43_occupancy_change,
               (case accom_type_id
                    when @rt44 then
                        occupancy_nbr
                   end
                   ) as rt44_occupancy_nbr,
               (case accom_type_id
                    when @rt44 then
                        occupancy_change
                   end
                   ) as rt44_occupancy_change,
               (case accom_type_id
                    when @rt45 then
                        occupancy_nbr
                   end
                   ) as rt45_occupancy_nbr,
               (case accom_type_id
                    when @rt45 then
                        occupancy_change
                   end
                   ) as rt45_occupancy_change,
               (case accom_type_id
                    when @rt46 then
                        occupancy_nbr
                   end
                   ) as rt46_occupancy_nbr,
               (case accom_type_id
                    when @rt46 then
                        occupancy_change
                   end
                   ) as rt46_occupancy_change,
               (case accom_type_id
                    when @rt47 then
                        occupancy_nbr
                   end
                   ) as rt47_occupancy_nbr,
               (case accom_type_id
                    when @rt47 then
                        occupancy_change
                   end
                   ) as rt47_occupancy_change,
               (case accom_type_id
                    when @rt48 then
                        occupancy_nbr
                   end
                   ) as rt48_occupancy_nbr,
               (case accom_type_id
                    when @rt48 then
                        occupancy_change
                   end
                   ) as rt48_occupancy_change,
               (case accom_type_id
                    when @rt49 then
                        occupancy_nbr
                   end
                   ) as rt49_occupancy_nbr,
               (case accom_type_id
                    when @rt49 then
                        occupancy_change
                   end
                   ) as rt49_occupancy_change,
               (case accom_type_id
                    when @rt50 then
                        occupancy_nbr
                   end
                   ) as rt50_occupancy_nbr,
               (case accom_type_id
                    when @rt50 then
                        occupancy_change
                   end
                   ) as rt50_occupancy_change,
               (case accom_type_id
                    when @rt1 then
                        occupancy_percent
                   end
                   ) as rt1_occupancy_percent,
               (case accom_type_id
                    when @rt1 then
                        occupancy_perc_change
                   end
                   ) as rt1_occupancy_perc_change,
               (case accom_type_id
                    when @rt2 then
                        occupancy_percent
                   end
                   ) as rt2_occupancy_percent,
               (case accom_type_id
                    when @rt2 then
                        occupancy_perc_change
                   end
                   ) as rt2_occupancy_perc_change,
               (case accom_type_id
                    when @rt3 then
                        occupancy_percent
                   end
                   ) as rt3_occupancy_percent,
               (case accom_type_id
                    when @rt3 then
                        occupancy_perc_change
                   end
                   ) as rt3_occupancy_perc_change,
               (case accom_type_id
                    when @rt4 then
                        occupancy_percent
                   end
                   ) as rt4_occupancy_percent,
               (case accom_type_id
                    when @rt4 then
                        occupancy_perc_change
                   end
                   ) as rt4_occupancy_perc_change,
               (case accom_type_id
                    when @rt5 then
                        occupancy_percent
                   end
                   ) as rt5_occupancy_percent,
               (case accom_type_id
                    when @rt5 then
                        occupancy_perc_change
                   end
                   ) as rt5_occupancy_perc_change,
               (case accom_type_id
                    when @rt6 then
                        occupancy_percent
                   end
                   ) as rt6_occupancy_percent,
               (case accom_type_id
                    when @rt6 then
                        occupancy_perc_change
                   end
                   ) as rt6_occupancy_perc_change,
               (case accom_type_id
                    when @rt7 then
                        occupancy_percent
                   end
                   ) as rt7_occupancy_percent,
               (case accom_type_id
                    when @rt7 then
                        occupancy_perc_change
                   end
                   ) as rt7_occupancy_perc_change,
               (case accom_type_id
                    when @rt8 then
                        occupancy_percent
                   end
                   ) as rt8_occupancy_percent,
               (case accom_type_id
                    when @rt8 then
                        occupancy_perc_change
                   end
                   ) as rt8_occupancy_perc_change,
               (case accom_type_id
                    when @rt9 then
                        occupancy_percent
                   end
                   ) as rt9_occupancy_percent,
               (case accom_type_id
                    when @rt9 then
                        occupancy_perc_change
                   end
                   ) as rt9_occupancy_perc_change,
               (case accom_type_id
                    when @rt10 then
                        occupancy_percent
                   end
                   ) as rt10_occupancy_percent,
               (case accom_type_id
                    when @rt10 then
                        occupancy_perc_change
                   end
                   ) as rt10_occupancy_perc_change,
               (case accom_type_id
                    when @rt11 then
                        occupancy_percent
                   end
                   ) as rt11_occupancy_percent,
               (case accom_type_id
                    when @rt11 then
                        occupancy_perc_change
                   end
                   ) as rt11_occupancy_perc_change,
               (case accom_type_id
                    when @rt12 then
                        occupancy_percent
                   end
                   ) as rt12_occupancy_percent,
               (case accom_type_id
                    when @rt12 then
                        occupancy_perc_change
                   end
                   ) as rt12_occupancy_perc_change,
               (case accom_type_id
                    when @rt13 then
                        occupancy_percent
                   end
                   ) as rt13_occupancy_percent,
               (case accom_type_id
                    when @rt13 then
                        occupancy_perc_change
                   end
                   ) as rt13_occupancy_perc_change,
               (case accom_type_id
                    when @rt14 then
                        occupancy_percent
                   end
                   ) as rt14_occupancy_percent,
               (case accom_type_id
                    when @rt14 then
                        occupancy_perc_change
                   end
                   ) as rt14_occupancy_perc_change,
               (case accom_type_id
                    when @rt15 then
                        occupancy_percent
                   end
                   ) as rt15_occupancy_percent,
               (case accom_type_id
                    when @rt15 then
                        occupancy_perc_change
                   end
                   ) as rt15_occupancy_perc_change,
               (case accom_type_id
                    when @rt16 then
                        occupancy_percent
                   end
                   ) as rt16_occupancy_percent,
               (case accom_type_id
                    when @rt16 then
                        occupancy_perc_change
                   end
                   ) as rt16_occupancy_perc_change,
               (case accom_type_id
                    when @rt17 then
                        occupancy_percent
                   end
                   ) as rt17_occupancy_percent,
               (case accom_type_id
                    when @rt17 then
                        occupancy_perc_change
                   end
                   ) as rt17_occupancy_perc_change,
               (case accom_type_id
                    when @rt18 then
                        occupancy_percent
                   end
                   ) as rt18_occupancy_percent,
               (case accom_type_id
                    when @rt18 then
                        occupancy_perc_change
                   end
                   ) as rt18_occupancy_perc_change,
               (case accom_type_id
                    when @rt19 then
                        occupancy_percent
                   end
                   ) as rt19_occupancy_percent,
               (case accom_type_id
                    when @rt19 then
                        occupancy_perc_change
                   end
                   ) as rt19_occupancy_perc_change,
               (case accom_type_id
                    when @rt20 then
                        occupancy_percent
                   end
                   ) as rt20_occupancy_percent,
               (case accom_type_id
                    when @rt20 then
                        occupancy_perc_change
                   end
                   ) as rt20_occupancy_perc_change,
               (case accom_type_id
                    when @rt21 then
                        occupancy_percent
                   end
                   ) as rt21_occupancy_percent,
               (case accom_type_id
                    when @rt21 then
                        occupancy_perc_change
                   end
                   ) as rt21_occupancy_perc_change,
               (case accom_type_id
                    when @rt22 then
                        occupancy_percent
                   end
                   ) as rt22_occupancy_percent,
               (case accom_type_id
                    when @rt22 then
                        occupancy_perc_change
                   end
                   ) as rt22_occupancy_perc_change,
               (case accom_type_id
                    when @rt23 then
                        occupancy_percent
                   end
                   ) as rt23_occupancy_percent,
               (case accom_type_id
                    when @rt23 then
                        occupancy_perc_change
                   end
                   ) as rt23_occupancy_perc_change,
               (case accom_type_id
                    when @rt24 then
                        occupancy_percent
                   end
                   ) as rt24_occupancy_percent,
               (case accom_type_id
                    when @rt24 then
                        occupancy_perc_change
                   end
                   ) as rt24_occupancy_perc_change,
               (case accom_type_id
                    when @rt25 then
                        occupancy_percent
                   end
                   ) as rt25_occupancy_percent,
               (case accom_type_id
                    when @rt25 then
                        occupancy_perc_change
                   end
                   ) as rt25_occupancy_perc_change,
               (case accom_type_id
                    when @rt26 then
                        occupancy_percent
                   end
                   ) as rt26_occupancy_percent,
               (case accom_type_id
                    when @rt26 then
                        occupancy_perc_change
                   end
                   ) as rt26_occupancy_perc_change,
               (case accom_type_id
                    when @rt27 then
                        occupancy_percent
                   end
                   ) as rt27_occupancy_percent,
               (case accom_type_id
                    when @rt27 then
                        occupancy_perc_change
                   end
                   ) as rt27_occupancy_perc_change,
               (case accom_type_id
                    when @rt28 then
                        occupancy_percent
                   end
                   ) as rt28_occupancy_percent,
               (case accom_type_id
                    when @rt28 then
                        occupancy_perc_change
                   end
                   ) as rt28_occupancy_perc_change,
               (case accom_type_id
                    when @rt29 then
                        occupancy_percent
                   end
                   ) as rt29_occupancy_percent,
               (case accom_type_id
                    when @rt29 then
                        occupancy_perc_change
                   end
                   ) as rt29_occupancy_perc_change,
               (case accom_type_id
                    when @rt30 then
                        occupancy_percent
                   end
                   ) as rt30_occupancy_percent,
               (case accom_type_id
                    when @rt30 then
                        occupancy_perc_change
                   end
                   ) as rt30_occupancy_perc_change,
               (case accom_type_id
                    when @rt31 then
                        occupancy_percent
                   end
                   ) as rt31_occupancy_percent,
               (case accom_type_id
                    when @rt31 then
                        occupancy_perc_change
                   end
                   ) as rt31_occupancy_perc_change,
               (case accom_type_id
                    when @rt32 then
                        occupancy_percent
                   end
                   ) as rt32_occupancy_percent,
               (case accom_type_id
                    when @rt32 then
                        occupancy_perc_change
                   end
                   ) as rt32_occupancy_perc_change,
               (case accom_type_id
                    when @rt33 then
                        occupancy_percent
                   end
                   ) as rt33_occupancy_percent,
               (case accom_type_id
                    when @rt33 then
                        occupancy_perc_change
                   end
                   ) as rt33_occupancy_perc_change,
               (case accom_type_id
                    when @rt34 then
                        occupancy_percent
                   end
                   ) as rt34_occupancy_percent,
               (case accom_type_id
                    when @rt34 then
                        occupancy_perc_change
                   end
                   ) as rt34_occupancy_perc_change,
               (case accom_type_id
                    when @rt35 then
                        occupancy_percent
                   end
                   ) as rt35_occupancy_percent,
               (case accom_type_id
                    when @rt35 then
                        occupancy_perc_change
                   end
                   ) as rt35_occupancy_perc_change,
               (case accom_type_id
                    when @rt36 then
                        occupancy_percent
                   end
                   ) as rt36_occupancy_percent,
               (case accom_type_id
                    when @rt36 then
                        occupancy_perc_change
                   end
                   ) as rt36_occupancy_perc_change,
               (case accom_type_id
                    when @rt37 then
                        occupancy_percent
                   end
                   ) as rt37_occupancy_percent,
               (case accom_type_id
                    when @rt37 then
                        occupancy_perc_change
                   end
                   ) as rt37_occupancy_perc_change,
               (case accom_type_id
                    when @rt38 then
                        occupancy_percent
                   end
                   ) as rt38_occupancy_percent,
               (case accom_type_id
                    when @rt38 then
                        occupancy_perc_change
                   end
                   ) as rt38_occupancy_perc_change,
               (case accom_type_id
                    when @rt39 then
                        occupancy_percent
                   end
                   ) as rt39_occupancy_percent,
               (case accom_type_id
                    when @rt39 then
                        occupancy_perc_change
                   end
                   ) as rt39_occupancy_perc_change,
               (case accom_type_id
                    when @rt40 then
                        occupancy_percent
                   end
                   ) as rt40_occupancy_percent,
               (case accom_type_id
                    when @rt40 then
                        occupancy_perc_change
                   end
                   ) as rt40_occupancy_perc_change,
               (case accom_type_id
                    when @rt41 then
                        occupancy_percent
                   end
                   ) as rt41_occupancy_percent,
               (case accom_type_id
                    when @rt41 then
                        occupancy_perc_change
                   end
                   ) as rt41_occupancy_perc_change,
               (case accom_type_id
                    when @rt42 then
                        occupancy_percent
                   end
                   ) as rt42_occupancy_percent,
               (case accom_type_id
                    when @rt42 then
                        occupancy_perc_change
                   end
                   ) as rt42_occupancy_perc_change,
               (case accom_type_id
                    when @rt43 then
                        occupancy_percent
                   end
                   ) as rt43_occupancy_percent,
               (case accom_type_id
                    when @rt43 then
                        occupancy_perc_change
                   end
                   ) as rt43_occupancy_perc_change,
               (case accom_type_id
                    when @rt44 then
                        occupancy_percent
                   end
                   ) as rt44_occupancy_percent,
               (case accom_type_id
                    when @rt44 then
                        occupancy_perc_change
                   end
                   ) as rt44_occupancy_perc_change,
               (case accom_type_id
                    when @rt45 then
                        occupancy_percent
                   end
                   ) as rt45_occupancy_percent,
               (case accom_type_id
                    when @rt45 then
                        occupancy_perc_change
                   end
                   ) as rt45_occupancy_perc_change,
               (case accom_type_id
                    when @rt46 then
                        occupancy_percent
                   end
                   ) as rt46_occupancy_percent,
               (case accom_type_id
                    when @rt46 then
                        occupancy_perc_change
                   end
                   ) as rt46_occupancy_perc_change,
               (case accom_type_id
                    when @rt47 then
                        occupancy_percent
                   end
                   ) as rt47_occupancy_percent,
               (case accom_type_id
                    when @rt47 then
                        occupancy_perc_change
                   end
                   ) as rt47_occupancy_perc_change,
               (case accom_type_id
                    when @rt48 then
                        occupancy_percent
                   end
                   ) as rt48_occupancy_percent,
               (case accom_type_id
                    when @rt48 then
                        occupancy_perc_change
                   end
                   ) as rt48_occupancy_perc_change,
               (case accom_type_id
                    when @rt49 then
                        occupancy_percent
                   end
                   ) as rt49_occupancy_percent,
               (case accom_type_id
                    when @rt49 then
                        occupancy_perc_change
                   end
                   ) as rt49_occupancy_perc_change,
               (case accom_type_id
                    when @rt50 then
                        occupancy_percent
                   end
                   ) as rt50_occupancy_percent,
               (case accom_type_id
                    when @rt50 then
                        occupancy_perc_change
                   end
                   ) as rt50_occupancy_perc_change,
               (case accom_type_id
                    when @rt1 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt1_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt1 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt1_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt2 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt2_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt2 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt2_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt3 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt3_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt3 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt3_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt4 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt4_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt4 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt4_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt5 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt5_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt5 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt5_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt6 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt6_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt6 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt6_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt7 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt7_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt7 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt7_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt8 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt8_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt8 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt8_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt9 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt9_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt9 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt9_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt10 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt10_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt10 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt10_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt11 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt11_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt11 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt11_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt12 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt12_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt12 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt12_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt13 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt13_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt13 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt13_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt14 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt14_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt14 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt14_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt15 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt15_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt15 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt15_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt16 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt16_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt16 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt16_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt17 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt17_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt17 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt17_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt18 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt18_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt18 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt18_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt19 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt19_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt19 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt19_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt20 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt20_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt20 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt20_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt21 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt21_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt21 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt21_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt22 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt22_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt22 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt22_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt23 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt23_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt23 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt23_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt24 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt24_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt24 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt24_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt25 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt25_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt25 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt25_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt26 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt26_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt26 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt26_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt27 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt27_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt27 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt27_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt28 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt28_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt28 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt28_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt29 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt29_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt29 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt29_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt30 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt30_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt30 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt30_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt31 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt31_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt31 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt31_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt32 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt32_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt32 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt32_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt33 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt33_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt33 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt33_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt34 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt34_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt34 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt34_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt35 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt35_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt35 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt35_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt36 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt36_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt36 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt36_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt37 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt37_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt37 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt37_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt38 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt38_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt38 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt38_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt39 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt39_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt39 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt39_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt40 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt40_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt40 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt40_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt41 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt41_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt41 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt41_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt42 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt42_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt42 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt42_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt43 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt43_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt43 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt43_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt44 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt44_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt44 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt44_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt45 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt45_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt45 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt45_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt46 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt46_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt46 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt46_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt47 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt47_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt47 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt47_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt48 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt48_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt48 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt48_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt49 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt49_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt49 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt49_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt50 then
                        occupancy_percent_without_ooo
                   end
                   ) as rt50_occupancy_percent_without_ooo,
               (case accom_type_id
                    when @rt50 then
                        occupancy_perc_change_without_ooo
                   end
                   ) as rt50_occupancy_perc_change_without_ooo,
               (case accom_type_id
                    when @rt1 then
                        booked_revenue
                   end
                   ) as rt1_booked_revenue,
               (case accom_type_id
                    when @rt1 then
                        booked_revenue_change
                   end
                   ) as rt1_booked_revenue_change,
               (case accom_type_id
                    when @rt2 then
                        booked_revenue
                   end
                   ) as rt2_booked_revenue,
               (case accom_type_id
                    when @rt2 then
                        booked_revenue_change
                   end
                   ) as rt2_booked_revenue_change,
               (case accom_type_id
                    when @rt3 then
                        booked_revenue
                   end
                   ) as rt3_booked_revenue,
               (case accom_type_id
                    when @rt3 then
                        booked_revenue_change
                   end
                   ) as rt3_booked_revenue_change,
               (case accom_type_id
                    when @rt4 then
                        booked_revenue
                   end
                   ) as rt4_booked_revenue,
               (case accom_type_id
                    when @rt4 then
                        booked_revenue_change
                   end
                   ) as rt4_booked_revenue_change,
               (case accom_type_id
                    when @rt5 then
                        booked_revenue
                   end
                   ) as rt5_booked_revenue,
               (case accom_type_id
                    when @rt5 then
                        booked_revenue_change
                   end
                   ) as rt5_booked_revenue_change,
               (case accom_type_id
                    when @rt6 then
                        booked_revenue
                   end
                   ) as rt6_booked_revenue,
               (case accom_type_id
                    when @rt6 then
                        booked_revenue_change
                   end
                   ) as rt6_booked_revenue_change,
               (case accom_type_id
                    when @rt7 then
                        booked_revenue
                   end
                   ) as rt7_booked_revenue,
               (case accom_type_id
                    when @rt7 then
                        booked_revenue_change
                   end
                   ) as rt7_booked_revenue_change,
               (case accom_type_id
                    when @rt8 then
                        booked_revenue
                   end
                   ) as rt8_booked_revenue,
               (case accom_type_id
                    when @rt8 then
                        booked_revenue_change
                   end
                   ) as rt8_booked_revenue_change,
               (case accom_type_id
                    when @rt9 then
                        booked_revenue
                   end
                   ) as rt9_booked_revenue,
               (case accom_type_id
                    when @rt9 then
                        booked_revenue_change
                   end
                   ) as rt9_booked_revenue_change,
               (case accom_type_id
                    when @rt10 then
                        booked_revenue
                   end
                   ) as rt10_booked_revenue,
               (case accom_type_id
                    when @rt10 then
                        booked_revenue_change
                   end
                   ) as rt10_booked_revenue_change,
               (case accom_type_id
                    when @rt11 then
                        booked_revenue
                   end
                   ) as rt11_booked_revenue,
               (case accom_type_id
                    when @rt11 then
                        booked_revenue_change
                   end
                   ) as rt11_booked_revenue_change,
               (case accom_type_id
                    when @rt12 then
                        booked_revenue
                   end
                   ) as rt12_booked_revenue,
               (case accom_type_id
                    when @rt12 then
                        booked_revenue_change
                   end
                   ) as rt12_booked_revenue_change,
               (case accom_type_id
                    when @rt13 then
                        booked_revenue
                   end
                   ) as rt13_booked_revenue,
               (case accom_type_id
                    when @rt13 then
                        booked_revenue_change
                   end
                   ) as rt13_booked_revenue_change,
               (case accom_type_id
                    when @rt14 then
                        booked_revenue
                   end
                   ) as rt14_booked_revenue,
               (case accom_type_id
                    when @rt14 then
                        booked_revenue_change
                   end
                   ) as rt14_booked_revenue_change,
               (case accom_type_id
                    when @rt15 then
                        booked_revenue
                   end
                   ) as rt15_booked_revenue,
               (case accom_type_id
                    when @rt15 then
                        booked_revenue_change
                   end
                   ) as rt15_booked_revenue_change,
               (case accom_type_id
                    when @rt16 then
                        booked_revenue
                   end
                   ) as rt16_booked_revenue,
               (case accom_type_id
                    when @rt16 then
                        booked_revenue_change
                   end
                   ) as rt16_booked_revenue_change,
               (case accom_type_id
                    when @rt17 then
                        booked_revenue
                   end
                   ) as rt17_booked_revenue,
               (case accom_type_id
                    when @rt17 then
                        booked_revenue_change
                   end
                   ) as rt17_booked_revenue_change,
               (case accom_type_id
                    when @rt18 then
                        booked_revenue
                   end
                   ) as rt18_booked_revenue,
               (case accom_type_id
                    when @rt18 then
                        booked_revenue_change
                   end
                   ) as rt18_booked_revenue_change,
               (case accom_type_id
                    when @rt19 then
                        booked_revenue
                   end
                   ) as rt19_booked_revenue,
               (case accom_type_id
                    when @rt19 then
                        booked_revenue_change
                   end
                   ) as rt19_booked_revenue_change,
               (case accom_type_id
                    when @rt20 then
                        booked_revenue
                   end
                   ) as rt20_booked_revenue,
               (case accom_type_id
                    when @rt20 then
                        booked_revenue_change
                   end
                   ) as rt20_booked_revenue_change,
               (case accom_type_id
                    when @rt21 then
                        booked_revenue
                   end
                   ) as rt21_booked_revenue,
               (case accom_type_id
                    when @rt21 then
                        booked_revenue_change
                   end
                   ) as rt21_booked_revenue_change,
               (case accom_type_id
                    when @rt22 then
                        booked_revenue
                   end
                   ) as rt22_booked_revenue,
               (case accom_type_id
                    when @rt22 then
                        booked_revenue_change
                   end
                   ) as rt22_booked_revenue_change,
               (case accom_type_id
                    when @rt23 then
                        booked_revenue
                   end
                   ) as rt23_booked_revenue,
               (case accom_type_id
                    when @rt23 then
                        booked_revenue_change
                   end
                   ) as rt23_booked_revenue_change,
               (case accom_type_id
                    when @rt24 then
                        booked_revenue
                   end
                   ) as rt24_booked_revenue,
               (case accom_type_id
                    when @rt24 then
                        booked_revenue_change
                   end
                   ) as rt24_booked_revenue_change,
               (case accom_type_id
                    when @rt25 then
                        booked_revenue
                   end
                   ) as rt25_booked_revenue,
               (case accom_type_id
                    when @rt25 then
                        booked_revenue_change
                   end
                   ) as rt25_booked_revenue_change,
               (case accom_type_id
                    when @rt26 then
                        booked_revenue
                   end
                   ) as rt26_booked_revenue,
               (case accom_type_id
                    when @rt26 then
                        booked_revenue_change
                   end
                   ) as rt26_booked_revenue_change,
               (case accom_type_id
                    when @rt27 then
                        booked_revenue
                   end
                   ) as rt27_booked_revenue,
               (case accom_type_id
                    when @rt27 then
                        booked_revenue_change
                   end
                   ) as rt27_booked_revenue_change,
               (case accom_type_id
                    when @rt28 then
                        booked_revenue
                   end
                   ) as rt28_booked_revenue,
               (case accom_type_id
                    when @rt28 then
                        booked_revenue_change
                   end
                   ) as rt28_booked_revenue_change,
               (case accom_type_id
                    when @rt29 then
                        booked_revenue
                   end
                   ) as rt29_booked_revenue,
               (case accom_type_id
                    when @rt29 then
                        booked_revenue_change
                   end
                   ) as rt29_booked_revenue_change,
               (case accom_type_id
                    when @rt30 then
                        booked_revenue
                   end
                   ) as rt30_booked_revenue,
               (case accom_type_id
                    when @rt30 then
                        booked_revenue_change
                   end
                   ) as rt30_booked_revenue_change,
               (case accom_type_id
                    when @rt31 then
                        booked_revenue
                   end
                   ) as rt31_booked_revenue,
               (case accom_type_id
                    when @rt31 then
                        booked_revenue_change
                   end
                   ) as rt31_booked_revenue_change,
               (case accom_type_id
                    when @rt32 then
                        booked_revenue
                   end
                   ) as rt32_booked_revenue,
               (case accom_type_id
                    when @rt32 then
                        booked_revenue_change
                   end
                   ) as rt32_booked_revenue_change,
               (case accom_type_id
                    when @rt33 then
                        booked_revenue
                   end
                   ) as rt33_booked_revenue,
               (case accom_type_id
                    when @rt33 then
                        booked_revenue_change
                   end
                   ) as rt33_booked_revenue_change,
               (case accom_type_id
                    when @rt34 then
                        booked_revenue
                   end
                   ) as rt34_booked_revenue,
               (case accom_type_id
                    when @rt34 then
                        booked_revenue_change
                   end
                   ) as rt34_booked_revenue_change,
               (case accom_type_id
                    when @rt35 then
                        booked_revenue
                   end
                   ) as rt35_booked_revenue,
               (case accom_type_id
                    when @rt35 then
                        booked_revenue_change
                   end
                   ) as rt35_booked_revenue_change,
               (case accom_type_id
                    when @rt36 then
                        booked_revenue
                   end
                   ) as rt36_booked_revenue,
               (case accom_type_id
                    when @rt36 then
                        booked_revenue_change
                   end
                   ) as rt36_booked_revenue_change,
               (case accom_type_id
                    when @rt37 then
                        booked_revenue
                   end
                   ) as rt37_booked_revenue,
               (case accom_type_id
                    when @rt37 then
                        booked_revenue_change
                   end
                   ) as rt37_booked_revenue_change,
               (case accom_type_id
                    when @rt38 then
                        booked_revenue
                   end
                   ) as rt38_booked_revenue,
               (case accom_type_id
                    when @rt38 then
                        booked_revenue_change
                   end
                   ) as rt38_booked_revenue_change,
               (case accom_type_id
                    when @rt39 then
                        booked_revenue
                   end
                   ) as rt39_booked_revenue,
               (case accom_type_id
                    when @rt39 then
                        booked_revenue_change
                   end
                   ) as rt39_booked_revenue_change,
               (case accom_type_id
                    when @rt40 then
                        booked_revenue
                   end
                   ) as rt40_booked_revenue,
               (case accom_type_id
                    when @rt40 then
                        booked_revenue_change
                   end
                   ) as rt40_booked_revenue_change,
               (case accom_type_id
                    when @rt41 then
                        booked_revenue
                   end
                   ) as rt41_booked_revenue,
               (case accom_type_id
                    when @rt41 then
                        booked_revenue_change
                   end
                   ) as rt41_booked_revenue_change,
               (case accom_type_id
                    when @rt42 then
                        booked_revenue
                   end
                   ) as rt42_booked_revenue,
               (case accom_type_id
                    when @rt42 then
                        booked_revenue_change
                   end
                   ) as rt42_booked_revenue_change,
               (case accom_type_id
                    when @rt43 then
                        booked_revenue
                   end
                   ) as rt43_booked_revenue,
               (case accom_type_id
                    when @rt43 then
                        booked_revenue_change
                   end
                   ) as rt43_booked_revenue_change,
               (case accom_type_id
                    when @rt44 then
                        booked_revenue
                   end
                   ) as rt44_booked_revenue,
               (case accom_type_id
                    when @rt44 then
                        booked_revenue_change
                   end
                   ) as rt44_booked_revenue_change,
               (case accom_type_id
                    when @rt45 then
                        booked_revenue
                   end
                   ) as rt45_booked_revenue,
               (case accom_type_id
                    when @rt45 then
                        booked_revenue_change
                   end
                   ) as rt45_booked_revenue_change,
               (case accom_type_id
                    when @rt46 then
                        booked_revenue
                   end
                   ) as rt46_booked_revenue,
               (case accom_type_id
                    when @rt46 then
                        booked_revenue_change
                   end
                   ) as rt46_booked_revenue_change,
               (case accom_type_id
                    when @rt47 then
                        booked_revenue
                   end
                   ) as rt47_booked_revenue,
               (case accom_type_id
                    when @rt47 then
                        booked_revenue_change
                   end
                   ) as rt47_booked_revenue_change,
               (case accom_type_id
                    when @rt48 then
                        booked_revenue
                   end
                   ) as rt48_booked_revenue,
               (case accom_type_id
                    when @rt48 then
                        booked_revenue_change
                   end
                   ) as rt48_booked_revenue_change,
               (case accom_type_id
                    when @rt49 then
                        booked_revenue
                   end
                   ) as rt49_booked_revenue,
               (case accom_type_id
                    when @rt49 then
                        booked_revenue_change
                   end
                   ) as rt49_booked_revenue_change,
               (case accom_type_id
                    when @rt50 then
                        booked_revenue
                   end
                   ) as rt50_booked_revenue,
               (case accom_type_id
                    when @rt50 then
                        booked_revenue_change
                   end
                   ) as rt50_booked_revenue_change,
               (case accom_type_id
                    when @rt1 then
                        revenue
                   end
                   ) as rt1_revenue,
               (case accom_type_id
                    when @rt1 then
                        revenue_change
                   end
                   ) as rt1_revenue_change,
               (case accom_type_id
                    when @rt2 then
                        revenue
                   end
                   ) as rt2_revenue,
               (case accom_type_id
                    when @rt2 then
                        revenue_change
                   end
                   ) as rt2_revenue_change,
               (case accom_type_id
                    when @rt3 then
                        revenue
                   end
                   ) as rt3_revenue,
               (case accom_type_id
                    when @rt3 then
                        revenue_change
                   end
                   ) as rt3_revenue_change,
               (case accom_type_id
                    when @rt4 then
                        revenue
                   end
                   ) as rt4_revenue,
               (case accom_type_id
                    when @rt4 then
                        revenue_change
                   end
                   ) as rt4_revenue_change,
               (case accom_type_id
                    when @rt5 then
                        revenue
                   end
                   ) as rt5_revenue,
               (case accom_type_id
                    when @rt5 then
                        revenue_change
                   end
                   ) as rt5_revenue_change,
               (case accom_type_id
                    when @rt6 then
                        revenue
                   end
                   ) as rt6_revenue,
               (case accom_type_id
                    when @rt6 then
                        revenue_change
                   end
                   ) as rt6_revenue_change,
               (case accom_type_id
                    when @rt7 then
                        revenue
                   end
                   ) as rt7_revenue,
               (case accom_type_id
                    when @rt7 then
                        revenue_change
                   end
                   ) as rt7_revenue_change,
               (case accom_type_id
                    when @rt8 then
                        revenue
                   end
                   ) as rt8_revenue,
               (case accom_type_id
                    when @rt8 then
                        revenue_change
                   end
                   ) as rt8_revenue_change,
               (case accom_type_id
                    when @rt9 then
                        revenue
                   end
                   ) as rt9_revenue,
               (case accom_type_id
                    when @rt9 then
                        revenue_change
                   end
                   ) as rt9_revenue_change,
               (case accom_type_id
                    when @rt10 then
                        revenue
                   end
                   ) as rt10_revenue,
               (case accom_type_id
                    when @rt10 then
                        revenue_change
                   end
                   ) as rt10_revenue_change,
               (case accom_type_id
                    when @rt11 then
                        revenue
                   end
                   ) as rt11_revenue,
               (case accom_type_id
                    when @rt11 then
                        revenue_change
                   end
                   ) as rt11_revenue_change,
               (case accom_type_id
                    when @rt12 then
                        revenue
                   end
                   ) as rt12_revenue,
               (case accom_type_id
                    when @rt12 then
                        revenue_change
                   end
                   ) as rt12_revenue_change,
               (case accom_type_id
                    when @rt13 then
                        revenue
                   end
                   ) as rt13_revenue,
               (case accom_type_id
                    when @rt13 then
                        revenue_change
                   end
                   ) as rt13_revenue_change,
               (case accom_type_id
                    when @rt14 then
                        revenue
                   end
                   ) as rt14_revenue,
               (case accom_type_id
                    when @rt14 then
                        revenue_change
                   end
                   ) as rt14_revenue_change,
               (case accom_type_id
                    when @rt15 then
                        revenue
                   end
                   ) as rt15_revenue,
               (case accom_type_id
                    when @rt15 then
                        revenue_change
                   end
                   ) as rt15_revenue_change,
               (case accom_type_id
                    when @rt16 then
                        revenue
                   end
                   ) as rt16_revenue,
               (case accom_type_id
                    when @rt16 then
                        revenue_change
                   end
                   ) as rt16_revenue_change,
               (case accom_type_id
                    when @rt17 then
                        revenue
                   end
                   ) as rt17_revenue,
               (case accom_type_id
                    when @rt17 then
                        revenue_change
                   end
                   ) as rt17_revenue_change,
               (case accom_type_id
                    when @rt18 then
                        revenue
                   end
                   ) as rt18_revenue,
               (case accom_type_id
                    when @rt18 then
                        revenue_change
                   end
                   ) as rt18_revenue_change,
               (case accom_type_id
                    when @rt19 then
                        revenue
                   end
                   ) as rt19_revenue,
               (case accom_type_id
                    when @rt19 then
                        revenue_change
                   end
                   ) as rt19_revenue_change,
               (case accom_type_id
                    when @rt20 then
                        revenue
                   end
                   ) as rt20_revenue,
               (case accom_type_id
                    when @rt20 then
                        revenue_change
                   end
                   ) as rt20_revenue_change,
               (case accom_type_id
                    when @rt21 then
                        revenue
                   end
                   ) as rt21_revenue,
               (case accom_type_id
                    when @rt21 then
                        revenue_change
                   end
                   ) as rt21_revenue_change,
               (case accom_type_id
                    when @rt22 then
                        revenue
                   end
                   ) as rt22_revenue,
               (case accom_type_id
                    when @rt22 then
                        revenue_change
                   end
                   ) as rt22_revenue_change,
               (case accom_type_id
                    when @rt23 then
                        revenue
                   end
                   ) as rt23_revenue,
               (case accom_type_id
                    when @rt23 then
                        revenue_change
                   end
                   ) as rt23_revenue_change,
               (case accom_type_id
                    when @rt24 then
                        revenue
                   end
                   ) as rt24_revenue,
               (case accom_type_id
                    when @rt24 then
                        revenue_change
                   end
                   ) as rt24_revenue_change,
               (case accom_type_id
                    when @rt25 then
                        revenue
                   end
                   ) as rt25_revenue,
               (case accom_type_id
                    when @rt25 then
                        revenue_change
                   end
                   ) as rt25_revenue_change,
               (case accom_type_id
                    when @rt26 then
                        revenue
                   end
                   ) as rt26_revenue,
               (case accom_type_id
                    when @rt26 then
                        revenue_change
                   end
                   ) as rt26_revenue_change,
               (case accom_type_id
                    when @rt27 then
                        revenue
                   end
                   ) as rt27_revenue,
               (case accom_type_id
                    when @rt27 then
                        revenue_change
                   end
                   ) as rt27_revenue_change,
               (case accom_type_id
                    when @rt28 then
                        revenue
                   end
                   ) as rt28_revenue,
               (case accom_type_id
                    when @rt28 then
                        revenue_change
                   end
                   ) as rt28_revenue_change,
               (case accom_type_id
                    when @rt29 then
                        revenue
                   end
                   ) as rt29_revenue,
               (case accom_type_id
                    when @rt29 then
                        revenue_change
                   end
                   ) as rt29_revenue_change,
               (case accom_type_id
                    when @rt30 then
                        revenue
                   end
                   ) as rt30_revenue,
               (case accom_type_id
                    when @rt30 then
                        revenue_change
                   end
                   ) as rt30_revenue_change,
               (case accom_type_id
                    when @rt31 then
                        revenue
                   end
                   ) as rt31_revenue,
               (case accom_type_id
                    when @rt31 then
                        revenue_change
                   end
                   ) as rt31_revenue_change,
               (case accom_type_id
                    when @rt32 then
                        revenue
                   end
                   ) as rt32_revenue,
               (case accom_type_id
                    when @rt32 then
                        revenue_change
                   end
                   ) as rt32_revenue_change,
               (case accom_type_id
                    when @rt33 then
                        revenue
                   end
                   ) as rt33_revenue,
               (case accom_type_id
                    when @rt33 then
                        revenue_change
                   end
                   ) as rt33_revenue_change,
               (case accom_type_id
                    when @rt34 then
                        revenue
                   end
                   ) as rt34_revenue,
               (case accom_type_id
                    when @rt34 then
                        revenue_change
                   end
                   ) as rt34_revenue_change,
               (case accom_type_id
                    when @rt35 then
                        revenue
                   end
                   ) as rt35_revenue,
               (case accom_type_id
                    when @rt35 then
                        revenue_change
                   end
                   ) as rt35_revenue_change,
               (case accom_type_id
                    when @rt36 then
                        revenue
                   end
                   ) as rt36_revenue,
               (case accom_type_id
                    when @rt36 then
                        revenue_change
                   end
                   ) as rt36_revenue_change,
               (case accom_type_id
                    when @rt37 then
                        revenue
                   end
                   ) as rt37_revenue,
               (case accom_type_id
                    when @rt37 then
                        revenue_change
                   end
                   ) as rt37_revenue_change,
               (case accom_type_id
                    when @rt38 then
                        revenue
                   end
                   ) as rt38_revenue,
               (case accom_type_id
                    when @rt38 then
                        revenue_change
                   end
                   ) as rt38_revenue_change,
               (case accom_type_id
                    when @rt39 then
                        revenue
                   end
                   ) as rt39_revenue,
               (case accom_type_id
                    when @rt39 then
                        revenue_change
                   end
                   ) as rt39_revenue_change,
               (case accom_type_id
                    when @rt40 then
                        revenue
                   end
                   ) as rt40_revenue,
               (case accom_type_id
                    when @rt40 then
                        revenue_change
                   end
                   ) as rt40_revenue_change,
               (case accom_type_id
                    when @rt41 then
                        revenue
                   end
                   ) as rt41_revenue,
               (case accom_type_id
                    when @rt41 then
                        revenue_change
                   end
                   ) as rt41_revenue_change,
               (case accom_type_id
                    when @rt42 then
                        revenue
                   end
                   ) as rt42_revenue,
               (case accom_type_id
                    when @rt42 then
                        revenue_change
                   end
                   ) as rt42_revenue_change,
               (case accom_type_id
                    when @rt43 then
                        revenue
                   end
                   ) as rt43_revenue,
               (case accom_type_id
                    when @rt43 then
                        revenue_change
                   end
                   ) as rt43_revenue_change,
               (case accom_type_id
                    when @rt44 then
                        revenue
                   end
                   ) as rt44_revenue,
               (case accom_type_id
                    when @rt44 then
                        revenue_change
                   end
                   ) as rt44_revenue_change,
               (case accom_type_id
                    when @rt45 then
                        revenue
                   end
                   ) as rt45_revenue,
               (case accom_type_id
                    when @rt45 then
                        revenue_change
                   end
                   ) as rt45_revenue_change,
               (case accom_type_id
                    when @rt46 then
                        revenue
                   end
                   ) as rt46_revenue,
               (case accom_type_id
                    when @rt46 then
                        revenue_change
                   end
                   ) as rt46_revenue_change,
               (case accom_type_id
                    when @rt47 then
                        revenue
                   end
                   ) as rt47_revenue,
               (case accom_type_id
                    when @rt47 then
                        revenue_change
                   end
                   ) as rt47_revenue_change,
               (case accom_type_id
                    when @rt48 then
                        revenue
                   end
                   ) as rt48_revenue,
               (case accom_type_id
                    when @rt48 then
                        revenue_change
                   end
                   ) as rt48_revenue_change,
               (case accom_type_id
                    when @rt49 then
                        revenue
                   end
                   ) as rt49_revenue,
               (case accom_type_id
                    when @rt49 then
                        revenue_change
                   end
                   ) as rt49_revenue_change,
               (case accom_type_id
                    when @rt50 then
                        revenue
                   end
                   ) as rt50_revenue,
               (case accom_type_id
                    when @rt50 then
                        revenue_change
                   end
                   ) as rt50_revenue_change,
               (case accom_type_id
                    when @rt1 then
                        booked_adr
                   end
                   ) as rt1_booked_adr,
               (case accom_type_id
                    when @rt1 then
                        booked_adr_change
                   end
                   ) as rt1_booked_adr_change,
               (case accom_type_id
                    when @rt2 then
                        booked_adr
                   end
                   ) as rt2_booked_adr,
               (case accom_type_id
                    when @rt2 then
                        booked_adr_change
                   end
                   ) as rt2_booked_adr_change,
               (case accom_type_id
                    when @rt3 then
                        booked_adr
                   end
                   ) as rt3_booked_adr,
               (case accom_type_id
                    when @rt3 then
                        booked_adr_change
                   end
                   ) as rt3_booked_adr_change,
               (case accom_type_id
                    when @rt4 then
                        booked_adr
                   end
                   ) as rt4_booked_adr,
               (case accom_type_id
                    when @rt4 then
                        booked_adr_change
                   end
                   ) as rt4_booked_adr_change,
               (case accom_type_id
                    when @rt5 then
                        booked_adr
                   end
                   ) as rt5_booked_adr,
               (case accom_type_id
                    when @rt5 then
                        booked_adr_change
                   end
                   ) as rt5_booked_adr_change,
               (case accom_type_id
                    when @rt6 then
                        booked_adr
                   end
                   ) as rt6_booked_adr,
               (case accom_type_id
                    when @rt6 then
                        booked_adr_change
                   end
                   ) as rt6_booked_adr_change,
               (case accom_type_id
                    when @rt7 then
                        booked_adr
                   end
                   ) as rt7_booked_adr,
               (case accom_type_id
                    when @rt7 then
                        booked_adr_change
                   end
                   ) as rt7_booked_adr_change,
               (case accom_type_id
                    when @rt8 then
                        booked_adr
                   end
                   ) as rt8_booked_adr,
               (case accom_type_id
                    when @rt8 then
                        booked_adr_change
                   end
                   ) as rt8_booked_adr_change,
               (case accom_type_id
                    when @rt9 then
                        booked_adr
                   end
                   ) as rt9_booked_adr,
               (case accom_type_id
                    when @rt9 then
                        booked_adr_change
                   end
                   ) as rt9_booked_adr_change,
               (case accom_type_id
                    when @rt10 then
                        booked_adr
                   end
                   ) as rt10_booked_adr,
               (case accom_type_id
                    when @rt10 then
                        booked_adr_change
                   end
                   ) as rt10_booked_adr_change,
               (case accom_type_id
                    when @rt11 then
                        booked_adr
                   end
                   ) as rt11_booked_adr,
               (case accom_type_id
                    when @rt11 then
                        booked_adr_change
                   end
                   ) as rt11_booked_adr_change,
               (case accom_type_id
                    when @rt12 then
                        booked_adr
                   end
                   ) as rt12_booked_adr,
               (case accom_type_id
                    when @rt12 then
                        booked_adr_change
                   end
                   ) as rt12_booked_adr_change,
               (case accom_type_id
                    when @rt13 then
                        booked_adr
                   end
                   ) as rt13_booked_adr,
               (case accom_type_id
                    when @rt13 then
                        booked_adr_change
                   end
                   ) as rt13_booked_adr_change,
               (case accom_type_id
                    when @rt14 then
                        booked_adr
                   end
                   ) as rt14_booked_adr,
               (case accom_type_id
                    when @rt14 then
                        booked_adr_change
                   end
                   ) as rt14_booked_adr_change,
               (case accom_type_id
                    when @rt15 then
                        booked_adr
                   end
                   ) as rt15_booked_adr,
               (case accom_type_id
                    when @rt15 then
                        booked_adr_change
                   end
                   ) as rt15_booked_adr_change,
               (case accom_type_id
                    when @rt16 then
                        booked_adr
                   end
                   ) as rt16_booked_adr,
               (case accom_type_id
                    when @rt16 then
                        booked_adr_change
                   end
                   ) as rt16_booked_adr_change,
               (case accom_type_id
                    when @rt17 then
                        booked_adr
                   end
                   ) as rt17_booked_adr,
               (case accom_type_id
                    when @rt17 then
                        booked_adr_change
                   end
                   ) as rt17_booked_adr_change,
               (case accom_type_id
                    when @rt18 then
                        booked_adr
                   end
                   ) as rt18_booked_adr,
               (case accom_type_id
                    when @rt18 then
                        booked_adr_change
                   end
                   ) as rt18_booked_adr_change,
               (case accom_type_id
                    when @rt19 then
                        booked_adr
                   end
                   ) as rt19_booked_adr,
               (case accom_type_id
                    when @rt19 then
                        booked_adr_change
                   end
                   ) as rt19_booked_adr_change,
               (case accom_type_id
                    when @rt20 then
                        booked_adr
                   end
                   ) as rt20_booked_adr,
               (case accom_type_id
                    when @rt20 then
                        booked_adr_change
                   end
                   ) as rt20_booked_adr_change,
               (case accom_type_id
                    when @rt21 then
                        booked_adr
                   end
                   ) as rt21_booked_adr,
               (case accom_type_id
                    when @rt21 then
                        booked_adr_change
                   end
                   ) as rt21_booked_adr_change,
               (case accom_type_id
                    when @rt22 then
                        booked_adr
                   end
                   ) as rt22_booked_adr,
               (case accom_type_id
                    when @rt22 then
                        booked_adr_change
                   end
                   ) as rt22_booked_adr_change,
               (case accom_type_id
                    when @rt23 then
                        booked_adr
                   end
                   ) as rt23_booked_adr,
               (case accom_type_id
                    when @rt23 then
                        booked_adr_change
                   end
                   ) as rt23_booked_adr_change,
               (case accom_type_id
                    when @rt24 then
                        booked_adr
                   end
                   ) as rt24_booked_adr,
               (case accom_type_id
                    when @rt24 then
                        booked_adr_change
                   end
                   ) as rt24_booked_adr_change,
               (case accom_type_id
                    when @rt25 then
                        booked_adr
                   end
                   ) as rt25_booked_adr,
               (case accom_type_id
                    when @rt25 then
                        booked_adr_change
                   end
                   ) as rt25_booked_adr_change,
               (case accom_type_id
                    when @rt26 then
                        booked_adr
                   end
                   ) as rt26_booked_adr,
               (case accom_type_id
                    when @rt26 then
                        booked_adr_change
                   end
                   ) as rt26_booked_adr_change,
               (case accom_type_id
                    when @rt27 then
                        booked_adr
                   end
                   ) as rt27_booked_adr,
               (case accom_type_id
                    when @rt27 then
                        booked_adr_change
                   end
                   ) as rt27_booked_adr_change,
               (case accom_type_id
                    when @rt28 then
                        booked_adr
                   end
                   ) as rt28_booked_adr,
               (case accom_type_id
                    when @rt28 then
                        booked_adr_change
                   end
                   ) as rt28_booked_adr_change,
               (case accom_type_id
                    when @rt29 then
                        booked_adr
                   end
                   ) as rt29_booked_adr,
               (case accom_type_id
                    when @rt29 then
                        booked_adr_change
                   end
                   ) as rt29_booked_adr_change,
               (case accom_type_id
                    when @rt30 then
                        booked_adr
                   end
                   ) as rt30_booked_adr,
               (case accom_type_id
                    when @rt30 then
                        booked_adr_change
                   end
                   ) as rt30_booked_adr_change,
               (case accom_type_id
                    when @rt31 then
                        booked_adr
                   end
                   ) as rt31_booked_adr,
               (case accom_type_id
                    when @rt31 then
                        booked_adr_change
                   end
                   ) as rt31_booked_adr_change,
               (case accom_type_id
                    when @rt32 then
                        booked_adr
                   end
                   ) as rt32_booked_adr,
               (case accom_type_id
                    when @rt32 then
                        booked_adr_change
                   end
                   ) as rt32_booked_adr_change,
               (case accom_type_id
                    when @rt33 then
                        booked_adr
                   end
                   ) as rt33_booked_adr,
               (case accom_type_id
                    when @rt33 then
                        booked_adr_change
                   end
                   ) as rt33_booked_adr_change,
               (case accom_type_id
                    when @rt34 then
                        booked_adr
                   end
                   ) as rt34_booked_adr,
               (case accom_type_id
                    when @rt34 then
                        booked_adr_change
                   end
                   ) as rt34_booked_adr_change,
               (case accom_type_id
                    when @rt35 then
                        booked_adr
                   end
                   ) as rt35_booked_adr,
               (case accom_type_id
                    when @rt35 then
                        booked_adr_change
                   end
                   ) as rt35_booked_adr_change,
               (case accom_type_id
                    when @rt36 then
                        booked_adr
                   end
                   ) as rt36_booked_adr,
               (case accom_type_id
                    when @rt36 then
                        booked_adr_change
                   end
                   ) as rt36_booked_adr_change,
               (case accom_type_id
                    when @rt37 then
                        booked_adr
                   end
                   ) as rt37_booked_adr,
               (case accom_type_id
                    when @rt37 then
                        booked_adr_change
                   end
                   ) as rt37_booked_adr_change,
               (case accom_type_id
                    when @rt38 then
                        booked_adr
                   end
                   ) as rt38_booked_adr,
               (case accom_type_id
                    when @rt38 then
                        booked_adr_change
                   end
                   ) as rt38_booked_adr_change,
               (case accom_type_id
                    when @rt39 then
                        booked_adr
                   end
                   ) as rt39_booked_adr,
               (case accom_type_id
                    when @rt39 then
                        booked_adr_change
                   end
                   ) as rt39_booked_adr_change,
               (case accom_type_id
                    when @rt40 then
                        booked_adr
                   end
                   ) as rt40_booked_adr,
               (case accom_type_id
                    when @rt40 then
                        booked_adr_change
                   end
                   ) as rt40_booked_adr_change,
               (case accom_type_id
                    when @rt41 then
                        booked_adr
                   end
                   ) as rt41_booked_adr,
               (case accom_type_id
                    when @rt41 then
                        booked_adr_change
                   end
                   ) as rt41_booked_adr_change,
               (case accom_type_id
                    when @rt42 then
                        booked_adr
                   end
                   ) as rt42_booked_adr,
               (case accom_type_id
                    when @rt42 then
                        booked_adr_change
                   end
                   ) as rt42_booked_adr_change,
               (case accom_type_id
                    when @rt43 then
                        booked_adr
                   end
                   ) as rt43_booked_adr,
               (case accom_type_id
                    when @rt43 then
                        booked_adr_change
                   end
                   ) as rt43_booked_adr_change,
               (case accom_type_id
                    when @rt44 then
                        booked_adr
                   end
                   ) as rt44_booked_adr,
               (case accom_type_id
                    when @rt44 then
                        booked_adr_change
                   end
                   ) as rt44_booked_adr_change,
               (case accom_type_id
                    when @rt45 then
                        booked_adr
                   end
                   ) as rt45_booked_adr,
               (case accom_type_id
                    when @rt45 then
                        booked_adr_change
                   end
                   ) as rt45_booked_adr_change,
               (case accom_type_id
                    when @rt46 then
                        booked_adr
                   end
                   ) as rt46_booked_adr,
               (case accom_type_id
                    when @rt46 then
                        booked_adr_change
                   end
                   ) as rt46_booked_adr_change,
               (case accom_type_id
                    when @rt47 then
                        booked_adr
                   end
                   ) as rt47_booked_adr,
               (case accom_type_id
                    when @rt47 then
                        booked_adr_change
                   end
                   ) as rt47_booked_adr_change,
               (case accom_type_id
                    when @rt48 then
                        booked_adr
                   end
                   ) as rt48_booked_adr,
               (case accom_type_id
                    when @rt48 then
                        booked_adr_change
                   end
                   ) as rt48_booked_adr_change,
               (case accom_type_id
                    when @rt49 then
                        booked_adr
                   end
                   ) as rt49_booked_adr,
               (case accom_type_id
                    when @rt49 then
                        booked_adr_change
                   end
                   ) as rt49_booked_adr_change,
               (case accom_type_id
                    when @rt50 then
                        booked_adr
                   end
                   ) as rt50_booked_adr,
               (case accom_type_id
                    when @rt50 then
                        booked_adr_change
                   end
                   ) as rt50_booked_adr_change,
               (case accom_type_id
                    when @rt1 then
                        adr
                   end
                   ) as rt1_adr,
               (case accom_type_id
                    when @rt1 then
                        adr_change
                   end
                   ) as rt1_adr_change,
               (case accom_type_id
                    when @rt2 then
                        adr
                   end
                   ) as rt2_adr,
               (case accom_type_id
                    when @rt2 then
                        adr_change
                   end
                   ) as rt2_adr_change,
               (case accom_type_id
                    when @rt3 then
                        adr
                   end
                   ) as rt3_adr,
               (case accom_type_id
                    when @rt3 then
                        adr_change
                   end
                   ) as rt3_adr_change,
               (case accom_type_id
                    when @rt4 then
                        adr
                   end
                   ) as rt4_adr,
               (case accom_type_id
                    when @rt4 then
                        adr_change
                   end
                   ) as rt4_adr_change,
               (case accom_type_id
                    when @rt5 then
                        adr
                   end
                   ) as rt5_adr,
               (case accom_type_id
                    when @rt5 then
                        adr_change
                   end
                   ) as rt5_adr_change,
               (case accom_type_id
                    when @rt6 then
                        adr
                   end
                   ) as rt6_adr,
               (case accom_type_id
                    when @rt6 then
                        adr_change
                   end
                   ) as rt6_adr_change,
               (case accom_type_id
                    when @rt7 then
                        adr
                   end
                   ) as rt7_adr,
               (case accom_type_id
                    when @rt7 then
                        adr_change
                   end
                   ) as rt7_adr_change,
               (case accom_type_id
                    when @rt8 then
                        adr
                   end
                   ) as rt8_adr,
               (case accom_type_id
                    when @rt8 then
                        adr_change
                   end
                   ) as rt8_adr_change,
               (case accom_type_id
                    when @rt9 then
                        adr
                   end
                   ) as rt9_adr,
               (case accom_type_id
                    when @rt9 then
                        adr_change
                   end
                   ) as rt9_adr_change,
               (case accom_type_id
                    when @rt10 then
                        adr
                   end
                   ) as rt10_adr,
               (case accom_type_id
                    when @rt10 then
                        adr_change
                   end
                   ) as rt10_adr_change,
               (case accom_type_id
                    when @rt11 then
                        adr
                   end
                   ) as rt11_adr,
               (case accom_type_id
                    when @rt11 then
                        adr_change
                   end
                   ) as rt11_adr_change,
               (case accom_type_id
                    when @rt12 then
                        adr
                   end
                   ) as rt12_adr,
               (case accom_type_id
                    when @rt12 then
                        adr_change
                   end
                   ) as rt12_adr_change,
               (case accom_type_id
                    when @rt13 then
                        adr
                   end
                   ) as rt13_adr,
               (case accom_type_id
                    when @rt13 then
                        adr_change
                   end
                   ) as rt13_adr_change,
               (case accom_type_id
                    when @rt14 then
                        adr
                   end
                   ) as rt14_adr,
               (case accom_type_id
                    when @rt14 then
                        adr_change
                   end
                   ) as rt14_adr_change,
               (case accom_type_id
                    when @rt15 then
                        adr
                   end
                   ) as rt15_adr,
               (case accom_type_id
                    when @rt15 then
                        adr_change
                   end
                   ) as rt15_adr_change,
               (case accom_type_id
                    when @rt16 then
                        adr
                   end
                   ) as rt16_adr,
               (case accom_type_id
                    when @rt16 then
                        adr_change
                   end
                   ) as rt16_adr_change,
               (case accom_type_id
                    when @rt17 then
                        adr
                   end
                   ) as rt17_adr,
               (case accom_type_id
                    when @rt17 then
                        adr_change
                   end
                   ) as rt17_adr_change,
               (case accom_type_id
                    when @rt18 then
                        adr
                   end
                   ) as rt18_adr,
               (case accom_type_id
                    when @rt18 then
                        adr_change
                   end
                   ) as rt18_adr_change,
               (case accom_type_id
                    when @rt19 then
                        adr
                   end
                   ) as rt19_adr,
               (case accom_type_id
                    when @rt19 then
                        adr_change
                   end
                   ) as rt19_adr_change,
               (case accom_type_id
                    when @rt20 then
                        adr
                   end
                   ) as rt20_adr,
               (case accom_type_id
                    when @rt20 then
                        adr_change
                   end
                   ) as rt20_adr_change,
               (case accom_type_id
                    when @rt21 then
                        adr
                   end
                   ) as rt21_adr,
               (case accom_type_id
                    when @rt21 then
                        adr_change
                   end
                   ) as rt21_adr_change,
               (case accom_type_id
                    when @rt22 then
                        adr
                   end
                   ) as rt22_adr,
               (case accom_type_id
                    when @rt22 then
                        adr_change
                   end
                   ) as rt22_adr_change,
               (case accom_type_id
                    when @rt23 then
                        adr
                   end
                   ) as rt23_adr,
               (case accom_type_id
                    when @rt23 then
                        adr_change
                   end
                   ) as rt23_adr_change,
               (case accom_type_id
                    when @rt24 then
                        adr
                   end
                   ) as rt24_adr,
               (case accom_type_id
                    when @rt24 then
                        adr_change
                   end
                   ) as rt24_adr_change,
               (case accom_type_id
                    when @rt25 then
                        adr
                   end
                   ) as rt25_adr,
               (case accom_type_id
                    when @rt25 then
                        adr_change
                   end
                   ) as rt25_adr_change,
               (case accom_type_id
                    when @rt26 then
                        adr
                   end
                   ) as rt26_adr,
               (case accom_type_id
                    when @rt26 then
                        adr_change
                   end
                   ) as rt26_adr_change,
               (case accom_type_id
                    when @rt27 then
                        adr
                   end
                   ) as rt27_adr,
               (case accom_type_id
                    when @rt27 then
                        adr_change
                   end
                   ) as rt27_adr_change,
               (case accom_type_id
                    when @rt28 then
                        adr
                   end
                   ) as rt28_adr,
               (case accom_type_id
                    when @rt28 then
                        adr_change
                   end
                   ) as rt28_adr_change,
               (case accom_type_id
                    when @rt29 then
                        adr
                   end
                   ) as rt29_adr,
               (case accom_type_id
                    when @rt29 then
                        adr_change
                   end
                   ) as rt29_adr_change,
               (case accom_type_id
                    when @rt30 then
                        adr
                   end
                   ) as rt30_adr,
               (case accom_type_id
                    when @rt30 then
                        adr_change
                   end
                   ) as rt30_adr_change,
               (case accom_type_id
                    when @rt31 then
                        adr
                   end
                   ) as rt31_adr,
               (case accom_type_id
                    when @rt31 then
                        adr_change
                   end
                   ) as rt31_adr_change,
               (case accom_type_id
                    when @rt32 then
                        adr
                   end
                   ) as rt32_adr,
               (case accom_type_id
                    when @rt32 then
                        adr_change
                   end
                   ) as rt32_adr_change,
               (case accom_type_id
                    when @rt33 then
                        adr
                   end
                   ) as rt33_adr,
               (case accom_type_id
                    when @rt33 then
                        adr_change
                   end
                   ) as rt33_adr_change,
               (case accom_type_id
                    when @rt34 then
                        adr
                   end
                   ) as rt34_adr,
               (case accom_type_id
                    when @rt34 then
                        adr_change
                   end
                   ) as rt34_adr_change,
               (case accom_type_id
                    when @rt35 then
                        adr
                   end
                   ) as rt35_adr,
               (case accom_type_id
                    when @rt35 then
                        adr_change
                   end
                   ) as rt35_adr_change,
               (case accom_type_id
                    when @rt36 then
                        adr
                   end
                   ) as rt36_adr,
               (case accom_type_id
                    when @rt36 then
                        adr_change
                   end
                   ) as rt36_adr_change,
               (case accom_type_id
                    when @rt37 then
                        adr
                   end
                   ) as rt37_adr,
               (case accom_type_id
                    when @rt37 then
                        adr_change
                   end
                   ) as rt37_adr_change,
               (case accom_type_id
                    when @rt38 then
                        adr
                   end
                   ) as rt38_adr,
               (case accom_type_id
                    when @rt38 then
                        adr_change
                   end
                   ) as rt38_adr_change,
               (case accom_type_id
                    when @rt39 then
                        adr
                   end
                   ) as rt39_adr,
               (case accom_type_id
                    when @rt39 then
                        adr_change
                   end
                   ) as rt39_adr_change,
               (case accom_type_id
                    when @rt40 then
                        adr
                   end
                   ) as rt40_adr,
               (case accom_type_id
                    when @rt40 then
                        adr_change
                   end
                   ) as rt40_adr_change,
               (case accom_type_id
                    when @rt41 then
                        adr
                   end
                   ) as rt41_adr,
               (case accom_type_id
                    when @rt41 then
                        adr_change
                   end
                   ) as rt41_adr_change,
               (case accom_type_id
                    when @rt42 then
                        adr
                   end
                   ) as rt42_adr,
               (case accom_type_id
                    when @rt42 then
                        adr_change
                   end
                   ) as rt42_adr_change,
               (case accom_type_id
                    when @rt43 then
                        adr
                   end
                   ) as rt43_adr,
               (case accom_type_id
                    when @rt43 then
                        adr_change
                   end
                   ) as rt43_adr_change,
               (case accom_type_id
                    when @rt44 then
                        adr
                   end
                   ) as rt44_adr,
               (case accom_type_id
                    when @rt44 then
                        adr_change
                   end
                   ) as rt44_adr_change,
               (case accom_type_id
                    when @rt45 then
                        adr
                   end
                   ) as rt45_adr,
               (case accom_type_id
                    when @rt45 then
                        adr_change
                   end
                   ) as rt45_adr_change,
               (case accom_type_id
                    when @rt46 then
                        adr
                   end
                   ) as rt46_adr,
               (case accom_type_id
                    when @rt46 then
                        adr_change
                   end
                   ) as rt46_adr_change,
               (case accom_type_id
                    when @rt47 then
                        adr
                   end
                   ) as rt47_adr,
               (case accom_type_id
                    when @rt47 then
                        adr_change
                   end
                   ) as rt47_adr_change,
               (case accom_type_id
                    when @rt48 then
                        adr
                   end
                   ) as rt48_adr,
               (case accom_type_id
                    when @rt48 then
                        adr_change
                   end
                   ) as rt48_adr_change,
               (case accom_type_id
                    when @rt49 then
                        adr
                   end
                   ) as rt49_adr,
               (case accom_type_id
                    when @rt49 then
                        adr_change
                   end
                   ) as rt49_adr_change,
               (case accom_type_id
                    when @rt50 then
                        adr
                   end
                   ) as rt50_adr,
               (case accom_type_id
                    when @rt50 then
                        adr_change
                   end
                   ) as rt50_adr_change,
               (case accom_type_id
                    when @rt1 then
                        booked_revpar
                   end
                   ) as rt1_booked_revpar,
               (case accom_type_id
                    when @rt1 then
                        booked_revpar_change
                   end
                   ) as rt1_booked_revpar_change,
               (case accom_type_id
                    when @rt2 then
                        booked_revpar
                   end
                   ) as rt2_booked_revpar,
               (case accom_type_id
                    when @rt2 then
                        booked_revpar_change
                   end
                   ) as rt2_booked_revpar_change,
               (case accom_type_id
                    when @rt3 then
                        booked_revpar
                   end
                   ) as rt3_booked_revpar,
               (case accom_type_id
                    when @rt3 then
                        booked_revpar_change
                   end
                   ) as rt3_booked_revpar_change,
               (case accom_type_id
                    when @rt4 then
                        booked_revpar
                   end
                   ) as rt4_booked_revpar,
               (case accom_type_id
                    when @rt4 then
                        booked_revpar_change
                   end
                   ) as rt4_booked_revpar_change,
               (case accom_type_id
                    when @rt5 then
                        booked_revpar
                   end
                   ) as rt5_booked_revpar,
               (case accom_type_id
                    when @rt5 then
                        booked_revpar_change
                   end
                   ) as rt5_booked_revpar_change,
               (case accom_type_id
                    when @rt6 then
                        booked_revpar
                   end
                   ) as rt6_booked_revpar,
               (case accom_type_id
                    when @rt6 then
                        booked_revpar_change
                   end
                   ) as rt6_booked_revpar_change,
               (case accom_type_id
                    when @rt7 then
                        booked_revpar
                   end
                   ) as rt7_booked_revpar,
               (case accom_type_id
                    when @rt7 then
                        booked_revpar_change
                   end
                   ) as rt7_booked_revpar_change,
               (case accom_type_id
                    when @rt8 then
                        booked_revpar
                   end
                   ) as rt8_booked_revpar,
               (case accom_type_id
                    when @rt8 then
                        booked_revpar_change
                   end
                   ) as rt8_booked_revpar_change,
               (case accom_type_id
                    when @rt9 then
                        booked_revpar
                   end
                   ) as rt9_booked_revpar,
               (case accom_type_id
                    when @rt9 then
                        booked_revpar_change
                   end
                   ) as rt9_booked_revpar_change,
               (case accom_type_id
                    when @rt10 then
                        booked_revpar
                   end
                   ) as rt10_booked_revpar,
               (case accom_type_id
                    when @rt10 then
                        booked_revpar_change
                   end
                   ) as rt10_booked_revpar_change,
               (case accom_type_id
                    when @rt11 then
                        booked_revpar
                   end
                   ) as rt11_booked_revpar,
               (case accom_type_id
                    when @rt11 then
                        booked_revpar_change
                   end
                   ) as rt11_booked_revpar_change,
               (case accom_type_id
                    when @rt12 then
                        booked_revpar
                   end
                   ) as rt12_booked_revpar,
               (case accom_type_id
                    when @rt12 then
                        booked_revpar_change
                   end
                   ) as rt12_booked_revpar_change,
               (case accom_type_id
                    when @rt13 then
                        booked_revpar
                   end
                   ) as rt13_booked_revpar,
               (case accom_type_id
                    when @rt13 then
                        booked_revpar_change
                   end
                   ) as rt13_booked_revpar_change,
               (case accom_type_id
                    when @rt14 then
                        booked_revpar
                   end
                   ) as rt14_booked_revpar,
               (case accom_type_id
                    when @rt14 then
                        booked_revpar_change
                   end
                   ) as rt14_booked_revpar_change,
               (case accom_type_id
                    when @rt15 then
                        booked_revpar
                   end
                   ) as rt15_booked_revpar,
               (case accom_type_id
                    when @rt15 then
                        booked_revpar_change
                   end
                   ) as rt15_booked_revpar_change,
               (case accom_type_id
                    when @rt16 then
                        booked_revpar
                   end
                   ) as rt16_booked_revpar,
               (case accom_type_id
                    when @rt16 then
                        booked_revpar_change
                   end
                   ) as rt16_booked_revpar_change,
               (case accom_type_id
                    when @rt17 then
                        booked_revpar
                   end
                   ) as rt17_booked_revpar,
               (case accom_type_id
                    when @rt17 then
                        booked_revpar_change
                   end
                   ) as rt17_booked_revpar_change,
               (case accom_type_id
                    when @rt18 then
                        booked_revpar
                   end
                   ) as rt18_booked_revpar,
               (case accom_type_id
                    when @rt18 then
                        booked_revpar_change
                   end
                   ) as rt18_booked_revpar_change,
               (case accom_type_id
                    when @rt19 then
                        booked_revpar
                   end
                   ) as rt19_booked_revpar,
               (case accom_type_id
                    when @rt19 then
                        booked_revpar_change
                   end
                   ) as rt19_booked_revpar_change,
               (case accom_type_id
                    when @rt20 then
                        booked_revpar
                   end
                   ) as rt20_booked_revpar,
               (case accom_type_id
                    when @rt20 then
                        booked_revpar_change
                   end
                   ) as rt20_booked_revpar_change,
               (case accom_type_id
                    when @rt21 then
                        booked_revpar
                   end
                   ) as rt21_booked_revpar,
               (case accom_type_id
                    when @rt21 then
                        booked_revpar_change
                   end
                   ) as rt21_booked_revpar_change,
               (case accom_type_id
                    when @rt22 then
                        booked_revpar
                   end
                   ) as rt22_booked_revpar,
               (case accom_type_id
                    when @rt22 then
                        booked_revpar_change
                   end
                   ) as rt22_booked_revpar_change,
               (case accom_type_id
                    when @rt23 then
                        booked_revpar
                   end
                   ) as rt23_booked_revpar,
               (case accom_type_id
                    when @rt23 then
                        booked_revpar_change
                   end
                   ) as rt23_booked_revpar_change,
               (case accom_type_id
                    when @rt24 then
                        booked_revpar
                   end
                   ) as rt24_booked_revpar,
               (case accom_type_id
                    when @rt24 then
                        booked_revpar_change
                   end
                   ) as rt24_booked_revpar_change,
               (case accom_type_id
                    when @rt25 then
                        booked_revpar
                   end
                   ) as rt25_booked_revpar,
               (case accom_type_id
                    when @rt25 then
                        booked_revpar_change
                   end
                   ) as rt25_booked_revpar_change,
               (case accom_type_id
                    when @rt26 then
                        booked_revpar
                   end
                   ) as rt26_booked_revpar,
               (case accom_type_id
                    when @rt26 then
                        booked_revpar_change
                   end
                   ) as rt26_booked_revpar_change,
               (case accom_type_id
                    when @rt27 then
                        booked_revpar
                   end
                   ) as rt27_booked_revpar,
               (case accom_type_id
                    when @rt27 then
                        booked_revpar_change
                   end
                   ) as rt27_booked_revpar_change,
               (case accom_type_id
                    when @rt28 then
                        booked_revpar
                   end
                   ) as rt28_booked_revpar,
               (case accom_type_id
                    when @rt28 then
                        booked_revpar_change
                   end
                   ) as rt28_booked_revpar_change,
               (case accom_type_id
                    when @rt29 then
                        booked_revpar
                   end
                   ) as rt29_booked_revpar,
               (case accom_type_id
                    when @rt29 then
                        booked_revpar_change
                   end
                   ) as rt29_booked_revpar_change,
               (case accom_type_id
                    when @rt30 then
                        booked_revpar
                   end
                   ) as rt30_booked_revpar,
               (case accom_type_id
                    when @rt30 then
                        booked_revpar_change
                   end
                   ) as rt30_booked_revpar_change,
               (case accom_type_id
                    when @rt31 then
                        booked_revpar
                   end
                   ) as rt31_booked_revpar,
               (case accom_type_id
                    when @rt31 then
                        booked_revpar_change
                   end
                   ) as rt31_booked_revpar_change,
               (case accom_type_id
                    when @rt32 then
                        booked_revpar
                   end
                   ) as rt32_booked_revpar,
               (case accom_type_id
                    when @rt32 then
                        booked_revpar_change
                   end
                   ) as rt32_booked_revpar_change,
               (case accom_type_id
                    when @rt33 then
                        booked_revpar
                   end
                   ) as rt33_booked_revpar,
               (case accom_type_id
                    when @rt33 then
                        booked_revpar_change
                   end
                   ) as rt33_booked_revpar_change,
               (case accom_type_id
                    when @rt34 then
                        booked_revpar
                   end
                   ) as rt34_booked_revpar,
               (case accom_type_id
                    when @rt34 then
                        booked_revpar_change
                   end
                   ) as rt34_booked_revpar_change,
               (case accom_type_id
                    when @rt35 then
                        booked_revpar
                   end
                   ) as rt35_booked_revpar,
               (case accom_type_id
                    when @rt35 then
                        booked_revpar_change
                   end
                   ) as rt35_booked_revpar_change,
               (case accom_type_id
                    when @rt36 then
                        booked_revpar
                   end
                   ) as rt36_booked_revpar,
               (case accom_type_id
                    when @rt36 then
                        booked_revpar_change
                   end
                   ) as rt36_booked_revpar_change,
               (case accom_type_id
                    when @rt37 then
                        booked_revpar
                   end
                   ) as rt37_booked_revpar,
               (case accom_type_id
                    when @rt37 then
                        booked_revpar_change
                   end
                   ) as rt37_booked_revpar_change,
               (case accom_type_id
                    when @rt38 then
                        booked_revpar
                   end
                   ) as rt38_booked_revpar,
               (case accom_type_id
                    when @rt38 then
                        booked_revpar_change
                   end
                   ) as rt38_booked_revpar_change,
               (case accom_type_id
                    when @rt39 then
                        booked_revpar
                   end
                   ) as rt39_booked_revpar,
               (case accom_type_id
                    when @rt39 then
                        booked_revpar_change
                   end
                   ) as rt39_booked_revpar_change,
               (case accom_type_id
                    when @rt40 then
                        booked_revpar
                   end
                   ) as rt40_booked_revpar,
               (case accom_type_id
                    when @rt40 then
                        booked_revpar_change
                   end
                   ) as rt40_booked_revpar_change,
               (case accom_type_id
                    when @rt41 then
                        booked_revpar
                   end
                   ) as rt41_booked_revpar,
               (case accom_type_id
                    when @rt41 then
                        booked_revpar_change
                   end
                   ) as rt41_booked_revpar_change,
               (case accom_type_id
                    when @rt42 then
                        booked_revpar
                   end
                   ) as rt42_booked_revpar,
               (case accom_type_id
                    when @rt42 then
                        booked_revpar_change
                   end
                   ) as rt42_booked_revpar_change,
               (case accom_type_id
                    when @rt43 then
                        booked_revpar
                   end
                   ) as rt43_booked_revpar,
               (case accom_type_id
                    when @rt43 then
                        booked_revpar_change
                   end
                   ) as rt43_booked_revpar_change,
               (case accom_type_id
                    when @rt44 then
                        booked_revpar
                   end
                   ) as rt44_booked_revpar,
               (case accom_type_id
                    when @rt44 then
                        booked_revpar_change
                   end
                   ) as rt44_booked_revpar_change,
               (case accom_type_id
                    when @rt45 then
                        booked_revpar
                   end
                   ) as rt45_booked_revpar,
               (case accom_type_id
                    when @rt45 then
                        booked_revpar_change
                   end
                   ) as rt45_booked_revpar_change,
               (case accom_type_id
                    when @rt46 then
                        booked_revpar
                   end
                   ) as rt46_booked_revpar,
               (case accom_type_id
                    when @rt46 then
                        booked_revpar_change
                   end
                   ) as rt46_booked_revpar_change,
               (case accom_type_id
                    when @rt47 then
                        booked_revpar
                   end
                   ) as rt47_booked_revpar,
               (case accom_type_id
                    when @rt47 then
                        booked_revpar_change
                   end
                   ) as rt47_booked_revpar_change,
               (case accom_type_id
                    when @rt48 then
                        booked_revpar
                   end
                   ) as rt48_booked_revpar,
               (case accom_type_id
                    when @rt48 then
                        booked_revpar_change
                   end
                   ) as rt48_booked_revpar_change,
               (case accom_type_id
                    when @rt49 then
                        booked_revpar
                   end
                   ) as rt49_booked_revpar,
               (case accom_type_id
                    when @rt49 then
                        booked_revpar_change
                   end
                   ) as rt49_booked_revpar_change,
               (case accom_type_id
                    when @rt50 then
                        booked_revpar
                   end
                   ) as rt50_booked_revpar,
               (case accom_type_id
                    when @rt50 then
                        booked_revpar_change
                   end
                   ) as rt50_booked_revpar_change,
               (case accom_type_id
                    when @rt1 then
                        revpar
                   end
                   ) as rt1_revpar,
               (case accom_type_id
                    when @rt1 then
                        revpar_change
                   end
                   ) as rt1_revpar_change,
               (case accom_type_id
                    when @rt2 then
                        revpar
                   end
                   ) as rt2_revpar,
               (case accom_type_id
                    when @rt2 then
                        revpar_change
                   end
                   ) as rt2_revpar_change,
               (case accom_type_id
                    when @rt3 then
                        revpar
                   end
                   ) as rt3_revpar,
               (case accom_type_id
                    when @rt3 then
                        revpar_change
                   end
                   ) as rt3_revpar_change,
               (case accom_type_id
                    when @rt4 then
                        revpar
                   end
                   ) as rt4_revpar,
               (case accom_type_id
                    when @rt4 then
                        revpar_change
                   end
                   ) as rt4_revpar_change,
               (case accom_type_id
                    when @rt5 then
                        revpar
                   end
                   ) as rt5_revpar,
               (case accom_type_id
                    when @rt5 then
                        revpar_change
                   end
                   ) as rt5_revpar_change,
               (case accom_type_id
                    when @rt6 then
                        revpar
                   end
                   ) as rt6_revpar,
               (case accom_type_id
                    when @rt6 then
                        revpar_change
                   end
                   ) as rt6_revpar_change,
               (case accom_type_id
                    when @rt7 then
                        revpar
                   end
                   ) as rt7_revpar,
               (case accom_type_id
                    when @rt7 then
                        revpar_change
                   end
                   ) as rt7_revpar_change,
               (case accom_type_id
                    when @rt8 then
                        revpar
                   end
                   ) as rt8_revpar,
               (case accom_type_id
                    when @rt8 then
                        revpar_change
                   end
                   ) as rt8_revpar_change,
               (case accom_type_id
                    when @rt9 then
                        revpar
                   end
                   ) as rt9_revpar,
               (case accom_type_id
                    when @rt9 then
                        revpar_change
                   end
                   ) as rt9_revpar_change,
               (case accom_type_id
                    when @rt10 then
                        revpar
                   end
                   ) as rt10_revpar,
               (case accom_type_id
                    when @rt10 then
                        revpar_change
                   end
                   ) as rt10_revpar_change,
               (case accom_type_id
                    when @rt11 then
                        revpar
                   end
                   ) as rt11_revpar,
               (case accom_type_id
                    when @rt11 then
                        revpar_change
                   end
                   ) as rt11_revpar_change,
               (case accom_type_id
                    when @rt12 then
                        revpar
                   end
                   ) as rt12_revpar,
               (case accom_type_id
                    when @rt12 then
                        revpar_change
                   end
                   ) as rt12_revpar_change,
               (case accom_type_id
                    when @rt13 then
                        revpar
                   end
                   ) as rt13_revpar,
               (case accom_type_id
                    when @rt13 then
                        revpar_change
                   end
                   ) as rt13_revpar_change,
               (case accom_type_id
                    when @rt14 then
                        revpar
                   end
                   ) as rt14_revpar,
               (case accom_type_id
                    when @rt14 then
                        revpar_change
                   end
                   ) as rt14_revpar_change,
               (case accom_type_id
                    when @rt15 then
                        revpar
                   end
                   ) as rt15_revpar,
               (case accom_type_id
                    when @rt15 then
                        revpar_change
                   end
                   ) as rt15_revpar_change,
               (case accom_type_id
                    when @rt16 then
                        revpar
                   end
                   ) as rt16_revpar,
               (case accom_type_id
                    when @rt16 then
                        revpar_change
                   end
                   ) as rt16_revpar_change,
               (case accom_type_id
                    when @rt17 then
                        revpar
                   end
                   ) as rt17_revpar,
               (case accom_type_id
                    when @rt17 then
                        revpar_change
                   end
                   ) as rt17_revpar_change,
               (case accom_type_id
                    when @rt18 then
                        revpar
                   end
                   ) as rt18_revpar,
               (case accom_type_id
                    when @rt18 then
                        revpar_change
                   end
                   ) as rt18_revpar_change,
               (case accom_type_id
                    when @rt19 then
                        revpar
                   end
                   ) as rt19_revpar,
               (case accom_type_id
                    when @rt19 then
                        revpar_change
                   end
                   ) as rt19_revpar_change,
               (case accom_type_id
                    when @rt20 then
                        revpar
                   end
                   ) as rt20_revpar,
               (case accom_type_id
                    when @rt20 then
                        revpar_change
                   end
                   ) as rt20_revpar_change,
               (case accom_type_id
                    when @rt21 then
                        revpar
                   end
                   ) as rt21_revpar,
               (case accom_type_id
                    when @rt21 then
                        revpar_change
                   end
                   ) as rt21_revpar_change,
               (case accom_type_id
                    when @rt22 then
                        revpar
                   end
                   ) as rt22_revpar,
               (case accom_type_id
                    when @rt22 then
                        revpar_change
                   end
                   ) as rt22_revpar_change,
               (case accom_type_id
                    when @rt23 then
                        revpar
                   end
                   ) as rt23_revpar,
               (case accom_type_id
                    when @rt23 then
                        revpar_change
                   end
                   ) as rt23_revpar_change,
               (case accom_type_id
                    when @rt24 then
                        revpar
                   end
                   ) as rt24_revpar,
               (case accom_type_id
                    when @rt24 then
                        revpar_change
                   end
                   ) as rt24_revpar_change,
               (case accom_type_id
                    when @rt25 then
                        revpar
                   end
                   ) as rt25_revpar,
               (case accom_type_id
                    when @rt25 then
                        revpar_change
                   end
                   ) as rt25_revpar_change,
               (case accom_type_id
                    when @rt26 then
                        revpar
                   end
                   ) as rt26_revpar,
               (case accom_type_id
                    when @rt26 then
                        revpar_change
                   end
                   ) as rt26_revpar_change,
               (case accom_type_id
                    when @rt27 then
                        revpar
                   end
                   ) as rt27_revpar,
               (case accom_type_id
                    when @rt27 then
                        revpar_change
                   end
                   ) as rt27_revpar_change,
               (case accom_type_id
                    when @rt28 then
                        revpar
                   end
                   ) as rt28_revpar,
               (case accom_type_id
                    when @rt28 then
                        revpar_change
                   end
                   ) as rt28_revpar_change,
               (case accom_type_id
                    when @rt29 then
                        revpar
                   end
                   ) as rt29_revpar,
               (case accom_type_id
                    when @rt29 then
                        revpar_change
                   end
                   ) as rt29_revpar_change,
               (case accom_type_id
                    when @rt30 then
                        revpar
                   end
                   ) as rt30_revpar,
               (case accom_type_id
                    when @rt30 then
                        revpar_change
                   end
                   ) as rt30_revpar_change,
               (case accom_type_id
                    when @rt31 then
                        revpar
                   end
                   ) as rt31_revpar,
               (case accom_type_id
                    when @rt31 then
                        revpar_change
                   end
                   ) as rt31_revpar_change,
               (case accom_type_id
                    when @rt32 then
                        revpar
                   end
                   ) as rt32_revpar,
               (case accom_type_id
                    when @rt32 then
                        revpar_change
                   end
                   ) as rt32_revpar_change,
               (case accom_type_id
                    when @rt33 then
                        revpar
                   end
                   ) as rt33_revpar,
               (case accom_type_id
                    when @rt33 then
                        revpar_change
                   end
                   ) as rt33_revpar_change,
               (case accom_type_id
                    when @rt34 then
                        revpar
                   end
                   ) as rt34_revpar,
               (case accom_type_id
                    when @rt34 then
                        revpar_change
                   end
                   ) as rt34_revpar_change,
               (case accom_type_id
                    when @rt35 then
                        revpar
                   end
                   ) as rt35_revpar,
               (case accom_type_id
                    when @rt35 then
                        revpar_change
                   end
                   ) as rt35_revpar_change,
               (case accom_type_id
                    when @rt36 then
                        revpar
                   end
                   ) as rt36_revpar,
               (case accom_type_id
                    when @rt36 then
                        revpar_change
                   end
                   ) as rt36_revpar_change,
               (case accom_type_id
                    when @rt37 then
                        revpar
                   end
                   ) as rt37_revpar,
               (case accom_type_id
                    when @rt37 then
                        revpar_change
                   end
                   ) as rt37_revpar_change,
               (case accom_type_id
                    when @rt38 then
                        revpar
                   end
                   ) as rt38_revpar,
               (case accom_type_id
                    when @rt38 then
                        revpar_change
                   end
                   ) as rt38_revpar_change,
               (case accom_type_id
                    when @rt39 then
                        revpar
                   end
                   ) as rt39_revpar,
               (case accom_type_id
                    when @rt39 then
                        revpar_change
                   end
                   ) as rt39_revpar_change,
               (case accom_type_id
                    when @rt40 then
                        revpar
                   end
                   ) as rt40_revpar,
               (case accom_type_id
                    when @rt40 then
                        revpar_change
                   end
                   ) as rt40_revpar_change,
               (case accom_type_id
                    when @rt41 then
                        revpar
                   end
                   ) as rt41_revpar,
               (case accom_type_id
                    when @rt41 then
                        revpar_change
                   end
                   ) as rt41_revpar_change,
               (case accom_type_id
                    when @rt42 then
                        revpar
                   end
                   ) as rt42_revpar,
               (case accom_type_id
                    when @rt42 then
                        revpar_change
                   end
                   ) as rt42_revpar_change,
               (case accom_type_id
                    when @rt43 then
                        revpar
                   end
                   ) as rt43_revpar,
               (case accom_type_id
                    when @rt43 then
                        revpar_change
                   end
                   ) as rt43_revpar_change,
               (case accom_type_id
                    when @rt44 then
                        revpar
                   end
                   ) as rt44_revpar,
               (case accom_type_id
                    when @rt44 then
                        revpar_change
                   end
                   ) as rt44_revpar_change,
               (case accom_type_id
                    when @rt45 then
                        revpar
                   end
                   ) as rt45_revpar,
               (case accom_type_id
                    when @rt45 then
                        revpar_change
                   end
                   ) as rt45_revpar_change,
               (case accom_type_id
                    when @rt46 then
                        revpar
                   end
                   ) as rt46_revpar,
               (case accom_type_id
                    when @rt46 then
                        revpar_change
                   end
                   ) as rt46_revpar_change,
               (case accom_type_id
                    when @rt47 then
                        revpar
                   end
                   ) as rt47_revpar,
               (case accom_type_id
                    when @rt47 then
                        revpar_change
                   end
                   ) as rt47_revpar_change,
               (case accom_type_id
                    when @rt48 then
                        revpar
                   end
                   ) as rt48_revpar,
               (case accom_type_id
                    when @rt48 then
                        revpar_change
                   end
                   ) as rt48_revpar_change,
               (case accom_type_id
                    when @rt49 then
                        revpar
                   end
                   ) as rt49_revpar,
               (case accom_type_id
                    when @rt49 then
                        revpar_change
                   end
                   ) as rt49_revpar_change,
               (case accom_type_id
                    when @rt50 then
                        revpar
                   end
                   ) as rt50_revpar,
               (case accom_type_id
                    when @rt50 then
                        revpar_change
                   end
                   ) as rt50_revpar_change,
               (case accom_type_id
                    when @rt1 then
                        overbookingcurrent
                   end
                   ) as rt1_overbookingcurrent,
               (case accom_type_id
                    when @rt1 then
                        overbookingchange
                   end
                   ) as rt1_overbookingchange,
               (case accom_type_id
                    when @rt2 then
                        overbookingcurrent
                   end
                   ) as rt2_overbookingcurrent,
               (case accom_type_id
                    when @rt2 then
                        overbookingchange
                   end
                   ) as rt2_overbookingchange,
               (case accom_type_id
                    when @rt3 then
                        overbookingcurrent
                   end
                   ) as rt3_overbookingcurrent,
               (case accom_type_id
                    when @rt3 then
                        overbookingchange
                   end
                   ) as rt3_overbookingchange,
               (case accom_type_id
                    when @rt4 then
                        overbookingcurrent
                   end
                   ) as rt4_overbookingcurrent,
               (case accom_type_id
                    when @rt4 then
                        overbookingchange
                   end
                   ) as rt4_overbookingchange,
               (case accom_type_id
                    when @rt5 then
                        overbookingcurrent
                   end
                   ) as rt5_overbookingcurrent,
               (case accom_type_id
                    when @rt5 then
                        overbookingchange
                   end
                   ) as rt5_overbookingchange,
               (case accom_type_id
                    when @rt6 then
                        overbookingcurrent
                   end
                   ) as rt6_overbookingcurrent,
               (case accom_type_id
                    when @rt6 then
                        overbookingchange
                   end
                   ) as rt6_overbookingchange,
               (case accom_type_id
                    when @rt7 then
                        overbookingcurrent
                   end
                   ) as rt7_overbookingcurrent,
               (case accom_type_id
                    when @rt7 then
                        overbookingchange
                   end
                   ) as rt7_overbookingchange,
               (case accom_type_id
                    when @rt8 then
                        overbookingcurrent
                   end
                   ) as rt8_overbookingcurrent,
               (case accom_type_id
                    when @rt8 then
                        overbookingchange
                   end
                   ) as rt8_overbookingchange,
               (case accom_type_id
                    when @rt9 then
                        overbookingcurrent
                   end
                   ) as rt9_overbookingcurrent,
               (case accom_type_id
                    when @rt9 then
                        overbookingchange
                   end
                   ) as rt9_overbookingchange,
               (case accom_type_id
                    when @rt10 then
                        overbookingcurrent
                   end
                   ) as rt10_overbookingcurrent,
               (case accom_type_id
                    when @rt10 then
                        overbookingchange
                   end
                   ) as rt10_overbookingchange,
               (case accom_type_id
                    when @rt11 then
                        overbookingcurrent
                   end
                   ) as rt11_overbookingcurrent,
               (case accom_type_id
                    when @rt11 then
                        overbookingchange
                   end
                   ) as rt11_overbookingchange,
               (case accom_type_id
                    when @rt12 then
                        overbookingcurrent
                   end
                   ) as rt12_overbookingcurrent,
               (case accom_type_id
                    when @rt12 then
                        overbookingchange
                   end
                   ) as rt12_overbookingchange,
               (case accom_type_id
                    when @rt13 then
                        overbookingcurrent
                   end
                   ) as rt13_overbookingcurrent,
               (case accom_type_id
                    when @rt13 then
                        overbookingchange
                   end
                   ) as rt13_overbookingchange,
               (case accom_type_id
                    when @rt14 then
                        overbookingcurrent
                   end
                   ) as rt14_overbookingcurrent,
               (case accom_type_id
                    when @rt14 then
                        overbookingchange
                   end
                   ) as rt14_overbookingchange,
               (case accom_type_id
                    when @rt15 then
                        overbookingcurrent
                   end
                   ) as rt15_overbookingcurrent,
               (case accom_type_id
                    when @rt15 then
                        overbookingchange
                   end
                   ) as rt15_overbookingchange,
               (case accom_type_id
                    when @rt16 then
                        overbookingcurrent
                   end
                   ) as rt16_overbookingcurrent,
               (case accom_type_id
                    when @rt16 then
                        overbookingchange
                   end
                   ) as rt16_overbookingchange,
               (case accom_type_id
                    when @rt17 then
                        overbookingcurrent
                   end
                   ) as rt17_overbookingcurrent,
               (case accom_type_id
                    when @rt17 then
                        overbookingchange
                   end
                   ) as rt17_overbookingchange,
               (case accom_type_id
                    when @rt18 then
                        overbookingcurrent
                   end
                   ) as rt18_overbookingcurrent,
               (case accom_type_id
                    when @rt18 then
                        overbookingchange
                   end
                   ) as rt18_overbookingchange,
               (case accom_type_id
                    when @rt19 then
                        overbookingcurrent
                   end
                   ) as rt19_overbookingcurrent,
               (case accom_type_id
                    when @rt19 then
                        overbookingchange
                   end
                   ) as rt19_overbookingchange,
               (case accom_type_id
                    when @rt20 then
                        overbookingcurrent
                   end
                   ) as rt20_overbookingcurrent,
               (case accom_type_id
                    when @rt20 then
                        overbookingchange
                   end
                   ) as rt20_overbookingchange,
               (case accom_type_id
                    when @rt21 then
                        overbookingcurrent
                   end
                   ) as rt21_overbookingcurrent,
               (case accom_type_id
                    when @rt21 then
                        overbookingchange
                   end
                   ) as rt21_overbookingchange,
               (case accom_type_id
                    when @rt22 then
                        overbookingcurrent
                   end
                   ) as rt22_overbookingcurrent,
               (case accom_type_id
                    when @rt22 then
                        overbookingchange
                   end
                   ) as rt22_overbookingchange,
               (case accom_type_id
                    when @rt23 then
                        overbookingcurrent
                   end
                   ) as rt23_overbookingcurrent,
               (case accom_type_id
                    when @rt23 then
                        overbookingchange
                   end
                   ) as rt23_overbookingchange,
               (case accom_type_id
                    when @rt24 then
                        overbookingcurrent
                   end
                   ) as rt24_overbookingcurrent,
               (case accom_type_id
                    when @rt24 then
                        overbookingchange
                   end
                   ) as rt24_overbookingchange,
               (case accom_type_id
                    when @rt25 then
                        overbookingcurrent
                   end
                   ) as rt25_overbookingcurrent,
               (case accom_type_id
                    when @rt25 then
                        overbookingchange
                   end
                   ) as rt25_overbookingchange,
               (case accom_type_id
                    when @rt26 then
                        overbookingcurrent
                   end
                   ) as rt26_overbookingcurrent,
               (case accom_type_id
                    when @rt26 then
                        overbookingchange
                   end
                   ) as rt26_overbookingchange,
               (case accom_type_id
                    when @rt27 then
                        overbookingcurrent
                   end
                   ) as rt27_overbookingcurrent,
               (case accom_type_id
                    when @rt27 then
                        overbookingchange
                   end
                   ) as rt27_overbookingchange,
               (case accom_type_id
                    when @rt28 then
                        overbookingcurrent
                   end
                   ) as rt28_overbookingcurrent,
               (case accom_type_id
                    when @rt28 then
                        overbookingchange
                   end
                   ) as rt28_overbookingchange,
               (case accom_type_id
                    when @rt29 then
                        overbookingcurrent
                   end
                   ) as rt29_overbookingcurrent,
               (case accom_type_id
                    when @rt29 then
                        overbookingchange
                   end
                   ) as rt29_overbookingchange,
               (case accom_type_id
                    when @rt30 then
                        overbookingcurrent
                   end
                   ) as rt30_overbookingcurrent,
               (case accom_type_id
                    when @rt30 then
                        overbookingchange
                   end
                   ) as rt30_overbookingchange,
               (case accom_type_id
                    when @rt31 then
                        overbookingcurrent
                   end
                   ) as rt31_overbookingcurrent,
               (case accom_type_id
                    when @rt31 then
                        overbookingchange
                   end
                   ) as rt31_overbookingchange,
               (case accom_type_id
                    when @rt32 then
                        overbookingcurrent
                   end
                   ) as rt32_overbookingcurrent,
               (case accom_type_id
                    when @rt32 then
                        overbookingchange
                   end
                   ) as rt32_overbookingchange,
               (case accom_type_id
                    when @rt33 then
                        overbookingcurrent
                   end
                   ) as rt33_overbookingcurrent,
               (case accom_type_id
                    when @rt33 then
                        overbookingchange
                   end
                   ) as rt33_overbookingchange,
               (case accom_type_id
                    when @rt34 then
                        overbookingcurrent
                   end
                   ) as rt34_overbookingcurrent,
               (case accom_type_id
                    when @rt34 then
                        overbookingchange
                   end
                   ) as rt34_overbookingchange,
               (case accom_type_id
                    when @rt35 then
                        overbookingcurrent
                   end
                   ) as rt35_overbookingcurrent,
               (case accom_type_id
                    when @rt35 then
                        overbookingchange
                   end
                   ) as rt35_overbookingchange,
               (case accom_type_id
                    when @rt36 then
                        overbookingcurrent
                   end
                   ) as rt36_overbookingcurrent,
               (case accom_type_id
                    when @rt36 then
                        overbookingchange
                   end
                   ) as rt36_overbookingchange,
               (case accom_type_id
                    when @rt37 then
                        overbookingcurrent
                   end
                   ) as rt37_overbookingcurrent,
               (case accom_type_id
                    when @rt37 then
                        overbookingchange
                   end
                   ) as rt37_overbookingchange,
               (case accom_type_id
                    when @rt38 then
                        overbookingcurrent
                   end
                   ) as rt38_overbookingcurrent,
               (case accom_type_id
                    when @rt38 then
                        overbookingchange
                   end
                   ) as rt38_overbookingchange,
               (case accom_type_id
                    when @rt39 then
                        overbookingcurrent
                   end
                   ) as rt39_overbookingcurrent,
               (case accom_type_id
                    when @rt39 then
                        overbookingchange
                   end
                   ) as rt39_overbookingchange,
               (case accom_type_id
                    when @rt40 then
                        overbookingcurrent
                   end
                   ) as rt40_overbookingcurrent,
               (case accom_type_id
                    when @rt40 then
                        overbookingchange
                   end
                   ) as rt40_overbookingchange,
               (case accom_type_id
                    when @rt41 then
                        overbookingcurrent
                   end
                   ) as rt41_overbookingcurrent,
               (case accom_type_id
                    when @rt41 then
                        overbookingchange
                   end
                   ) as rt41_overbookingchange,
               (case accom_type_id
                    when @rt42 then
                        overbookingcurrent
                   end
                   ) as rt42_overbookingcurrent,
               (case accom_type_id
                    when @rt42 then
                        overbookingchange
                   end
                   ) as rt42_overbookingchange,
               (case accom_type_id
                    when @rt43 then
                        overbookingcurrent
                   end
                   ) as rt43_overbookingcurrent,
               (case accom_type_id
                    when @rt43 then
                        overbookingchange
                   end
                   ) as rt43_overbookingchange,
               (case accom_type_id
                    when @rt44 then
                        overbookingcurrent
                   end
                   ) as rt44_overbookingcurrent,
               (case accom_type_id
                    when @rt44 then
                        overbookingchange
                   end
                   ) as rt44_overbookingchange,
               (case accom_type_id
                    when @rt45 then
                        overbookingcurrent
                   end
                   ) as rt45_overbookingcurrent,
               (case accom_type_id
                    when @rt45 then
                        overbookingchange
                   end
                   ) as rt45_overbookingchange,
               (case accom_type_id
                    when @rt46 then
                        overbookingcurrent
                   end
                   ) as rt46_overbookingcurrent,
               (case accom_type_id
                    when @rt46 then
                        overbookingchange
                   end
                   ) as rt46_overbookingchange,
               (case accom_type_id
                    when @rt47 then
                        overbookingcurrent
                   end
                   ) as rt47_overbookingcurrent,
               (case accom_type_id
                    when @rt47 then
                        overbookingchange
                   end
                   ) as rt47_overbookingchange,
               (case accom_type_id
                    when @rt48 then
                        overbookingcurrent
                   end
                   ) as rt48_overbookingcurrent,
               (case accom_type_id
                    when @rt48 then
                        overbookingchange
                   end
                   ) as rt48_overbookingchange,
               (case accom_type_id
                    when @rt49 then
                        overbookingcurrent
                   end
                   ) as rt49_overbookingcurrent,
               (case accom_type_id
                    when @rt49 then
                        overbookingchange
                   end
                   ) as rt49_overbookingchange,
               (case accom_type_id
                    when @rt50 then
                        overbookingcurrent
                   end
                   ) as rt50_overbookingcurrent,
               (case accom_type_id
                    when @rt50 then
                        overbookingchange
                   end
                   ) as rt50_overbookingchange,
               --Archana added for group block and group pickup-Start
               (case accom_type_id
                    when @rt1 then
                        Blocks
                   end
                   ) as rt1_Blocks,
               (case accom_type_id
                    when @rt1 then
                        Blocks_change
                   end
                   ) as rt1_Blocks_change,
               (case accom_type_id
                    when @rt1 then
                        Blocks_Available
                   end
                   ) as rt1_Blocks_Available,
               (case accom_type_id
                    when @rt2 then
                        Blocks
                   end
                   ) as rt2_Blocks,
               (case accom_type_id
                    when @rt2 then
                        Blocks_change
                   end
                   ) as rt2_Blocks_change,
               (case accom_type_id
                    when @rt2 then
                        Blocks_Available
                   end
                   ) as rt2_Blocks_Available,
               (case accom_type_id
                    when @rt3 then
                        Blocks
                   end
                   ) as rt3_Blocks,
               (case accom_type_id
                    when @rt3 then
                        Blocks_change
                   end
                   ) as rt3_Blocks_change,
               (case accom_type_id
                    when @rt3 then
                        Blocks_Available
                   end
                   ) as rt3_Blocks_Available,
               (case accom_type_id
                    when @rt4 then
                        Blocks
                   end
                   ) as rt4_Blocks,
               (case accom_type_id
                    when @rt4 then
                        Blocks_change
                   end
                   ) as rt4_Blocks_change,
               (case accom_type_id
                    when @rt4 then
                        Blocks_Available
                   end
                   ) as rt4_Blocks_Available,
               (case accom_type_id
                    when @rt5 then
                        Blocks
                   end
                   ) as rt5_Blocks,
               (case accom_type_id
                    when @rt5 then
                        Blocks_change
                   end
                   ) as rt5_Blocks_change,
               (case accom_type_id
                    when @rt5 then
                        Blocks_Available
                   end
                   ) as rt5_Blocks_Available,
               (case accom_type_id
                    when @rt6 then
                        Blocks
                   end
                   ) as rt6_Blocks,
               (case accom_type_id
                    when @rt6 then
                        Blocks_change
                   end
                   ) as rt6_Blocks_change,
               (case accom_type_id
                    when @rt6 then
                        Blocks_Available
                   end
                   ) as rt6_Blocks_Available,
               (case accom_type_id
                    when @rt7 then
                        Blocks
                   end
                   ) as rt7_Blocks,
               (case accom_type_id
                    when @rt7 then
                        Blocks_change
                   end
                   ) as rt7_Blocks_change,
               (case accom_type_id
                    when @rt7 then
                        Blocks_Available
                   end
                   ) as rt7_Blocks_Available,
               (case accom_type_id
                    when @rt8 then
                        Blocks
                   end
                   ) as rt8_Blocks,
               (case accom_type_id
                    when @rt8 then
                        Blocks_change
                   end
                   ) as rt8_Blocks_change,
               (case accom_type_id
                    when @rt8 then
                        Blocks_Available
                   end
                   ) as rt8_Blocks_Available,
               (case accom_type_id
                    when @rt9 then
                        Blocks
                   end
                   ) as rt9_Blocks,
               (case accom_type_id
                    when @rt9 then
                        Blocks_change
                   end
                   ) as rt9_Blocks_change,
               (case accom_type_id
                    when @rt9 then
                        Blocks_Available
                   end
                   ) as rt9_Blocks_Available,
               (case accom_type_id
                    when @rt10 then
                        Blocks
                   end
                   ) as rt10_Blocks,
               (case accom_type_id
                    when @rt10 then
                        Blocks_change
                   end
                   ) as rt10_Blocks_change,
               (case accom_type_id
                    when @rt10 then
                        Blocks_Available
                   end
                   ) as rt10_Blocks_Available,
               (case accom_type_id
                    when @rt11 then
                        Blocks
                   end
                   ) as rt11_Blocks,
               (case accom_type_id
                    when @rt11 then
                        Blocks_change
                   end
                   ) as rt11_Blocks_change,
               (case accom_type_id
                    when @rt11 then
                        Blocks_Available
                   end
                   ) as rt11_Blocks_Available,
               (case accom_type_id
                    when @rt12 then
                        Blocks
                   end
                   ) as rt12_Blocks,
               (case accom_type_id
                    when @rt12 then
                        Blocks_change
                   end
                   ) as rt12_Blocks_change,
               (case accom_type_id
                    when @rt12 then
                        Blocks_Available
                   end
                   ) as rt12_Blocks_Available,
               (case accom_type_id
                    when @rt13 then
                        Blocks
                   end
                   ) as rt13_Blocks,
               (case accom_type_id
                    when @rt13 then
                        Blocks_change
                   end
                   ) as rt13_Blocks_change,
               (case accom_type_id
                    when @rt13 then
                        Blocks_Available
                   end
                   ) as rt13_Blocks_Available,
               (case accom_type_id
                    when @rt14 then
                        Blocks
                   end
                   ) as rt14_Blocks,
               (case accom_type_id
                    when @rt14 then
                        Blocks_change
                   end
                   ) as rt14_Blocks_change,
               (case accom_type_id
                    when @rt14 then
                        Blocks_Available
                   end
                   ) as rt14_Blocks_Available,
               (case accom_type_id
                    when @rt15 then
                        Blocks
                   end
                   ) as rt15_Blocks,
               (case accom_type_id
                    when @rt15 then
                        Blocks_change
                   end
                   ) as rt15_Blocks_change,
               (case accom_type_id
                    when @rt15 then
                        Blocks_Available
                   end
                   ) as rt15_Blocks_Available,
               (case accom_type_id
                    when @rt16 then
                        Blocks
                   end
                   ) as rt16_Blocks,
               (case accom_type_id
                    when @rt16 then
                        Blocks_change
                   end
                   ) as rt16_Blocks_change,
               (case accom_type_id
                    when @rt16 then
                        Blocks_Available
                   end
                   ) as rt16_Blocks_Available,
               (case accom_type_id
                    when @rt17 then
                        Blocks
                   end
                   ) as rt17_Blocks,
               (case accom_type_id
                    when @rt17 then
                        Blocks_change
                   end
                   ) as rt17_Blocks_change,
               (case accom_type_id
                    when @rt17 then
                        Blocks_Available
                   end
                   ) as rt17_Blocks_Available,
               (case accom_type_id
                    when @rt18 then
                        Blocks
                   end
                   ) as rt18_Blocks,
               (case accom_type_id
                    when @rt18 then
                        Blocks_change
                   end
                   ) as rt18_Blocks_change,
               (case accom_type_id
                    when @rt18 then
                        Blocks_Available
                   end
                   ) as rt18_Blocks_Available,
               (case accom_type_id
                    when @rt19 then
                        Blocks
                   end
                   ) as rt19_Blocks,
               (case accom_type_id
                    when @rt19 then
                        Blocks_change
                   end
                   ) as rt19_Blocks_change,
               (case accom_type_id
                    when @rt19 then
                        Blocks_Available
                   end
                   ) as rt19_Blocks_Available,
               (case accom_type_id
                    when @rt20 then
                        Blocks
                   end
                   ) as rt20_Blocks,
               (case accom_type_id
                    when @rt20 then
                        Blocks_change
                   end
                   ) as rt20_Blocks_change,
               (case accom_type_id
                    when @rt20 then
                        Blocks_Available
                   end
                   ) as rt20_Blocks_Available,
               (case accom_type_id
                    when @rt21 then
                        Blocks
                   end
                   ) as rt21_Blocks,
               (case accom_type_id
                    when @rt21 then
                        Blocks_change
                   end
                   ) as rt21_Blocks_change,
               (case accom_type_id
                    when @rt21 then
                        Blocks_Available
                   end
                   ) as rt21_Blocks_Available,
               (case accom_type_id
                    when @rt22 then
                        Blocks
                   end
                   ) as rt22_Blocks,
               (case accom_type_id
                    when @rt22 then
                        Blocks_change
                   end
                   ) as rt22_Blocks_change,
               (case accom_type_id
                    when @rt22 then
                        Blocks_Available
                   end
                   ) as rt22_Blocks_Available,
               (case accom_type_id
                    when @rt23 then
                        Blocks
                   end
                   ) as rt23_Blocks,
               (case accom_type_id
                    when @rt23 then
                        Blocks_change
                   end
                   ) as rt23_Blocks_change,
               (case accom_type_id
                    when @rt23 then
                        Blocks_Available
                   end
                   ) as rt23_Blocks_Available,
               (case accom_type_id
                    when @rt24 then
                        Blocks
                   end
                   ) as rt24_Blocks,
               (case accom_type_id
                    when @rt24 then
                        Blocks_change
                   end
                   ) as rt24_Blocks_change,
               (case accom_type_id
                    when @rt24 then
                        Blocks_Available
                   end
                   ) as rt24_Blocks_Available,
               (case accom_type_id
                    when @rt25 then
                        Blocks
                   end
                   ) as rt25_Blocks,
               (case accom_type_id
                    when @rt25 then
                        Blocks_change
                   end
                   ) as rt25_Blocks_change,
               (case accom_type_id
                    when @rt25 then
                        Blocks_Available
                   end
                   ) as rt25_Blocks_Available,
               (case accom_type_id
                    when @rt26 then
                        Blocks
                   end
                   ) as rt26_Blocks,
               (case accom_type_id
                    when @rt26 then
                        Blocks_change
                   end
                   ) as rt26_Blocks_change,
               (case accom_type_id
                    when @rt26 then
                        Blocks_Available
                   end
                   ) as rt26_Blocks_Available,
               (case accom_type_id
                    when @rt27 then
                        Blocks
                   end
                   ) as rt27_Blocks,
               (case accom_type_id
                    when @rt27 then
                        Blocks_change
                   end
                   ) as rt27_Blocks_change,
               (case accom_type_id
                    when @rt27 then
                        Blocks_Available
                   end
                   ) as rt27_Blocks_Available,
               (case accom_type_id
                    when @rt28 then
                        Blocks
                   end
                   ) as rt28_Blocks,
               (case accom_type_id
                    when @rt28 then
                        Blocks_change
                   end
                   ) as rt28_Blocks_change,
               (case accom_type_id
                    when @rt28 then
                        Blocks_Available
                   end
                   ) as rt28_Blocks_Available,
               (case accom_type_id
                    when @rt29 then
                        Blocks
                   end
                   ) as rt29_Blocks,
               (case accom_type_id
                    when @rt29 then
                        Blocks_change
                   end
                   ) as rt29_Blocks_change,
               (case accom_type_id
                    when @rt29 then
                        Blocks_Available
                   end
                   ) as rt29_Blocks_Available,
               (case accom_type_id
                    when @rt30 then
                        Blocks
                   end
                   ) as rt30_Blocks,
               (case accom_type_id
                    when @rt30 then
                        Blocks_change
                   end
                   ) as rt30_Blocks_change,
               (case accom_type_id
                    when @rt30 then
                        Blocks_Available
                   end
                   ) as rt30_Blocks_Available,
               (case accom_type_id
                    when @rt31 then
                        Blocks
                   end
                   ) as rt31_Blocks,
               (case accom_type_id
                    when @rt31 then
                        Blocks_change
                   end
                   ) as rt31_Blocks_change,
               (case accom_type_id
                    when @rt31 then
                        Blocks_Available
                   end
                   ) as rt31_Blocks_Available,
               (case accom_type_id
                    when @rt32 then
                        Blocks
                   end
                   ) as rt32_Blocks,
               (case accom_type_id
                    when @rt32 then
                        Blocks_change
                   end
                   ) as rt32_Blocks_change,
               (case accom_type_id
                    when @rt32 then
                        Blocks_Available
                   end
                   ) as rt32_Blocks_Available,
               (case accom_type_id
                    when @rt33 then
                        Blocks
                   end
                   ) as rt33_Blocks,
               (case accom_type_id
                    when @rt33 then
                        Blocks_change
                   end
                   ) as rt33_Blocks_change,
               (case accom_type_id
                    when @rt33 then
                        Blocks_Available
                   end
                   ) as rt33_Blocks_Available,
               (case accom_type_id
                    when @rt34 then
                        Blocks
                   end
                   ) as rt34_Blocks,
               (case accom_type_id
                    when @rt34 then
                        Blocks_change
                   end
                   ) as rt34_Blocks_change,
               (case accom_type_id
                    when @rt34 then
                        Blocks_Available
                   end
                   ) as rt34_Blocks_Available,
               (case accom_type_id
                    when @rt35 then
                        Blocks
                   end
                   ) as rt35_Blocks,
               (case accom_type_id
                    when @rt35 then
                        Blocks_change
                   end
                   ) as rt35_Blocks_change,
               (case accom_type_id
                    when @rt35 then
                        Blocks_Available
                   end
                   ) as rt35_Blocks_Available,
               (case accom_type_id
                    when @rt36 then
                        Blocks
                   end
                   ) as rt36_Blocks,
               (case accom_type_id
                    when @rt36 then
                        Blocks_change
                   end
                   ) as rt36_Blocks_change,
               (case accom_type_id
                    when @rt36 then
                        Blocks_Available
                   end
                   ) as rt36_Blocks_Available,
               (case accom_type_id
                    when @rt37 then
                        Blocks
                   end
                   ) as rt37_Blocks,
               (case accom_type_id
                    when @rt37 then
                        Blocks_change
                   end
                   ) as rt37_Blocks_change,
               (case accom_type_id
                    when @rt37 then
                        Blocks_Available
                   end
                   ) as rt37_Blocks_Available,
               (case accom_type_id
                    when @rt38 then
                        Blocks
                   end
                   ) as rt38_Blocks,
               (case accom_type_id
                    when @rt38 then
                        Blocks_change
                   end
                   ) as rt38_Blocks_change,
               (case accom_type_id
                    when @rt38 then
                        Blocks_Available
                   end
                   ) as rt38_Blocks_Available,
               (case accom_type_id
                    when @rt39 then
                        Blocks
                   end
                   ) as rt39_Blocks,
               (case accom_type_id
                    when @rt39 then
                        Blocks_change
                   end
                   ) as rt39_Blocks_change,
               (case accom_type_id
                    when @rt39 then
                        Blocks_Available
                   end
                   ) as rt39_Blocks_Available,
               (case accom_type_id
                    when @rt40 then
                        Blocks
                   end
                   ) as rt40_Blocks,
               (case accom_type_id
                    when @rt40 then
                        Blocks_change
                   end
                   ) as rt40_Blocks_change,
               (case accom_type_id
                    when @rt40 then
                        Blocks_Available
                   end
                   ) as rt40_Blocks_Available,
               (case accom_type_id
                    when @rt41 then
                        Blocks
                   end
                   ) as rt41_Blocks,
               (case accom_type_id
                    when @rt41 then
                        Blocks_change
                   end
                   ) as rt41_Blocks_change,
               (case accom_type_id
                    when @rt41 then
                        Blocks_Available
                   end
                   ) as rt41_Blocks_Available,
               (case accom_type_id
                    when @rt42 then
                        Blocks
                   end
                   ) as rt42_Blocks,
               (case accom_type_id
                    when @rt42 then
                        Blocks_change
                   end
                   ) as rt42_Blocks_change,
               (case accom_type_id
                    when @rt42 then
                        Blocks_Available
                   end
                   ) as rt42_Blocks_Available,
               (case accom_type_id
                    when @rt43 then
                        Blocks
                   end
                   ) as rt43_Blocks,
               (case accom_type_id
                    when @rt43 then
                        Blocks_change
                   end
                   ) as rt43_Blocks_change,
               (case accom_type_id
                    when @rt43 then
                        Blocks_Available
                   end
                   ) as rt43_Blocks_Available,
               (case accom_type_id
                    when @rt44 then
                        Blocks
                   end
                   ) as rt44_Blocks,
               (case accom_type_id
                    when @rt44 then
                        Blocks_change
                   end
                   ) as rt44_Blocks_change,
               (case accom_type_id
                    when @rt44 then
                        Blocks_Available
                   end
                   ) as rt44_Blocks_Available,
               (case accom_type_id
                    when @rt45 then
                        Blocks
                   end
                   ) as rt45_Blocks,
               (case accom_type_id
                    when @rt45 then
                        Blocks_change
                   end
                   ) as rt45_Blocks_change,
               (case accom_type_id
                    when @rt45 then
                        Blocks_Available
                   end
                   ) as rt45_Blocks_Available,
               (case accom_type_id
                    when @rt46 then
                        Blocks
                   end
                   ) as rt46_Blocks,
               (case accom_type_id
                    when @rt46 then
                        Blocks_change
                   end
                   ) as rt46_Blocks_change,
               (case accom_type_id
                    when @rt46 then
                        Blocks_Available
                   end
                   ) as rt46_Blocks_Available,
               (case accom_type_id
                    when @rt47 then
                        Blocks
                   end
                   ) as rt47_Blocks,
               (case accom_type_id
                    when @rt47 then
                        Blocks_change
                   end
                   ) as rt47_Blocks_change,
               (case accom_type_id
                    when @rt47 then
                        Blocks_Available
                   end
                   ) as rt47_Blocks_Available,
               (case accom_type_id
                    when @rt48 then
                        Blocks
                   end
                   ) as rt48_Blocks,
               (case accom_type_id
                    when @rt48 then
                        Blocks_change
                   end
                   ) as rt48_Blocks_change,
               (case accom_type_id
                    when @rt48 then
                        Blocks_Available
                   end
                   ) as rt48_Blocks_Available,
               (case accom_type_id
                    when @rt49 then
                        Blocks
                   end
                   ) as rt49_Blocks,
               (case accom_type_id
                    when @rt49 then
                        Blocks_change
                   end
                   ) as rt49_Blocks_change,
               (case accom_type_id
                    when @rt49 then
                        Blocks_Available
                   end
                   ) as rt49_Blocks_Available,
               (case accom_type_id
                    when @rt50 then
                        Blocks
                   end
                   ) as rt50_Blocks,
               (case accom_type_id
                    when @rt50 then
                        Blocks_change
                   end
                   ) as rt50_Blocks_change,
               (case accom_type_id
                    when @rt50 then
                        Blocks_Available
                   end
                   ) as rt50_Blocks_Available,
               --Archana added for group block and group pickup-End
               (case accom_type_id
                    when @rt1 then
                        barcurrent
                   end
                   ) as rt1_barcurrent,
               (case accom_type_id
                    when @rt1 then
                        barchange
                   end
                   ) as rt1_barchange,
               (case accom_type_id
                    when @rt2 then
                        barcurrent
                   end
                   ) as rt2_barcurrent,
               (case accom_type_id
                    when @rt2 then
                        barchange
                   end
                   ) as rt2_barchange,
               (case accom_type_id
                    when @rt3 then
                        barcurrent
                   end
                   ) as rt3_barcurrent,
               (case accom_type_id
                    when @rt3 then
                        barchange
                   end
                   ) as rt3_barchange,
               (case accom_type_id
                    when @rt4 then
                        barcurrent
                   end
                   ) as rt4_barcurrent,
               (case accom_type_id
                    when @rt4 then
                        barchange
                   end
                   ) as rt4_barchange,
               (case accom_type_id
                    when @rt5 then
                        barcurrent
                   end
                   ) as rt5_barcurrent,
               (case accom_type_id
                    when @rt5 then
                        barchange
                   end
                   ) as rt5_barchange,
               (case accom_type_id
                    when @rt6 then
                        barcurrent
                   end
                   ) as rt6_barcurrent,
               (case accom_type_id
                    when @rt6 then
                        barchange
                   end
                   ) as rt6_barchange,
               (case accom_type_id
                    when @rt7 then
                        barcurrent
                   end
                   ) as rt7_barcurrent,
               (case accom_type_id
                    when @rt7 then
                        barchange
                   end
                   ) as rt7_barchange,
               (case accom_type_id
                    when @rt8 then
                        barcurrent
                   end
                   ) as rt8_barcurrent,
               (case accom_type_id
                    when @rt8 then
                        barchange
                   end
                   ) as rt8_barchange,
               (case accom_type_id
                    when @rt9 then
                        barcurrent
                   end
                   ) as rt9_barcurrent,
               (case accom_type_id
                    when @rt9 then
                        barchange
                   end
                   ) as rt9_barchange,
               (case accom_type_id
                    when @rt10 then
                        barcurrent
                   end
                   ) as rt10_barcurrent,
               (case accom_type_id
                    when @rt10 then
                        barchange
                   end
                   ) as rt10_barchange,
               (case accom_type_id
                    when @rt11 then
                        barcurrent
                   end
                   ) as rt11_barcurrent,
               (case accom_type_id
                    when @rt11 then
                        barchange
                   end
                   ) as rt11_barchange,
               (case accom_type_id
                    when @rt12 then
                        barcurrent
                   end
                   ) as rt12_barcurrent,
               (case accom_type_id
                    when @rt12 then
                        barchange
                   end
                   ) as rt12_barchange,
               (case accom_type_id
                    when @rt13 then
                        barcurrent
                   end
                   ) as rt13_barcurrent,
               (case accom_type_id
                    when @rt13 then
                        barchange
                   end
                   ) as rt13_barchange,
               (case accom_type_id
                    when @rt14 then
                        barcurrent
                   end
                   ) as rt14_barcurrent,
               (case accom_type_id
                    when @rt14 then
                        barchange
                   end
                   ) as rt14_barchange,
               (case accom_type_id
                    when @rt15 then
                        barcurrent
                   end
                   ) as rt15_barcurrent,
               (case accom_type_id
                    when @rt15 then
                        barchange
                   end
                   ) as rt15_barchange,
               (case accom_type_id
                    when @rt16 then
                        barcurrent
                   end
                   ) as rt16_barcurrent,
               (case accom_type_id
                    when @rt16 then
                        barchange
                   end
                   ) as rt16_barchange,
               (case accom_type_id
                    when @rt17 then
                        barcurrent
                   end
                   ) as rt17_barcurrent,
               (case accom_type_id
                    when @rt17 then
                        barchange
                   end
                   ) as rt17_barchange,
               (case accom_type_id
                    when @rt18 then
                        barcurrent
                   end
                   ) as rt18_barcurrent,
               (case accom_type_id
                    when @rt18 then
                        barchange
                   end
                   ) as rt18_barchange,
               (case accom_type_id
                    when @rt19 then
                        barcurrent
                   end
                   ) as rt19_barcurrent,
               (case accom_type_id
                    when @rt19 then
                        barchange
                   end
                   ) as rt19_barchange,
               (case accom_type_id
                    when @rt20 then
                        barcurrent
                   end
                   ) as rt20_barcurrent,
               (case accom_type_id
                    when @rt20 then
                        barchange
                   end
                   ) as rt20_barchange,
               (case accom_type_id
                    when @rt21 then
                        barcurrent
                   end
                   ) as rt21_barcurrent,
               (case accom_type_id
                    when @rt21 then
                        barchange
                   end
                   ) as rt21_barchange,
               (case accom_type_id
                    when @rt22 then
                        barcurrent
                   end
                   ) as rt22_barcurrent,
               (case accom_type_id
                    when @rt22 then
                        barchange
                   end
                   ) as rt22_barchange,
               (case accom_type_id
                    when @rt23 then
                        barcurrent
                   end
                   ) as rt23_barcurrent,
               (case accom_type_id
                    when @rt23 then
                        barchange
                   end
                   ) as rt23_barchange,
               (case accom_type_id
                    when @rt24 then
                        barcurrent
                   end
                   ) as rt24_barcurrent,
               (case accom_type_id
                    when @rt24 then
                        barchange
                   end
                   ) as rt24_barchange,
               (case accom_type_id
                    when @rt25 then
                        barcurrent
                   end
                   ) as rt25_barcurrent,
               (case accom_type_id
                    when @rt25 then
                        barchange
                   end
                   ) as rt25_barchange,
               (case accom_type_id
                    when @rt26 then
                        barcurrent
                   end
                   ) as rt26_barcurrent,
               (case accom_type_id
                    when @rt26 then
                        barchange
                   end
                   ) as rt26_barchange,
               (case accom_type_id
                    when @rt27 then
                        barcurrent
                   end
                   ) as rt27_barcurrent,
               (case accom_type_id
                    when @rt27 then
                        barchange
                   end
                   ) as rt27_barchange,
               (case accom_type_id
                    when @rt28 then
                        barcurrent
                   end
                   ) as rt28_barcurrent,
               (case accom_type_id
                    when @rt28 then
                        barchange
                   end
                   ) as rt28_barchange,
               (case accom_type_id
                    when @rt29 then
                        barcurrent
                   end
                   ) as rt29_barcurrent,
               (case accom_type_id
                    when @rt29 then
                        barchange
                   end
                   ) as rt29_barchange,
               (case accom_type_id
                    when @rt30 then
                        barcurrent
                   end
                   ) as rt30_barcurrent,
               (case accom_type_id
                    when @rt30 then
                        barchange
                   end
                   ) as rt30_barchange,
               (case accom_type_id
                    when @rt31 then
                        barcurrent
                   end
                   ) as rt31_barcurrent,
               (case accom_type_id
                    when @rt31 then
                        barchange
                   end
                   ) as rt31_barchange,
               (case accom_type_id
                    when @rt32 then
                        barcurrent
                   end
                   ) as rt32_barcurrent,
               (case accom_type_id
                    when @rt32 then
                        barchange
                   end
                   ) as rt32_barchange,
               (case accom_type_id
                    when @rt33 then
                        barcurrent
                   end
                   ) as rt33_barcurrent,
               (case accom_type_id
                    when @rt33 then
                        barchange
                   end
                   ) as rt33_barchange,
               (case accom_type_id
                    when @rt34 then
                        barcurrent
                   end
                   ) as rt34_barcurrent,
               (case accom_type_id
                    when @rt34 then
                        barchange
                   end
                   ) as rt34_barchange,
               (case accom_type_id
                    when @rt35 then
                        barcurrent
                   end
                   ) as rt35_barcurrent,
               (case accom_type_id
                    when @rt35 then
                        barchange
                   end
                   ) as rt35_barchange,
               (case accom_type_id
                    when @rt36 then
                        barcurrent
                   end
                   ) as rt36_barcurrent,
               (case accom_type_id
                    when @rt36 then
                        barchange
                   end
                   ) as rt36_barchange,
               (case accom_type_id
                    when @rt37 then
                        barcurrent
                   end
                   ) as rt37_barcurrent,
               (case accom_type_id
                    when @rt37 then
                        barchange
                   end
                   ) as rt37_barchange,
               (case accom_type_id
                    when @rt38 then
                        barcurrent
                   end
                   ) as rt38_barcurrent,
               (case accom_type_id
                    when @rt38 then
                        barchange
                   end
                   ) as rt38_barchange,
               (case accom_type_id
                    when @rt39 then
                        barcurrent
                   end
                   ) as rt39_barcurrent,
               (case accom_type_id
                    when @rt39 then
                        barchange
                   end
                   ) as rt39_barchange,
               (case accom_type_id
                    when @rt40 then
                        barcurrent
                   end
                   ) as rt40_barcurrent,
               (case accom_type_id
                    when @rt40 then
                        barchange
                   end
                   ) as rt40_barchange,
               (case accom_type_id
                    when @rt41 then
                        barcurrent
                   end
                   ) as rt41_barcurrent,
               (case accom_type_id
                    when @rt41 then
                        barchange
                   end
                   ) as rt41_barchange,
               (case accom_type_id
                    when @rt42 then
                        barcurrent
                   end
                   ) as rt42_barcurrent,
               (case accom_type_id
                    when @rt42 then
                        barchange
                   end
                   ) as rt42_barchange,
               (case accom_type_id
                    when @rt43 then
                        barcurrent
                   end
                   ) as rt43_barcurrent,
               (case accom_type_id
                    when @rt43 then
                        barchange
                   end
                   ) as rt43_barchange,
               (case accom_type_id
                    when @rt44 then
                        barcurrent
                   end
                   ) as rt44_barcurrent,
               (case accom_type_id
                    when @rt44 then
                        barchange
                   end
                   ) as rt44_barchange,
               (case accom_type_id
                    when @rt45 then
                        barcurrent
                   end
                   ) as rt45_barcurrent,
               (case accom_type_id
                    when @rt45 then
                        barchange
                   end
                   ) as rt45_barchange,
               (case accom_type_id
                    when @rt46 then
                        barcurrent
                   end
                   ) as rt46_barcurrent,
               (case accom_type_id
                    when @rt46 then
                        barchange
                   end
                   ) as rt46_barchange,
               (case accom_type_id
                    when @rt47 then
                        barcurrent
                   end
                   ) as rt47_barcurrent,
               (case accom_type_id
                    when @rt47 then
                        barchange
                   end
                   ) as rt47_barchange,
               (case accom_type_id
                    when @rt48 then
                        barcurrent
                   end
                   ) as rt48_barcurrent,
               (case accom_type_id
                    when @rt48 then
                        barchange
                   end
                   ) as rt48_barchange,
               (case accom_type_id
                    when @rt49 then
                        barcurrent
                   end
                   ) as rt49_barcurrent,
               (case accom_type_id
                    when @rt49 then
                        barchange
                   end
                   ) as rt49_barchange,
               (case accom_type_id
                    when @rt50 then
                        barcurrent
                   end
                   ) as rt50_barcurrent,
               (case accom_type_id
                    when @rt50 then
                        barchange
                   end
                   ) as rt50_barchange,
               (case accom_type_id
                    when @rt1 then
                        barchangeLastOptimize
                   end
                   ) as rt1_barchangeLastOptimize,
               (case accom_type_id
                    when @rt2 then
                        barchangeLastOptimize
                   end
                   ) as rt2_barchangeLastOptimize,
               (case accom_type_id
                    when @rt3 then
                        barchangeLastOptimize
                   end
                   ) as rt3_barchangeLastOptimize,
               (case accom_type_id
                    when @rt4 then
                        barchangeLastOptimize
                   end
                   ) as rt4_barchangeLastOptimize,
               (case accom_type_id
                    when @rt5 then
                        barchangeLastOptimize
                   end
                   ) as rt5_barchangeLastOptimize,
               (case accom_type_id
                    when @rt6 then
                        barchangeLastOptimize
                   end
                   ) as rt6_barchangeLastOptimize,
               (case accom_type_id
                    when @rt7 then
                        barchangeLastOptimize
                   end
                   ) as rt7_barchangeLastOptimize,
               (case accom_type_id
                    when @rt8 then
                        barchangeLastOptimize
                   end
                   ) as rt8_barchangeLastOptimize,
               (case accom_type_id
                    when @rt9 then
                        barchangeLastOptimize
                   end
                   ) as rt9_barchangeLastOptimize,
               (case accom_type_id
                    when @rt10 then
                        barchangeLastOptimize
                   end
                   ) as rt10_barchangeLastOptimize,
               (case accom_type_id
                    when @rt11 then
                        barchangeLastOptimize
                   end
                   ) as rt11_barchangeLastOptimize,
               (case accom_type_id
                    when @rt12 then
                        barchangeLastOptimize
                   end
                   ) as rt12_barchangeLastOptimize,
               (case accom_type_id
                    when @rt13 then
                        barchangeLastOptimize
                   end
                   ) as rt13_barchangeLastOptimize,
               (case accom_type_id
                    when @rt14 then
                        barchangeLastOptimize
                   end
                   ) as rt14_barchangeLastOptimize,
               (case accom_type_id
                    when @rt15 then
                        barchangeLastOptimize
                   end
                   ) as rt15_barchangeLastOptimize,
               (case accom_type_id
                    when @rt16 then
                        barchangeLastOptimize
                   end
                   ) as rt16_barchangeLastOptimize,
               (case accom_type_id
                    when @rt17 then
                        barchangeLastOptimize
                   end
                   ) as rt17_barchangeLastOptimize,
               (case accom_type_id
                    when @rt18 then
                        barchangeLastOptimize
                   end
                   ) as rt18_barchangeLastOptimize,
               (case accom_type_id
                    when @rt19 then
                        barchangeLastOptimize
                   end
                   ) as rt19_barchangeLastOptimize,
               (case accom_type_id
                    when @rt20 then
                        barchangeLastOptimize
                   end
                   ) as rt20_barchangeLastOptimize,
               (case accom_type_id
                    when @rt21 then
                        barchangeLastOptimize
                   end
                   ) as rt21_barchangeLastOptimize,
               (case accom_type_id
                    when @rt22 then
                        barchangeLastOptimize
                   end
                   ) as rt22_barchangeLastOptimize,
               (case accom_type_id
                    when @rt23 then
                        barchangeLastOptimize
                   end
                   ) as rt23_barchangeLastOptimize,
               (case accom_type_id
                    when @rt24 then
                        barchangeLastOptimize
                   end
                   ) as rt24_barchangeLastOptimize,
               (case accom_type_id
                    when @rt25 then
                        barchangeLastOptimize
                   end
                   ) as rt25_barchangeLastOptimize,
               (case accom_type_id
                    when @rt26 then
                        barchangeLastOptimize
                   end
                   ) as rt26_barchangeLastOptimize,
               (case accom_type_id
                    when @rt27 then
                        barchangeLastOptimize
                   end
                   ) as rt27_barchangeLastOptimize,
               (case accom_type_id
                    when @rt28 then
                        barchangeLastOptimize
                   end
                   ) as rt28_barchangeLastOptimize,
               (case accom_type_id
                    when @rt29 then
                        barchangeLastOptimize
                   end
                   ) as rt29_barchangeLastOptimize,
               (case accom_type_id
                    when @rt30 then
                        barchangeLastOptimize
                   end
                   ) as rt30_barchangeLastOptimize,
               (case accom_type_id
                    when @rt31 then
                        barchangeLastOptimize
                   end
                   ) as rt31_barchangeLastOptimize,
               (case accom_type_id
                    when @rt32 then
                        barchangeLastOptimize
                   end
                   ) as rt32_barchangeLastOptimize,
               (case accom_type_id
                    when @rt33 then
                        barchangeLastOptimize
                   end
                   ) as rt33_barchangeLastOptimize,
               (case accom_type_id
                    when @rt34 then
                        barchangeLastOptimize
                   end
                   ) as rt34_barchangeLastOptimize,
               (case accom_type_id
                    when @rt35 then
                        barchangeLastOptimize
                   end
                   ) as rt35_barchangeLastOptimize,
               (case accom_type_id
                    when @rt36 then
                        barchangeLastOptimize
                   end
                   ) as rt36_barchangeLastOptimize,
               (case accom_type_id
                    when @rt37 then
                        barchangeLastOptimize
                   end
                   ) as rt37_barchangeLastOptimize,
               (case accom_type_id
                    when @rt38 then
                        barchangeLastOptimize
                   end
                   ) as rt38_barchangeLastOptimize,
               (case accom_type_id
                    when @rt39 then
                        barchangeLastOptimize
                   end
                   ) as rt39_barchangeLastOptimize,
               (case accom_type_id
                    when @rt40 then
                        barchangeLastOptimize
                   end
                   ) as rt40_barchangeLastOptimize,
               (case accom_type_id
                    when @rt41 then
                        barchangeLastOptimize
                   end
                   ) as rt41_barchangeLastOptimize,
               (case accom_type_id
                    when @rt42 then
                        barchangeLastOptimize
                   end
                   ) as rt42_barchangeLastOptimize,
               (case accom_type_id
                    when @rt43 then
                        barchangeLastOptimize
                   end
                   ) as rt43_barchangeLastOptimize,
               (case accom_type_id
                    when @rt44 then
                        barchangeLastOptimize
                   end
                   ) as rt44_barchangeLastOptimize,
               (case accom_type_id
                    when @rt45 then
                        barchangeLastOptimize
                   end
                   ) as rt45_barchangeLastOptimize,
               (case accom_type_id
                    when @rt46 then
                        barchangeLastOptimize
                   end
                   ) as rt46_barchangeLastOptimize,
               (case accom_type_id
                    when @rt47 then
                        barchangeLastOptimize
                   end
                   ) as rt47_barchangeLastOptimize,
               (case accom_type_id
                    when @rt48 then
                        barchangeLastOptimize
                   end
                   ) as rt48_barchangeLastOptimize,
               (case accom_type_id
                    when @rt49 then
                        barchangeLastOptimize
                   end
                   ) as rt49_barchangeLastOptimize,
               (case accom_type_id
                    when @rt50 then
                        barchangeLastOptimize
                   end
                   ) as rt50_barchangeLastOptimize,
               (case accom_type_id
                    when @rt1 then
                        decisionreasontypecurrent
                   end
                   ) as rt1_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt1 then
                        decisionreasontypechange
                   end
                   ) as rt1_decisionreasontypechange,
               (case accom_type_id
                    when @rt2 then
                        decisionreasontypecurrent
                   end
                   ) as rt2_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt2 then
                        decisionreasontypechange
                   end
                   ) as rt2_decisionreasontypechange,
               (case accom_type_id
                    when @rt3 then
                        decisionreasontypecurrent
                   end
                   ) as rt3_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt3 then
                        decisionreasontypechange
                   end
                   ) as rt3_decisionreasontypechange,
               (case accom_type_id
                    when @rt4 then
                        decisionreasontypecurrent
                   end
                   ) as rt4_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt4 then
                        decisionreasontypechange
                   end
                   ) as rt4_decisionreasontypechange,
               (case accom_type_id
                    when @rt5 then
                        decisionreasontypecurrent
                   end
                   ) as rt5_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt5 then
                        decisionreasontypechange
                   end
                   ) as rt5_decisionreasontypechange,
               (case accom_type_id
                    when @rt6 then
                        decisionreasontypecurrent
                   end
                   ) as rt6_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt6 then
                        decisionreasontypechange
                   end
                   ) as rt6_decisionreasontypechange,
               (case accom_type_id
                    when @rt7 then
                        decisionreasontypecurrent
                   end
                   ) as rt7_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt7 then
                        decisionreasontypechange
                   end
                   ) as rt7_decisionreasontypechange,
               (case accom_type_id
                    when @rt8 then
                        decisionreasontypecurrent
                   end
                   ) as rt8_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt8 then
                        decisionreasontypechange
                   end
                   ) as rt8_decisionreasontypechange,
               (case accom_type_id
                    when @rt9 then
                        decisionreasontypecurrent
                   end
                   ) as rt9_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt9 then
                        decisionreasontypechange
                   end
                   ) as rt9_decisionreasontypechange,
               (case accom_type_id
                    when @rt10 then
                        decisionreasontypecurrent
                   end
                   ) as rt10_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt10 then
                        decisionreasontypechange
                   end
                   ) as rt10_decisionreasontypechange,
               (case accom_type_id
                    when @rt11 then
                        decisionreasontypecurrent
                   end
                   ) as rt11_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt11 then
                        decisionreasontypechange
                   end
                   ) as rt11_decisionreasontypechange,
               (case accom_type_id
                    when @rt12 then
                        decisionreasontypecurrent
                   end
                   ) as rt12_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt12 then
                        decisionreasontypechange
                   end
                   ) as rt12_decisionreasontypechange,
               (case accom_type_id
                    when @rt13 then
                        decisionreasontypecurrent
                   end
                   ) as rt13_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt13 then
                        decisionreasontypechange
                   end
                   ) as rt13_decisionreasontypechange,
               (case accom_type_id
                    when @rt14 then
                        decisionreasontypecurrent
                   end
                   ) as rt14_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt14 then
                        decisionreasontypechange
                   end
                   ) as rt14_decisionreasontypechange,
               (case accom_type_id
                    when @rt15 then
                        decisionreasontypecurrent
                   end
                   ) as rt15_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt15 then
                        decisionreasontypechange
                   end
                   ) as rt15_decisionreasontypechange,
               (case accom_type_id
                    when @rt16 then
                        decisionreasontypecurrent
                   end
                   ) as rt16_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt16 then
                        decisionreasontypechange
                   end
                   ) as rt16_decisionreasontypechange,
               (case accom_type_id
                    when @rt17 then
                        decisionreasontypecurrent
                   end
                   ) as rt17_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt17 then
                        decisionreasontypechange
                   end
                   ) as rt17_decisionreasontypechange,
               (case accom_type_id
                    when @rt18 then
                        decisionreasontypecurrent
                   end
                   ) as rt18_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt18 then
                        decisionreasontypechange
                   end
                   ) as rt18_decisionreasontypechange,
               (case accom_type_id
                    when @rt19 then
                        decisionreasontypecurrent
                   end
                   ) as rt19_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt19 then
                        decisionreasontypechange
                   end
                   ) as rt19_decisionreasontypechange,
               (case accom_type_id
                    when @rt20 then
                        decisionreasontypecurrent
                   end
                   ) as rt20_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt20 then
                        decisionreasontypechange
                   end
                   ) as rt20_decisionreasontypechange,
               (case accom_type_id
                    when @rt21 then
                        decisionreasontypecurrent
                   end
                   ) as rt21_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt21 then
                        decisionreasontypechange
                   end
                   ) as rt21_decisionreasontypechange,
               (case accom_type_id
                    when @rt22 then
                        decisionreasontypecurrent
                   end
                   ) as rt22_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt22 then
                        decisionreasontypechange
                   end
                   ) as rt22_decisionreasontypechange,
               (case accom_type_id
                    when @rt23 then
                        decisionreasontypecurrent
                   end
                   ) as rt23_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt23 then
                        decisionreasontypechange
                   end
                   ) as rt23_decisionreasontypechange,
               (case accom_type_id
                    when @rt24 then
                        decisionreasontypecurrent
                   end
                   ) as rt24_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt24 then
                        decisionreasontypechange
                   end
                   ) as rt24_decisionreasontypechange,
               (case accom_type_id
                    when @rt25 then
                        decisionreasontypecurrent
                   end
                   ) as rt25_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt25 then
                        decisionreasontypechange
                   end
                   ) as rt25_decisionreasontypechange,
               (case accom_type_id
                    when @rt26 then
                        decisionreasontypecurrent
                   end
                   ) as rt26_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt26 then
                        decisionreasontypechange
                   end
                   ) as rt26_decisionreasontypechange,
               (case accom_type_id
                    when @rt27 then
                        decisionreasontypecurrent
                   end
                   ) as rt27_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt27 then
                        decisionreasontypechange
                   end
                   ) as rt27_decisionreasontypechange,
               (case accom_type_id
                    when @rt28 then
                        decisionreasontypecurrent
                   end
                   ) as rt28_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt28 then
                        decisionreasontypechange
                   end
                   ) as rt28_decisionreasontypechange,
               (case accom_type_id
                    when @rt29 then
                        decisionreasontypecurrent
                   end
                   ) as rt29_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt29 then
                        decisionreasontypechange
                   end
                   ) as rt29_decisionreasontypechange,
               (case accom_type_id
                    when @rt30 then
                        decisionreasontypecurrent
                   end
                   ) as rt30_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt30 then
                        decisionreasontypechange
                   end
                   ) as rt30_decisionreasontypechange,
               (case accom_type_id
                    when @rt31 then
                        decisionreasontypecurrent
                   end
                   ) as rt31_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt31 then
                        decisionreasontypechange
                   end
                   ) as rt31_decisionreasontypechange,
               (case accom_type_id
                    when @rt32 then
                        decisionreasontypecurrent
                   end
                   ) as rt32_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt32 then
                        decisionreasontypechange
                   end
                   ) as rt32_decisionreasontypechange,
               (case accom_type_id
                    when @rt33 then
                        decisionreasontypecurrent
                   end
                   ) as rt33_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt33 then
                        decisionreasontypechange
                   end
                   ) as rt33_decisionreasontypechange,
               (case accom_type_id
                    when @rt34 then
                        decisionreasontypecurrent
                   end
                   ) as rt34_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt34 then
                        decisionreasontypechange
                   end
                   ) as rt34_decisionreasontypechange,
               (case accom_type_id
                    when @rt35 then
                        decisionreasontypecurrent
                   end
                   ) as rt35_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt35 then
                        decisionreasontypechange
                   end
                   ) as rt35_decisionreasontypechange,
               (case accom_type_id
                    when @rt36 then
                        decisionreasontypecurrent
                   end
                   ) as rt36_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt36 then
                        decisionreasontypechange
                   end
                   ) as rt36_decisionreasontypechange,
               (case accom_type_id
                    when @rt37 then
                        decisionreasontypecurrent
                   end
                   ) as rt37_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt37 then
                        decisionreasontypechange
                   end
                   ) as rt37_decisionreasontypechange,
               (case accom_type_id
                    when @rt38 then
                        decisionreasontypecurrent
                   end
                   ) as rt38_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt38 then
                        decisionreasontypechange
                   end
                   ) as rt38_decisionreasontypechange,
               (case accom_type_id
                    when @rt39 then
                        decisionreasontypecurrent
                   end
                   ) as rt39_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt39 then
                        decisionreasontypechange
                   end
                   ) as rt39_decisionreasontypechange,
               (case accom_type_id
                    when @rt40 then
                        decisionreasontypecurrent
                   end
                   ) as rt40_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt40 then
                        decisionreasontypechange
                   end
                   ) as rt40_decisionreasontypechange,
               (case accom_type_id
                    when @rt41 then
                        decisionreasontypecurrent
                   end
                   ) as rt41_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt41 then
                        decisionreasontypechange
                   end
                   ) as rt41_decisionreasontypechange,
               (case accom_type_id
                    when @rt42 then
                        decisionreasontypecurrent
                   end
                   ) as rt42_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt42 then
                        decisionreasontypechange
                   end
                   ) as rt42_decisionreasontypechange,
               (case accom_type_id
                    when @rt43 then
                        decisionreasontypecurrent
                   end
                   ) as rt43_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt43 then
                        decisionreasontypechange
                   end
                   ) as rt43_decisionreasontypechange,
               (case accom_type_id
                    when @rt44 then
                        decisionreasontypecurrent
                   end
                   ) as rt44_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt44 then
                        decisionreasontypechange
                   end
                   ) as rt44_decisionreasontypechange,
               (case accom_type_id
                    when @rt45 then
                        decisionreasontypecurrent
                   end
                   ) as rt45_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt45 then
                        decisionreasontypechange
                   end
                   ) as rt45_decisionreasontypechange,
               (case accom_type_id
                    when @rt46 then
                        decisionreasontypecurrent
                   end
                   ) as rt46_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt46 then
                        decisionreasontypechange
                   end
                   ) as rt46_decisionreasontypechange,
               (case accom_type_id
                    when @rt47 then
                        decisionreasontypecurrent
                   end
                   ) as rt47_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt47 then
                        decisionreasontypechange
                   end
                   ) as rt47_decisionreasontypechange,
               (case accom_type_id
                    when @rt48 then
                        decisionreasontypecurrent
                   end
                   ) as rt48_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt48 then
                        decisionreasontypechange
                   end
                   ) as rt48_decisionreasontypechange,
               (case accom_type_id
                    when @rt49 then
                        decisionreasontypecurrent
                   end
                   ) as rt49_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt49 then
                        decisionreasontypechange
                   end
                   ) as rt49_decisionreasontypechange,
               (case accom_type_id
                    when @rt50 then
                        decisionreasontypecurrent
                   end
                   ) as rt50_decisionreasontypecurrent,
               (case accom_type_id
                    when @rt50 then
                        decisionreasontypechange
                   end
                   ) as rt50_decisionreasontypechange
        from
            (
                select base.occupancy_dt,
                       datename(dw, base.occupancy_dt) as dow,
                       base.property_id,
                       base.accom_type_id,
                       isnull(a.rooms_sold, 0) rooms_sold,
                       isnull(a.outoforder, 0.0) outoforder,
                       f.special_event_name as special_event,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.rooms_sold, 0.0) - isnull(e.rooms_sold, isnull(a.rooms_sold, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.rooms_sold, 0.0) - isnull(e.rooms_sold, 0.0)) as numeric(19, 2))
                           end as rooms_solds_change,
                       isnull(b.occupancyforecast, 0.0) as occupancy_nbr,
                       isnull(   (case (a.capacity - a.outoforder)
                                      when 0 then
                                          0
                                      else
                                              (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                           end
                                     ),
                                 0.0
                           ) occupancy_percent,
                       isnull(   (case a.capacity
                                      when 0 then
                                          0
                                      else
                                              (b.occupancyforecast / a.capacity) * 100
                           end
                                     ),
                                 0.0
                           ) occupancy_percent_without_ooo,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.occupancy_nbr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                                   isnull(b.occupancyforecast, 0.0) - isnull(g.occupancy_nbr, isnull(b.occupancyforecast, 0.0))
                           else
                                   isnull(b.occupancyforecast, 0.0) - isnull(g.occupancy_nbr, 0.0)
                           end as occupancy_change,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.occupancy_nbr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                                   isnull(   (case (a.capacity - a.outoforder)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                                       end
                                                 ),
                                             (case (a.capacity - a.outoforder)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                                                 end
                                                 )
                                       )
                                   - isnull(
                                           (case (e.accom_capacity_businessenddate - e.OOO_BusinessendDate)
                                                when 0 then
                                                    0
                                                else
                                                        (g.occupancy_nbr / (e.accom_capacity_businessenddate - e.OOO_BusinessendDate))
                                                        * 100
                                               end
                                               ),
                                           (case (a.capacity - a.outoforder)
                                                when 0 then
                                                    0
                                                else
                                                        (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                                               end
                                               )
                                       )
                           else
                                   isnull(   (case (a.capacity - a.outoforder)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                                       end
                                                 ),
                                             (case (a.capacity - a.outoforder)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / (a.capacity - a.outoforder)) * 100
                                                 end
                                                 )
                                       )
                                   - isnull(
                                           (case (e.accom_capacity_businessenddate - e.OOO_BusinessendDate)
                                                when 0 then
                                                    0
                                                else
                                                        (isnull(g.occupancy_nbr, 0.0)
                                                            / (e.accom_capacity_businessenddate - e.OOO_BusinessendDate)
                                                            ) * 100
                                               end
                                               ),
                                           0.0
                                       )
                           end as occupancy_perc_change,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.occupancy_nbr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                                   isnull(   (case (a.capacity)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / a.capacity) * 100
                                       end
                                                 ),
                                             (case a.capacity
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / a.capacity) * 100
                                                 end
                                                 )
                                       ) - isnull(   (case (e.accom_capacity_businessenddate)
                                                          when 0 then
                                                              0
                                                          else
                                                                  (g.occupancy_nbr / e.accom_capacity_businessenddate) * 100
                                   end
                                                         ),
                                                     (case a.capacity
                                                          when 0 then
                                                              0
                                                          else
                                                                  (b.occupancyforecast / a.capacity) * 100
                                                         end
                                                         )
                                       )
                           else
                                   isnull(   (case (a.capacity)
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / a.capacity) * 100
                                       end
                                                 ),
                                             (case a.capacity
                                                  when 0 then
                                                      0
                                                  else
                                                          (b.occupancyforecast / a.capacity) * 100
                                                 end
                                                 )
                                       ) - isnull(   (case (e.accom_capacity_businessenddate)
                                                          when 0 then
                                                              0
                                                          else
                                                                  (isnull(g.occupancy_nbr, 0.0) / e.accom_capacity_businessenddate) * 100
                                   end
                                                         ),
                                                     0.0
                                       )
                           end as occupancy_perc_change_without_ooo,
                       isnull(b.revenue, 0.0) revenue,
                       isnull(b.adr, 0.0) adr,
                       isnull(
                               (
                                   (
                                       select revpar
                                       from dbo.ufn_calculate_revpar(
                                               b.revenue,
                                               a.capacity,
                                               a.outoforder,
                                               @use_physical_capacity
                                           )
                                   )
                               ),
                               0.0
                           ) revpar,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.revenue is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                                   (
                                       select revpar
                                       from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity)
                                   )
                                   - isnull(
                                           (
                                               (
                                                   select revpar
                                                   from dbo.ufn_calculate_revpar(
                                                           g.revenue,
                                                           e.accom_capacity_businessenddate,
                                                           e.ooo_businessenddate,
                                                           @use_physical_capacity
                                                       )
                                               )
                                           ),
                                           (
                                               (
                                                   select revpar
                                                   from dbo.ufn_calculate_revpar(
                                                           b.revenue,
                                                           a.capacity,
                                                           a.outoforder,
                                                           @use_physical_capacity
                                                       )
                                               )
                                           )
                                       )
                           else
                                   (
                                       select revpar
                                       from dbo.ufn_calculate_revpar(b.revenue, a.capacity, a.outoforder, @use_physical_capacity)
                                   )
                                   - isnull(
                                           (
                                               (
                                                   select revpar
                                                   from dbo.ufn_calculate_revpar(
                                                           isnull(g.revenue, 0.0),
                                                           e.accom_capacity_businessenddate,
                                                           e.ooo_businessenddate,
                                                           @use_physical_capacity
                                                       )
                                               )
                                           ),
                                           0.0
                                       )
                           end as revpar_change,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.revenue is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(b.revenue, 0.0) - isnull(g.revenue, isnull(b.revenue, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(b.revenue, 0.0) - isnull(g.revenue, 0.0)) as numeric(19, 2))
                           end as revenue_change,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and g.adr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(b.adr, 0.0) - isnull(g.adr, isnull(b.adr, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(b.adr, 0.0) - isnull(g.adr, 0.0)) as numeric(19, 2))
                           end as adr_change,
                       isnull(a.booked_room_revenue, 0.0) booked_revenue,
                       isnull(a.booked_adr, 0.0) booked_adr,
                       isnull(a.booked_revpar, 0.0) booked_revpar,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.booked_adr, 0.0) - isnull(e.adr, isnull(a.booked_adr, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.booked_adr, 0.0) - isnull(e.adr, 0.0)) as numeric(19, 2))
                           end as booked_adr_change,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.booked_revpar, 0.0) - isnull(e.revpar, isnull(a.booked_revpar, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.booked_revpar, 0.0) - isnull(e.revpar, 0.0)) as numeric(19, 2))
                           end as booked_revpar_change,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.booked_room_revenue, 0.0)
                                   - isnull(e.room_revenue, isnull(a.booked_room_revenue, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(a.booked_room_revenue, 0.0) - isnull(e.room_revenue, 0.0)) as numeric(19, 2))
                           end as booked_revenue_change,
                       isnull(h.overbooking, 0.0) as overbookingcurrent,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and i.overbooking is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(h.overbooking, 0.0) - isnull(i.overbooking, isnull(h.overbooking, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(h.overbooking, 0.0) - isnull(i.overbooking, 0.0)) as numeric(19, 2))
                           end as overbookingchange,
                       isnull(groupBlock.Blocks, 0.0) as Blocks,
                       isnull(groupBlock.Blocks_Pickup, 0.0) as Blocks_change,
                       isnull(groupBlock.Blocks_Available, 0.0) as Blocks_Available,
                       isnull(bar.Final_BAR, 0.0) as barcurrent,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and barForBusinessDate.Final_BAR is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(bar.Final_BAR, 0.0)
                                   - isnull(barForBusinessDate.Final_BAR, isnull(bar.Final_BAR, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(bar.Final_BAR, 0.0) - isnull(barForBusinessDate.Final_BAR, 0.0)) as numeric(19, 2))
                           end as barchange,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and barForLastOptimization.Final_BAR is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(bar.Final_BAR, 0.0)
                                   - isnull(barForLastOptimization.Final_BAR, isnull(bar.Final_BAR, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(bar.Final_BAR, 0.0) - isnull(barForLastOptimization.Final_BAR, 0.0)) as numeric(19, 2))
                           end as barchangeLastOptimize,
                       case
                           when @product_code in ('AGILE_RATES', 'SMALL_GROUP')
                               then ''
                           else
                               isNull(highest_bar_current.Decision_Reason_Type, '-')
                           end as decisionreasontypecurrent,
                       case
                           when @product_code in ('AGILE_RATES', 'SMALL_GROUP')
                               then ''
                           else
                               (select dbo.ufn_determine_change_by_current_and_past_value(
                                               highest_bar_current.Decision_Reason_Type,
                                               highest_bar_change.Decision_Reason_Type))
                           end as decisionreasontypechange
                from
                    (
                        select @property_id property_id,
                               CAST(calendar_date as date) Occupancy_DT,
                               accom_type_id
                        from calendar_dim
                                 left join #tempRT
                                           on accom_type_id is not null
                        where calendar_date
                                  between @start_date and @end_date
                    ) base
                        left join
                    (
                        select *
                        from ufn_get_activity_by_individual_rt(
                                @property_id,
                                @roomtype_id,
                                @start_date,
                                @end_date,
                                @use_physical_capacity
                            )
                    ) as a
                    on base.property_id = a.property_id
                        and base.Occupancy_DT = a.occupancy_dt
                        and base.accom_type_id = a.accom_type_id
                        left join
                    (
                        select occupancy_dt,
                               property_id,
                               accom_type_id,
                               at_occupancy_forecast as occupancyforecast,
                               at_revenue as revenue,
                               at_adr as adr
                        from dbo.ufn_get_occupancy_forecast_by_individual_rt(
                                @property_id,
                                @roomtype_id,
                                3,
                                13,
                                @start_date,
                                @end_date
                            )
                    ) as b
                    on base.occupancy_dt = b.occupancy_dt
                        and base.property_id = b.property_id
                        and base.accom_type_id = b.accom_type_id
                        left join
                    (
                        select occupancy_dt,
                               property_id,
                               accom_type_id,
                               rooms_sold,
                               accom_capacity_businessstartdate as accom_capacity_businessenddate,
                               ooo_businessstartdate as ooo_businessenddate,
                               room_revenue,
                               adr,
                               revpar
                        from ufn_get_activity_asof_lastoptimization_or_businessdate_by_individual_rt(
                                @property_id,
                                @roomtype_id,
                                @business_dt,
                                @start_date,
                                @end_date,
                                @use_physical_capacity,
                                @rolling_business_dt
                            )
                    ) as e
                    on base.property_id = e.property_id
                        and base.occupancy_dt = e.occupancy_dt
                        and base.accom_type_id = e.accom_type_id
                        left join
                    (
                        select *
                        from ufn_get_special_events_including_information_only_events_by_property(@property_id, @start_date, @end_date)
                    ) as f
                    on base.property_id = f.property_id
                        and base.occupancy_dt = f.event_cal_date
                        left join
                    (
                        select occupancy_dt,
                               property_id,
                               accom_type_id,
                               occupancy_nbr_businessstartdate as occupancy_nbr,
                               revenue_businessstartdate as revenue,
                               adr
                        from dbo.ufn_get_occupancy_forecast_asof_lastoptimization_or_businessdate_by_individual_rt(
                                @property_id,
                                @roomtype_id,
                                @business_dt,
                                @start_date,
                                @end_date,
                                @rolling_business_dt
                            )
                    ) as g
                    on base.property_id = g.property_id
                        and base.occupancy_dt = g.occupancy_dt
                        and base.accom_type_id = g.accom_type_id
                        left join
                    (
                        select *
                        from ufn_get_ovrbk_decision_by_individual_rt(@property_id, @roomtype_id, @start_date, @end_date)
                    ) as h
                    on base.property_id = h.property_id
                        and base.occupancy_dt = h.occupancy_dt
                        and base.accom_type_id = h.accom_type_id
                        left join
                    #overbookingasofdate as i
                    on base.property_id = i.property_id
                        and base.occupancy_dt = i.occupancy_dt
                        and base.accom_type_id = i.accom_type_id
                        --Archana added group block-START
                        left join
                    #GroupBlock as groupBlock
                    on base.property_id = groupBlock.property_id
                        and base.Occupancy_DT = groupBlock.occupancy_dt
                        and base.accom_type_id = groupBlock.Accom_type_Id
                        --Archana added group block-END
                        left join
                    (
                        select cdbo.Property_ID,
                               cdbo.Arrival_DT,
                               cdbo.Accom_Type_ID,
                               cdbo.Final_BAR
                        from dbo.CP_Decision_Bar_Output cdbo
                        where cdbo.Property_ID = @property_id
                          and cdbo.Arrival_DT
                            between @start_date and @end_date
                          and cdbo.Product_ID = @product_id
                          and cdbo.Accom_Type_ID in (
                            SELECT Value FROM varcharToInt(@roomtype_id, ',')
                        )
                    ) as bar
                    on base.property_id = bar.property_id
                        and base.Occupancy_DT = bar.Arrival_DT
                        and base.accom_type_id = bar.Accom_Type_ID
                        left join
                    (
                        select @property_id as Property_ID,
                               barForBusinessDateTemp.Arrival_DT,
                               barForBusinessDateTemp.Accom_Type_ID,
                               barForBusinessDateTemp.Final_BAR
                        from
                            (
                                select cpdbo.decision_ID,
                                       cpdbo.Accom_Type_ID,
                                       cpdbo.Arrival_DT,
                                       cpdbo.Final_BAR,
                                       cpdbo.business_dt,
                                       ROW_NUMBER() over (PARTITION BY cpdbo.arrival_dt,
                                                               cpdbo.Accom_Type_ID
                                                  order by cpdbo.decision_id desc
                                                 ) as rn
                                from CP_Pace_Decision_Bar_Output_Differential cpdbo
											inner join Decision d
											            on d.Decision_ID = cpdbo.Decision_ID
														left join #Temp_BDE_decision_Ids tbd
														on (cpdbo.Decision_ID = tbd.maxDecisionIdOfBDE or tbd.maxDecisionIdOfBDE > 0)
                                where cpdbo.Accom_Type_ID in (
                                    SELECT Value FROM varcharToInt(@roomtype_id, ',')
                                )
                                  and cpdbo.business_dt <= @business_dt
                                  and cpdbo.Arrival_DT
                                    between @start_date and @end_date
                                  and cpdbo.Arrival_DT > @business_dt
                                  and cpdbo.Product_ID = @product_id
								  and ((tbd.Business_DT is not null and cpdbo.Decision_ID <= tbd.maxDecisionIdOfBDE))
                            ) barForBusinessDateTemp
                        where barForBusinessDateTemp.rn = 1
                    ) as barForBusinessDate
                    on base.property_id = barForBusinessDate.property_id
                        and base.Occupancy_DT = barForBusinessDate.Arrival_DT
                        and base.accom_type_id = barForBusinessDate.Accom_Type_ID
                        left join
                    (
                        select @property_id as Property_ID,
                               Arrival_DT,
                               Accom_Type_ID,
                               Final_BAR
                        from
                            (
                                select ROW_NUMBER() over (PARTITION BY barForLastOptimizationA.Accom_Type_ID,
                                                               barForLastOptimizationA.Arrival_DT
                                                  order by barForLastOptimizationA.Decision_ID desc
                                                 ) as rn1,
                                        *
                                from
                                    (
                                        select @property_id as Property_ID,
                                               pcdbo.Arrival_DT,
                                               pcdbo.Accom_Type_ID,
                                               pcdbo.Final_BAR,
                                               pcdbo.Decision_Id,
                                               ROW_NUMBER() over (PARTITION BY pcdbo.Product_Id,
                                                                   pcdbo.Accom_Type_ID,
                                                                   pcdbo.Arrival_DT,
                                                                   pcdbo.LOS,
                                                                   d.decision_type_id,
                                                                   pcdbo.decision_reason_type_id
                                                      order by pcdbo.Decision_ID desc
                                                     ) as rn
                                        from dbo.CP_Pace_Decision_Bar_Output_Differential pcdbo
                                                 INNER JOIN Decision d
                                                            ON pcdbo.Decision_ID = d.Decision_ID
															left join #Temp_BDE_CDP_decision_Ids tbcd
														on (pcdbo.Decision_ID = tbcd.maxDecisionIdOfBdeCdp or tbcd.maxDecisionIdOfBdeCdp > 0)
                                        where pcdbo.Product_ID = @product_id
                                          and pcdbo.Arrival_DT
                                            between @start_date and @end_date
                                          and pcdbo.Arrival_DT > @business_dt
                                          and pcdbo.Accom_Type_ID in (
                                            SELECT Value FROM varcharToInt(@roomtype_id, ',')
                                        )
                                          and d.Decision_ID <=
                                              (
                                                  SELECT TOP 1
                                          x.Decision_ID
                                                  From
                                                      (
                                                          select Top 2
                                              *
                                                          from Decision d
                                                          where ((tbcd.Business_DT is not null and pcdbo.Decision_ID <= tbcd.maxDecisionIdOfBdeCdp))
                                                          ORDER BY Decision_ID DESC
                                                      ) x
                                                  ORDER BY Decision_ID desc
                                              )
                                    ) barForLastOptimizationA
                                where barForLastOptimizationA.rn = 1
                            ) barForLastOptimizationB
                        where barForLastOptimizationB.rn1 = 1
                    ) as barForLastOptimization
                    on base.property_id = barForLastOptimization.property_id
                        and base.Occupancy_DT = barForLastOptimization.Arrival_DT
                        and base.accom_type_id = barForLastOptimization.Accom_Type_ID
                        left join
                    (
                        select cdbo.Property_id,
                               cdbo.Arrival_dt,
                               cdbo.Accom_Type_ID,
                               (case cdbo.Decision_Reason_Type_ID
                                    when 2 then
                                        'Y'
                                    else
                                        'N'
                                   end
                                   ) as Decision_Reason_Type
                        from dbo.CP_Decision_Bar_Output cdbo
                        where cdbo.Property_ID = @property_id
                          and cdbo.los = -1
                          and cdbo.Accom_Type_ID in (
                            SELECT Value FROM varcharToInt(@roomtype_id, ',')
                        )
                          and cdbo.Arrival_DT
                            between @start_date and @end_date
                          and cdbo.Product_ID = @product_id
                    ) as highest_bar_current
                    on base.property_id = highest_bar_current.Property_ID
                        and base.occupancy_dt = highest_bar_current.Arrival_DT
                        and base.accom_type_id = highest_bar_current.Accom_Type_ID
                        left join
                    (
                        select @property_id as Property_ID,
                               Arrival_DT,
                               Accom_Type_ID,
                               (case Decision_Reason_Type_id
                                    when 2 then
                                        'Y'
                                    else
                                        'N'
                                   end
                                   ) as Decision_Reason_Type
                        from
                            (
                                select ROW_NUMBER() over (PARTITION BY fcpdbod.arrival_dt,
                                                               fcpdbod.Accom_Type_ID
                                                  order by fcpdbod.decision_id desc
                                                 ) as rn1,
                                        fcpdbod.Arrival_DT,
                                       fcpdbod.Accom_Type_ID,
                                       fcpdbod.Decision_Reason_Type_id
                                from
                                    (
                                        select ROW_NUMBER() over (PARTITION BY cpdbod.arrival_dt,
                                                                   cpdbod.Accom_Type_ID,
                                                                   cpdbod.Decision_Reason_Type_id
                                                      order by cpdbod.decision_id desc
                                                     ) as rn,
                                                cpdbod.decision_id,
                                               cpdbod.Arrival_DT,
                                               cpdbod.Accom_Type_ID,
                                               cpdbod.Decision_Reason_Type_id
                                        from CP_Pace_Decision_Bar_Output_Differential cpdbod
                                        where cpdbod.business_dt <= @business_dt
                                          and cpdbod.Arrival_DT
                                            between @start_date and @end_date
                                          and cpdbod.Arrival_DT > @business_dt
                                          and cpdbod.Product_ID = @product_id
                                          and cpdbod.Accom_Type_ID in (
                                            SELECT Value FROM varcharToInt(@roomtype_id, ',')
                                        )
                                    ) fcpdbod
                                where fcpdbod.rn = 1
                            ) highest_bar_changeTemp
                        where highest_bar_changeTemp.rn1 = 1
                    ) as highest_bar_change
                    on base.property_id = highest_bar_change.Property_ID
                        and base.occupancy_dt = highest_bar_change.Arrival_DT
                        and base.accom_type_id = highest_bar_change.Accom_Type_ID
            ) data
    ) data2
group by occupancy_dt,
         dow,
         special_event
order by occupancy_dt

drop table #tempRT
drop table #Temp_BDE_decision_Ids
drop table #Temp_BDE_CDP_decision_Ids
drop table #overbookingasofdate
drop table #GroupBlock
    return
end