DROP PROCEDURE IF EXISTS [dbo].[DQI_Past_Occ_Consistent_At_MS]
GO
CREATE PROCEDURE [dbo].[DQI_Past_Occ_Consistent_At_MS] @property_ID INT, @system_date Date, @DQI_ID INT, @detail_analysis as CHAR(1) = 'N'
AS
BEGIN
	DECLARE @percent_of_deviation AS Numeric(5,2)= 5
	DECLARE @percent_of_deviation_actual AS Numeric(5,2)= @percent_of_deviation/100
	DECLARE @no_of_history_days AS Integer
	DECLARE @past_2_years_date AS DATE = DATEADD(D, 2*365*-1, @system_date)
	
	/** get how many days of history data we have **/
	SELECT @no_of_history_days=COUNT(*)
	FROM
	(
		Select property_ID, occupancy_DT FROM mkt_accom_activity WHERE Property_ID = @property_ID AND Occupancy_DT between @past_2_years_date and DATEADD(d, -1, @system_date)
		AND occupancy_DT NOT IN 
			(
				select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
				INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
				and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
				WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date)  between @past_2_years_date and DATEADD(d, -1, @system_date)
				and ip_cfg_mark_property_date.Status_ID = 1
			)		
		Group by Property_ID, Occupancy_DT
	) as OuterTable
	Group by property_ID
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	
	IF OBJECT_ID('tempdb..#Table_Final_Details_AllData') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final_Details_AllData
	END

	select todays.property_ID, todays.Occupancy_DT, todays.arrivals as todays_arrivals, todays.departures as todays_departures,yesterdays_arrivals,yesterdays.Rooms_Sold as yesterdays_sold,
	todays.rooms_sold as todays_sold, yesterdays.Rooms_Sold + todays.Arrivals - todays.Departures as todays_sold_calculated,
	@percent_of_deviation_actual*todays.Rooms_Sold as five_percent_of_solds,
	CASE WHEN @percent_of_deviation_actual*todays.Rooms_Sold > 2 then @percent_of_deviation_actual*todays.Rooms_Sold else 2 end as five_percent_of_solds_logical,
	abs(todays.Rooms_Sold - (yesterdays.Rooms_Sold + todays.Arrivals - todays.Departures)) as absolute_descrepancy_solds
	INTO #Table_Final_Details_AllData
	from 
	(
		select property_ID, occupancy_DT, sum(rooms_sold) as rooms_sold, sum(arrivals) as arrivals, 
		SUM(departures) as departures
		 from mkt_accom_activity as todays
		 WHERE property_ID = @property_ID AND todays.Occupancy_DT between @past_2_years_date and DATEADD(d, -1, @system_date)
		AND todays.occupancy_DT NOT IN 
		(
			select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
			INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
			and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
			WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date)  between @past_2_years_date and DATEADD(d, -1, @system_date)
			and ip_cfg_mark_property_date.Status_ID = 1
		)		
		 group by property_ID, Occupancy_DT
	 ) as todays
	INNER JOIN 
	(
		select property_ID, DATEADD(d, 1, occupancy_DT) as occupancy_DT, sum(rooms_sold) as rooms_sold,SUM(Arrivals) as yesterdays_arrivals from mkt_accom_activity 
		WHERE property_ID = @property_ID AND  Occupancy_DT between @past_2_years_date and DATEADD(d, -1, @system_date)
		AND occupancy_DT NOT IN 
		(
			select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
			INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
			and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
			WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date)  between @past_2_years_date and DATEADD(d, -1, @system_date) 
			and ip_cfg_mark_property_date.Status_ID = 1
		)		
		group by property_ID, Occupancy_DT
	) as yesterdays ON todays.property_ID = yesterdays.property_ID AND yesterdays.Occupancy_DT = todays.Occupancy_DT
		
	select * into #Table_Final from #Table_Final_Details_AllData WHERE
		abs(todays_sold - (yesterdays_sold + todays_arrivals - todays_departures))> CASE WHEN @percent_of_deviation_actual*todays_sold > 2 then @percent_of_deviation_actual*todays_sold else 2 end
			
	if (@detail_analysis)='Y'
	begin
		SELECT property_ID, Occupancy_DT, todays_arrivals, todays_departures,yesterdays_arrivals,yesterdays_sold,
			todays_sold, todays_sold_calculated, five_percent_of_solds,
			CASE WHEN @percent_of_deviation_actual*todays_sold > 2 then @percent_of_deviation_actual*todays_sold else 2 end as five_percent_of_solds_logical,
			abs(todays_sold - (yesterdays_sold + todays_arrivals - todays_departures)) as absolute_descrepancy_solds,			
			case when @percent_of_deviation_actual*todays_sold > 2 and abs(todays_sold - (yesterdays_sold + todays_arrivals - todays_departures)) > (@percent_of_deviation_actual*todays_sold)
				then 'Y'
				when 2 >= (@percent_of_deviation_actual*todays_sold) and abs(todays_sold - (yesterdays_sold + todays_arrivals - todays_departures)) > 2
				then 'Y'
				else 'N'
			end as error
		FROM #Table_Final_Details_AllData
		order by Occupancy_DT
	end

	IF exists(SELECT TOP 1 property_ID FROM #Table_Final)
	begin
		SELECT @DQI_ID AS DQI_ID, property_ID, @no_of_history_days as actual_days_analysed, count_failed_days, 
			case when count_failed_days > .05*@no_of_history_days then 'RED' else case when count_failed_days > .02*@no_of_history_days then 'YELLOW' else 'GREEN' end end
			as indicator
			FROM
			(		
				SELECT property_ID, COUNT(*) as count_failed_days
				from #Table_Final
				group by property_ID
			) as outerTable
	end
	else
	begin
		SELECT @DQI_ID AS DQI_ID, @property_ID as property_ID, CASE when @no_of_history_days IS null then 0 else @no_of_history_days  end as actual_days_analysed, 0 as count_failed_days, CASE when @no_of_history_days IS null then 'RED' else 'GREEN'  end as indicator
	end

	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	IF OBJECT_ID('tempdb..#Table_Final_Details_AllData') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final_Details_AllData
	END
END
GO


