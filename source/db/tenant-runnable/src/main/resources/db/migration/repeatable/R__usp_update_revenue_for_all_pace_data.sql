DROP PROCEDURE IF EXISTS [dbo].[usp_update_revenue_for_all_pace_data]
GO

CREATE PROCEDURE [dbo].[usp_update_revenue_for_all_pace_data]
(
	@fileMetadataId INT,
	@snapshotDt DATE,
	@maxNonZeroDt DATE,
	@removeAMSFromIntegration BIT
)
AS
SET NOCOUNT ON
BEGIN
	DECLARE @businessStartDate DATE, @businessEndDate DATE
	SELECT  @businessStartDate = DATEADD(D,-Past_Window_Size,SnapShot_DT),
			@businessEndDate = DATEADD(D,Future_Window_Size-1,SnapShot_DT)
	FROM file_metadata WHERE file_metadata_id = @fileMetadataId
	
	IF(@removeAMSFromIntegration = 0)
	BEGIN		
		UPDATE PMA SET PMA.Room_Revenue = MAA_Grouped.Rate_Value
		FROM PACE_Mkt_Activity PMA
		INNER JOIN (SELECT MAA.Occupancy_DT, MAA.Mkt_Seg_ID, SUM(MAA.Room_Revenue) as Rate_Value 
			FROM Mkt_Accom_activity	MAA 
			WHERE MAA.Occupancy_DT BETWEEN @businessStartDate AND @businessEndDate
			GROUP BY MAA.Occupancy_DT, MAA.Mkt_Seg_ID) AS MAA_Grouped
		ON PMA.Occupancy_DT = MAA_Grouped.Occupancy_DT
		AND PMA.Mkt_Seg_ID = MAA_Grouped.Mkt_Seg_ID
		WHERE PMA.Business_Day_End_DT = CASE WHEN cast(MAA_Grouped.Occupancy_DT as date)< @snapshotDt THEN MAA_Grouped.Occupancy_DT ELSE (DATEADD(DD, -1, @snapshotDt)) END
		AND MAA_Grouped.Occupancy_DT <= @maxNonZeroDt

		UPDATE PAA SET PAA.Room_Revenue = MAA_Grouped.Rate_Value
		FROM PACE_Accom_Activity PAA
		INNER JOIN (SELECT MAA.Occupancy_DT, MAA.Accom_Type_ID, SUM(MAA.Room_Revenue) as Rate_Value 
			FROM Mkt_Accom_activity	MAA 
			WHERE MAA.Occupancy_DT BETWEEN @businessStartDate AND @businessEndDate
			GROUP BY MAA.Occupancy_DT, MAA.Accom_Type_ID) AS MAA_Grouped
		ON PAA.Occupancy_DT = MAA_Grouped.Occupancy_DT
		AND PAA.Accom_Type_ID = MAA_Grouped.Accom_Type_ID
		WHERE PAA.Business_Day_End_DT = CASE WHEN cast(MAA_Grouped.Occupancy_DT as date) < @snapshotDt THEN MAA_Grouped.Occupancy_DT ELSE (DATEADD(DD, -1, @snapshotDt)) END
		AND MAA_Grouped.Occupancy_DT <= @maxNonZeroDt

	END
	ELSE
	BEGIN
		UPDATE PAA SET PAA.Room_Revenue = CMAA_Grouped.Rate_Value
		FROM PACE_Accom_Activity PAA
		INNER JOIN (SELECT CMAA.Occupancy_DT, CMAA.Accom_Type_ID, SUM(CMAA.Room_Revenue) as Rate_Value 
			FROM Current_Mkt_Accom_activity	CMAA 
			GROUP BY CMAA.Occupancy_DT, CMAA.Accom_Type_ID) AS CMAA_Grouped
		ON PAA.Occupancy_DT=CMAA_Grouped.Occupancy_DT
		AND PAA.Accom_Type_ID=CMAA_Grouped.Accom_Type_ID
		WHERE PAA.Business_Day_End_DT = CASE WHEN cast(CMAA_Grouped.Occupancy_DT as date)< @snapshotDt THEN CMAA_Grouped.Occupancy_DT ELSE (DATEADD(DD, -1, @snapshotDt)) END
		AND CMAA_Grouped.Occupancy_DT<=@maxNonZeroDt
	END

	UPDATE PTA SET PTA.Room_Revenue = AA_Grouped.Rate_Value
	FROM PACE_Total_Activity PTA
	INNER JOIN (SELECT AA.Occupancy_DT, SUM(AA.Room_Revenue) as Rate_Value 
		FROM Accom_Activity	AA 
		WHERE AA.Occupancy_DT BETWEEN @businessStartDate AND @businessEndDate
		GROUP BY AA.Occupancy_DT) AS AA_Grouped
	ON PTA.Occupancy_DT = AA_Grouped.Occupancy_DT
	WHERE PTA.Business_Day_End_DT = CASE WHEN cast(AA_Grouped.Occupancy_DT as date) < @snapshotDt THEN AA_Grouped.Occupancy_DT ELSE (DATEADD(DD, -1, @snapshotDt)) END
	AND AA_Grouped.Occupancy_DT <= @maxNonZeroDt
	
END