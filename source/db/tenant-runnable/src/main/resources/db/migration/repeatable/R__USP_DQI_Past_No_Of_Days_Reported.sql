DROP PROCEDURE IF EXISTS [DQI_Past_No_Of_Days_Reported]
GO
CREATE PROCEDURE [dbo].[DQI_Past_No_Of_Days_Reported] @property_ID INT, @system_date Date, @DQI_ID int, @detail_analysis as CHAR(1) = 'N'
AS
BEGIN
	DECLARE @MIN_OCCUPANCY_DATE AS DATE
	select @MIN_OCCUPANCY_DATE = MIN(occupancy_dt) from Total_Activity where Rooms_Sold >0
	/** past no. of days reported **/
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	select property_ID, count(*) as available_past_days INTO #Table_Final
		from total_Activity where property_ID = @property_ID AND occupancy_dt < @system_date
		AND occupancy_DT NOT IN
		(
				select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
				INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
				and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
				WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date)  < @system_date 
				and ip_cfg_mark_property_date.Status_ID = 1
		)
		And Occupancy_DT >= @MIN_OCCUPANCY_DATE	
		group by property_ID

	if (@detail_analysis)='Y'
		SELECT * FROM #Table_Final

	If exists (SELECT top 1 property_ID FROM #Table_Final)
	BEGIN
		SELECT @DQI_ID as DQI_ID, property_ID, available_past_days as actual_days_analysed, 
		case WHEN available_past_days >= 365 THEN 0 ELSE (365-available_past_days) END as count_of_failed_days, 
		case WHEN available_past_days >= 365 THEN 'GREEN' ELSE 'RED' END as indicator
		FROM #Table_Final
	END
	ELSE
	BEGIN
		SELECT @DQI_ID as DQI_ID, @property_ID as property_ID, 0 as actual_days_analysed, 365 as count_of_failed_days, 'RED' as indicator
	END
	
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
END
GO


