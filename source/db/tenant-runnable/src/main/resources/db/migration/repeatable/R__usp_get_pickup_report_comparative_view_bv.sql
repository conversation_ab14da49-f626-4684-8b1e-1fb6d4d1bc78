DROP PROCEDURE IF EXISTS [dbo].[usp_get_pickup_report_comparative_view_BV]
    GO

CREATE PROCEDURE [dbo].[usp_get_pickup_report_comparative_view_BV]
(
    @property_id int,
    @Business_Group_ID varchar(1000),
    @business_start_dt date,
    @business_end_dt date,
    @start_date date,
    @end_date date,
    @isRollingDate int,
    @rolling_business_start_dt nvarchar(50),
    @rolling_business_end_dt nvarchar(50),
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50)
)
as
begin
    declare @caughtupdate date
    declare @occupancy_dt date
    declare @dow varchar(10)
    declare @bv1_businessgroupname nvarchar(100)
    declare @bv2_businessgroupname nvarchar(100)
    declare @bv3_businessgroupname nvarchar(100)
    declare @bv4_businessgroupname nvarchar(100)
    declare @bv5_businessgroupname nvarchar(100)
    declare @bv6_businessgroupname nvarchar(100)
    declare @bv7_businessgroupname nvarchar(100)
    declare @bv8_businessgroupname nvarchar(100)
    declare @bv9_businessgroupname nvarchar(100)
    declare @bv10_businessgroupname nvarchar(100)
    declare @bv11_businessgroupname nvarchar(100)
    declare @bv12_businessgroupname nvarchar(100)
    declare @bv13_businessgroupname nvarchar(100)
    declare @bv14_businessgroupname nvarchar(100)
    declare @bv15_businessgroupname nvarchar(100)
    declare @bv16_businessgroupname nvarchar(100)
    declare @bv17_businessgroupname nvarchar(100)
    declare @bv18_businessgroupname nvarchar(100)
    declare @bv19_businessgroupname nvarchar(100)
    declare @bv20_businessgroupname nvarchar(100)
    declare @bv21_businessgroupname nvarchar(100)
    declare @bv22_businessgroupname nvarchar(100)
    declare @bv23_businessgroupname nvarchar(100)
    declare @bv24_businessgroupname nvarchar(100)
    declare @bv25_businessgroupname nvarchar(100)
    declare @bv26_businessgroupname nvarchar(100)
    declare @bv27_businessgroupname nvarchar(100)
    declare @bv28_businessgroupname nvarchar(100)
    declare @bv29_businessgroupname nvarchar(100)
    declare @bv30_businessgroupname nvarchar(100)
    declare @bv31_businessgroupname nvarchar(100)
    declare @bv32_businessgroupname nvarchar(100)
    declare @bv33_businessgroupname nvarchar(100)
    declare @bv34_businessgroupname nvarchar(100)
    declare @bv35_businessgroupname nvarchar(100)
    declare @bv36_businessgroupname nvarchar(100)
    declare @bv37_businessgroupname nvarchar(100)
    declare @bv38_businessgroupname nvarchar(100)
    declare @bv39_businessgroupname nvarchar(100)
    declare @bv40_businessgroupname nvarchar(100)
    declare @bv41_businessgroupname nvarchar(100)
    declare @bv42_businessgroupname nvarchar(100)
    declare @bv43_businessgroupname nvarchar(100)
    declare @bv44_businessgroupname nvarchar(100)
    declare @bv45_businessgroupname nvarchar(100)
    declare @bv46_businessgroupname nvarchar(100)
    declare @bv47_businessgroupname nvarchar(100)
    declare @bv48_businessgroupname nvarchar(100)
    declare @bv49_businessgroupname nvarchar(100)
    declare @bv50_businessgroupname nvarchar(100)
    declare @bv1_roomsoldcurrent int
    declare @bv1_roomssoldpickup int
    declare @bv2_roomsoldcurrent int
    declare @bv2_roomssoldpickup int
    declare @bv3_roomsoldcurrent int
    declare @bv3_roomssoldpickup int
    declare @bv4_roomsoldcurrent int
    declare @bv4_roomssoldpickup int
    declare @bv5_roomsoldcurrent int
    declare @bv5_roomssoldpickup int
    declare @bv6_roomsoldcurrent int
    declare @bv6_roomssoldpickup int
    declare @bv7_roomsoldcurrent int
    declare @bv7_roomssoldpickup int
    declare @bv8_roomsoldcurrent int
    declare @bv8_roomssoldpickup int
    declare @bv9_roomsoldcurrent int
    declare @bv9_roomssoldpickup int
    declare @bv10_roomsoldcurrent int
    declare @bv10_roomssoldpickup int
    declare @bv11_roomsoldcurrent int
    declare @bv11_roomssoldpickup int
    declare @bv12_roomsoldcurrent int
    declare @bv12_roomssoldpickup int
    declare @bv13_roomsoldcurrent int
    declare @bv13_roomssoldpickup int
    declare @bv14_roomsoldcurrent int
    declare @bv14_roomssoldpickup int
    declare @bv15_roomsoldcurrent int
    declare @bv15_roomssoldpickup int
    declare @bv16_roomsoldcurrent int
    declare @bv16_roomssoldpickup int
    declare @bv17_roomsoldcurrent int
    declare @bv17_roomssoldpickup int
    declare @bv18_roomsoldcurrent int
    declare @bv18_roomssoldpickup int
    declare @bv19_roomsoldcurrent int
    declare @bv19_roomssoldpickup int
    declare @bv20_roomsoldcurrent int
    declare @bv20_roomssoldpickup int
    declare @bv21_roomsoldcurrent int
    declare @bv21_roomssoldpickup int
    declare @bv22_roomsoldcurrent int
    declare @bv22_roomssoldpickup int
    declare @bv23_roomsoldcurrent int
    declare @bv23_roomssoldpickup int
    declare @bv24_roomsoldcurrent int
    declare @bv24_roomssoldpickup int
    declare @bv25_roomsoldcurrent int
    declare @bv25_roomssoldpickup int
    declare @bv26_roomsoldcurrent int
    declare @bv26_roomssoldpickup int
    declare @bv27_roomsoldcurrent int
    declare @bv27_roomssoldpickup int
    declare @bv28_roomsoldcurrent int
    declare @bv28_roomssoldpickup int
    declare @bv29_roomsoldcurrent int
    declare @bv29_roomssoldpickup int
    declare @bv30_roomsoldcurrent int
    declare @bv30_roomssoldpickup int
    declare @bv31_roomsoldcurrent int
    declare @bv31_roomssoldpickup int
    declare @bv32_roomsoldcurrent int
    declare @bv32_roomssoldpickup int
    declare @bv33_roomsoldcurrent int
    declare @bv33_roomssoldpickup int
    declare @bv34_roomsoldcurrent int
    declare @bv34_roomssoldpickup int
    declare @bv35_roomsoldcurrent int
    declare @bv35_roomssoldpickup int
    declare @bv36_roomsoldcurrent int
    declare @bv36_roomssoldpickup int
    declare @bv37_roomsoldcurrent int
    declare @bv37_roomssoldpickup int
    declare @bv38_roomsoldcurrent int
    declare @bv38_roomssoldpickup int
    declare @bv39_roomsoldcurrent int
    declare @bv39_roomssoldpickup int
    declare @bv40_roomsoldcurrent int
    declare @bv40_roomssoldpickup int
    declare @bv41_roomsoldcurrent int
    declare @bv41_roomssoldpickup int
    declare @bv42_roomsoldcurrent int
    declare @bv42_roomssoldpickup int
    declare @bv43_roomsoldcurrent int
    declare @bv43_roomssoldpickup int
    declare @bv44_roomsoldcurrent int
    declare @bv44_roomssoldpickup int
    declare @bv45_roomsoldcurrent int
    declare @bv45_roomssoldpickup int
    declare @bv46_roomsoldcurrent int
    declare @bv46_roomssoldpickup int
    declare @bv47_roomsoldcurrent int
    declare @bv47_roomssoldpickup int
    declare @bv48_roomsoldcurrent int
    declare @bv48_roomssoldpickup int
    declare @bv49_roomsoldcurrent int
    declare @bv49_roomssoldpickup int
    declare @bv50_roomsoldcurrent int
    declare @bv50_roomssoldpickup int
    declare @bv1_occfcstcurrent numeric(8, 2)
    declare @bv1_occfcstpickup numeric(8, 2)
    declare @bv2_occfcstcurrent numeric(8, 2)
    declare @bv2_occfcstpickup numeric(8, 2)
    declare @bv3_occfcstcurrent numeric(8, 2)
    declare @bv3_occfcstpickup numeric(8, 2)
    declare @bv4_occfcstcurrent numeric(8, 2)
    declare @bv4_occfcstpickup numeric(8, 2)
    declare @bv5_occfcstcurrent numeric(8, 2)
    declare @bv5_occfcstpickup numeric(8, 2)
    declare @bv6_occfcstcurrent numeric(8, 2)
    declare @bv6_occfcstpickup numeric(8, 2)
    declare @bv7_occfcstcurrent numeric(8, 2)
    declare @bv7_occfcstpickup numeric(8, 2)
    declare @bv8_occfcstcurrent numeric(8, 2)
    declare @bv8_occfcstpickup numeric(8, 2)
    declare @bv9_occfcstcurrent numeric(8, 2)
    declare @bv9_occfcstpickup numeric(8, 2)
    declare @bv10_occfcstcurrent numeric(8, 2)
    declare @bv10_occfcstpickup numeric(8, 2)
    declare @bv11_occfcstcurrent numeric(8, 2)
    declare @bv11_occfcstpickup numeric(8, 2)
    declare @bv12_occfcstcurrent numeric(8, 2)
    declare @bv12_occfcstpickup numeric(8, 2)
    declare @bv13_occfcstcurrent numeric(8, 2)
    declare @bv13_occfcstpickup numeric(8, 2)
    declare @bv14_occfcstcurrent numeric(8, 2)
    declare @bv14_occfcstpickup numeric(8, 2)
    declare @bv15_occfcstcurrent numeric(8, 2)
    declare @bv15_occfcstpickup numeric(8, 2)
    declare @bv16_occfcstcurrent numeric(8, 2)
    declare @bv16_occfcstpickup numeric(8, 2)
    declare @bv17_occfcstcurrent numeric(8, 2)
    declare @bv17_occfcstpickup numeric(8, 2)
    declare @bv18_occfcstcurrent numeric(8, 2)
    declare @bv18_occfcstpickup numeric(8, 2)
    declare @bv19_occfcstcurrent numeric(8, 2)
    declare @bv19_occfcstpickup numeric(8, 2)
    declare @bv20_occfcstcurrent numeric(8, 2)
    declare @bv20_occfcstpickup numeric(8, 2)
    declare @bv21_occfcstcurrent numeric(8, 2)
    declare @bv21_occfcstpickup numeric(8, 2)
    declare @bv22_occfcstcurrent numeric(8, 2)
    declare @bv22_occfcstpickup numeric(8, 2)
    declare @bv23_occfcstcurrent numeric(8, 2)
    declare @bv23_occfcstpickup numeric(8, 2)
    declare @bv24_occfcstcurrent numeric(8, 2)
    declare @bv24_occfcstpickup numeric(8, 2)
    declare @bv25_occfcstcurrent numeric(8, 2)
    declare @bv25_occfcstpickup numeric(8, 2)
    declare @bv26_occfcstcurrent numeric(8, 2)
    declare @bv26_occfcstpickup numeric(8, 2)
    declare @bv27_occfcstcurrent numeric(8, 2)
    declare @bv27_occfcstpickup numeric(8, 2)
    declare @bv28_occfcstcurrent numeric(8, 2)
    declare @bv28_occfcstpickup numeric(8, 2)
    declare @bv29_occfcstcurrent numeric(8, 2)
    declare @bv29_occfcstpickup numeric(8, 2)
    declare @bv30_occfcstcurrent numeric(8, 2)
    declare @bv30_occfcstpickup numeric(8, 2)
    declare @bv31_occfcstcurrent numeric(8, 2)
    declare @bv31_occfcstpickup numeric(8, 2)
    declare @bv32_occfcstcurrent numeric(8, 2)
    declare @bv32_occfcstpickup numeric(8, 2)
    declare @bv33_occfcstcurrent numeric(8, 2)
    declare @bv33_occfcstpickup numeric(8, 2)
    declare @bv34_occfcstcurrent numeric(8, 2)
    declare @bv34_occfcstpickup numeric(8, 2)
    declare @bv35_occfcstcurrent numeric(8, 2)
    declare @bv35_occfcstpickup numeric(8, 2)
    declare @bv36_occfcstcurrent numeric(8, 2)
    declare @bv36_occfcstpickup numeric(8, 2)
    declare @bv37_occfcstcurrent numeric(8, 2)
    declare @bv37_occfcstpickup numeric(8, 2)
    declare @bv38_occfcstcurrent numeric(8, 2)
    declare @bv38_occfcstpickup numeric(8, 2)
    declare @bv39_occfcstcurrent numeric(8, 2)
    declare @bv39_occfcstpickup numeric(8, 2)
    declare @bv40_occfcstcurrent numeric(8, 2)
    declare @bv40_occfcstpickup numeric(8, 2)
    declare @bv41_occfcstcurrent numeric(8, 2)
    declare @bv41_occfcstpickup numeric(8, 2)
    declare @bv42_occfcstcurrent numeric(8, 2)
    declare @bv42_occfcstpickup numeric(8, 2)
    declare @bv43_occfcstcurrent numeric(8, 2)
    declare @bv43_occfcstpickup numeric(8, 2)
    declare @bv44_occfcstcurrent numeric(8, 2)
    declare @bv44_occfcstpickup numeric(8, 2)
    declare @bv45_occfcstcurrent numeric(8, 2)
    declare @bv45_occfcstpickup numeric(8, 2)
    declare @bv46_occfcstcurrent numeric(8, 2)
    declare @bv46_occfcstpickup numeric(8, 2)
    declare @bv47_occfcstcurrent numeric(8, 2)
    declare @bv47_occfcstpickup numeric(8, 2)
    declare @bv48_occfcstcurrent numeric(8, 2)
    declare @bv48_occfcstpickup numeric(8, 2)
    declare @bv49_occfcstcurrent numeric(8, 2)
    declare @bv49_occfcstpickup numeric(8, 2)
    declare @bv50_occfcstcurrent numeric(8, 2)
    declare @bv50_occfcstpickup numeric(8, 2)
    declare @bv1_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv1_bookedroomrevenuepickup numeric(19, 5)
    declare @bv2_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv2_bookedroomrevenuepickup numeric(19, 5)
    declare @bv3_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv3_bookedroomrevenuepickup numeric(19, 5)
    declare @bv4_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv4_bookedroomrevenuepickup numeric(19, 5)
    declare @bv5_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv5_bookedroomrevenuepickup numeric(19, 5)
    declare @bv6_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv6_bookedroomrevenuepickup numeric(19, 5)
    declare @bv7_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv7_bookedroomrevenuepickup numeric(19, 5)
    declare @bv8_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv8_bookedroomrevenuepickup numeric(19, 5)
    declare @bv9_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv9_bookedroomrevenuepickup numeric(19, 5)
    declare @bv10_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv10_bookedroomrevenuepickup numeric(19, 5)
    declare @bv11_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv11_bookedroomrevenuepickup numeric(19, 5)
    declare @bv12_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv12_bookedroomrevenuepickup numeric(19, 5)
    declare @bv13_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv13_bookedroomrevenuepickup numeric(19, 5)
    declare @bv14_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv14_bookedroomrevenuepickup numeric(19, 5)
    declare @bv15_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv15_bookedroomrevenuepickup numeric(19, 5)
    declare @bv16_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv16_bookedroomrevenuepickup numeric(19, 5)
    declare @bv17_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv17_bookedroomrevenuepickup numeric(19, 5)
    declare @bv18_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv18_bookedroomrevenuepickup numeric(19, 5)
    declare @bv19_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv19_bookedroomrevenuepickup numeric(19, 5)
    declare @bv20_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv20_bookedroomrevenuepickup numeric(19, 5)
    declare @bv21_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv21_bookedroomrevenuepickup numeric(19, 5)
    declare @bv22_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv22_bookedroomrevenuepickup numeric(19, 5)
    declare @bv23_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv23_bookedroomrevenuepickup numeric(19, 5)
    declare @bv24_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv24_bookedroomrevenuepickup numeric(19, 5)
    declare @bv25_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv25_bookedroomrevenuepickup numeric(19, 5)
    declare @bv26_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv26_bookedroomrevenuepickup numeric(19, 5)
    declare @bv27_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv27_bookedroomrevenuepickup numeric(19, 5)
    declare @bv28_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv28_bookedroomrevenuepickup numeric(19, 5)
    declare @bv29_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv29_bookedroomrevenuepickup numeric(19, 5)
    declare @bv30_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv30_bookedroomrevenuepickup numeric(19, 5)
    declare @bv31_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv31_bookedroomrevenuepickup numeric(19, 5)
    declare @bv32_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv32_bookedroomrevenuepickup numeric(19, 5)
    declare @bv33_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv33_bookedroomrevenuepickup numeric(19, 5)
    declare @bv34_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv34_bookedroomrevenuepickup numeric(19, 5)
    declare @bv35_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv35_bookedroomrevenuepickup numeric(19, 5)
    declare @bv36_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv36_bookedroomrevenuepickup numeric(19, 5)
    declare @bv37_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv37_bookedroomrevenuepickup numeric(19, 5)
    declare @bv38_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv38_bookedroomrevenuepickup numeric(19, 5)
    declare @bv39_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv39_bookedroomrevenuepickup numeric(19, 5)
    declare @bv40_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv40_bookedroomrevenuepickup numeric(19, 5)
    declare @bv41_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv41_bookedroomrevenuepickup numeric(19, 5)
    declare @bv42_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv42_bookedroomrevenuepickup numeric(19, 5)
    declare @bv43_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv43_bookedroomrevenuepickup numeric(19, 5)
    declare @bv44_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv44_bookedroomrevenuepickup numeric(19, 5)
    declare @bv45_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv45_bookedroomrevenuepickup numeric(19, 5)
    declare @bv46_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv46_bookedroomrevenuepickup numeric(19, 5)
    declare @bv47_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv47_bookedroomrevenuepickup numeric(19, 5)
    declare @bv48_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv48_bookedroomrevenuepickup numeric(19, 5)
    declare @bv49_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv49_bookedroomrevenuepickup numeric(19, 5)
    declare @bv50_bookedroomrevenuecurrent numeric(19, 5)
    declare @bv50_bookedroomrevenuepickup numeric(19, 5)
    declare @bv1_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv1_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv2_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv2_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv3_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv3_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv4_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv4_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv5_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv5_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv6_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv6_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv7_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv7_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv8_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv8_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv9_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv9_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv10_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv10_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv11_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv11_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv12_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv12_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv13_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv13_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv14_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv14_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv15_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv15_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv16_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv16_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv17_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv17_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv18_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv18_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv19_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv19_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv20_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv20_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv21_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv21_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv22_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv22_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv23_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv23_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv24_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv24_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv25_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv25_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv26_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv26_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv27_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv27_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv28_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv28_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv29_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv29_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv30_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv30_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv31_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv31_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv32_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv32_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv33_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv33_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv34_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv34_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv35_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv35_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv36_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv36_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv37_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv37_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv38_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv38_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv39_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv39_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv40_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv40_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv41_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv41_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv42_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv42_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv43_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv43_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv44_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv44_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv45_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv45_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv46_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv46_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv47_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv47_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv48_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv48_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv49_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv49_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv50_fcstedroomrevenuecurrent numeric(19, 5)
    declare @bv50_fcstedroomrevenuepickup numeric(19, 5)
    declare @bv1_bookedadrcurrent numeric(19, 5)
    declare @bv1_bookedadrpickup numeric(19, 5)
    declare @bv2_bookedadrcurrent numeric(19, 5)
    declare @bv2_bookedadrpickup numeric(19, 5)
    declare @bv3_bookedadrcurrent numeric(19, 5)
    declare @bv3_bookedadrpickup numeric(19, 5)
    declare @bv4_bookedadrcurrent numeric(19, 5)
    declare @bv4_bookedadrpickup numeric(19, 5)
    declare @bv5_bookedadrcurrent numeric(19, 5)
    declare @bv5_bookedadrpickup numeric(19, 5)
    declare @bv6_bookedadrcurrent numeric(19, 5)
    declare @bv6_bookedadrpickup numeric(19, 5)
    declare @bv7_bookedadrcurrent numeric(19, 5)
    declare @bv7_bookedadrpickup numeric(19, 5)
    declare @bv8_bookedadrcurrent numeric(19, 5)
    declare @bv8_bookedadrpickup numeric(19, 5)
    declare @bv9_bookedadrcurrent numeric(19, 5)
    declare @bv9_bookedadrpickup numeric(19, 5)
    declare @bv10_bookedadrcurrent numeric(19, 5)
    declare @bv10_bookedadrpickup numeric(19, 5)
    declare @bv11_bookedadrcurrent numeric(19, 5)
    declare @bv11_bookedadrpickup numeric(19, 5)
    declare @bv12_bookedadrcurrent numeric(19, 5)
    declare @bv12_bookedadrpickup numeric(19, 5)
    declare @bv13_bookedadrcurrent numeric(19, 5)
    declare @bv13_bookedadrpickup numeric(19, 5)
    declare @bv14_bookedadrcurrent numeric(19, 5)
    declare @bv14_bookedadrpickup numeric(19, 5)
    declare @bv15_bookedadrcurrent numeric(19, 5)
    declare @bv15_bookedadrpickup numeric(19, 5)
    declare @bv16_bookedadrcurrent numeric(19, 5)
    declare @bv16_bookedadrpickup numeric(19, 5)
    declare @bv17_bookedadrcurrent numeric(19, 5)
    declare @bv17_bookedadrpickup numeric(19, 5)
    declare @bv18_bookedadrcurrent numeric(19, 5)
    declare @bv18_bookedadrpickup numeric(19, 5)
    declare @bv19_bookedadrcurrent numeric(19, 5)
    declare @bv19_bookedadrpickup numeric(19, 5)
    declare @bv20_bookedadrcurrent numeric(19, 5)
    declare @bv20_bookedadrpickup numeric(19, 5)
    declare @bv21_bookedadrcurrent numeric(19, 5)
    declare @bv21_bookedadrpickup numeric(19, 5)
    declare @bv22_bookedadrcurrent numeric(19, 5)
    declare @bv22_bookedadrpickup numeric(19, 5)
    declare @bv23_bookedadrcurrent numeric(19, 5)
    declare @bv23_bookedadrpickup numeric(19, 5)
    declare @bv24_bookedadrcurrent numeric(19, 5)
    declare @bv24_bookedadrpickup numeric(19, 5)
    declare @bv25_bookedadrcurrent numeric(19, 5)
    declare @bv25_bookedadrpickup numeric(19, 5)
    declare @bv26_bookedadrcurrent numeric(19, 5)
    declare @bv26_bookedadrpickup numeric(19, 5)
    declare @bv27_bookedadrcurrent numeric(19, 5)
    declare @bv27_bookedadrpickup numeric(19, 5)
    declare @bv28_bookedadrcurrent numeric(19, 5)
    declare @bv28_bookedadrpickup numeric(19, 5)
    declare @bv29_bookedadrcurrent numeric(19, 5)
    declare @bv29_bookedadrpickup numeric(19, 5)
    declare @bv30_bookedadrcurrent numeric(19, 5)
    declare @bv30_bookedadrpickup numeric(19, 5)
    declare @bv31_bookedadrcurrent numeric(19, 5)
    declare @bv31_bookedadrpickup numeric(19, 5)
    declare @bv32_bookedadrcurrent numeric(19, 5)
    declare @bv32_bookedadrpickup numeric(19, 5)
    declare @bv33_bookedadrcurrent numeric(19, 5)
    declare @bv33_bookedadrpickup numeric(19, 5)
    declare @bv34_bookedadrcurrent numeric(19, 5)
    declare @bv34_bookedadrpickup numeric(19, 5)
    declare @bv35_bookedadrcurrent numeric(19, 5)
    declare @bv35_bookedadrpickup numeric(19, 5)
    declare @bv36_bookedadrcurrent numeric(19, 5)
    declare @bv36_bookedadrpickup numeric(19, 5)
    declare @bv37_bookedadrcurrent numeric(19, 5)
    declare @bv37_bookedadrpickup numeric(19, 5)
    declare @bv38_bookedadrcurrent numeric(19, 5)
    declare @bv38_bookedadrpickup numeric(19, 5)
    declare @bv39_bookedadrcurrent numeric(19, 5)
    declare @bv39_bookedadrpickup numeric(19, 5)
    declare @bv40_bookedadrcurrent numeric(19, 5)
    declare @bv40_bookedadrpickup numeric(19, 5)
    declare @bv41_bookedadrcurrent numeric(19, 5)
    declare @bv41_bookedadrpickup numeric(19, 5)
    declare @bv42_bookedadrcurrent numeric(19, 5)
    declare @bv42_bookedadrpickup numeric(19, 5)
    declare @bv43_bookedadrcurrent numeric(19, 5)
    declare @bv43_bookedadrpickup numeric(19, 5)
    declare @bv44_bookedadrcurrent numeric(19, 5)
    declare @bv44_bookedadrpickup numeric(19, 5)
    declare @bv45_bookedadrcurrent numeric(19, 5)
    declare @bv45_bookedadrpickup numeric(19, 5)
    declare @bv46_bookedadrcurrent numeric(19, 5)
    declare @bv46_bookedadrpickup numeric(19, 5)
    declare @bv47_bookedadrcurrent numeric(19, 5)
    declare @bv47_bookedadrpickup numeric(19, 5)
    declare @bv48_bookedadrcurrent numeric(19, 5)
    declare @bv48_bookedadrpickup numeric(19, 5)
    declare @bv49_bookedadrcurrent numeric(19, 5)
    declare @bv49_bookedadrpickup numeric(19, 5)
    declare @bv50_bookedadrcurrent numeric(19, 5)
    declare @bv50_bookedadrpickup numeric(19, 5)
    declare @bv1_fcstedadrcurrent numeric(19, 5)
    declare @bv1_fcstedadrpickup numeric(19, 5)
    declare @bv2_fcstedadrcurrent numeric(19, 5)
    declare @bv2_fcstedadrpickup numeric(19, 5)
    declare @bv3_fcstedadrcurrent numeric(19, 5)
    declare @bv3_fcstedadrpickup numeric(19, 5)
    declare @bv4_fcstedadrcurrent numeric(19, 5)
    declare @bv4_fcstedadrpickup numeric(19, 5)
    declare @bv5_fcstedadrcurrent numeric(19, 5)
    declare @bv5_fcstedadrpickup numeric(19, 5)
    declare @bv6_fcstedadrcurrent numeric(19, 5)
    declare @bv6_fcstedadrpickup numeric(19, 5)
    declare @bv7_fcstedadrcurrent numeric(19, 5)
    declare @bv7_fcstedadrpickup numeric(19, 5)
    declare @bv8_fcstedadrcurrent numeric(19, 5)
    declare @bv8_fcstedadrpickup numeric(19, 5)
    declare @bv9_fcstedadrcurrent numeric(19, 5)
    declare @bv9_fcstedadrpickup numeric(19, 5)
    declare @bv10_fcstedadrcurrent numeric(19, 5)
    declare @bv10_fcstedadrpickup numeric(19, 5)
    declare @bv11_fcstedadrcurrent numeric(19, 5)
    declare @bv11_fcstedadrpickup numeric(19, 5)
    declare @bv12_fcstedadrcurrent numeric(19, 5)
    declare @bv12_fcstedadrpickup numeric(19, 5)
    declare @bv13_fcstedadrcurrent numeric(19, 5)
    declare @bv13_fcstedadrpickup numeric(19, 5)
    declare @bv14_fcstedadrcurrent numeric(19, 5)
    declare @bv14_fcstedadrpickup numeric(19, 5)
    declare @bv15_fcstedadrcurrent numeric(19, 5)
    declare @bv15_fcstedadrpickup numeric(19, 5)
    declare @bv16_fcstedadrcurrent numeric(19, 5)
    declare @bv16_fcstedadrpickup numeric(19, 5)
    declare @bv17_fcstedadrcurrent numeric(19, 5)
    declare @bv17_fcstedadrpickup numeric(19, 5)
    declare @bv18_fcstedadrcurrent numeric(19, 5)
    declare @bv18_fcstedadrpickup numeric(19, 5)
    declare @bv19_fcstedadrcurrent numeric(19, 5)
    declare @bv19_fcstedadrpickup numeric(19, 5)
    declare @bv20_fcstedadrcurrent numeric(19, 5)
    declare @bv20_fcstedadrpickup numeric(19, 5)
    declare @bv21_fcstedadrcurrent numeric(19, 5)
    declare @bv21_fcstedadrpickup numeric(19, 5)
    declare @bv22_fcstedadrcurrent numeric(19, 5)
    declare @bv22_fcstedadrpickup numeric(19, 5)
    declare @bv23_fcstedadrcurrent numeric(19, 5)
    declare @bv23_fcstedadrpickup numeric(19, 5)
    declare @bv24_fcstedadrcurrent numeric(19, 5)
    declare @bv24_fcstedadrpickup numeric(19, 5)
    declare @bv25_fcstedadrcurrent numeric(19, 5)
    declare @bv25_fcstedadrpickup numeric(19, 5)
    declare @bv26_fcstedadrcurrent numeric(19, 5)
    declare @bv26_fcstedadrpickup numeric(19, 5)
    declare @bv27_fcstedadrcurrent numeric(19, 5)
    declare @bv27_fcstedadrpickup numeric(19, 5)
    declare @bv28_fcstedadrcurrent numeric(19, 5)
    declare @bv28_fcstedadrpickup numeric(19, 5)
    declare @bv29_fcstedadrcurrent numeric(19, 5)
    declare @bv29_fcstedadrpickup numeric(19, 5)
    declare @bv30_fcstedadrcurrent numeric(19, 5)
    declare @bv30_fcstedadrpickup numeric(19, 5)
    declare @bv31_fcstedadrcurrent numeric(19, 5)
    declare @bv31_fcstedadrpickup numeric(19, 5)
    declare @bv32_fcstedadrcurrent numeric(19, 5)
    declare @bv32_fcstedadrpickup numeric(19, 5)
    declare @bv33_fcstedadrcurrent numeric(19, 5)
    declare @bv33_fcstedadrpickup numeric(19, 5)
    declare @bv34_fcstedadrcurrent numeric(19, 5)
    declare @bv34_fcstedadrpickup numeric(19, 5)
    declare @bv35_fcstedadrcurrent numeric(19, 5)
    declare @bv35_fcstedadrpickup numeric(19, 5)
    declare @bv36_fcstedadrcurrent numeric(19, 5)
    declare @bv36_fcstedadrpickup numeric(19, 5)
    declare @bv37_fcstedadrcurrent numeric(19, 5)
    declare @bv37_fcstedadrpickup numeric(19, 5)
    declare @bv38_fcstedadrcurrent numeric(19, 5)
    declare @bv38_fcstedadrpickup numeric(19, 5)
    declare @bv39_fcstedadrcurrent numeric(19, 5)
    declare @bv39_fcstedadrpickup numeric(19, 5)
    declare @bv40_fcstedadrcurrent numeric(19, 5)
    declare @bv40_fcstedadrpickup numeric(19, 5)
    declare @bv41_fcstedadrcurrent numeric(19, 5)
    declare @bv41_fcstedadrpickup numeric(19, 5)
    declare @bv42_fcstedadrcurrent numeric(19, 5)
    declare @bv42_fcstedadrpickup numeric(19, 5)
    declare @bv43_fcstedadrcurrent numeric(19, 5)
    declare @bv43_fcstedadrpickup numeric(19, 5)
    declare @bv44_fcstedadrcurrent numeric(19, 5)
    declare @bv44_fcstedadrpickup numeric(19, 5)
    declare @bv45_fcstedadrcurrent numeric(19, 5)
    declare @bv45_fcstedadrpickup numeric(19, 5)
    declare @bv46_fcstedadrcurrent numeric(19, 5)
    declare @bv46_fcstedadrpickup numeric(19, 5)
    declare @bv47_fcstedadrcurrent numeric(19, 5)
    declare @bv47_fcstedadrpickup numeric(19, 5)
    declare @bv48_fcstedadrcurrent numeric(19, 5)
    declare @bv48_fcstedadrpickup numeric(19, 5)
    declare @bv49_fcstedadrcurrent numeric(19, 5)
    declare @bv49_fcstedadrpickup numeric(19, 5)
    declare @bv50_fcstedadrcurrent numeric(19, 5)
    declare @bv50_fcstedadrpickup numeric(19, 5)
    declare @bv1_block int
    declare @bv1_block_available int
    declare @bv1_block_pickup int
    declare @bv2_block int
    declare @bv2_block_available int
    declare @bv2_block_pickup int
    declare @bv3_block int
    declare @bv3_block_available int
    declare @bv3_block_pickup int
    declare @bv4_block int
    declare @bv4_block_available int
    declare @bv4_block_pickup int
    declare @bv5_block int
    declare @bv5_block_available int
    declare @bv5_block_pickup int
    declare @bv6_block int
    declare @bv6_block_available int
    declare @bv6_block_pickup int
    declare @bv7_block int
    declare @bv7_block_available int
    declare @bv7_block_pickup int
    declare @bv8_block int
    declare @bv8_block_available int
    declare @bv8_block_pickup int
    declare @bv9_block int
    declare @bv9_block_available int
    declare @bv9_block_pickup int
    declare @bv10_block int
    declare @bv10_block_available int
    declare @bv10_block_pickup int
    declare @bv11_block int
    declare @bv11_block_available int
    declare @bv11_block_pickup int
    declare @bv12_block int
    declare @bv12_block_available int
    declare @bv12_block_pickup int
    declare @bv13_block int
    declare @bv13_block_available int
    declare @bv13_block_pickup int
    declare @bv14_block int
    declare @bv14_block_available int
    declare @bv14_block_pickup int
    declare @bv15_block int
    declare @bv15_block_available int
    declare @bv15_block_pickup int
    declare @bv16_block int
    declare @bv16_block_available int
    declare @bv16_block_pickup int
    declare @bv17_block int
    declare @bv17_block_available int
    declare @bv17_block_pickup int
    declare @bv18_block int
    declare @bv18_block_available int
    declare @bv18_block_pickup int
    declare @bv19_block int
    declare @bv19_block_available int
    declare @bv19_block_pickup int
    declare @bv20_block int
    declare @bv20_block_available int
    declare @bv20_block_pickup int
    declare @bv21_block int
    declare @bv21_block_available int
    declare @bv21_block_pickup int
    declare @bv22_block int
    declare @bv22_block_available int
    declare @bv22_block_pickup int
    declare @bv23_block int
    declare @bv23_block_available int
    declare @bv23_block_pickup int
    declare @bv24_block int
    declare @bv24_block_available int
    declare @bv24_block_pickup int
    declare @bv25_block int
    declare @bv25_block_available int
    declare @bv25_block_pickup int
    declare @bv26_block int
    declare @bv26_block_available int
    declare @bv26_block_pickup int
    declare @bv27_block int
    declare @bv27_block_available int
    declare @bv27_block_pickup int
    declare @bv28_block int
    declare @bv28_block_available int
    declare @bv28_block_pickup int
    declare @bv29_block int
    declare @bv29_block_available int
    declare @bv29_block_pickup int
    declare @bv30_block int
    declare @bv30_block_available int
    declare @bv30_block_pickup int
    declare @bv31_block int
    declare @bv31_block_available int
    declare @bv31_block_pickup int
    declare @bv32_block int
    declare @bv32_block_available int
    declare @bv32_block_pickup int
    declare @bv33_block int
    declare @bv33_block_available int
    declare @bv33_block_pickup int
    declare @bv34_block int
    declare @bv34_block_available int
    declare @bv34_block_pickup int
    declare @bv35_block int
    declare @bv35_block_available int
    declare @bv35_block_pickup int
    declare @bv36_block int
    declare @bv36_block_available int
    declare @bv36_block_pickup int
    declare @bv37_block int
    declare @bv37_block_available int
    declare @bv37_block_pickup int
    declare @bv38_block int
    declare @bv38_block_available int
    declare @bv38_block_pickup int
    declare @bv39_block int
    declare @bv39_block_available int
    declare @bv39_block_pickup int
    declare @bv40_block int
    declare @bv40_block_available int
    declare @bv40_block_pickup int
    declare @bv41_block int
    declare @bv41_block_available int
    declare @bv41_block_pickup int
    declare @bv42_block int
    declare @bv42_block_available int
    declare @bv42_block_pickup int
    declare @bv43_block int
    declare @bv43_block_available int
    declare @bv43_block_pickup int
    declare @bv44_block int
    declare @bv44_block_available int
    declare @bv44_block_pickup int
    declare @bv45_block int
    declare @bv45_block_available int
    declare @bv45_block_pickup int
    declare @bv46_block int
    declare @bv46_block_available int
    declare @bv46_block_pickup int
    declare @bv47_block int
    declare @bv47_block_available int
    declare @bv47_block_pickup int
    declare @bv48_block int
    declare @bv48_block_available int
    declare @bv48_block_pickup int
    declare @bv49_block int
    declare @bv49_block_available int
    declare @bv49_block_pickup int
    declare @bv50_block int
    declare @bv50_block_available int
    declare @bv50_block_pickup int
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    ) --> extract caughtup date for a property

    if (@isRollingDate = 1)
begin
        set @business_start_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_start_dt, @caughtupdate)
        )
        set @business_end_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_end_dt, @caughtupdate)
        )
        set @start_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate)
        )
        set @end_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate)
        )
end

    declare @bv1 int
    declare @bv2 int
    declare @bv3 int
    declare @bv4 int
    declare @bv5 int
    declare @bv6 int
    declare @bv7 int
    declare @bv8 int
    declare @bv9 int
    declare @bv10 int
    declare @bv11 int
    declare @bv12 int
    declare @bv13 int
    declare @bv14 int
    declare @bv15 int
    declare @bv16 int
    declare @bv17 int
    declare @bv18 int
    declare @bv19 int
    declare @bv20 int
    declare @bv21 int
    declare @bv22 int
    declare @bv23 int
    declare @bv24 int
    declare @bv25 int
    declare @bv26 int
    declare @bv27 int
    declare @bv28 int
    declare @bv29 int
    declare @bv30 int
    declare @bv31 int
    declare @bv32 int
    declare @bv33 int
    declare @bv34 int
    declare @bv35 int
    declare @bv36 int
    declare @bv37 int
    declare @bv38 int
    declare @bv39 int
    declare @bv40 int
    declare @bv41 int
    declare @bv42 int
    declare @bv43 int
    declare @bv44 int
    declare @bv45 int
    declare @bv46 int
    declare @bv47 int
    declare @bv48 int
    declare @bv49 int
    declare @bv50 int


    declare @tempBV table
    (
        number int,
        Business_Group_ID int,
        Business_Group_Name nvarchar(100)
    )
    insert into @tempBV
select number = ROW_NUMBER() OVER (ORDER BY Business_Group_ID),
        Business_Group_ID,
       Business_Group_Name
from Business_Group
where Property_ID = @property_id
  and Business_Group_ID in (
    SELECT Value FROM varcharToInt(@Business_Group_ID, ',')
)
  and Status_ID = 1

    set @bv1 =
    (
        Select Business_Group_ID from @tempBV where number = 1
    )
set @bv2 =
    (
    Select Business_Group_ID from @tempBV where number = 2
    )
set @bv3 =
    (
    Select Business_Group_ID from @tempBV where number = 3
    )
set @bv4 =
    (
    Select Business_Group_ID from @tempBV where number = 4
    )
set @bv5 =
    (
    Select Business_Group_ID from @tempBV where number = 5
    )
set @bv6 =
    (
    Select Business_Group_ID from @tempBV where number = 6
    )
set @bv7 =
    (
    Select Business_Group_ID from @tempBV where number = 7
    )
set @bv8 =
    (
    Select Business_Group_ID from @tempBV where number = 8
    )
set @bv9 =
    (
    Select Business_Group_ID from @tempBV where number = 9
    )
set @bv10 =
    (
    Select Business_Group_ID from @tempBV where number = 10
    )
set @bv11 =
    (
    Select Business_Group_ID from @tempBV where number = 11
    )
set @bv12 =
    (
    Select Business_Group_ID from @tempBV where number = 12
    )
set @bv13 =
    (
    Select Business_Group_ID from @tempBV where number = 13
    )
set @bv14 =
    (
    Select Business_Group_ID from @tempBV where number = 14
    )
set @bv15 =
    (
    Select Business_Group_ID from @tempBV where number = 15
    )
set @bv16 =
    (
    Select Business_Group_ID from @tempBV where number = 16
    )
set @bv17 =
    (
    Select Business_Group_ID from @tempBV where number = 17
    )
set @bv18 =
    (
    Select Business_Group_ID from @tempBV where number = 18
    )
set @bv19 =
    (
    Select Business_Group_ID from @tempBV where number = 19
    )
set @bv20 =
    (
    Select Business_Group_ID from @tempBV where number = 20
    )
set @bv21 =
    (
    Select Business_Group_ID from @tempBV where number = 21
    )
set @bv22 =
    (
    Select Business_Group_ID from @tempBV where number = 22
    )
set @bv23 =
    (
    Select Business_Group_ID from @tempBV where number = 23
    )
set @bv24 =
    (
    Select Business_Group_ID from @tempBV where number = 24
    )
set @bv25 =
    (
    Select Business_Group_ID from @tempBV where number = 25
    )
set @bv26 =
    (
    Select Business_Group_ID from @tempBV where number = 26
    )
set @bv27 =
    (
    Select Business_Group_ID from @tempBV where number = 27
    )
set @bv28 =
    (
    Select Business_Group_ID from @tempBV where number = 28
    )
set @bv29 =
    (
    Select Business_Group_ID from @tempBV where number = 29
    )
set @bv30 =
    (
    Select Business_Group_ID from @tempBV where number = 30
    )
set @bv31 =
    (
    Select Business_Group_ID from @tempBV where number = 31
    )
set @bv32 =
    (
    Select Business_Group_ID from @tempBV where number = 32
    )
set @bv33 =
    (
    Select Business_Group_ID from @tempBV where number = 33
    )
set @bv34 =
    (
    Select Business_Group_ID from @tempBV where number = 34
    )
set @bv35 =
    (
    Select Business_Group_ID from @tempBV where number = 35
    )
set @bv36 =
    (
    Select Business_Group_ID from @tempBV where number = 36
    )
set @bv37 =
    (
    Select Business_Group_ID from @tempBV where number = 37
    )
set @bv38 =
    (
    Select Business_Group_ID from @tempBV where number = 38
    )
set @bv39 =
    (
    Select Business_Group_ID from @tempBV where number = 39
    )
set @bv40 =
    (
    Select Business_Group_ID from @tempBV where number = 40
    )
set @bv41 =
    (
    Select Business_Group_ID from @tempBV where number = 41
    )
set @bv42 =
    (
    Select Business_Group_ID from @tempBV where number = 42
    )
set @bv43 =
    (
    Select Business_Group_ID from @tempBV where number = 43
    )
set @bv44 =
    (
    Select Business_Group_ID from @tempBV where number = 44
    )
set @bv45 =
    (
    Select Business_Group_ID from @tempBV where number = 45
    )
set @bv46 =
    (
    Select Business_Group_ID from @tempBV where number = 46
    )
set @bv47 =
    (
    Select Business_Group_ID from @tempBV where number = 47
    )
set @bv48 =
    (
    Select Business_Group_ID from @tempBV where number = 48
    )
set @bv49 =
    (
    Select Business_Group_ID from @tempBV where number = 49
    )
set @bv50 =
    (
    Select Business_Group_ID from @tempBV where number = 50
    )


--- extract report metrics
select occupancy_dt,
       dow,
       MAX(bv1_businessgroupname) as bv1_businessgroupname,
       MAX(bv2_businessgroupname) as bv2_businessgroupname,
       MAX(bv3_businessgroupname) as bv3_businessgroupname,
       MAX(bv4_businessgroupname) as bv4_businessgroupname,
       MAX(bv5_businessgroupname) as bv5_businessgroupname,
       MAX(bv6_businessgroupname) as bv6_businessgroupname,
       MAX(bv7_businessgroupname) as bv7_businessgroupname,
       MAX(bv8_businessgroupname) as bv8_businessgroupname,
       MAX(bv9_businessgroupname) as bv9_businessgroupname,
       MAX(bv10_businessgroupname) as bv10_businessgroupname,
       MAX(bv11_businessgroupname) as bv11_businessgroupname,
       MAX(bv12_businessgroupname) as bv12_businessgroupname,
       MAX(bv13_businessgroupname) as bv13_businessgroupname,
       MAX(bv14_businessgroupname) as bv14_businessgroupname,
       MAX(bv15_businessgroupname) as bv15_businessgroupname,
       MAX(bv16_businessgroupname) as bv16_businessgroupname,
       MAX(bv17_businessgroupname) as bv17_businessgroupname,
       MAX(bv18_businessgroupname) as bv18_businessgroupname,
       MAX(bv19_businessgroupname) as bv19_businessgroupname,
       MAX(bv20_businessgroupname) as bv20_businessgroupname,
       MAX(bv21_businessgroupname) as bv21_businessgroupname,
       MAX(bv22_businessgroupname) as bv22_businessgroupname,
       MAX(bv23_businessgroupname) as bv23_businessgroupname,
       MAX(bv24_businessgroupname) as bv24_businessgroupname,
       MAX(bv25_businessgroupname) as bv25_businessgroupname,
       MAX(bv26_businessgroupname) as bv26_businessgroupname,
       MAX(bv27_businessgroupname) as bv27_businessgroupname,
       MAX(bv28_businessgroupname) as bv28_businessgroupname,
       MAX(bv29_businessgroupname) as bv29_businessgroupname,
       MAX(bv30_businessgroupname) as bv30_businessgroupname,
       MAX(bv31_businessgroupname) as bv31_businessgroupname,
       MAX(bv32_businessgroupname) as bv32_businessgroupname,
       MAX(bv33_businessgroupname) as bv33_businessgroupname,
       MAX(bv34_businessgroupname) as bv34_businessgroupname,
       MAX(bv35_businessgroupname) as bv35_businessgroupname,
       MAX(bv36_businessgroupname) as bv36_businessgroupname,
       MAX(bv37_businessgroupname) as bv37_businessgroupname,
       MAX(bv38_businessgroupname) as bv38_businessgroupname,
       MAX(bv39_businessgroupname) as bv39_businessgroupname,
       MAX(bv40_businessgroupname) as bv40_businessgroupname,
       MAX(bv41_businessgroupname) as bv41_businessgroupname,
       MAX(bv42_businessgroupname) as bv42_businessgroupname,
       MAX(bv43_businessgroupname) as bv43_businessgroupname,
       MAX(bv44_businessgroupname) as bv44_businessgroupname,
       MAX(bv45_businessgroupname) as bv45_businessgroupname,
       MAX(bv46_businessgroupname) as bv46_businessgroupname,
       MAX(bv47_businessgroupname) as bv47_businessgroupname,
       MAX(bv48_businessgroupname) as bv48_businessgroupname,
       MAX(bv49_businessgroupname) as bv49_businessgroupname,
       MAX(bv50_businessgroupname) as bv50_businessgroupname,
       isnull(MAX(bv1_roomsoldcurrent), 0.0) as bv1_roomsoldcurrent,
       isnull(MAX(bv1_roomssoldpickup), 0.0) as bv1_roomssoldpickup,
       isnull(MAX(bv2_roomsoldcurrent), 0.0) as bv2_roomsoldcurrent,
       isnull(MAX(bv2_roomssoldpickup), 0.0) as bv2_roomssoldpickup,
       isnull(MAX(bv3_roomsoldcurrent), 0.0) as bv3_roomsoldcurrent,
       isnull(MAX(bv3_roomssoldpickup), 0.0) as bv3_roomssoldpickup,
       isnull(MAX(bv4_roomsoldcurrent), 0.0) as bv4_roomsoldcurrent,
       isnull(MAX(bv4_roomssoldpickup), 0.0) as bv4_roomssoldpickup,
       isnull(MAX(bv5_roomsoldcurrent), 0.0) as bv5_roomsoldcurrent,
       isnull(MAX(bv5_roomssoldpickup), 0.0) as bv5_roomssoldpickup,
       isnull(MAX(bv6_roomsoldcurrent), 0.0) as bv6_roomsoldcurrent,
       isnull(MAX(bv6_roomssoldpickup), 0.0) as bv6_roomssoldpickup,
       isnull(MAX(bv7_roomsoldcurrent), 0.0) as bv7_roomsoldcurrent,
       isnull(MAX(bv7_roomssoldpickup), 0.0) as bv7_roomssoldpickup,
       isnull(MAX(bv8_roomsoldcurrent), 0.0) as bv8_roomsoldcurrent,
       isnull(MAX(bv8_roomssoldpickup), 0.0) as bv8_roomssoldpickup,
       isnull(MAX(bv9_roomsoldcurrent), 0.0) as bv9_roomsoldcurrent,
       isnull(MAX(bv9_roomssoldpickup), 0.0) as bv9_roomssoldpickup,
       isnull(MAX(bv10_roomsoldcurrent), 0.0) as bv10_roomsoldcurrent,
       isnull(MAX(bv10_roomssoldpickup), 0.0) as bv10_roomssoldpickup,
       isnull(MAX(bv11_roomsoldcurrent), 0.0) as bv11_roomsoldcurrent,
       isnull(MAX(bv11_roomssoldpickup), 0.0) as bv11_roomssoldpickup,
       isnull(MAX(bv12_roomsoldcurrent), 0.0) as bv12_roomsoldcurrent,
       isnull(MAX(bv12_roomssoldpickup), 0.0) as bv12_roomssoldpickup,
       isnull(MAX(bv13_roomsoldcurrent), 0.0) as bv13_roomsoldcurrent,
       isnull(MAX(bv13_roomssoldpickup), 0.0) as bv13_roomssoldpickup,
       isnull(MAX(bv14_roomsoldcurrent), 0.0) as bv14_roomsoldcurrent,
       isnull(MAX(bv14_roomssoldpickup), 0.0) as bv14_roomssoldpickup,
       isnull(MAX(bv15_roomsoldcurrent), 0.0) as bv15_roomsoldcurrent,
       isnull(MAX(bv15_roomssoldpickup), 0.0) as bv15_roomssoldpickup,
       isnull(MAX(bv16_roomsoldcurrent), 0.0) as bv16_roomsoldcurrent,
       isnull(MAX(bv16_roomssoldpickup), 0.0) as bv16_roomssoldpickup,
       isnull(MAX(bv17_roomsoldcurrent), 0.0) as bv17_roomsoldcurrent,
       isnull(MAX(bv17_roomssoldpickup), 0.0) as bv17_roomssoldpickup,
       isnull(MAX(bv18_roomsoldcurrent), 0.0) as bv18_roomsoldcurrent,
       isnull(MAX(bv18_roomssoldpickup), 0.0) as bv18_roomssoldpickup,
       isnull(MAX(bv19_roomsoldcurrent), 0.0) as bv19_roomsoldcurrent,
       isnull(MAX(bv19_roomssoldpickup), 0.0) as bv19_roomssoldpickup,
       isnull(MAX(bv20_roomsoldcurrent), 0.0) as bv20_roomsoldcurrent,
       isnull(MAX(bv20_roomssoldpickup), 0.0) as bv20_roomssoldpickup,
       isnull(MAX(bv21_roomsoldcurrent), 0.0) as bv21_roomsoldcurrent,
       isnull(MAX(bv21_roomssoldpickup), 0.0) as bv21_roomssoldpickup,
       isnull(MAX(bv22_roomsoldcurrent), 0.0) as bv22_roomsoldcurrent,
       isnull(MAX(bv22_roomssoldpickup), 0.0) as bv22_roomssoldpickup,
       isnull(MAX(bv23_roomsoldcurrent), 0.0) as bv23_roomsoldcurrent,
       isnull(MAX(bv23_roomssoldpickup), 0.0) as bv23_roomssoldpickup,
       isnull(MAX(bv24_roomsoldcurrent), 0.0) as bv24_roomsoldcurrent,
       isnull(MAX(bv24_roomssoldpickup), 0.0) as bv24_roomssoldpickup,
       isnull(MAX(bv25_roomsoldcurrent), 0.0) as bv25_roomsoldcurrent,
       isnull(MAX(bv25_roomssoldpickup), 0.0) as bv25_roomssoldpickup,
       isnull(MAX(bv26_roomsoldcurrent), 0.0) as bv26_roomsoldcurrent,
       isnull(MAX(bv26_roomssoldpickup), 0.0) as bv26_roomssoldpickup,
       isnull(MAX(bv27_roomsoldcurrent), 0.0) as bv27_roomsoldcurrent,
       isnull(MAX(bv27_roomssoldpickup), 0.0) as bv27_roomssoldpickup,
       isnull(MAX(bv28_roomsoldcurrent), 0.0) as bv28_roomsoldcurrent,
       isnull(MAX(bv28_roomssoldpickup), 0.0) as bv28_roomssoldpickup,
       isnull(MAX(bv29_roomsoldcurrent), 0.0) as bv29_roomsoldcurrent,
       isnull(MAX(bv29_roomssoldpickup), 0.0) as bv29_roomssoldpickup,
       isnull(MAX(bv30_roomsoldcurrent), 0.0) as bv30_roomsoldcurrent,
       isnull(MAX(bv30_roomssoldpickup), 0.0) as bv30_roomssoldpickup,
       isnull(MAX(bv31_roomsoldcurrent), 0.0) as bv31_roomsoldcurrent,
       isnull(MAX(bv31_roomssoldpickup), 0.0) as bv31_roomssoldpickup,
       isnull(MAX(bv32_roomsoldcurrent), 0.0) as bv32_roomsoldcurrent,
       isnull(MAX(bv32_roomssoldpickup), 0.0) as bv32_roomssoldpickup,
       isnull(MAX(bv33_roomsoldcurrent), 0.0) as bv33_roomsoldcurrent,
       isnull(MAX(bv33_roomssoldpickup), 0.0) as bv33_roomssoldpickup,
       isnull(MAX(bv34_roomsoldcurrent), 0.0) as bv34_roomsoldcurrent,
       isnull(MAX(bv34_roomssoldpickup), 0.0) as bv34_roomssoldpickup,
       isnull(MAX(bv35_roomsoldcurrent), 0.0) as bv35_roomsoldcurrent,
       isnull(MAX(bv35_roomssoldpickup), 0.0) as bv35_roomssoldpickup,
       isnull(MAX(bv36_roomsoldcurrent), 0.0) as bv36_roomsoldcurrent,
       isnull(MAX(bv36_roomssoldpickup), 0.0) as bv36_roomssoldpickup,
       isnull(MAX(bv37_roomsoldcurrent), 0.0) as bv37_roomsoldcurrent,
       isnull(MAX(bv37_roomssoldpickup), 0.0) as bv37_roomssoldpickup,
       isnull(MAX(bv38_roomsoldcurrent), 0.0) as bv38_roomsoldcurrent,
       isnull(MAX(bv38_roomssoldpickup), 0.0) as bv38_roomssoldpickup,
       isnull(MAX(bv39_roomsoldcurrent), 0.0) as bv39_roomsoldcurrent,
       isnull(MAX(bv39_roomssoldpickup), 0.0) as bv39_roomssoldpickup,
       isnull(MAX(bv40_roomsoldcurrent), 0.0) as bv40_roomsoldcurrent,
       isnull(MAX(bv40_roomssoldpickup), 0.0) as bv40_roomssoldpickup,
       isnull(MAX(bv41_roomsoldcurrent), 0.0) as bv41_roomsoldcurrent,
       isnull(MAX(bv41_roomssoldpickup), 0.0) as bv41_roomssoldpickup,
       isnull(MAX(bv42_roomsoldcurrent), 0.0) as bv42_roomsoldcurrent,
       isnull(MAX(bv42_roomssoldpickup), 0.0) as bv42_roomssoldpickup,
       isnull(MAX(bv43_roomsoldcurrent), 0.0) as bv43_roomsoldcurrent,
       isnull(MAX(bv43_roomssoldpickup), 0.0) as bv43_roomssoldpickup,
       isnull(MAX(bv44_roomsoldcurrent), 0.0) as bv44_roomsoldcurrent,
       isnull(MAX(bv44_roomssoldpickup), 0.0) as bv44_roomssoldpickup,
       isnull(MAX(bv45_roomsoldcurrent), 0.0) as bv45_roomsoldcurrent,
       isnull(MAX(bv45_roomssoldpickup), 0.0) as bv45_roomssoldpickup,
       isnull(MAX(bv46_roomsoldcurrent), 0.0) as bv46_roomsoldcurrent,
       isnull(MAX(bv46_roomssoldpickup), 0.0) as bv46_roomssoldpickup,
       isnull(MAX(bv47_roomsoldcurrent), 0.0) as bv47_roomsoldcurrent,
       isnull(MAX(bv47_roomssoldpickup), 0.0) as bv47_roomssoldpickup,
       isnull(MAX(bv48_roomsoldcurrent), 0.0) as bv48_roomsoldcurrent,
       isnull(MAX(bv48_roomssoldpickup), 0.0) as bv48_roomssoldpickup,
       isnull(MAX(bv49_roomsoldcurrent), 0.0) as bv49_roomsoldcurrent,
       isnull(MAX(bv49_roomssoldpickup), 0.0) as bv49_roomssoldpickup,
       isnull(MAX(bv50_roomsoldcurrent), 0.0) as bv50_roomsoldcurrent,
       isnull(MAX(bv50_roomssoldpickup), 0.0) as bv50_roomssoldpickup,
       isnull(MAX(bv1_occfcstcurrent), 0.0) as bv1_occfcstcurrent,
       isnull(MAX(bv1_occfcstpickup), 0.0) as bv1_occfcstpickup,
       isnull(MAX(bv2_occfcstcurrent), 0.0) as bv2_occfcstcurrent,
       isnull(MAX(bv2_occfcstpickup), 0.0) as bv2_occfcstpickup,
       isnull(MAX(bv3_occfcstcurrent), 0.0) as bv3_occfcstcurrent,
       isnull(MAX(bv3_occfcstpickup), 0.0) as bv3_occfcstpickup,
       isnull(MAX(bv4_occfcstcurrent), 0.0) as bv4_occfcstcurrent,
       isnull(MAX(bv4_occfcstpickup), 0.0) as bv4_occfcstpickup,
       isnull(MAX(bv5_occfcstcurrent), 0.0) as bv5_occfcstcurrent,
       isnull(MAX(bv5_occfcstpickup), 0.0) as bv5_occfcstpickup,
       isnull(MAX(bv6_occfcstcurrent), 0.0) as bv6_occfcstcurrent,
       isnull(MAX(bv6_occfcstpickup), 0.0) as bv6_occfcstpickup,
       isnull(MAX(bv7_occfcstcurrent), 0.0) as bv7_occfcstcurrent,
       isnull(MAX(bv7_occfcstpickup), 0.0) as bv7_occfcstpickup,
       isnull(MAX(bv8_occfcstcurrent), 0.0) as bv8_occfcstcurrent,
       isnull(MAX(bv8_occfcstpickup), 0.0) as bv8_occfcstpickup,
       isnull(MAX(bv9_occfcstcurrent), 0.0) as bv9_occfcstcurrent,
       isnull(MAX(bv9_occfcstpickup), 0.0) as bv9_occfcstpickup,
       isnull(MAX(bv10_occfcstcurrent), 0.0) as bv10_occfcstcurrent,
       isnull(MAX(bv10_occfcstpickup), 0.0) as bv10_occfcstpickup,
       isnull(MAX(bv11_occfcstcurrent), 0.0) as bv11_occfcstcurrent,
       isnull(MAX(bv11_occfcstpickup), 0.0) as bv11_occfcstpickup,
       isnull(MAX(bv12_occfcstcurrent), 0.0) as bv12_occfcstcurrent,
       isnull(MAX(bv12_occfcstpickup), 0.0) as bv12_occfcstpickup,
       isnull(MAX(bv13_occfcstcurrent), 0.0) as bv13_occfcstcurrent,
       isnull(MAX(bv13_occfcstpickup), 0.0) as bv13_occfcstpickup,
       isnull(MAX(bv14_occfcstcurrent), 0.0) as bv14_occfcstcurrent,
       isnull(MAX(bv14_occfcstpickup), 0.0) as bv14_occfcstpickup,
       isnull(MAX(bv15_occfcstcurrent), 0.0) as bv15_occfcstcurrent,
       isnull(MAX(bv15_occfcstpickup), 0.0) as bv15_occfcstpickup,
       isnull(MAX(bv16_occfcstcurrent), 0.0) as bv16_occfcstcurrent,
       isnull(MAX(bv16_occfcstpickup), 0.0) as bv16_occfcstpickup,
       isnull(MAX(bv17_occfcstcurrent), 0.0) as bv17_occfcstcurrent,
       isnull(MAX(bv17_occfcstpickup), 0.0) as bv17_occfcstpickup,
       isnull(MAX(bv18_occfcstcurrent), 0.0) as bv18_occfcstcurrent,
       isnull(MAX(bv18_occfcstpickup), 0.0) as bv18_occfcstpickup,
       isnull(MAX(bv19_occfcstcurrent), 0.0) as bv19_occfcstcurrent,
       isnull(MAX(bv19_occfcstpickup), 0.0) as bv19_occfcstpickup,
       isnull(MAX(bv20_occfcstcurrent), 0.0) as bv20_occfcstcurrent,
       isnull(MAX(bv20_occfcstpickup), 0.0) as bv20_occfcstpickup,
       isnull(MAX(bv21_occfcstcurrent), 0.0) as bv21_occfcstcurrent,
       isnull(MAX(bv21_occfcstpickup), 0.0) as bv21_occfcstpickup,
       isnull(MAX(bv22_occfcstcurrent), 0.0) as bv22_occfcstcurrent,
       isnull(MAX(bv22_occfcstpickup), 0.0) as bv22_occfcstpickup,
       isnull(MAX(bv23_occfcstcurrent), 0.0) as bv23_occfcstcurrent,
       isnull(MAX(bv23_occfcstpickup), 0.0) as bv23_occfcstpickup,
       isnull(MAX(bv24_occfcstcurrent), 0.0) as bv24_occfcstcurrent,
       isnull(MAX(bv24_occfcstpickup), 0.0) as bv24_occfcstpickup,
       isnull(MAX(bv25_occfcstcurrent), 0.0) as bv25_occfcstcurrent,
       isnull(MAX(bv25_occfcstpickup), 0.0) as bv25_occfcstpickup,
       isnull(MAX(bv26_occfcstcurrent), 0.0) as bv26_occfcstcurrent,
       isnull(MAX(bv26_occfcstpickup), 0.0) as bv26_occfcstpickup,
       isnull(MAX(bv27_occfcstcurrent), 0.0) as bv27_occfcstcurrent,
       isnull(MAX(bv27_occfcstpickup), 0.0) as bv27_occfcstpickup,
       isnull(MAX(bv28_occfcstcurrent), 0.0) as bv28_occfcstcurrent,
       isnull(MAX(bv28_occfcstpickup), 0.0) as bv28_occfcstpickup,
       isnull(MAX(bv29_occfcstcurrent), 0.0) as bv29_occfcstcurrent,
       isnull(MAX(bv29_occfcstpickup), 0.0) as bv29_occfcstpickup,
       isnull(MAX(bv30_occfcstcurrent), 0.0) as bv30_occfcstcurrent,
       isnull(MAX(bv30_occfcstpickup), 0.0) as bv30_occfcstpickup,
       isnull(MAX(bv31_occfcstcurrent), 0.0) as bv31_occfcstcurrent,
       isnull(MAX(bv31_occfcstpickup), 0.0) as bv31_occfcstpickup,
       isnull(MAX(bv32_occfcstcurrent), 0.0) as bv32_occfcstcurrent,
       isnull(MAX(bv32_occfcstpickup), 0.0) as bv32_occfcstpickup,
       isnull(MAX(bv33_occfcstcurrent), 0.0) as bv33_occfcstcurrent,
       isnull(MAX(bv33_occfcstpickup), 0.0) as bv33_occfcstpickup,
       isnull(MAX(bv34_occfcstcurrent), 0.0) as bv34_occfcstcurrent,
       isnull(MAX(bv34_occfcstpickup), 0.0) as bv34_occfcstpickup,
       isnull(MAX(bv35_occfcstcurrent), 0.0) as bv35_occfcstcurrent,
       isnull(MAX(bv35_occfcstpickup), 0.0) as bv35_occfcstpickup,
       isnull(MAX(bv36_occfcstcurrent), 0.0) as bv36_occfcstcurrent,
       isnull(MAX(bv36_occfcstpickup), 0.0) as bv36_occfcstpickup,
       isnull(MAX(bv37_occfcstcurrent), 0.0) as bv37_occfcstcurrent,
       isnull(MAX(bv37_occfcstpickup), 0.0) as bv37_occfcstpickup,
       isnull(MAX(bv38_occfcstcurrent), 0.0) as bv38_occfcstcurrent,
       isnull(MAX(bv38_occfcstpickup), 0.0) as bv38_occfcstpickup,
       isnull(MAX(bv39_occfcstcurrent), 0.0) as bv39_occfcstcurrent,
       isnull(MAX(bv39_occfcstpickup), 0.0) as bv39_occfcstpickup,
       isnull(MAX(bv40_occfcstcurrent), 0.0) as bv40_occfcstcurrent,
       isnull(MAX(bv40_occfcstpickup), 0.0) as bv40_occfcstpickup,
       isnull(MAX(bv41_occfcstcurrent), 0.0) as bv41_occfcstcurrent,
       isnull(MAX(bv41_occfcstpickup), 0.0) as bv41_occfcstpickup,
       isnull(MAX(bv42_occfcstcurrent), 0.0) as bv42_occfcstcurrent,
       isnull(MAX(bv42_occfcstpickup), 0.0) as bv42_occfcstpickup,
       isnull(MAX(bv43_occfcstcurrent), 0.0) as bv43_occfcstcurrent,
       isnull(MAX(bv43_occfcstpickup), 0.0) as bv43_occfcstpickup,
       isnull(MAX(bv44_occfcstcurrent), 0.0) as bv44_occfcstcurrent,
       isnull(MAX(bv44_occfcstpickup), 0.0) as bv44_occfcstpickup,
       isnull(MAX(bv45_occfcstcurrent), 0.0) as bv45_occfcstcurrent,
       isnull(MAX(bv45_occfcstpickup), 0.0) as bv45_occfcstpickup,
       isnull(MAX(bv46_occfcstcurrent), 0.0) as bv46_occfcstcurrent,
       isnull(MAX(bv46_occfcstpickup), 0.0) as bv46_occfcstpickup,
       isnull(MAX(bv47_occfcstcurrent), 0.0) as bv47_occfcstcurrent,
       isnull(MAX(bv47_occfcstpickup), 0.0) as bv47_occfcstpickup,
       isnull(MAX(bv48_occfcstcurrent), 0.0) as bv48_occfcstcurrent,
       isnull(MAX(bv48_occfcstpickup), 0.0) as bv48_occfcstpickup,
       isnull(MAX(bv49_occfcstcurrent), 0.0) as bv49_occfcstcurrent,
       isnull(MAX(bv49_occfcstpickup), 0.0) as bv49_occfcstpickup,
       isnull(MAX(bv50_occfcstcurrent), 0.0) as bv50_occfcstcurrent,
       isnull(MAX(bv50_occfcstpickup), 0.0) as bv50_occfcstpickup,
       isnull(MAX(bv1_bookedroomrevenuecurrent), 0.0) as bv1_bookedroomrevenuecurrent,
       isnull(MAX(bv1_bookedroomrevenuepickup), 0.0) as bv1_bookedroomrevenuepickup,
       isnull(MAX(bv2_bookedroomrevenuecurrent), 0.0) as bv2_bookedroomrevenuecurrent,
       isnull(MAX(bv2_bookedroomrevenuepickup), 0.0) as bv2_bookedroomrevenuepickup,
       isnull(MAX(bv3_bookedroomrevenuecurrent), 0.0) as bv3_bookedroomrevenuecurrent,
       isnull(MAX(bv3_bookedroomrevenuepickup), 0.0) as bv3_bookedroomrevenuepickup,
       isnull(MAX(bv4_bookedroomrevenuecurrent), 0.0) as bv4_bookedroomrevenuecurrent,
       isnull(MAX(bv4_bookedroomrevenuepickup), 0.0) as bv4_bookedroomrevenuepickup,
       isnull(MAX(bv5_bookedroomrevenuecurrent), 0.0) as bv5_bookedroomrevenuecurrent,
       isnull(MAX(bv5_bookedroomrevenuepickup), 0.0) as bv5_bookedroomrevenuepickup,
       isnull(MAX(bv6_bookedroomrevenuecurrent), 0.0) as bv6_bookedroomrevenuecurrent,
       isnull(MAX(bv6_bookedroomrevenuepickup), 0.0) as bv6_bookedroomrevenuepickup,
       isnull(MAX(bv7_bookedroomrevenuecurrent), 0.0) as bv7_bookedroomrevenuecurrent,
       isnull(MAX(bv7_bookedroomrevenuepickup), 0.0) as bv7_bookedroomrevenuepickup,
       isnull(MAX(bv8_bookedroomrevenuecurrent), 0.0) as bv8_bookedroomrevenuecurrent,
       isnull(MAX(bv8_bookedroomrevenuepickup), 0.0) as bv8_bookedroomrevenuepickup,
       isnull(MAX(bv9_bookedroomrevenuecurrent), 0.0) as bv9_bookedroomrevenuecurrent,
       isnull(MAX(bv9_bookedroomrevenuepickup), 0.0) as bv9_bookedroomrevenuepickup,
       isnull(MAX(bv10_bookedroomrevenuecurrent), 0.0) as bv10_bookedroomrevenuecurrent,
       isnull(MAX(bv10_bookedroomrevenuepickup), 0.0) as bv10_bookedroomrevenuepickup,
       isnull(MAX(bv11_bookedroomrevenuecurrent), 0.0) as bv11_bookedroomrevenuecurrent,
       isnull(MAX(bv11_bookedroomrevenuepickup), 0.0) as bv11_bookedroomrevenuepickup,
       isnull(MAX(bv12_bookedroomrevenuecurrent), 0.0) as bv12_bookedroomrevenuecurrent,
       isnull(MAX(bv12_bookedroomrevenuepickup), 0.0) as bv12_bookedroomrevenuepickup,
       isnull(MAX(bv13_bookedroomrevenuecurrent), 0.0) as bv13_bookedroomrevenuecurrent,
       isnull(MAX(bv13_bookedroomrevenuepickup), 0.0) as bv13_bookedroomrevenuepickup,
       isnull(MAX(bv14_bookedroomrevenuecurrent), 0.0) as bv14_bookedroomrevenuecurrent,
       isnull(MAX(bv14_bookedroomrevenuepickup), 0.0) as bv14_bookedroomrevenuepickup,
       isnull(MAX(bv15_bookedroomrevenuecurrent), 0.0) as bv15_bookedroomrevenuecurrent,
       isnull(MAX(bv15_bookedroomrevenuepickup), 0.0) as bv15_bookedroomrevenuepickup,
       isnull(MAX(bv16_bookedroomrevenuecurrent), 0.0) as bv16_bookedroomrevenuecurrent,
       isnull(MAX(bv16_bookedroomrevenuepickup), 0.0) as bv16_bookedroomrevenuepickup,
       isnull(MAX(bv17_bookedroomrevenuecurrent), 0.0) as bv17_bookedroomrevenuecurrent,
       isnull(MAX(bv17_bookedroomrevenuepickup), 0.0) as bv17_bookedroomrevenuepickup,
       isnull(MAX(bv18_bookedroomrevenuecurrent), 0.0) as bv18_bookedroomrevenuecurrent,
       isnull(MAX(bv18_bookedroomrevenuepickup), 0.0) as bv18_bookedroomrevenuepickup,
       isnull(MAX(bv19_bookedroomrevenuecurrent), 0.0) as bv19_bookedroomrevenuecurrent,
       isnull(MAX(bv19_bookedroomrevenuepickup), 0.0) as bv19_bookedroomrevenuepickup,
       isnull(MAX(bv20_bookedroomrevenuecurrent), 0.0) as bv20_bookedroomrevenuecurrent,
       isnull(MAX(bv20_bookedroomrevenuepickup), 0.0) as bv20_bookedroomrevenuepickup,
       isnull(MAX(bv21_bookedroomrevenuecurrent), 0.0) as bv21_bookedroomrevenuecurrent,
       isnull(MAX(bv21_bookedroomrevenuepickup), 0.0) as bv21_bookedroomrevenuepickup,
       isnull(MAX(bv22_bookedroomrevenuecurrent), 0.0) as bv22_bookedroomrevenuecurrent,
       isnull(MAX(bv22_bookedroomrevenuepickup), 0.0) as bv22_bookedroomrevenuepickup,
       isnull(MAX(bv23_bookedroomrevenuecurrent), 0.0) as bv23_bookedroomrevenuecurrent,
       isnull(MAX(bv23_bookedroomrevenuepickup), 0.0) as bv23_bookedroomrevenuepickup,
       isnull(MAX(bv24_bookedroomrevenuecurrent), 0.0) as bv24_bookedroomrevenuecurrent,
       isnull(MAX(bv24_bookedroomrevenuepickup), 0.0) as bv24_bookedroomrevenuepickup,
       isnull(MAX(bv25_bookedroomrevenuecurrent), 0.0) as bv25_bookedroomrevenuecurrent,
       isnull(MAX(bv25_bookedroomrevenuepickup), 0.0) as bv25_bookedroomrevenuepickup,
       isnull(MAX(bv26_bookedroomrevenuecurrent), 0.0) as bv26_bookedroomrevenuecurrent,
       isnull(MAX(bv26_bookedroomrevenuepickup), 0.0) as bv26_bookedroomrevenuepickup,
       isnull(MAX(bv27_bookedroomrevenuecurrent), 0.0) as bv27_bookedroomrevenuecurrent,
       isnull(MAX(bv27_bookedroomrevenuepickup), 0.0) as bv27_bookedroomrevenuepickup,
       isnull(MAX(bv28_bookedroomrevenuecurrent), 0.0) as bv28_bookedroomrevenuecurrent,
       isnull(MAX(bv28_bookedroomrevenuepickup), 0.0) as bv28_bookedroomrevenuepickup,
       isnull(MAX(bv29_bookedroomrevenuecurrent), 0.0) as bv29_bookedroomrevenuecurrent,
       isnull(MAX(bv29_bookedroomrevenuepickup), 0.0) as bv29_bookedroomrevenuepickup,
       isnull(MAX(bv30_bookedroomrevenuecurrent), 0.0) as bv30_bookedroomrevenuecurrent,
       isnull(MAX(bv30_bookedroomrevenuepickup), 0.0) as bv30_bookedroomrevenuepickup,
       isnull(MAX(bv31_bookedroomrevenuecurrent), 0.0) as bv31_bookedroomrevenuecurrent,
       isnull(MAX(bv31_bookedroomrevenuepickup), 0.0) as bv31_bookedroomrevenuepickup,
       isnull(MAX(bv32_bookedroomrevenuecurrent), 0.0) as bv32_bookedroomrevenuecurrent,
       isnull(MAX(bv32_bookedroomrevenuepickup), 0.0) as bv32_bookedroomrevenuepickup,
       isnull(MAX(bv33_bookedroomrevenuecurrent), 0.0) as bv33_bookedroomrevenuecurrent,
       isnull(MAX(bv33_bookedroomrevenuepickup), 0.0) as bv33_bookedroomrevenuepickup,
       isnull(MAX(bv34_bookedroomrevenuecurrent), 0.0) as bv34_bookedroomrevenuecurrent,
       isnull(MAX(bv34_bookedroomrevenuepickup), 0.0) as bv34_bookedroomrevenuepickup,
       isnull(MAX(bv35_bookedroomrevenuecurrent), 0.0) as bv35_bookedroomrevenuecurrent,
       isnull(MAX(bv35_bookedroomrevenuepickup), 0.0) as bv35_bookedroomrevenuepickup,
       isnull(MAX(bv36_bookedroomrevenuecurrent), 0.0) as bv36_bookedroomrevenuecurrent,
       isnull(MAX(bv36_bookedroomrevenuepickup), 0.0) as bv36_bookedroomrevenuepickup,
       isnull(MAX(bv37_bookedroomrevenuecurrent), 0.0) as bv37_bookedroomrevenuecurrent,
       isnull(MAX(bv37_bookedroomrevenuepickup), 0.0) as bv37_bookedroomrevenuepickup,
       isnull(MAX(bv38_bookedroomrevenuecurrent), 0.0) as bv38_bookedroomrevenuecurrent,
       isnull(MAX(bv38_bookedroomrevenuepickup), 0.0) as bv38_bookedroomrevenuepickup,
       isnull(MAX(bv39_bookedroomrevenuecurrent), 0.0) as bv39_bookedroomrevenuecurrent,
       isnull(MAX(bv39_bookedroomrevenuepickup), 0.0) as bv39_bookedroomrevenuepickup,
       isnull(MAX(bv40_bookedroomrevenuecurrent), 0.0) as bv40_bookedroomrevenuecurrent,
       isnull(MAX(bv40_bookedroomrevenuepickup), 0.0) as bv40_bookedroomrevenuepickup,
       isnull(MAX(bv41_bookedroomrevenuecurrent), 0.0) as bv41_bookedroomrevenuecurrent,
       isnull(MAX(bv41_bookedroomrevenuepickup), 0.0) as bv41_bookedroomrevenuepickup,
       isnull(MAX(bv42_bookedroomrevenuecurrent), 0.0) as bv42_bookedroomrevenuecurrent,
       isnull(MAX(bv42_bookedroomrevenuepickup), 0.0) as bv42_bookedroomrevenuepickup,
       isnull(MAX(bv43_bookedroomrevenuecurrent), 0.0) as bv43_bookedroomrevenuecurrent,
       isnull(MAX(bv43_bookedroomrevenuepickup), 0.0) as bv43_bookedroomrevenuepickup,
       isnull(MAX(bv44_bookedroomrevenuecurrent), 0.0) as bv44_bookedroomrevenuecurrent,
       isnull(MAX(bv44_bookedroomrevenuepickup), 0.0) as bv44_bookedroomrevenuepickup,
       isnull(MAX(bv45_bookedroomrevenuecurrent), 0.0) as bv45_bookedroomrevenuecurrent,
       isnull(MAX(bv45_bookedroomrevenuepickup), 0.0) as bv45_bookedroomrevenuepickup,
       isnull(MAX(bv46_bookedroomrevenuecurrent), 0.0) as bv46_bookedroomrevenuecurrent,
       isnull(MAX(bv46_bookedroomrevenuepickup), 0.0) as bv46_bookedroomrevenuepickup,
       isnull(MAX(bv47_bookedroomrevenuecurrent), 0.0) as bv47_bookedroomrevenuecurrent,
       isnull(MAX(bv47_bookedroomrevenuepickup), 0.0) as bv47_bookedroomrevenuepickup,
       isnull(MAX(bv48_bookedroomrevenuecurrent), 0.0) as bv48_bookedroomrevenuecurrent,
       isnull(MAX(bv48_bookedroomrevenuepickup), 0.0) as bv48_bookedroomrevenuepickup,
       isnull(MAX(bv49_bookedroomrevenuecurrent), 0.0) as bv49_bookedroomrevenuecurrent,
       isnull(MAX(bv49_bookedroomrevenuepickup), 0.0) as bv49_bookedroomrevenuepickup,
       isnull(MAX(bv50_bookedroomrevenuecurrent), 0.0) as bv50_bookedroomrevenuecurrent,
       isnull(MAX(bv50_bookedroomrevenuepickup), 0.0) as bv50_bookedroomrevenuepickup,
       isnull(MAX(bv1_fcstedroomrevenuecurrent), 0.0) as bv1_fcstedroomrevenuecurrent,
       isnull(MAX(bv1_fcstedroomrevenuepickup), 0.0) as bv1_fcstedroomrevenuepickup,
       isnull(MAX(bv2_fcstedroomrevenuecurrent), 0.0) as bv2_fcstedroomrevenuecurrent,
       isnull(MAX(bv2_fcstedroomrevenuepickup), 0.0) as bv2_fcstedroomrevenuepickup,
       isnull(MAX(bv3_fcstedroomrevenuecurrent), 0.0) as bv3_fcstedroomrevenuecurrent,
       isnull(MAX(bv3_fcstedroomrevenuepickup), 0.0) as bv3_fcstedroomrevenuepickup,
       isnull(MAX(bv4_fcstedroomrevenuecurrent), 0.0) as bv4_fcstedroomrevenuecurrent,
       isnull(MAX(bv4_fcstedroomrevenuepickup), 0.0) as bv4_fcstedroomrevenuepickup,
       isnull(MAX(bv5_fcstedroomrevenuecurrent), 0.0) as bv5_fcstedroomrevenuecurrent,
       isnull(MAX(bv5_fcstedroomrevenuepickup), 0.0) as bv5_fcstedroomrevenuepickup,
       isnull(MAX(bv6_fcstedroomrevenuecurrent), 0.0) as bv6_fcstedroomrevenuecurrent,
       isnull(MAX(bv6_fcstedroomrevenuepickup), 0.0) as bv6_fcstedroomrevenuepickup,
       isnull(MAX(bv7_fcstedroomrevenuecurrent), 0.0) as bv7_fcstedroomrevenuecurrent,
       isnull(MAX(bv7_fcstedroomrevenuepickup), 0.0) as bv7_fcstedroomrevenuepickup,
       isnull(MAX(bv8_fcstedroomrevenuecurrent), 0.0) as bv8_fcstedroomrevenuecurrent,
       isnull(MAX(bv8_fcstedroomrevenuepickup), 0.0) as bv8_fcstedroomrevenuepickup,
       isnull(MAX(bv9_fcstedroomrevenuecurrent), 0.0) as bv9_fcstedroomrevenuecurrent,
       isnull(MAX(bv9_fcstedroomrevenuepickup), 0.0) as bv9_fcstedroomrevenuepickup,
       isnull(MAX(bv10_fcstedroomrevenuecurrent), 0.0) as bv10_fcstedroomrevenuecurrent,
       isnull(MAX(bv10_fcstedroomrevenuepickup), 0.0) as bv10_fcstedroomrevenuepickup,
       isnull(MAX(bv11_fcstedroomrevenuecurrent), 0.0) as bv11_fcstedroomrevenuecurrent,
       isnull(MAX(bv11_fcstedroomrevenuepickup), 0.0) as bv11_fcstedroomrevenuepickup,
       isnull(MAX(bv12_fcstedroomrevenuecurrent), 0.0) as bv12_fcstedroomrevenuecurrent,
       isnull(MAX(bv12_fcstedroomrevenuepickup), 0.0) as bv12_fcstedroomrevenuepickup,
       isnull(MAX(bv13_fcstedroomrevenuecurrent), 0.0) as bv13_fcstedroomrevenuecurrent,
       isnull(MAX(bv13_fcstedroomrevenuepickup), 0.0) as bv13_fcstedroomrevenuepickup,
       isnull(MAX(bv14_fcstedroomrevenuecurrent), 0.0) as bv14_fcstedroomrevenuecurrent,
       isnull(MAX(bv14_fcstedroomrevenuepickup), 0.0) as bv14_fcstedroomrevenuepickup,
       isnull(MAX(bv15_fcstedroomrevenuecurrent), 0.0) as bv15_fcstedroomrevenuecurrent,
       isnull(MAX(bv15_fcstedroomrevenuepickup), 0.0) as bv15_fcstedroomrevenuepickup,
       isnull(MAX(bv16_fcstedroomrevenuecurrent), 0.0) as bv16_fcstedroomrevenuecurrent,
       isnull(MAX(bv16_fcstedroomrevenuepickup), 0.0) as bv16_fcstedroomrevenuepickup,
       isnull(MAX(bv17_fcstedroomrevenuecurrent), 0.0) as bv17_fcstedroomrevenuecurrent,
       isnull(MAX(bv17_fcstedroomrevenuepickup), 0.0) as bv17_fcstedroomrevenuepickup,
       isnull(MAX(bv18_fcstedroomrevenuecurrent), 0.0) as bv18_fcstedroomrevenuecurrent,
       isnull(MAX(bv18_fcstedroomrevenuepickup), 0.0) as bv18_fcstedroomrevenuepickup,
       isnull(MAX(bv19_fcstedroomrevenuecurrent), 0.0) as bv19_fcstedroomrevenuecurrent,
       isnull(MAX(bv19_fcstedroomrevenuepickup), 0.0) as bv19_fcstedroomrevenuepickup,
       isnull(MAX(bv20_fcstedroomrevenuecurrent), 0.0) as bv20_fcstedroomrevenuecurrent,
       isnull(MAX(bv20_fcstedroomrevenuepickup), 0.0) as bv20_fcstedroomrevenuepickup,
       isnull(MAX(bv21_fcstedroomrevenuecurrent), 0.0) as bv21_fcstedroomrevenuecurrent,
       isnull(MAX(bv21_fcstedroomrevenuepickup), 0.0) as bv21_fcstedroomrevenuepickup,
       isnull(MAX(bv22_fcstedroomrevenuecurrent), 0.0) as bv22_fcstedroomrevenuecurrent,
       isnull(MAX(bv22_fcstedroomrevenuepickup), 0.0) as bv22_fcstedroomrevenuepickup,
       isnull(MAX(bv23_fcstedroomrevenuecurrent), 0.0) as bv23_fcstedroomrevenuecurrent,
       isnull(MAX(bv23_fcstedroomrevenuepickup), 0.0) as bv23_fcstedroomrevenuepickup,
       isnull(MAX(bv24_fcstedroomrevenuecurrent), 0.0) as bv24_fcstedroomrevenuecurrent,
       isnull(MAX(bv24_fcstedroomrevenuepickup), 0.0) as bv24_fcstedroomrevenuepickup,
       isnull(MAX(bv25_fcstedroomrevenuecurrent), 0.0) as bv25_fcstedroomrevenuecurrent,
       isnull(MAX(bv25_fcstedroomrevenuepickup), 0.0) as bv25_fcstedroomrevenuepickup,
       isnull(MAX(bv26_fcstedroomrevenuecurrent), 0.0) as bv26_fcstedroomrevenuecurrent,
       isnull(MAX(bv26_fcstedroomrevenuepickup), 0.0) as bv26_fcstedroomrevenuepickup,
       isnull(MAX(bv27_fcstedroomrevenuecurrent), 0.0) as bv27_fcstedroomrevenuecurrent,
       isnull(MAX(bv27_fcstedroomrevenuepickup), 0.0) as bv27_fcstedroomrevenuepickup,
       isnull(MAX(bv28_fcstedroomrevenuecurrent), 0.0) as bv28_fcstedroomrevenuecurrent,
       isnull(MAX(bv28_fcstedroomrevenuepickup), 0.0) as bv28_fcstedroomrevenuepickup,
       isnull(MAX(bv29_fcstedroomrevenuecurrent), 0.0) as bv29_fcstedroomrevenuecurrent,
       isnull(MAX(bv29_fcstedroomrevenuepickup), 0.0) as bv29_fcstedroomrevenuepickup,
       isnull(MAX(bv30_fcstedroomrevenuecurrent), 0.0) as bv30_fcstedroomrevenuecurrent,
       isnull(MAX(bv30_fcstedroomrevenuepickup), 0.0) as bv30_fcstedroomrevenuepickup,
       isnull(MAX(bv31_fcstedroomrevenuecurrent), 0.0) as bv31_fcstedroomrevenuecurrent,
       isnull(MAX(bv31_fcstedroomrevenuepickup), 0.0) as bv31_fcstedroomrevenuepickup,
       isnull(MAX(bv32_fcstedroomrevenuecurrent), 0.0) as bv32_fcstedroomrevenuecurrent,
       isnull(MAX(bv32_fcstedroomrevenuepickup), 0.0) as bv32_fcstedroomrevenuepickup,
       isnull(MAX(bv33_fcstedroomrevenuecurrent), 0.0) as bv33_fcstedroomrevenuecurrent,
       isnull(MAX(bv33_fcstedroomrevenuepickup), 0.0) as bv33_fcstedroomrevenuepickup,
       isnull(MAX(bv34_fcstedroomrevenuecurrent), 0.0) as bv34_fcstedroomrevenuecurrent,
       isnull(MAX(bv34_fcstedroomrevenuepickup), 0.0) as bv34_fcstedroomrevenuepickup,
       isnull(MAX(bv35_fcstedroomrevenuecurrent), 0.0) as bv35_fcstedroomrevenuecurrent,
       isnull(MAX(bv35_fcstedroomrevenuepickup), 0.0) as bv35_fcstedroomrevenuepickup,
       isnull(MAX(bv36_fcstedroomrevenuecurrent), 0.0) as bv36_fcstedroomrevenuecurrent,
       isnull(MAX(bv36_fcstedroomrevenuepickup), 0.0) as bv36_fcstedroomrevenuepickup,
       isnull(MAX(bv37_fcstedroomrevenuecurrent), 0.0) as bv37_fcstedroomrevenuecurrent,
       isnull(MAX(bv37_fcstedroomrevenuepickup), 0.0) as bv37_fcstedroomrevenuepickup,
       isnull(MAX(bv38_fcstedroomrevenuecurrent), 0.0) as bv38_fcstedroomrevenuecurrent,
       isnull(MAX(bv38_fcstedroomrevenuepickup), 0.0) as bv38_fcstedroomrevenuepickup,
       isnull(MAX(bv39_fcstedroomrevenuecurrent), 0.0) as bv39_fcstedroomrevenuecurrent,
       isnull(MAX(bv39_fcstedroomrevenuepickup), 0.0) as bv39_fcstedroomrevenuepickup,
       isnull(MAX(bv40_fcstedroomrevenuecurrent), 0.0) as bv40_fcstedroomrevenuecurrent,
       isnull(MAX(bv40_fcstedroomrevenuepickup), 0.0) as bv40_fcstedroomrevenuepickup,
       isnull(MAX(bv41_fcstedroomrevenuecurrent), 0.0) as bv41_fcstedroomrevenuecurrent,
       isnull(MAX(bv41_fcstedroomrevenuepickup), 0.0) as bv41_fcstedroomrevenuepickup,
       isnull(MAX(bv42_fcstedroomrevenuecurrent), 0.0) as bv42_fcstedroomrevenuecurrent,
       isnull(MAX(bv42_fcstedroomrevenuepickup), 0.0) as bv42_fcstedroomrevenuepickup,
       isnull(MAX(bv43_fcstedroomrevenuecurrent), 0.0) as bv43_fcstedroomrevenuecurrent,
       isnull(MAX(bv43_fcstedroomrevenuepickup), 0.0) as bv43_fcstedroomrevenuepickup,
       isnull(MAX(bv44_fcstedroomrevenuecurrent), 0.0) as bv44_fcstedroomrevenuecurrent,
       isnull(MAX(bv44_fcstedroomrevenuepickup), 0.0) as bv44_fcstedroomrevenuepickup,
       isnull(MAX(bv45_fcstedroomrevenuecurrent), 0.0) as bv45_fcstedroomrevenuecurrent,
       isnull(MAX(bv45_fcstedroomrevenuepickup), 0.0) as bv45_fcstedroomrevenuepickup,
       isnull(MAX(bv46_fcstedroomrevenuecurrent), 0.0) as bv46_fcstedroomrevenuecurrent,
       isnull(MAX(bv46_fcstedroomrevenuepickup), 0.0) as bv46_fcstedroomrevenuepickup,
       isnull(MAX(bv47_fcstedroomrevenuecurrent), 0.0) as bv47_fcstedroomrevenuecurrent,
       isnull(MAX(bv47_fcstedroomrevenuepickup), 0.0) as bv47_fcstedroomrevenuepickup,
       isnull(MAX(bv48_fcstedroomrevenuecurrent), 0.0) as bv48_fcstedroomrevenuecurrent,
       isnull(MAX(bv48_fcstedroomrevenuepickup), 0.0) as bv48_fcstedroomrevenuepickup,
       isnull(MAX(bv49_fcstedroomrevenuecurrent), 0.0) as bv49_fcstedroomrevenuecurrent,
       isnull(MAX(bv49_fcstedroomrevenuepickup), 0.0) as bv49_fcstedroomrevenuepickup,
       isnull(MAX(bv50_fcstedroomrevenuecurrent), 0.0) as bv50_fcstedroomrevenuecurrent,
       isnull(MAX(bv50_fcstedroomrevenuepickup), 0.0) as bv50_fcstedroomrevenuepickup,
       isnull(MAX(bv1_bookedadrcurrent), 0.0) as bv1_bookedadrcurrent,
       isnull(MAX(bv1_bookedadrpickup), 0.0) as bv1_bookedadrpickup,
       isnull(MAX(bv2_bookedadrcurrent), 0.0) as bv2_bookedadrcurrent,
       isnull(MAX(bv2_bookedadrpickup), 0.0) as bv2_bookedadrpickup,
       isnull(MAX(bv3_bookedadrcurrent), 0.0) as bv3_bookedadrcurrent,
       isnull(MAX(bv3_bookedadrpickup), 0.0) as bv3_bookedadrpickup,
       isnull(MAX(bv4_bookedadrcurrent), 0.0) as bv4_bookedadrcurrent,
       isnull(MAX(bv4_bookedadrpickup), 0.0) as bv4_bookedadrpickup,
       isnull(MAX(bv5_bookedadrcurrent), 0.0) as bv5_bookedadrcurrent,
       isnull(MAX(bv5_bookedadrpickup), 0.0) as bv5_bookedadrpickup,
       isnull(MAX(bv6_bookedadrcurrent), 0.0) as bv6_bookedadrcurrent,
       isnull(MAX(bv6_bookedadrpickup), 0.0) as bv6_bookedadrpickup,
       isnull(MAX(bv7_bookedadrcurrent), 0.0) as bv7_bookedadrcurrent,
       isnull(MAX(bv7_bookedadrpickup), 0.0) as bv7_bookedadrpickup,
       isnull(MAX(bv8_bookedadrcurrent), 0.0) as bv8_bookedadrcurrent,
       isnull(MAX(bv8_bookedadrpickup), 0.0) as bv8_bookedadrpickup,
       isnull(MAX(bv9_bookedadrcurrent), 0.0) as bv9_bookedadrcurrent,
       isnull(MAX(bv9_bookedadrpickup), 0.0) as bv9_bookedadrpickup,
       isnull(MAX(bv10_bookedadrcurrent), 0.0) as bv10_bookedadrcurrent,
       isnull(MAX(bv10_bookedadrpickup), 0.0) as bv10_bookedadrpickup,
       isnull(MAX(bv11_bookedadrcurrent), 0.0) as bv11_bookedadrcurrent,
       isnull(MAX(bv11_bookedadrpickup), 0.0) as bv11_bookedadrpickup,
       isnull(MAX(bv12_bookedadrcurrent), 0.0) as bv12_bookedadrcurrent,
       isnull(MAX(bv12_bookedadrpickup), 0.0) as bv12_bookedadrpickup,
       isnull(MAX(bv13_bookedadrcurrent), 0.0) as bv13_bookedadrcurrent,
       isnull(MAX(bv13_bookedadrpickup), 0.0) as bv13_bookedadrpickup,
       isnull(MAX(bv14_bookedadrcurrent), 0.0) as bv14_bookedadrcurrent,
       isnull(MAX(bv14_bookedadrpickup), 0.0) as bv14_bookedadrpickup,
       isnull(MAX(bv15_bookedadrcurrent), 0.0) as bv15_bookedadrcurrent,
       isnull(MAX(bv15_bookedadrpickup), 0.0) as bv15_bookedadrpickup,
       isnull(MAX(bv16_bookedadrcurrent), 0.0) as bv16_bookedadrcurrent,
       isnull(MAX(bv16_bookedadrpickup), 0.0) as bv16_bookedadrpickup,
       isnull(MAX(bv17_bookedadrcurrent), 0.0) as bv17_bookedadrcurrent,
       isnull(MAX(bv17_bookedadrpickup), 0.0) as bv17_bookedadrpickup,
       isnull(MAX(bv18_bookedadrcurrent), 0.0) as bv18_bookedadrcurrent,
       isnull(MAX(bv18_bookedadrpickup), 0.0) as bv18_bookedadrpickup,
       isnull(MAX(bv19_bookedadrcurrent), 0.0) as bv19_bookedadrcurrent,
       isnull(MAX(bv19_bookedadrpickup), 0.0) as bv19_bookedadrpickup,
       isnull(MAX(bv20_bookedadrcurrent), 0.0) as bv20_bookedadrcurrent,
       isnull(MAX(bv20_bookedadrpickup), 0.0) as bv20_bookedadrpickup,
       isnull(MAX(bv21_bookedadrcurrent), 0.0) as bv21_bookedadrcurrent,
       isnull(MAX(bv21_bookedadrpickup), 0.0) as bv21_bookedadrpickup,
       isnull(MAX(bv22_bookedadrcurrent), 0.0) as bv22_bookedadrcurrent,
       isnull(MAX(bv22_bookedadrpickup), 0.0) as bv22_bookedadrpickup,
       isnull(MAX(bv23_bookedadrcurrent), 0.0) as bv23_bookedadrcurrent,
       isnull(MAX(bv23_bookedadrpickup), 0.0) as bv23_bookedadrpickup,
       isnull(MAX(bv24_bookedadrcurrent), 0.0) as bv24_bookedadrcurrent,
       isnull(MAX(bv24_bookedadrpickup), 0.0) as bv24_bookedadrpickup,
       isnull(MAX(bv25_bookedadrcurrent), 0.0) as bv25_bookedadrcurrent,
       isnull(MAX(bv25_bookedadrpickup), 0.0) as bv25_bookedadrpickup,
       isnull(MAX(bv26_bookedadrcurrent), 0.0) as bv26_bookedadrcurrent,
       isnull(MAX(bv26_bookedadrpickup), 0.0) as bv26_bookedadrpickup,
       isnull(MAX(bv27_bookedadrcurrent), 0.0) as bv27_bookedadrcurrent,
       isnull(MAX(bv27_bookedadrpickup), 0.0) as bv27_bookedadrpickup,
       isnull(MAX(bv28_bookedadrcurrent), 0.0) as bv28_bookedadrcurrent,
       isnull(MAX(bv28_bookedadrpickup), 0.0) as bv28_bookedadrpickup,
       isnull(MAX(bv29_bookedadrcurrent), 0.0) as bv29_bookedadrcurrent,
       isnull(MAX(bv29_bookedadrpickup), 0.0) as bv29_bookedadrpickup,
       isnull(MAX(bv30_bookedadrcurrent), 0.0) as bv30_bookedadrcurrent,
       isnull(MAX(bv30_bookedadrpickup), 0.0) as bv30_bookedadrpickup,
       isnull(MAX(bv31_bookedadrcurrent), 0.0) as bv31_bookedadrcurrent,
       isnull(MAX(bv31_bookedadrpickup), 0.0) as bv31_bookedadrpickup,
       isnull(MAX(bv32_bookedadrcurrent), 0.0) as bv32_bookedadrcurrent,
       isnull(MAX(bv32_bookedadrpickup), 0.0) as bv32_bookedadrpickup,
       isnull(MAX(bv33_bookedadrcurrent), 0.0) as bv33_bookedadrcurrent,
       isnull(MAX(bv33_bookedadrpickup), 0.0) as bv33_bookedadrpickup,
       isnull(MAX(bv34_bookedadrcurrent), 0.0) as bv34_bookedadrcurrent,
       isnull(MAX(bv34_bookedadrpickup), 0.0) as bv34_bookedadrpickup,
       isnull(MAX(bv35_bookedadrcurrent), 0.0) as bv35_bookedadrcurrent,
       isnull(MAX(bv35_bookedadrpickup), 0.0) as bv35_bookedadrpickup,
       isnull(MAX(bv36_bookedadrcurrent), 0.0) as bv36_bookedadrcurrent,
       isnull(MAX(bv36_bookedadrpickup), 0.0) as bv36_bookedadrpickup,
       isnull(MAX(bv37_bookedadrcurrent), 0.0) as bv37_bookedadrcurrent,
       isnull(MAX(bv37_bookedadrpickup), 0.0) as bv37_bookedadrpickup,
       isnull(MAX(bv38_bookedadrcurrent), 0.0) as bv38_bookedadrcurrent,
       isnull(MAX(bv38_bookedadrpickup), 0.0) as bv38_bookedadrpickup,
       isnull(MAX(bv39_bookedadrcurrent), 0.0) as bv39_bookedadrcurrent,
       isnull(MAX(bv39_bookedadrpickup), 0.0) as bv39_bookedadrpickup,
       isnull(MAX(bv40_bookedadrcurrent), 0.0) as bv40_bookedadrcurrent,
       isnull(MAX(bv40_bookedadrpickup), 0.0) as bv40_bookedadrpickup,
       isnull(MAX(bv41_bookedadrcurrent), 0.0) as bv41_bookedadrcurrent,
       isnull(MAX(bv41_bookedadrpickup), 0.0) as bv41_bookedadrpickup,
       isnull(MAX(bv42_bookedadrcurrent), 0.0) as bv42_bookedadrcurrent,
       isnull(MAX(bv42_bookedadrpickup), 0.0) as bv42_bookedadrpickup,
       isnull(MAX(bv43_bookedadrcurrent), 0.0) as bv43_bookedadrcurrent,
       isnull(MAX(bv43_bookedadrpickup), 0.0) as bv43_bookedadrpickup,
       isnull(MAX(bv44_bookedadrcurrent), 0.0) as bv44_bookedadrcurrent,
       isnull(MAX(bv44_bookedadrpickup), 0.0) as bv44_bookedadrpickup,
       isnull(MAX(bv45_bookedadrcurrent), 0.0) as bv45_bookedadrcurrent,
       isnull(MAX(bv45_bookedadrpickup), 0.0) as bv45_bookedadrpickup,
       isnull(MAX(bv46_bookedadrcurrent), 0.0) as bv46_bookedadrcurrent,
       isnull(MAX(bv46_bookedadrpickup), 0.0) as bv46_bookedadrpickup,
       isnull(MAX(bv47_bookedadrcurrent), 0.0) as bv47_bookedadrcurrent,
       isnull(MAX(bv47_bookedadrpickup), 0.0) as bv47_bookedadrpickup,
       isnull(MAX(bv48_bookedadrcurrent), 0.0) as bv48_bookedadrcurrent,
       isnull(MAX(bv48_bookedadrpickup), 0.0) as bv48_bookedadrpickup,
       isnull(MAX(bv49_bookedadrcurrent), 0.0) as bv49_bookedadrcurrent,
       isnull(MAX(bv49_bookedadrpickup), 0.0) as bv49_bookedadrpickup,
       isnull(MAX(bv50_bookedadrcurrent), 0.0) as bv50_bookedadrcurrent,
       isnull(MAX(bv50_bookedadrpickup), 0.0) as bv50_bookedadrpickup,
       isnull(MAX(bv1_fcstedadrcurrent), 0.0) as bv1_fcstedadrcurrent,
       isnull(MAX(bv1_fcstedadrpickup), 0.0) as bv1_fcstedadrpickup,
       isnull(MAX(bv2_fcstedadrcurrent), 0.0) as bv2_fcstedadrcurrent,
       isnull(MAX(bv2_fcstedadrpickup), 0.0) as bv2_fcstedadrpickup,
       isnull(MAX(bv3_fcstedadrcurrent), 0.0) as bv3_fcstedadrcurrent,
       isnull(MAX(bv3_fcstedadrpickup), 0.0) as bv3_fcstedadrpickup,
       isnull(MAX(bv4_fcstedadrcurrent), 0.0) as bv4_fcstedadrcurrent,
       isnull(MAX(bv4_fcstedadrpickup), 0.0) as bv4_fcstedadrpickup,
       isnull(MAX(bv5_fcstedadrcurrent), 0.0) as bv5_fcstedadrcurrent,
       isnull(MAX(bv5_fcstedadrpickup), 0.0) as bv5_fcstedadrpickup,
       isnull(MAX(bv6_fcstedadrcurrent), 0.0) as bv6_fcstedadrcurrent,
       isnull(MAX(bv6_fcstedadrpickup), 0.0) as bv6_fcstedadrpickup,
       isnull(MAX(bv7_fcstedadrcurrent), 0.0) as bv7_fcstedadrcurrent,
       isnull(MAX(bv7_fcstedadrpickup), 0.0) as bv7_fcstedadrpickup,
       isnull(MAX(bv8_fcstedadrcurrent), 0.0) as bv8_fcstedadrcurrent,
       isnull(MAX(bv8_fcstedadrpickup), 0.0) as bv8_fcstedadrpickup,
       isnull(MAX(bv9_fcstedadrcurrent), 0.0) as bv9_fcstedadrcurrent,
       isnull(MAX(bv9_fcstedadrpickup), 0.0) as bv9_fcstedadrpickup,
       isnull(MAX(bv10_fcstedadrcurrent), 0.0) as bv10_fcstedadrcurrent,
       isnull(MAX(bv10_fcstedadrpickup), 0.0) as bv10_fcstedadrpickup,
       isnull(MAX(bv11_fcstedadrcurrent), 0.0) as bv11_fcstedadrcurrent,
       isnull(MAX(bv11_fcstedadrpickup), 0.0) as bv11_fcstedadrpickup,
       isnull(MAX(bv12_fcstedadrcurrent), 0.0) as bv12_fcstedadrcurrent,
       isnull(MAX(bv12_fcstedadrpickup), 0.0) as bv12_fcstedadrpickup,
       isnull(MAX(bv13_fcstedadrcurrent), 0.0) as bv13_fcstedadrcurrent,
       isnull(MAX(bv13_fcstedadrpickup), 0.0) as bv13_fcstedadrpickup,
       isnull(MAX(bv14_fcstedadrcurrent), 0.0) as bv14_fcstedadrcurrent,
       isnull(MAX(bv14_fcstedadrpickup), 0.0) as bv14_fcstedadrpickup,
       isnull(MAX(bv15_fcstedadrcurrent), 0.0) as bv15_fcstedadrcurrent,
       isnull(MAX(bv15_fcstedadrpickup), 0.0) as bv15_fcstedadrpickup,
       isnull(MAX(bv16_fcstedadrcurrent), 0.0) as bv16_fcstedadrcurrent,
       isnull(MAX(bv16_fcstedadrpickup), 0.0) as bv16_fcstedadrpickup,
       isnull(MAX(bv17_fcstedadrcurrent), 0.0) as bv17_fcstedadrcurrent,
       isnull(MAX(bv17_fcstedadrpickup), 0.0) as bv17_fcstedadrpickup,
       isnull(MAX(bv18_fcstedadrcurrent), 0.0) as bv18_fcstedadrcurrent,
       isnull(MAX(bv18_fcstedadrpickup), 0.0) as bv18_fcstedadrpickup,
       isnull(MAX(bv19_fcstedadrcurrent), 0.0) as bv19_fcstedadrcurrent,
       isnull(MAX(bv19_fcstedadrpickup), 0.0) as bv19_fcstedadrpickup,
       isnull(MAX(bv20_fcstedadrcurrent), 0.0) as bv20_fcstedadrcurrent,
       isnull(MAX(bv20_fcstedadrpickup), 0.0) as bv20_fcstedadrpickup,
       isnull(MAX(bv21_fcstedadrcurrent), 0.0) as bv21_fcstedadrcurrent,
       isnull(MAX(bv21_fcstedadrpickup), 0.0) as bv21_fcstedadrpickup,
       isnull(MAX(bv22_fcstedadrcurrent), 0.0) as bv22_fcstedadrcurrent,
       isnull(MAX(bv22_fcstedadrpickup), 0.0) as bv22_fcstedadrpickup,
       isnull(MAX(bv23_fcstedadrcurrent), 0.0) as bv23_fcstedadrcurrent,
       isnull(MAX(bv23_fcstedadrpickup), 0.0) as bv23_fcstedadrpickup,
       isnull(MAX(bv24_fcstedadrcurrent), 0.0) as bv24_fcstedadrcurrent,
       isnull(MAX(bv24_fcstedadrpickup), 0.0) as bv24_fcstedadrpickup,
       isnull(MAX(bv25_fcstedadrcurrent), 0.0) as bv25_fcstedadrcurrent,
       isnull(MAX(bv25_fcstedadrpickup), 0.0) as bv25_fcstedadrpickup,
       isnull(MAX(bv26_fcstedadrcurrent), 0.0) as bv26_fcstedadrcurrent,
       isnull(MAX(bv26_fcstedadrpickup), 0.0) as bv26_fcstedadrpickup,
       isnull(MAX(bv27_fcstedadrcurrent), 0.0) as bv27_fcstedadrcurrent,
       isnull(MAX(bv27_fcstedadrpickup), 0.0) as bv27_fcstedadrpickup,
       isnull(MAX(bv28_fcstedadrcurrent), 0.0) as bv28_fcstedadrcurrent,
       isnull(MAX(bv28_fcstedadrpickup), 0.0) as bv28_fcstedadrpickup,
       isnull(MAX(bv29_fcstedadrcurrent), 0.0) as bv29_fcstedadrcurrent,
       isnull(MAX(bv29_fcstedadrpickup), 0.0) as bv29_fcstedadrpickup,
       isnull(MAX(bv30_fcstedadrcurrent), 0.0) as bv30_fcstedadrcurrent,
       isnull(MAX(bv30_fcstedadrpickup), 0.0) as bv30_fcstedadrpickup,
       isnull(MAX(bv31_fcstedadrcurrent), 0.0) as bv31_fcstedadrcurrent,
       isnull(MAX(bv31_fcstedadrpickup), 0.0) as bv31_fcstedadrpickup,
       isnull(MAX(bv32_fcstedadrcurrent), 0.0) as bv32_fcstedadrcurrent,
       isnull(MAX(bv32_fcstedadrpickup), 0.0) as bv32_fcstedadrpickup,
       isnull(MAX(bv33_fcstedadrcurrent), 0.0) as bv33_fcstedadrcurrent,
       isnull(MAX(bv33_fcstedadrpickup), 0.0) as bv33_fcstedadrpickup,
       isnull(MAX(bv34_fcstedadrcurrent), 0.0) as bv34_fcstedadrcurrent,
       isnull(MAX(bv34_fcstedadrpickup), 0.0) as bv34_fcstedadrpickup,
       isnull(MAX(bv35_fcstedadrcurrent), 0.0) as bv35_fcstedadrcurrent,
       isnull(MAX(bv35_fcstedadrpickup), 0.0) as bv35_fcstedadrpickup,
       isnull(MAX(bv36_fcstedadrcurrent), 0.0) as bv36_fcstedadrcurrent,
       isnull(MAX(bv36_fcstedadrpickup), 0.0) as bv36_fcstedadrpickup,
       isnull(MAX(bv37_fcstedadrcurrent), 0.0) as bv37_fcstedadrcurrent,
       isnull(MAX(bv37_fcstedadrpickup), 0.0) as bv37_fcstedadrpickup,
       isnull(MAX(bv38_fcstedadrcurrent), 0.0) as bv38_fcstedadrcurrent,
       isnull(MAX(bv38_fcstedadrpickup), 0.0) as bv38_fcstedadrpickup,
       isnull(MAX(bv39_fcstedadrcurrent), 0.0) as bv39_fcstedadrcurrent,
       isnull(MAX(bv39_fcstedadrpickup), 0.0) as bv39_fcstedadrpickup,
       isnull(MAX(bv40_fcstedadrcurrent), 0.0) as bv40_fcstedadrcurrent,
       isnull(MAX(bv40_fcstedadrpickup), 0.0) as bv40_fcstedadrpickup,
       isnull(MAX(bv41_fcstedadrcurrent), 0.0) as bv41_fcstedadrcurrent,
       isnull(MAX(bv41_fcstedadrpickup), 0.0) as bv41_fcstedadrpickup,
       isnull(MAX(bv42_fcstedadrcurrent), 0.0) as bv42_fcstedadrcurrent,
       isnull(MAX(bv42_fcstedadrpickup), 0.0) as bv42_fcstedadrpickup,
       isnull(MAX(bv43_fcstedadrcurrent), 0.0) as bv43_fcstedadrcurrent,
       isnull(MAX(bv43_fcstedadrpickup), 0.0) as bv43_fcstedadrpickup,
       isnull(MAX(bv44_fcstedadrcurrent), 0.0) as bv44_fcstedadrcurrent,
       isnull(MAX(bv44_fcstedadrpickup), 0.0) as bv44_fcstedadrpickup,
       isnull(MAX(bv45_fcstedadrcurrent), 0.0) as bv45_fcstedadrcurrent,
       isnull(MAX(bv45_fcstedadrpickup), 0.0) as bv45_fcstedadrpickup,
       isnull(MAX(bv46_fcstedadrcurrent), 0.0) as bv46_fcstedadrcurrent,
       isnull(MAX(bv46_fcstedadrpickup), 0.0) as bv46_fcstedadrpickup,
       isnull(MAX(bv47_fcstedadrcurrent), 0.0) as bv47_fcstedadrcurrent,
       isnull(MAX(bv47_fcstedadrpickup), 0.0) as bv47_fcstedadrpickup,
       isnull(MAX(bv48_fcstedadrcurrent), 0.0) as bv48_fcstedadrcurrent,
       isnull(MAX(bv48_fcstedadrpickup), 0.0) as bv48_fcstedadrpickup,
       isnull(MAX(bv49_fcstedadrcurrent), 0.0) as bv49_fcstedadrcurrent,
       isnull(MAX(bv49_fcstedadrpickup), 0.0) as bv49_fcstedadrpickup,
       isnull(MAX(bv50_fcstedadrcurrent), 0.0) as bv50_fcstedadrcurrent,
       isnull(MAX(bv50_fcstedadrpickup), 0.0) as bv50_fcstedadrpickup,
       --Archana added for group block-, group pickup-Start
       isnull(MAX(bv1_block), 0) as bv1_block,
       isnull(MAX(bv1_block_available), 0) as bv1_block_available,
       isnull(MAX(bv1_block_pickup), 0) as bv1_block_pickup,
       isnull(MAX(bv2_block), 0) as bv2_block,
       isnull(MAX(bv2_block_available), 0) as bv2_block_available,
       isnull(MAX(bv2_block_pickup), 0) as bv2_block_pickup,
       isnull(MAX(bv3_block), 0) as bv3_block,
       isnull(MAX(bv3_block_available), 0) as bv3_block_available,
       isnull(MAX(bv3_block_pickup), 0) as bv3_block_pickup,
       isnull(MAX(bv4_block), 0) as bv4_block,
       isnull(MAX(bv4_block_available), 0) as bv4_block_available,
       isnull(MAX(bv4_block_pickup), 0) as bv4_block_pickup,
       isnull(MAX(bv5_block), 0) as bv5_block,
       isnull(MAX(bv5_block_available), 0) as bv5_block_available,
       isnull(MAX(bv5_block_pickup), 0) as bv5_block_pickup,
       isnull(MAX(bv6_block), 0) as bv6_block,
       isnull(MAX(bv6_block_available), 0) as bv6_block_available,
       isnull(MAX(bv6_block_pickup), 0) as bv6_block_pickup,
       isnull(MAX(bv7_block), 0) as bv7_block,
       isnull(MAX(bv7_block_available), 0) as bv7_block_available,
       isnull(MAX(bv7_block_pickup), 0) as bv7_block_pickup,
       isnull(MAX(bv8_block), 0) as bv8_block,
       isnull(MAX(bv8_block_available), 0) as bv8_block_available,
       isnull(MAX(bv8_block_pickup), 0) as bv8_block_pickup,
       isnull(MAX(bv9_block), 0) as bv9_block,
       isnull(MAX(bv9_block_available), 0) as bv9_block_available,
       isnull(MAX(bv9_block_pickup), 0) as bv9_block_pickup,
       isnull(MAX(bv10_block), 0) as bv10_block,
       isnull(MAX(bv10_block_available), 0) as bv10_block_available,
       isnull(MAX(bv10_block_pickup), 0) as bv10_block_pickup,
       isnull(MAX(bv11_block), 0) as bv11_block,
       isnull(MAX(bv11_block_available), 0) as bv11_block_available,
       isnull(MAX(bv11_block_pickup), 0) as bv11_block_pickup,
       isnull(MAX(bv12_block), 0) as bv12_block,
       isnull(MAX(bv12_block_available), 0) as bv12_block_available,
       isnull(MAX(bv12_block_pickup), 0) as bv12_block_pickup,
       isnull(MAX(bv13_block), 0) as bv13_block,
       isnull(MAX(bv13_block_available), 0) as bv13_block_available,
       isnull(MAX(bv13_block_pickup), 0) as bv13_block_pickup,
       isnull(MAX(bv14_block), 0) as bv14_block,
       isnull(MAX(bv14_block_available), 0) as bv14_block_available,
       isnull(MAX(bv14_block_pickup), 0) as bv14_block_pickup,
       isnull(MAX(bv15_block), 0) as bv15_block,
       isnull(MAX(bv15_block_available), 0) as bv15_block_available,
       isnull(MAX(bv15_block_pickup), 0) as bv15_block_pickup,
       isnull(MAX(bv16_block), 0) as bv16_block,
       isnull(MAX(bv16_block_available), 0) as bv16_block_available,
       isnull(MAX(bv16_block_pickup), 0) as bv16_block_pickup,
       isnull(MAX(bv17_block), 0) as bv17_block,
       isnull(MAX(bv17_block_available), 0) as bv17_block_available,
       isnull(MAX(bv17_block_pickup), 0) as bv17_block_pickup,
       isnull(MAX(bv18_block), 0) as bv18_block,
       isnull(MAX(bv18_block_available), 0) as bv18_block_available,
       isnull(MAX(bv18_block_pickup), 0) as bv18_block_pickup,
       isnull(MAX(bv19_block), 0) as bv19_block,
       isnull(MAX(bv19_block_available), 0) as bv19_block_available,
       isnull(MAX(bv19_block_pickup), 0) as bv19_block_pickup,
       isnull(MAX(bv20_block), 0) as bv20_block,
       isnull(MAX(bv20_block_available), 0) as bv20_block_available,
       isnull(MAX(bv20_block_pickup), 0) as bv20_block_pickup,
       isnull(MAX(bv21_block), 0) as bv21_block,
       isnull(MAX(bv21_block_available), 0) as bv21_block_available,
       isnull(MAX(bv21_block_pickup), 0) as bv21_block_pickup,
       isnull(MAX(bv22_block), 0) as bv22_block,
       isnull(MAX(bv22_block_available), 0) as bv22_block_available,
       isnull(MAX(bv22_block_pickup), 0) as bv22_block_pickup,
       isnull(MAX(bv23_block), 0) as bv23_block,
       isnull(MAX(bv23_block_available), 0) as bv23_block_available,
       isnull(MAX(bv23_block_pickup), 0) as bv23_block_pickup,
       isnull(MAX(bv24_block), 0) as bv24_block,
       isnull(MAX(bv24_block_available), 0) as bv24_block_available,
       isnull(MAX(bv24_block_pickup), 0) as bv24_block_pickup,
       isnull(MAX(bv25_block), 0) as bv25_block,
       isnull(MAX(bv25_block_available), 0) as bv25_block_available,
       isnull(MAX(bv25_block_pickup), 0) as bv25_block_pickup,
       isnull(MAX(bv26_block), 0) as bv26_block,
       isnull(MAX(bv26_block_available), 0) as bv26_block_available,
       isnull(MAX(bv26_block_pickup), 0) as bv26_block_pickup,
       isnull(MAX(bv27_block), 0) as bv27_block,
       isnull(MAX(bv27_block_available), 0) as bv27_block_available,
       isnull(MAX(bv27_block_pickup), 0) as bv27_block_pickup,
       isnull(MAX(bv28_block), 0) as bv28_block,
       isnull(MAX(bv28_block_available), 0) as bv28_block_available,
       isnull(MAX(bv28_block_pickup), 0) as bv28_block_pickup,
       isnull(MAX(bv29_block), 0) as bv29_block,
       isnull(MAX(bv29_block_available), 0) as bv29_block_available,
       isnull(MAX(bv29_block_pickup), 0) as bv29_block_pickup,
       isnull(MAX(bv30_block), 0) as bv30_block,
       isnull(MAX(bv30_block_available), 0) as bv30_block_available,
       isnull(MAX(bv30_block_pickup), 0) as bv30_block_pickup,
       isnull(MAX(bv31_block), 0) as bv31_block,
       isnull(MAX(bv31_block_available), 0) as bv31_block_available,
       isnull(MAX(bv31_block_pickup), 0) as bv31_block_pickup,
       isnull(MAX(bv32_block), 0) as bv32_block,
       isnull(MAX(bv32_block_available), 0) as bv32_block_available,
       isnull(MAX(bv32_block_pickup), 0) as bv32_block_pickup,
       isnull(MAX(bv33_block), 0) as bv33_block,
       isnull(MAX(bv33_block_available), 0) as bv33_block_available,
       isnull(MAX(bv33_block_pickup), 0) as bv33_block_pickup,
       isnull(MAX(bv34_block), 0) as bv34_block,
       isnull(MAX(bv34_block_available), 0) as bv34_block_available,
       isnull(MAX(bv34_block_pickup), 0) as bv34_block_pickup,
       isnull(MAX(bv35_block), 0) as bv35_block,
       isnull(MAX(bv35_block_available), 0) as bv35_block_available,
       isnull(MAX(bv35_block_pickup), 0) as bv35_block_pickup,
       isnull(MAX(bv36_block), 0) as bv36_block,
       isnull(MAX(bv36_block_available), 0) as bv36_block_available,
       isnull(MAX(bv36_block_pickup), 0) as bv36_block_pickup,
       isnull(MAX(bv37_block), 0) as bv37_block,
       isnull(MAX(bv37_block_available), 0) as bv37_block_available,
       isnull(MAX(bv37_block_pickup), 0) as bv37_block_pickup,
       isnull(MAX(bv38_block), 0) as bv38_block,
       isnull(MAX(bv38_block_available), 0) as bv38_block_available,
       isnull(MAX(bv38_block_pickup), 0) as bv38_block_pickup,
       isnull(MAX(bv39_block), 0) as bv39_block,
       isnull(MAX(bv39_block_available), 0) as bv39_block_available,
       isnull(MAX(bv39_block_pickup), 0) as bv39_block_pickup,
       isnull(MAX(bv40_block), 0) as bv40_block,
       isnull(MAX(bv40_block_available), 0) as bv40_block_available,
       isnull(MAX(bv40_block_pickup), 0) as bv40_block_pickup,
       isnull(MAX(bv41_block), 0) as bv41_block,
       isnull(MAX(bv41_block_available), 0) as bv41_block_available,
       isnull(MAX(bv41_block_pickup), 0) as bv41_block_pickup,
       isnull(MAX(bv42_block), 0) as bv42_block,
       isnull(MAX(bv42_block_available), 0) as bv42_block_available,
       isnull(MAX(bv42_block_pickup), 0) as bv42_block_pickup,
       isnull(MAX(bv43_block), 0) as bv43_block,
       isnull(MAX(bv43_block_available), 0) as bv43_block_available,
       isnull(MAX(bv43_block_pickup), 0) as bv43_block_pickup,
       isnull(MAX(bv44_block), 0) as bv44_block,
       isnull(MAX(bv44_block_available), 0) as bv44_block_available,
       isnull(MAX(bv44_block_pickup), 0) as bv44_block_pickup,
       isnull(MAX(bv45_block), 0) as bv45_block,
       isnull(MAX(bv45_block_available), 0) as bv45_block_available,
       isnull(MAX(bv45_block_pickup), 0) as bv45_block_pickup,
       isnull(MAX(bv46_block), 0) as bv46_block,
       isnull(MAX(bv46_block_available), 0) as bv46_block_available,
       isnull(MAX(bv46_block_pickup), 0) as bv46_block_pickup,
       isnull(MAX(bv47_block), 0) as bv47_block,
       isnull(MAX(bv47_block_available), 0) as bv47_block_available,
       isnull(MAX(bv47_block_pickup), 0) as bv47_block_pickup,
       isnull(MAX(bv48_block), 0) as bv48_block,
       isnull(MAX(bv48_block_available), 0) as bv48_block_available,
       isnull(MAX(bv48_block_pickup), 0) as bv48_block_pickup,
       isnull(MAX(bv49_block), 0) as bv49_block,
       isnull(MAX(bv49_block_available), 0) as bv49_block_available,
       isnull(MAX(bv49_block_pickup), 0) as bv49_block_pickup,
       isnull(MAX(bv50_block), 0) as bv50_block,
       isnull(MAX(bv50_block_available), 0) as bv50_block_available,
       isnull(MAX(bv50_block_pickup), 0) as bv50_block_pickup
       --Archana added for group block-, group pickup-End
from
    (
        select occupancy_dt,
               dow,
               (case Business_Group_ID
                    when @bv1 then
                        Business_Group_Name
                   end
                   ) as bv1_businessgroupname,
               (case Business_Group_ID
                    when @bv2 then
                        Business_Group_Name
                   end
                   ) as bv2_businessgroupname,
               (case Business_Group_ID
                    when @bv3 then
                        Business_Group_Name
                   end
                   ) as bv3_businessgroupname,
               (case Business_Group_ID
                    when @bv4 then
                        Business_Group_Name
                   end
                   ) as bv4_businessgroupname,
               (case Business_Group_ID
                    when @bv5 then
                        Business_Group_Name
                   end
                   ) as bv5_businessgroupname,
               (case Business_Group_ID
                    when @bv6 then
                        Business_Group_Name
                   end
                   ) as bv6_businessgroupname,
               (case Business_Group_ID
                    when @bv7 then
                        Business_Group_Name
                   end
                   ) as bv7_businessgroupname,
               (case Business_Group_ID
                    when @bv8 then
                        Business_Group_Name
                   end
                   ) as bv8_businessgroupname,
               (case Business_Group_ID
                    when @bv9 then
                        Business_Group_Name
                   end
                   ) as bv9_businessgroupname,
               (case Business_Group_ID
                    when @bv10 then
                        Business_Group_Name
                   end
                   ) as bv10_businessgroupname,
               (case Business_Group_ID
                    when @bv11 then
                        Business_Group_Name
                   end
                   ) as bv11_businessgroupname,
               (case Business_Group_ID
                    when @bv12 then
                        Business_Group_Name
                   end
                   ) as bv12_businessgroupname,
               (case Business_Group_ID
                    when @bv13 then
                        Business_Group_Name
                   end
                   ) as bv13_businessgroupname,
               (case Business_Group_ID
                    when @bv14 then
                        Business_Group_Name
                   end
                   ) as bv14_businessgroupname,
               (case Business_Group_ID
                    when @bv15 then
                        Business_Group_Name
                   end
                   ) as bv15_businessgroupname,
               (case Business_Group_ID
                    when @bv16 then
                        Business_Group_Name
                   end
                   ) as bv16_businessgroupname,
               (case Business_Group_ID
                    when @bv17 then
                        Business_Group_Name
                   end
                   ) as bv17_businessgroupname,
               (case Business_Group_ID
                    when @bv18 then
                        Business_Group_Name
                   end
                   ) as bv18_businessgroupname,
               (case Business_Group_ID
                    when @bv19 then
                        Business_Group_Name
                   end
                   ) as bv19_businessgroupname,
               (case Business_Group_ID
                    when @bv20 then
                        Business_Group_Name
                   end
                   ) as bv20_businessgroupname,
               (case Business_Group_ID
                    when @bv21 then
                        Business_Group_Name
                   end
                   ) as bv21_businessgroupname,
               (case Business_Group_ID
                    when @bv22 then
                        Business_Group_Name
                   end
                   ) as bv22_businessgroupname,
               (case Business_Group_ID
                    when @bv23 then
                        Business_Group_Name
                   end
                   ) as bv23_businessgroupname,
               (case Business_Group_ID
                    when @bv24 then
                        Business_Group_Name
                   end
                   ) as bv24_businessgroupname,
               (case Business_Group_ID
                    when @bv25 then
                        Business_Group_Name
                   end
                   ) as bv25_businessgroupname,
               (case Business_Group_ID
                    when @bv26 then
                        Business_Group_Name
                   end
                   ) as bv26_businessgroupname,
               (case Business_Group_ID
                    when @bv27 then
                        Business_Group_Name
                   end
                   ) as bv27_businessgroupname,
               (case Business_Group_ID
                    when @bv28 then
                        Business_Group_Name
                   end
                   ) as bv28_businessgroupname,
               (case Business_Group_ID
                    when @bv29 then
                        Business_Group_Name
                   end
                   ) as bv29_businessgroupname,
               (case Business_Group_ID
                    when @bv30 then
                        Business_Group_Name
                   end
                   ) as bv30_businessgroupname,
               (case Business_Group_ID
                    when @bv31 then
                        Business_Group_Name
                   end
                   ) as bv31_businessgroupname,
               (case Business_Group_ID
                    when @bv32 then
                        Business_Group_Name
                   end
                   ) as bv32_businessgroupname,
               (case Business_Group_ID
                    when @bv33 then
                        Business_Group_Name
                   end
                   ) as bv33_businessgroupname,
               (case Business_Group_ID
                    when @bv34 then
                        Business_Group_Name
                   end
                   ) as bv34_businessgroupname,
               (case Business_Group_ID
                    when @bv35 then
                        Business_Group_Name
                   end
                   ) as bv35_businessgroupname,
               (case Business_Group_ID
                    when @bv36 then
                        Business_Group_Name
                   end
                   ) as bv36_businessgroupname,
               (case Business_Group_ID
                    when @bv37 then
                        Business_Group_Name
                   end
                   ) as bv37_businessgroupname,
               (case Business_Group_ID
                    when @bv38 then
                        Business_Group_Name
                   end
                   ) as bv38_businessgroupname,
               (case Business_Group_ID
                    when @bv39 then
                        Business_Group_Name
                   end
                   ) as bv39_businessgroupname,
               (case Business_Group_ID
                    when @bv40 then
                        Business_Group_Name
                   end
                   ) as bv40_businessgroupname,
               (case Business_Group_ID
                    when @bv41 then
                        Business_Group_Name
                   end
                   ) as bv41_businessgroupname,
               (case Business_Group_ID
                    when @bv42 then
                        Business_Group_Name
                   end
                   ) as bv42_businessgroupname,
               (case Business_Group_ID
                    when @bv43 then
                        Business_Group_Name
                   end
                   ) as bv43_businessgroupname,
               (case Business_Group_ID
                    when @bv44 then
                        Business_Group_Name
                   end
                   ) as bv44_businessgroupname,
               (case Business_Group_ID
                    when @bv45 then
                        Business_Group_Name
                   end
                   ) as bv45_businessgroupname,
               (case Business_Group_ID
                    when @bv46 then
                        Business_Group_Name
                   end
                   ) as bv46_businessgroupname,
               (case Business_Group_ID
                    when @bv47 then
                        Business_Group_Name
                   end
                   ) as bv47_businessgroupname,
               (case Business_Group_ID
                    when @bv48 then
                        Business_Group_Name
                   end
                   ) as bv48_businessgroupname,
               (case Business_Group_ID
                    when @bv49 then
                        Business_Group_Name
                   end
                   ) as bv49_businessgroupname,
               (case Business_Group_ID
                    when @bv50 then
                        Business_Group_Name
                   end
                   ) as bv50_businessgroupname,
               (case Business_Group_ID
                    when @bv1 then
                        roomsoldcurrent
                   end
                   ) as bv1_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        roomssoldpickup
                   end
                   ) as bv1_roomssoldpickup,
               (case Business_Group_ID
                    when @bv2 then
                        roomsoldcurrent
                   end
                   ) as bv2_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        roomssoldpickup
                   end
                   ) as bv2_roomssoldpickup,
               (case Business_Group_ID
                    when @bv3 then
                        roomsoldcurrent
                   end
                   ) as bv3_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        roomssoldpickup
                   end
                   ) as bv3_roomssoldpickup,
               (case Business_Group_ID
                    when @bv4 then
                        roomsoldcurrent
                   end
                   ) as bv4_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        roomssoldpickup
                   end
                   ) as bv4_roomssoldpickup,
               (case Business_Group_ID
                    when @bv5 then
                        roomsoldcurrent
                   end
                   ) as bv5_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        roomssoldpickup
                   end
                   ) as bv5_roomssoldpickup,
               (case Business_Group_ID
                    when @bv6 then
                        roomsoldcurrent
                   end
                   ) as bv6_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        roomssoldpickup
                   end
                   ) as bv6_roomssoldpickup,
               (case Business_Group_ID
                    when @bv7 then
                        roomsoldcurrent
                   end
                   ) as bv7_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        roomssoldpickup
                   end
                   ) as bv7_roomssoldpickup,
               (case Business_Group_ID
                    when @bv8 then
                        roomsoldcurrent
                   end
                   ) as bv8_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        roomssoldpickup
                   end
                   ) as bv8_roomssoldpickup,
               (case Business_Group_ID
                    when @bv9 then
                        roomsoldcurrent
                   end
                   ) as bv9_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        roomssoldpickup
                   end
                   ) as bv9_roomssoldpickup,
               (case Business_Group_ID
                    when @bv10 then
                        roomsoldcurrent
                   end
                   ) as bv10_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        roomssoldpickup
                   end
                   ) as bv10_roomssoldpickup,
               (case Business_Group_ID
                    when @bv11 then
                        roomsoldcurrent
                   end
                   ) as bv11_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        roomssoldpickup
                   end
                   ) as bv11_roomssoldpickup,
               (case Business_Group_ID
                    when @bv12 then
                        roomsoldcurrent
                   end
                   ) as bv12_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        roomssoldpickup
                   end
                   ) as bv12_roomssoldpickup,
               (case Business_Group_ID
                    when @bv13 then
                        roomsoldcurrent
                   end
                   ) as bv13_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        roomssoldpickup
                   end
                   ) as bv13_roomssoldpickup,
               (case Business_Group_ID
                    when @bv14 then
                        roomsoldcurrent
                   end
                   ) as bv14_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        roomssoldpickup
                   end
                   ) as bv14_roomssoldpickup,
               (case Business_Group_ID
                    when @bv15 then
                        roomsoldcurrent
                   end
                   ) as bv15_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        roomssoldpickup
                   end
                   ) as bv15_roomssoldpickup,
               (case Business_Group_ID
                    when @bv16 then
                        roomsoldcurrent
                   end
                   ) as bv16_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        roomssoldpickup
                   end
                   ) as bv16_roomssoldpickup,
               (case Business_Group_ID
                    when @bv17 then
                        roomsoldcurrent
                   end
                   ) as bv17_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        roomssoldpickup
                   end
                   ) as bv17_roomssoldpickup,
               (case Business_Group_ID
                    when @bv18 then
                        roomsoldcurrent
                   end
                   ) as bv18_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        roomssoldpickup
                   end
                   ) as bv18_roomssoldpickup,
               (case Business_Group_ID
                    when @bv19 then
                        roomsoldcurrent
                   end
                   ) as bv19_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        roomssoldpickup
                   end
                   ) as bv19_roomssoldpickup,
               (case Business_Group_ID
                    when @bv20 then
                        roomsoldcurrent
                   end
                   ) as bv20_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        roomssoldpickup
                   end
                   ) as bv20_roomssoldpickup,
               (case Business_Group_ID
                    when @bv21 then
                        roomsoldcurrent
                   end
                   ) as bv21_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        roomssoldpickup
                   end
                   ) as bv21_roomssoldpickup,
               (case Business_Group_ID
                    when @bv22 then
                        roomsoldcurrent
                   end
                   ) as bv22_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        roomssoldpickup
                   end
                   ) as bv22_roomssoldpickup,
               (case Business_Group_ID
                    when @bv23 then
                        roomsoldcurrent
                   end
                   ) as bv23_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        roomssoldpickup
                   end
                   ) as bv23_roomssoldpickup,
               (case Business_Group_ID
                    when @bv24 then
                        roomsoldcurrent
                   end
                   ) as bv24_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        roomssoldpickup
                   end
                   ) as bv24_roomssoldpickup,
               (case Business_Group_ID
                    when @bv25 then
                        roomsoldcurrent
                   end
                   ) as bv25_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        roomssoldpickup
                   end
                   ) as bv25_roomssoldpickup,
               (case Business_Group_ID
                    when @bv26 then
                        roomsoldcurrent
                   end
                   ) as bv26_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        roomssoldpickup
                   end
                   ) as bv26_roomssoldpickup,
               (case Business_Group_ID
                    when @bv27 then
                        roomsoldcurrent
                   end
                   ) as bv27_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        roomssoldpickup
                   end
                   ) as bv27_roomssoldpickup,
               (case Business_Group_ID
                    when @bv28 then
                        roomsoldcurrent
                   end
                   ) as bv28_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        roomssoldpickup
                   end
                   ) as bv28_roomssoldpickup,
               (case Business_Group_ID
                    when @bv29 then
                        roomsoldcurrent
                   end
                   ) as bv29_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        roomssoldpickup
                   end
                   ) as bv29_roomssoldpickup,
               (case Business_Group_ID
                    when @bv30 then
                        roomsoldcurrent
                   end
                   ) as bv30_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        roomssoldpickup
                   end
                   ) as bv30_roomssoldpickup,
               (case Business_Group_ID
                    when @bv31 then
                        roomsoldcurrent
                   end
                   ) as bv31_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        roomssoldpickup
                   end
                   ) as bv31_roomssoldpickup,
               (case Business_Group_ID
                    when @bv32 then
                        roomsoldcurrent
                   end
                   ) as bv32_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        roomssoldpickup
                   end
                   ) as bv32_roomssoldpickup,
               (case Business_Group_ID
                    when @bv33 then
                        roomsoldcurrent
                   end
                   ) as bv33_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        roomssoldpickup
                   end
                   ) as bv33_roomssoldpickup,
               (case Business_Group_ID
                    when @bv34 then
                        roomsoldcurrent
                   end
                   ) as bv34_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        roomssoldpickup
                   end
                   ) as bv34_roomssoldpickup,
               (case Business_Group_ID
                    when @bv35 then
                        roomsoldcurrent
                   end
                   ) as bv35_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        roomssoldpickup
                   end
                   ) as bv35_roomssoldpickup,
               (case Business_Group_ID
                    when @bv36 then
                        roomsoldcurrent
                   end
                   ) as bv36_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        roomssoldpickup
                   end
                   ) as bv36_roomssoldpickup,
               (case Business_Group_ID
                    when @bv37 then
                        roomsoldcurrent
                   end
                   ) as bv37_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        roomssoldpickup
                   end
                   ) as bv37_roomssoldpickup,
               (case Business_Group_ID
                    when @bv38 then
                        roomsoldcurrent
                   end
                   ) as bv38_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        roomssoldpickup
                   end
                   ) as bv38_roomssoldpickup,
               (case Business_Group_ID
                    when @bv39 then
                        roomsoldcurrent
                   end
                   ) as bv39_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        roomssoldpickup
                   end
                   ) as bv39_roomssoldpickup,
               (case Business_Group_ID
                    when @bv40 then
                        roomsoldcurrent
                   end
                   ) as bv40_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        roomssoldpickup
                   end
                   ) as bv40_roomssoldpickup,
               (case Business_Group_ID
                    when @bv41 then
                        roomsoldcurrent
                   end
                   ) as bv41_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        roomssoldpickup
                   end
                   ) as bv41_roomssoldpickup,
               (case Business_Group_ID
                    when @bv42 then
                        roomsoldcurrent
                   end
                   ) as bv42_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        roomssoldpickup
                   end
                   ) as bv42_roomssoldpickup,
               (case Business_Group_ID
                    when @bv43 then
                        roomsoldcurrent
                   end
                   ) as bv43_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        roomssoldpickup
                   end
                   ) as bv43_roomssoldpickup,
               (case Business_Group_ID
                    when @bv44 then
                        roomsoldcurrent
                   end
                   ) as bv44_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        roomssoldpickup
                   end
                   ) as bv44_roomssoldpickup,
               (case Business_Group_ID
                    when @bv45 then
                        roomsoldcurrent
                   end
                   ) as bv45_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        roomssoldpickup
                   end
                   ) as bv45_roomssoldpickup,
               (case Business_Group_ID
                    when @bv46 then
                        roomsoldcurrent
                   end
                   ) as bv46_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        roomssoldpickup
                   end
                   ) as bv46_roomssoldpickup,
               (case Business_Group_ID
                    when @bv47 then
                        roomsoldcurrent
                   end
                   ) as bv47_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        roomssoldpickup
                   end
                   ) as bv47_roomssoldpickup,
               (case Business_Group_ID
                    when @bv48 then
                        roomsoldcurrent
                   end
                   ) as bv48_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        roomssoldpickup
                   end
                   ) as bv48_roomssoldpickup,
               (case Business_Group_ID
                    when @bv49 then
                        roomsoldcurrent
                   end
                   ) as bv49_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        roomssoldpickup
                   end
                   ) as bv49_roomssoldpickup,
               (case Business_Group_ID
                    when @bv50 then
                        roomsoldcurrent
                   end
                   ) as bv50_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        roomssoldpickup
                   end
                   ) as bv50_roomssoldpickup,
               (case Business_Group_ID
                    when @bv1 then
                        occfcstcurrent
                   end
                   ) as bv1_occfcstcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        occfcstpickup
                   end
                   ) as bv1_occfcstpickup,
               (case Business_Group_ID
                    when @bv2 then
                        occfcstcurrent
                   end
                   ) as bv2_occfcstcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        occfcstpickup
                   end
                   ) as bv2_occfcstpickup,
               (case Business_Group_ID
                    when @bv3 then
                        occfcstcurrent
                   end
                   ) as bv3_occfcstcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        occfcstpickup
                   end
                   ) as bv3_occfcstpickup,
               (case Business_Group_ID
                    when @bv4 then
                        occfcstcurrent
                   end
                   ) as bv4_occfcstcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        occfcstpickup
                   end
                   ) as bv4_occfcstpickup,
               (case Business_Group_ID
                    when @bv5 then
                        occfcstcurrent
                   end
                   ) as bv5_occfcstcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        occfcstpickup
                   end
                   ) as bv5_occfcstpickup,
               (case Business_Group_ID
                    when @bv6 then
                        occfcstcurrent
                   end
                   ) as bv6_occfcstcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        occfcstpickup
                   end
                   ) as bv6_occfcstpickup,
               (case Business_Group_ID
                    when @bv7 then
                        occfcstcurrent
                   end
                   ) as bv7_occfcstcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        occfcstpickup
                   end
                   ) as bv7_occfcstpickup,
               (case Business_Group_ID
                    when @bv8 then
                        occfcstcurrent
                   end
                   ) as bv8_occfcstcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        occfcstpickup
                   end
                   ) as bv8_occfcstpickup,
               (case Business_Group_ID
                    when @bv9 then
                        occfcstcurrent
                   end
                   ) as bv9_occfcstcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        occfcstpickup
                   end
                   ) as bv9_occfcstpickup,
               (case Business_Group_ID
                    when @bv10 then
                        occfcstcurrent
                   end
                   ) as bv10_occfcstcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        occfcstpickup
                   end
                   ) as bv10_occfcstpickup,
               (case Business_Group_ID
                    when @bv11 then
                        occfcstcurrent
                   end
                   ) as bv11_occfcstcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        occfcstpickup
                   end
                   ) as bv11_occfcstpickup,
               (case Business_Group_ID
                    when @bv12 then
                        occfcstcurrent
                   end
                   ) as bv12_occfcstcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        occfcstpickup
                   end
                   ) as bv12_occfcstpickup,
               (case Business_Group_ID
                    when @bv13 then
                        occfcstcurrent
                   end
                   ) as bv13_occfcstcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        occfcstpickup
                   end
                   ) as bv13_occfcstpickup,
               (case Business_Group_ID
                    when @bv14 then
                        occfcstcurrent
                   end
                   ) as bv14_occfcstcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        occfcstpickup
                   end
                   ) as bv14_occfcstpickup,
               (case Business_Group_ID
                    when @bv15 then
                        occfcstcurrent
                   end
                   ) as bv15_occfcstcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        occfcstpickup
                   end
                   ) as bv15_occfcstpickup,
               (case Business_Group_ID
                    when @bv16 then
                        occfcstcurrent
                   end
                   ) as bv16_occfcstcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        occfcstpickup
                   end
                   ) as bv16_occfcstpickup,
               (case Business_Group_ID
                    when @bv17 then
                        occfcstcurrent
                   end
                   ) as bv17_occfcstcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        occfcstpickup
                   end
                   ) as bv17_occfcstpickup,
               (case Business_Group_ID
                    when @bv18 then
                        occfcstcurrent
                   end
                   ) as bv18_occfcstcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        occfcstpickup
                   end
                   ) as bv18_occfcstpickup,
               (case Business_Group_ID
                    when @bv19 then
                        occfcstcurrent
                   end
                   ) as bv19_occfcstcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        occfcstpickup
                   end
                   ) as bv19_occfcstpickup,
               (case Business_Group_ID
                    when @bv20 then
                        occfcstcurrent
                   end
                   ) as bv20_occfcstcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        occfcstpickup
                   end
                   ) as bv20_occfcstpickup,
               (case Business_Group_ID
                    when @bv21 then
                        occfcstcurrent
                   end
                   ) as bv21_occfcstcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        occfcstpickup
                   end
                   ) as bv21_occfcstpickup,
               (case Business_Group_ID
                    when @bv22 then
                        occfcstcurrent
                   end
                   ) as bv22_occfcstcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        occfcstpickup
                   end
                   ) as bv22_occfcstpickup,
               (case Business_Group_ID
                    when @bv23 then
                        occfcstcurrent
                   end
                   ) as bv23_occfcstcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        occfcstpickup
                   end
                   ) as bv23_occfcstpickup,
               (case Business_Group_ID
                    when @bv24 then
                        occfcstcurrent
                   end
                   ) as bv24_occfcstcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        occfcstpickup
                   end
                   ) as bv24_occfcstpickup,
               (case Business_Group_ID
                    when @bv25 then
                        occfcstcurrent
                   end
                   ) as bv25_occfcstcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        occfcstpickup
                   end
                   ) as bv25_occfcstpickup,
               (case Business_Group_ID
                    when @bv26 then
                        occfcstcurrent
                   end
                   ) as bv26_occfcstcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        occfcstpickup
                   end
                   ) as bv26_occfcstpickup,
               (case Business_Group_ID
                    when @bv27 then
                        occfcstcurrent
                   end
                   ) as bv27_occfcstcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        occfcstpickup
                   end
                   ) as bv27_occfcstpickup,
               (case Business_Group_ID
                    when @bv28 then
                        occfcstcurrent
                   end
                   ) as bv28_occfcstcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        occfcstpickup
                   end
                   ) as bv28_occfcstpickup,
               (case Business_Group_ID
                    when @bv29 then
                        occfcstcurrent
                   end
                   ) as bv29_occfcstcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        occfcstpickup
                   end
                   ) as bv29_occfcstpickup,
               (case Business_Group_ID
                    when @bv30 then
                        occfcstcurrent
                   end
                   ) as bv30_occfcstcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        occfcstpickup
                   end
                   ) as bv30_occfcstpickup,
               (case Business_Group_ID
                    when @bv31 then
                        occfcstcurrent
                   end
                   ) as bv31_occfcstcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        occfcstpickup
                   end
                   ) as bv31_occfcstpickup,
               (case Business_Group_ID
                    when @bv32 then
                        occfcstcurrent
                   end
                   ) as bv32_occfcstcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        occfcstpickup
                   end
                   ) as bv32_occfcstpickup,
               (case Business_Group_ID
                    when @bv33 then
                        occfcstcurrent
                   end
                   ) as bv33_occfcstcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        occfcstpickup
                   end
                   ) as bv33_occfcstpickup,
               (case Business_Group_ID
                    when @bv34 then
                        occfcstcurrent
                   end
                   ) as bv34_occfcstcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        occfcstpickup
                   end
                   ) as bv34_occfcstpickup,
               (case Business_Group_ID
                    when @bv35 then
                        occfcstcurrent
                   end
                   ) as bv35_occfcstcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        occfcstpickup
                   end
                   ) as bv35_occfcstpickup,
               (case Business_Group_ID
                    when @bv36 then
                        occfcstcurrent
                   end
                   ) as bv36_occfcstcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        occfcstpickup
                   end
                   ) as bv36_occfcstpickup,
               (case Business_Group_ID
                    when @bv37 then
                        occfcstcurrent
                   end
                   ) as bv37_occfcstcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        occfcstpickup
                   end
                   ) as bv37_occfcstpickup,
               (case Business_Group_ID
                    when @bv38 then
                        occfcstcurrent
                   end
                   ) as bv38_occfcstcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        occfcstpickup
                   end
                   ) as bv38_occfcstpickup,
               (case Business_Group_ID
                    when @bv39 then
                        occfcstcurrent
                   end
                   ) as bv39_occfcstcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        occfcstpickup
                   end
                   ) as bv39_occfcstpickup,
               (case Business_Group_ID
                    when @bv40 then
                        occfcstcurrent
                   end
                   ) as bv40_occfcstcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        occfcstpickup
                   end
                   ) as bv40_occfcstpickup,
               (case Business_Group_ID
                    when @bv41 then
                        occfcstcurrent
                   end
                   ) as bv41_occfcstcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        occfcstpickup
                   end
                   ) as bv41_occfcstpickup,
               (case Business_Group_ID
                    when @bv42 then
                        occfcstcurrent
                   end
                   ) as bv42_occfcstcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        occfcstpickup
                   end
                   ) as bv42_occfcstpickup,
               (case Business_Group_ID
                    when @bv43 then
                        occfcstcurrent
                   end
                   ) as bv43_occfcstcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        occfcstpickup
                   end
                   ) as bv43_occfcstpickup,
               (case Business_Group_ID
                    when @bv44 then
                        occfcstcurrent
                   end
                   ) as bv44_occfcstcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        occfcstpickup
                   end
                   ) as bv44_occfcstpickup,
               (case Business_Group_ID
                    when @bv45 then
                        occfcstcurrent
                   end
                   ) as bv45_occfcstcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        occfcstpickup
                   end
                   ) as bv45_occfcstpickup,
               (case Business_Group_ID
                    when @bv46 then
                        occfcstcurrent
                   end
                   ) as bv46_occfcstcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        occfcstpickup
                   end
                   ) as bv46_occfcstpickup,
               (case Business_Group_ID
                    when @bv47 then
                        occfcstcurrent
                   end
                   ) as bv47_occfcstcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        occfcstpickup
                   end
                   ) as bv47_occfcstpickup,
               (case Business_Group_ID
                    when @bv48 then
                        occfcstcurrent
                   end
                   ) as bv48_occfcstcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        occfcstpickup
                   end
                   ) as bv48_occfcstpickup,
               (case Business_Group_ID
                    when @bv49 then
                        occfcstcurrent
                   end
                   ) as bv49_occfcstcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        occfcstpickup
                   end
                   ) as bv49_occfcstpickup,
               (case Business_Group_ID
                    when @bv50 then
                        occfcstcurrent
                   end
                   ) as bv50_occfcstcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        occfcstpickup
                   end
                   ) as bv50_occfcstpickup,
               (case Business_Group_ID
                    when @bv1 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv1_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv1 then
                        bookedroomrevenuepickup
                   end
                   ) as bv1_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv2 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv2_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv2 then
                        bookedroomrevenuepickup
                   end
                   ) as bv2_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv3 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv3_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv3 then
                        bookedroomrevenuepickup
                   end
                   ) as bv3_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv4 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv4_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv4 then
                        bookedroomrevenuepickup
                   end
                   ) as bv4_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv5 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv5_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv5 then
                        bookedroomrevenuepickup
                   end
                   ) as bv5_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv6 then
                        bookedroomrevenuepickup
                   end
                   ) as bv6_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv6 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv6_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv7_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedroomrevenuepickup
                   end
                   ) as bv7_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv8 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv8_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv8 then
                        bookedroomrevenuepickup
                   end
                   ) as bv8_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv9 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv9_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv9 then
                        bookedroomrevenuepickup
                   end
                   ) as bv9_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv10 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv10_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv10 then
                        bookedroomrevenuepickup
                   end
                   ) as bv10_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv11 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv11_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv11 then
                        bookedroomrevenuepickup
                   end
                   ) as bv11_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv12 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv12_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv12 then
                        bookedroomrevenuepickup
                   end
                   ) as bv12_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv13 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv13_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv13 then
                        bookedroomrevenuepickup
                   end
                   ) as bv13_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv14 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv14_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv14 then
                        bookedroomrevenuepickup
                   end
                   ) as bv14_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv15 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv15_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv15 then
                        bookedroomrevenuepickup
                   end
                   ) as bv15_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv16 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv16_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv16 then
                        bookedroomrevenuepickup
                   end
                   ) as bv16_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv17 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv17_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv17 then
                        bookedroomrevenuepickup
                   end
                   ) as bv17_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv18 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv18_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv18 then
                        bookedroomrevenuepickup
                   end
                   ) as bv18_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv19 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv19_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv19 then
                        bookedroomrevenuepickup
                   end
                   ) as bv19_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv20 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv20_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv20 then
                        bookedroomrevenuepickup
                   end
                   ) as bv20_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv21 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv21_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv21 then
                        bookedroomrevenuepickup
                   end
                   ) as bv21_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv22 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv22_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv22 then
                        bookedroomrevenuepickup
                   end
                   ) as bv22_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv23 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv23_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv23 then
                        bookedroomrevenuepickup
                   end
                   ) as bv23_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv24 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv24_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv24 then
                        bookedroomrevenuepickup
                   end
                   ) as bv24_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv25 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv25_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv25 then
                        bookedroomrevenuepickup
                   end
                   ) as bv25_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv26 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv26_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv26 then
                        bookedroomrevenuepickup
                   end
                   ) as bv26_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv27 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv27_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv27 then
                        bookedroomrevenuepickup
                   end
                   ) as bv27_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv28 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv28_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv28 then
                        bookedroomrevenuepickup
                   end
                   ) as bv28_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv29 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv29_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv29 then
                        bookedroomrevenuepickup
                   end
                   ) as bv29_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv30 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv30_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv30 then
                        bookedroomrevenuepickup
                   end
                   ) as bv30_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv31 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv31_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv31 then
                        bookedroomrevenuepickup
                   end
                   ) as bv31_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv32 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv32_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv32 then
                        bookedroomrevenuepickup
                   end
                   ) as bv32_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv33 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv33_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv33 then
                        bookedroomrevenuepickup
                   end
                   ) as bv33_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv34 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv34_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv34 then
                        bookedroomrevenuepickup
                   end
                   ) as bv34_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv35 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv35_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv35 then
                        bookedroomrevenuepickup
                   end
                   ) as bv35_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv36 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv36_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv36 then
                        bookedroomrevenuepickup
                   end
                   ) as bv36_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv37 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv37_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv37 then
                        bookedroomrevenuepickup
                   end
                   ) as bv37_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv38 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv38_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv38 then
                        bookedroomrevenuepickup
                   end
                   ) as bv38_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv39 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv39_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv39 then
                        bookedroomrevenuepickup
                   end
                   ) as bv39_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv40 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv40_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv40 then
                        bookedroomrevenuepickup
                   end
                   ) as bv40_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv41 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv41_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv41 then
                        bookedroomrevenuepickup
                   end
                   ) as bv41_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv42 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv42_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv42 then
                        bookedroomrevenuepickup
                   end
                   ) as bv42_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv43 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv43_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv43 then
                        bookedroomrevenuepickup
                   end
                   ) as bv43_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv44 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv44_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv44 then
                        bookedroomrevenuepickup
                   end
                   ) as bv44_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv45 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv45_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv45 then
                        bookedroomrevenuepickup
                   end
                   ) as bv45_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv46 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv46_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv46 then
                        bookedroomrevenuepickup
                   end
                   ) as bv46_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv47 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv47_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv47 then
                        bookedroomrevenuepickup
                   end
                   ) as bv47_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv48 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv48_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv48 then
                        bookedroomrevenuepickup
                   end
                   ) as bv48_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv49 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv49_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv49 then
                        bookedroomrevenuepickup
                   end
                   ) as bv49_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv50 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv50_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv50 then
                        bookedroomrevenuepickup
                   end
                   ) as bv50_bookedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv1_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv1_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv2_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv2_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv3_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv3_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv4_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv4_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv5_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv5_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv6_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv6_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv7_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv7_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv8_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv8_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv9_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv9_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv10_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv10_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv11_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv11_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv12_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv12_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv13_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv13_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv14_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv14_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv15_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv15_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv16_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv16_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv17_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv17_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv18_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv18_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv19_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv19_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv20_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv20_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv21_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv21_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv22_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv22_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv23_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv23_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv24_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv24_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv25_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv25_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv26_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv26_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv27_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv27_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv28_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv28_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv29_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv29_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv30_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv30_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv31_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv31_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv32_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv32_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv33_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv33_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv34_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv34_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv35_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv35_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv36_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv36_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv37_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv37_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv38_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv38_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv39_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv39_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv40_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv40_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv41_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv41_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv42_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv42_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv43_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv43_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv44_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv44_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv45_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv45_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv46_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv46_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv47_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv47_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv48_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv48_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv49_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv49_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv50_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedroomrevenuepickup
                   end
                   ) as bv50_fcstedroomrevenuepickup,
               (case Business_Group_ID
                    when @bv1 then
                        bookedadrcurrent
                   end
                   ) as bv1_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        bookedadrpickup
                   end
                   ) as bv1_bookedadrpickup,
               (case Business_Group_ID
                    when @bv2 then
                        bookedadrcurrent
                   end
                   ) as bv2_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        bookedadrpickup
                   end
                   ) as bv2_bookedadrpickup,
               (case Business_Group_ID
                    when @bv3 then
                        bookedadrcurrent
                   end
                   ) as bv3_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        bookedadrpickup
                   end
                   ) as bv3_bookedadrpickup,
               (case Business_Group_ID
                    when @bv4 then
                        bookedadrcurrent
                   end
                   ) as bv4_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        bookedadrpickup
                   end
                   ) as bv4_bookedadrpickup,
               (case Business_Group_ID
                    when @bv5 then
                        bookedadrcurrent
                   end
                   ) as bv5_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        bookedadrpickup
                   end
                   ) as bv5_bookedadrpickup,
               (case Business_Group_ID
                    when @bv6 then
                        bookedadrcurrent
                   end
                   ) as bv6_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        bookedadrpickup
                   end
                   ) as bv6_bookedadrpickup,
               (case Business_Group_ID
                    when @bv7 then
                        bookedadrcurrent
                   end
                   ) as bv7_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedadrpickup
                   end
                   ) as bv7_bookedadrpickup,
               (case Business_Group_ID
                    when @bv8 then
                        bookedadrcurrent
                   end
                   ) as bv8_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        bookedadrpickup
                   end
                   ) as bv8_bookedadrpickup,
               (case Business_Group_ID
                    when @bv9 then
                        bookedadrcurrent
                   end
                   ) as bv9_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        bookedadrpickup
                   end
                   ) as bv9_bookedadrpickup,
               (case Business_Group_ID
                    when @bv10 then
                        bookedadrcurrent
                   end
                   ) as bv10_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        bookedadrpickup
                   end
                   ) as bv10_bookedadrpickup,
               (case Business_Group_ID
                    when @bv11 then
                        bookedadrcurrent
                   end
                   ) as bv11_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        bookedadrpickup
                   end
                   ) as bv11_bookedadrpickup,
               (case Business_Group_ID
                    when @bv12 then
                        bookedadrcurrent
                   end
                   ) as bv12_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        bookedadrpickup
                   end
                   ) as bv12_bookedadrpickup,
               (case Business_Group_ID
                    when @bv13 then
                        bookedadrcurrent
                   end
                   ) as bv13_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        bookedadrpickup
                   end
                   ) as bv13_bookedadrpickup,
               (case Business_Group_ID
                    when @bv14 then
                        bookedadrcurrent
                   end
                   ) as bv14_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        bookedadrpickup
                   end
                   ) as bv14_bookedadrpickup,
               (case Business_Group_ID
                    when @bv15 then
                        bookedadrcurrent
                   end
                   ) as bv15_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        bookedadrpickup
                   end
                   ) as bv15_bookedadrpickup,
               (case Business_Group_ID
                    when @bv16 then
                        bookedadrcurrent
                   end
                   ) as bv16_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        bookedadrpickup
                   end
                   ) as bv16_bookedadrpickup,
               (case Business_Group_ID
                    when @bv17 then
                        bookedadrcurrent
                   end
                   ) as bv17_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        bookedadrpickup
                   end
                   ) as bv17_bookedadrpickup,
               (case Business_Group_ID
                    when @bv18 then
                        bookedadrcurrent
                   end
                   ) as bv18_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        bookedadrpickup
                   end
                   ) as bv18_bookedadrpickup,
               (case Business_Group_ID
                    when @bv19 then
                        bookedadrcurrent
                   end
                   ) as bv19_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        bookedadrpickup
                   end
                   ) as bv19_bookedadrpickup,
               (case Business_Group_ID
                    when @bv20 then
                        bookedadrcurrent
                   end
                   ) as bv20_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        bookedadrpickup
                   end
                   ) as bv20_bookedadrpickup,
               (case Business_Group_ID
                    when @bv21 then
                        bookedadrcurrent
                   end
                   ) as bv21_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        bookedadrpickup
                   end
                   ) as bv21_bookedadrpickup,
               (case Business_Group_ID
                    when @bv22 then
                        bookedadrcurrent
                   end
                   ) as bv22_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        bookedadrpickup
                   end
                   ) as bv22_bookedadrpickup,
               (case Business_Group_ID
                    when @bv23 then
                        bookedadrcurrent
                   end
                   ) as bv23_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        bookedadrpickup
                   end
                   ) as bv23_bookedadrpickup,
               (case Business_Group_ID
                    when @bv24 then
                        bookedadrcurrent
                   end
                   ) as bv24_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        bookedadrpickup
                   end
                   ) as bv24_bookedadrpickup,
               (case Business_Group_ID
                    when @bv25 then
                        bookedadrcurrent
                   end
                   ) as bv25_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        bookedadrpickup
                   end
                   ) as bv25_bookedadrpickup,
               (case Business_Group_ID
                    when @bv26 then
                        bookedadrcurrent
                   end
                   ) as bv26_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        bookedadrpickup
                   end
                   ) as bv26_bookedadrpickup,
               (case Business_Group_ID
                    when @bv27 then
                        bookedadrcurrent
                   end
                   ) as bv27_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        bookedadrpickup
                   end
                   ) as bv27_bookedadrpickup,
               (case Business_Group_ID
                    when @bv28 then
                        bookedadrcurrent
                   end
                   ) as bv28_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        bookedadrpickup
                   end
                   ) as bv28_bookedadrpickup,
               (case Business_Group_ID
                    when @bv29 then
                        bookedadrcurrent
                   end
                   ) as bv29_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        bookedadrpickup
                   end
                   ) as bv29_bookedadrpickup,
               (case Business_Group_ID
                    when @bv30 then
                        bookedadrcurrent
                   end
                   ) as bv30_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        bookedadrpickup
                   end
                   ) as bv30_bookedadrpickup,
               (case Business_Group_ID
                    when @bv31 then
                        bookedadrcurrent
                   end
                   ) as bv31_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        bookedadrpickup
                   end
                   ) as bv31_bookedadrpickup,
               (case Business_Group_ID
                    when @bv32 then
                        bookedadrcurrent
                   end
                   ) as bv32_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        bookedadrpickup
                   end
                   ) as bv32_bookedadrpickup,
               (case Business_Group_ID
                    when @bv33 then
                        bookedadrcurrent
                   end
                   ) as bv33_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        bookedadrpickup
                   end
                   ) as bv33_bookedadrpickup,
               (case Business_Group_ID
                    when @bv34 then
                        bookedadrcurrent
                   end
                   ) as bv34_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        bookedadrpickup
                   end
                   ) as bv34_bookedadrpickup,
               (case Business_Group_ID
                    when @bv35 then
                        bookedadrcurrent
                   end
                   ) as bv35_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        bookedadrpickup
                   end
                   ) as bv35_bookedadrpickup,
               (case Business_Group_ID
                    when @bv36 then
                        bookedadrcurrent
                   end
                   ) as bv36_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        bookedadrpickup
                   end
                   ) as bv36_bookedadrpickup,
               (case Business_Group_ID
                    when @bv37 then
                        bookedadrcurrent
                   end
                   ) as bv37_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        bookedadrpickup
                   end
                   ) as bv37_bookedadrpickup,
               (case Business_Group_ID
                    when @bv38 then
                        bookedadrcurrent
                   end
                   ) as bv38_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        bookedadrpickup
                   end
                   ) as bv38_bookedadrpickup,
               (case Business_Group_ID
                    when @bv39 then
                        bookedadrcurrent
                   end
                   ) as bv39_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        bookedadrpickup
                   end
                   ) as bv39_bookedadrpickup,
               (case Business_Group_ID
                    when @bv40 then
                        bookedadrcurrent
                   end
                   ) as bv40_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        bookedadrpickup
                   end
                   ) as bv40_bookedadrpickup,
               (case Business_Group_ID
                    when @bv41 then
                        bookedadrcurrent
                   end
                   ) as bv41_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        bookedadrpickup
                   end
                   ) as bv41_bookedadrpickup,
               (case Business_Group_ID
                    when @bv42 then
                        bookedadrcurrent
                   end
                   ) as bv42_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        bookedadrpickup
                   end
                   ) as bv42_bookedadrpickup,
               (case Business_Group_ID
                    when @bv43 then
                        bookedadrcurrent
                   end
                   ) as bv43_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        bookedadrpickup
                   end
                   ) as bv43_bookedadrpickup,
               (case Business_Group_ID
                    when @bv44 then
                        bookedadrcurrent
                   end
                   ) as bv44_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        bookedadrpickup
                   end
                   ) as bv44_bookedadrpickup,
               (case Business_Group_ID
                    when @bv45 then
                        bookedadrcurrent
                   end
                   ) as bv45_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        bookedadrpickup
                   end
                   ) as bv45_bookedadrpickup,
               (case Business_Group_ID
                    when @bv46 then
                        bookedadrcurrent
                   end
                   ) as bv46_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        bookedadrpickup
                   end
                   ) as bv46_bookedadrpickup,
               (case Business_Group_ID
                    when @bv47 then
                        bookedadrcurrent
                   end
                   ) as bv47_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        bookedadrpickup
                   end
                   ) as bv47_bookedadrpickup,
               (case Business_Group_ID
                    when @bv48 then
                        bookedadrcurrent
                   end
                   ) as bv48_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        bookedadrpickup
                   end
                   ) as bv48_bookedadrpickup,
               (case Business_Group_ID
                    when @bv49 then
                        bookedadrcurrent
                   end
                   ) as bv49_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        bookedadrpickup
                   end
                   ) as bv49_bookedadrpickup,
               (case Business_Group_ID
                    when @bv50 then
                        bookedadrcurrent
                   end
                   ) as bv50_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        bookedadrpickup
                   end
                   ) as bv50_bookedadrpickup,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedadrcurrent
                   end
                   ) as bv1_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedadrpickup
                   end
                   ) as bv1_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedadrcurrent
                   end
                   ) as bv2_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedadrpickup
                   end
                   ) as bv2_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedadrcurrent
                   end
                   ) as bv3_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedadrpickup
                   end
                   ) as bv3_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedadrcurrent
                   end
                   ) as bv4_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedadrpickup
                   end
                   ) as bv4_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedadrcurrent
                   end
                   ) as bv5_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedadrpickup
                   end
                   ) as bv5_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedadrcurrent
                   end
                   ) as bv6_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedadrpickup
                   end
                   ) as bv6_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedadrcurrent
                   end
                   ) as bv7_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedadrpickup
                   end
                   ) as bv7_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedadrcurrent
                   end
                   ) as bv8_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedadrpickup
                   end
                   ) as bv8_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedadrcurrent
                   end
                   ) as bv9_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedadrpickup
                   end
                   ) as bv9_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedadrcurrent
                   end
                   ) as bv10_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedadrpickup
                   end
                   ) as bv10_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedadrcurrent
                   end
                   ) as bv11_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedadrpickup
                   end
                   ) as bv11_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedadrcurrent
                   end
                   ) as bv12_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedadrpickup
                   end
                   ) as bv12_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedadrcurrent
                   end
                   ) as bv13_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedadrpickup
                   end
                   ) as bv13_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedadrcurrent
                   end
                   ) as bv14_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedadrpickup
                   end
                   ) as bv14_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedadrcurrent
                   end
                   ) as bv15_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedadrpickup
                   end
                   ) as bv15_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedadrcurrent
                   end
                   ) as bv16_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedadrpickup
                   end
                   ) as bv16_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedadrcurrent
                   end
                   ) as bv17_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedadrpickup
                   end
                   ) as bv17_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedadrcurrent
                   end
                   ) as bv18_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedadrpickup
                   end
                   ) as bv18_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedadrcurrent
                   end
                   ) as bv19_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedadrpickup
                   end
                   ) as bv19_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedadrcurrent
                   end
                   ) as bv20_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedadrpickup
                   end
                   ) as bv20_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedadrcurrent
                   end
                   ) as bv21_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedadrpickup
                   end
                   ) as bv21_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedadrcurrent
                   end
                   ) as bv22_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedadrpickup
                   end
                   ) as bv22_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedadrcurrent
                   end
                   ) as bv23_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedadrpickup
                   end
                   ) as bv23_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedadrcurrent
                   end
                   ) as bv24_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedadrpickup
                   end
                   ) as bv24_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedadrcurrent
                   end
                   ) as bv25_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedadrpickup
                   end
                   ) as bv25_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedadrcurrent
                   end
                   ) as bv26_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedadrpickup
                   end
                   ) as bv26_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedadrcurrent
                   end
                   ) as bv27_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedadrpickup
                   end
                   ) as bv27_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedadrcurrent
                   end
                   ) as bv28_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedadrpickup
                   end
                   ) as bv28_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedadrcurrent
                   end
                   ) as bv29_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedadrpickup
                   end
                   ) as bv29_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedadrcurrent
                   end
                   ) as bv30_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedadrpickup
                   end
                   ) as bv30_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedadrcurrent
                   end
                   ) as bv31_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedadrpickup
                   end
                   ) as bv31_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedadrcurrent
                   end
                   ) as bv32_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedadrpickup
                   end
                   ) as bv32_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedadrcurrent
                   end
                   ) as bv33_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedadrpickup
                   end
                   ) as bv33_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedadrcurrent
                   end
                   ) as bv34_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedadrpickup
                   end
                   ) as bv34_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedadrcurrent
                   end
                   ) as bv35_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedadrpickup
                   end
                   ) as bv35_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedadrcurrent
                   end
                   ) as bv36_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedadrpickup
                   end
                   ) as bv36_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedadrcurrent
                   end
                   ) as bv37_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedadrpickup
                   end
                   ) as bv37_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedadrcurrent
                   end
                   ) as bv38_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedadrpickup
                   end
                   ) as bv38_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedadrcurrent
                   end
                   ) as bv39_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedadrpickup
                   end
                   ) as bv39_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedadrcurrent
                   end
                   ) as bv40_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedadrpickup
                   end
                   ) as bv40_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedadrcurrent
                   end
                   ) as bv41_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedadrpickup
                   end
                   ) as bv41_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedadrcurrent
                   end
                   ) as bv42_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedadrpickup
                   end
                   ) as bv42_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedadrcurrent
                   end
                   ) as bv43_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedadrpickup
                   end
                   ) as bv43_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedadrcurrent
                   end
                   ) as bv44_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedadrpickup
                   end
                   ) as bv44_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedadrcurrent
                   end
                   ) as bv45_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedadrpickup
                   end
                   ) as bv45_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedadrcurrent
                   end
                   ) as bv46_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedadrpickup
                   end
                   ) as bv46_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedadrcurrent
                   end
                   ) as bv47_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedadrpickup
                   end
                   ) as bv47_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedadrcurrent
                   end
                   ) as bv48_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedadrpickup
                   end
                   ) as bv48_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedadrcurrent
                   end
                   ) as bv49_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedadrpickup
                   end
                   ) as bv49_fcstedadrpickup,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedadrcurrent
                   end
                   ) as bv50_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedadrpickup
                   end
                   ) as bv50_fcstedadrpickup,
               --Archana added for group block and group pickup-Start
               (case Business_Group_ID
                    when @bv1 then
                        block
                   end
                   ) as bv1_block,
               (case Business_Group_ID
                    when @bv1 then
                        block_pickup
                   end
                   ) as bv1_block_pickup,
               (case Business_Group_ID
                    when @bv1 then
                        block_available
                   end
                   ) as bv1_block_available,
               (case Business_Group_ID
                    when @bv2 then
                        block
                   end
                   ) as bv2_block,
               (case Business_Group_ID
                    when @bv2 then
                        block_pickup
                   end
                   ) as bv2_block_pickup,
               (case Business_Group_ID
                    when @bv2 then
                        block_available
                   end
                   ) as bv2_block_available,
               (case Business_Group_ID
                    when @bv3 then
                        block
                   end
                   ) as bv3_block,
               (case Business_Group_ID
                    when @bv3 then
                        block_pickup
                   end
                   ) as bv3_block_pickup,
               (case Business_Group_ID
                    when @bv3 then
                        block_available
                   end
                   ) as bv3_block_available,
               (case Business_Group_ID
                    when @bv4 then
                        block
                   end
                   ) as bv4_block,
               (case Business_Group_ID
                    when @bv4 then
                        block_pickup
                   end
                   ) as bv4_block_pickup,
               (case Business_Group_ID
                    when @bv4 then
                        block_available
                   end
                   ) as bv4_block_available,
               (case Business_Group_ID
                    when @bv5 then
                        block
                   end
                   ) as bv5_block,
               (case Business_Group_ID
                    when @bv5 then
                        block_pickup
                   end
                   ) as bv5_block_pickup,
               (case Business_Group_ID
                    when @bv5 then
                        block_available
                   end
                   ) as bv5_block_available,
               (case Business_Group_ID
                    when @bv6 then
                        block
                   end
                   ) as bv6_block,
               (case Business_Group_ID
                    when @bv6 then
                        block_pickup
                   end
                   ) as bv6_block_pickup,
               (case Business_Group_ID
                    when @bv6 then
                        block_available
                   end
                   ) as bv6_block_available,
               (case Business_Group_ID
                    when @bv7 then
                        block
                   end
                   ) as bv7_block,
               (case Business_Group_ID
                    when @bv7 then
                        block_pickup
                   end
                   ) as bv7_block_pickup,
               (case Business_Group_ID
                    when @bv7 then
                        block_available
                   end
                   ) as bv7_block_available,
               (case Business_Group_ID
                    when @bv8 then
                        block
                   end
                   ) as bv8_block,
               (case Business_Group_ID
                    when @bv8 then
                        block_pickup
                   end
                   ) as bv8_block_pickup,
               (case Business_Group_ID
                    when @bv8 then
                        block_available
                   end
                   ) as bv8_block_available,
               (case Business_Group_ID
                    when @bv9 then
                        block
                   end
                   ) as bv9_block,
               (case Business_Group_ID
                    when @bv9 then
                        block_pickup
                   end
                   ) as bv9_block_pickup,
               (case Business_Group_ID
                    when @bv9 then
                        block_available
                   end
                   ) as bv9_block_available,
               (case Business_Group_ID
                    when @bv10 then
                        block
                   end
                   ) as bv10_block,
               (case Business_Group_ID
                    when @bv10 then
                        block_pickup
                   end
                   ) as bv10_block_pickup,
               (case Business_Group_ID
                    when @bv10 then
                        block_available
                   end
                   ) as bv10_block_available,
               (case Business_Group_ID
                    when @bv11 then
                        block
                   end
                   ) as bv11_block,
               (case Business_Group_ID
                    when @bv11 then
                        block_pickup
                   end
                   ) as bv11_block_pickup,
               (case Business_Group_ID
                    when @bv11 then
                        block_available
                   end
                   ) as bv11_block_available,
               (case Business_Group_ID
                    when @bv12 then
                        block
                   end
                   ) as bv12_block,
               (case Business_Group_ID
                    when @bv12 then
                        block_pickup
                   end
                   ) as bv12_block_pickup,
               (case Business_Group_ID
                    when @bv12 then
                        block_available
                   end
                   ) as bv12_block_available,
               (case Business_Group_ID
                    when @bv13 then
                        block
                   end
                   ) as bv13_block,
               (case Business_Group_ID
                    when @bv13 then
                        block_pickup
                   end
                   ) as bv13_block_pickup,
               (case Business_Group_ID
                    when @bv13 then
                        block_available
                   end
                   ) as bv13_block_available,
               (case Business_Group_ID
                    when @bv14 then
                        block
                   end
                   ) as bv14_block,
               (case Business_Group_ID
                    when @bv14 then
                        block_pickup
                   end
                   ) as bv14_block_pickup,
               (case Business_Group_ID
                    when @bv14 then
                        block_available
                   end
                   ) as bv14_block_available,
               (case Business_Group_ID
                    when @bv15 then
                        block
                   end
                   ) as bv15_block,
               (case Business_Group_ID
                    when @bv15 then
                        block_pickup
                   end
                   ) as bv15_block_pickup,
               (case Business_Group_ID
                    when @bv15 then
                        block_available
                   end
                   ) as bv15_block_available,
               (case Business_Group_ID
                    when @bv16 then
                        block
                   end
                   ) as bv16_block,
               (case Business_Group_ID
                    when @bv16 then
                        block_pickup
                   end
                   ) as bv16_block_pickup,
               (case Business_Group_ID
                    when @bv16 then
                        block_available
                   end
                   ) as bv16_block_available,
               (case Business_Group_ID
                    when @bv17 then
                        block
                   end
                   ) as bv17_block,
               (case Business_Group_ID
                    when @bv17 then
                        block_pickup
                   end
                   ) as bv17_block_pickup,
               (case Business_Group_ID
                    when @bv17 then
                        block_available
                   end
                   ) as bv17_block_available,
               (case Business_Group_ID
                    when @bv18 then
                        block
                   end
                   ) as bv18_block,
               (case Business_Group_ID
                    when @bv18 then
                        block_pickup
                   end
                   ) as bv18_block_pickup,
               (case Business_Group_ID
                    when @bv18 then
                        block_available
                   end
                   ) as bv18_block_available,
               (case Business_Group_ID
                    when @bv19 then
                        block
                   end
                   ) as bv19_block,
               (case Business_Group_ID
                    when @bv19 then
                        block_pickup
                   end
                   ) as bv19_block_pickup,
               (case Business_Group_ID
                    when @bv19 then
                        block_available
                   end
                   ) as bv19_block_available,
               (case Business_Group_ID
                    when @bv20 then
                        block
                   end
                   ) as bv20_block,
               (case Business_Group_ID
                    when @bv20 then
                        block_pickup
                   end
                   ) as bv20_block_pickup,
               (case Business_Group_ID
                    when @bv20 then
                        block_available
                   end
                   ) as bv20_block_available,
               (case Business_Group_ID
                    when @bv21 then
                        block
                   end
                   ) as bv21_block,
               (case Business_Group_ID
                    when @bv21 then
                        block_pickup
                   end
                   ) as bv21_block_pickup,
               (case Business_Group_ID
                    when @bv21 then
                        block_available
                   end
                   ) as bv21_block_available,
               (case Business_Group_ID
                    when @bv22 then
                        block
                   end
                   ) as bv22_block,
               (case Business_Group_ID
                    when @bv22 then
                        block_pickup
                   end
                   ) as bv22_block_pickup,
               (case Business_Group_ID
                    when @bv22 then
                        block_available
                   end
                   ) as bv22_block_available,
               (case Business_Group_ID
                    when @bv23 then
                        block
                   end
                   ) as bv23_block,
               (case Business_Group_ID
                    when @bv23 then
                        block_pickup
                   end
                   ) as bv23_block_pickup,
               (case Business_Group_ID
                    when @bv23 then
                        block_available
                   end
                   ) as bv23_block_available,
               (case Business_Group_ID
                    when @bv24 then
                        block
                   end
                   ) as bv24_block,
               (case Business_Group_ID
                    when @bv24 then
                        block_pickup
                   end
                   ) as bv24_block_pickup,
               (case Business_Group_ID
                    when @bv24 then
                        block_available
                   end
                   ) as bv24_block_available,
               (case Business_Group_ID
                    when @bv25 then
                        block
                   end
                   ) as bv25_block,
               (case Business_Group_ID
                    when @bv25 then
                        block_pickup
                   end
                   ) as bv25_block_pickup,
               (case Business_Group_ID
                    when @bv25 then
                        block_available
                   end
                   ) as bv25_block_available,
               (case Business_Group_ID
                    when @bv26 then
                        block
                   end
                   ) as bv26_block,
               (case Business_Group_ID
                    when @bv26 then
                        block_pickup
                   end
                   ) as bv26_block_pickup,
               (case Business_Group_ID
                    when @bv26 then
                        block_available
                   end
                   ) as bv26_block_available,
               (case Business_Group_ID
                    when @bv27 then
                        block
                   end
                   ) as bv27_block,
               (case Business_Group_ID
                    when @bv27 then
                        block_pickup
                   end
                   ) as bv27_block_pickup,
               (case Business_Group_ID
                    when @bv27 then
                        block_available
                   end
                   ) as bv27_block_available,
               (case Business_Group_ID
                    when @bv28 then
                        block
                   end
                   ) as bv28_block,
               (case Business_Group_ID
                    when @bv28 then
                        block_pickup
                   end
                   ) as bv28_block_pickup,
               (case Business_Group_ID
                    when @bv28 then
                        block_available
                   end
                   ) as bv28_block_available,
               (case Business_Group_ID
                    when @bv29 then
                        block
                   end
                   ) as bv29_block,
               (case Business_Group_ID
                    when @bv29 then
                        block_pickup
                   end
                   ) as bv29_block_pickup,
               (case Business_Group_ID
                    when @bv29 then
                        block_available
                   end
                   ) as bv29_block_available,
               (case Business_Group_ID
                    when @bv30 then
                        block
                   end
                   ) as bv30_block,
               (case Business_Group_ID
                    when @bv30 then
                        block_pickup
                   end
                   ) as bv30_block_pickup,
               (case Business_Group_ID
                    when @bv30 then
                        block_available
                   end
                   ) as bv30_block_available,
               (case Business_Group_ID
                    when @bv31 then
                        block
                   end
                   ) as bv31_block,
               (case Business_Group_ID
                    when @bv31 then
                        block_pickup
                   end
                   ) as bv31_block_pickup,
               (case Business_Group_ID
                    when @bv31 then
                        block_available
                   end
                   ) as bv31_block_available,
               (case Business_Group_ID
                    when @bv32 then
                        block
                   end
                   ) as bv32_block,
               (case Business_Group_ID
                    when @bv32 then
                        block_pickup
                   end
                   ) as bv32_block_pickup,
               (case Business_Group_ID
                    when @bv32 then
                        block_available
                   end
                   ) as bv32_block_available,
               (case Business_Group_ID
                    when @bv33 then
                        block
                   end
                   ) as bv33_block,
               (case Business_Group_ID
                    when @bv33 then
                        block_pickup
                   end
                   ) as bv33_block_pickup,
               (case Business_Group_ID
                    when @bv33 then
                        block_available
                   end
                   ) as bv33_block_available,
               (case Business_Group_ID
                    when @bv34 then
                        block
                   end
                   ) as bv34_block,
               (case Business_Group_ID
                    when @bv34 then
                        block_pickup
                   end
                   ) as bv34_block_pickup,
               (case Business_Group_ID
                    when @bv34 then
                        block_available
                   end
                   ) as bv34_block_available,
               (case Business_Group_ID
                    when @bv35 then
                        block
                   end
                   ) as bv35_block,
               (case Business_Group_ID
                    when @bv35 then
                        block_pickup
                   end
                   ) as bv35_block_pickup,
               (case Business_Group_ID
                    when @bv35 then
                        block_available
                   end
                   ) as bv35_block_available,
               (case Business_Group_ID
                    when @bv36 then
                        block
                   end
                   ) as bv36_block,
               (case Business_Group_ID
                    when @bv36 then
                        block_pickup
                   end
                   ) as bv36_block_pickup,
               (case Business_Group_ID
                    when @bv36 then
                        block_available
                   end
                   ) as bv36_block_available,
               (case Business_Group_ID
                    when @bv37 then
                        block
                   end
                   ) as bv37_block,
               (case Business_Group_ID
                    when @bv37 then
                        block_pickup
                   end
                   ) as bv37_block_pickup,
               (case Business_Group_ID
                    when @bv37 then
                        block_available
                   end
                   ) as bv37_block_available,
               (case Business_Group_ID
                    when @bv38 then
                        block
                   end
                   ) as bv38_block,
               (case Business_Group_ID
                    when @bv38 then
                        block_pickup
                   end
                   ) as bv38_block_pickup,
               (case Business_Group_ID
                    when @bv38 then
                        block_available
                   end
                   ) as bv38_block_available,
               (case Business_Group_ID
                    when @bv39 then
                        block
                   end
                   ) as bv39_block,
               (case Business_Group_ID
                    when @bv39 then
                        block_pickup
                   end
                   ) as bv39_block_pickup,
               (case Business_Group_ID
                    when @bv39 then
                        block_available
                   end
                   ) as bv39_block_available,
               (case Business_Group_ID
                    when @bv40 then
                        block
                   end
                   ) as bv40_block,
               (case Business_Group_ID
                    when @bv40 then
                        block_pickup
                   end
                   ) as bv40_block_pickup,
               (case Business_Group_ID
                    when @bv40 then
                        block_available
                   end
                   ) as bv40_block_available,
               (case Business_Group_ID
                    when @bv41 then
                        block
                   end
                   ) as bv41_block,
               (case Business_Group_ID
                    when @bv41 then
                        block_pickup
                   end
                   ) as bv41_block_pickup,
               (case Business_Group_ID
                    when @bv41 then
                        block_available
                   end
                   ) as bv41_block_available,
               (case Business_Group_ID
                    when @bv42 then
                        block
                   end
                   ) as bv42_block,
               (case Business_Group_ID
                    when @bv42 then
                        block_pickup
                   end
                   ) as bv42_block_pickup,
               (case Business_Group_ID
                    when @bv42 then
                        block_available
                   end
                   ) as bv42_block_available,
               (case Business_Group_ID
                    when @bv43 then
                        block
                   end
                   ) as bv43_block,
               (case Business_Group_ID
                    when @bv43 then
                        block_pickup
                   end
                   ) as bv43_block_pickup,
               (case Business_Group_ID
                    when @bv43 then
                        block_available
                   end
                   ) as bv43_block_available,
               (case Business_Group_ID
                    when @bv44 then
                        block
                   end
                   ) as bv44_block,
               (case Business_Group_ID
                    when @bv44 then
                        block_pickup
                   end
                   ) as bv44_block_pickup,
               (case Business_Group_ID
                    when @bv44 then
                        block_available
                   end
                   ) as bv44_block_available,
               (case Business_Group_ID
                    when @bv45 then
                        block
                   end
                   ) as bv45_block,
               (case Business_Group_ID
                    when @bv45 then
                        block_pickup
                   end
                   ) as bv45_block_pickup,
               (case Business_Group_ID
                    when @bv45 then
                        block_available
                   end
                   ) as bv45_block_available,
               (case Business_Group_ID
                    when @bv46 then
                        block
                   end
                   ) as bv46_block,
               (case Business_Group_ID
                    when @bv46 then
                        block_pickup
                   end
                   ) as bv46_block_pickup,
               (case Business_Group_ID
                    when @bv46 then
                        block_available
                   end
                   ) as bv46_block_available,
               (case Business_Group_ID
                    when @bv47 then
                        block
                   end
                   ) as bv47_block,
               (case Business_Group_ID
                    when @bv47 then
                        block_pickup
                   end
                   ) as bv47_block_pickup,
               (case Business_Group_ID
                    when @bv47 then
                        block_available
                   end
                   ) as bv47_block_available,
               (case Business_Group_ID
                    when @bv48 then
                        block
                   end
                   ) as bv48_block,
               (case Business_Group_ID
                    when @bv48 then
                        block_pickup
                   end
                   ) as bv48_block_pickup,
               (case Business_Group_ID
                    when @bv48 then
                        block_available
                   end
                   ) as bv48_block_available,
               (case Business_Group_ID
                    when @bv49 then
                        block
                   end
                   ) as bv49_block,
               (case Business_Group_ID
                    when @bv49 then
                        block_pickup
                   end
                   ) as bv49_block_pickup,
               (case Business_Group_ID
                    when @bv49 then
                        block_available
                   end
                   ) as bv49_block_available,
               (case Business_Group_ID
                    when @bv50 then
                        block
                   end
                   ) as bv50_block,
               (case Business_Group_ID
                    when @bv50 then
                        block_pickup
                   end
                   ) as bv50_block_pickup,
               (case Business_Group_ID
                    when @bv50 then
                        block_available
                   end
                   ) as bv50_block_available
               --Archana added for group block and group pickup-End

        from
            (
                select base.occupancy_dt,
                       base.Business_Group_ID,
                       base.Business_Group_Name,
                       datename(dw, base.occupancy_dt) as dow,
                       isnull(a.rooms_sold, 0.0) as roomsoldcurrent,
                       isnull(a.room_revenue, 0.0) as bookedroomrevenuecurrent,
                       isnull(a.adr, 0.0) as bookedadrcurrent,
                       isnull(b.occupancy_forecast_current, 0.0) as occfcstcurrent,
                       isnull(b.fcsted_room_revenue_current, 0.0) as fcstedroomrevenuecurrent,
                       isnull(b.estimated_adr_current, 0.0) as fcstedadrcurrent,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_rooms_sold, isnull(a.rooms_sold, 0.0))
                                   - ISNULL(c.start_date_rooms_sold, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.rooms_sold, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_rooms_sold, 0.0) - ISNULL(c.start_date_rooms_sold, 0.0) as numeric(19, 2))
                           end as roomssoldpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_Date_occupancy_nbr, isnull(b.occupancy_forecast_current, 0.0))
                                   - ISNULL(d.start_date_occupancy_nbr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.occupancy_forecast_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_Date_occupancy_nbr, 0.0) - ISNULL(d.start_date_occupancy_nbr, 0.0) as numeric(19, 2))
                           end as occfcstpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_room_revenue, isnull(a.room_revenue, 0.0))
                                   - ISNULL(c.start_date_room_revenue, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.room_revenue, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_room_revenue, 0.0) - ISNULL(c.start_date_room_revenue, 0.0) as numeric(19, 2))
                           end as bookedroomrevenuepickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_date_revenue, isnull(b.fcsted_room_revenue_current, 0.0))
                                   - ISNULL(d.start_date_revenue, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.fcsted_room_revenue_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_date_revenue, 0.0) - ISNULL(d.start_date_revenue, 0.0) as numeric(19, 2))
                           end as fcstedroomrevenuepickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(c.end_date_adr, isnull(a.adr, 0.0))
                                   - ISNULL(c.start_date_adr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(a.adr, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(c.end_date_adr, 0.0) - ISNULL(c.start_date_adr, 0.0) as numeric(19, 2))
                           end as bookedadrpickup,
                       case
                           when base.occupancy_dt <= @business_end_dt then
                               cast(isnull(d.end_date_adr, isnull(b.estimated_adr_current, 0.0))
                                   - ISNULL(d.start_date_adr, IIF(base.occupancy_dt > @business_start_dt,0.0,isnull(b.estimated_adr_current, 0.0))) as numeric(19, 2))
                           else
                               cast(isnull(d.end_date_adr, 0.0) - ISNULL(d.start_date_adr, 0.0) as numeric(19, 2))
                           end as fcstedadrpickup,
                       groupBlock.block,
                       groupBlock.block_pickup,
                       groupBlock.block_available
                from
                    (
                        select @property_id property_id,
                               CAST(calendar_date as date) Occupancy_DT,
                               Business_Group_ID,
                               Business_Group_Name
                        from calendar_dim
                                 left join @tempBV
                                           on Business_Group_ID is not null
                        where calendar_date
                                  between @start_date and @end_date
                    ) base
                        left join
                    (
                        select *
                        from ufn_get_activity_by_individual_bv(@property_id, @Business_Group_ID, @start_date, @end_date)
                    ) as a
                    on base.property_id = a.property_id
                        and base.occupancy_dt = a.occupancy_dt
                        and base.Business_Group_ID = a.Business_Group_ID
                        left join
                    (
                        select *
                        from dbo.ufn_get_occupancy_forecast_by_individual_bv(
                                @property_id,
                                @Business_Group_ID,
                                @start_date,
                                @end_date
                            )
                    ) as b
                    on base.property_id = b.property_id
                        and base.occupancy_dt = b.occupancy_dt
                        and base.Business_Group_ID = b.Business_Group_ID
                        left join
                    (
                        select q2.property_id,
                               q2.occupancy_dt,
                               q2.Business_Group_ID,
                               q1.rooms_sold as end_date_rooms_sold,
                               q2.rooms_sold as start_date_rooms_sold,
                               q1.room_revenue as end_date_room_revenue,
                               q2.room_revenue as start_date_room_revenue,
                               q1.adr as end_date_adr,
                               q2.adr as start_date_adr
                        from
                            (
                                select occupancy_dt,
                                       property_id,
                                       Business_Group_ID,
                                       rooms_sold,
                                       room_revenue,
                                       adr
                                from ufn_get_activity_asof_businessdate_by_individual_bv_for_pickup(
                                        @property_id,
                                        @Business_Group_ID,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q1
                                right outer join
                            (
                                select occupancy_dt,
                                       property_id,
                                       Business_Group_ID,
                                       rooms_sold,
                                       room_revenue,
                                       adr
                                from ufn_get_activity_asof_businessdate_by_individual_bv_for_pickup(
                                        @property_id,
                                        @Business_Group_ID,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q2
                            on q1.property_id = q2.property_id
                                and q1.occupancy_dt = q2.occupancy_dt
                                and q1.Business_Group_ID = q2.Business_Group_ID
                    ) as c
                    on base.property_id = c.property_id
                        and base.occupancy_dt = c.occupancy_dt
                        and base.Business_Group_ID = c.Business_Group_ID
                        left join
                    (
                        select q2.property_id,
                               q2.occupancy_dt,
                               q2.Business_Group_ID,
                               q1.occupancy_nbr as end_Date_occupancy_nbr,
                               q2.occupancy_nbr as start_date_occupancy_nbr,
                               q1.revenue as end_date_revenue,
                               q2.revenue as start_date_revenue,
                               q1.adr as end_date_adr,
                               q2.adr as start_date_adr
                        from
                            (
                                select *
                                from ufn_get_occupancy_forecast_asof_businessdate_by_individual_bv(
                                        @property_id,
                                        @Business_Group_ID,
                                        @business_end_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q1
                                right outer join
                            (
                                select *
                                from ufn_get_occupancy_forecast_asof_businessdate_by_individual_bv(
                                        @property_id,
                                        @Business_Group_ID,
                                        @business_start_dt,
                                        @start_date,
                                        @end_date
                                    )
                            ) q2
                            on q1.property_id = q2.property_id
                                and q1.occupancy_dt = q2.occupancy_dt
                                and q1.Business_Group_ID = q2.Business_Group_ID
                    ) as d
                    on base.property_id = d.property_id
                        and base.occupancy_dt = d.occupancy_dt
                        and base.Business_Group_ID = d.Business_Group_ID
                        --Archana added group block-Aggregated-START
                        left join
                    (
                        select *
                        from ufn_get_groupBlock_groupPickup_by_BusinessView(
                                @property_id,
                                @Business_Group_ID,
                                @start_date,
                                @end_date,
                                0
                            )
                    ) as groupBlock
                    on a.property_id = groupBlock.property_id
                        and a.occupancy_dt = groupBlock.occupancy_dt
                        and a.Business_Group_ID = groupBlock.business_group_Id
                --Archana added group block-Aggregated-END
            ) data
    ) data2
group by occupancy_dt,
         dow
order by occupancy_dt
    return
end