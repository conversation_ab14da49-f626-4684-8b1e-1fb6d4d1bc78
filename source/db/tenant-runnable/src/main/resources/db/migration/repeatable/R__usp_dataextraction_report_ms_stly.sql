if
    exists(select *
           from sys.objects
           where object_id = object_id(N'[dbo].[usp_dataextraction_report_ms_stly]'))
    drop procedure [dbo].[usp_dataextraction_report_ms_stly]

GO

/****** Object:  StoredProcedure [dbo].[usp_dataextraction_report_ms_stly] Script Date: 17-Jan-21 22:21:46 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp_dataextraction_report_ms_stly](@property_id int,
                                                           @ly_start_date date,
                                                           @ly_end_date date,
                                                           @ly_businessdate date,
                                                           @is_stly_or_2y int,
														   @includeDisconMS smallint,
														   @MktExcludeCompFlag varchar(5))
as
begin
	declare @MktSegStatus varchar(16)
		set @MktSegStatus = '1'
	IF (@includeDisconMS = 1) 
		set @MktSegStatus = '1,3'
    select (case
                when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT)
                else DateAdd(WEEK, 104, pace.Occupancy_DT) end
               ) as Occupancy_DT,
           mkt.mkt_seg_name,
           rooms_sold,
           room_revenue,
           mkt.Mkt_Seg_ID
    from PACE_Mkt_Activity pace
             join Mkt_Seg mkt
                  on mkt.property_Id = pace.property_Id
                      and mkt.Mkt_Seg_ID = pace.Mkt_Seg_ID
                      and mkt.status_id in(select value from varchartoint(@MktSegStatus,','))
                      and mkt.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag, ','))
    where pace.Occupancy_DT between @ly_start_date and @ly_end_date
      and pace.Property_ID = @property_id
      and pace.Business_Day_End_DT = @ly_businessdate
    order by Occupancy_DT, Mkt_Seg_Name

end

GO