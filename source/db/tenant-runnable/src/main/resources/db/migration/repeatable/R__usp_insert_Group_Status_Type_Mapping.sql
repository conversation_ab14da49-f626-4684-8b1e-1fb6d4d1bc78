drop procedure if exists [dbo].[usp_insert_Group_Status_Type_Mapping]
    GO
CREATE PROCEDURE [dbo].[usp_insert_Group_Status_Type_Mapping]
(
    @EXT_SYS_ID INT
)
AS
BEGIN
DELETE FROM [G3_GROUP_STATUS_TYPE_MAP]
INSERT INTO [G3_Group_Status_Type_Map]
VALUES
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'TENTATIVE' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'T1', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'TENTATIVE' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'T2', @EXT_SYS_ID),
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'TENTATIVE' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'T3', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'TENTATIVE' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'T4', @EXT_SYS_ID),
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'TENTATIVE' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'T5', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'PROSPECT' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'R', @EXT_SYS_ID),
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'PROSPECT' AND G3_GROUP_STATUS_TYPE = 'Non Adjust'), 'Q', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'DEFINITE' AND G3_GROUP_STATUS_TYPE = 'Adjust'), 'D', @EXT_SYS_ID),
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'CANCELLED' AND G3_GROUP_STATUS_TYPE = 'Adjust'), 'C', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'LOST/REGRET' AND G3_GROUP_STATUS_TYPE = ''), 'L', @EXT_SYS_ID),
    ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'DEFINITE' AND G3_GROUP_STATUS_TYPE = 'Adjust'), 'A', @EXT_SYS_ID), ((SELECT G3_GROUP_STATUS_TYPE_ID FROM G3_GROUP_STATUS_TYPE WHERE G3_GROUP_STATUS_CODE = 'LOST/REGRET' AND G3_GROUP_STATUS_TYPE = ''), 'U', @EXT_SYS_ID);
END
GO
