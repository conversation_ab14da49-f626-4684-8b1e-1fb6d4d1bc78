IF EXISTS (
		SELECT *
		FROM sys.objects
		WHERE object_id = OBJECT_ID(N'[dbo].[usp_rdd_load_basic_data_cp_optimized]')
		)
	DROP PROCEDURE [dbo].[usp_rdd_load_basic_data_cp_optimized]

GO

CREATE PROCEDURE [dbo].[usp_rdd_load_basic_data_cp_optimized] (
	 @propertyId INT
	,@startDate DATE
	,@endDate DATE
	,@isPhysicalCapacityEnabled INT
	,@compRoomsFilter INT
    ,@masterClassId INT
    ,@caughtUpDate DATE
	)
AS
BEGIN

	DECLARE @exclude_comp_room VARCHAR(5) = '0,1'
	IF (@compRoomsFilter = 1)
	   SET @exclude_comp_room = '0'


	if OBJECT_ID('tempdb..#temp_Calendar') is not null
begin
drop table #temp_Calendar
end
create table #temp_Calendar(
    Occupancy_DT DATE
)

    if OBJECT_ID('tempdb..#FN_Occupancy_Forecast_comp_room_exclusion_table') is not null
begin
drop table #FN_Occupancy_Forecast_comp_room_exclusion_table
end
create table #FN_Occupancy_Forecast_comp_room_exclusion_table(
     Occupancy_DT DATE,
     Occupancy_NBR FLOAT
)


    if OBJECT_ID('tempdb..#grp_data') is not null
begin
drop table #grp_data
end
create table #grp_data(
                          Occupancy_DT Date,
                          Business_Type_ID int,
                          group_sold decimal
)

    -- Populate temporary tables
    INSERT INTO #grp_data (
			Occupancy_DT ,
			Business_Type_ID ,
			group_sold )
SELECT
    maa.Occupancy_DT,
    MSD.Business_Type_ID,
    SUM(maa.Rooms_Sold) AS group_sold
FROM
    Mkt_Accom_Activity maa
        INNER JOIN Accom_Type at ON maa.Accom_Type_ID = at.Accom_Type_ID
    INNER JOIN Mkt_Seg_Details MSD ON maa.Mkt_Seg_ID = MSD.Mkt_Seg_ID
WHERE
    maa.Occupancy_DT BETWEEN @startDate AND @endDate
  AND at.isComponentRoom = 'N'
  AND MSD.Business_Type_ID = 1
GROUP BY
    maa.Occupancy_DT,
    MSD.Business_Type_ID;


INSERT INTO #temp_Calendar (Occupancy_DT)
SELECT CAST(calendar_date AS DATE)
FROM calendar_dim
WHERE calendar_date BETWEEN @startDate AND @endDate;

If @caughtUpDate <= @endDate
BEGIN
-- Create a temporary table to hold data from the Occupancy_FCST subquery
SELECT
    occ.Occupancy_DT,
    occ.Occupancy_NBR
INTO #Occupancy_FCST_Subquery
FROM
    Occupancy_FCST occ
        JOIN Accom_Type at ON occ.Accom_Type_ID = at.Accom_Type_ID
    JOIN Mkt_Seg ms ON occ.MKT_SEG_ID = ms.MKT_SEG_ID
WHERE
    occ.Occupancy_DT BETWEEN @caughtUpDate AND @endDate
  AND at.isComponentRoom = 'N'
  AND ms.Exclude_CompHouse_Data_Display IN (SELECT value FROM varcharToInt(@exclude_comp_room, ','));

-- Insert into #FN_Occupancy_Forecast_comp_room_exclusion_table using the temporary table
INSERT INTO #FN_Occupancy_Forecast_comp_room_exclusion_table (Occupancy_DT, Occupancy_NBR)
SELECT
    cal.Occupancy_DT AS Occupancy_DT,
    ISNULL(SUM(subquery.Occupancy_NBR), 0) AS Occupancy_NBR
FROM
    #temp_Calendar cal
        LEFT JOIN
    #Occupancy_FCST_Subquery subquery ON cal.Occupancy_DT = subquery.Occupancy_DT
WHERE
    cal.Occupancy_DT BETWEEN @startDate AND @endDate
  AND cal.Occupancy_DT >= @caughtUpDate
GROUP BY
    cal.Occupancy_DT;

-- Drop the temporary table
DROP TABLE #Occupancy_FCST_Subquery
END

IF @caughtUpDate > @startDate
BEGIN
INSERT INTO #FN_Occupancy_Forecast_comp_room_exclusion_table (Occupancy_DT, Occupancy_NBR)
SELECT
    ta.Occupancy_DT,
    CAST(
            (case
                 when @exclude_comp_room = '0'
                     then ta.Rooms_Sold - isNull(maa.Rooms_Sold, 0)
                 else
                     ta.Rooms_Sold
                end) as NUMERIC(8,2))
        AS Occupancy_NBR
FROM
    Total_Activity ta
        left join
    (
        select
            mact.Occupancy_DT,
            sum(mact.Rooms_Sold) as Rooms_Sold,
            sum(mact.Room_Revenue) as Room_Revenue
        from Mkt_Accom_Activity mact
                 inner join Mkt_Seg ms on ms.Mkt_Seg_ID = mact.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (1)
        where mact.Occupancy_DT < @caughtUpDate
        group by mact.Occupancy_DT
    ) as maa
    on maa.Occupancy_DT = ta.Occupancy_DT
WHERE ta.Occupancy_DT < @caughtUpDate
  AND  ta.Occupancy_DT between @startDate and  @endDate ;
END

select
    base.Occupancy_DT,
    SUBSTRING(DATENAME(dw, base.Occupancy_DT), 1, 3) AS DOW,
    activity.capacity AS Capacity,
    activity.onbooks AS Onbooks,
    grp.group_sold AS Group_Sold,
    CAST(activity.ADR AS NUMERIC(19, 2)) AS ADR,
    CAST(activity.REVPAR AS NUMERIC(19, 2)) AS REVPAR,
    activity.ooo AS OOO,
    CAST(bar.barrate AS NUMERIC(19, 2)) AS Bar_Rate,
    CAST(lrv.lrv AS NUMERIC(19, 2)) AS LRV,
    ovbk.Overbooking_Decision AS Overbooking_Decision,
    CAST(fcst.Occupancy_NBR AS NUMERIC(8, 1)) AS Occupancy_NBR,
    CAST(dmd.User_Remaining_Demand AS NUMERIC(19, 2)) AS User_Remaining_Demand,
    wash.User_Wash AS User_Wash,
    CAST((CASE WHEN activity.capacity - activity.ooo = 0 THEN 0 ELSE (fcst.Occupancy_NBR / (activity.capacity - activity.ooo)) * 100 END) AS NUMERIC(19, 2)) AS Occupancy_perc,
    bar.decisionReasonTypeId AS decisionReasonTypeId,
    ovk.Autoscaled AS Autoscaled

from
    #temp_Calendar base left join
    (
        select Property_ID,Occupancy_DT,capacity,onbooks,
               ADR =
               case(onbooks)
                   when 0 then 0
                   else
                           Room_Revenue/onbooks
                   end,
               REVPAR =
               case (@isPhysicalCapacityEnabled)
                   when 1 then
                       case (capacity)
                           when 0 then 0
                           else (Room_Revenue/capacity)
                           end
                   else
                       case (capacity - ooo)
                           when 0 then 0
                           else
                                   Room_Revenue/(capacity - ooo)
                           end
                   end,
               ooo
        from
            (
                select Property_ID,ta.Occupancy_DT,Total_Accom_Capacity capacity,
                       (
                           CASE
                               WHEN @compRoomsFilter = 1
                                   THEN ta.Rooms_Sold - isNull(maa.Rooms_Sold, 0)
                               ELSE ta.Rooms_Sold
                               END
                           ) AS onbooks
                        ,(
                           CASE
                               WHEN @compRoomsFilter = 1
                                   THEN ta.Room_Revenue - isNull(maa.Room_Revenue, 0)
                               ELSE ta.Room_Revenue
                               END
                           ) AS Room_Revenue,
                       (Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) ooo
                from Total_Activity ta
                         LEFT JOIN (
                    SELECT mact.Occupancy_DT
                         ,sum(mact.Rooms_Sold) AS Rooms_Sold
                         ,sum(mact.Room_Revenue) AS Room_Revenue
                    FROM Mkt_Accom_Activity mact
                             INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = mact.Mkt_Seg_ID
                        AND ms.Exclude_CompHouse_Data_Display IN (1)
                    WHERE mact.Property_ID = @propertyId
                      AND mact.Occupancy_DT BETWEEN @startDate
                        AND @endDate
                    GROUP BY mact.Occupancy_DT
                ) AS maa ON maa.Occupancy_DT = ta.Occupancy_DT
                where Property_Id=@propertyId and ta.Occupancy_DT between @startDate and  @endDate
            ) data
    ) activity on base.Occupancy_DT=activity.Occupancy_DT
                        left join
    (
        select arrival_dt as Occupancy_DT , barrate, decisionReasonTypeId
        from
            (
                select dbo.property_id,arrival_dt,dbo.Decision_Reason_Type_ID as decisionReasonTypeId, dbo.Final_BAR as barrate
                from cp_decision_bar_output dbo
                         inner join Accom_Type at on at.Accom_Type_ID = dbo.Accom_Type_ID
                    inner join accom_class ac
                    on ac.accom_class_id = at.accom_class_id and ac.master_class = 1 and ac.Status_ID=1 and ac.System_Default=0
                    inner join CP_Cfg_AC cfg on cfg.Accom_Type_ID = at.Accom_Type_ID
                    inner join Product p on dbo.Product_ID = p.Product_ID
                where dbo.property_id = @propertyId
                  and p.System_Default = 1
                  and dbo.arrival_dt between @startDate and @endDate and LOS = -1
            ) data group by property_id,arrival_dt,decisionReasonTypeId, barrate
    )bar on base.Occupancy_DT=bar.Occupancy_DT
                        left join
    (
        select occupancy_dt,lrv from ufn_get_decision_lrv_by_rc (@propertyId,@masterClassId,@startDate, @endDate)
    ) lrv on base.Occupancy_DT=lrv.occupancy_dt
                        left join
    (
        select Occupancy_DT,Overbooking_Decision from Decision_Ovrbk_Property
        where property_id = @propertyId and Occupancy_DT between @startDate and @endDate
    ) ovbk on bar.Occupancy_DT = ovbk.Occupancy_DT
                        left join
    (
        select Occupancy_DT,Occupancy_NBR from
            #FN_Occupancy_Forecast_comp_room_exclusion_table
    ) fcst on base.Occupancy_DT=fcst.Occupancy_DT
                        left join
    (
        select Occupancy_DT,
               User_Remaining_Demand =
               case
                   when Occupancy_DT >= @caughtUpDate then SUM(User_Remaining_Demand)
                   else 0
                   end
        from Occupancy_Demand_FCST
        where Occupancy_DT between @startDate and  @endDate and  Property_ID=@propertyId
        group by Occupancy_DT
    ) dmd on base.Occupancy_DT=dmd.Occupancy_DT
                        left join
    (
        select Occupancy_DT,User_Wash from Wash_Property_FCST where Property_Id=@propertyId and Occupancy_DT between @startDate and  @endDate
    ) wash on base.Occupancy_DT = wash.Occupancy_DT
     left join
    #grp_data grp on base.Occupancy_DT=grp.Occupancy_DT
                    left join
    (
        select Occupancy_DT,Autoscaled from Decision_Ovrbk_Property
        where property_id = @propertyId and Occupancy_DT between @startDate and @endDate
    ) ovk on bar.Occupancy_DT = ovk.Occupancy_DT
order by base.Occupancy_DT

END