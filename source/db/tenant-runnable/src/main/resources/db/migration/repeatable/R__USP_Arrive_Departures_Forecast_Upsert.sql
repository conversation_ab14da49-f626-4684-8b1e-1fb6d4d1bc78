DROP PROCEDURE IF EXISTS [dbo].[usp_Arr_Dep_FCST_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Arr_Dep_FCST_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Arr_Dep_FCST_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Arr_Dep_FCST_Batch]
GO
CREATE TYPE dbo.Arr_Dep_FCST_Batch AS TABLE
(
    [Decision_ID]     [bigint],
    [Property_ID]     [int],
    [Occupancy_DT]    [date],
    [Arrival_OTB]     [int],
    [Departures_OTB]  [int],
    [Staythru_OTB]    [int],
    [Arr_Fcst]        [int],
    [Dep_Fcst]        [int],
    [Staythru_Fcst]   [int],
    [Month_ID]        [int],
    [Year_ID]         [int],
    [CreateDate_DTTM] [datetime]
);
GO

CREATE PROCEDURE usp_Arr_Dep_FCST_Upsert @Arr_Dep_FCST_Batch Arr_Dep_FCST_Batch READONLY
AS
BEGIN
MERGE INTO dbo.Arr_Dep_FCST AS Target
    USING @Arr_Dep_FCST_Batch AS Source
    ON (Target.Property_ID = Source.Property_ID
        AND Target.Occupancy_DT = Source.Occupancy_DT)
    WHEN MATCHED THEN
        UPDATE
            SET Target.Decision_ID     = Source.Decision_ID,
                Target.Arrival_OTB     = Source.Arrival_OTB,
                Target.Departures_OTB  = Source.Departures_OTB,
                Target.Staythru_OTB    = Source.Staythru_OTB,
                Target.Arr_Fcst        = Source.Arr_Fcst,
                Target.Dep_Fcst        = Source.Dep_Fcst,
                Target.Staythru_Fcst   = Source.Staythru_Fcst,
                Target.Month_ID        = Source.Month_ID,
                Target.Year_ID         = Source.Year_ID,
                Target.CreateDate_DTTM = Source.CreateDate_DTTM
    WHEN NOT MATCHED THEN
        INSERT (Decision_ID, Property_ID, Occupancy_DT, Arrival_OTB, Departures_OTB,
                Staythru_OTB, Arr_Fcst, Dep_Fcst, Staythru_Fcst, Month_ID, Year_ID, CreateDate_DTTM)
            VALUES (Source.Decision_ID, Source.Property_ID, Source.Occupancy_DT, Source.Arrival_OTB,
                    Source.Departures_OTB, Source.Staythru_OTB, Source.Arr_Fcst, Source.Dep_Fcst,
                    Source.Staythru_Fcst, Source.Month_ID, Source.Year_ID, Source.CreateDate_DTTM);
END
GO

CREATE PROCEDURE usp_Arr_Dep_FCST_Insert @Arr_Dep_FCST_Batch Arr_Dep_FCST_Batch READONLY
AS
BEGIN
INSERT INTO [dbo].[Arr_Dep_FCST] (Decision_ID, Property_ID, Occupancy_DT, Arrival_OTB, Departures_OTB,
                                  Staythru_OTB, Arr_Fcst, Dep_Fcst, Staythru_Fcst, Month_ID, Year_ID, CreateDate_DTTM)
SELECT [Decision_ID], [Property_ID], [Occupancy_DT], [Arrival_OTB],
       [Departures_OTB], [Staythru_OTB], [Arr_Fcst], [Dep_Fcst],
       [Staythru_Fcst], [Month_ID], [Year_ID], [CreateDate_DTTM]
FROM @Arr_Dep_FCST_Batch Arr_Dep_FCST_Batch;
END
GO

CREATE PROCEDURE usp_Arr_Dep_FCST_Update @Arr_Dep_FCST_Batch Arr_Dep_FCST_Batch READONLY
AS
BEGIN
UPDATE dbo.Arr_Dep_FCST
SET Decision_ID     = Source.Decision_ID,
    Arrival_OTB     = Source.Arrival_OTB,
    Departures_OTB  = Source.Departures_OTB,
    Staythru_OTB    = Source.Staythru_OTB,
    Arr_Fcst        = Source.Arr_Fcst,
    Dep_Fcst        = Source.Dep_Fcst,
    Staythru_Fcst   = Source.Staythru_Fcst,
    Month_ID        = Source.Month_ID,
    Year_ID         = Source.Year_ID,
    CreateDate_DTTM = Source.CreateDate_DTTM
    FROM @Arr_Dep_FCST_Batch AS Source
WHERE
    [Arr_Dep_FCST].Property_ID = Source.Property_ID AND
    [Arr_Dep_FCST].Occupancy_DT = Source.Occupancy_DT
END
GO