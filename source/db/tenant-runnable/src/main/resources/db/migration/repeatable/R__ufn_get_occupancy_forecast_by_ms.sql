if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_occupancy_forecast_by_ms]'))
    drop function [ufn_get_occupancy_forecast_by_ms]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Function Name: ufn_get_occupancy_forecast_by_ms

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@business_type --> refers to type of business - 'Group' or 'Transient'
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
Ouput Parameter : NA

Execution: this is just an example
	Example 1: Report at market segment level 
	-----------------------------------------
	select * from dbo.ufn_get_occupancy_forecast_by_ms (18,3,13,'84','2011-07-01','2011-07-31')
	
	select * from dbo.ufn_get_occupancy_forecast_by_ms (18,3,13,'81,84','2011-07-01','2011-07-31')
	

	
Purpose: The purpose of this function is to report 'rooms_sold','adr' and 'room_revenue' values by 
		 market segment for a given occupancy date range. 

Assumptions : Please enclose the market segment ids (@mkt_seg_id) in single quotes.
		 
Author: Atul and Manohar

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
09/23/2011	Atul				Shendye					Initial Version
09/23/2011	Manohar				Sunkum					Refactored
12/05/2022	Vikas 			    Shivankar				FEYNMAN-1348 :Increase the size of MS input parameter to 1500 characters
***************************************************************************************/

create function [dbo].[ufn_get_occupancy_forecast_by_ms]

(
		@property_id int,
		@record_type_id int,
		@process_status_id int,
		@mkt_seg_id varchar(1500),
		@start_date date,
		@end_date date
)		
returns  @adr_room_rev table
	(	
		occupancy_dt	date,
		property_id	int,
		occupancy_forecast_current	numeric(8,2),
		fcsted_room_revenue_current	numeric(19,5),
		estimated_adr_current	numeric(19,5)
	)
as
begin
		-- extract caughtupdate
		declare @caughtupdate date
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,@record_type_id,@process_status_id))
		-- extract the metrics by market segment
		insert into @adr_room_rev
		select 
			occupancy_dt,
			property_id,
			sum(occupancy_nbr) as occupancy_forecast_current, 
			sum(revenue) as fcsted_room_revenue_current,
			estimated_adr_current =
				case (sum(occupancy_nbr))
					when 0 then 0
				else
					sum(revenue)/sum(occupancy_nbr)
				end
		from
		(
			select Occupancy_DT,fnATMS.Property_ID,fnATMS.accom_class_id,Occupancy_NBR,revenue
			from dbo.FN_AT_MS_Occupancy(@caughtupdate, 0) fnATMS inner join Accom_Type at 
			on fnATMS.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
			where fnATMS.property_id=@property_id and occupancy_dt between @start_date and @end_date
			and mkt_seg_id in (select value from varchartoint(@mkt_seg_id,','))
		) ocf  group by occupancy_dt,property_id 
		order by occupancy_dt
	return
end