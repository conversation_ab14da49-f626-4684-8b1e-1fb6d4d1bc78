DROP PROCEDURE IF EXISTS [dbo].[usp_Wash_Forecast_Group_FCST_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Wash_Forecast_Group_FCST_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Wash_Forecast_Group_FCST_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Wash_Forecast_Group_FCST_Batch]

CREATE TYPE dbo.Wash_Forecast_Group_FCST_Batch AS TABLE
    (

    [Decision_ID]       [bigint],
    [Property_ID]       [int],
    [Forecast_Group_ID] [int],
    [Occupancy_DT]      [date],
    [System_Wash]       [numeric](18, 2),
    [User_Wash]         [numeric](18, 2),
    [CreateDate_DTTM]   [datetime]
    );
GO

CREATE PROCEDURE usp_Wash_Forecast_Group_FCST_Upsert @Wash_Forecast_Group_FCST_Batch Wash_Forecast_Group_FCST_Batch READONLY
AS
BEGIN
MERGE INTO [dbo].[Wash_Forecast_Group_FCST] AS Target
    USING @Wash_Forecast_Group_FCST_Batch AS Source
    ON (Target.[Property_ID] = Source.[Property_ID]
    AND Target.[Forecast_Group_ID] = Source.[Forecast_Group_ID]
    AND Target.[Occupancy_DT] = Source.[Occupancy_DT])
    WHEN MATCHED THEN
UPDATE
    SET Target.[Decision_ID]     = Source.[Decision_ID],
    Target.[System_Wash]     = Source.[System_Wash],
    Target.[User_Wash]       = Source.[User_Wash],
    Target.[CreateDate_DTTM] = Source.[CreateDate_DTTM]

    WHEN NOT MATCHED THEN
INSERT ( [Decision_ID]
, [Property_ID]
, [Forecast_Group_ID]
, [Occupancy_DT]
, [System_Wash]
, [User_Wash]
, [CreateDate_DTTM])
VALUES ( Source.[Decision_ID]
        , Source.[Property_ID]
        , Source.[Forecast_Group_ID]
        , Source.[Occupancy_DT]
        , Source.[System_Wash]
        , Source.[User_Wash]
        , Source.[CreateDate_DTTM]);
END
GO

CREATE PROCEDURE usp_Wash_Forecast_Group_FCST_Update @Wash_Forecast_Group_FCST_Batch Wash_Forecast_Group_FCST_Batch READONLY
AS
BEGIN
UPDATE [dbo].[Wash_Forecast_Group_FCST]
SET [Decision_ID]     = Source.[Decision_ID],
    [System_Wash]     = Source.[System_Wash],
    [User_Wash]       = Source.[User_Wash],
    [CreateDate_DTTM] = Source.[CreateDate_DTTM]
FROM @Wash_Forecast_Group_FCST_Batch AS Source
WHERE [Wash_Forecast_Group_FCST].[Property_ID] = Source.[Property_ID]
  AND [Wash_Forecast_Group_FCST].[Forecast_Group_ID] = Source.[Forecast_Group_ID]
  AND [Wash_Forecast_Group_FCST].[Occupancy_DT] = Source.[Occupancy_DT];
END
GO

CREATE PROCEDURE usp_Wash_Forecast_Group_FCST_Insert @Wash_Forecast_Group_FCST_Batch Wash_Forecast_Group_FCST_Batch READONLY
AS
BEGIN
INSERT INTO dbo.Wash_Forecast_Group_FCST (Decision_ID, Property_ID, Forecast_Group_ID,Occupancy_DT,System_Wash,User_Wash,CreateDate_DTTM)
SELECT Decision_ID, Property_ID, Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash, CreateDate_DTTM
FROM @Wash_Forecast_Group_FCST_Batch Wash_Forecast_Group_FCST_Batch;
END
GO