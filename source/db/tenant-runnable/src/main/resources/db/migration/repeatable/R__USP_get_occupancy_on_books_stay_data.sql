DROP PROCEDURE IF EXISTS [dbo].[usp_get_occupancy_on_books_for_stay_data]

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON

GO
CREATE PROCEDURE [dbo].[usp_get_occupancy_on_books_for_stay_data](
    @startDate DATETIME,
    @endDate DATETIME,
    @forecastGroupId INT,
    @accomClassId INT
)
AS
BEGIN
    IF @forecastGroupId <= 0 SET  @forecastGroupId = null

    SELECT MSFG.Mkt_Seg_ID
    INTO #forecast_group
    FROM Mkt_Seg_Forecast_Group MSFG
    WHERE MSFG.Status_ID = 1
      AND MSFG.Forecast_Group_ID = isnull(@forecastGroupId, MSFG.Forecast_Group_ID)

    IF @accomClassId <= 0
        BEGIN

            SELECT MA.Occupancy_DT,
                   SUM(MA.Rooms_Sold) Occupancy
            FROM MKT_Accom_Activity MA
                     JOIN #forecast_group MSFG on MSFG.Mkt_Seg_ID = MA.Mkt_Seg_ID
            WHERE MA.Occupancy_DT between @startDate AND @endDate

            GROUP BY MA.Occupancy_DT
            ORDER BY MA.Occupancy_DT
        END

    ELSE
        BEGIN

            SELECT AT.Accom_Type_ID
            INTO #accom_type
            FROM Accom_Type AT
                     JOIN Accom_Class AC on AC.Accom_Class_ID = AT.Accom_Class_ID
            WHERE AC.Accom_Class_ID = @accomClassId

            SELECT MA.Occupancy_DT,
                   SUM(MA.Rooms_Sold) Occupancy
            FROM MKT_Accom_Activity MA
                     JOIN #accom_type AT on MA.Accom_Type_ID = AT.Accom_Type_ID
                     JOIN #forecast_group MSFG on MSFG.Mkt_Seg_ID = MA.Mkt_Seg_ID
            WHERE MA.Occupancy_DT between @startDate AND @endDate

            GROUP BY MA.Occupancy_DT
            ORDER BY MA.Occupancy_DT

            DROP TABLE #accom_type
        END

    DROP TABLE #forecast_group
END
GO