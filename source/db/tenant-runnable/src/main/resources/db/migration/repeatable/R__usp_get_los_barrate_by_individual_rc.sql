IF EXISTS
(
    SELECT * FROM sys.objects WHERE object_id = object_id(N'[usp_get_los_barrate_by_individual_rc]')
)
    DROP PROCEDURE [dbo].[usp_get_los_barrate_by_individual_rc]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_get_los_barrate_by_individual_rc]
(
    @property_id INT,
    @roomclass_id VARCHAR(500),
    @start_date DATE,
    @end_date DATE,
    @includeZeroCapacityRT INT
)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @los_barrate TABLE
    (
        property_id INT,
        arrival_dt DATE,
        accom_class_id INT,
        bar_los1 FLOAT,
        bar_los2 FLOAT,
        bar_los3 FLOAT,
        bar_los4 FLOAT,
        bar_los5 FLOAT,
        bar_los6 FLOAT,
        bar_los7 FLOAT,
        bar_los8 FLOAT,
        bar_by_day FLOAT
    )

    INSERT INTO @los_barrate
    SELECT property_id,
           arrival_dt,
           accom_class_id,
           ISNULL([1], 0.0) AS [1],
           ISNULL([2], 0.0) AS [2],
           ISNULL([3], 0.0) AS [3],
           ISNULL([4], 0.0) AS [4],
           ISNULL([5], 0.0) AS [5],
           ISNULL([6], 0.0) AS [6],
           ISNULL([7], 0.0) AS [7],
           ISNULL([8], 0.0) AS [8],
           ISNULL([-1], 0.0) AS [-1]
    FROM
    (
        SELECT property_id,
               arrival_dt,
               accom_class_id,
               los,
               MIN(rate) AS barrate
        FROM
        (
            SELECT dbo.property_id,
                   arrival_dt,
                   ac.accom_class_id,
                   rate_code_name,
                   los,
                   rate = CASE (DATENAME(dw, arrival_dt))
                              WHEN 'monday' THEN
                                  rud.monday
                              WHEN 'tuesday' THEN
                                  rud.tuesday
                              WHEN 'wednesday' THEN
                                  rud.wednesday
                              WHEN 'thursday' THEN
                                  rud.thursday
                              WHEN 'friday' THEN
                                  rud.friday
                              WHEN 'saturday' THEN
                                  rud.saturday
                              WHEN 'sunday' THEN
                                  rud.sunday
                          END
            FROM decision_bar_output dbo
                INNER JOIN accom_class ac
                    ON dbo.accom_class_id = ac.accom_class_id
                       AND ac.accom_class_id IN (
                                                    SELECT value FROM dbo.varcharToInt(@roomclass_id, ',')
                                                )
                INNER JOIN rate_unqualified ru
                    ON dbo.rate_unqualified_id = ru.rate_unqualified_id
                -- AND dbo.arrival_dt BETWEEN ru.start_date_dt AND ru.end_date_dt
                INNER JOIN rate_unqualified_details rud
                    ON ru.rate_unqualified_id = rud.rate_unqualified_id
                INNER JOIN accom_type at
                    ON rud.accom_type_id = at.accom_type_id
                       AND at.Status_ID = 1
                       AND at.System_Default = 0
                       AND at.accom_class_id IN (
                                                    SELECT value FROM dbo.varcharToInt(@roomclass_id, ',')
                                                )
            WHERE dbo.property_id = @property_id
                  AND dbo.arrival_dt
                  BETWEEN rud.start_date_dt AND rud.end_date_dt
                  AND dbo.arrival_dt
                  BETWEEN @start_date AND @end_date
                  AND at.Display_Status_ID IN (
                                                  SELECT 1
                                                  UNION
                                                  SELECT CASE
                                                             WHEN @includeZeroCapacityRT = 1 THEN
                                                                 2
                                                             ELSE
                                                                 1
                                                         END
                                              )
        ) bar
        GROUP BY property_id,
                 arrival_dt,
                 accom_class_id,
                 rate_code_name,
                 los
    ) AS src
    PIVOT
    (
        AVG(barrate)
        FOR los IN ([1], [2], [3], [4], [5], [6], [7], [8], [-1])
    ) AS pivottable2
    ORDER BY arrival_dt;
    SELECT * FROM @los_barrate;
    RETURN;
END