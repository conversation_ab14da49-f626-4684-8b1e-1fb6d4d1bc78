DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_Ovrbk_Accom_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_Ovrbk_Accom_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_Ovrbk_Accom_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Decision_Ovrbk_Accom_Batch]
GO

CREATE TYPE dbo.Decision_Ovrbk_Accom_Batch AS TABLE
(

    [Decision_ID]          [bigint],
    [Property_ID]          [int],
    [Occupancy_DT]         [date],
    [Accom_Type_ID]        [int],
    [Overbooking_Decision] [numeric](18, 0),
    [CreateDate_DTTM]      [datetime]
    );
GO

CREATE PROCEDURE usp_Decision_Ovrbk_Accom_Upsert @Decision_Ovrbk_Accom_Batch Decision_Ovrbk_Accom_Batch READONLY
AS
BEGIN
MERGE INTO [dbo].[Decision_Ovrbk_Accom] AS Target
    USING @Decision_Ovrbk_Accom_Batch AS Source
    ON (Target.[Property_ID] = Source.[Property_ID]
    AND Target.[Accom_Type_ID] = Source.[Accom_Type_ID]
    AND Target.[Occupancy_DT] = Source.[Occupancy_DT])
    WHEN MATCHED THEN
UPDATE
    SET Target.[Decision_ID]          = Source.[Decision_ID],
    Target.[Overbooking_Decision] = Source.[Overbooking_Decision],
    Target.[CreateDate_DTTM]      = Source.[CreateDate_DTTM]

    WHEN NOT MATCHED THEN
INSERT ( [Decision_ID]
, [Property_ID]
, [Occupancy_DT]
, [Accom_Type_ID]
, [Overbooking_Decision]
, [CreateDate_DTTM])
VALUES ( Source.[Decision_ID]
        , Source.[Property_ID]
        , Source.[Occupancy_DT]
        , Source.[Accom_Type_ID]
        , Source.[Overbooking_Decision]
        , Source.[CreateDate_DTTM]);
END
GO

CREATE PROCEDURE usp_Decision_Ovrbk_Accom_Update @Decision_Ovrbk_Accom_Batch Decision_Ovrbk_Accom_Batch READONLY
AS
BEGIN
UPDATE [dbo].[Decision_Ovrbk_Accom]
SET [Decision_ID]          = Source.[Decision_ID],
    [Overbooking_Decision] = Source.[Overbooking_Decision],
    [CreateDate_DTTM]      = Source.[CreateDate_DTTM]
FROM @Decision_Ovrbk_Accom_Batch AS Source
WHERE [Decision_Ovrbk_Accom].[Property_ID] = Source.[Property_ID]
  AND [Decision_Ovrbk_Accom].[Accom_Type_ID] = Source.[Accom_Type_ID]
  AND [Decision_Ovrbk_Accom].[Occupancy_DT] = Source.[Occupancy_DT];
END
GO

CREATE PROCEDURE usp_Decision_Ovrbk_Accom_Insert @Decision_Ovrbk_Accom_Batch Decision_Ovrbk_Accom_Batch READONLY
AS
BEGIN
INSERT INTO [dbo].[Decision_Ovrbk_Accom] (Decision_ID, Property_ID, Occupancy_DT, Accom_Type_ID, Overbooking_Decision, CreateDate_DTTM)
SELECT Decision_ID, Property_ID, Occupancy_DT, Accom_Type_ID, Overbooking_Decision, CreateDate_DTTM
FROM @Decision_Ovrbk_Accom_Batch Decision_Ovrbk_Accom_Batch;
END
GO