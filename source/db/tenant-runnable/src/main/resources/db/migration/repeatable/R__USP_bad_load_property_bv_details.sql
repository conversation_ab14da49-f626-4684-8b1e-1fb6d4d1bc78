IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_bad_load_property_bv_details]'))
DROP PROCEDURE [dbo].[usp_bad_load_property_bv_details]
/****** Object:  StoredProcedure [dbo].[usp_bad_load_property_bv_details]    Script Date: 9/28/2021 1:02:10 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Stored Procedure Name: usp_bad_load_property_bv_details

Input Parameters : 
	@property_id --> property id associated with a property (e.g.,'XNAES' id FROM the property table is 10027)
	@start_date --> start date from which we need data ('2016-03-23')
	@end_date --> end date till which we need data ('2016-03-31')
	@business_date --> business run date for the selected property
	@business_group_ids --> business group ids for which we need data
	@ms_status_ids --> status id for market segment
	@comp_rooms_filter_flag --> comp filter is associated with market segment

Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_bad_load_property_bv_details 010027, '2016-03-23', '2016-03-31','2016-03-22', 2, '1,2,3,4,5',1

Purpose: The purpose of this stored procedure is to get the occupancy, adr, booking etc result for a given business view date range. 

Assumptions : NA
		 
Author: Anil
		 
Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
07/30/2021		Anil				Borgude					Initial Version
---------------------------------------------------------------------------------------
06/10/2021		Anil				Borgude					     7.3.3
---------------------------------------------------------------------------------------
05/25/2022		Rajratna			Awale				Adds script to show Budget and
                                                        User forecast data on BV Level.
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_bad_load_property_bv_details]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@business_date date,
	@business_group_ids nvarchar(1000),
	@ms_status_ids varchar(16),
	@comp_rooms_filter_flag int
)		
AS
BEGIN
	SET NOCOUNT ON
		declare @caughtupdate date 

	-- extract caughtup date for a property 
	set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) 

	declare @exclude_comp_room varchar(5) = '0,1';
		if(@comp_rooms_filter_flag = 1)
			set @exclude_comp_room = '0';

		IF OBJECT_ID('tempdb..#temp_mkt_seg') IS NOT NULL
		BEGIN
		DROP TABLE #temp_mkt_seg
		END
		CREATE table #temp_mkt_seg
		(
			Property_ID int,
			Mkt_Seg_ID int,
			Mkt_Seg_Name nvarchar(100)
		)
		insert into #temp_mkt_seg
			select Property_ID, Mkt_Seg_ID, Mkt_Seg_Name
			from Mkt_Seg where Property_ID=@property_id and Status_ID in (select items from Split(@ms_status_ids , ',')) and
			 Exclude_CompHouse_Data_Display in (select value from varcharToInt(@exclude_comp_room, ','))

	-- Get all Business Views for Market Segment
	DECLARE @business_view_Ids TABLE (bg_id INT)

	INSERT INTO @business_view_Ids
	SELECT * FROM varcharToInt(@business_group_ids, ',')

	DECLARE @bv_id INT
	CREATE TABLE #business_view_mkt_seg (view_id INT, view_name VARCHAR(100), mkt_id INT)

		INSERT INTO #business_view_mkt_seg
		SELECT bg.Business_Group_ID, bg.Business_Group_Name, msbg.Mkt_Seg_Id
		FROM Business_Group bg
		INNER JOIN @business_view_Ids bvi on bvi.bg_id = bg.Business_Group_ID AND bvi.bg_id > 0
		INNER JOIN Mkt_Seg_Business_Group msbg on bg.Business_Group_ID= msbg.Business_Group_ID
		INNER JOIN #temp_mkt_seg ms ON ms.Mkt_Seg_ID = msbg.Mkt_Seg_ID 
	
		INSERT INTO #business_view_mkt_seg
		SELECT -1, 'Unassigned' , ms.Mkt_Seg_ID 
		FROM  @business_view_Ids bvi 
		INNER JOIN #temp_mkt_seg ms on bvi.bg_id = -1
		where Mkt_Seg_ID NOT IN (select Mkt_Seg_ID from Mkt_Seg_Business_Group)


	CREATE table #temp_mkt_accom_activity (
			View_ID int,
			Mkt_Seg_ID int,
			Occupancy_DT date,
			Rooms_Sold numeric,
			Rooms_Revenue numeric(19,2)
	)
		insert into #temp_mkt_accom_activity
			select bvms.view_id, maa.Mkt_Seg_ID, Occupancy_DT, sum(maa.Rooms_Sold) Rooms_Sold, sum(maa.Room_Revenue) Room_Revenue
			from Mkt_Accom_Activity maa
				inner join Accom_Type at on maa.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
				inner join #business_view_mkt_seg bvms on bvms.mkt_id = maa.Mkt_Seg_ID
				where maa.Property_Id=@property_id 
				and maa.Occupancy_DT between @start_date and @end_date
				group by bvms.view_id, maa.Mkt_Seg_ID, Occupancy_DT

	-- Budget Data for BV
	CREATE TABLE #Budget (
		Property_ID INT,
		Business_Group_ID INT,
		Rooms_Sold NUMERIC,
		Room_Revenue NUMERIC(19,2),
		ADR NUMERIC(19,2)
	)
	INSERT INTO #Budget
		SELECT
			@property_id AS property_id,
			bv.Business_Group_ID,
			SUM(bd.Rooms_Sold), 
			SUM(bd.Room_Revenue),
			(CASE SUM(bd.Rooms_Sold)
				WHEN 0 
					THEN 0 
				ELSE 
					SUM(bd.Room_Revenue) / SUM(bd.Rooms_Sold) 
			END) as ADR
		FROM Budget_Data bd
			INNER JOIN Business_Group bv ON bd.Segment_ID = bv.Business_Group_ID
		WHERE Occupancy_Date BETWEEN @start_date AND @end_date AND
			(SELECT bc.Budget_Level_ID FROM Budget_Config bc WHERE bc.Module_Name = 'client.budget')
							= (SELECT Budget_Level_ID FROM budget_level WHERE Budget_Level = 'Business View')
		GROUP BY bv.Business_Group_ID;

	-- My Forecast Data for BV
	CREATE TABLE #UserForecast (
		Property_ID INT,
		Business_Group_ID INT,
		Rooms_Sold NUMERIC,
		Room_Revenue NUMERIC(19,2),
		ADR NUMERIC(19,2)
	)
	INSERT INTO #UserForecast
		SELECT 
			@property_id as Property_ID,
			ufd.Business_Group_ID,
			SUM(ufd.Rooms_Sold), 
			SUM(ufd.Room_Revenue),
			(CASE SUM(ufd.Rooms_Sold)
				WHEN 0 
					THEN 0 
				ELSE 
					SUM(ufd.Room_Revenue) / SUM(ufd.Rooms_Sold) 
			END) as ADR
		FROM User_Forecast_Data ufd
		WHERE Occupancy_Date BETWEEN @start_date AND @end_date
		  AND (SELECT bc.Budget_Level_ID FROM Budget_Config bc WHERE bc.Module_Name = 'client.user.forecast')
            = (SELECT Budget_Level_ID FROM budget_level WHERE Budget_Level = 'Business View')
		GROUP BY ufd.Business_Group_ID;

	--- Sum Market Segment data for the Business_View
	select bvms.view_id as Business_View_ID, bvms.view_name as Business_View_Name, sum(mkt_seg_totals.Rooms_Sold) Rooms_Sold, 
	cast(sum(Occupancy_Forecast) as numeric(8,1)) Occupancy_Forecast,
	ADR_OnBooks = cast(case(sum(mkt_seg_totals.Rooms_Sold)) when 0 then 0 else SUM(Revenue) / sum(mkt_seg_totals.Rooms_Sold) end as numeric(19,2)),
	ADR_Forecast = cast(case(sum(Occupancy_Forecast)) when 0 then 0 else sum(Revenue_Forecast) / sum(Occupancy_Forecast) end as numeric(19,2)),
	cast(sum(Revenue) as numeric(19,2)) Revenue,
	cast(sum(Revenue_Forecast) as numeric(19,2)) Revenue_Forecast,
	(SUM(mkt_seg_totals.Rooms_sold) - ISNULL(Rooms_Sold_business_dt, 0)) as Rooms_Sold_Pickup,
	(SUM(Revenue) - ISNULL(Rooms_Revenue_business_dt, 0)) as Rooms_Revenue_Pickup,
	cast((CASE WHEN ISNULL(SUM(mkt_seg_totals.Rooms_Sold),0) > 0 THEN  sum(Revenue)/SUM(mkt_seg_totals.Rooms_Sold) ELSE 0 END) 
		 - 
        (CASE WHEN ISNULL(Rooms_Sold_business_dt,0) > 0 THEN  ISNULL(Rooms_Revenue_business_dt, 0)/Rooms_Sold_business_dt ELSE 0 END)
		  as numeric(19,2)) as Adr_Pickup,
	
	--budget and user forecast
	budget.Rooms_Sold as Budget_Rooms_Sold, budget.Room_Revenue as Budget_Rooms_Revenue, budget.ADR as Budget_ADR,
	userForecast.Rooms_Sold as User_Forecast_Rooms_Sold, userForecast.Room_Revenue as User_Forecast_Rooms_Revenue, userForecast.ADR AS User_Forecast_ADR

	from (
		-- Get Market Segment Data 
		select activity.View_ID, activity.Mkt_Seg_ID, Rooms_Sold, Occupancy_Forecast, Revenue, Revenue_Forecast
		from (
			-- Market Segment Activity data
				select
				View_ID, 
				Mkt_Seg_ID,
				Rooms_Sold,
				Room_Revenue Revenue
			from
			(
				select 
					tmac.View_ID,
					tmac.Mkt_Seg_ID,
					sum(tmac.Rooms_Sold) Rooms_Sold,
					sum(tmac.Rooms_Revenue) Room_Revenue
				from #temp_mkt_accom_activity tmac
				group by tmac.view_id, tmac.Mkt_Seg_ID
			) activity_type
		) as activity 
		left join
		(
			-- Get the Market Segment Occupancy Forecast
			select
				 fnATMS.Mkt_Seg_ID,
				 sum(Occupancy_NBR) as Occupancy_Forecast,
				 sum(Revenue) as Revenue_Forecast
				 from FN_AT_MS_Occupancy(@caughtupdate, 1) fnATMS
				 inner join Accom_Type att on fnATMS.Accom_Type_ID = att.Accom_Type_ID and att.isComponentRoom = 'N'
				 inner join #business_view_mkt_seg bvms on bvms.mkt_id = fnATMS.Mkt_Seg_ID
				 where fnATMS.Property_ID=@property_id and Occupancy_DT between @start_date and @end_date
				 group by fnATMS.Mkt_Seg_ID
		) as occ_forecast on occ_forecast.Mkt_Seg_ID = activity.Mkt_Seg_ID
	) mkt_seg_totals 
	inner join #business_view_mkt_seg bvms on bvms.mkt_id = mkt_seg_totals.Mkt_Seg_ID
	left join 
	(
		select
			View_ID,
			sum(Rooms_Sold_business_dt) Rooms_Sold_business_dt,
			sum(Rooms_Revenue_business_dt) Rooms_Revenue_business_dt
		from (
				select
				bvms.view_id,
				sum(pma.Rooms_Sold) Rooms_Sold_business_dt,
				sum(pma.Room_Revenue) Rooms_Revenue_business_dt
				from 
				PACE_Mkt_Activity pma 
				inner join #business_view_mkt_seg bvms on bvms.mkt_id = pma.Mkt_Seg_ID
				where pma.Property_Id=@property_id 
				and pma.Mkt_Seg_ID=bvms.mkt_id
				and pma.Business_Day_End_DT = @business_date
				and pma.Occupancy_DT between @start_date and @end_date
				and Occupancy_DT > @business_date
				group by bvms.view_id
				union all 
				select 
					View_ID,
					sum(Rooms_Sold) Rooms_Sold_business_dt,
					sum(Rooms_Revenue) Rooms_Revenue_business_dt
				from #temp_mkt_accom_activity
				where Occupancy_DT <= @business_date
				group by View_ID
		) temp group by View_ID
	) pace_activity on mkt_seg_totals.view_id = pace_activity.view_id 
	left join #Budget budget on budget.Business_Group_ID = bvms.view_id
	left join #UserForecast userForecast on userForecast.Business_Group_ID = bvms.view_id
	group by bvms.view_id, bvms.view_name, pace_activity.Rooms_Sold_business_dt, pace_activity.Rooms_Revenue_business_dt, 
		budget.Rooms_Sold, budget.Room_Revenue, budget.ADR, userForecast.Rooms_Sold, userForecast.Room_Revenue, userForecast.ADR
	order by bvms.view_name

	Drop table #business_view_mkt_seg
	Drop table #temp_mkt_accom_activity
	DROP TABLE #temp_mkt_seg
	DROP TABLE #Budget;
	DROP TABLE #UserForecast;
end