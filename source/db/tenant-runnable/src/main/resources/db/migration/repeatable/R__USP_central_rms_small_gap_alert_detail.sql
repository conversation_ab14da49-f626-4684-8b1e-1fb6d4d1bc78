DROP PROCEDURE IF EXISTS [dbo].[usp_central_rms_small_gap_alert_detail]
GO
CREATE Procedure [dbo].[usp_central_rms_small_gap_alert_detail]
	@Arrival_dt DATE
	,@Evaluation_Days INT = 90
	,@Gap DECIMAL(3,2) = 0.23
AS
BEGIN

	DECLARE @Arrival_Start_Date DATE = @Arrival_dt
	DECLARE @Arrival_End_Date DATE = DATEADD(dd, @Evaluation_Days - 1, @Arrival_dt)

	DROP TABLE IF EXISTS #CP_Cfg_Base_AT
	SELECT * INTO #CP_Cfg_Base_AT
	FROM CP_Cfg_Base_AT
	WHERE Product_ID = 1
		AND (
			(Start_Date IS NULL AND End_Date IS NULL)
			OR (Start_Date BETWEEN @Arrival_Start_Date AND @Arrival_End_Date)
			OR (End_Date BETWEEN @Arrival_Start_Date AND @Arrival_End_Date)
		)

	SELECT
		cp_cfg_base.Season_Name
		,cp_cfg_base.Start_Date
		,cp_cfg_base.End_Date
		,accom_class.Accom_Class_Name
		,CAST(accom_class.Master_Class AS BIT) master_class
		,accom_class.Rank_Order accom_class_rank_order
		,cp_cfg_base.Sunday_Ceil_Rate_w_Tax
		,cp_cfg_base.Sunday_Floor_Rate_w_Tax
		,((Sunday_Ceil_Rate_w_Tax - Sunday_Floor_Rate_w_Tax) / NULLIF(((Sunday_Ceil_Rate_w_Tax + Sunday_Floor_Rate_w_Tax) / 2), 0)) Sunday_gap
		,CAST(CASE WHEN ((Sunday_Ceil_Rate_w_Tax - Sunday_Floor_Rate_w_Tax) / NULLIF(((Sunday_Ceil_Rate_w_Tax + Sunday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Sunday_small_gap
		,cp_cfg_base.Monday_Ceil_Rate_w_Tax
		,cp_cfg_base.Monday_Floor_Rate_w_Tax
		,((Monday_Ceil_Rate_w_Tax - Monday_Floor_Rate_w_Tax) / NULLIF(((Monday_Ceil_Rate_w_Tax + Monday_Floor_Rate_w_Tax) / 2), 0)) Monday_gap
		,CAST(CASE WHEN ((Monday_Ceil_Rate_w_Tax - Monday_Floor_Rate_w_Tax) / NULLIF(((Monday_Ceil_Rate_w_Tax + Monday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Monday_small_gap
		,cp_cfg_base.Tuesday_Ceil_Rate_w_Tax
		,cp_cfg_base.Tuesday_Floor_Rate_w_Tax
		,((Tuesday_Ceil_Rate_w_Tax - Tuesday_Floor_Rate_w_Tax) / NULLIF(((Tuesday_Ceil_Rate_w_Tax + Tuesday_Floor_Rate_w_Tax) / 2), 0)) Tuesday_gap
		,CAST(CASE WHEN ((Tuesday_Ceil_Rate_w_Tax - Tuesday_Floor_Rate_w_Tax) / NULLIF(((Tuesday_Ceil_Rate_w_Tax + Tuesday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Tuesday_small_gap
		,cp_cfg_base.Wednesday_Ceil_Rate_w_Tax
		,cp_cfg_base.Wednesday_Floor_Rate_w_Tax
		,((Wednesday_Ceil_Rate_w_Tax - Wednesday_Floor_Rate_w_Tax) / NULLIF(((Wednesday_Ceil_Rate_w_Tax + Wednesday_Floor_Rate_w_Tax) / 2), 0)) Wednesday_gap
		,CAST(CASE WHEN ((Wednesday_Ceil_Rate_w_Tax - Wednesday_Floor_Rate_w_Tax) / NULLIF(((Wednesday_Ceil_Rate_w_Tax + Wednesday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Wednesday_small_gap
		,cp_cfg_base.Thursday_Ceil_Rate_w_Tax
		,cp_cfg_base.Thursday_Floor_Rate_w_Tax
		,((Thursday_Ceil_Rate_w_Tax - Thursday_Floor_Rate_w_Tax) / NULLIF(((Thursday_Ceil_Rate_w_Tax + Thursday_Floor_Rate_w_Tax) / 2), 0)) Thursday_gap
		,CAST(CASE WHEN ((Thursday_Ceil_Rate_w_Tax - Thursday_Floor_Rate_w_Tax) / NULLIF(((Thursday_Ceil_Rate_w_Tax + Thursday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Thursday_small_gap
		,cp_cfg_base.Friday_Ceil_Rate_w_Tax
		,cp_cfg_base.Friday_Floor_Rate_w_Tax
		,((Friday_Ceil_Rate_w_Tax - Friday_Floor_Rate_w_Tax) / NULLIF(((Friday_Ceil_Rate_w_Tax + Friday_Floor_Rate_w_Tax) / 2), 0)) Friday_gap
		,CAST(CASE WHEN ((Friday_Ceil_Rate_w_Tax - Friday_Floor_Rate_w_Tax) / NULLIF(((Friday_Ceil_Rate_w_Tax + Friday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Friday_small_gap
		,cp_cfg_base.Saturday_Ceil_Rate_w_Tax
		,cp_cfg_base.Saturday_Floor_Rate_w_Tax
		,((Saturday_Ceil_Rate_w_Tax - Saturday_Floor_Rate_w_Tax) / NULLIF(((Saturday_Ceil_Rate_w_Tax + Saturday_Floor_Rate_w_Tax) / 2), 0)) Saturday_gap
		,CAST(CASE WHEN ((Saturday_Ceil_Rate_w_Tax - Saturday_Floor_Rate_w_Tax) / NULLIF(((Saturday_Ceil_Rate_w_Tax + Saturday_Floor_Rate_w_Tax) / 2), 0)) < @Gap THEN 1 ELSE 0 END AS BIT) Saturday_small_gap
	FROM #CP_Cfg_Base_AT cp_cfg_base
		INNER JOIN Accom_Type accom_type ON cp_cfg_base.Accom_Type_ID = accom_type.Accom_Type_ID
	        AND accom_type.Display_Status_ID = 1
	        AND accom_type.isComponentRoom = 'N'
	        AND accom_type.Status_ID = 1
		INNER JOIN Accom_Class accom_class ON accom_class.Accom_Class_ID = accom_type.Accom_Class_ID
	        AND accom_class.Status_ID = 1
        INNER JOIN CP_Cfg_AC cp_cfg_ac ON cp_cfg_ac.Accom_Class_ID = accom_type.Accom_Class_ID
	        AND cp_cfg_ac.Accom_Type_ID = accom_type.Accom_Type_ID
	        AND cp_cfg_ac.Is_Price_Excluded = 0
	WHERE
		((Sunday_Ceil_Rate_w_Tax - Sunday_Floor_Rate_w_Tax) / NULLIF(((Sunday_Ceil_Rate_w_Tax + Sunday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Monday_Ceil_Rate_w_Tax - Monday_Floor_Rate_w_Tax) / NULLIF(((Monday_Ceil_Rate_w_Tax + Monday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Tuesday_Ceil_Rate_w_Tax - Tuesday_Floor_Rate_w_Tax) / NULLIF(((Tuesday_Ceil_Rate_w_Tax + Tuesday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Wednesday_Ceil_Rate_w_Tax - Wednesday_Floor_Rate_w_Tax) / NULLIF(((Wednesday_Ceil_Rate_w_Tax + Wednesday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Thursday_Ceil_Rate_w_Tax - Thursday_Floor_Rate_w_Tax) / NULLIF(((Thursday_Ceil_Rate_w_Tax + Thursday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Friday_Ceil_Rate_w_Tax - Friday_Floor_Rate_w_Tax) / NULLIF(((Friday_Ceil_Rate_w_Tax + Friday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
		OR ((Saturday_Ceil_Rate_w_Tax - Saturday_Floor_Rate_w_Tax) / NULLIF(((Saturday_Ceil_Rate_w_Tax + Saturday_Floor_Rate_w_Tax) / 2), 0)) < @Gap
END
GO

