
IF EXISTS(SELECT *
          FROM sys.objects
          WHERE object_id = object_id(N'[usp_ams_to_straight_ms_configuration]'))
    DROP PROCEDURE [usp_ams_to_straight_ms_configuration]
GO
/*******************************************************************************************************************************************

Store Procedure Name: usp_ams_to_straight_ms_configuration

Output : Populate configurations required for AMS to Straight MS.

Execution: this is just an example
	Example 1: usp_ams_to_straight_ms_configuration
	-----------------------------------------
	exec dbo.usp_ams_to_straight_ms_configuration

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
12/09/2024		Sachin 				Kale					Initial Version
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_ams_to_straight_ms_configuration]
AS
BEGIN
    declare @rev_id bigint;
    set @rev_id = (select max(id) from TetrisRevisionEntity)

    declare @straight_mkt_seg_codes table
                                    (
                                        mkt_code varchar(50)
                                    )
    insert into @straight_mkt_seg_codes
    SELECT distinct Market_Code
    FROM Analytical_Mkt_Seg
    WHERE Mapped_Market_Code LIKE '%!_%' ESCAPE '!';

    declare @split_mkt_seg_codes table
                                 (
                                     mkt_code varchar(50)
                                 )
    insert into @split_mkt_seg_codes
    SELECT distinct Mapped_Market_Code
    FROM Analytical_Mkt_Seg
    WHERE Mapped_Market_Code LIKE '%!_%' ESCAPE '!';


    --1. Insert split MS into Analytical_Mkt_Seg_AUD with rev_type 2
    insert into Analytical_Mkt_Seg_AUD
    SELECT Analytical_Mkt_Seg_ID,
           @rev_id,
           2,
           Market_Code,
           Rate_Code,
           Mapped_Market_Code,
           Attribute,
           Created_By_User_ID,
           Created_DTTM,
           Last_Updated_By_User_ID,
           Last_Updated_DTTM,
           Rate_Code_Type,
           Rank,
           complimentary,
           Preserved
    FROM Analytical_Mkt_Seg
    WHERE Mapped_Market_Code LIKE '%!_%' ESCAPE '!';

    --3.
    insert into AMS_Composition_Change
    select ams.Analytical_Mkt_Seg_ID, ms.Mkt_Seg_ID, 100, getdate(), 95, 1
    from Mkt_Seg ms
             join Analytical_Mkt_Seg ams on ams.Market_Code = ms.Mkt_Seg_Code
    WHERE ams.mapped_market_code in (select mkt_code from @straight_mkt_seg_codes)

    --4.
    insert into Mkt_seg_details_proposed
    select msd.Mkt_Seg_ID,msd.Business_Type_ID,msd.Yield_Type_ID,msd.Forecast_Activity_Type_ID,msd.Qualified,msd.Booking_Block_Pc,msd.Fenced,msd.Package,msd.Link,msd.Template_ID,msd.Template_Default,msd.Process_Status_ID,msd.Offset_Type_ID,msd.Offset_Value,msd.Status_ID,msd.Last_Updated_DTTM,msd.Priced_By_BAR,msd.Created_By_User_ID,msd.Created_DTTM,msd.Last_Updated_By_User_ID
    from Mkt_seg_details msd
             join Mkt_Seg ms on ms.Mkt_seg_ID = msd.Mkt_seg_ID
    where ms.mkt_seg_code in (select mkt_code from @split_mkt_seg_codes)

--5. delete split MS
    delete from Analytical_Mkt_Seg where mapped_market_Code in (select mkt_code from @split_mkt_seg_codes)

END;