drop procedure if exists [usp_rdd_load_basic_override_data_cp]
    GO
/****** Object:  StoredProcedure [dbo].[[usp_rdd_load_basic_override_data_cp]]    Script Date@ 3/21/2023 7:55:39 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_rdd_load_basic_override_data_cp] (
	 @propertyId INT
	,@startDate DATE
	,@endDate DATE
	,@productId Int
    ,@inventoryGroupId INT
    ,@accomTypeId INT
	)
AS
BEGIN
declare @AccomClassIds table(acId int);
declare @AccomTypeIds table(atId int);
if(@accomTypeId=-1)
    INSERT INTO @AccomTypeIds (atId) select Accom_Type_ID from Accom_Type;
else
    INSERT INTO @AccomTypeIds (atId) select Accom_Type_ID from Accom_Type where @accomTypeId=Accom_Type_ID;
if(@inventoryGroupId=-1)
   INSERT INTO @AccomClassIds (acId) select Accom_Class_id from Accom_Class where System_Default=0 and Status_ID=1;
else
   INSERT INTO @AccomClassIds (acId) select ac.Accom_Class_ID from Inventory_Group_Details invgd
                inner join Accom_Class ac on ac.Accom_Class_ID=invgd.Accom_Class_ID
                where Inventory_Group_ID=@inventoryGroupId;

With ValidAccomTypes as (
    select Accom_Type_Id as Id, accom_class_id from Accom_Type
    where accom_type_capacity > 0 and status_id = 1 and display_status_id = 1 and System_Default = 0
    and Accom_Type_ID in (select atId from @AccomTypeIds)
    and Accom_Class_ID in (select acId from @AccomClassIds)
),
 ValidCpDecisionBarOutput as (
     select cdbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, cdbo.Product_ID, cdbo.Override,
            cast(cdbo.User_Specified_Rate as Numeric(19,2)) as User_Specified_Rate,
            cast(cdbo.Ceil_Rate as Numeric(19,2)) as Ceil_Rate,
            cast(cdbo.Floor_Rate as Numeric(19,2)) as Floor_Rate,
            cdbo.Final_BAR,
            cdbo.Accom_Type_ID,
            ROW_NUMBER() OVER ( PARTITION BY cdbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, cdbo.Override
                    ORDER BY cdbo.Arrival_DT, ac.Accom_Class_ID, ac.Accom_Class_Name, cdbo.Override) row_num
     from  CP_Decision_Bar_Output cdbo
               inner join ValidAccomTypes vat on cdbo.Accom_Type_ID=vat.Id
               inner join Accom_Class ac on ac.Accom_Class_ID=vat.Accom_Class_ID and ac.Status_ID=1 and ac.System_Default=0
     where cdbo.Property_ID=@propertyId and cdbo.Arrival_DT between @startDate and @endDate and cdbo.Product_ID=@productId and cdbo.LOS in (1,-1)
     order by ac.View_Order offset 0 Rows
 )
    select Occupancy_DT,
           barRestricted.accom_classes as LRV_Greater_AC,
           userOvr.accom_classes_with_rate as UserOvr_AC_With_Rate,
           floorOvr.accom_classes_with_rate as FloorOvr_AC_With_Rate,
           ceilOvr.accom_classes_with_rate Ceil_Ovr_AC_With_Rate,
           gpfloorOvr.accom_classes_with_rate GpFloor_AC_With_Rate
    from
        (
            select CAST(calendar_date as date) Occupancy_DT
            from calendar_dim where calendar_date between @startDate and @endDate
        )base
            left join
        (
            select Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),bar.Accom_Class_Name), CHAR(10)) accom_classes from
                (select distinct Arrival_DT, ac.Accom_Class_Name, ac.View_Order from CP_Decision_Bar_Output vcdbo
                    inner join Accom_Type at on at.Accom_Type_ID=vcdbo.Accom_Type_ID
                    inner join Accom_Class ac on ac.Accom_Class_ID = at.Accom_Class_ID
                 where vcdbo.Decision_Reason_Type_ID = 2
                 order by ac.View_Order offset 0 Rows) bar group by bar.Arrival_DT
        ) barRestricted on base.Occupancy_DT=barRestricted.Arrival_DT
            left join
        (
            select barOutput.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),CONCAT(barOutput.Accom_Class_Name, '-', isnull(baseRTBarOutput.User_Specified_rate,barOutput.User_Specified_rate))), CHAR(10)) accom_classes_with_rate
             from
                 (select Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Accom_Class_Name, vcdbo.Override, User_Specified_rate from ValidCpDecisionBarOutput vcdbo
                  where vcdbo.Override like '%USER%' and vcdbo.row_num = 1) barOutput
                     left join
                 (select distinct Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Override, User_Specified_rate from ValidCpDecisionBarOutput vcdbo
                                                                                                                 inner join CP_Cfg_AC cpcfg on cpcfg.Accom_Class_ID = vcdbo.Accom_Class_ID and cpcfg.Accom_Type_ID=vcdbo.Accom_Type_ID
                  where vcdbo.Override like '%USER%') baseRTBarOutput
                 on barOutput.Accom_Class_Id=baseRTBarOutput.Accom_Class_ID and barOutput.Override=baseRTBarOutput.Override and barOutput.Arrival_DT=baseRTBarOutput.Arrival_DT
             group by barOutput.Arrival_DT ) as userOvr on Occupancy_DT = userOvr.Arrival_DT
            left join
        (select barOutput.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),CONCAT(barOutput.Accom_Class_Name, '-', isnull(baseRTBarOutput.Ceil_Rate, barOutput.Ceil_Rate))), CHAR(10)) accom_classes_with_rate from
            (select Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Accom_Class_Name, vcdbo.Override, Ceil_Rate from ValidCpDecisionBarOutput vcdbo
             where vcdbo.Override like '%CEIL%' and vcdbo.row_num = 1) barOutput
                left join
            (select  distinct Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Override, Ceil_Rate  from ValidCpDecisionBarOutput vcdbo
                                                                                                    inner join CP_Cfg_AC cpcfg on cpcfg.Accom_Class_ID = vcdbo.Accom_Class_ID and cpcfg.Accom_Type_ID=vcdbo.Accom_Type_ID
             where vcdbo.Override like '%CEIL%' ) baseRTBarOutput
            on barOutput.Accom_Class_Id=baseRTBarOutput.Accom_Class_ID and barOutput.Override=baseRTBarOutput.Override and barOutput.Arrival_DT=baseRTBarOutput.Arrival_DT
         group by barOutput.Arrival_DT ) as ceilOvr on Occupancy_DT = ceilOvr.Arrival_DT
            left join
        (select barOutput.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),CONCAT(barOutput.Accom_Class_Name, '-', isnull(baseRTBarOutput.Floor_Rate,barOutput.Floor_Rate))), CHAR(10)) accom_classes_with_rate from
            (select Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Accom_Class_Name, vcdbo.Override, Floor_Rate from ValidCpDecisionBarOutput vcdbo
             where vcdbo.Override like 'FLOOR%' and vcdbo.row_num = 1) barOutput
                left join
            (select distinct Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Override, Floor_Rate from ValidCpDecisionBarOutput vcdbo
                                                                                                   inner join CP_Cfg_AC cpcfg on cpcfg.Accom_Class_ID = vcdbo.Accom_Class_ID and cpcfg.Accom_Type_ID=vcdbo.Accom_Type_ID
             where vcdbo.Override like 'FLOOR%') baseRTBarOutput
            on barOutput.Accom_Class_Id=baseRTBarOutput.Accom_Class_ID and barOutput.Override=baseRTBarOutput.Override and barOutput.Arrival_DT=baseRTBarOutput.Arrival_DT
         group by barOutput.Arrival_DT ) as floorOvr on Occupancy_DT = floorOvr.Arrival_DT
            left join
        (select barOutput.Arrival_DT, STRING_AGG(CONVERT(NVARCHAR(max),CONCAT(barOutput.Accom_Class_Name, '-', isnull(baseRTBarOutput.Floor_Rate,barOutput.Floor_Rate))), CHAR(10)) accom_classes_with_rate from
            (select b.Arrival_DT, b.Accom_Class_Name, b.Accom_Class_ID, b.Override, Floor_Rate from
                (select Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Accom_Class_Name, vcdbo.Override, vcdbo.Floor_Rate, row_num, vcdbo.Accom_Type_ID, Final_BAR
                 from ValidCpDecisionBarOutput vcdbo where vcdbo.Override like '%GPFLOOR%' and row_num = 1) b
                    inner join
                (
                    select Arrival_DT,New_Floor_Rate,Accom_Type_ID from CP_Decision_Bar_Output_Ovr -- replicating from bad_indicator condition
                    where Property_ID=@propertyId and product_id=@productId and New_Override like '%GPFLOOR%' and Arrival_DT between @startDate and @endDate and LOS in (1,-1)
                ) c on c.Arrival_DT=b.Arrival_DT and b.Accom_Type_ID=c.Accom_Type_ID  and c.New_Floor_Rate=b.Final_BAR
            ) barOutput
                left join
            (select distinct Arrival_DT, vcdbo.Accom_Class_ID, vcdbo.Override, Floor_Rate from ValidCpDecisionBarOutput vcdbo
                                                                                                   inner join CP_Cfg_AC cpcfg on cpcfg.Accom_Class_ID = vcdbo.Accom_Class_ID and cpcfg.Accom_Type_ID=vcdbo.Accom_Type_ID
             where vcdbo.Override like '%GPFLOOR%') baseRTBarOutput
            on barOutput.Accom_Class_Id=baseRTBarOutput.Accom_Class_ID and barOutput.Override=baseRTBarOutput.Override and barOutput.Arrival_DT=baseRTBarOutput.Arrival_DT
         group by barOutput.Arrival_DT ) as gpfloorOvr on Occupancy_DT = gpfloorOvr.Arrival_DT
    End