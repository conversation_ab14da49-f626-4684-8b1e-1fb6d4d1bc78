DROP PROCEDURE IF EXISTS [dbo].[usp_CP_Decision_Bar_Output_Upsert]
GO

DROP TYPE IF EXISTS [dbo].[CP_Decision_Bar_Output_Batch]

CREATE TYPE dbo.CP_Decision_Bar_Output_Batch AS TABLE
(

    [Property_ID]             int,
    [Decision_ID]             bigint,
    [Product_ID]              bigint,
    [Decision_Reason_Type_ID] int,
    [Accom_Type_ID]           int,
    [Arrival_DT]              date,
    [LOS]                     int,
    [Optimal_BAR]             float,
    [Pretty_BAR]              numeric(19, 5),
    [Override]                nvarchar(50),
    [Floor_Rate]              float,
    [Ceil_Rate]               float,
    [User_Specified_Rate]     float,
    [CreateDate]              datetime,
    [Final_BAR]               numeric(19, 5),
    [Rooms_Only_BAR]          numeric(19, 5),
    [Previous_BAR]            numeric(19, 2),
    [Optimal_BAR_Type]        int,
    [Adjustment_Value]        numeric(19, 5)

);
GO
GO

CREATE PROCEDURE usp_CP_Decision_Bar_Output_Upsert @CP_Decision_Bar_Output_Batch CP_Decision_Bar_Output_Batch READONLY
AS
BEGIN
    MERGE INTO [dbo].[CP_Decision_Bar_Output] AS Target
    USING @CP_Decision_Bar_Output_Batch AS Source
    ON (Target.[Arrival_DT] = Source.[Arrival_DT]
        AND Target.[Product_ID] = Source.[Product_ID]
        AND Target.[Accom_Type_ID] = Source.[Accom_Type_ID]
        AND Target.[LOS] = Source.[LOS]
        AND Target.[Property_ID] = Source.[Property_ID])
    WHEN MATCHED THEN
        UPDATE
        SET Target.[Decision_ID]             = Source.[Decision_ID],
            Target.[Decision_Reason_Type_ID] = Source.[Decision_Reason_Type_ID],
            Target.[Optimal_BAR]             = Source.[Optimal_BAR],
            Target.[Pretty_BAR]              = Source.[Pretty_BAR],
            Target.[Override]                = Source.[Override],
            Target.[Floor_Rate]              = Source.[Floor_Rate],
            Target.[Ceil_Rate]               = Source.[Ceil_Rate],
            Target.[User_Specified_Rate]     = Source.[User_Specified_Rate],
            Target.[CreateDate]              = Source.[CreateDate],
            Target.[Final_BAR]               = Source.[Final_BAR],
            Target.[Rooms_Only_BAR]          = Source.[Rooms_Only_BAR],
            Target.[Previous_BAR]            = Source.[Previous_BAR],
            Target.[Optimal_BAR_Type]        = Source.[Optimal_BAR_Type],
            Target.[Adjustment_Value]        = Source.[Adjustment_Value]

    WHEN NOT MATCHED THEN
        INSERT ([Property_ID], [Decision_ID], [Product_ID], [Decision_Reason_Type_ID], [Accom_Type_ID], [Arrival_DT],
                [LOS], [Optimal_BAR], [Pretty_BAR], [Override], [Floor_Rate], [Ceil_Rate], [User_Specified_Rate],
                [CreateDate], [Final_BAR], [Rooms_Only_BAR], [Previous_BAR], [Optimal_BAR_Type], [Adjustment_Value])
        VALUES (Source.[Property_ID], Source.[Decision_ID], Source.[Product_ID], Source.[Decision_Reason_Type_ID],
                Source.[Accom_Type_ID], Source.[Arrival_DT], Source.[LOS], Source.[Optimal_BAR], Source.[Pretty_BAR],
                Source.[Override], Source.[Floor_Rate], Source.[Ceil_Rate], Source.[User_Specified_Rate],
                Source.[CreateDate], Source.[Final_BAR], Source.[Rooms_Only_BAR], Source.[Previous_BAR],
                Source.[Optimal_BAR_Type], Source.[Adjustment_Value]);
END
GO




