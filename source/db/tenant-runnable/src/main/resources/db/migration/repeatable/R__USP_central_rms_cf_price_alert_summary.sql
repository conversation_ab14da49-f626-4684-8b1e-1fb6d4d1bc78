DROP PROCEDURE IF EXISTS [dbo].[usp_central_rms_cf_price_alert_summary]
GO
CREATE Procedure [dbo].[usp_central_rms_cf_price_alert_summary]
	@Arrival_dt DATE
	,@Evaluation_Days INT = 90
AS
BEGIN

	DECLARE @Arrival_Start_Date DATE = @Arrival_dt
	DECLARE @Arrival_End_Date DATE = DATEADD(dd, @Evaluation_Days - 1, @Arrival_dt)

	DECLARE @Evaluation_Dates TABLE (Arrival_DT DATE)

	INSERT INTO @Evaluation_Dates
	SELECT CAST(calendar_date AS DATE)
	FROM Calendar_Dim
	WHERE calendar_date BETWEEN @Arrival_Start_Date AND @Arrival_End_Date

	DECLARE @Central_RMS_Price_Alert_Detail TABLE (
		Arrival_DT DATE
		,accom_class_id INT
		,accom_class_name NVARCHAR(150)
		,is_master_class BIT
		,override NVARCHAR(20)
		,season_name <PERSON>VA<PERSON><PERSON><PERSON>(255)
		,Final_BAR DECIMAL(19, 2)
		,Effective_Floor_Rate DECIMAL(19, 2)
		,Effective_Ceil_Rate DECIMAL(19, 2)
		,Hist_BAR_ADR_Lower DECIMAL(19,2)
		,Hist_BAR_ADR_Upper DECIMAL(19,2)
		,Min_Adj_Comp_Price DECIMAL(19,2)
		,Max_Adj_Comp_Price DECIMAL(19,2)
		,ADR_inconsistent BIT
		,Competitor_inconsistent BIT
		,LRV DECIMAL(19,5)
		,Occupancy_Fcst_Pct DECIMAL(19, 2)
		,Floor BIT
		,Ceil BIT
	)

	INSERT @Central_RMS_Price_Alert_Detail EXEC [dbo].[usp_central_rms_cf_price_alert_detail] @Arrival_dt = @Arrival_dt, @Evaluation_Days = @Evaluation_Days

	SELECT central_rms_detail.accom_class_name
		,central_rms_detail.is_master_class
		,datename(dw, central_rms_detail.Arrival_DT) AS DOW
        ,sum(cast(central_rms_detail.Ceil as INT)) ceil_total
        ,sum(cast(central_rms_detail.Floor as INT)) floor_total
	FROM
		@Evaluation_Dates eval
		INNER JOIN @Central_RMS_Price_Alert_Detail central_rms_detail
		    ON central_rms_detail.Arrival_DT = eval.Arrival_DT
	GROUP BY GROUPING SETS (
		(),
		(central_rms_detail.accom_class_name, central_rms_detail.is_master_class),
		(central_rms_detail.accom_class_name, central_rms_detail.is_master_class, datename(dw, central_rms_detail.Arrival_DT), central_rms_detail.ceil, central_rms_detail.floor)
	)
END
GO
