IF EXISTS
(
    SELECT * FROM sys.objects WHERE object_id = object_id(N'[usp_get_web_competitors_rate_by_rc]')
)
    DROP PROCEDURE [dbo].[usp_get_web_competitors_rate_by_rc]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_get_web_competitors_rate_by_rc]
    @property_id INT,
    @webrate_comp_id INT,
    @start_date DATE,
    @end_date DATE,
    @RoomClasses NVARCHAR(200)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @webrateTypeID INT;
    SET @webrateTypeID =
    (
        SELECT TOP 1
            wtp.Webrate_Type_ID
        FROM Webrate_Type_Product wtp
        WHERE wtp.Product_ID = 1
    );

    DECLARE @web_rate_comp TABLE
    (
        property_id INT,
        accom_Class_id INT,
        occupancy_dt DATE,
        webrate_currency NVARCHAR(50),
        webrate_ratevalue NUMERIC(19, 5),
        webrate_competitors_name NVARCHAR(150)
    );

    INSERT INTO @web_rate_comp
    SELECT name.property_id,
           name.accom_Class_id,
           name.occupancy_dt,
           webrate_currency,
           CAST(webrate_ratevalue_display AS NUMERIC(19, 2)),
           name.webrate_competitors_name
    FROM
    (
        SELECT a.property_id,
               a.Webrate_Competitors_Name,
               b.Occupancy_DT,
               Accom_Class_id,
               a.Webrate_Competitors_ID
        FROM
        (
            SELECT @property_id AS property_id,
                   webrate_competitors_name,
                   Accom_Class_id,
                   Webrate_Competitors_ID
            FROM Webrate_Competitors
                INNER JOIN
                (
                    SELECT Accom_Class_id
                    FROM Accom_Class
                    WHERE Status_ID = 1
                          AND System_Default = 0
                          AND Accom_Class_id IN (
                                                    SELECT Value FROM varcharToInt(@RoomClasses, ',')
                                                )
                ) acc
                    ON property_id = @property_id
            WHERE Webrate_Competitors_ID = @webrate_comp_id
                  AND status_id IN ( 1, 2, 3 )
        ) a
            LEFT JOIN
            (
                SELECT Property_ID,
                       Occupancy_DT
                FROM Total_Activity
                WHERE Property_ID = @property_id
                      AND Occupancy_DT
                      BETWEEN @start_date AND @end_date
            ) b
                ON a.property_id = b.Property_ID
    ) name
        LEFT JOIN
        (
            SELECT *
            FROM dbo.vw_webrate_channel
            WHERE occupancy_dt
                  BETWEEN @start_date AND @end_date
                  AND property_id = @property_id
        ) channel
            ON name.property_id = channel.property_id
               AND name.Occupancy_DT = channel.Occupancy_DT
        LEFT JOIN
        (
            SELECT *
            FROM
            (
                SELECT webrate_competitors_id,
                       webrate_channel_id,
                       occupancy_dt,
                       webrate_currency,
                       Accom_Class_ID,
                       webrate_ratevalue_display,
                       ROW_NUMBER() OVER (PARTITION BY webrate_competitors_id,
                                                       webrate_channel_id,
                                                       occupancy_dt,
                                                       Accom_Class_ID
                                          ORDER BY webrate_competitors_id,
                                                   webrate_channel_id,
                                                   occupancy_dt,
                                                   Accom_Class_ID,
                                                   Webrate_GenerationDate DESC,
                                                   webrate_ratevalue_display ASC
                                         ) AS rowNUm
                FROM dbo.webrate vweb
                    LEFT JOIN ACCom_CLass acc
                        ON acc.Accom_Class_ID IN (
                                                     SELECT Accom_Class_id
                                                     FROM Accom_Class
                                                     WHERE Status_ID = 1
                                                           AND System_Default = 0
                                                           AND Accom_Class_id IN (
                                                                                     SELECT Value FROM varcharToInt(
                                                                                                                       @RoomClasses,
                                                                                                                       ','
                                                                                                                   )
                                                                                 )
                                                 )
                WHERE webrate_competitors_id = @webrate_comp_id
                      AND occupancy_dt
                      BETWEEN @start_date AND @end_date
                      AND LOS = 1
                      AND Webrate_status = 'A'
                      AND Webrate_Accom_Type_ID IN (
                                                       SELECT webT.Webrate_Accom_Type_ID
                                                       FROM webrate_accom_type webT
                                                           INNER JOIN Webrate_Accom_Class_Mapping webM
                                                               ON webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID
                                                                  AND webM.Accom_Class_ID = acc.Accom_Class_id
                                                                  AND webT.property_ID = @property_id
                                                   )
                      AND (
                              @webrateTypeID IS NULL
                              OR Webrate_Type_ID = @webrateTypeID
                          )
            ) AS der_webRate
            WHERE rowNUm = 1
        ) webrate_info
            ON channel.occupancy_dt = webrate_info.occupancy_dt
               AND channel.channel_id = webrate_info.webrate_channel_id
               AND name.Accom_Class_ID = webrate_info.Accom_Class_ID
    ORDER BY name.property_id,
             name.occupancy_dt;
    SELECT * FROM @web_rate_comp;
    RETURN;
END
GO
