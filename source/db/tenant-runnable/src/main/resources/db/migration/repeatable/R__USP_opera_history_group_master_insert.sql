DROP PROCEDURE IF EXISTS [opera].USP_History_Group_Master_Insert
GO
CREATE PROCEDURE [opera].USP_History_Group_Master_Insert
	@History_Group_Master_Batch [opera].History_Group_Master_Batch READONLY
AS
BEGIN
	INSERT INTO [opera].[History_Group_Master] 
	([Hotel_Code], [Group_ID], [Block_Code], [Group_Name], [Master_Group_ID], [Status], [Market_Segment], 
	[Arrival_DT], [Departure_DT], [Insert_DT], [Group_Type], [Data_Load_Metadata_ID])
	SELECT 
	[Hotel_Code], [Group_ID], [Block_Code], [Group_Name], [Master_Group_ID], [Status], [Market_segment], 
	[Arrival_DT], [Departure_DT], [Insert_DT], [Group_Type], [Data_Load_Metadata_ID] 
	FROM 
	@History_Group_Master_Batch
END
GO