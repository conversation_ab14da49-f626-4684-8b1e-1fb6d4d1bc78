DROP PROCEDURE IF EXISTS
[dbo].[DQI_Future_BAR_Price_Sequence]
GO
CREATE PROCEDURE [dbo].[DQI_Future_BAR_Price_Sequence] @property_ID INT, @system_date Date, @forecast_window INT, @DQI_ID as int, @detail_analysis as CHAR(1) = 'N'
AS
BEGIN
	DECLARE @forecast_window_date AS DATE = DATEADD(day,@forecast_window-1,@system_date)
	DECLARE @actual_days_analysed AS INT

	IF OBJECT_ID('tempdb..#Table_RankByValue') IS NOT NULL
	BEGIN
		DROP TABLE #Table_RankByValue
	END

	/** For each DOW find out the realistic ranking by looking @ the rate value **/
		select ru.property_ID, CAST(calendar_dim.calendar_date as DATE) as Occupancy_Date, rud.accom_Type_ID, rud.rate_unqualified_id, ru.ranking_level, 
		CASE datepart(dw, CAST(calendar_dim.calendar_date as DATE))
					when 1 then rud.Sunday
					when 2 then rud.Monday
					when 3 then rud.Tuesday
					when 4 then rud.Wednesday
					when 5 then rud.Thursday
					when 6 then rud.Friday
					when 7 then rud.Saturday
					end
				BAR_Value
		INTO #Table_RankByValue	
		from Rate_Unqualified ru 
		inner join rate_unqualified_details rud ON ru.rate_unqualified_id = rud.Rate_Unqualified_ID 
		INNER join  Accom_Type at ON ru.Property_ID = at.Property_ID AND  at.accom_Type_ID = rud.accom_Type_ID
		inner join calendar_dim ON CAST(calendar_dim.calendar_date as DATE) between rud.Start_Date_DT AND rud.End_Date_DT
		WHERE ru.property_ID = @property_ID AND ru.Rate_Code_Name != 'None' AND ru.Yieldable=1 AND ru.status_ID = 1 and ru.system_default = 0 
		AND at.Accom_Type_Capacity!=0
		AND CAST(calendar_dim.calendar_date as DATE) between @system_date and @forecast_window_date
		AND CAST(calendar_dim.calendar_date as DATE) NOT IN 
		(
			select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
			INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
			and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
			WHERE ip_cfg_mark_property_date.property_ID=@property_ID AND cast(calendar_dim.calendar_date as date) between @system_date and @forecast_window_date
		)
		order by property_ID, Occupancy_Date, accom_type_id, BAR_Value desc, ru.ranking_level

	IF OBJECT_ID('tempdb..#Table_RankAsExpected') IS NOT NULL
	BEGIN
		DROP TABLE #Table_RankAsExpected
	END

	/** For each DOW get the expected ranking by looking @ the ranking level defined **/

	SELECT ru.property_ID, rate_unqualified_ID, ranking_level
	INTO #Table_RankAsExpected
	 FROM
	Rate_Unqualified ru
	WHERE ru.property_ID = @property_ID AND Rate_Code_Name != 'None'  AND ru.Yieldable=1 AND ru.status_ID = 1 and ru.system_default = 0 
	order by ru.[property_ID], ranking_level

	DECLARE @expected_ranking_level_pattern AS VARCHAR(1000)

	SELECT @expected_ranking_level_pattern=	( 
			SELECT CAST(ranking_level AS NVARCHAR(1000)) + ',' 
			FROM #Table_RankAsExpected AS intern 
			WHERE extern.property_ID = intern.property_ID 
			FOR XML PATH('') 
		)
		FROM #Table_RankAsExpected AS extern  
		GROUP BY property_ID

	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END

	SELECT actualPattern.property_ID, actualPattern.Occupancy_Date as arrival_date, actualPattern.accom_Type_id,
	actualPattern.ranking_level_pattern as actualPattern, @expected_ranking_level_pattern as expectedPattern
	INTO #Table_Final
	FROM
	(
		SELECT property_ID, Occupancy_Date, accom_Type_id,(
			SELECT CAST(ranking_level AS NVARCHAR(1000)) + ',' 
			FROM #Table_RankByValue AS intern 
			WHERE extern.property_ID = intern.property_ID AND
			extern.Occupancy_Date = intern.Occupancy_Date 
			AND extern.accom_Type_id=intern.accom_Type_id
			order by property_ID, Occupancy_Date, accom_type_id, BAR_Value desc
			FOR XML PATH('') 
		)ranking_level_pattern 
		FROM #Table_RankByValue AS extern 
		GROUP BY property_ID, Occupancy_Date, accom_Type_id
	) as actualPattern
	where actualPattern.ranking_level_pattern!=@expected_ranking_level_pattern
/** FOR DETAIL ANALYSIS**/
if (@detail_analysis)='Y'
	select * from #Table_Final ORDER BY property_ID, arrival_date, accom_Type_id

	SELECT @actual_days_analysed=count(*) 
	FROM 
	(SELECT distinct property_ID, Occupancy_Date FROM #Table_RankByValue) as innerTable


	if exists (select top 1 property_ID FROM #Table_Final)
	BEGIN
		SELECT @DQI_ID as DQI_ID, property_ID, @actual_days_analysed as actual_days_analysed, count_of_failed_days,
		case when count_of_failed_days >= .05 * @actual_days_analysed then 'RED' 
		else 
			case when count_of_failed_days > 0 then 'YELLOW'
			else 'GREEN'
			end
		end AS indicator
		FROM
		(
			Select property_ID, count(*) as count_of_failed_days
			 FROM
			(
					SELECT #Table_Final.property_ID, #Table_Final.arrival_date
					FROM #Table_Final		
					group by #Table_Final.property_ID, #Table_Final.arrival_date
				) as FinalTable_Inner
			Group By property_ID
		) as FinalTable_Outer
	END
	ELSE
	BEGIN
		IF exists( select top 1 property_ID FROM #Table_RankAsExpected)
		BEGIN
			select @DQI_ID as DQI_ID, @property_ID as property_ID, @actual_days_analysed as actual_days_analysed, 0 as count_of_failed_days, 'GREEN' as indicator
		END
		ELSE
		BEGIN
			select @DQI_ID as DQI_ID, @property_ID as property_ID, @actual_days_analysed as actual_days_analysed, 0 as count_of_failed_days, 'RED' as indicator
		END
	END

	IF OBJECT_ID('tempdb..#Table_RankByValue') IS NOT NULL
	BEGIN
		DROP TABLE #Table_RankByValue
	END

	IF OBJECT_ID('tempdb..#Table_RankAsExpected') IS NOT NULL
	BEGIN
		DROP TABLE #Table_RankAsExpected
	END

	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
end
GO


