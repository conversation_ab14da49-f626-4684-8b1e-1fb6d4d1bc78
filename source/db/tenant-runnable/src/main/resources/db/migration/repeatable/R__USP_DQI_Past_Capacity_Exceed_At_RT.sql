DROP PROCEDURE IF EXISTS [dbo].[DQI_Past_Capacity_Exceed_At_RT]
GO
CREATE PROCEDURE [dbo].[DQI_Past_Capacity_Exceed_At_RT] @property_ID INT, @system_date Date, @DQI_ID as int, @configurableNoOfYears as int, @detail_analysis as CHAR(1) = 'N'
AS
BEGIN

	DECLARE @percent_of_deviation AS Numeric(5,2)= 5
	DECLARE @percent_of_deviation_actual AS Numeric(5,2)= @percent_of_deviation/100
	DECLARE @no_of_history_days AS Integer
	DECLARE @no_of_room_class AS Integer
	DECLARE @past_years_date AS DATE = DATEADD(D, @configurableNoOfYears*365*-1, @system_date)
		
	select @no_of_room_class=count(*) from (
	select Distinct Accom_Class.* from Accom_Class
	inner join Accom_Type
	on Accom_Class.Accom_Class_ID = Accom_Type.Accom_Class_ID
	and Accom_Class.Property_ID = Accom_Type.Property_ID
	where Accom_Class.Property_ID = @property_ID and Accom_Class.Status_ID = 1 
	and Accom_Class.System_Default = 0
	) as ActiveRCTable

	IF(@no_of_room_class=1)
	BEGIN
		SELECT  @DQI_ID as DQI_ID, @property_ID as property_ID, 0, 0 as count_failed_days, 'GREEN' as indicator
	END
	ELSE
	BEGIN
	SELECT @no_of_history_days=COUNT(*)
	FROM
	(
		Select property_ID, occupancy_DT FROM Accom_Activity WHERE property_ID = @property_ID AND occupancy_DT between @past_years_date and DATEADD(d, -1, @system_date) 
		AND occupancy_DT NOT IN 
		(
			select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
			INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
			and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
			WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND cast(calendar_dim.calendar_date as date) between @past_years_date and DATEADD(d, -1, @system_date) 
			and ip_cfg_mark_property_date.Status_ID = 1
		)	
		Group by Property_ID, Occupancy_DT
	) as OuterTable
	Group by property_ID

	/** accom activity - past data cleanliness **/
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	
	/** If Detailed Analysis, execute the if part */
	if (@detail_analysis)='Y'
	begin
		IF OBJECT_ID('tempdb..#Table_Final_Details_AllData') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Final_Details_AllData
		END
		IF OBJECT_ID('tempdb..#Table_Final_Details') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Final_Details
		END
		
		select property_ID, Occupancy_DT, Accom_Type_ID, Rooms_Sold ,(Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) as available_capacity,
		(rooms_Sold - (Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other)) as descrepancy_solds_if_postive_value, @percent_of_deviation_actual*Rooms_Sold as five_percent_of_solds,
		CASE WHEN @percent_of_deviation_actual*Rooms_Sold > 2 then @percent_of_deviation_actual*Rooms_Sold else 2 end as five_percent_of_solds_logical
		INTO #Table_Final_Details_AllData
		From Accom_Activity
		where  property_ID = @property_ID AND Accom_Capacity!=0 AND occupancy_DT between @past_years_date and DATEADD(d, -1, @system_date)
		AND occupancy_DT NOT IN
		(
				select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
				INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
				and IP_Cfg_Mark_Property_Date.IP_CFG_DATA_TYPE_ID in (1,2)
				WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND occupancy_DT  between @past_years_date and DATEADD(d, -1, @system_date) 
				and ip_cfg_mark_property_date.Status_ID = 1
		)
		
		select * into #Table_Final_Details from #Table_Final_Details_AllData where 
			descrepancy_solds_if_postive_value > CASE WHEN @percent_of_deviation_actual*Rooms_Sold > 2 then @percent_of_deviation_actual*Rooms_Sold else 2 end    
		
		select Property.Property_Code,Accom_Type.Accom_Type_Code,#Table_Final_Details_AllData.*, 
		case when @percent_of_deviation_actual*Rooms_Sold > 2 and descrepancy_solds_if_postive_value > @percent_of_deviation_actual*Rooms_Sold
			then 'Y' 
			when 2>= @percent_of_deviation_actual*Rooms_Sold and descrepancy_solds_if_postive_value > 2 then 'Y'	
			else 'N'		  
		end as error
		from #Table_Final_Details_AllData 
		inner join Property on #Table_Final_Details_AllData.Property_ID = Property.Property_ID
		inner join Accom_Type on Accom_Type.Property_ID = Property.Property_ID and Accom_Type.Accom_Type_ID = #Table_Final_Details_AllData.Accom_Type_ID
		order by Property.Property_ID, Occupancy_DT, Accom_Type_ID	
				
		if exists( select top 1 property_ID from #Table_Final_Details)
		BEGIN
			SELECT @DQI_ID as DQI_ID, property_ID, @no_of_history_days as actual_days_analysed, count_failed_days, 
			case when count_failed_days > .1*@no_of_history_days then 'RED' else case when count_failed_days > .02*@no_of_history_days then 'YELLOW' else 'GREEN' end end
			as indicator
			FROM
			(
				SELECT property_ID, COUNT(*) as count_failed_days
				FROM 
				(
					SELECT property_ID, occupancy_DT
					FROM #Table_Final_Details
					group by property_ID, Occupancy_DT
				) as outerTable
				group by Property_ID
			) as outermostTable
		end
		else
		begin
			SELECT  @DQI_ID as DQI_ID, @property_ID as property_ID, CASE WHEN @no_of_history_days IS NULL THEN 0 ELSE  @no_of_history_days  END as actual_days_analysed, 0 as count_failed_days, CASE WHEN @no_of_history_days IS NULL THEN 'GRAY' ELSE  'GREEN'  END as indicator
		end	 
		
		IF OBJECT_ID('tempdb..#Table_Final_Details_AllData') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Final_Details_AllData
		END
		IF OBJECT_ID('tempdb..#Table_Final_Details') IS NOT NULL
		BEGIN
			DROP TABLE #Table_Final_Details
		END
		
	end
	else
	begin
	
	select property_ID, Occupancy_DT, Accom_Type_ID, Rooms_Sold ,(Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) as available_capacity,
	(rooms_Sold - (Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other)) as descrepancy_solds_if_postive_value, @percent_of_deviation_actual*Rooms_Sold as five_percent_of_solds,
	CASE WHEN @percent_of_deviation_actual*Rooms_Sold > 2 then @percent_of_deviation_actual*Rooms_Sold else 2 end as five_percent_of_solds_logical
	INTO #Table_Final
	From Accom_Activity
	where  property_ID = @property_ID AND Accom_Capacity!=0 AND occupancy_DT between @past_years_date and DATEADD(d, -1, @system_date)
	AND occupancy_DT NOT IN
	(
			select cast(calendar_dim.calendar_date as date) from ip_cfg_mark_property_date
			INNER JOIN calendar_dim ON cast(calendar_dim.calendar_date as date) between start_date and end_date
			WHERE ip_cfg_mark_property_date.Property_ID=@property_ID AND occupancy_DT  between @past_years_date and DATEADD(d, -1, @system_date) 
			and ip_cfg_mark_property_date.Status_ID = 1
	)	
	 
	AND 
	(rooms_Sold - (Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other)) > CASE WHEN @percent_of_deviation_actual*Rooms_Sold > 2 then @percent_of_deviation_actual*Rooms_Sold else 2 end 

	if exists( select top 1 property_ID from #Table_Final)
	BEGIN
		SELECT @DQI_ID as DQI_ID, property_ID, @no_of_history_days as actual_days_analysed, count_failed_days, 
		case when count_failed_days > .1*@no_of_history_days then 'RED' else case when count_failed_days > .02*@no_of_history_days then 'YELLOW' else 'GREEN' end end
		as indicator
		FROM
		(
			SELECT property_ID, COUNT(*) as count_failed_days
			FROM 
			(
				SELECT property_ID, occupancy_DT
				FROM #Table_Final
				group by property_ID, Occupancy_DT
			) as outerTable
			group by Property_ID
		) as outermostTable
	end
	else
	begin
		SELECT  @DQI_ID as DQI_ID, @property_ID as property_ID, CASE WHEN @no_of_history_days IS NULL THEN 0 ELSE  @no_of_history_days  END as actual_days_analysed, 0 as count_failed_days, CASE WHEN @no_of_history_days IS NULL THEN 'GRAY' ELSE  'GREEN'  END as indicator
	end
	IF OBJECT_ID('tempdb..#Table_Final') IS NOT NULL
	BEGIN
		DROP TABLE #Table_Final
	END
	END /* End of else part of detailed analysis condition*/
END /*End of else part of IF(@no_of_room_class=1)*/
END
GO


