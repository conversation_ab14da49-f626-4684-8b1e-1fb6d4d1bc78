drop procedure if exists [dbo].[usp_bad_load_property_ac_at_details]

GO
/****** Object:  StoredProcedure [dbo].[usp_bad_load_property_ac_at_details]    Script Date: 12/14/2021 10:51:55 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Procedure Name: usp_bad_load_property_ac_at_details

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@business_date --> business_date ('2011-07-31')
	@is_physical_capacity_enabled --> refers to the pacman.feature.EnablePhysicalCapacityConsideration parameter
	@typeOfAccomType --> typeOfAccomType
	@showDisplayStatusRts --> showDisplayStatusRts
	@accomTypeStatusIds --> accomTypeStatusIds
	@compRoomsFilter --> exclude complimentary market segments
	
Ouput Parameter : NA

Execution: this is just an example
	Example : 
	------------------------------------------------------
	exec dbo.usp_bad_load_property_ac_at_details 18,'2011-07-01','2011-07-31','2011-07-01','1','1','','1','0'
	
	
Purpose: The purpose of this procedure is to load data metrics required for reference data dashboard
		 
Author: 

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
12/14/2021		Shrey				Vegda					Added complimentary rooms filter

***************************************************************************************/

CREATE procedure [dbo].[usp_bad_load_property_ac_at_details]
(
		@property_id int,
		@accom_class_id int,
		@start_date date,
		@end_date date,
		@business_date date,
		@is_physical_capacity_enabled int,
		@typeOfAccomType int,
		@showDisplayStatusRts varchar(10),
		@accomTypeStatusIds varchar(10),
		@compRoomsFilter INT
)		

as

begin
	declare @caughtupdate date 

	-- extract caughtup date for a property 
	set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) 

	--Unassigned Accom Class ID
	declare @unassignedACId int = (select Accom_Class_ID from accom_class where accom_class_name = 'Unassigned')

	DECLARE @exclude_comp_room VARCHAR(5) = '0,1';

	IF (@compRoomsFilter = 1)
		SET @exclude_comp_room = '0';
		
	--- Merge AccomClass data with Accom_Class table to get the name returned
	select accom_totals.Accom_Type_ID, Accom_Type_Name, Rooms_Sold,
		case when Accom_Class_ID = @unassignedACId then null else cast(Occupancy_Forecast as numeric(8,1)) end Occupancy_Forecast,
		cast(ADR_OnBooks as numeric(19,2)) ADR_OnBooks,
		case when Accom_Class_ID = @unassignedACId then null else cast(ADR_Forecast as numeric(19,2)) end ADR_Forecast,
		cast(Revenue as numeric(19,2)) Revenue,
		case when Accom_Class_ID = @unassignedACId then null else cast(Revenue_Forecast as numeric(19,2)) end as Revenue_Forecast,
		cast(REVPAR_OnBooks as numeric(19,2)) REVPAR_OnBooks,
		case when Accom_Class_ID = @unassignedACId then null else cast(REVPAR_Forecast as numeric(19,2)) end REVPAR_Forecast,
		case when @compRoomsFilter = 1 then null else (Rooms_Sold - Rooms_Sold_asOf_business_date) end as Rooms_Sold_Pickup,
		case when @compRoomsFilter = 1 then null else (Revenue - Room_Revenue_asOf_business_date) end as Room_Revenue_Pickup,
		
		case when Rooms_Sold_asOf_business_date is null then null
		when Room_Revenue_asOf_business_date is null then null
		when @compRoomsFilter = 1 then null
		else		
		cast((CASE WHEN ISNULL(Rooms_Sold,0) > 0 THEN  Revenue / Rooms_Sold ELSE 0 END) 
		 - 
        (CASE WHEN ISNULL(Rooms_Sold_asOf_business_date,0) > 0 THEN  Room_Revenue_asOf_business_date / Rooms_Sold_asOf_business_date ELSE 0 END) 
		  as numeric(19,2)) end as Adr_Pickup,
	    Capacity AS Physical_Capacity,
	    Capacity - OOO AS Effective_Capacity
				
	from (
		-- Get Accom Class Data 
		select activity.Accom_Type_ID,Capacity,Rooms_Sold,Occupancy_Forecast,ADR_OnBooks,ADR_Forecast,
		Revenue,Revenue_Forecast,REVPAR_OnBooks,OOO,
		REVPAR_Forecast =
		  case (@is_physical_capacity_enabled)
       when 1 then
        case (Capacity)
          WHEN 0 THEN 0
        else(Revenue_Forecast/capacity)
        end
      else
		   case(Capacity - OOO)
		    when 0 then 0
			 else Revenue_Forecast /(Capacity - OOO)
			 end
			end,
		Rooms_Sold_asOf_business_date,
		Room_Revenue_asOf_business_date
		from (
			-- AccomClass Activity data
			select 
				Accom_Type_ID,
				Capacity,
				Rooms_Sold,
				ADR_OnBooks = case(Rooms_Sold) when 0 then 0 else Room_Revenue / Rooms_Sold end,
				REVPAR_OnBooks =
			    case (@is_physical_capacity_enabled)
            when 1 then
              CASE (capacity)
               WHEN 0 THEN 0
              ELSE (Room_Revenue/capacity)
              END
          else
			      case(Capacity - OOO)
			        when 0 then 0
			      else Room_Revenue /(Capacity - OOO)
			      end
			    end,
				OOO,
				Room_Revenue Revenue
			from
			(
			select 
					aa.Accom_Type_ID,
					aa.Capacity,
					(
					CASE 
						WHEN @compRoomsFilter = 1
							THEN aa.Rooms_Sold - isNull(maa.Rooms_Sold, 0)
						ELSE aa.Rooms_Sold
						END
					) AS Rooms_Sold,
					(
					CASE 
						WHEN @compRoomsFilter = 1
							THEN aa.Room_Revenue - isNull(maa.Room_Revenue, 0)
						ELSE aa.Room_Revenue
						END
					) AS Room_Revenue,
					aa.OOO
			from (
				select 
					aa.Accom_Type_ID,
					sum(aa.Accom_Capacity) Capacity, 
					sum(aa.Rooms_Sold) Rooms_Sold,
					sum(aa.Room_Revenue) Room_Revenue,
					sum(aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other) OOO
				from Accom_Activity aa inner join Accom_Type at on aa.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID in (select items from Split(@accomTypeStatusIds , ',')) and at.Display_Status_ID in (select items from Split(@showDisplayStatusRts , ',')) and at.System_Default = 0
				where aa.Property_Id=@property_id and aa.Occupancy_DT between @start_date and @end_date
				and at.Accom_Class_ID = @accom_class_id
				and at.isComponentRoom in (
					select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'Y' end 
						union 
					select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'N' end
					)
				group by aa.Accom_Type_ID
				)aa
			LEFT JOIN (
				SELECT mact.Accom_Type_ID
					,sum(mact.Rooms_Sold) AS Rooms_Sold
					,sum(mact.Room_Revenue) AS Room_Revenue
				FROM Mkt_Accom_Activity mact
				inner join Accom_Type at on mact.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID in (select items from Split(@accomTypeStatusIds , ',')) and at.Display_Status_ID in (select items from Split(@showDisplayStatusRts , ',')) and at.System_Default = 0
				INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = mact.Mkt_Seg_ID AND ms.Exclude_CompHouse_Data_Display IN (1)
				where mact.Property_Id=@property_id and mact.Occupancy_DT between @start_date and @end_date
				and at.Accom_Class_ID = @accom_class_id
				and at.isComponentRoom in (
					select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'Y' end 
						union 
					select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'N' end
					)
				group by mact.Accom_Type_ID
				) AS maa ON maa.Accom_Type_ID= aa.Accom_Type_ID
			) activity_type
		) as activity
		left join
		(
			select 
				Accom_Type_ID,
				sum(Rooms_Sold_asOf_business_date ) Rooms_Sold_asOf_business_date,
				sum(Room_Revenue_asOf_business_date) Room_Revenue_asOf_business_date
			from
			(
				select 
					paa.Accom_Type_ID,
					sum(paa.Rooms_Sold) Rooms_Sold_asOf_business_date,
					sum(paa.Room_Revenue) Room_Revenue_asOf_business_date
				from pace_Accom_Activity paa 
				inner join Accom_Type at 
					on paa.Accom_Type_ID = at.Accom_Type_ID
					and at.Status_ID in (select items from Split(@accomTypeStatusIds , ','))
					and at.Display_Status_ID in (select items from Split(@showDisplayStatusRts , ','))
					and at.System_Default = 0
				where
					 paa.Property_Id=@property_id 
					 and paa.Business_Day_End_DT = @business_date
					 and paa.Occupancy_DT between @start_date and @end_date
					 and paa.Occupancy_DT > @business_date
					and at.Accom_Class_ID = @accom_class_id
					and at.isComponentRoom in (
						select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'Y' end 
							union 
						select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'N' end
						)
				group by paa.Accom_Type_ID
				union all
				select 
					aa.Accom_Type_ID,
					(
					CASE 
						WHEN @compRoomsFilter = 1
							THEN aa.Rooms_Sold_asOf_business_date - isNull(maa.Rooms_Sold, 0)
						ELSE aa.Rooms_Sold_asOf_business_date
						END
					) AS Rooms_Sold_asOf_business_date,
					(
					CASE 
						WHEN @compRoomsFilter = 1
							THEN aa.Room_Revenue_asOf_business_date - isNull(maa.Room_Revenue, 0)
						ELSE aa.Room_Revenue_asOf_business_date
						END
					) AS Room_Revenue_asOf_business_date
			from (
				select 
						paa.Accom_Type_ID,
						sum(paa.Rooms_Sold) Rooms_Sold_asOf_business_date,
						sum(paa.Room_Revenue) Room_Revenue_asOf_business_date
					from Accom_Activity paa 
					inner join Accom_Type at 
						on paa.Accom_Type_ID = at.Accom_Type_ID
						and at.Status_ID in (select items from Split(@accomTypeStatusIds , ','))
						and at.Display_Status_ID in (select items from Split(@showDisplayStatusRts , ',')) 
						and at.System_Default = 0
					where paa.Property_Id=@property_id 
						and paa.Occupancy_DT between @start_date and @end_date
						and paa.Occupancy_DT <= @business_date
						and at.Accom_Class_ID = @accom_class_id
						and at.isComponentRoom in (
							select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'Y' end 
								union 
							select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'N' end
							)
					group by paa.Accom_Type_ID 
					)aa
			LEFT JOIN (
				SELECT mact.Accom_Type_ID
					,sum(mact.Rooms_Sold) AS Rooms_Sold
					,sum(mact.Room_Revenue) AS Room_Revenue
				FROM Mkt_Accom_Activity mact
				inner join Accom_Type at 
						on mact.Accom_Type_ID = at.Accom_Type_ID
						and at.Status_ID in (select items from Split(@accomTypeStatusIds , ','))
						and at.Display_Status_ID in (select items from Split(@showDisplayStatusRts , ',')) 
						and at.System_Default = 0
					where mact.Property_Id=@property_id 
						and mact.Occupancy_DT between @start_date and @end_date
						and mact.Occupancy_DT <= @business_date
						and at.Accom_Class_ID = @accom_class_id
						and at.isComponentRoom in (
							select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'Y' end 
								union 
							select case when @typeOfAccomType = 0 then 'N' when @typeOfAccomType = 1 then 'Y' when @typeOfAccomType = 2 then 'N' end
							)
					group by mact.Accom_Type_ID 
				) AS maa ON maa.Accom_Type_ID= aa.Accom_Type_ID
			) activity_type group by Accom_Type_ID
		) as pace_activity on activity.Accom_Type_ID = pace_activity.Accom_Type_ID
		left join
		(
			-- Get the AccomClass Occupancy Forecast
			select 
			Property_ID,
			Accom_Type_ID,
			Occupancy_Forecast,
			ADR_Forecast = case(Occupancy_Forecast) when 0 then 0 else Room_Revenue / Occupancy_Forecast end,				
			Room_Revenue Revenue_Forecast
			from
			(
				select 
					oc.Property_ID,
					oc.Accom_Type_ID,
					sum(oc.Occupancy_NBR) Occupancy_Forecast,
					sum(oc.Revenue) Room_Revenue
				from dbo.FN_AT_Occupancy_comp_room_exclusion(@caughtupdate,@exclude_comp_room) oc inner join Accom_Type at on oc.Accom_Type_ID = at.Accom_Type_ID and at.Accom_Class_ID = @accom_class_id
				where oc.Property_Id=@property_id
				and oc.Occupancy_DT between @start_date and @end_date
				group by oc.Property_ID, oc.Accom_Type_ID
			) data
		) as occ_forecast on occ_forecast.Accom_Type_ID = activity.Accom_Type_ID
	) accom_totals inner join Accom_Type on accom_totals.Accom_Type_ID = Accom_Type.Accom_Type_ID and Accom_Type.Status_ID in (select items from Split(@accomTypeStatusIds , ',')) and Accom_Type.Display_Status_ID IN (select items from Split(@showDisplayStatusRts , ','))  and Accom_Type.System_Default = 0


END
GO


