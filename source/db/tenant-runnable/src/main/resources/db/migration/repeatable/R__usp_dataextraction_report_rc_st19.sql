DROP PROCEDURE IF EXISTS [dbo].[usp_dataextraction_report_rc_st19]
GO

/****** Object:  StoredProcedure [dbo].[usp_dataextraction_report_rc_st19]    Script Date: 05/18/2022 13:30 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp_dataextraction_report_rc_st19](
    @property_id INT,
    @ly_start_date DATE,
    @ly_end_date DATE,
    @ly_businessdate DATE,
    @includeZeroCapacityRT INT,
    @includePseudoRT INT,
    @day_diff INT
)
AS
BEGIN

    SELECT Occupancy_DT,
           Accom_Class_Name,
           SUM(rooms_sold)   AS rooms_sold,
           SUM(room_revenue) AS room_revenue,
           SUM(Total_Profit) AS profit
    FROM (
             SELECT pace.property_Id,
                    accom.Accom_Class_Name,
                    DateAdd(DAY, @day_diff, pace.Occupancy_DT) AS Occupancy_DT,
                    rooms_sold,
                    room_revenue,
                    Total_Profit
             FROM PACE_Accom_Activity pace
                      JOIN Accom_Type acct
                           ON acct.Accom_Type_ID = pace.Accom_Type_ID
                               AND acct.property_Id = pace.property_Id
                               AND acct.status_id IN (SELECT 1
                                                      UNION
                                                      SELECT CASE WHEN @includePseudoRT = 1 THEN 6 ELSE 1 END)
                               AND acct.display_status_id IN (SELECT 1
                                                              UNION
                                                              SELECT CASE WHEN @includeZeroCapacityRT = 1 THEN 2 ELSE 1 END
                                                              UNION
                                                              SELECT CASE WHEN @includePseudoRT = 1 THEN 4 ELSE 1 END)
                      JOIN Accom_Class accom
                           ON accom.property_Id = acct.property_Id
                               AND accom.Accom_Class_ID = acct.Accom_Class_ID
                               AND accom.status_id = 1
             WHERE pace.Occupancy_DT BETWEEN @ly_start_date AND @ly_end_date
               AND pace.Property_ID = @property_id
               AND pace.Business_Day_End_DT = @ly_businessdate
         ) AS groupstly
    GROUP BY Occupancy_DT, Accom_Class_Name
    ORDER BY Occupancy_DT, Accom_Class_Name

END
GO
