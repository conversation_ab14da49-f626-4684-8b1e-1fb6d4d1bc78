if
exists (select * from sys.objects where object_id = object_id(N'[dbo].[ufn_tent_grp_fcst_alert_preprocess_grp_code]'))
drop function
[dbo].[ufn_tent_grp_fcst_alert_preprocess_grp_code]
GO
create function [dbo].[ufn_tent_grp_fcst_alert_preprocess_grp_code](
        @group_code nvarchar(max),
        @isPCRS int,
        @isVirtualProperty int,
        @virtualPropertyCodesForPCRS nvarchar(max))
RETURNS nvarchar(max)
AS
BEGIN
            DECLARE @ret nvarchar(max);
select @ret = case
                  when @isVirtualProperty = 1
                      then
                      case
                          when (LEFT(@group_code, CHARINDEX('_', @group_code)-1) in (select value from STRING_SPLIT(@virtualPropertyCodesForPCRS, ',')))
                              then SUBSTRING(@group_code, CHARINDEX('_', @group_code) + 4,
                                             <PERSON><PERSON>(@group_code))
                          else
                              SUBSTRING(@group_code, CHARINDEX('_', @group_code) + 1,
                                        <PERSON><PERSON>(@group_code))
                          end
                  when @isPCRS = 1
                      then SUBSTRING(@group_code, 4, LEN(@group_code))
                  else
                      @group_code
    end;
return @ret;
END;