if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_output_override_ovbk_report]'))
drop function [ufn_get_output_override_ovbk_report]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE function [dbo].[ufn_get_output_override_ovbk_report]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@isRollingDate int,
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50)
)
returns  @output_override_ovbk_report table
(
	Property_Name nvarchar(150),
	Accom_Class_Name nvarchar(150),
	OVR_Overbooking_Type_Name nvarchar(50),
	Occupancy_DT date,
	DOW varchar (10),
	Overbooking_OVR numeric(18,0),
	CostofWalk_Value_OVR numeric(19,2),
	OverrideCategory nvarchar(50),
	User_Name  nvarchar(50),
	Last_Updated_DTTM datetime,
	Notes nvarchar(max),
	Email_Address nvarchar(150),
	Custom_Sort bit
)
as
begin

		declare @caughtupdate date
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
begin
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
end

insert into @output_override_ovbk_report
select p.Property_Name,baseUnion.Accom_Type_Name,baseUnion.OVR_Overbooking_Type_Name,Occupancy_DT,DATENAME(dw,Occupancy_DT) as dow,
       Overbooking_OVR,
       CostofWalk_Value_OVR,
       OverrideCategory,
       u.User_Name,
       baseUnion.Last_Updated_DTTM,
       baseUnion.notes as Notes,
       u.Email_Address,
       Custom_Sort
from
    (

        select DOPO.Property_ID, 0 as Custom_Sort ,'common.property' as Accom_Type_Name,OVR_Overbooking_Type_Name,
               Occupancy_DT,
               Overbooking_OVR,0 as CostofWalk_Value_OVR ,
               'common.overbooking' as OverrideCategory,
               Last_Updated_By_User_ID,
               DOPO.Last_Updated_DTTM,n.Notes
        from Decision_Ovrbk_Property_OVR DOPO
                 inner JOIN dbo.OVR_Overbooking_Type OOT ON OOT.OVR_Overbooking_Type_ID=DOPO.OVR_Overbooking_type_ID
                 left join
             (
                 select * from ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Overbooking')
             ) n
             ON n.Arrival_DT=DOPO.Occupancy_DT
                 and n.Property_ID=DOPO.Property_ID
        where DOPO.Property_ID=@property_id
          and Occupancy_DT between @start_date and @end_date and DOPO.Status_ID=1

        union
        select Decision_Ovrbk_Accom_OVR.Property_ID, 1 as Custom_Sort, Accom_Type_Name,OVR_Overbooking_Type_Name,
               Occupancy_DT,
               Overbooking_OVR,0 as CostofWalk_Value_OVR ,
               'common.overbooking' as OverrideCategory,
               Decision_Ovrbk_Accom_OVR.Last_Updated_By_User_ID as USER_ID,
               Decision_Ovrbk_Accom_OVR.Created_DTTM,
               Notes.Notes
        from dbo.Decision_Ovrbk_Accom_OVR Decision_Ovrbk_Accom_OVR
                 JOIN dbo.Accom_Type  Accom_Type ON Decision_Ovrbk_Accom_OVR.Accom_Type_ID=Accom_Type.Accom_Type_ID
                 JOIN dbo.OVR_Overbooking_Type OVR_Overbooking_Type
                      ON OVR_Overbooking_Type.OVR_Overbooking_Type_ID=Decision_Ovrbk_Accom_OVR.OVR_Overbooking_type_ID
                 LEFT OUTER JOIN
             (
                 select * from ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Overbooking')
             ) Notes
             ON Notes.Arrival_DT=Decision_Ovrbk_Accom_OVR.Occupancy_DT
                 and Notes.Property_ID=Decision_Ovrbk_Accom_OVR.Property_ID
        where Decision_Ovrbk_Accom_OVR.Occupancy_DT between @start_date and @end_date
          and Decision_Ovrbk_Accom_OVR.Property_ID=@property_id and Decision_Ovrbk_Accom_OVR.Status_ID=1

        union

        select Decision_COW_Value_OVR.Property_ID, 1 as Custom_Sort,Accom_Type_Name,'' as  OVR_Overbooking_Type_Name,
               Occupancy_DT,0 as  Overbooking_OVR, CostofWalk_Value_OVR,
               'common.costofwalk' as OverrideCategory,
               USER_ID,
               Decision_COW_Value_OVR.CreateDate_DTTM,
               Notes.Notes
        from dbo.Decision_COW_Value_OVR Decision_COW_Value_OVR
                 JOIN dbo.Accom_Type  Accom_Type ON Decision_COW_Value_OVR.Accom_Type_ID=Accom_Type.Accom_Type_ID
                 LEFT OUTER JOIN
             (
                 select * from ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Overbooking')
             )Notes on
                         Notes.Arrival_DT=Decision_COW_Value_OVR.Occupancy_DT
                     and Notes.Property_ID=Decision_COW_Value_OVR.Property_ID
        where Decision_COW_Value_OVR.Occupancy_DT between @start_date and @end_date
          and Decision_COW_Value_OVR.Property_ID=@property_id  and Decision_COW_Value_OVR.Status_ID=1
    )
        baseUnion
        inner join Property p on p.Property_ID=baseUnion.Property_ID
        inner join Users u on baseUnion.Last_Updated_By_User_ID=u.User_ID

order by Occupancy_DT,Custom_Sort
    return
end