GO
/****** Object@  StoredProcedure [dbo].[usp_get_projected_adr_with_floor]    Script Date@ 3/18/2024******/
SET ANSI_NULLS ON
 GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_get_projected_adr_with_floor]'))
BEGIN
        DROP PROCEDURE [dbo].[usp_get_projected_adr_with_floor]
END
GO
CREATE procedure [dbo].[usp_get_projected_adr_with_floor]
(
	@propertyId Int,
	@endDate Date,
	@threshold Int
)
AS
BEGIN
DROP TABLE if EXISTS #floorWithADRTempTable
DROP TABLE if EXISTS #masterRCBaseRTFloorTmpTable

DECLARE @caughtupdate DATE = (SELECT  dbo.ufn_get_caughtup_date_by_property(@propertyId,3,13))

DECLARE @startDate DATE = (SELECT DATEADD(month, DATEDIFF(month, 0, @caughtupdate)+1, 0))

SELECT ccbt.*, prod.Name productName
into #masterRCBaseRTFloorTmpTable
FROM CP_Cfg_Base_AT ccbt
         inner join CP_Cfg_AC cca ON ccbt.Accom_Type_Id=cca.Accom_Type_Id
         inner join Accom_Class ac ON cca.Accom_Class_ID=ac.Accom_Class_ID and ac.Master_Class=1
         inner join Product prod ON prod.product_Id=ccbt.Product_ID and prod.System_Default=1

SELECT pro.Occupancy_Date,
       CASE
           WHEN pro.rooms = 0 THEN 0
           ELSE pro.revenue / pro.rooms END AS Projected_ADR,
       CASE (DATEPART(WEEKDAY,pro.Occupancy_Date))
           WHEN 1 THEN IsNull(seasonFloor.Sunday_Floor_Rate, floor.Sunday_Floor_Rate)
           WHEN 2 THEN IsNull(seasonFloor.Monday_Floor_Rate, floor.Monday_Floor_Rate)
           WHEN 3 THEN IsNull(seasonFloor.Tuesday_Floor_Rate, floor.Tuesday_Floor_Rate)
           WHEN 4 THEN IsNull(seasonFloor.Wednesday_Floor_Rate, floor.Wednesday_Floor_Rate)
           WHEN 5 THEN IsNull(seasonFloor.Thursday_Floor_Rate, floor.Thursday_Floor_Rate)
           WHEN 6 THEN IsNull(seasonFloor.Friday_Floor_Rate, floor.Friday_Floor_Rate)
           WHEN 7 THEN IsNull(seasonFloor.Saturday_Floor_Rate, floor.Saturday_Floor_Rate)
           END AS floor_rate,
       floor.productName
INTO #floorWithADRTempTable
FROM (
         SELECT Occupancy_Date, Sum(Room_Revenue) revenue, sum(Rooms_sold) rooms FROM LDB_Projection pro
                                                                                          inner join Mkt_Seg ms ON pro.Market_Segment=ms.Mkt_Seg_Code
                                                                                          inner join Mkt_Seg_Details msd ON ms.Mkt_Seg_ID=msd.Mkt_Seg_ID
             and msd.Qualified=0 and msd.Priced_By_BAR=1
         GROUP BY Occupancy_Date) pro
         left join (
    SELECT * FROM #masterRCBaseRTFloorTmpTable) seasonFloor
                   ON Season_Name is not null and Start_Date is not null and Start_Date is not null and pro.Occupancy_Date between seasonFloor.Start_Date and seasonFloor.End_Date
         left join (
    SELECT * FROM #masterRCBaseRTFloorTmpTable) floor
                   ON floor.Season_Name is null
WHERE pro.Occupancy_Date between @startDate and @endDate

SELECT Occupancy_Date, DATENAME(weekday,Occupancy_Date) dow, floor_rate, Projected_ADR, productName from #floorWithADRTempTable where floor_rate > ((100 + @threshold) * Projected_ADR/100) order by Occupancy_Date
DROP TABLE if EXISTS #floorWithADRTempTable
DROP TABLE if EXISTS #masterRCBaseRTFloorTmpTable

END
GO