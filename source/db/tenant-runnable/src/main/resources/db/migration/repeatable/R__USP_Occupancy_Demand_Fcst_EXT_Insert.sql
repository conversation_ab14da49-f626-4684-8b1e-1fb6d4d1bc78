DROP PROCEDURE IF EXISTS [dbo].[usp_Occupancy_Demand_FCST_Ext_Upsert]
GO
DROP TYPE IF EXISTS [dbo].[Occupancy_Demand_FCST_Ext_Batch]
GO

CREATE TYPE dbo.Occupancy_Demand_FCST_Ext_Batch AS TABLE
(
    [Decision_ID]       [bigint],
    [Forecast_Group_ID] [int],
    [Occupancy_DT]      [date],
    [Remaining_Demand]  [numeric](18, 2),
    [CreateDate_DTTM]   [datetime]
);
GO

CREATE PROCEDURE usp_Occupancy_Demand_FCST_Ext_Upsert @Occupancy_Demand_FCST_Ext_Batch Occupancy_Demand_FCST_Ext_Batch READONLY
AS
BEGIN
    INSERT INTO [dbo].[Occupancy_Demand_FCST_Ext]
    ( [Decision_ID]
    , [Forecast_Group_ID]
    , [Occupancy_DT]
    , [Remaining_Demand]
    , [CreateDate_DTTM])
    SELECT [Decision_ID], [Forecast_Group_ID], [Occupancy_DT], [Remaining_Demand], [CreateDate_DTTM]
    FROM @Occupancy_Demand_FCST_Ext_Batch Occupancy_Demand_FCST_Ext_Batch;
END
GO




