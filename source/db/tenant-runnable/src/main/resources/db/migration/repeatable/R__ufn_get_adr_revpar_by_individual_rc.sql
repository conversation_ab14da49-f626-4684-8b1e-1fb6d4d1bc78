if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_adr_revpar_by_individual_rc]'))
drop function [ufn_get_adr_revpar_by_individual_rc]

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
create function [dbo].[ufn_get_adr_revpar_by_individual_rc]
(
		@property_id int,
		@roomclass_id varchar(500),
		@start_date date,
		@end_date date,
		@use_physical_capacity int,
		@includeZeroCapacityRT int
)		
returns  @adr_revpar table
	(	
		occupancy_dt	date,
		property_id	int,
		accom_class_id int,
		rooms_sold	numeric(18,0),
		total_accom_capacity	numeric(18,0),
		outoforder	numeric(18,0),
		room_revenue	numeric(19,5),
		adr	numeric(19,5),
		revpar	numeric(19,5)
	)
as

begin	
	if (@roomclass_id = '') 
			insert into @adr_revpar
  			select 
  				occupancy_dt,property_id,null,
  				rooms_sold,
  				total_accom_capacity,
				(rooms_not_avail_maint+rooms_not_avail_other) outoforder,
	 			room_revenue,
				adr =
				case (rooms_sold)
					when 0 then 0
					else round(room_revenue / rooms_sold, 2)
				end,
        (select revpar from dbo.ufn_calculate_revpar(room_revenue, total_accom_capacity, (rooms_not_avail_maint + rooms_not_avail_other), @use_physical_capacity)) as revpar

			from total_activity ta
			where ta.property_id = @property_id and occupancy_dt between @start_date and @end_date	
			order by occupancy_dt
	 else
			insert into @adr_revpar
			select 
				aa.occupancy_dt,
  				aa.property_id,
				ac.accom_class_id,
  				sum(aa.rooms_sold) as rooms_sold,
  				sum(aa.accom_capacity) as accom_capacity,
				sum((aa.rooms_not_avail_maint+aa.rooms_not_avail_other)) as outoforder,
	 			sum(aa.room_revenue) as room_revenue,
				adr =
				case (sum(aa.rooms_sold))
					when 0 then 0
					else round(sum(aa.room_revenue) / sum(aa.rooms_sold), 2)
				end,
        (select revpar from dbo.ufn_calculate_revpar(sum(aa.room_revenue), sum(aa.accom_capacity), sum(aa.rooms_not_avail_maint+aa.rooms_not_avail_other), @use_physical_capacity)) as revpar
				from accom_activity aa join accom_type at 
				on aa.accom_type_id = at.accom_type_id and aa.property_id = at.property_id join accom_class ac 
				on ac.accom_class_id = at.accom_class_id and ac.property_id = at.property_id
				where aa.property_id = @property_id and
					aa.occupancy_dt between @start_date and @end_date	and 
					ac.accom_class_id in (select value from dbo.varcharToInt(@roomclass_id,',')) and at.Display_Status_ID
					 in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end )
				group by aa.occupancy_dt,aa.property_id,ac.accom_class_id
				order by aa.occupancy_dt,aa.property_id,ac.accom_class_id
	return
end

