DROP FUNCTION IF EXISTS [dbo].[ufn_get_pace_occupancy_fcst_hotel]
GO
/****** Object:  UserDefinedFunction [dbo].[ufn_get_pace_occupancy_fcst_hotel]    Script Date: 26-Nov-21 19:09:33 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Function Name: ufn_get_pace_occupancy_fcst_hotel

Input Parameters :
	@property_id --> property Id associated with a property (e.g.,'XNAES' id from the property table is 10027)
	@start_date --> start date from which you need pace ('2016-02-01')
	@end_date --> end date till which you need pace ('2016-02-29')
	@past_start_date --> maximum past date till which we want pace from
    @excludeCompRooms --> weather or not to include comp room data ('0' = excluded / '0,1' = all data included)

Output Parameter : NA

Execution: this is just an example
	select * from dbo.ufn_get_pace_occupancy_fcst_hotel 10027,'2016-02-01', '2016-02-29','2015-12-03', '0,1'

Purpose: The purpose of this function is to extract pace of forecast for a given date range.

Assumptions : NA

Author: Rajratna Awale

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
11/26/2021		Rajratna			Awale				7.4.1 - Adds comp room data exclusion support in the existing function.
***************************************************************************************/
CREATE function [dbo].[ufn_get_pace_occupancy_fcst_hotel]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@excludeCompRooms varchar(5)
)		
returns @pace_occupancy_fcst_hotel table
(	
	Business_Day_End_DT date,
	Occupancy_NBR numeric(8,2),
	count_records numeric (8,0)
)
as
begin
	declare @Businessdate date 
	set @Businessdate = DATEADD(DAY, -1, cast((select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) as Date)) --> extract caughtup date for a property

	declare @temp_end_date as date = @end_date
	if(@Businessdate>@end_date)
	begin
		set @temp_end_date = @Businessdate
	end
		
	declare @temp_decision table
	(
		Property_ID int,
		Business_DT date,
		Decision_ID int,
		UNIQUE CLUSTERED (property_id,Business_DT,Decision_ID)
	)
		
	insert into @temp_decision
	select Property_ID,Business_DT,Decision_ID from Decision
		where Property_ID=@property_id and Business_DT between @past_start_date and @Businessdate
			and Decision_Type_ID=1

	declare @temp_Pace_occupancy_fcst table
	(
		Property_ID int,
		Occupancy_DT date,
		Decision_ID int,
		Occupancy_NBR [numeric](8, 2),
		UNIQUE CLUSTERED (property_id,Occupancy_DT,Decision_ID)
	)
	
	insert into @temp_Pace_occupancy_fcst
		select occ.Property_ID,Occupancy_DT,Decision_ID,SUM(Occupancy_NBR)Occupancy_NBR
        from PACE_Mkt_Occupancy_FCST occ
        inner join Mkt_Seg ms ON ms.Mkt_Seg_ID = occ.MKT_SEG_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@excludeCompRooms, ','))
		where occ.Property_ID=@property_id and Occupancy_DT between @start_date and @end_date
		group by occ.Property_ID,Occupancy_DT,Decision_ID
	
	
	declare @temp_occupancy_fcst table
	(
		Property_ID int,
		Occupancy_DT date,
		Occupancy_NBR [numeric](8, 2),
		UNIQUE CLUSTERED (property_id,Occupancy_DT)
	)
		
	insert into @temp_occupancy_fcst
	select occ.Property_ID,Occupancy_DT,SUM(Occupancy_NBR)Occupancy_NBR from Occupancy_FCST occ 
		inner join Mkt_Seg ms ON ms.Mkt_Seg_ID = occ.MKT_SEG_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@excludeCompRooms, ','))
		inner join Accom_Type at on occ.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom ='N' 
		where Occupancy_DT between @start_date and @temp_end_date and occ.Property_ID=@property_id
		group by occ.Property_ID,Occupancy_DT
	
	insert into @pace_occupancy_fcst_hotel
	SELECT Business_Day_End_DT, SUM(Occupancy_NBR), COUNT(Occupancy_NBR) 
	FROM ( 	
		select a.Business_Day_End_DT,b.Occupancy_NBR from 
		(	
			select occ.Occupancy_DT,Business_DT as Business_Day_End_DT,MAX(occ.Decision_id)Decision_id  from @temp_Pace_occupancy_fcst occ 
				inner join @temp_decision de 
			on occ.Property_ID=de.Property_ID	
				and occ.Decision_ID=de.Decision_ID
			group by occ.Occupancy_DT,Business_DT
		) a inner join
		(
			select occ.Occupancy_DT,Business_DT as Business_Day_End_DT,occ.Decision_ID,Occupancy_NBR  from @temp_Pace_occupancy_fcst occ 
				inner join @temp_decision de 
			on occ.Property_ID=de.Property_ID	
				and occ.Decision_ID=de.Decision_ID
		)b on a.Business_Day_End_DT=b.Business_Day_End_DT
			and a.Decision_id=b.Decision_ID
			and a.Occupancy_DT=b.Occupancy_DT	
		union all	
		SELECT Business_Day_End_DT, Occupancy_NBR  
		FROM @temp_occupancy_fcst tof CROSS JOIN 
			( 
				SELECT occupancy_dt AS Business_Day_End_DT, tof2.property_id  
				FROM @temp_occupancy_fcst tof2 
				WHERE occupancy_dt between @start_date AND @Businessdate
			) AS a  
		WHERE tof.occupancy_dt <= Business_Day_End_DT  
			AND tof.property_id = a.property_id 
			AND Occupancy_DT BETWEEN @start_date and @end_date	
		) AS data 
	GROUP BY Business_Day_End_DT  
	ORDER BY Business_Day_End_DT 

	return
end

GO
