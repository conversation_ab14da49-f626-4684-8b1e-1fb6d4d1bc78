DROP PROCEDURE IF EXISTS [dbo].[usp_central_rms_cf_price_alert_detail]
GO
CREATE Procedure [dbo].[usp_central_rms_cf_price_alert_detail]
	@Arrival_dt DATE
	,@Evaluation_Days INT = 90
    ,@Threshold INT = 30
    ,@Use_physical_capacity BIT = 0
AS
BEGIN

	DECLARE @Arrival_Start_Date DATE = @Arrival_dt
	DECLARE @Arrival_End_Date DATE = DATEADD(dd, @Evaluation_Days - 1, @Arrival_dt)

	-- Gather all the metadata to compare the BAR against the effective floor ceiling
	DROP TABLE IF EXISTS #BAR_Effective_Floor_Ceiling
	CREATE TABLE #BAR_Effective_Floor_Ceiling  (
		Arrival_DT DATE
		,accom_class_id INT
	    ,accom_class_code NVARCHAR(150)
		,accom_class_name NVARCHAR(150)
		,is_master_class BIT
        ,accom_class_rank_order INT
		,override NVARCHAR(20)
		,season_name NVARCHAR(255)
		,Final_BAR DECIMAL(19, 2)
		,Effective_Floor_Rate DECIMAL(19, 2)
		,Effective_Ceil_Rate DECIMAL(19, 2)
	)

    INSERT #BAR_Effective_Floor_Ceiling
	EXEC [dbo].[usp_central_rms_bar_effective_floor_ceiling]
	    @Arrival_dt = @Arrival_dt
	    ,@Evaluation_Days = @Evaluation_Days

    -- Gather special event type data
    DECLARE @Special_Event TABLE (
        Special_Event_Type_Name NVARCHAR(75)
		,startDate DATE
		,endDate DATE
	)
    INSERT @Special_Event
    EXEC [dbo].[usp_central_rms_special_event_categories]
        @Informational_only = 0,
        @Include_pre_post_days = 1,
        @Active = 1,
        @Start_date = @Arrival_Start_Date,
        @End_date = @Arrival_End_Date

    -- Gather all of the occupancy percentages for the evaluation window, calling the function
    -- in a nested select/inner-join is slow
	DECLARE @Occupancy_Forecast TABLE (
		Occupancy_DT DATE,
        Occupancy_Percent DECIMAL(11, 6)
    )

    INSERT INTO @Occupancy_Forecast
    EXEC [dbo].[usp_central_rms_property_occupancy_forecast_pct]
		@Use_Physical_Capacity = @Use_physical_capacity,
		@Start_DT = @Arrival_Start_Date,
		@End_DT = @Arrival_End_Date

    -- Get all relevant Analytics data for faster joins
	DROP TABLE IF EXISTS #Central_RMS_Price_Data
	SELECT * INTO #Central_RMS_Price_Data
	FROM Central_RMS_Price_Data
	WHERE Occupancy_DT BETWEEN @Arrival_Start_Date AND @Arrival_End_Date

	-- Select all of the alert metadata
    DROP TABLE IF EXISTS #Central_RMS_Alert_Data
	SELECT hits.Arrival_DT Arrival_DT
		,hits.accom_class_id accom_class_id
	    ,hits.accom_class_code accom_class_code
		,hits.accom_class_name accom_class_name
		,hits.is_master_class is_master_class
        ,hits.accom_class_rank_order accom_class_rank_order
		,hits.override override
		,hits.season_name season_name
		,hits.Final_BAR Final_Bar
		,hits.Effective_Floor_Rate Effective_Floor_Rate
		,hits.Effective_Ceil_Rate Effective_Ceil_Rate
		,central_rms.Hist_BAR_ADR_Lower Hist_BAR_ADR_Lower
		,central_rms.Hist_BAR_ADR_Upper Hist_BAR_ADR_Upper
		,central_rms.Min_Adj_Comp_Price Min_Adj_Comp_Price
		,central_rms.Max_Adj_Comp_Price Max_Adj_Comp_Price
		,CAST(CASE
			WHEN (Final_BAR >= Effective_Ceil_Rate AND central_rms.Hist_BAR_ADR_Lower > hits.Effective_Ceil_Rate) OR
				 (Final_BAR <= Effective_Floor_Rate AND central_rms.Hist_BAR_ADR_Upper < hits.Effective_Floor_Rate) THEN 1
			ELSE 0
		 END AS BIT) ADR_inconsistent
		,CAST(CASE
			WHEN (Final_BAR >= Effective_Ceil_Rate AND central_rms.Min_Adj_Comp_Price > hits.Effective_Ceil_Rate) OR
                 (Final_BAR <= Effective_Floor_Rate AND central_rms.Max_Adj_Comp_Price < hits.Effective_Floor_Rate) THEN 1
			ELSE 0
		 END AS BIT) Competitor_inconsistent
		,lrv.LRV LRV
		,occ_pct.Occupancy_Percent Occupancy_Fcst_Pct
        ,CAST(CASE WHEN Final_BAR <= Effective_Floor_Rate AND (Hist_BAR_ADR_Upper < Effective_Floor_Rate OR Max_Adj_Comp_Price < Effective_Floor_Rate) THEN 1 ELSE 0 END AS BIT)Floor
        ,CAST(CASE WHEN Final_BAR >= Effective_Ceil_Rate AND (Hist_BAR_ADR_Lower > Effective_Ceil_Rate OR Min_Adj_Comp_Price > Effective_Ceil_Rate) THEN 1 ELSE 0 END AS BIT) Ceil
	INTO #Central_RMS_Alert_Data
	FROM
        #Central_RMS_Price_Data central_rms
		INNER JOIN #BAR_Effective_Floor_Ceiling hits
			ON central_rms.Occupancy_DT = hits.Arrival_DT and central_rms.Accom_Class_ID = hits.accom_class_id
		LEFT JOIN Decision_LRV lrv
			ON central_rms.Occupancy_DT = lrv.Occupancy_DT AND central_rms.accom_class_id = lrv.Accom_Class_ID
		LEFT JOIN @Occupancy_Forecast occ_pct
			ON occ_pct.Occupancy_DT = central_rms.Occupancy_DT
	WHERE
		(hits.Final_BAR >= hits.Effective_Ceil_Rate
			AND (central_rms.Hist_BAR_ADR_Lower > hits.Effective_Ceil_Rate OR central_rms.Min_Adj_Comp_Price > hits.Effective_Ceil_Rate)
		)
		OR (hits.Final_BAR <= hits.Effective_Floor_Rate
			AND (central_rms.Hist_BAR_ADR_Upper < hits.Effective_Floor_Rate OR central_rms.Max_Adj_Comp_Price < hits.Effective_Floor_Rate)
		)

    -- Get all the room classes that have at least @Threshold arrival dates for ceiling and floor separately
    DECLARE @Room_Class_Counts TABLE (
		accom_class_id INT
		,countFloor INT
		,countCeil INT
	)

	INSERT INTO @Room_Class_Counts
    SELECT accom_class_id, SUM(CAST(Floor AS INT)), SUM(CAST(Ceil AS INT))
    FROM #Central_RMS_Alert_Data
    GROUP BY accom_class_id
    HAVING SUM(CAST(Floor AS INT)) >= @Threshold OR SUM(CAST(Ceil AS INT)) >= @Threshold

    -- Get all the dates which have special events in a csv column
    DECLARE @Special_Event_Dates TABLE (
		arrival_dt DATE,
		special_events NVARCHAR(MAX)
	)

	INSERT INTO @Special_Event_Dates
    SELECT
        flattened.Arrival_DT,
        STRING_AGG(flattened.Special_Event_Type_Name, ',') special_events
    FROM (
        SELECT
            DISTINCT
            alert_data.Arrival_DT,
            sp_event.Special_Event_Type_Name
        FROM
            #Central_RMS_Alert_Data alert_data
            INNER JOIN  @Special_Event sp_event
                ON alert_data.Arrival_DT BETWEEN sp_event.startDate AND sp_event.endDate
    ) flattened
    GROUP BY
        flattened.Arrival_DT

	-- Only select the data with room classes that have at least @Threshold arrival dates
    SELECT
        alert_data.*,
        sp_event.special_events Special_Events,
        CASE
            WHEN alert_data.override = 'USER' AND (alert_data.Floor = 1 OR alert_data.Ceil = 1) THEN 'USER'
            WHEN alert_data.override = 'FLOORANDCEIL' AND alert_data.Effective_Ceil_Rate = alert_data.Effective_Floor_Rate AND alert_data.Floor = 1 AND alert_data.Ceil = 1 THEN 'FLOORANDCEIL'
            WHEN alert_data.Floor = 1 AND (alert_data.override = 'FLOOR' OR alert_data.override = 'FLOORANDCEIL') THEN 'FLOOR'
            WHEN alert_data.Ceil = 1 AND (alert_data.override = 'CEIL' OR alert_data.override = 'FLOORANDCEIL') THEN 'CEIL'
            ELSE 'NONE'
        END as violating_override
    FROM #Central_RMS_Alert_Data alert_data
        INNER JOIN @Room_Class_Counts counts
            ON alert_data.accom_class_id = counts.accom_class_id
                AND ((alert_data.Ceil = 1 AND counts.countCeil >= @Threshold) OR (alert_data.Floor = 1 AND counts.countFloor >= @Threshold))
        LEFT JOIN @Special_Event_Dates sp_event
            ON alert_data.Arrival_DT = sp_event.arrival_dt
END
GO
