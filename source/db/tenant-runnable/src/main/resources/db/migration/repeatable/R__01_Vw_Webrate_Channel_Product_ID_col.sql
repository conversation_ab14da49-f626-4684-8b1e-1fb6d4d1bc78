IF EXISTS(SELECT * FROM sys.views WHERE name = 'Vw_Webrate_Channel_Product_ID_col' AND schema_id = SCHEMA_ID('dbo'))
DROP VIEW [dbo].[Vw_Webrate_Channel_Product_ID_col];
GO
CREATE VIEW [dbo].[Vw_Webrate_Channel_Product_ID_col]
AS
select distinct Webrate_Source_Property.property_id as Property_ID,Occupancy_DT,Channel_ID =
      CASE 
            WHEN Webrate_Override_Channel.Monday_Override_Channel_ID IS NULL  THEN
              CASE DATEPART (weekday,Webrate.Occupancy_DT)
                  WHEN 1 THEN Webrate_Default_Channel.Sunday_Channel_ID
                  WHEN 2 THEN Webrate_Default_Channel.Monday_Channel_ID
                  WHEN 3 THEN Webrate_Default_Channel.Tuesday_Channel_ID
                  WHEN 4 THEN Webrate_Default_Channel.Wednesday_Channel_ID
                  WHEN 5 THEN Webrate_Default_Channel.Thursday_Channel_ID
                  WHEN 6 THEN Webrate_Default_Channel.Friday_Channel_ID
                  WHEN 7 THEN Webrate_Default_Channel.Saturday_Channel_ID
                  ELSE 0
              END
      ELSE
              CASE DATEPART (weekday,Webrate.Occupancy_DT)
                  WHEN 1 THEN Webrate_Override_Channel.Sunday_Override_Channel_ID
                  WHEN 2 THEN Webrate_Override_Channel.Monday_Override_Channel_ID
                  WHEN 3 THEN Webrate_Override_Channel.Tuesday_Override_Channel_ID
                  WHEN 4 THEN Webrate_Override_Channel.Wednesday_Override_Channel_ID
                  WHEN 5 THEN Webrate_Override_Channel.Thursday_Override_Channel_ID
                  WHEN 6 THEN Webrate_Override_Channel.Friday_Override_Channel_ID
                  WHEN 7 THEN Webrate_Override_Channel.Saturday_Override_Channel_ID
                  ELSE 0
               END 
      END,
      product_id  =
      CASE 
            WHEN Webrate_Override_Channel.Monday_Override_Channel_ID IS NULL  THEN
			Webrate_Default_Channel.Product_ID
              --CASE DATEPART (weekday,Webrate.Occupancy_DT)
              --    WHEN 1 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 2 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 3 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 4 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 5 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 6 THEN Webrate_Default_Channel.Product_ID
              --    WHEN 7 THEN Webrate_Default_Channel.Product_ID
              --    ELSE 0
              --END
      ELSE
	  Webrate_Override_Channel.Product_ID
              --CASE DATEPART (weekday,Webrate.Occupancy_DT)
              --    WHEN 1 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 2 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 3 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 4 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 5 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 6 THEN Webrate_Override_Channel.Product_ID
              --    WHEN 7 THEN Webrate_Override_Channel.Product_ID
              --    ELSE 0
              -- END 

      END
from
Webrate inner join Webrate_Source_Property 
on Webrate.Webrate_Source_Property_ID=Webrate_Source_Property.Webrate_Source_Property_ID
inner join Webrate_Default_Channel on Webrate_Source_Property.Property_ID= Webrate_Default_Channel.Property_ID
left join Webrate_Override_Channel on Webrate_Source_Property.Property_ID= Webrate_Default_Channel.Property_ID
and
Webrate.Occupancy_DT between Webrate_Override_Channel.Channel_Override_Start_DT and Webrate_Override_Channel.Channel_Override_End_DT
GO


