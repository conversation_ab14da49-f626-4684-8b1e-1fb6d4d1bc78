DROP PROCEDURE IF EXISTS [dbo].[usp_get_change_report_comparative_view_BV]
GO

CREATE PROCEDURE [dbo].[usp_get_change_report_comparative_view_BV]
(
    @property_id int,
    @Business_Group_ID varchar(1000),
    @business_dt date,
    @start_date date,
    @end_date date,
    @isRollingDate int,
    @rolling_business_dt nvarchar(50),
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50)
)
AS
BEGIN

    declare @caughtupdate date
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    ) --> extract caughtup date for a property

    if (@isRollingDate = 1)
begin
        set @business_dt =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_business_dt, @caughtupdate)
        )
        set @start_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate)
        )
        set @end_date =
        (
            select absolute_date
            from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate)
        )
end

select number = ROW_NUMBER() OVER (ORDER BY Business_Group_ID),
        Business_Group_ID,
       Business_Group_Name
into #tempBV
from Business_Group
where Property_ID = @property_id
  and Business_Group_ID in (
    SELECT Value FROM varcharToInt(@Business_Group_ID, ',')
)
  and Status_ID = 1

declare @bv1 int = (
                           Select Business_Group_ID from #tempBV where number = 1
                       )
    declare @bv2 int = (
                           Select Business_Group_ID from #tempBV where number = 2
                       )
    declare @bv3 int = (
                           Select Business_Group_ID from #tempBV where number = 3
                       )
    declare @bv4 int = (
                           Select Business_Group_ID from #tempBV where number = 4
                       )
    declare @bv5 int = (
                           Select Business_Group_ID from #tempBV where number = 5
                       )
    declare @bv6 int = (
                           Select Business_Group_ID from #tempBV where number = 6
                       )
    declare @bv7 int = (
                           Select Business_Group_ID from #tempBV where number = 7
                       )
    declare @bv8 int = (
                           Select Business_Group_ID from #tempBV where number = 8
                       )
    declare @bv9 int = (
                           Select Business_Group_ID from #tempBV where number = 9
                       )
    declare @bv10 int = (
                            Select Business_Group_ID from #tempBV where number = 10
                        )
    declare @bv11 int = (
                            Select Business_Group_ID from #tempBV where number = 11
                        )
    declare @bv12 int = (
                            Select Business_Group_ID from #tempBV where number = 12
                        )
    declare @bv13 int = (
                            Select Business_Group_ID from #tempBV where number = 13
                        )
    declare @bv14 int = (
                            Select Business_Group_ID from #tempBV where number = 14
                        )
    declare @bv15 int = (
                            Select Business_Group_ID from #tempBV where number = 15
                        )
    declare @bv16 int = (
                            Select Business_Group_ID from #tempBV where number = 16
                        )
    declare @bv17 int = (
                            Select Business_Group_ID from #tempBV where number = 17
                        )
    declare @bv18 int = (
                            Select Business_Group_ID from #tempBV where number = 18
                        )
    declare @bv19 int = (
                            Select Business_Group_ID from #tempBV where number = 19
                        )
    declare @bv20 int = (
                            Select Business_Group_ID from #tempBV where number = 20
                        )
    declare @bv21 int = (
                            Select Business_Group_ID from #tempBV where number = 21
                        )
    declare @bv22 int = (
                            Select Business_Group_ID from #tempBV where number = 22
                        )
    declare @bv23 int = (
                            Select Business_Group_ID from #tempBV where number = 23
                        )
    declare @bv24 int = (
                            Select Business_Group_ID from #tempBV where number = 24
                        )
    declare @bv25 int = (
                            Select Business_Group_ID from #tempBV where number = 25
                        )
    declare @bv26 int = (
                            Select Business_Group_ID from #tempBV where number = 26
                        )
    declare @bv27 int = (
                            Select Business_Group_ID from #tempBV where number = 27
                        )
    declare @bv28 int = (
                            Select Business_Group_ID from #tempBV where number = 28
                        )
    declare @bv29 int = (
                            Select Business_Group_ID from #tempBV where number = 29
                        )
    declare @bv30 int = (
                            Select Business_Group_ID from #tempBV where number = 30
                        )
    declare @bv31 int = (
                            Select Business_Group_ID from #tempBV where number = 31
                        )
    declare @bv32 int = (
                            Select Business_Group_ID from #tempBV where number = 32
                        )
    declare @bv33 int = (
                            Select Business_Group_ID from #tempBV where number = 33
                        )
    declare @bv34 int = (
                            Select Business_Group_ID from #tempBV where number = 34
                        )
    declare @bv35 int = (
                            Select Business_Group_ID from #tempBV where number = 35
                        )
    declare @bv36 int = (
                            Select Business_Group_ID from #tempBV where number = 36
                        )
    declare @bv37 int = (
                            Select Business_Group_ID from #tempBV where number = 37
                        )
    declare @bv38 int = (
                            Select Business_Group_ID from #tempBV where number = 38
                        )
    declare @bv39 int = (
                            Select Business_Group_ID from #tempBV where number = 39
                        )
    declare @bv40 int = (
                            Select Business_Group_ID from #tempBV where number = 40
                        )
    declare @bv41 int = (
                            Select Business_Group_ID from #tempBV where number = 41
                        )
    declare @bv42 int = (
                            Select Business_Group_ID from #tempBV where number = 42
                        )
    declare @bv43 int = (
                            Select Business_Group_ID from #tempBV where number = 43
                        )
    declare @bv44 int = (
                            Select Business_Group_ID from #tempBV where number = 44
                        )
    declare @bv45 int = (
                            Select Business_Group_ID from #tempBV where number = 45
                        )
    declare @bv46 int = (
                            Select Business_Group_ID from #tempBV where number = 46
                        )
    declare @bv47 int = (
                            Select Business_Group_ID from #tempBV where number = 47
                        )
    declare @bv48 int = (
                            Select Business_Group_ID from #tempBV where number = 48
                        )
    declare @bv49 int = (
                            Select Business_Group_ID from #tempBV where number = 49
                        )
    declare @bv50 int = (
                            Select Business_Group_ID from #tempBV where number = 50
                        )


select @property_id property_id,
       CAST(calendar_date as date) Occupancy_DT,
       Business_Group_ID,
       Business_Group_Name
into #calendare_temp
from calendar_dim
         left join #tempBV
                   on Business_Group_ID is not null
where calendar_date
          between @start_date and @end_date

select *
into #activity
from ufn_get_activity_by_individual_bv(@property_id, @Business_Group_ID, @start_date, @end_date)

select *
into #forecast
from dbo.ufn_get_occupancy_forecast_by_individual_bv(@property_id, @Business_Group_ID, @start_date, @end_date)

select occupancy_dt,
       property_id,
       Business_Group_ID,
       rooms_sold,
       room_revenue,
       adr
into #activity_asof
from ufn_get_activity_asof_lastOptimization_or_businessdate_by_individual_bv(
        @property_id,
        @Business_Group_ID,
        @business_dt,
        @start_date,
        @end_date,
        @rolling_business_dt
    )

select *
into #forecast_asof
from ufn_get_occupancy_forecast_asof_lastOptimization_or_businessdate_by_individual_bv(
        @property_id,
        @Business_Group_ID,
        @business_dt,
        @start_date,
        @end_date,
        @rolling_business_dt
    )

select *
into #group_block
from ufn_get_groupBlock_groupPickup_by_BusinessView(@property_id, @Business_Group_ID, @start_date, @end_date, 0)

--- extract report metrics

create table #change_report_bv_comparative_view
(
    occupancy_dt date,
    dow varchar(10),
    bv1_businessgroupname nvarchar(100),
    bv2_businessgroupname nvarchar(100),
    bv3_businessgroupname nvarchar(100),
    bv4_businessgroupname nvarchar(100),
    bv5_businessgroupname nvarchar(100),
    bv6_businessgroupname nvarchar(100),
    bv7_businessgroupname nvarchar(100),
    bv8_businessgroupname nvarchar(100),
    bv9_businessgroupname nvarchar(100),
    bv10_businessgroupname nvarchar(100),
    bv11_businessgroupname nvarchar(100),
    bv12_businessgroupname nvarchar(100),
    bv13_businessgroupname nvarchar(100),
    bv14_businessgroupname nvarchar(100),
    bv15_businessgroupname nvarchar(100),
    bv16_businessgroupname nvarchar(100),
    bv17_businessgroupname nvarchar(100),
    bv18_businessgroupname nvarchar(100),
    bv19_businessgroupname nvarchar(100),
    bv20_businessgroupname nvarchar(100),
    bv21_businessgroupname nvarchar(100),
    bv22_businessgroupname nvarchar(100),
    bv23_businessgroupname nvarchar(100),
    bv24_businessgroupname nvarchar(100),
    bv25_businessgroupname nvarchar(100),
    bv26_businessgroupname nvarchar(100),
    bv27_businessgroupname nvarchar(100),
    bv28_businessgroupname nvarchar(100),
    bv29_businessgroupname nvarchar(100),
    bv30_businessgroupname nvarchar(100),
    bv31_businessgroupname nvarchar(100),
    bv32_businessgroupname nvarchar(100),
    bv33_businessgroupname nvarchar(100),
    bv34_businessgroupname nvarchar(100),
    bv35_businessgroupname nvarchar(100),
    bv36_businessgroupname nvarchar(100),
    bv37_businessgroupname nvarchar(100),
    bv38_businessgroupname nvarchar(100),
    bv39_businessgroupname nvarchar(100),
    bv40_businessgroupname nvarchar(100),
    bv41_businessgroupname nvarchar(100),
    bv42_businessgroupname nvarchar(100),
    bv43_businessgroupname nvarchar(100),
    bv44_businessgroupname nvarchar(100),
    bv45_businessgroupname nvarchar(100),
    bv46_businessgroupname nvarchar(100),
    bv47_businessgroupname nvarchar(100),
    bv48_businessgroupname nvarchar(100),
    bv49_businessgroupname nvarchar(100),
    bv50_businessgroupname nvarchar(100),
    bv1_roomsoldcurrent int,
    bv1_roomssoldchange int,
    bv2_roomsoldcurrent int,
    bv2_roomssoldchange int,
    bv3_roomsoldcurrent int,
    bv3_roomssoldchange int,
    bv4_roomsoldcurrent int,
    bv4_roomssoldchange int,
    bv5_roomsoldcurrent int,
    bv5_roomssoldchange int,
    bv6_roomsoldcurrent int,
    bv6_roomssoldchange int,
    bv7_roomsoldcurrent int,
    bv7_roomssoldchange int,
    bv8_roomsoldcurrent int,
    bv8_roomssoldchange int,
    bv9_roomsoldcurrent int,
    bv9_roomssoldchange int,
    bv10_roomsoldcurrent int,
    bv10_roomssoldchange int,
    bv11_roomsoldcurrent int,
    bv11_roomssoldchange int,
    bv12_roomsoldcurrent int,
    bv12_roomssoldchange int,
    bv13_roomsoldcurrent int,
    bv13_roomssoldchange int,
    bv14_roomsoldcurrent int,
    bv14_roomssoldchange int,
    bv15_roomsoldcurrent int,
    bv15_roomssoldchange int,
    bv16_roomsoldcurrent int,
    bv16_roomssoldchange int,
    bv17_roomsoldcurrent int,
    bv17_roomssoldchange int,
    bv18_roomsoldcurrent int,
    bv18_roomssoldchange int,
    bv19_roomsoldcurrent int,
    bv19_roomssoldchange int,
    bv20_roomsoldcurrent int,
    bv20_roomssoldchange int,
    bv21_roomsoldcurrent int,
    bv21_roomssoldchange int,
    bv22_roomsoldcurrent int,
    bv22_roomssoldchange int,
    bv23_roomsoldcurrent int,
    bv23_roomssoldchange int,
    bv24_roomsoldcurrent int,
    bv24_roomssoldchange int,
    bv25_roomsoldcurrent int,
    bv25_roomssoldchange int,
    bv26_roomsoldcurrent int,
    bv26_roomssoldchange int,
    bv27_roomsoldcurrent int,
    bv27_roomssoldchange int,
    bv28_roomsoldcurrent int,
    bv28_roomssoldchange int,
    bv29_roomsoldcurrent int,
    bv29_roomssoldchange int,
    bv30_roomsoldcurrent int,
    bv30_roomssoldchange int,
    bv31_roomsoldcurrent int,
    bv31_roomssoldchange int,
    bv32_roomsoldcurrent int,
    bv32_roomssoldchange int,
    bv33_roomsoldcurrent int,
    bv33_roomssoldchange int,
    bv34_roomsoldcurrent int,
    bv34_roomssoldchange int,
    bv35_roomsoldcurrent int,
    bv35_roomssoldchange int,
    bv36_roomsoldcurrent int,
    bv36_roomssoldchange int,
    bv37_roomsoldcurrent int,
    bv37_roomssoldchange int,
    bv38_roomsoldcurrent int,
    bv38_roomssoldchange int,
    bv39_roomsoldcurrent int,
    bv39_roomssoldchange int,
    bv40_roomsoldcurrent int,
    bv40_roomssoldchange int,
    bv41_roomsoldcurrent int,
    bv41_roomssoldchange int,
    bv42_roomsoldcurrent int,
    bv42_roomssoldchange int,
    bv43_roomsoldcurrent int,
    bv43_roomssoldchange int,
    bv44_roomsoldcurrent int,
    bv44_roomssoldchange int,
    bv45_roomsoldcurrent int,
    bv45_roomssoldchange int,
    bv46_roomsoldcurrent int,
    bv46_roomssoldchange int,
    bv47_roomsoldcurrent int,
    bv47_roomssoldchange int,
    bv48_roomsoldcurrent int,
    bv48_roomssoldchange int,
    bv49_roomsoldcurrent int,
    bv49_roomssoldchange int,
    bv50_roomsoldcurrent int,
    bv50_roomssoldchange int,
    bv1_occfcstcurrent numeric(8, 2),
    bv1_occfcstchange numeric(8, 2),
    bv2_occfcstcurrent numeric(8, 2),
    bv2_occfcstchange numeric(8, 2),
    bv3_occfcstcurrent numeric(8, 2),
    bv3_occfcstchange numeric(8, 2),
    bv4_occfcstcurrent numeric(8, 2),
    bv4_occfcstchange numeric(8, 2),
    bv5_occfcstcurrent numeric(8, 2),
    bv5_occfcstchange numeric(8, 2),
    bv6_occfcstcurrent numeric(8, 2),
    bv6_occfcstchange numeric(8, 2),
    bv7_occfcstcurrent numeric(8, 2),
    bv7_occfcstchange numeric(8, 2),
    bv8_occfcstcurrent numeric(8, 2),
    bv8_occfcstchange numeric(8, 2),
    bv9_occfcstcurrent numeric(8, 2),
    bv9_occfcstchange numeric(8, 2),
    bv10_occfcstcurrent numeric(8, 2),
    bv10_occfcstchange numeric(8, 2),
    bv11_occfcstcurrent numeric(8, 2),
    bv11_occfcstchange numeric(8, 2),
    bv12_occfcstcurrent numeric(8, 2),
    bv12_occfcstchange numeric(8, 2),
    bv13_occfcstcurrent numeric(8, 2),
    bv13_occfcstchange numeric(8, 2),
    bv14_occfcstcurrent numeric(8, 2),
    bv14_occfcstchange numeric(8, 2),
    bv15_occfcstcurrent numeric(8, 2),
    bv15_occfcstchange numeric(8, 2),
    bv16_occfcstcurrent numeric(8, 2),
    bv16_occfcstchange numeric(8, 2),
    bv17_occfcstcurrent numeric(8, 2),
    bv17_occfcstchange numeric(8, 2),
    bv18_occfcstcurrent numeric(8, 2),
    bv18_occfcstchange numeric(8, 2),
    bv19_occfcstcurrent numeric(8, 2),
    bv19_occfcstchange numeric(8, 2),
    bv20_occfcstcurrent numeric(8, 2),
    bv20_occfcstchange numeric(8, 2),
    bv21_occfcstcurrent numeric(8, 2),
    bv21_occfcstchange numeric(8, 2),
    bv22_occfcstcurrent numeric(8, 2),
    bv22_occfcstchange numeric(8, 2),
    bv23_occfcstcurrent numeric(8, 2),
    bv23_occfcstchange numeric(8, 2),
    bv24_occfcstcurrent numeric(8, 2),
    bv24_occfcstchange numeric(8, 2),
    bv25_occfcstcurrent numeric(8, 2),
    bv25_occfcstchange numeric(8, 2),
    bv26_occfcstcurrent numeric(8, 2),
    bv26_occfcstchange numeric(8, 2),
    bv27_occfcstcurrent numeric(8, 2),
    bv27_occfcstchange numeric(8, 2),
    bv28_occfcstcurrent numeric(8, 2),
    bv28_occfcstchange numeric(8, 2),
    bv29_occfcstcurrent numeric(8, 2),
    bv29_occfcstchange numeric(8, 2),
    bv30_occfcstcurrent numeric(8, 2),
    bv30_occfcstchange numeric(8, 2),
    bv31_occfcstcurrent numeric(8, 2),
    bv31_occfcstchange numeric(8, 2),
    bv32_occfcstcurrent numeric(8, 2),
    bv32_occfcstchange numeric(8, 2),
    bv33_occfcstcurrent numeric(8, 2),
    bv33_occfcstchange numeric(8, 2),
    bv34_occfcstcurrent numeric(8, 2),
    bv34_occfcstchange numeric(8, 2),
    bv35_occfcstcurrent numeric(8, 2),
    bv35_occfcstchange numeric(8, 2),
    bv36_occfcstcurrent numeric(8, 2),
    bv36_occfcstchange numeric(8, 2),
    bv37_occfcstcurrent numeric(8, 2),
    bv37_occfcstchange numeric(8, 2),
    bv38_occfcstcurrent numeric(8, 2),
    bv38_occfcstchange numeric(8, 2),
    bv39_occfcstcurrent numeric(8, 2),
    bv39_occfcstchange numeric(8, 2),
    bv40_occfcstcurrent numeric(8, 2),
    bv40_occfcstchange numeric(8, 2),
    bv41_occfcstcurrent numeric(8, 2),
    bv41_occfcstchange numeric(8, 2),
    bv42_occfcstcurrent numeric(8, 2),
    bv42_occfcstchange numeric(8, 2),
    bv43_occfcstcurrent numeric(8, 2),
    bv43_occfcstchange numeric(8, 2),
    bv44_occfcstcurrent numeric(8, 2),
    bv44_occfcstchange numeric(8, 2),
    bv45_occfcstcurrent numeric(8, 2),
    bv45_occfcstchange numeric(8, 2),
    bv46_occfcstcurrent numeric(8, 2),
    bv46_occfcstchange numeric(8, 2),
    bv47_occfcstcurrent numeric(8, 2),
    bv47_occfcstchange numeric(8, 2),
    bv48_occfcstcurrent numeric(8, 2),
    bv48_occfcstchange numeric(8, 2),
    bv49_occfcstcurrent numeric(8, 2),
    bv49_occfcstchange numeric(8, 2),
    bv50_occfcstcurrent numeric(8, 2),
    bv50_occfcstchange numeric(8, 2),
    bv1_bookedroomrevenuecurrent numeric(19, 5),
    bv1_bookedroomrevenuechange numeric(19, 5),
    bv2_bookedroomrevenuecurrent numeric(19, 5),
    bv2_bookedroomrevenuechange numeric(19, 5),
    bv3_bookedroomrevenuecurrent numeric(19, 5),
    bv3_bookedroomrevenuechange numeric(19, 5),
    bv4_bookedroomrevenuecurrent numeric(19, 5),
    bv4_bookedroomrevenuechange numeric(19, 5),
    bv5_bookedroomrevenuecurrent numeric(19, 5),
    bv5_bookedroomrevenuechange numeric(19, 5),
    bv6_bookedroomrevenuecurrent numeric(19, 5),
    bv6_bookedroomrevenuechange numeric(19, 5),
    bv7_bookedroomrevenuecurrent numeric(19, 5),
    bv7_bookedroomrevenuechange numeric(19, 5),
    bv8_bookedroomrevenuecurrent numeric(19, 5),
    bv8_bookedroomrevenuechange numeric(19, 5),
    bv9_bookedroomrevenuecurrent numeric(19, 5),
    bv9_bookedroomrevenuechange numeric(19, 5),
    bv10_bookedroomrevenuecurrent numeric(19, 5),
    bv10_bookedroomrevenuechange numeric(19, 5),
    bv11_bookedroomrevenuecurrent numeric(19, 5),
    bv11_bookedroomrevenuechange numeric(19, 5),
    bv12_bookedroomrevenuecurrent numeric(19, 5),
    bv12_bookedroomrevenuechange numeric(19, 5),
    bv13_bookedroomrevenuecurrent numeric(19, 5),
    bv13_bookedroomrevenuechange numeric(19, 5),
    bv14_bookedroomrevenuecurrent numeric(19, 5),
    bv14_bookedroomrevenuechange numeric(19, 5),
    bv15_bookedroomrevenuecurrent numeric(19, 5),
    bv15_bookedroomrevenuechange numeric(19, 5),
    bv16_bookedroomrevenuecurrent numeric(19, 5),
    bv16_bookedroomrevenuechange numeric(19, 5),
    bv17_bookedroomrevenuecurrent numeric(19, 5),
    bv17_bookedroomrevenuechange numeric(19, 5),
    bv18_bookedroomrevenuecurrent numeric(19, 5),
    bv18_bookedroomrevenuechange numeric(19, 5),
    bv19_bookedroomrevenuecurrent numeric(19, 5),
    bv19_bookedroomrevenuechange numeric(19, 5),
    bv20_bookedroomrevenuecurrent numeric(19, 5),
    bv20_bookedroomrevenuechange numeric(19, 5),
    bv21_bookedroomrevenuecurrent numeric(19, 5),
    bv21_bookedroomrevenuechange numeric(19, 5),
    bv22_bookedroomrevenuecurrent numeric(19, 5),
    bv22_bookedroomrevenuechange numeric(19, 5),
    bv23_bookedroomrevenuecurrent numeric(19, 5),
    bv23_bookedroomrevenuechange numeric(19, 5),
    bv24_bookedroomrevenuecurrent numeric(19, 5),
    bv24_bookedroomrevenuechange numeric(19, 5),
    bv25_bookedroomrevenuecurrent numeric(19, 5),
    bv25_bookedroomrevenuechange numeric(19, 5),
    bv26_bookedroomrevenuecurrent numeric(19, 5),
    bv26_bookedroomrevenuechange numeric(19, 5),
    bv27_bookedroomrevenuecurrent numeric(19, 5),
    bv27_bookedroomrevenuechange numeric(19, 5),
    bv28_bookedroomrevenuecurrent numeric(19, 5),
    bv28_bookedroomrevenuechange numeric(19, 5),
    bv29_bookedroomrevenuecurrent numeric(19, 5),
    bv29_bookedroomrevenuechange numeric(19, 5),
    bv30_bookedroomrevenuecurrent numeric(19, 5),
    bv30_bookedroomrevenuechange numeric(19, 5),
    bv31_bookedroomrevenuecurrent numeric(19, 5),
    bv31_bookedroomrevenuechange numeric(19, 5),
    bv32_bookedroomrevenuecurrent numeric(19, 5),
    bv32_bookedroomrevenuechange numeric(19, 5),
    bv33_bookedroomrevenuecurrent numeric(19, 5),
    bv33_bookedroomrevenuechange numeric(19, 5),
    bv34_bookedroomrevenuecurrent numeric(19, 5),
    bv34_bookedroomrevenuechange numeric(19, 5),
    bv35_bookedroomrevenuecurrent numeric(19, 5),
    bv35_bookedroomrevenuechange numeric(19, 5),
    bv36_bookedroomrevenuecurrent numeric(19, 5),
    bv36_bookedroomrevenuechange numeric(19, 5),
    bv37_bookedroomrevenuecurrent numeric(19, 5),
    bv37_bookedroomrevenuechange numeric(19, 5),
    bv38_bookedroomrevenuecurrent numeric(19, 5),
    bv38_bookedroomrevenuechange numeric(19, 5),
    bv39_bookedroomrevenuecurrent numeric(19, 5),
    bv39_bookedroomrevenuechange numeric(19, 5),
    bv40_bookedroomrevenuecurrent numeric(19, 5),
    bv40_bookedroomrevenuechange numeric(19, 5),
    bv41_bookedroomrevenuecurrent numeric(19, 5),
    bv41_bookedroomrevenuechange numeric(19, 5),
    bv42_bookedroomrevenuecurrent numeric(19, 5),
    bv42_bookedroomrevenuechange numeric(19, 5),
    bv43_bookedroomrevenuecurrent numeric(19, 5),
    bv43_bookedroomrevenuechange numeric(19, 5),
    bv44_bookedroomrevenuecurrent numeric(19, 5),
    bv44_bookedroomrevenuechange numeric(19, 5),
    bv45_bookedroomrevenuecurrent numeric(19, 5),
    bv45_bookedroomrevenuechange numeric(19, 5),
    bv46_bookedroomrevenuecurrent numeric(19, 5),
    bv46_bookedroomrevenuechange numeric(19, 5),
    bv47_bookedroomrevenuecurrent numeric(19, 5),
    bv47_bookedroomrevenuechange numeric(19, 5),
    bv48_bookedroomrevenuecurrent numeric(19, 5),
    bv48_bookedroomrevenuechange numeric(19, 5),
    bv49_bookedroomrevenuecurrent numeric(19, 5),
    bv49_bookedroomrevenuechange numeric(19, 5),
    bv50_bookedroomrevenuecurrent numeric(19, 5),
    bv50_bookedroomrevenuechange numeric(19, 5),
    bv1_fcstedroomrevenuecurrent numeric(19, 5),
    bv1_fcstedroomrevenuechange numeric(19, 5),
    bv2_fcstedroomrevenuecurrent numeric(19, 5),
    bv2_fcstedroomrevenuechange numeric(19, 5),
    bv3_fcstedroomrevenuecurrent numeric(19, 5),
    bv3_fcstedroomrevenuechange numeric(19, 5),
    bv4_fcstedroomrevenuecurrent numeric(19, 5),
    bv4_fcstedroomrevenuechange numeric(19, 5),
    bv5_fcstedroomrevenuecurrent numeric(19, 5),
    bv5_fcstedroomrevenuechange numeric(19, 5),
    bv6_fcstedroomrevenuecurrent numeric(19, 5),
    bv6_fcstedroomrevenuechange numeric(19, 5),
    bv7_fcstedroomrevenuecurrent numeric(19, 5),
    bv7_fcstedroomrevenuechange numeric(19, 5),
    bv8_fcstedroomrevenuecurrent numeric(19, 5),
    bv8_fcstedroomrevenuechange numeric(19, 5),
    bv9_fcstedroomrevenuecurrent numeric(19, 5),
    bv9_fcstedroomrevenuechange numeric(19, 5),
    bv10_fcstedroomrevenuecurrent numeric(19, 5),
    bv10_fcstedroomrevenuechange numeric(19, 5),
    bv11_fcstedroomrevenuecurrent numeric(19, 5),
    bv11_fcstedroomrevenuechange numeric(19, 5),
    bv12_fcstedroomrevenuecurrent numeric(19, 5),
    bv12_fcstedroomrevenuechange numeric(19, 5),
    bv13_fcstedroomrevenuecurrent numeric(19, 5),
    bv13_fcstedroomrevenuechange numeric(19, 5),
    bv14_fcstedroomrevenuecurrent numeric(19, 5),
    bv14_fcstedroomrevenuechange numeric(19, 5),
    bv15_fcstedroomrevenuecurrent numeric(19, 5),
    bv15_fcstedroomrevenuechange numeric(19, 5),
    bv16_fcstedroomrevenuecurrent numeric(19, 5),
    bv16_fcstedroomrevenuechange numeric(19, 5),
    bv17_fcstedroomrevenuecurrent numeric(19, 5),
    bv17_fcstedroomrevenuechange numeric(19, 5),
    bv18_fcstedroomrevenuecurrent numeric(19, 5),
    bv18_fcstedroomrevenuechange numeric(19, 5),
    bv19_fcstedroomrevenuecurrent numeric(19, 5),
    bv19_fcstedroomrevenuechange numeric(19, 5),
    bv20_fcstedroomrevenuecurrent numeric(19, 5),
    bv20_fcstedroomrevenuechange numeric(19, 5),
    bv21_fcstedroomrevenuecurrent numeric(19, 5),
    bv21_fcstedroomrevenuechange numeric(19, 5),
    bv22_fcstedroomrevenuecurrent numeric(19, 5),
    bv22_fcstedroomrevenuechange numeric(19, 5),
    bv23_fcstedroomrevenuecurrent numeric(19, 5),
    bv23_fcstedroomrevenuechange numeric(19, 5),
    bv24_fcstedroomrevenuecurrent numeric(19, 5),
    bv24_fcstedroomrevenuechange numeric(19, 5),
    bv25_fcstedroomrevenuecurrent numeric(19, 5),
    bv25_fcstedroomrevenuechange numeric(19, 5),
    bv26_fcstedroomrevenuecurrent numeric(19, 5),
    bv26_fcstedroomrevenuechange numeric(19, 5),
    bv27_fcstedroomrevenuecurrent numeric(19, 5),
    bv27_fcstedroomrevenuechange numeric(19, 5),
    bv28_fcstedroomrevenuecurrent numeric(19, 5),
    bv28_fcstedroomrevenuechange numeric(19, 5),
    bv29_fcstedroomrevenuecurrent numeric(19, 5),
    bv29_fcstedroomrevenuechange numeric(19, 5),
    bv30_fcstedroomrevenuecurrent numeric(19, 5),
    bv30_fcstedroomrevenuechange numeric(19, 5),
    bv31_fcstedroomrevenuecurrent numeric(19, 5),
    bv31_fcstedroomrevenuechange numeric(19, 5),
    bv32_fcstedroomrevenuecurrent numeric(19, 5),
    bv32_fcstedroomrevenuechange numeric(19, 5),
    bv33_fcstedroomrevenuecurrent numeric(19, 5),
    bv33_fcstedroomrevenuechange numeric(19, 5),
    bv34_fcstedroomrevenuecurrent numeric(19, 5),
    bv34_fcstedroomrevenuechange numeric(19, 5),
    bv35_fcstedroomrevenuecurrent numeric(19, 5),
    bv35_fcstedroomrevenuechange numeric(19, 5),
    bv36_fcstedroomrevenuecurrent numeric(19, 5),
    bv36_fcstedroomrevenuechange numeric(19, 5),
    bv37_fcstedroomrevenuecurrent numeric(19, 5),
    bv37_fcstedroomrevenuechange numeric(19, 5),
    bv38_fcstedroomrevenuecurrent numeric(19, 5),
    bv38_fcstedroomrevenuechange numeric(19, 5),
    bv39_fcstedroomrevenuecurrent numeric(19, 5),
    bv39_fcstedroomrevenuechange numeric(19, 5),
    bv40_fcstedroomrevenuecurrent numeric(19, 5),
    bv40_fcstedroomrevenuechange numeric(19, 5),
    bv41_fcstedroomrevenuecurrent numeric(19, 5),
    bv41_fcstedroomrevenuechange numeric(19, 5),
    bv42_fcstedroomrevenuecurrent numeric(19, 5),
    bv42_fcstedroomrevenuechange numeric(19, 5),
    bv43_fcstedroomrevenuecurrent numeric(19, 5),
    bv43_fcstedroomrevenuechange numeric(19, 5),
    bv44_fcstedroomrevenuecurrent numeric(19, 5),
    bv44_fcstedroomrevenuechange numeric(19, 5),
    bv45_fcstedroomrevenuecurrent numeric(19, 5),
    bv45_fcstedroomrevenuechange numeric(19, 5),
    bv46_fcstedroomrevenuecurrent numeric(19, 5),
    bv46_fcstedroomrevenuechange numeric(19, 5),
    bv47_fcstedroomrevenuecurrent numeric(19, 5),
    bv47_fcstedroomrevenuechange numeric(19, 5),
    bv48_fcstedroomrevenuecurrent numeric(19, 5),
    bv48_fcstedroomrevenuechange numeric(19, 5),
    bv49_fcstedroomrevenuecurrent numeric(19, 5),
    bv49_fcstedroomrevenuechange numeric(19, 5),
    bv50_fcstedroomrevenuecurrent numeric(19, 5),
    bv50_fcstedroomrevenuechange numeric(19, 5),
    bv1_bookedadrcurrent numeric(19, 5),
    bv1_bookedadrchange numeric(19, 5),
    bv2_bookedadrcurrent numeric(19, 5),
    bv2_bookedadrchange numeric(19, 5),
    bv3_bookedadrcurrent numeric(19, 5),
    bv3_bookedadrchange numeric(19, 5),
    bv4_bookedadrcurrent numeric(19, 5),
    bv4_bookedadrchange numeric(19, 5),
    bv5_bookedadrcurrent numeric(19, 5),
    bv5_bookedadrchange numeric(19, 5),
    bv6_bookedadrcurrent numeric(19, 5),
    bv6_bookedadrchange numeric(19, 5),
    bv7_bookedadrcurrent numeric(19, 5),
    bv7_bookedadrchange numeric(19, 5),
    bv8_bookedadrcurrent numeric(19, 5),
    bv8_bookedadrchange numeric(19, 5),
    bv9_bookedadrcurrent numeric(19, 5),
    bv9_bookedadrchange numeric(19, 5),
    bv10_bookedadrcurrent numeric(19, 5),
    bv10_bookedadrchange numeric(19, 5),
    bv11_bookedadrcurrent numeric(19, 5),
    bv11_bookedadrchange numeric(19, 5),
    bv12_bookedadrcurrent numeric(19, 5),
    bv12_bookedadrchange numeric(19, 5),
    bv13_bookedadrcurrent numeric(19, 5),
    bv13_bookedadrchange numeric(19, 5),
    bv14_bookedadrcurrent numeric(19, 5),
    bv14_bookedadrchange numeric(19, 5),
    bv15_bookedadrcurrent numeric(19, 5),
    bv15_bookedadrchange numeric(19, 5),
    bv16_bookedadrcurrent numeric(19, 5),
    bv16_bookedadrchange numeric(19, 5),
    bv17_bookedadrcurrent numeric(19, 5),
    bv17_bookedadrchange numeric(19, 5),
    bv18_bookedadrcurrent numeric(19, 5),
    bv18_bookedadrchange numeric(19, 5),
    bv19_bookedadrcurrent numeric(19, 5),
    bv19_bookedadrchange numeric(19, 5),
    bv20_bookedadrcurrent numeric(19, 5),
    bv20_bookedadrchange numeric(19, 5),
    bv21_bookedadrcurrent numeric(19, 5),
    bv21_bookedadrchange numeric(19, 5),
    bv22_bookedadrcurrent numeric(19, 5),
    bv22_bookedadrchange numeric(19, 5),
    bv23_bookedadrcurrent numeric(19, 5),
    bv23_bookedadrchange numeric(19, 5),
    bv24_bookedadrcurrent numeric(19, 5),
    bv24_bookedadrchange numeric(19, 5),
    bv25_bookedadrcurrent numeric(19, 5),
    bv25_bookedadrchange numeric(19, 5),
    bv26_bookedadrcurrent numeric(19, 5),
    bv26_bookedadrchange numeric(19, 5),
    bv27_bookedadrcurrent numeric(19, 5),
    bv27_bookedadrchange numeric(19, 5),
    bv28_bookedadrcurrent numeric(19, 5),
    bv28_bookedadrchange numeric(19, 5),
    bv29_bookedadrcurrent numeric(19, 5),
    bv29_bookedadrchange numeric(19, 5),
    bv30_bookedadrcurrent numeric(19, 5),
    bv30_bookedadrchange numeric(19, 5),
    bv31_bookedadrcurrent numeric(19, 5),
    bv31_bookedadrchange numeric(19, 5),
    bv32_bookedadrcurrent numeric(19, 5),
    bv32_bookedadrchange numeric(19, 5),
    bv33_bookedadrcurrent numeric(19, 5),
    bv33_bookedadrchange numeric(19, 5),
    bv34_bookedadrcurrent numeric(19, 5),
    bv34_bookedadrchange numeric(19, 5),
    bv35_bookedadrcurrent numeric(19, 5),
    bv35_bookedadrchange numeric(19, 5),
    bv36_bookedadrcurrent numeric(19, 5),
    bv36_bookedadrchange numeric(19, 5),
    bv37_bookedadrcurrent numeric(19, 5),
    bv37_bookedadrchange numeric(19, 5),
    bv38_bookedadrcurrent numeric(19, 5),
    bv38_bookedadrchange numeric(19, 5),
    bv39_bookedadrcurrent numeric(19, 5),
    bv39_bookedadrchange numeric(19, 5),
    bv40_bookedadrcurrent numeric(19, 5),
    bv40_bookedadrchange numeric(19, 5),
    bv41_bookedadrcurrent numeric(19, 5),
    bv41_bookedadrchange numeric(19, 5),
    bv42_bookedadrcurrent numeric(19, 5),
    bv42_bookedadrchange numeric(19, 5),
    bv43_bookedadrcurrent numeric(19, 5),
    bv43_bookedadrchange numeric(19, 5),
    bv44_bookedadrcurrent numeric(19, 5),
    bv44_bookedadrchange numeric(19, 5),
    bv45_bookedadrcurrent numeric(19, 5),
    bv45_bookedadrchange numeric(19, 5),
    bv46_bookedadrcurrent numeric(19, 5),
    bv46_bookedadrchange numeric(19, 5),
    bv47_bookedadrcurrent numeric(19, 5),
    bv47_bookedadrchange numeric(19, 5),
    bv48_bookedadrcurrent numeric(19, 5),
    bv48_bookedadrchange numeric(19, 5),
    bv49_bookedadrcurrent numeric(19, 5),
    bv49_bookedadrchange numeric(19, 5),
    bv50_bookedadrcurrent numeric(19, 5),
    bv50_bookedadrchange numeric(19, 5),
    bv1_fcstedadrcurrent numeric(19, 5),
    bv1_fcstedadrchange numeric(19, 5),
    bv2_fcstedadrcurrent numeric(19, 5),
    bv2_fcstedadrchange numeric(19, 5),
    bv3_fcstedadrcurrent numeric(19, 5),
    bv3_fcstedadrchange numeric(19, 5),
    bv4_fcstedadrcurrent numeric(19, 5),
    bv4_fcstedadrchange numeric(19, 5),
    bv5_fcstedadrcurrent numeric(19, 5),
    bv5_fcstedadrchange numeric(19, 5),
    bv6_fcstedadrcurrent numeric(19, 5),
    bv6_fcstedadrchange numeric(19, 5),
    bv7_fcstedadrcurrent numeric(19, 5),
    bv7_fcstedadrchange numeric(19, 5),
    bv8_fcstedadrcurrent numeric(19, 5),
    bv8_fcstedadrchange numeric(19, 5),
    bv9_fcstedadrcurrent numeric(19, 5),
    bv9_fcstedadrchange numeric(19, 5),
    bv10_fcstedadrcurrent numeric(19, 5),
    bv10_fcstedadrchange numeric(19, 5),
    bv11_fcstedadrcurrent numeric(19, 5),
    bv11_fcstedadrchange numeric(19, 5),
    bv12_fcstedadrcurrent numeric(19, 5),
    bv12_fcstedadrchange numeric(19, 5),
    bv13_fcstedadrcurrent numeric(19, 5),
    bv13_fcstedadrchange numeric(19, 5),
    bv14_fcstedadrcurrent numeric(19, 5),
    bv14_fcstedadrchange numeric(19, 5),
    bv15_fcstedadrcurrent numeric(19, 5),
    bv15_fcstedadrchange numeric(19, 5),
    bv16_fcstedadrcurrent numeric(19, 5),
    bv16_fcstedadrchange numeric(19, 5),
    bv17_fcstedadrcurrent numeric(19, 5),
    bv17_fcstedadrchange numeric(19, 5),
    bv18_fcstedadrcurrent numeric(19, 5),
    bv18_fcstedadrchange numeric(19, 5),
    bv19_fcstedadrcurrent numeric(19, 5),
    bv19_fcstedadrchange numeric(19, 5),
    bv20_fcstedadrcurrent numeric(19, 5),
    bv20_fcstedadrchange numeric(19, 5),
    bv21_fcstedadrcurrent numeric(19, 5),
    bv21_fcstedadrchange numeric(19, 5),
    bv22_fcstedadrcurrent numeric(19, 5),
    bv22_fcstedadrchange numeric(19, 5),
    bv23_fcstedadrcurrent numeric(19, 5),
    bv23_fcstedadrchange numeric(19, 5),
    bv24_fcstedadrcurrent numeric(19, 5),
    bv24_fcstedadrchange numeric(19, 5),
    bv25_fcstedadrcurrent numeric(19, 5),
    bv25_fcstedadrchange numeric(19, 5),
    bv26_fcstedadrcurrent numeric(19, 5),
    bv26_fcstedadrchange numeric(19, 5),
    bv27_fcstedadrcurrent numeric(19, 5),
    bv27_fcstedadrchange numeric(19, 5),
    bv28_fcstedadrcurrent numeric(19, 5),
    bv28_fcstedadrchange numeric(19, 5),
    bv29_fcstedadrcurrent numeric(19, 5),
    bv29_fcstedadrchange numeric(19, 5),
    bv30_fcstedadrcurrent numeric(19, 5),
    bv30_fcstedadrchange numeric(19, 5),
    bv31_fcstedadrcurrent numeric(19, 5),
    bv31_fcstedadrchange numeric(19, 5),
    bv32_fcstedadrcurrent numeric(19, 5),
    bv32_fcstedadrchange numeric(19, 5),
    bv33_fcstedadrcurrent numeric(19, 5),
    bv33_fcstedadrchange numeric(19, 5),
    bv34_fcstedadrcurrent numeric(19, 5),
    bv34_fcstedadrchange numeric(19, 5),
    bv35_fcstedadrcurrent numeric(19, 5),
    bv35_fcstedadrchange numeric(19, 5),
    bv36_fcstedadrcurrent numeric(19, 5),
    bv36_fcstedadrchange numeric(19, 5),
    bv37_fcstedadrcurrent numeric(19, 5),
    bv37_fcstedadrchange numeric(19, 5),
    bv38_fcstedadrcurrent numeric(19, 5),
    bv38_fcstedadrchange numeric(19, 5),
    bv39_fcstedadrcurrent numeric(19, 5),
    bv39_fcstedadrchange numeric(19, 5),
    bv40_fcstedadrcurrent numeric(19, 5),
    bv40_fcstedadrchange numeric(19, 5),
    bv41_fcstedadrcurrent numeric(19, 5),
    bv41_fcstedadrchange numeric(19, 5),
    bv42_fcstedadrcurrent numeric(19, 5),
    bv42_fcstedadrchange numeric(19, 5),
    bv43_fcstedadrcurrent numeric(19, 5),
    bv43_fcstedadrchange numeric(19, 5),
    bv44_fcstedadrcurrent numeric(19, 5),
    bv44_fcstedadrchange numeric(19, 5),
    bv45_fcstedadrcurrent numeric(19, 5),
    bv45_fcstedadrchange numeric(19, 5),
    bv46_fcstedadrcurrent numeric(19, 5),
    bv46_fcstedadrchange numeric(19, 5),
    bv47_fcstedadrcurrent numeric(19, 5),
    bv47_fcstedadrchange numeric(19, 5),
    bv48_fcstedadrcurrent numeric(19, 5),
    bv48_fcstedadrchange numeric(19, 5),
    bv49_fcstedadrcurrent numeric(19, 5),
    bv49_fcstedadrchange numeric(19, 5),
    bv50_fcstedadrcurrent numeric(19, 5),
    bv50_fcstedadrchange numeric(19, 5),
    bv1_block int,
    bv1_block_available int,
    bv1_block_pickup int,
    bv2_block int,
    bv2_block_available int,
    bv2_block_pickup int,
    bv3_block int,
    bv3_block_available int,
    bv3_block_pickup int,
    bv4_block int,
    bv4_block_available int,
    bv4_block_pickup int,
    bv5_block int,
    bv5_block_available int,
    bv5_block_pickup int,
    bv6_block int,
    bv6_block_available int,
    bv6_block_pickup int,
    bv7_block int,
    bv7_block_available int,
    bv7_block_pickup int,
    bv8_block int,
    bv8_block_available int,
    bv8_block_pickup int,
    bv9_block int,
    bv9_block_available int,
    bv9_block_pickup int,
    bv10_block int,
    bv10_block_available int,
    bv10_block_pickup int,
    bv11_block int,
    bv11_block_available int,
    bv11_block_pickup int,
    bv12_block int,
    bv12_block_available int,
    bv12_block_pickup int,
    bv13_block int,
    bv13_block_available int,
    bv13_block_pickup int,
    bv14_block int,
    bv14_block_available int,
    bv14_block_pickup int,
    bv15_block int,
    bv15_block_available int,
    bv15_block_pickup int,
    bv16_block int,
    bv16_block_available int,
    bv16_block_pickup int,
    bv17_block int,
    bv17_block_available int,
    bv17_block_pickup int,
    bv18_block int,
    bv18_block_available int,
    bv18_block_pickup int,
    bv19_block int,
    bv19_block_available int,
    bv19_block_pickup int,
    bv20_block int,
    bv20_block_available int,
    bv20_block_pickup int,
    bv21_block int,
    bv21_block_available int,
    bv21_block_pickup int,
    bv22_block int,
    bv22_block_available int,
    bv22_block_pickup int,
    bv23_block int,
    bv23_block_available int,
    bv23_block_pickup int,
    bv24_block int,
    bv24_block_available int,
    bv24_block_pickup int,
    bv25_block int,
    bv25_block_available int,
    bv25_block_pickup int,
    bv26_block int,
    bv26_block_available int,
    bv26_block_pickup int,
    bv27_block int,
    bv27_block_available int,
    bv27_block_pickup int,
    bv28_block int,
    bv28_block_available int,
    bv28_block_pickup int,
    bv29_block int,
    bv29_block_available int,
    bv29_block_pickup int,
    bv30_block int,
    bv30_block_available int,
    bv30_block_pickup int,
    bv31_block int,
    bv31_block_available int,
    bv31_block_pickup int,
    bv32_block int,
    bv32_block_available int,
    bv32_block_pickup int,
    bv33_block int,
    bv33_block_available int,
    bv33_block_pickup int,
    bv34_block int,
    bv34_block_available int,
    bv34_block_pickup int,
    bv35_block int,
    bv35_block_available int,
    bv35_block_pickup int,
    bv36_block int,
    bv36_block_available int,
    bv36_block_pickup int,
    bv37_block int,
    bv37_block_available int,
    bv37_block_pickup int,
    bv38_block int,
    bv38_block_available int,
    bv38_block_pickup int,
    bv39_block int,
    bv39_block_available int,
    bv39_block_pickup int,
    bv40_block int,
    bv40_block_available int,
    bv40_block_pickup int,
    bv41_block int,
    bv41_block_available int,
    bv41_block_pickup int,
    bv42_block int,
    bv42_block_available int,
    bv42_block_pickup int,
    bv43_block int,
    bv43_block_available int,
    bv43_block_pickup int,
    bv44_block int,
    bv44_block_available int,
    bv44_block_pickup int,
    bv45_block int,
    bv45_block_available int,
    bv45_block_pickup int,
    bv46_block int,
    bv46_block_available int,
    bv46_block_pickup int,
    bv47_block int,
    bv47_block_available int,
    bv47_block_pickup int,
    bv48_block int,
    bv48_block_available int,
    bv48_block_pickup int,
    bv49_block int,
    bv49_block_available int,
    bv49_block_pickup int,
    bv50_block int,
    bv50_block_available int,
    bv50_block_pickup int
)

    insert into #change_report_bv_comparative_view
select occupancy_dt,
       dow,
       MAX(bv1_businessgroupname) as bv1_businessgroupname,
       MAX(bv2_businessgroupname) as bv2_businessgroupname,
       MAX(bv3_businessgroupname) as bv3_businessgroupname,
       MAX(bv4_businessgroupname) as bv4_businessgroupname,
       MAX(bv5_businessgroupname) as bv5_businessgroupname,
       MAX(bv6_businessgroupname) as bv6_businessgroupname,
       MAX(bv7_businessgroupname) as bv7_businessgroupname,
       MAX(bv8_businessgroupname) as bv8_businessgroupname,
       MAX(bv9_businessgroupname) as bv9_businessgroupname,
       MAX(bv10_businessgroupname) as bv10_businessgroupname,
       MAX(bv11_businessgroupname) as bv11_businessgroupname,
       MAX(bv12_businessgroupname) as bv12_businessgroupname,
       MAX(bv13_businessgroupname) as bv13_businessgroupname,
       MAX(bv14_businessgroupname) as bv14_businessgroupname,
       MAX(bv15_businessgroupname) as bv15_businessgroupname,
       MAX(bv16_businessgroupname) as bv16_businessgroupname,
       MAX(bv17_businessgroupname) as bv17_businessgroupname,
       MAX(bv18_businessgroupname) as bv18_businessgroupname,
       MAX(bv19_businessgroupname) as bv19_businessgroupname,
       MAX(bv20_businessgroupname) as bv20_businessgroupname,
       MAX(bv21_businessgroupname) as bv21_businessgroupname,
       MAX(bv22_businessgroupname) as bv22_businessgroupname,
       MAX(bv23_businessgroupname) as bv23_businessgroupname,
       MAX(bv24_businessgroupname) as bv24_businessgroupname,
       MAX(bv25_businessgroupname) as bv25_businessgroupname,
       MAX(bv26_businessgroupname) as bv26_businessgroupname,
       MAX(bv27_businessgroupname) as bv27_businessgroupname,
       MAX(bv28_businessgroupname) as bv28_businessgroupname,
       MAX(bv29_businessgroupname) as bv29_businessgroupname,
       MAX(bv30_businessgroupname) as bv30_businessgroupname,
       MAX(bv31_businessgroupname) as bv31_businessgroupname,
       MAX(bv32_businessgroupname) as bv32_businessgroupname,
       MAX(bv33_businessgroupname) as bv33_businessgroupname,
       MAX(bv34_businessgroupname) as bv34_businessgroupname,
       MAX(bv35_businessgroupname) as bv35_businessgroupname,
       MAX(bv36_businessgroupname) as bv36_businessgroupname,
       MAX(bv37_businessgroupname) as bv37_businessgroupname,
       MAX(bv38_businessgroupname) as bv38_businessgroupname,
       MAX(bv39_businessgroupname) as bv39_businessgroupname,
       MAX(bv40_businessgroupname) as bv40_businessgroupname,
       MAX(bv41_businessgroupname) as bv41_businessgroupname,
       MAX(bv42_businessgroupname) as bv42_businessgroupname,
       MAX(bv43_businessgroupname) as bv43_businessgroupname,
       MAX(bv44_businessgroupname) as bv44_businessgroupname,
       MAX(bv45_businessgroupname) as bv45_businessgroupname,
       MAX(bv46_businessgroupname) as bv46_businessgroupname,
       MAX(bv47_businessgroupname) as bv47_businessgroupname,
       MAX(bv48_businessgroupname) as bv48_businessgroupname,
       MAX(bv49_businessgroupname) as bv49_businessgroupname,
       MAX(bv50_businessgroupname) as bv50_businessgroupname,
       isnull(MAX(bv1_roomsoldcurrent), 0.0) as bv1_roomsoldcurrent,
       isnull(MAX(bv1_roomssoldchange), 0.0) as bv1_roomssoldchange,
       isnull(MAX(bv2_roomsoldcurrent), 0.0) as bv2_roomsoldcurrent,
       isnull(MAX(bv2_roomssoldchange), 0.0) as bv2_roomssoldchange,
       isnull(MAX(bv3_roomsoldcurrent), 0.0) as bv3_roomsoldcurrent,
       isnull(MAX(bv3_roomssoldchange), 0.0) as bv3_roomssoldchange,
       isnull(MAX(bv4_roomsoldcurrent), 0.0) as bv4_roomsoldcurrent,
       isnull(MAX(bv4_roomssoldchange), 0.0) as bv4_roomssoldchange,
       isnull(MAX(bv5_roomsoldcurrent), 0.0) as bv5_roomsoldcurrent,
       isnull(MAX(bv5_roomssoldchange), 0.0) as bv5_roomssoldchange,
       isnull(MAX(bv6_roomsoldcurrent), 0.0) as bv6_roomsoldcurrent,
       isnull(MAX(bv6_roomssoldchange), 0.0) as bv6_roomssoldchange,
       isnull(MAX(bv7_roomsoldcurrent), 0.0) as bv7_roomsoldcurrent,
       isnull(MAX(bv7_roomssoldchange), 0.0) as bv7_roomssoldchange,
       isnull(MAX(bv8_roomsoldcurrent), 0.0) as bv8_roomsoldcurrent,
       isnull(MAX(bv8_roomssoldchange), 0.0) as bv8_roomssoldchange,
       isnull(MAX(bv9_roomsoldcurrent), 0.0) as bv9_roomsoldcurrent,
       isnull(MAX(bv9_roomssoldchange), 0.0) as bv9_roomssoldchange,
       isnull(MAX(bv10_roomsoldcurrent), 0.0) as bv10_roomsoldcurrent,
       isnull(MAX(bv10_roomssoldchange), 0.0) as bv10_roomssoldchange,
       isnull(MAX(bv11_roomsoldcurrent), 0.0) as bv11_roomsoldcurrent,
       isnull(MAX(bv11_roomssoldchange), 0.0) as bv11_roomssoldchange,
       isnull(MAX(bv12_roomsoldcurrent), 0.0) as bv12_roomsoldcurrent,
       isnull(MAX(bv12_roomssoldchange), 0.0) as bv12_roomssoldchange,
       isnull(MAX(bv13_roomsoldcurrent), 0.0) as bv13_roomsoldcurrent,
       isnull(MAX(bv13_roomssoldchange), 0.0) as bv13_roomssoldchange,
       isnull(MAX(bv14_roomsoldcurrent), 0.0) as bv14_roomsoldcurrent,
       isnull(MAX(bv14_roomssoldchange), 0.0) as bv14_roomssoldchange,
       isnull(MAX(bv15_roomsoldcurrent), 0.0) as bv15_roomsoldcurrent,
       isnull(MAX(bv15_roomssoldchange), 0.0) as bv15_roomssoldchange,
       isnull(MAX(bv16_roomsoldcurrent), 0.0) as bv16_roomsoldcurrent,
       isnull(MAX(bv16_roomssoldchange), 0.0) as bv16_roomssoldchange,
       isnull(MAX(bv17_roomsoldcurrent), 0.0) as bv17_roomsoldcurrent,
       isnull(MAX(bv17_roomssoldchange), 0.0) as bv17_roomssoldchange,
       isnull(MAX(bv18_roomsoldcurrent), 0.0) as bv18_roomsoldcurrent,
       isnull(MAX(bv18_roomssoldchange), 0.0) as bv18_roomssoldchange,
       isnull(MAX(bv19_roomsoldcurrent), 0.0) as bv19_roomsoldcurrent,
       isnull(MAX(bv19_roomssoldchange), 0.0) as bv19_roomssoldchange,
       isnull(MAX(bv20_roomsoldcurrent), 0.0) as bv20_roomsoldcurrent,
       isnull(MAX(bv20_roomssoldchange), 0.0) as bv20_roomssoldchange,
       isnull(MAX(bv21_roomsoldcurrent), 0.0) as bv21_roomsoldcurrent,
       isnull(MAX(bv21_roomssoldchange), 0.0) as bv21_roomssoldchange,
       isnull(MAX(bv22_roomsoldcurrent), 0.0) as bv22_roomsoldcurrent,
       isnull(MAX(bv22_roomssoldchange), 0.0) as bv22_roomssoldchange,
       isnull(MAX(bv23_roomsoldcurrent), 0.0) as bv23_roomsoldcurrent,
       isnull(MAX(bv23_roomssoldchange), 0.0) as bv23_roomssoldchange,
       isnull(MAX(bv24_roomsoldcurrent), 0.0) as bv24_roomsoldcurrent,
       isnull(MAX(bv24_roomssoldchange), 0.0) as bv24_roomssoldchange,
       isnull(MAX(bv25_roomsoldcurrent), 0.0) as bv25_roomsoldcurrent,
       isnull(MAX(bv25_roomssoldchange), 0.0) as bv25_roomssoldchange,
       isnull(MAX(bv26_roomsoldcurrent), 0.0) as bv26_roomsoldcurrent,
       isnull(MAX(bv26_roomssoldchange), 0.0) as bv26_roomssoldchange,
       isnull(MAX(bv27_roomsoldcurrent), 0.0) as bv27_roomsoldcurrent,
       isnull(MAX(bv27_roomssoldchange), 0.0) as bv27_roomssoldchange,
       isnull(MAX(bv28_roomsoldcurrent), 0.0) as bv28_roomsoldcurrent,
       isnull(MAX(bv28_roomssoldchange), 0.0) as bv28_roomssoldchange,
       isnull(MAX(bv29_roomsoldcurrent), 0.0) as bv29_roomsoldcurrent,
       isnull(MAX(bv29_roomssoldchange), 0.0) as bv29_roomssoldchange,
       isnull(MAX(bv30_roomsoldcurrent), 0.0) as bv30_roomsoldcurrent,
       isnull(MAX(bv30_roomssoldchange), 0.0) as bv30_roomssoldchange,
       isnull(MAX(bv31_roomsoldcurrent), 0.0) as bv31_roomsoldcurrent,
       isnull(MAX(bv31_roomssoldchange), 0.0) as bv31_roomssoldchange,
       isnull(MAX(bv32_roomsoldcurrent), 0.0) as bv32_roomsoldcurrent,
       isnull(MAX(bv32_roomssoldchange), 0.0) as bv32_roomssoldchange,
       isnull(MAX(bv33_roomsoldcurrent), 0.0) as bv33_roomsoldcurrent,
       isnull(MAX(bv33_roomssoldchange), 0.0) as bv33_roomssoldchange,
       isnull(MAX(bv34_roomsoldcurrent), 0.0) as bv34_roomsoldcurrent,
       isnull(MAX(bv34_roomssoldchange), 0.0) as bv34_roomssoldchange,
       isnull(MAX(bv35_roomsoldcurrent), 0.0) as bv35_roomsoldcurrent,
       isnull(MAX(bv35_roomssoldchange), 0.0) as bv35_roomssoldchange,
       isnull(MAX(bv36_roomsoldcurrent), 0.0) as bv36_roomsoldcurrent,
       isnull(MAX(bv36_roomssoldchange), 0.0) as bv36_roomssoldchange,
       isnull(MAX(bv37_roomsoldcurrent), 0.0) as bv37_roomsoldcurrent,
       isnull(MAX(bv37_roomssoldchange), 0.0) as bv37_roomssoldchange,
       isnull(MAX(bv38_roomsoldcurrent), 0.0) as bv38_roomsoldcurrent,
       isnull(MAX(bv38_roomssoldchange), 0.0) as bv38_roomssoldchange,
       isnull(MAX(bv39_roomsoldcurrent), 0.0) as bv39_roomsoldcurrent,
       isnull(MAX(bv39_roomssoldchange), 0.0) as bv39_roomssoldchange,
       isnull(MAX(bv40_roomsoldcurrent), 0.0) as bv40_roomsoldcurrent,
       isnull(MAX(bv40_roomssoldchange), 0.0) as bv40_roomssoldchange,
       isnull(MAX(bv41_roomsoldcurrent), 0.0) as bv41_roomsoldcurrent,
       isnull(MAX(bv41_roomssoldchange), 0.0) as bv41_roomssoldchange,
       isnull(MAX(bv42_roomsoldcurrent), 0.0) as bv42_roomsoldcurrent,
       isnull(MAX(bv42_roomssoldchange), 0.0) as bv42_roomssoldchange,
       isnull(MAX(bv43_roomsoldcurrent), 0.0) as bv43_roomsoldcurrent,
       isnull(MAX(bv43_roomssoldchange), 0.0) as bv43_roomssoldchange,
       isnull(MAX(bv44_roomsoldcurrent), 0.0) as bv44_roomsoldcurrent,
       isnull(MAX(bv44_roomssoldchange), 0.0) as bv44_roomssoldchange,
       isnull(MAX(bv45_roomsoldcurrent), 0.0) as bv45_roomsoldcurrent,
       isnull(MAX(bv45_roomssoldchange), 0.0) as bv45_roomssoldchange,
       isnull(MAX(bv46_roomsoldcurrent), 0.0) as bv46_roomsoldcurrent,
       isnull(MAX(bv46_roomssoldchange), 0.0) as bv46_roomssoldchange,
       isnull(MAX(bv47_roomsoldcurrent), 0.0) as bv47_roomsoldcurrent,
       isnull(MAX(bv47_roomssoldchange), 0.0) as bv47_roomssoldchange,
       isnull(MAX(bv48_roomsoldcurrent), 0.0) as bv48_roomsoldcurrent,
       isnull(MAX(bv48_roomssoldchange), 0.0) as bv48_roomssoldchange,
       isnull(MAX(bv49_roomsoldcurrent), 0.0) as bv49_roomsoldcurrent,
       isnull(MAX(bv49_roomssoldchange), 0.0) as bv49_roomssoldchange,
       isnull(MAX(bv50_roomsoldcurrent), 0.0) as bv50_roomsoldcurrent,
       isnull(MAX(bv50_roomssoldchange), 0.0) as bv50_roomssoldchange,
       isnull(MAX(bv1_occfcstcurrent), 0.0) as bv1_occfcstcurrent,
       isnull(MAX(bv1_occfcstchange), 0.0) as bv1_occfcstchange,
       isnull(MAX(bv2_occfcstcurrent), 0.0) as bv2_occfcstcurrent,
       isnull(MAX(bv2_occfcstchange), 0.0) as bv2_occfcstchange,
       isnull(MAX(bv3_occfcstcurrent), 0.0) as bv3_occfcstcurrent,
       isnull(MAX(bv3_occfcstchange), 0.0) as bv3_occfcstchange,
       isnull(MAX(bv4_occfcstcurrent), 0.0) as bv4_occfcstcurrent,
       isnull(MAX(bv4_occfcstchange), 0.0) as bv4_occfcstchange,
       isnull(MAX(bv5_occfcstcurrent), 0.0) as bv5_occfcstcurrent,
       isnull(MAX(bv5_occfcstchange), 0.0) as bv5_occfcstchange,
       isnull(MAX(bv6_occfcstcurrent), 0.0) as bv6_occfcstcurrent,
       isnull(MAX(bv6_occfcstchange), 0.0) as bv6_occfcstchange,
       isnull(MAX(bv7_occfcstcurrent), 0.0) as bv7_occfcstcurrent,
       isnull(MAX(bv7_occfcstchange), 0.0) as bv7_occfcstchange,
       isnull(MAX(bv8_occfcstcurrent), 0.0) as bv8_occfcstcurrent,
       isnull(MAX(bv8_occfcstchange), 0.0) as bv8_occfcstchange,
       isnull(MAX(bv9_occfcstcurrent), 0.0) as bv9_occfcstcurrent,
       isnull(MAX(bv9_occfcstchange), 0.0) as bv9_occfcstchange,
       isnull(MAX(bv10_occfcstcurrent), 0.0) as bv10_occfcstcurrent,
       isnull(MAX(bv10_occfcstchange), 0.0) as bv10_occfcstchange,
       isnull(MAX(bv11_occfcstcurrent), 0.0) as bv11_occfcstcurrent,
       isnull(MAX(bv11_occfcstchange), 0.0) as bv11_occfcstchange,
       isnull(MAX(bv12_occfcstcurrent), 0.0) as bv12_occfcstcurrent,
       isnull(MAX(bv12_occfcstchange), 0.0) as bv12_occfcstchange,
       isnull(MAX(bv13_occfcstcurrent), 0.0) as bv13_occfcstcurrent,
       isnull(MAX(bv13_occfcstchange), 0.0) as bv13_occfcstchange,
       isnull(MAX(bv14_occfcstcurrent), 0.0) as bv14_occfcstcurrent,
       isnull(MAX(bv14_occfcstchange), 0.0) as bv14_occfcstchange,
       isnull(MAX(bv15_occfcstcurrent), 0.0) as bv15_occfcstcurrent,
       isnull(MAX(bv15_occfcstchange), 0.0) as bv15_occfcstchange,
       isnull(MAX(bv16_occfcstcurrent), 0.0) as bv16_occfcstcurrent,
       isnull(MAX(bv16_occfcstchange), 0.0) as bv16_occfcstchange,
       isnull(MAX(bv17_occfcstcurrent), 0.0) as bv17_occfcstcurrent,
       isnull(MAX(bv17_occfcstchange), 0.0) as bv17_occfcstchange,
       isnull(MAX(bv18_occfcstcurrent), 0.0) as bv18_occfcstcurrent,
       isnull(MAX(bv18_occfcstchange), 0.0) as bv18_occfcstchange,
       isnull(MAX(bv19_occfcstcurrent), 0.0) as bv19_occfcstcurrent,
       isnull(MAX(bv19_occfcstchange), 0.0) as bv19_occfcstchange,
       isnull(MAX(bv20_occfcstcurrent), 0.0) as bv20_occfcstcurrent,
       isnull(MAX(bv20_occfcstchange), 0.0) as bv20_occfcstchange,
       isnull(MAX(bv21_occfcstcurrent), 0.0) as bv21_occfcstcurrent,
       isnull(MAX(bv21_occfcstchange), 0.0) as bv21_occfcstchange,
       isnull(MAX(bv22_occfcstcurrent), 0.0) as bv22_occfcstcurrent,
       isnull(MAX(bv22_occfcstchange), 0.0) as bv22_occfcstchange,
       isnull(MAX(bv23_occfcstcurrent), 0.0) as bv23_occfcstcurrent,
       isnull(MAX(bv23_occfcstchange), 0.0) as bv23_occfcstchange,
       isnull(MAX(bv24_occfcstcurrent), 0.0) as bv24_occfcstcurrent,
       isnull(MAX(bv24_occfcstchange), 0.0) as bv24_occfcstchange,
       isnull(MAX(bv25_occfcstcurrent), 0.0) as bv25_occfcstcurrent,
       isnull(MAX(bv25_occfcstchange), 0.0) as bv25_occfcstchange,
       isnull(MAX(bv26_occfcstcurrent), 0.0) as bv26_occfcstcurrent,
       isnull(MAX(bv26_occfcstchange), 0.0) as bv26_occfcstchange,
       isnull(MAX(bv27_occfcstcurrent), 0.0) as bv27_occfcstcurrent,
       isnull(MAX(bv27_occfcstchange), 0.0) as bv27_occfcstchange,
       isnull(MAX(bv28_occfcstcurrent), 0.0) as bv28_occfcstcurrent,
       isnull(MAX(bv28_occfcstchange), 0.0) as bv28_occfcstchange,
       isnull(MAX(bv29_occfcstcurrent), 0.0) as bv29_occfcstcurrent,
       isnull(MAX(bv29_occfcstchange), 0.0) as bv29_occfcstchange,
       isnull(MAX(bv30_occfcstcurrent), 0.0) as bv30_occfcstcurrent,
       isnull(MAX(bv30_occfcstchange), 0.0) as bv30_occfcstchange,
       isnull(MAX(bv31_occfcstcurrent), 0.0) as bv31_occfcstcurrent,
       isnull(MAX(bv31_occfcstchange), 0.0) as bv31_occfcstchange,
       isnull(MAX(bv32_occfcstcurrent), 0.0) as bv32_occfcstcurrent,
       isnull(MAX(bv32_occfcstchange), 0.0) as bv32_occfcstchange,
       isnull(MAX(bv33_occfcstcurrent), 0.0) as bv33_occfcstcurrent,
       isnull(MAX(bv33_occfcstchange), 0.0) as bv33_occfcstchange,
       isnull(MAX(bv34_occfcstcurrent), 0.0) as bv34_occfcstcurrent,
       isnull(MAX(bv34_occfcstchange), 0.0) as bv34_occfcstchange,
       isnull(MAX(bv35_occfcstcurrent), 0.0) as bv35_occfcstcurrent,
       isnull(MAX(bv35_occfcstchange), 0.0) as bv35_occfcstchange,
       isnull(MAX(bv36_occfcstcurrent), 0.0) as bv36_occfcstcurrent,
       isnull(MAX(bv36_occfcstchange), 0.0) as bv36_occfcstchange,
       isnull(MAX(bv37_occfcstcurrent), 0.0) as bv37_occfcstcurrent,
       isnull(MAX(bv37_occfcstchange), 0.0) as bv37_occfcstchange,
       isnull(MAX(bv38_occfcstcurrent), 0.0) as bv38_occfcstcurrent,
       isnull(MAX(bv38_occfcstchange), 0.0) as bv38_occfcstchange,
       isnull(MAX(bv39_occfcstcurrent), 0.0) as bv39_occfcstcurrent,
       isnull(MAX(bv39_occfcstchange), 0.0) as bv39_occfcstchange,
       isnull(MAX(bv40_occfcstcurrent), 0.0) as bv40_occfcstcurrent,
       isnull(MAX(bv40_occfcstchange), 0.0) as bv40_occfcstchange,
       isnull(MAX(bv41_occfcstcurrent), 0.0) as bv41_occfcstcurrent,
       isnull(MAX(bv41_occfcstchange), 0.0) as bv41_occfcstchange,
       isnull(MAX(bv42_occfcstcurrent), 0.0) as bv42_occfcstcurrent,
       isnull(MAX(bv42_occfcstchange), 0.0) as bv42_occfcstchange,
       isnull(MAX(bv43_occfcstcurrent), 0.0) as bv43_occfcstcurrent,
       isnull(MAX(bv43_occfcstchange), 0.0) as bv43_occfcstchange,
       isnull(MAX(bv44_occfcstcurrent), 0.0) as bv44_occfcstcurrent,
       isnull(MAX(bv44_occfcstchange), 0.0) as bv44_occfcstchange,
       isnull(MAX(bv45_occfcstcurrent), 0.0) as bv45_occfcstcurrent,
       isnull(MAX(bv45_occfcstchange), 0.0) as bv45_occfcstchange,
       isnull(MAX(bv46_occfcstcurrent), 0.0) as bv46_occfcstcurrent,
       isnull(MAX(bv46_occfcstchange), 0.0) as bv46_occfcstchange,
       isnull(MAX(bv47_occfcstcurrent), 0.0) as bv47_occfcstcurrent,
       isnull(MAX(bv47_occfcstchange), 0.0) as bv47_occfcstchange,
       isnull(MAX(bv48_occfcstcurrent), 0.0) as bv48_occfcstcurrent,
       isnull(MAX(bv48_occfcstchange), 0.0) as bv48_occfcstchange,
       isnull(MAX(bv49_occfcstcurrent), 0.0) as bv49_occfcstcurrent,
       isnull(MAX(bv49_occfcstchange), 0.0) as bv49_occfcstchange,
       isnull(MAX(bv50_occfcstcurrent), 0.0) as bv50_occfcstcurrent,
       isnull(MAX(bv50_occfcstchange), 0.0) as bv50_occfcstchange,
       isnull(MAX(bv1_bookedroomrevenuecurrent), 0.0) as bv1_bookedroomrevenuecurrent,
       isnull(MAX(bv1_bookedroomrevenuechange), 0.0) as bv1_bookedroomrevenuechange,
       isnull(MAX(bv2_bookedroomrevenuecurrent), 0.0) as bv2_bookedroomrevenuecurrent,
       isnull(MAX(bv2_bookedroomrevenuechange), 0.0) as bv2_bookedroomrevenuechange,
       isnull(MAX(bv3_bookedroomrevenuecurrent), 0.0) as bv3_bookedroomrevenuecurrent,
       isnull(MAX(bv3_bookedroomrevenuechange), 0.0) as bv3_bookedroomrevenuechange,
       isnull(MAX(bv4_bookedroomrevenuecurrent), 0.0) as bv4_bookedroomrevenuecurrent,
       isnull(MAX(bv4_bookedroomrevenuechange), 0.0) as bv4_bookedroomrevenuechange,
       isnull(MAX(bv5_bookedroomrevenuecurrent), 0.0) as bv5_bookedroomrevenuecurrent,
       isnull(MAX(bv5_bookedroomrevenuechange), 0.0) as bv5_bookedroomrevenuechange,
       isnull(MAX(bv6_bookedroomrevenuecurrent), 0.0) as bv6_bookedroomrevenuecurrent,
       isnull(MAX(bv6_bookedroomrevenuechange), 0.0) as bv6_bookedroomrevenuechange,
       isnull(MAX(bv7_bookedroomrevenuecurrent), 0.0) as bv7_bookedroomrevenuecurrent,
       isnull(MAX(bv7_bookedroomrevenuechange), 0.0) as bv7_bookedroomrevenuechange,
       isnull(MAX(bv8_bookedroomrevenuecurrent), 0.0) as bv8_bookedroomrevenuecurrent,
       isnull(MAX(bv8_bookedroomrevenuechange), 0.0) as bv8_bookedroomrevenuechange,
       isnull(MAX(bv9_bookedroomrevenuecurrent), 0.0) as bv9_bookedroomrevenuecurrent,
       isnull(MAX(bv9_bookedroomrevenuechange), 0.0) as bv9_bookedroomrevenuechange,
       isnull(MAX(bv10_bookedroomrevenuecurrent), 0.0) as bv10_bookedroomrevenuecurrent,
       isnull(MAX(bv10_bookedroomrevenuechange), 0.0) as bv10_bookedroomrevenuechange,
       isnull(MAX(bv11_bookedroomrevenuecurrent), 0.0) as bv11_bookedroomrevenuecurrent,
       isnull(MAX(bv11_bookedroomrevenuechange), 0.0) as bv11_bookedroomrevenuechange,
       isnull(MAX(bv12_bookedroomrevenuecurrent), 0.0) as bv12_bookedroomrevenuecurrent,
       isnull(MAX(bv12_bookedroomrevenuechange), 0.0) as bv12_bookedroomrevenuechange,
       isnull(MAX(bv13_bookedroomrevenuecurrent), 0.0) as bv13_bookedroomrevenuecurrent,
       isnull(MAX(bv13_bookedroomrevenuechange), 0.0) as bv13_bookedroomrevenuechange,
       isnull(MAX(bv14_bookedroomrevenuecurrent), 0.0) as bv14_bookedroomrevenuecurrent,
       isnull(MAX(bv14_bookedroomrevenuechange), 0.0) as bv14_bookedroomrevenuechange,
       isnull(MAX(bv15_bookedroomrevenuecurrent), 0.0) as bv15_bookedroomrevenuecurrent,
       isnull(MAX(bv15_bookedroomrevenuechange), 0.0) as bv15_bookedroomrevenuechange,
       isnull(MAX(bv16_bookedroomrevenuecurrent), 0.0) as bv16_bookedroomrevenuecurrent,
       isnull(MAX(bv16_bookedroomrevenuechange), 0.0) as bv16_bookedroomrevenuechange,
       isnull(MAX(bv17_bookedroomrevenuecurrent), 0.0) as bv17_bookedroomrevenuecurrent,
       isnull(MAX(bv17_bookedroomrevenuechange), 0.0) as bv17_bookedroomrevenuechange,
       isnull(MAX(bv18_bookedroomrevenuecurrent), 0.0) as bv18_bookedroomrevenuecurrent,
       isnull(MAX(bv18_bookedroomrevenuechange), 0.0) as bv18_bookedroomrevenuechange,
       isnull(MAX(bv19_bookedroomrevenuecurrent), 0.0) as bv19_bookedroomrevenuecurrent,
       isnull(MAX(bv19_bookedroomrevenuechange), 0.0) as bv19_bookedroomrevenuechange,
       isnull(MAX(bv20_bookedroomrevenuecurrent), 0.0) as bv20_bookedroomrevenuecurrent,
       isnull(MAX(bv20_bookedroomrevenuechange), 0.0) as bv20_bookedroomrevenuechange,
       isnull(MAX(bv21_bookedroomrevenuecurrent), 0.0) as bv21_bookedroomrevenuecurrent,
       isnull(MAX(bv21_bookedroomrevenuechange), 0.0) as bv21_bookedroomrevenuechange,
       isnull(MAX(bv22_bookedroomrevenuecurrent), 0.0) as bv22_bookedroomrevenuecurrent,
       isnull(MAX(bv22_bookedroomrevenuechange), 0.0) as bv22_bookedroomrevenuechange,
       isnull(MAX(bv23_bookedroomrevenuecurrent), 0.0) as bv23_bookedroomrevenuecurrent,
       isnull(MAX(bv23_bookedroomrevenuechange), 0.0) as bv23_bookedroomrevenuechange,
       isnull(MAX(bv24_bookedroomrevenuecurrent), 0.0) as bv24_bookedroomrevenuecurrent,
       isnull(MAX(bv24_bookedroomrevenuechange), 0.0) as bv24_bookedroomrevenuechange,
       isnull(MAX(bv25_bookedroomrevenuecurrent), 0.0) as bv25_bookedroomrevenuecurrent,
       isnull(MAX(bv25_bookedroomrevenuechange), 0.0) as bv25_bookedroomrevenuechange,
       isnull(MAX(bv26_bookedroomrevenuecurrent), 0.0) as bv26_bookedroomrevenuecurrent,
       isnull(MAX(bv26_bookedroomrevenuechange), 0.0) as bv26_bookedroomrevenuechange,
       isnull(MAX(bv27_bookedroomrevenuecurrent), 0.0) as bv27_bookedroomrevenuecurrent,
       isnull(MAX(bv27_bookedroomrevenuechange), 0.0) as bv27_bookedroomrevenuechange,
       isnull(MAX(bv28_bookedroomrevenuecurrent), 0.0) as bv28_bookedroomrevenuecurrent,
       isnull(MAX(bv28_bookedroomrevenuechange), 0.0) as bv28_bookedroomrevenuechange,
       isnull(MAX(bv29_bookedroomrevenuecurrent), 0.0) as bv29_bookedroomrevenuecurrent,
       isnull(MAX(bv29_bookedroomrevenuechange), 0.0) as bv29_bookedroomrevenuechange,
       isnull(MAX(bv30_bookedroomrevenuecurrent), 0.0) as bv30_bookedroomrevenuecurrent,
       isnull(MAX(bv30_bookedroomrevenuechange), 0.0) as bv30_bookedroomrevenuechange,
       isnull(MAX(bv31_bookedroomrevenuecurrent), 0.0) as bv31_bookedroomrevenuecurrent,
       isnull(MAX(bv31_bookedroomrevenuechange), 0.0) as bv31_bookedroomrevenuechange,
       isnull(MAX(bv32_bookedroomrevenuecurrent), 0.0) as bv32_bookedroomrevenuecurrent,
       isnull(MAX(bv32_bookedroomrevenuechange), 0.0) as bv32_bookedroomrevenuechange,
       isnull(MAX(bv33_bookedroomrevenuecurrent), 0.0) as bv33_bookedroomrevenuecurrent,
       isnull(MAX(bv33_bookedroomrevenuechange), 0.0) as bv33_bookedroomrevenuechange,
       isnull(MAX(bv34_bookedroomrevenuecurrent), 0.0) as bv34_bookedroomrevenuecurrent,
       isnull(MAX(bv34_bookedroomrevenuechange), 0.0) as bv34_bookedroomrevenuechange,
       isnull(MAX(bv35_bookedroomrevenuecurrent), 0.0) as bv35_bookedroomrevenuecurrent,
       isnull(MAX(bv35_bookedroomrevenuechange), 0.0) as bv35_bookedroomrevenuechange,
       isnull(MAX(bv36_bookedroomrevenuecurrent), 0.0) as bv36_bookedroomrevenuecurrent,
       isnull(MAX(bv36_bookedroomrevenuechange), 0.0) as bv36_bookedroomrevenuechange,
       isnull(MAX(bv37_bookedroomrevenuecurrent), 0.0) as bv37_bookedroomrevenuecurrent,
       isnull(MAX(bv37_bookedroomrevenuechange), 0.0) as bv37_bookedroomrevenuechange,
       isnull(MAX(bv38_bookedroomrevenuecurrent), 0.0) as bv38_bookedroomrevenuecurrent,
       isnull(MAX(bv38_bookedroomrevenuechange), 0.0) as bv38_bookedroomrevenuechange,
       isnull(MAX(bv39_bookedroomrevenuecurrent), 0.0) as bv39_bookedroomrevenuecurrent,
       isnull(MAX(bv39_bookedroomrevenuechange), 0.0) as bv39_bookedroomrevenuechange,
       isnull(MAX(bv40_bookedroomrevenuecurrent), 0.0) as bv40_bookedroomrevenuecurrent,
       isnull(MAX(bv40_bookedroomrevenuechange), 0.0) as bv40_bookedroomrevenuechange,
       isnull(MAX(bv41_bookedroomrevenuecurrent), 0.0) as bv41_bookedroomrevenuecurrent,
       isnull(MAX(bv41_bookedroomrevenuechange), 0.0) as bv41_bookedroomrevenuechange,
       isnull(MAX(bv42_bookedroomrevenuecurrent), 0.0) as bv42_bookedroomrevenuecurrent,
       isnull(MAX(bv42_bookedroomrevenuechange), 0.0) as bv42_bookedroomrevenuechange,
       isnull(MAX(bv43_bookedroomrevenuecurrent), 0.0) as bv43_bookedroomrevenuecurrent,
       isnull(MAX(bv43_bookedroomrevenuechange), 0.0) as bv43_bookedroomrevenuechange,
       isnull(MAX(bv44_bookedroomrevenuecurrent), 0.0) as bv44_bookedroomrevenuecurrent,
       isnull(MAX(bv44_bookedroomrevenuechange), 0.0) as bv44_bookedroomrevenuechange,
       isnull(MAX(bv45_bookedroomrevenuecurrent), 0.0) as bv45_bookedroomrevenuecurrent,
       isnull(MAX(bv45_bookedroomrevenuechange), 0.0) as bv45_bookedroomrevenuechange,
       isnull(MAX(bv46_bookedroomrevenuecurrent), 0.0) as bv46_bookedroomrevenuecurrent,
       isnull(MAX(bv46_bookedroomrevenuechange), 0.0) as bv46_bookedroomrevenuechange,
       isnull(MAX(bv47_bookedroomrevenuecurrent), 0.0) as bv47_bookedroomrevenuecurrent,
       isnull(MAX(bv47_bookedroomrevenuechange), 0.0) as bv47_bookedroomrevenuechange,
       isnull(MAX(bv48_bookedroomrevenuecurrent), 0.0) as bv48_bookedroomrevenuecurrent,
       isnull(MAX(bv48_bookedroomrevenuechange), 0.0) as bv48_bookedroomrevenuechange,
       isnull(MAX(bv49_bookedroomrevenuecurrent), 0.0) as bv49_bookedroomrevenuecurrent,
       isnull(MAX(bv49_bookedroomrevenuechange), 0.0) as bv49_bookedroomrevenuechange,
       isnull(MAX(bv50_bookedroomrevenuecurrent), 0.0) as bv50_bookedroomrevenuecurrent,
       isnull(MAX(bv50_bookedroomrevenuechange), 0.0) as bv50_bookedroomrevenuechange,
       isnull(MAX(bv1_fcstedroomrevenuecurrent), 0.0) as bv1_fcstedroomrevenuecurrent,
       isnull(MAX(bv1_fcstedroomrevenuechange), 0.0) as bv1_fcstedroomrevenuechange,
       isnull(MAX(bv2_fcstedroomrevenuecurrent), 0.0) as bv2_fcstedroomrevenuecurrent,
       isnull(MAX(bv2_fcstedroomrevenuechange), 0.0) as bv2_fcstedroomrevenuechange,
       isnull(MAX(bv3_fcstedroomrevenuecurrent), 0.0) as bv3_fcstedroomrevenuecurrent,
       isnull(MAX(bv3_fcstedroomrevenuechange), 0.0) as bv3_fcstedroomrevenuechange,
       isnull(MAX(bv4_fcstedroomrevenuecurrent), 0.0) as bv4_fcstedroomrevenuecurrent,
       isnull(MAX(bv4_fcstedroomrevenuechange), 0.0) as bv4_fcstedroomrevenuechange,
       isnull(MAX(bv5_fcstedroomrevenuecurrent), 0.0) as bv5_fcstedroomrevenuecurrent,
       isnull(MAX(bv5_fcstedroomrevenuechange), 0.0) as bv5_fcstedroomrevenuechange,
       isnull(MAX(bv6_fcstedroomrevenuecurrent), 0.0) as bv6_fcstedroomrevenuecurrent,
       isnull(MAX(bv6_fcstedroomrevenuechange), 0.0) as bv6_fcstedroomrevenuechange,
       isnull(MAX(bv7_fcstedroomrevenuecurrent), 0.0) as bv7_fcstedroomrevenuecurrent,
       isnull(MAX(bv7_fcstedroomrevenuechange), 0.0) as bv7_fcstedroomrevenuechange,
       isnull(MAX(bv8_fcstedroomrevenuecurrent), 0.0) as bv8_fcstedroomrevenuecurrent,
       isnull(MAX(bv8_fcstedroomrevenuechange), 0.0) as bv8_fcstedroomrevenuechange,
       isnull(MAX(bv9_fcstedroomrevenuecurrent), 0.0) as bv9_fcstedroomrevenuecurrent,
       isnull(MAX(bv9_fcstedroomrevenuechange), 0.0) as bv9_fcstedroomrevenuechange,
       isnull(MAX(bv10_fcstedroomrevenuecurrent), 0.0) as bv10_fcstedroomrevenuecurrent,
       isnull(MAX(bv10_fcstedroomrevenuechange), 0.0) as bv10_fcstedroomrevenuechange,
       isnull(MAX(bv11_fcstedroomrevenuecurrent), 0.0) as bv11_fcstedroomrevenuecurrent,
       isnull(MAX(bv11_fcstedroomrevenuechange), 0.0) as bv11_fcstedroomrevenuechange,
       isnull(MAX(bv12_fcstedroomrevenuecurrent), 0.0) as bv12_fcstedroomrevenuecurrent,
       isnull(MAX(bv12_fcstedroomrevenuechange), 0.0) as bv12_fcstedroomrevenuechange,
       isnull(MAX(bv13_fcstedroomrevenuecurrent), 0.0) as bv13_fcstedroomrevenuecurrent,
       isnull(MAX(bv13_fcstedroomrevenuechange), 0.0) as bv13_fcstedroomrevenuechange,
       isnull(MAX(bv14_fcstedroomrevenuecurrent), 0.0) as bv14_fcstedroomrevenuecurrent,
       isnull(MAX(bv14_fcstedroomrevenuechange), 0.0) as bv14_fcstedroomrevenuechange,
       isnull(MAX(bv15_fcstedroomrevenuecurrent), 0.0) as bv15_fcstedroomrevenuecurrent,
       isnull(MAX(bv15_fcstedroomrevenuechange), 0.0) as bv15_fcstedroomrevenuechange,
       isnull(MAX(bv16_fcstedroomrevenuecurrent), 0.0) as bv16_fcstedroomrevenuecurrent,
       isnull(MAX(bv16_fcstedroomrevenuechange), 0.0) as bv16_fcstedroomrevenuechange,
       isnull(MAX(bv17_fcstedroomrevenuecurrent), 0.0) as bv17_fcstedroomrevenuecurrent,
       isnull(MAX(bv17_fcstedroomrevenuechange), 0.0) as bv17_fcstedroomrevenuechange,
       isnull(MAX(bv18_fcstedroomrevenuecurrent), 0.0) as bv18_fcstedroomrevenuecurrent,
       isnull(MAX(bv18_fcstedroomrevenuechange), 0.0) as bv18_fcstedroomrevenuechange,
       isnull(MAX(bv19_fcstedroomrevenuecurrent), 0.0) as bv19_fcstedroomrevenuecurrent,
       isnull(MAX(bv19_fcstedroomrevenuechange), 0.0) as bv19_fcstedroomrevenuechange,
       isnull(MAX(bv20_fcstedroomrevenuecurrent), 0.0) as bv20_fcstedroomrevenuecurrent,
       isnull(MAX(bv20_fcstedroomrevenuechange), 0.0) as bv20_fcstedroomrevenuechange,
       isnull(MAX(bv21_fcstedroomrevenuecurrent), 0.0) as bv21_fcstedroomrevenuecurrent,
       isnull(MAX(bv21_fcstedroomrevenuechange), 0.0) as bv21_fcstedroomrevenuechange,
       isnull(MAX(bv22_fcstedroomrevenuecurrent), 0.0) as bv22_fcstedroomrevenuecurrent,
       isnull(MAX(bv22_fcstedroomrevenuechange), 0.0) as bv22_fcstedroomrevenuechange,
       isnull(MAX(bv23_fcstedroomrevenuecurrent), 0.0) as bv23_fcstedroomrevenuecurrent,
       isnull(MAX(bv23_fcstedroomrevenuechange), 0.0) as bv23_fcstedroomrevenuechange,
       isnull(MAX(bv24_fcstedroomrevenuecurrent), 0.0) as bv24_fcstedroomrevenuecurrent,
       isnull(MAX(bv24_fcstedroomrevenuechange), 0.0) as bv24_fcstedroomrevenuechange,
       isnull(MAX(bv25_fcstedroomrevenuecurrent), 0.0) as bv25_fcstedroomrevenuecurrent,
       isnull(MAX(bv25_fcstedroomrevenuechange), 0.0) as bv25_fcstedroomrevenuechange,
       isnull(MAX(bv26_fcstedroomrevenuecurrent), 0.0) as bv26_fcstedroomrevenuecurrent,
       isnull(MAX(bv26_fcstedroomrevenuechange), 0.0) as bv26_fcstedroomrevenuechange,
       isnull(MAX(bv27_fcstedroomrevenuecurrent), 0.0) as bv27_fcstedroomrevenuecurrent,
       isnull(MAX(bv27_fcstedroomrevenuechange), 0.0) as bv27_fcstedroomrevenuechange,
       isnull(MAX(bv28_fcstedroomrevenuecurrent), 0.0) as bv28_fcstedroomrevenuecurrent,
       isnull(MAX(bv28_fcstedroomrevenuechange), 0.0) as bv28_fcstedroomrevenuechange,
       isnull(MAX(bv29_fcstedroomrevenuecurrent), 0.0) as bv29_fcstedroomrevenuecurrent,
       isnull(MAX(bv29_fcstedroomrevenuechange), 0.0) as bv29_fcstedroomrevenuechange,
       isnull(MAX(bv30_fcstedroomrevenuecurrent), 0.0) as bv30_fcstedroomrevenuecurrent,
       isnull(MAX(bv30_fcstedroomrevenuechange), 0.0) as bv30_fcstedroomrevenuechange,
       isnull(MAX(bv31_fcstedroomrevenuecurrent), 0.0) as bv31_fcstedroomrevenuecurrent,
       isnull(MAX(bv31_fcstedroomrevenuechange), 0.0) as bv31_fcstedroomrevenuechange,
       isnull(MAX(bv32_fcstedroomrevenuecurrent), 0.0) as bv32_fcstedroomrevenuecurrent,
       isnull(MAX(bv32_fcstedroomrevenuechange), 0.0) as bv32_fcstedroomrevenuechange,
       isnull(MAX(bv33_fcstedroomrevenuecurrent), 0.0) as bv33_fcstedroomrevenuecurrent,
       isnull(MAX(bv33_fcstedroomrevenuechange), 0.0) as bv33_fcstedroomrevenuechange,
       isnull(MAX(bv34_fcstedroomrevenuecurrent), 0.0) as bv34_fcstedroomrevenuecurrent,
       isnull(MAX(bv34_fcstedroomrevenuechange), 0.0) as bv34_fcstedroomrevenuechange,
       isnull(MAX(bv35_fcstedroomrevenuecurrent), 0.0) as bv35_fcstedroomrevenuecurrent,
       isnull(MAX(bv35_fcstedroomrevenuechange), 0.0) as bv35_fcstedroomrevenuechange,
       isnull(MAX(bv36_fcstedroomrevenuecurrent), 0.0) as bv36_fcstedroomrevenuecurrent,
       isnull(MAX(bv36_fcstedroomrevenuechange), 0.0) as bv36_fcstedroomrevenuechange,
       isnull(MAX(bv37_fcstedroomrevenuecurrent), 0.0) as bv37_fcstedroomrevenuecurrent,
       isnull(MAX(bv37_fcstedroomrevenuechange), 0.0) as bv37_fcstedroomrevenuechange,
       isnull(MAX(bv38_fcstedroomrevenuecurrent), 0.0) as bv38_fcstedroomrevenuecurrent,
       isnull(MAX(bv38_fcstedroomrevenuechange), 0.0) as bv38_fcstedroomrevenuechange,
       isnull(MAX(bv39_fcstedroomrevenuecurrent), 0.0) as bv39_fcstedroomrevenuecurrent,
       isnull(MAX(bv39_fcstedroomrevenuechange), 0.0) as bv39_fcstedroomrevenuechange,
       isnull(MAX(bv40_fcstedroomrevenuecurrent), 0.0) as bv40_fcstedroomrevenuecurrent,
       isnull(MAX(bv40_fcstedroomrevenuechange), 0.0) as bv40_fcstedroomrevenuechange,
       isnull(MAX(bv41_fcstedroomrevenuecurrent), 0.0) as bv41_fcstedroomrevenuecurrent,
       isnull(MAX(bv41_fcstedroomrevenuechange), 0.0) as bv41_fcstedroomrevenuechange,
       isnull(MAX(bv42_fcstedroomrevenuecurrent), 0.0) as bv42_fcstedroomrevenuecurrent,
       isnull(MAX(bv42_fcstedroomrevenuechange), 0.0) as bv42_fcstedroomrevenuechange,
       isnull(MAX(bv43_fcstedroomrevenuecurrent), 0.0) as bv43_fcstedroomrevenuecurrent,
       isnull(MAX(bv43_fcstedroomrevenuechange), 0.0) as bv43_fcstedroomrevenuechange,
       isnull(MAX(bv44_fcstedroomrevenuecurrent), 0.0) as bv44_fcstedroomrevenuecurrent,
       isnull(MAX(bv44_fcstedroomrevenuechange), 0.0) as bv44_fcstedroomrevenuechange,
       isnull(MAX(bv45_fcstedroomrevenuecurrent), 0.0) as bv45_fcstedroomrevenuecurrent,
       isnull(MAX(bv45_fcstedroomrevenuechange), 0.0) as bv45_fcstedroomrevenuechange,
       isnull(MAX(bv46_fcstedroomrevenuecurrent), 0.0) as bv46_fcstedroomrevenuecurrent,
       isnull(MAX(bv46_fcstedroomrevenuechange), 0.0) as bv46_fcstedroomrevenuechange,
       isnull(MAX(bv47_fcstedroomrevenuecurrent), 0.0) as bv47_fcstedroomrevenuecurrent,
       isnull(MAX(bv47_fcstedroomrevenuechange), 0.0) as bv47_fcstedroomrevenuechange,
       isnull(MAX(bv48_fcstedroomrevenuecurrent), 0.0) as bv48_fcstedroomrevenuecurrent,
       isnull(MAX(bv48_fcstedroomrevenuechange), 0.0) as bv48_fcstedroomrevenuechange,
       isnull(MAX(bv49_fcstedroomrevenuecurrent), 0.0) as bv49_fcstedroomrevenuecurrent,
       isnull(MAX(bv49_fcstedroomrevenuechange), 0.0) as bv49_fcstedroomrevenuechange,
       isnull(MAX(bv50_fcstedroomrevenuecurrent), 0.0) as bv50_fcstedroomrevenuecurrent,
       isnull(MAX(bv50_fcstedroomrevenuechange), 0.0) as bv50_fcstedroomrevenuechange,
       isnull(MAX(bv1_bookedadrcurrent), 0.0) as bv1_bookedadrcurrent,
       isnull(MAX(bv1_bookedadrchange), 0.0) as bv1_bookedadrchange,
       isnull(MAX(bv2_bookedadrcurrent), 0.0) as bv2_bookedadrcurrent,
       isnull(MAX(bv2_bookedadrchange), 0.0) as bv2_bookedadrchange,
       isnull(MAX(bv3_bookedadrcurrent), 0.0) as bv3_bookedadrcurrent,
       isnull(MAX(bv3_bookedadrchange), 0.0) as bv3_bookedadrchange,
       isnull(MAX(bv4_bookedadrcurrent), 0.0) as bv4_bookedadrcurrent,
       isnull(MAX(bv4_bookedadrchange), 0.0) as bv4_bookedadrchange,
       isnull(MAX(bv5_bookedadrcurrent), 0.0) as bv5_bookedadrcurrent,
       isnull(MAX(bv5_bookedadrchange), 0.0) as bv5_bookedadrchange,
       isnull(MAX(bv6_bookedadrcurrent), 0.0) as bv6_bookedadrcurrent,
       isnull(MAX(bv6_bookedadrchange), 0.0) as bv6_bookedadrchange,
       isnull(MAX(bv7_bookedadrcurrent), 0.0) as bv7_bookedadrcurrent,
       isnull(MAX(bv7_bookedadrchange), 0.0) as bv7_bookedadrchange,
       isnull(MAX(bv8_bookedadrcurrent), 0.0) as bv8_bookedadrcurrent,
       isnull(MAX(bv8_bookedadrchange), 0.0) as bv8_bookedadrchange,
       isnull(MAX(bv9_bookedadrcurrent), 0.0) as bv9_bookedadrcurrent,
       isnull(MAX(bv9_bookedadrchange), 0.0) as bv9_bookedadrchange,
       isnull(MAX(bv10_bookedadrcurrent), 0.0) as bv10_bookedadrcurrent,
       isnull(MAX(bv10_bookedadrchange), 0.0) as bv10_bookedadrchange,
       isnull(MAX(bv11_bookedadrcurrent), 0.0) as bv11_bookedadrcurrent,
       isnull(MAX(bv11_bookedadrchange), 0.0) as bv11_bookedadrchange,
       isnull(MAX(bv12_bookedadrcurrent), 0.0) as bv12_bookedadrcurrent,
       isnull(MAX(bv12_bookedadrchange), 0.0) as bv12_bookedadrchange,
       isnull(MAX(bv13_bookedadrcurrent), 0.0) as bv13_bookedadrcurrent,
       isnull(MAX(bv13_bookedadrchange), 0.0) as bv13_bookedadrchange,
       isnull(MAX(bv14_bookedadrcurrent), 0.0) as bv14_bookedadrcurrent,
       isnull(MAX(bv14_bookedadrchange), 0.0) as bv14_bookedadrchange,
       isnull(MAX(bv15_bookedadrcurrent), 0.0) as bv15_bookedadrcurrent,
       isnull(MAX(bv15_bookedadrchange), 0.0) as bv15_bookedadrchange,
       isnull(MAX(bv16_bookedadrcurrent), 0.0) as bv16_bookedadrcurrent,
       isnull(MAX(bv16_bookedadrchange), 0.0) as bv16_bookedadrchange,
       isnull(MAX(bv17_bookedadrcurrent), 0.0) as bv17_bookedadrcurrent,
       isnull(MAX(bv17_bookedadrchange), 0.0) as bv17_bookedadrchange,
       isnull(MAX(bv18_bookedadrcurrent), 0.0) as bv18_bookedadrcurrent,
       isnull(MAX(bv18_bookedadrchange), 0.0) as bv18_bookedadrchange,
       isnull(MAX(bv19_bookedadrcurrent), 0.0) as bv19_bookedadrcurrent,
       isnull(MAX(bv19_bookedadrchange), 0.0) as bv19_bookedadrchange,
       isnull(MAX(bv20_bookedadrcurrent), 0.0) as bv20_bookedadrcurrent,
       isnull(MAX(bv20_bookedadrchange), 0.0) as bv20_bookedadrchange,
       isnull(MAX(bv21_bookedadrcurrent), 0.0) as bv21_bookedadrcurrent,
       isnull(MAX(bv21_bookedadrchange), 0.0) as bv21_bookedadrchange,
       isnull(MAX(bv22_bookedadrcurrent), 0.0) as bv22_bookedadrcurrent,
       isnull(MAX(bv22_bookedadrchange), 0.0) as bv22_bookedadrchange,
       isnull(MAX(bv23_bookedadrcurrent), 0.0) as bv23_bookedadrcurrent,
       isnull(MAX(bv23_bookedadrchange), 0.0) as bv23_bookedadrchange,
       isnull(MAX(bv24_bookedadrcurrent), 0.0) as bv24_bookedadrcurrent,
       isnull(MAX(bv24_bookedadrchange), 0.0) as bv24_bookedadrchange,
       isnull(MAX(bv25_bookedadrcurrent), 0.0) as bv25_bookedadrcurrent,
       isnull(MAX(bv25_bookedadrchange), 0.0) as bv25_bookedadrchange,
       isnull(MAX(bv26_bookedadrcurrent), 0.0) as bv26_bookedadrcurrent,
       isnull(MAX(bv26_bookedadrchange), 0.0) as bv26_bookedadrchange,
       isnull(MAX(bv27_bookedadrcurrent), 0.0) as bv27_bookedadrcurrent,
       isnull(MAX(bv27_bookedadrchange), 0.0) as bv27_bookedadrchange,
       isnull(MAX(bv28_bookedadrcurrent), 0.0) as bv28_bookedadrcurrent,
       isnull(MAX(bv28_bookedadrchange), 0.0) as bv28_bookedadrchange,
       isnull(MAX(bv29_bookedadrcurrent), 0.0) as bv29_bookedadrcurrent,
       isnull(MAX(bv29_bookedadrchange), 0.0) as bv29_bookedadrchange,
       isnull(MAX(bv30_bookedadrcurrent), 0.0) as bv30_bookedadrcurrent,
       isnull(MAX(bv30_bookedadrchange), 0.0) as bv30_bookedadrchange,
       isnull(MAX(bv31_bookedadrcurrent), 0.0) as bv31_bookedadrcurrent,
       isnull(MAX(bv31_bookedadrchange), 0.0) as bv31_bookedadrchange,
       isnull(MAX(bv32_bookedadrcurrent), 0.0) as bv32_bookedadrcurrent,
       isnull(MAX(bv32_bookedadrchange), 0.0) as bv32_bookedadrchange,
       isnull(MAX(bv33_bookedadrcurrent), 0.0) as bv33_bookedadrcurrent,
       isnull(MAX(bv33_bookedadrchange), 0.0) as bv33_bookedadrchange,
       isnull(MAX(bv34_bookedadrcurrent), 0.0) as bv34_bookedadrcurrent,
       isnull(MAX(bv34_bookedadrchange), 0.0) as bv34_bookedadrchange,
       isnull(MAX(bv35_bookedadrcurrent), 0.0) as bv35_bookedadrcurrent,
       isnull(MAX(bv35_bookedadrchange), 0.0) as bv35_bookedadrchange,
       isnull(MAX(bv36_bookedadrcurrent), 0.0) as bv36_bookedadrcurrent,
       isnull(MAX(bv36_bookedadrchange), 0.0) as bv36_bookedadrchange,
       isnull(MAX(bv37_bookedadrcurrent), 0.0) as bv37_bookedadrcurrent,
       isnull(MAX(bv37_bookedadrchange), 0.0) as bv37_bookedadrchange,
       isnull(MAX(bv38_bookedadrcurrent), 0.0) as bv38_bookedadrcurrent,
       isnull(MAX(bv38_bookedadrchange), 0.0) as bv38_bookedadrchange,
       isnull(MAX(bv39_bookedadrcurrent), 0.0) as bv39_bookedadrcurrent,
       isnull(MAX(bv39_bookedadrchange), 0.0) as bv39_bookedadrchange,
       isnull(MAX(bv40_bookedadrcurrent), 0.0) as bv40_bookedadrcurrent,
       isnull(MAX(bv40_bookedadrchange), 0.0) as bv40_bookedadrchange,
       isnull(MAX(bv41_bookedadrcurrent), 0.0) as bv41_bookedadrcurrent,
       isnull(MAX(bv41_bookedadrchange), 0.0) as bv41_bookedadrchange,
       isnull(MAX(bv42_bookedadrcurrent), 0.0) as bv42_bookedadrcurrent,
       isnull(MAX(bv42_bookedadrchange), 0.0) as bv42_bookedadrchange,
       isnull(MAX(bv43_bookedadrcurrent), 0.0) as bv43_bookedadrcurrent,
       isnull(MAX(bv43_bookedadrchange), 0.0) as bv43_bookedadrchange,
       isnull(MAX(bv44_bookedadrcurrent), 0.0) as bv44_bookedadrcurrent,
       isnull(MAX(bv44_bookedadrchange), 0.0) as bv44_bookedadrchange,
       isnull(MAX(bv45_bookedadrcurrent), 0.0) as bv45_bookedadrcurrent,
       isnull(MAX(bv45_bookedadrchange), 0.0) as bv45_bookedadrchange,
       isnull(MAX(bv46_bookedadrcurrent), 0.0) as bv46_bookedadrcurrent,
       isnull(MAX(bv46_bookedadrchange), 0.0) as bv46_bookedadrchange,
       isnull(MAX(bv47_bookedadrcurrent), 0.0) as bv47_bookedadrcurrent,
       isnull(MAX(bv47_bookedadrchange), 0.0) as bv47_bookedadrchange,
       isnull(MAX(bv48_bookedadrcurrent), 0.0) as bv48_bookedadrcurrent,
       isnull(MAX(bv48_bookedadrchange), 0.0) as bv48_bookedadrchange,
       isnull(MAX(bv49_bookedadrcurrent), 0.0) as bv49_bookedadrcurrent,
       isnull(MAX(bv49_bookedadrchange), 0.0) as bv49_bookedadrchange,
       isnull(MAX(bv50_bookedadrcurrent), 0.0) as bv50_bookedadrcurrent,
       isnull(MAX(bv50_bookedadrchange), 0.0) as bv50_bookedadrchange,
       isnull(MAX(bv1_fcstedadrcurrent), 0.0) as bv1_fcstedadrcurrent,
       isnull(MAX(bv1_fcstedadrchange), 0.0) as bv1_fcstedadrchange,
       isnull(MAX(bv2_fcstedadrcurrent), 0.0) as bv2_fcstedadrcurrent,
       isnull(MAX(bv2_fcstedadrchange), 0.0) as bv2_fcstedadrchange,
       isnull(MAX(bv3_fcstedadrcurrent), 0.0) as bv3_fcstedadrcurrent,
       isnull(MAX(bv3_fcstedadrchange), 0.0) as bv3_fcstedadrchange,
       isnull(MAX(bv4_fcstedadrcurrent), 0.0) as bv4_fcstedadrcurrent,
       isnull(MAX(bv4_fcstedadrchange), 0.0) as bv4_fcstedadrchange,
       isnull(MAX(bv5_fcstedadrcurrent), 0.0) as bv5_fcstedadrcurrent,
       isnull(MAX(bv5_fcstedadrchange), 0.0) as bv5_fcstedadrchange,
       isnull(MAX(bv6_fcstedadrcurrent), 0.0) as bv6_fcstedadrcurrent,
       isnull(MAX(bv6_fcstedadrchange), 0.0) as bv6_fcstedadrchange,
       isnull(MAX(bv7_fcstedadrcurrent), 0.0) as bv7_fcstedadrcurrent,
       isnull(MAX(bv7_fcstedadrchange), 0.0) as bv7_fcstedadrchange,
       isnull(MAX(bv8_fcstedadrcurrent), 0.0) as bv8_fcstedadrcurrent,
       isnull(MAX(bv8_fcstedadrchange), 0.0) as bv8_fcstedadrchange,
       isnull(MAX(bv9_fcstedadrcurrent), 0.0) as bv9_fcstedadrcurrent,
       isnull(MAX(bv9_fcstedadrchange), 0.0) as bv9_fcstedadrchange,
       isnull(MAX(bv10_fcstedadrcurrent), 0.0) as bv10_fcstedadrcurrent,
       isnull(MAX(bv10_fcstedadrchange), 0.0) as bv10_fcstedadrchange,
       isnull(MAX(bv11_fcstedadrcurrent), 0.0) as bv11_fcstedadrcurrent,
       isnull(MAX(bv11_fcstedadrchange), 0.0) as bv11_fcstedadrchange,
       isnull(MAX(bv12_fcstedadrcurrent), 0.0) as bv12_fcstedadrcurrent,
       isnull(MAX(bv12_fcstedadrchange), 0.0) as bv12_fcstedadrchange,
       isnull(MAX(bv13_fcstedadrcurrent), 0.0) as bv13_fcstedadrcurrent,
       isnull(MAX(bv13_fcstedadrchange), 0.0) as bv13_fcstedadrchange,
       isnull(MAX(bv14_fcstedadrcurrent), 0.0) as bv14_fcstedadrcurrent,
       isnull(MAX(bv14_fcstedadrchange), 0.0) as bv14_fcstedadrchange,
       isnull(MAX(bv15_fcstedadrcurrent), 0.0) as bv15_fcstedadrcurrent,
       isnull(MAX(bv15_fcstedadrchange), 0.0) as bv15_fcstedadrchange,
       isnull(MAX(bv16_fcstedadrcurrent), 0.0) as bv16_fcstedadrcurrent,
       isnull(MAX(bv16_fcstedadrchange), 0.0) as bv16_fcstedadrchange,
       isnull(MAX(bv17_fcstedadrcurrent), 0.0) as bv17_fcstedadrcurrent,
       isnull(MAX(bv17_fcstedadrchange), 0.0) as bv17_fcstedadrchange,
       isnull(MAX(bv18_fcstedadrcurrent), 0.0) as bv18_fcstedadrcurrent,
       isnull(MAX(bv18_fcstedadrchange), 0.0) as bv18_fcstedadrchange,
       isnull(MAX(bv19_fcstedadrcurrent), 0.0) as bv19_fcstedadrcurrent,
       isnull(MAX(bv19_fcstedadrchange), 0.0) as bv19_fcstedadrchange,
       isnull(MAX(bv20_fcstedadrcurrent), 0.0) as bv20_fcstedadrcurrent,
       isnull(MAX(bv20_fcstedadrchange), 0.0) as bv20_fcstedadrchange,
       isnull(MAX(bv21_fcstedadrcurrent), 0.0) as bv21_fcstedadrcurrent,
       isnull(MAX(bv21_fcstedadrchange), 0.0) as bv21_fcstedadrchange,
       isnull(MAX(bv22_fcstedadrcurrent), 0.0) as bv22_fcstedadrcurrent,
       isnull(MAX(bv22_fcstedadrchange), 0.0) as bv22_fcstedadrchange,
       isnull(MAX(bv23_fcstedadrcurrent), 0.0) as bv23_fcstedadrcurrent,
       isnull(MAX(bv23_fcstedadrchange), 0.0) as bv23_fcstedadrchange,
       isnull(MAX(bv24_fcstedadrcurrent), 0.0) as bv24_fcstedadrcurrent,
       isnull(MAX(bv24_fcstedadrchange), 0.0) as bv24_fcstedadrchange,
       isnull(MAX(bv25_fcstedadrcurrent), 0.0) as bv25_fcstedadrcurrent,
       isnull(MAX(bv25_fcstedadrchange), 0.0) as bv25_fcstedadrchange,
       isnull(MAX(bv26_fcstedadrcurrent), 0.0) as bv26_fcstedadrcurrent,
       isnull(MAX(bv26_fcstedadrchange), 0.0) as bv26_fcstedadrchange,
       isnull(MAX(bv27_fcstedadrcurrent), 0.0) as bv27_fcstedadrcurrent,
       isnull(MAX(bv27_fcstedadrchange), 0.0) as bv27_fcstedadrchange,
       isnull(MAX(bv28_fcstedadrcurrent), 0.0) as bv28_fcstedadrcurrent,
       isnull(MAX(bv28_fcstedadrchange), 0.0) as bv28_fcstedadrchange,
       isnull(MAX(bv29_fcstedadrcurrent), 0.0) as bv29_fcstedadrcurrent,
       isnull(MAX(bv29_fcstedadrchange), 0.0) as bv29_fcstedadrchange,
       isnull(MAX(bv30_fcstedadrcurrent), 0.0) as bv30_fcstedadrcurrent,
       isnull(MAX(bv30_fcstedadrchange), 0.0) as bv30_fcstedadrchange,
       isnull(MAX(bv31_fcstedadrcurrent), 0.0) as bv31_fcstedadrcurrent,
       isnull(MAX(bv31_fcstedadrchange), 0.0) as bv31_fcstedadrchange,
       isnull(MAX(bv32_fcstedadrcurrent), 0.0) as bv32_fcstedadrcurrent,
       isnull(MAX(bv32_fcstedadrchange), 0.0) as bv32_fcstedadrchange,
       isnull(MAX(bv33_fcstedadrcurrent), 0.0) as bv33_fcstedadrcurrent,
       isnull(MAX(bv33_fcstedadrchange), 0.0) as bv33_fcstedadrchange,
       isnull(MAX(bv34_fcstedadrcurrent), 0.0) as bv34_fcstedadrcurrent,
       isnull(MAX(bv34_fcstedadrchange), 0.0) as bv34_fcstedadrchange,
       isnull(MAX(bv35_fcstedadrcurrent), 0.0) as bv35_fcstedadrcurrent,
       isnull(MAX(bv35_fcstedadrchange), 0.0) as bv35_fcstedadrchange,
       isnull(MAX(bv36_fcstedadrcurrent), 0.0) as bv36_fcstedadrcurrent,
       isnull(MAX(bv36_fcstedadrchange), 0.0) as bv36_fcstedadrchange,
       isnull(MAX(bv37_fcstedadrcurrent), 0.0) as bv37_fcstedadrcurrent,
       isnull(MAX(bv37_fcstedadrchange), 0.0) as bv37_fcstedadrchange,
       isnull(MAX(bv38_fcstedadrcurrent), 0.0) as bv38_fcstedadrcurrent,
       isnull(MAX(bv38_fcstedadrchange), 0.0) as bv38_fcstedadrchange,
       isnull(MAX(bv39_fcstedadrcurrent), 0.0) as bv39_fcstedadrcurrent,
       isnull(MAX(bv39_fcstedadrchange), 0.0) as bv39_fcstedadrchange,
       isnull(MAX(bv40_fcstedadrcurrent), 0.0) as bv40_fcstedadrcurrent,
       isnull(MAX(bv40_fcstedadrchange), 0.0) as bv40_fcstedadrchange,
       isnull(MAX(bv41_fcstedadrcurrent), 0.0) as bv41_fcstedadrcurrent,
       isnull(MAX(bv41_fcstedadrchange), 0.0) as bv41_fcstedadrchange,
       isnull(MAX(bv42_fcstedadrcurrent), 0.0) as bv42_fcstedadrcurrent,
       isnull(MAX(bv42_fcstedadrchange), 0.0) as bv42_fcstedadrchange,
       isnull(MAX(bv43_fcstedadrcurrent), 0.0) as bv43_fcstedadrcurrent,
       isnull(MAX(bv43_fcstedadrchange), 0.0) as bv43_fcstedadrchange,
       isnull(MAX(bv44_fcstedadrcurrent), 0.0) as bv44_fcstedadrcurrent,
       isnull(MAX(bv44_fcstedadrchange), 0.0) as bv44_fcstedadrchange,
       isnull(MAX(bv45_fcstedadrcurrent), 0.0) as bv45_fcstedadrcurrent,
       isnull(MAX(bv45_fcstedadrchange), 0.0) as bv45_fcstedadrchange,
       isnull(MAX(bv46_fcstedadrcurrent), 0.0) as bv46_fcstedadrcurrent,
       isnull(MAX(bv46_fcstedadrchange), 0.0) as bv46_fcstedadrchange,
       isnull(MAX(bv47_fcstedadrcurrent), 0.0) as bv47_fcstedadrcurrent,
       isnull(MAX(bv47_fcstedadrchange), 0.0) as bv47_fcstedadrchange,
       isnull(MAX(bv48_fcstedadrcurrent), 0.0) as bv48_fcstedadrcurrent,
       isnull(MAX(bv48_fcstedadrchange), 0.0) as bv48_fcstedadrchange,
       isnull(MAX(bv49_fcstedadrcurrent), 0.0) as bv49_fcstedadrcurrent,
       isnull(MAX(bv49_fcstedadrchange), 0.0) as bv49_fcstedadrchange,
       isnull(MAX(bv50_fcstedadrcurrent), 0.0) as bv50_fcstedadrcurrent,
       isnull(MAX(bv50_fcstedadrchange), 0.0) as bv50_fcstedadrchange,
       --Archana added for group block-, group pickup-Start
       isnull(MAX(bv1_block), 0) as bv1_block,
       isnull(MAX(bv1_block_available), 0) as bv1_block_available,
       isnull(MAX(bv1_block_pickup), 0) as bv1_block_pickup,
       isnull(MAX(bv2_block), 0) as bv2_block,
       isnull(MAX(bv2_block_available), 0) as bv2_block_available,
       isnull(MAX(bv2_block_pickup), 0) as bv2_block_pickup,
       isnull(MAX(bv3_block), 0) as bv3_block,
       isnull(MAX(bv3_block_available), 0) as bv3_block_available,
       isnull(MAX(bv3_block_pickup), 0) as bv3_block_pickup,
       isnull(MAX(bv4_block), 0) as bv4_block,
       isnull(MAX(bv4_block_available), 0) as bv4_block_available,
       isnull(MAX(bv4_block_pickup), 0) as bv4_block_pickup,
       isnull(MAX(bv5_block), 0) as bv5_block,
       isnull(MAX(bv5_block_available), 0) as bv5_block_available,
       isnull(MAX(bv5_block_pickup), 0) as bv5_block_pickup,
       isnull(MAX(bv6_block), 0) as bv6_block,
       isnull(MAX(bv6_block_available), 0) as bv6_block_available,
       isnull(MAX(bv6_block_pickup), 0) as bv6_block_pickup,
       isnull(MAX(bv7_block), 0) as bv7_block,
       isnull(MAX(bv7_block_available), 0) as bv7_block_available,
       isnull(MAX(bv7_block_pickup), 0) as bv7_block_pickup,
       isnull(MAX(bv8_block), 0) as bv8_block,
       isnull(MAX(bv8_block_available), 0) as bv8_block_available,
       isnull(MAX(bv8_block_pickup), 0) as bv8_block_pickup,
       isnull(MAX(bv9_block), 0) as bv9_block,
       isnull(MAX(bv9_block_available), 0) as bv9_block_available,
       isnull(MAX(bv9_block_pickup), 0) as bv9_block_pickup,
       isnull(MAX(bv10_block), 0) as bv10_block,
       isnull(MAX(bv10_block_available), 0) as bv10_block_available,
       isnull(MAX(bv10_block_pickup), 0) as bv10_block_pickup,
       isnull(MAX(bv11_block), 0) as bv11_block,
       isnull(MAX(bv11_block_available), 0) as bv11_block_available,
       isnull(MAX(bv11_block_pickup), 0) as bv11_block_pickup,
       isnull(MAX(bv12_block), 0) as bv12_block,
       isnull(MAX(bv12_block_available), 0) as bv12_block_available,
       isnull(MAX(bv12_block_pickup), 0) as bv12_block_pickup,
       isnull(MAX(bv13_block), 0) as bv13_block,
       isnull(MAX(bv13_block_available), 0) as bv13_block_available,
       isnull(MAX(bv13_block_pickup), 0) as bv13_block_pickup,
       isnull(MAX(bv14_block), 0) as bv14_block,
       isnull(MAX(bv14_block_available), 0) as bv14_block_available,
       isnull(MAX(bv14_block_pickup), 0) as bv14_block_pickup,
       isnull(MAX(bv15_block), 0) as bv15_block,
       isnull(MAX(bv15_block_available), 0) as bv15_block_available,
       isnull(MAX(bv15_block_pickup), 0) as bv15_block_pickup,
       isnull(MAX(bv16_block), 0) as bv16_block,
       isnull(MAX(bv16_block_available), 0) as bv16_block_available,
       isnull(MAX(bv16_block_pickup), 0) as bv16_block_pickup,
       isnull(MAX(bv17_block), 0) as bv17_block,
       isnull(MAX(bv17_block_available), 0) as bv17_block_available,
       isnull(MAX(bv17_block_pickup), 0) as bv17_block_pickup,
       isnull(MAX(bv18_block), 0) as bv18_block,
       isnull(MAX(bv18_block_available), 0) as bv18_block_available,
       isnull(MAX(bv18_block_pickup), 0) as bv18_block_pickup,
       isnull(MAX(bv19_block), 0) as bv19_block,
       isnull(MAX(bv19_block_available), 0) as bv19_block_available,
       isnull(MAX(bv19_block_pickup), 0) as bv19_block_pickup,
       isnull(MAX(bv20_block), 0) as bv20_block,
       isnull(MAX(bv20_block_available), 0) as bv20_block_available,
       isnull(MAX(bv20_block_pickup), 0) as bv20_block_pickup,
       isnull(MAX(bv21_block), 0) as bv21_block,
       isnull(MAX(bv21_block_available), 0) as bv21_block_available,
       isnull(MAX(bv21_block_pickup), 0) as bv21_block_pickup,
       isnull(MAX(bv22_block), 0) as bv22_block,
       isnull(MAX(bv22_block_available), 0) as bv22_block_available,
       isnull(MAX(bv22_block_pickup), 0) as bv22_block_pickup,
       isnull(MAX(bv23_block), 0) as bv23_block,
       isnull(MAX(bv23_block_available), 0) as bv23_block_available,
       isnull(MAX(bv23_block_pickup), 0) as bv23_block_pickup,
       isnull(MAX(bv24_block), 0) as bv24_block,
       isnull(MAX(bv24_block_available), 0) as bv24_block_available,
       isnull(MAX(bv24_block_pickup), 0) as bv24_block_pickup,
       isnull(MAX(bv25_block), 0) as bv25_block,
       isnull(MAX(bv25_block_available), 0) as bv25_block_available,
       isnull(MAX(bv25_block_pickup), 0) as bv25_block_pickup,
       isnull(MAX(bv26_block), 0) as bv26_block,
       isnull(MAX(bv26_block_available), 0) as bv26_block_available,
       isnull(MAX(bv26_block_pickup), 0) as bv26_block_pickup,
       isnull(MAX(bv27_block), 0) as bv27_block,
       isnull(MAX(bv27_block_available), 0) as bv27_block_available,
       isnull(MAX(bv27_block_pickup), 0) as bv27_block_pickup,
       isnull(MAX(bv28_block), 0) as bv28_block,
       isnull(MAX(bv28_block_available), 0) as bv28_block_available,
       isnull(MAX(bv28_block_pickup), 0) as bv28_block_pickup,
       isnull(MAX(bv29_block), 0) as bv29_block,
       isnull(MAX(bv29_block_available), 0) as bv29_block_available,
       isnull(MAX(bv29_block_pickup), 0) as bv29_block_pickup,
       isnull(MAX(bv30_block), 0) as bv30_block,
       isnull(MAX(bv30_block_available), 0) as bv30_block_available,
       isnull(MAX(bv30_block_pickup), 0) as bv30_block_pickup,
       isnull(MAX(bv31_block), 0) as bv31_block,
       isnull(MAX(bv31_block_available), 0) as bv31_block_available,
       isnull(MAX(bv31_block_pickup), 0) as bv31_block_pickup,
       isnull(MAX(bv32_block), 0) as bv32_block,
       isnull(MAX(bv32_block_available), 0) as bv32_block_available,
       isnull(MAX(bv32_block_pickup), 0) as bv32_block_pickup,
       isnull(MAX(bv33_block), 0) as bv33_block,
       isnull(MAX(bv33_block_available), 0) as bv33_block_available,
       isnull(MAX(bv33_block_pickup), 0) as bv33_block_pickup,
       isnull(MAX(bv34_block), 0) as bv34_block,
       isnull(MAX(bv34_block_available), 0) as bv34_block_available,
       isnull(MAX(bv34_block_pickup), 0) as bv34_block_pickup,
       isnull(MAX(bv35_block), 0) as bv35_block,
       isnull(MAX(bv35_block_available), 0) as bv35_block_available,
       isnull(MAX(bv35_block_pickup), 0) as bv35_block_pickup,
       isnull(MAX(bv36_block), 0) as bv36_block,
       isnull(MAX(bv36_block_available), 0) as bv36_block_available,
       isnull(MAX(bv36_block_pickup), 0) as bv36_block_pickup,
       isnull(MAX(bv37_block), 0) as bv37_block,
       isnull(MAX(bv37_block_available), 0) as bv37_block_available,
       isnull(MAX(bv37_block_pickup), 0) as bv37_block_pickup,
       isnull(MAX(bv38_block), 0) as bv38_block,
       isnull(MAX(bv38_block_available), 0) as bv38_block_available,
       isnull(MAX(bv38_block_pickup), 0) as bv38_block_pickup,
       isnull(MAX(bv39_block), 0) as bv39_block,
       isnull(MAX(bv39_block_available), 0) as bv39_block_available,
       isnull(MAX(bv39_block_pickup), 0) as bv39_block_pickup,
       isnull(MAX(bv40_block), 0) as bv40_block,
       isnull(MAX(bv40_block_available), 0) as bv40_block_available,
       isnull(MAX(bv40_block_pickup), 0) as bv40_block_pickup,
       isnull(MAX(bv41_block), 0) as bv41_block,
       isnull(MAX(bv41_block_available), 0) as bv41_block_available,
       isnull(MAX(bv41_block_pickup), 0) as bv41_block_pickup,
       isnull(MAX(bv42_block), 0) as bv42_block,
       isnull(MAX(bv42_block_available), 0) as bv42_block_available,
       isnull(MAX(bv42_block_pickup), 0) as bv42_block_pickup,
       isnull(MAX(bv43_block), 0) as bv43_block,
       isnull(MAX(bv43_block_available), 0) as bv43_block_available,
       isnull(MAX(bv43_block_pickup), 0) as bv43_block_pickup,
       isnull(MAX(bv44_block), 0) as bv44_block,
       isnull(MAX(bv44_block_available), 0) as bv44_block_available,
       isnull(MAX(bv44_block_pickup), 0) as bv44_block_pickup,
       isnull(MAX(bv45_block), 0) as bv45_block,
       isnull(MAX(bv45_block_available), 0) as bv45_block_available,
       isnull(MAX(bv45_block_pickup), 0) as bv45_block_pickup,
       isnull(MAX(bv46_block), 0) as bv46_block,
       isnull(MAX(bv46_block_available), 0) as bv46_block_available,
       isnull(MAX(bv46_block_pickup), 0) as bv46_block_pickup,
       isnull(MAX(bv47_block), 0) as bv47_block,
       isnull(MAX(bv47_block_available), 0) as bv47_block_available,
       isnull(MAX(bv47_block_pickup), 0) as bv47_block_pickup,
       isnull(MAX(bv48_block), 0) as bv48_block,
       isnull(MAX(bv48_block_available), 0) as bv48_block_available,
       isnull(MAX(bv48_block_pickup), 0) as bv48_block_pickup,
       isnull(MAX(bv49_block), 0) as bv49_block,
       isnull(MAX(bv49_block_available), 0) as bv49_block_available,
       isnull(MAX(bv49_block_pickup), 0) as bv49_block_pickup,
       isnull(MAX(bv50_block), 0) as bv50_block,
       isnull(MAX(bv50_block_available), 0) as bv50_block_available,
       isnull(MAX(bv50_block_pickup), 0) as bv50_block_pickup
       --Archana added for group block-, group pickup-End

from
    (
        select occupancy_dt,
               dow,
               (case Business_Group_ID
                    when @bv1 then
                        Business_Group_Name
                   end
                   ) as bv1_businessgroupname,
               (case Business_Group_ID
                    when @bv2 then
                        Business_Group_Name
                   end
                   ) as bv2_businessgroupname,
               (case Business_Group_ID
                    when @bv3 then
                        Business_Group_Name
                   end
                   ) as bv3_businessgroupname,
               (case Business_Group_ID
                    when @bv4 then
                        Business_Group_Name
                   end
                   ) as bv4_businessgroupname,
               (case Business_Group_ID
                    when @bv5 then
                        Business_Group_Name
                   end
                   ) as bv5_businessgroupname,
               (case Business_Group_ID
                    when @bv6 then
                        Business_Group_Name
                   end
                   ) as bv6_businessgroupname,
               (case Business_Group_ID
                    when @bv7 then
                        Business_Group_Name
                   end
                   ) as bv7_businessgroupname,
               (case Business_Group_ID
                    when @bv8 then
                        Business_Group_Name
                   end
                   ) as bv8_businessgroupname,
               (case Business_Group_ID
                    when @bv9 then
                        Business_Group_Name
                   end
                   ) as bv9_businessgroupname,
               (case Business_Group_ID
                    when @bv10 then
                        Business_Group_Name
                   end
                   ) as bv10_businessgroupname,
               (case Business_Group_ID
                    when @bv11 then
                        Business_Group_Name
                   end
                   ) as bv11_businessgroupname,
               (case Business_Group_ID
                    when @bv12 then
                        Business_Group_Name
                   end
                   ) as bv12_businessgroupname,
               (case Business_Group_ID
                    when @bv13 then
                        Business_Group_Name
                   end
                   ) as bv13_businessgroupname,
               (case Business_Group_ID
                    when @bv14 then
                        Business_Group_Name
                   end
                   ) as bv14_businessgroupname,
               (case Business_Group_ID
                    when @bv15 then
                        Business_Group_Name
                   end
                   ) as bv15_businessgroupname,
               (case Business_Group_ID
                    when @bv16 then
                        Business_Group_Name
                   end
                   ) as bv16_businessgroupname,
               (case Business_Group_ID
                    when @bv17 then
                        Business_Group_Name
                   end
                   ) as bv17_businessgroupname,
               (case Business_Group_ID
                    when @bv18 then
                        Business_Group_Name
                   end
                   ) as bv18_businessgroupname,
               (case Business_Group_ID
                    when @bv19 then
                        Business_Group_Name
                   end
                   ) as bv19_businessgroupname,
               (case Business_Group_ID
                    when @bv20 then
                        Business_Group_Name
                   end
                   ) as bv20_businessgroupname,
               (case Business_Group_ID
                    when @bv21 then
                        Business_Group_Name
                   end
                   ) as bv21_businessgroupname,
               (case Business_Group_ID
                    when @bv22 then
                        Business_Group_Name
                   end
                   ) as bv22_businessgroupname,
               (case Business_Group_ID
                    when @bv23 then
                        Business_Group_Name
                   end
                   ) as bv23_businessgroupname,
               (case Business_Group_ID
                    when @bv24 then
                        Business_Group_Name
                   end
                   ) as bv24_businessgroupname,
               (case Business_Group_ID
                    when @bv25 then
                        Business_Group_Name
                   end
                   ) as bv25_businessgroupname,
               (case Business_Group_ID
                    when @bv26 then
                        Business_Group_Name
                   end
                   ) as bv26_businessgroupname,
               (case Business_Group_ID
                    when @bv27 then
                        Business_Group_Name
                   end
                   ) as bv27_businessgroupname,
               (case Business_Group_ID
                    when @bv28 then
                        Business_Group_Name
                   end
                   ) as bv28_businessgroupname,
               (case Business_Group_ID
                    when @bv29 then
                        Business_Group_Name
                   end
                   ) as bv29_businessgroupname,
               (case Business_Group_ID
                    when @bv30 then
                        Business_Group_Name
                   end
                   ) as bv30_businessgroupname,
               (case Business_Group_ID
                    when @bv31 then
                        Business_Group_Name
                   end
                   ) as bv31_businessgroupname,
               (case Business_Group_ID
                    when @bv32 then
                        Business_Group_Name
                   end
                   ) as bv32_businessgroupname,
               (case Business_Group_ID
                    when @bv33 then
                        Business_Group_Name
                   end
                   ) as bv33_businessgroupname,
               (case Business_Group_ID
                    when @bv34 then
                        Business_Group_Name
                   end
                   ) as bv34_businessgroupname,
               (case Business_Group_ID
                    when @bv35 then
                        Business_Group_Name
                   end
                   ) as bv35_businessgroupname,
               (case Business_Group_ID
                    when @bv36 then
                        Business_Group_Name
                   end
                   ) as bv36_businessgroupname,
               (case Business_Group_ID
                    when @bv37 then
                        Business_Group_Name
                   end
                   ) as bv37_businessgroupname,
               (case Business_Group_ID
                    when @bv38 then
                        Business_Group_Name
                   end
                   ) as bv38_businessgroupname,
               (case Business_Group_ID
                    when @bv39 then
                        Business_Group_Name
                   end
                   ) as bv39_businessgroupname,
               (case Business_Group_ID
                    when @bv40 then
                        Business_Group_Name
                   end
                   ) as bv40_businessgroupname,
               (case Business_Group_ID
                    when @bv41 then
                        Business_Group_Name
                   end
                   ) as bv41_businessgroupname,
               (case Business_Group_ID
                    when @bv42 then
                        Business_Group_Name
                   end
                   ) as bv42_businessgroupname,
               (case Business_Group_ID
                    when @bv43 then
                        Business_Group_Name
                   end
                   ) as bv43_businessgroupname,
               (case Business_Group_ID
                    when @bv44 then
                        Business_Group_Name
                   end
                   ) as bv44_businessgroupname,
               (case Business_Group_ID
                    when @bv45 then
                        Business_Group_Name
                   end
                   ) as bv45_businessgroupname,
               (case Business_Group_ID
                    when @bv46 then
                        Business_Group_Name
                   end
                   ) as bv46_businessgroupname,
               (case Business_Group_ID
                    when @bv47 then
                        Business_Group_Name
                   end
                   ) as bv47_businessgroupname,
               (case Business_Group_ID
                    when @bv48 then
                        Business_Group_Name
                   end
                   ) as bv48_businessgroupname,
               (case Business_Group_ID
                    when @bv49 then
                        Business_Group_Name
                   end
                   ) as bv49_businessgroupname,
               (case Business_Group_ID
                    when @bv50 then
                        Business_Group_Name
                   end
                   ) as bv50_businessgroupname,
               (case Business_Group_ID
                    when @bv1 then
                        roomsoldcurrent
                   end
                   ) as bv1_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        roomssoldchange
                   end
                   ) as bv1_roomssoldchange,
               (case Business_Group_ID
                    when @bv2 then
                        roomsoldcurrent
                   end
                   ) as bv2_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        roomssoldchange
                   end
                   ) as bv2_roomssoldchange,
               (case Business_Group_ID
                    when @bv3 then
                        roomsoldcurrent
                   end
                   ) as bv3_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        roomssoldchange
                   end
                   ) as bv3_roomssoldchange,
               (case Business_Group_ID
                    when @bv4 then
                        roomsoldcurrent
                   end
                   ) as bv4_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        roomssoldchange
                   end
                   ) as bv4_roomssoldchange,
               (case Business_Group_ID
                    when @bv5 then
                        roomsoldcurrent
                   end
                   ) as bv5_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        roomssoldchange
                   end
                   ) as bv5_roomssoldchange,
               (case Business_Group_ID
                    when @bv6 then
                        roomsoldcurrent
                   end
                   ) as bv6_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        roomssoldchange
                   end
                   ) as bv6_roomssoldchange,
               (case Business_Group_ID
                    when @bv7 then
                        roomsoldcurrent
                   end
                   ) as bv7_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        roomssoldchange
                   end
                   ) as bv7_roomssoldchange,
               (case Business_Group_ID
                    when @bv8 then
                        roomsoldcurrent
                   end
                   ) as bv8_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        roomssoldchange
                   end
                   ) as bv8_roomssoldchange,
               (case Business_Group_ID
                    when @bv9 then
                        roomsoldcurrent
                   end
                   ) as bv9_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        roomssoldchange
                   end
                   ) as bv9_roomssoldchange,
               (case Business_Group_ID
                    when @bv10 then
                        roomsoldcurrent
                   end
                   ) as bv10_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        roomssoldchange
                   end
                   ) as bv10_roomssoldchange,
               (case Business_Group_ID
                    when @bv11 then
                        roomsoldcurrent
                   end
                   ) as bv11_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        roomssoldchange
                   end
                   ) as bv11_roomssoldchange,
               (case Business_Group_ID
                    when @bv12 then
                        roomsoldcurrent
                   end
                   ) as bv12_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        roomssoldchange
                   end
                   ) as bv12_roomssoldchange,
               (case Business_Group_ID
                    when @bv13 then
                        roomsoldcurrent
                   end
                   ) as bv13_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        roomssoldchange
                   end
                   ) as bv13_roomssoldchange,
               (case Business_Group_ID
                    when @bv14 then
                        roomsoldcurrent
                   end
                   ) as bv14_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        roomssoldchange
                   end
                   ) as bv14_roomssoldchange,
               (case Business_Group_ID
                    when @bv15 then
                        roomsoldcurrent
                   end
                   ) as bv15_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        roomssoldchange
                   end
                   ) as bv15_roomssoldchange,
               (case Business_Group_ID
                    when @bv16 then
                        roomsoldcurrent
                   end
                   ) as bv16_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        roomssoldchange
                   end
                   ) as bv16_roomssoldchange,
               (case Business_Group_ID
                    when @bv17 then
                        roomsoldcurrent
                   end
                   ) as bv17_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        roomssoldchange
                   end
                   ) as bv17_roomssoldchange,
               (case Business_Group_ID
                    when @bv18 then
                        roomsoldcurrent
                   end
                   ) as bv18_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        roomssoldchange
                   end
                   ) as bv18_roomssoldchange,
               (case Business_Group_ID
                    when @bv19 then
                        roomsoldcurrent
                   end
                   ) as bv19_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        roomssoldchange
                   end
                   ) as bv19_roomssoldchange,
               (case Business_Group_ID
                    when @bv20 then
                        roomsoldcurrent
                   end
                   ) as bv20_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        roomssoldchange
                   end
                   ) as bv20_roomssoldchange,
               (case Business_Group_ID
                    when @bv21 then
                        roomsoldcurrent
                   end
                   ) as bv21_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        roomssoldchange
                   end
                   ) as bv21_roomssoldchange,
               (case Business_Group_ID
                    when @bv22 then
                        roomsoldcurrent
                   end
                   ) as bv22_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        roomssoldchange
                   end
                   ) as bv22_roomssoldchange,
               (case Business_Group_ID
                    when @bv23 then
                        roomsoldcurrent
                   end
                   ) as bv23_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        roomssoldchange
                   end
                   ) as bv23_roomssoldchange,
               (case Business_Group_ID
                    when @bv24 then
                        roomsoldcurrent
                   end
                   ) as bv24_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        roomssoldchange
                   end
                   ) as bv24_roomssoldchange,
               (case Business_Group_ID
                    when @bv25 then
                        roomsoldcurrent
                   end
                   ) as bv25_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        roomssoldchange
                   end
                   ) as bv25_roomssoldchange,
               (case Business_Group_ID
                    when @bv26 then
                        roomsoldcurrent
                   end
                   ) as bv26_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        roomssoldchange
                   end
                   ) as bv26_roomssoldchange,
               (case Business_Group_ID
                    when @bv27 then
                        roomsoldcurrent
                   end
                   ) as bv27_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        roomssoldchange
                   end
                   ) as bv27_roomssoldchange,
               (case Business_Group_ID
                    when @bv28 then
                        roomsoldcurrent
                   end
                   ) as bv28_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        roomssoldchange
                   end
                   ) as bv28_roomssoldchange,
               (case Business_Group_ID
                    when @bv29 then
                        roomsoldcurrent
                   end
                   ) as bv29_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        roomssoldchange
                   end
                   ) as bv29_roomssoldchange,
               (case Business_Group_ID
                    when @bv30 then
                        roomsoldcurrent
                   end
                   ) as bv30_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        roomssoldchange
                   end
                   ) as bv30_roomssoldchange,
               (case Business_Group_ID
                    when @bv31 then
                        roomsoldcurrent
                   end
                   ) as bv31_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        roomssoldchange
                   end
                   ) as bv31_roomssoldchange,
               (case Business_Group_ID
                    when @bv32 then
                        roomsoldcurrent
                   end
                   ) as bv32_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        roomssoldchange
                   end
                   ) as bv32_roomssoldchange,
               (case Business_Group_ID
                    when @bv33 then
                        roomsoldcurrent
                   end
                   ) as bv33_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        roomssoldchange
                   end
                   ) as bv33_roomssoldchange,
               (case Business_Group_ID
                    when @bv34 then
                        roomsoldcurrent
                   end
                   ) as bv34_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        roomssoldchange
                   end
                   ) as bv34_roomssoldchange,
               (case Business_Group_ID
                    when @bv35 then
                        roomsoldcurrent
                   end
                   ) as bv35_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        roomssoldchange
                   end
                   ) as bv35_roomssoldchange,
               (case Business_Group_ID
                    when @bv36 then
                        roomsoldcurrent
                   end
                   ) as bv36_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        roomssoldchange
                   end
                   ) as bv36_roomssoldchange,
               (case Business_Group_ID
                    when @bv37 then
                        roomsoldcurrent
                   end
                   ) as bv37_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        roomssoldchange
                   end
                   ) as bv37_roomssoldchange,
               (case Business_Group_ID
                    when @bv38 then
                        roomsoldcurrent
                   end
                   ) as bv38_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        roomssoldchange
                   end
                   ) as bv38_roomssoldchange,
               (case Business_Group_ID
                    when @bv39 then
                        roomsoldcurrent
                   end
                   ) as bv39_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        roomssoldchange
                   end
                   ) as bv39_roomssoldchange,
               (case Business_Group_ID
                    when @bv40 then
                        roomsoldcurrent
                   end
                   ) as bv40_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        roomssoldchange
                   end
                   ) as bv40_roomssoldchange,
               (case Business_Group_ID
                    when @bv41 then
                        roomsoldcurrent
                   end
                   ) as bv41_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        roomssoldchange
                   end
                   ) as bv41_roomssoldchange,
               (case Business_Group_ID
                    when @bv42 then
                        roomsoldcurrent
                   end
                   ) as bv42_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        roomssoldchange
                   end
                   ) as bv42_roomssoldchange,
               (case Business_Group_ID
                    when @bv43 then
                        roomsoldcurrent
                   end
                   ) as bv43_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        roomssoldchange
                   end
                   ) as bv43_roomssoldchange,
               (case Business_Group_ID
                    when @bv44 then
                        roomsoldcurrent
                   end
                   ) as bv44_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        roomssoldchange
                   end
                   ) as bv44_roomssoldchange,
               (case Business_Group_ID
                    when @bv45 then
                        roomsoldcurrent
                   end
                   ) as bv45_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        roomssoldchange
                   end
                   ) as bv45_roomssoldchange,
               (case Business_Group_ID
                    when @bv46 then
                        roomsoldcurrent
                   end
                   ) as bv46_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        roomssoldchange
                   end
                   ) as bv46_roomssoldchange,
               (case Business_Group_ID
                    when @bv47 then
                        roomsoldcurrent
                   end
                   ) as bv47_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        roomssoldchange
                   end
                   ) as bv47_roomssoldchange,
               (case Business_Group_ID
                    when @bv48 then
                        roomsoldcurrent
                   end
                   ) as bv48_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        roomssoldchange
                   end
                   ) as bv48_roomssoldchange,
               (case Business_Group_ID
                    when @bv49 then
                        roomsoldcurrent
                   end
                   ) as bv49_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        roomssoldchange
                   end
                   ) as bv49_roomssoldchange,
               (case Business_Group_ID
                    when @bv50 then
                        roomsoldcurrent
                   end
                   ) as bv50_roomsoldcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        roomssoldchange
                   end
                   ) as bv50_roomssoldchange,
               (case Business_Group_ID
                    when @bv1 then
                        occfcstcurrent
                   end
                   ) as bv1_occfcstcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        occfcstchange
                   end
                   ) as bv1_occfcstchange,
               (case Business_Group_ID
                    when @bv2 then
                        occfcstcurrent
                   end
                   ) as bv2_occfcstcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        occfcstchange
                   end
                   ) as bv2_occfcstchange,
               (case Business_Group_ID
                    when @bv3 then
                        occfcstcurrent
                   end
                   ) as bv3_occfcstcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        occfcstchange
                   end
                   ) as bv3_occfcstchange,
               (case Business_Group_ID
                    when @bv4 then
                        occfcstcurrent
                   end
                   ) as bv4_occfcstcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        occfcstchange
                   end
                   ) as bv4_occfcstchange,
               (case Business_Group_ID
                    when @bv5 then
                        occfcstcurrent
                   end
                   ) as bv5_occfcstcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        occfcstchange
                   end
                   ) as bv5_occfcstchange,
               (case Business_Group_ID
                    when @bv6 then
                        occfcstcurrent
                   end
                   ) as bv6_occfcstcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        occfcstchange
                   end
                   ) as bv6_occfcstchange,
               (case Business_Group_ID
                    when @bv7 then
                        occfcstcurrent
                   end
                   ) as bv7_occfcstcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        occfcstchange
                   end
                   ) as bv7_occfcstchange,
               (case Business_Group_ID
                    when @bv8 then
                        occfcstcurrent
                   end
                   ) as bv8_occfcstcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        occfcstchange
                   end
                   ) as bv8_occfcstchange,
               (case Business_Group_ID
                    when @bv9 then
                        occfcstcurrent
                   end
                   ) as bv9_occfcstcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        occfcstchange
                   end
                   ) as bv9_occfcstchange,
               (case Business_Group_ID
                    when @bv10 then
                        occfcstcurrent
                   end
                   ) as bv10_occfcstcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        occfcstchange
                   end
                   ) as bv10_occfcstchange,
               (case Business_Group_ID
                    when @bv11 then
                        occfcstcurrent
                   end
                   ) as bv11_occfcstcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        occfcstchange
                   end
                   ) as bv11_occfcstchange,
               (case Business_Group_ID
                    when @bv12 then
                        occfcstcurrent
                   end
                   ) as bv12_occfcstcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        occfcstchange
                   end
                   ) as bv12_occfcstchange,
               (case Business_Group_ID
                    when @bv13 then
                        occfcstcurrent
                   end
                   ) as bv13_occfcstcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        occfcstchange
                   end
                   ) as bv13_occfcstchange,
               (case Business_Group_ID
                    when @bv14 then
                        occfcstcurrent
                   end
                   ) as bv14_occfcstcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        occfcstchange
                   end
                   ) as bv14_occfcstchange,
               (case Business_Group_ID
                    when @bv15 then
                        occfcstcurrent
                   end
                   ) as bv15_occfcstcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        occfcstchange
                   end
                   ) as bv15_occfcstchange,
               (case Business_Group_ID
                    when @bv16 then
                        occfcstcurrent
                   end
                   ) as bv16_occfcstcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        occfcstchange
                   end
                   ) as bv16_occfcstchange,
               (case Business_Group_ID
                    when @bv17 then
                        occfcstcurrent
                   end
                   ) as bv17_occfcstcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        occfcstchange
                   end
                   ) as bv17_occfcstchange,
               (case Business_Group_ID
                    when @bv18 then
                        occfcstcurrent
                   end
                   ) as bv18_occfcstcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        occfcstchange
                   end
                   ) as bv18_occfcstchange,
               (case Business_Group_ID
                    when @bv19 then
                        occfcstcurrent
                   end
                   ) as bv19_occfcstcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        occfcstchange
                   end
                   ) as bv19_occfcstchange,
               (case Business_Group_ID
                    when @bv20 then
                        occfcstcurrent
                   end
                   ) as bv20_occfcstcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        occfcstchange
                   end
                   ) as bv20_occfcstchange,
               (case Business_Group_ID
                    when @bv21 then
                        occfcstcurrent
                   end
                   ) as bv21_occfcstcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        occfcstchange
                   end
                   ) as bv21_occfcstchange,
               (case Business_Group_ID
                    when @bv22 then
                        occfcstcurrent
                   end
                   ) as bv22_occfcstcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        occfcstchange
                   end
                   ) as bv22_occfcstchange,
               (case Business_Group_ID
                    when @bv23 then
                        occfcstcurrent
                   end
                   ) as bv23_occfcstcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        occfcstchange
                   end
                   ) as bv23_occfcstchange,
               (case Business_Group_ID
                    when @bv24 then
                        occfcstcurrent
                   end
                   ) as bv24_occfcstcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        occfcstchange
                   end
                   ) as bv24_occfcstchange,
               (case Business_Group_ID
                    when @bv25 then
                        occfcstcurrent
                   end
                   ) as bv25_occfcstcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        occfcstchange
                   end
                   ) as bv25_occfcstchange,
               (case Business_Group_ID
                    when @bv26 then
                        occfcstcurrent
                   end
                   ) as bv26_occfcstcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        occfcstchange
                   end
                   ) as bv26_occfcstchange,
               (case Business_Group_ID
                    when @bv27 then
                        occfcstcurrent
                   end
                   ) as bv27_occfcstcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        occfcstchange
                   end
                   ) as bv27_occfcstchange,
               (case Business_Group_ID
                    when @bv28 then
                        occfcstcurrent
                   end
                   ) as bv28_occfcstcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        occfcstchange
                   end
                   ) as bv28_occfcstchange,
               (case Business_Group_ID
                    when @bv29 then
                        occfcstcurrent
                   end
                   ) as bv29_occfcstcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        occfcstchange
                   end
                   ) as bv29_occfcstchange,
               (case Business_Group_ID
                    when @bv30 then
                        occfcstcurrent
                   end
                   ) as bv30_occfcstcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        occfcstchange
                   end
                   ) as bv30_occfcstchange,
               (case Business_Group_ID
                    when @bv31 then
                        occfcstcurrent
                   end
                   ) as bv31_occfcstcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        occfcstchange
                   end
                   ) as bv31_occfcstchange,
               (case Business_Group_ID
                    when @bv32 then
                        occfcstcurrent
                   end
                   ) as bv32_occfcstcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        occfcstchange
                   end
                   ) as bv32_occfcstchange,
               (case Business_Group_ID
                    when @bv33 then
                        occfcstcurrent
                   end
                   ) as bv33_occfcstcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        occfcstchange
                   end
                   ) as bv33_occfcstchange,
               (case Business_Group_ID
                    when @bv34 then
                        occfcstcurrent
                   end
                   ) as bv34_occfcstcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        occfcstchange
                   end
                   ) as bv34_occfcstchange,
               (case Business_Group_ID
                    when @bv35 then
                        occfcstcurrent
                   end
                   ) as bv35_occfcstcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        occfcstchange
                   end
                   ) as bv35_occfcstchange,
               (case Business_Group_ID
                    when @bv36 then
                        occfcstcurrent
                   end
                   ) as bv36_occfcstcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        occfcstchange
                   end
                   ) as bv36_occfcstchange,
               (case Business_Group_ID
                    when @bv37 then
                        occfcstcurrent
                   end
                   ) as bv37_occfcstcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        occfcstchange
                   end
                   ) as bv37_occfcstchange,
               (case Business_Group_ID
                    when @bv38 then
                        occfcstcurrent
                   end
                   ) as bv38_occfcstcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        occfcstchange
                   end
                   ) as bv38_occfcstchange,
               (case Business_Group_ID
                    when @bv39 then
                        occfcstcurrent
                   end
                   ) as bv39_occfcstcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        occfcstchange
                   end
                   ) as bv39_occfcstchange,
               (case Business_Group_ID
                    when @bv40 then
                        occfcstcurrent
                   end
                   ) as bv40_occfcstcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        occfcstchange
                   end
                   ) as bv40_occfcstchange,
               (case Business_Group_ID
                    when @bv41 then
                        occfcstcurrent
                   end
                   ) as bv41_occfcstcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        occfcstchange
                   end
                   ) as bv41_occfcstchange,
               (case Business_Group_ID
                    when @bv42 then
                        occfcstcurrent
                   end
                   ) as bv42_occfcstcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        occfcstchange
                   end
                   ) as bv42_occfcstchange,
               (case Business_Group_ID
                    when @bv43 then
                        occfcstcurrent
                   end
                   ) as bv43_occfcstcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        occfcstchange
                   end
                   ) as bv43_occfcstchange,
               (case Business_Group_ID
                    when @bv44 then
                        occfcstcurrent
                   end
                   ) as bv44_occfcstcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        occfcstchange
                   end
                   ) as bv44_occfcstchange,
               (case Business_Group_ID
                    when @bv45 then
                        occfcstcurrent
                   end
                   ) as bv45_occfcstcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        occfcstchange
                   end
                   ) as bv45_occfcstchange,
               (case Business_Group_ID
                    when @bv46 then
                        occfcstcurrent
                   end
                   ) as bv46_occfcstcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        occfcstchange
                   end
                   ) as bv46_occfcstchange,
               (case Business_Group_ID
                    when @bv47 then
                        occfcstcurrent
                   end
                   ) as bv47_occfcstcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        occfcstchange
                   end
                   ) as bv47_occfcstchange,
               (case Business_Group_ID
                    when @bv48 then
                        occfcstcurrent
                   end
                   ) as bv48_occfcstcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        occfcstchange
                   end
                   ) as bv48_occfcstchange,
               (case Business_Group_ID
                    when @bv49 then
                        occfcstcurrent
                   end
                   ) as bv49_occfcstcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        occfcstchange
                   end
                   ) as bv49_occfcstchange,
               (case Business_Group_ID
                    when @bv50 then
                        occfcstcurrent
                   end
                   ) as bv50_occfcstcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        occfcstchange
                   end
                   ) as bv50_occfcstchange,
               (case Business_Group_ID
                    when @bv1 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv1_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv1 then
                        bookedroomrevenuechange
                   end
                   ) as bv1_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv2 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv2_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv2 then
                        bookedroomrevenuechange
                   end
                   ) as bv2_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv3 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv3_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv3 then
                        bookedroomrevenuechange
                   end
                   ) as bv3_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv4 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv4_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv4 then
                        bookedroomrevenuechange
                   end
                   ) as bv4_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv5 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv5_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv5 then
                        bookedroomrevenuechange
                   end
                   ) as bv5_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv6 then
                        bookedroomrevenuechange
                   end
                   ) as bv6_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv6 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv6_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv7_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedroomrevenuechange
                   end
                   ) as bv7_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv8 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv8_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv8 then
                        bookedroomrevenuechange
                   end
                   ) as bv8_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv9 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv9_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv9 then
                        bookedroomrevenuechange
                   end
                   ) as bv9_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv10 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv10_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv10 then
                        bookedroomrevenuechange
                   end
                   ) as bv10_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv11 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv11_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv11 then
                        bookedroomrevenuechange
                   end
                   ) as bv11_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv12 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv12_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv12 then
                        bookedroomrevenuechange
                   end
                   ) as bv12_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv13 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv13_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv13 then
                        bookedroomrevenuechange
                   end
                   ) as bv13_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv14 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv14_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv14 then
                        bookedroomrevenuechange
                   end
                   ) as bv14_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv15 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv15_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv15 then
                        bookedroomrevenuechange
                   end
                   ) as bv15_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv16 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv16_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv16 then
                        bookedroomrevenuechange
                   end
                   ) as bv16_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv17 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv17_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv17 then
                        bookedroomrevenuechange
                   end
                   ) as bv17_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv18 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv18_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv18 then
                        bookedroomrevenuechange
                   end
                   ) as bv18_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv19 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv19_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv19 then
                        bookedroomrevenuechange
                   end
                   ) as bv19_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv20 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv20_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv20 then
                        bookedroomrevenuechange
                   end
                   ) as bv20_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv21 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv21_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv21 then
                        bookedroomrevenuechange
                   end
                   ) as bv21_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv22 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv22_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv22 then
                        bookedroomrevenuechange
                   end
                   ) as bv22_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv23 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv23_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv23 then
                        bookedroomrevenuechange
                   end
                   ) as bv23_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv24 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv24_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv24 then
                        bookedroomrevenuechange
                   end
                   ) as bv24_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv25 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv25_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv25 then
                        bookedroomrevenuechange
                   end
                   ) as bv25_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv26 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv26_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv26 then
                        bookedroomrevenuechange
                   end
                   ) as bv26_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv27 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv27_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv27 then
                        bookedroomrevenuechange
                   end
                   ) as bv27_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv28 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv28_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv28 then
                        bookedroomrevenuechange
                   end
                   ) as bv28_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv29 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv29_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv29 then
                        bookedroomrevenuechange
                   end
                   ) as bv29_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv30 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv30_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv30 then
                        bookedroomrevenuechange
                   end
                   ) as bv30_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv31 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv31_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv31 then
                        bookedroomrevenuechange
                   end
                   ) as bv31_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv32 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv32_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv32 then
                        bookedroomrevenuechange
                   end
                   ) as bv32_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv33 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv33_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv33 then
                        bookedroomrevenuechange
                   end
                   ) as bv33_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv34 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv34_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv34 then
                        bookedroomrevenuechange
                   end
                   ) as bv34_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv35 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv35_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv35 then
                        bookedroomrevenuechange
                   end
                   ) as bv35_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv36 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv36_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv36 then
                        bookedroomrevenuechange
                   end
                   ) as bv36_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv37 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv37_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv37 then
                        bookedroomrevenuechange
                   end
                   ) as bv37_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv38 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv38_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv38 then
                        bookedroomrevenuechange
                   end
                   ) as bv38_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv39 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv39_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv39 then
                        bookedroomrevenuechange
                   end
                   ) as bv39_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv40 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv40_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv40 then
                        bookedroomrevenuechange
                   end
                   ) as bv40_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv41 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv41_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv41 then
                        bookedroomrevenuechange
                   end
                   ) as bv41_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv42 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv42_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv42 then
                        bookedroomrevenuechange
                   end
                   ) as bv42_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv43 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv43_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv43 then
                        bookedroomrevenuechange
                   end
                   ) as bv43_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv44 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv44_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv44 then
                        bookedroomrevenuechange
                   end
                   ) as bv44_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv45 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv45_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv45 then
                        bookedroomrevenuechange
                   end
                   ) as bv45_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv46 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv46_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv46 then
                        bookedroomrevenuechange
                   end
                   ) as bv46_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv47 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv47_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv47 then
                        bookedroomrevenuechange
                   end
                   ) as bv47_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv48 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv48_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv48 then
                        bookedroomrevenuechange
                   end
                   ) as bv48_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv49 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv49_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv49 then
                        bookedroomrevenuechange
                   end
                   ) as bv49_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv50 then
                        bookedroomrevenuecurrent
                   end
                   ) as bv50_bookedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv50 then
                        bookedroomrevenuechange
                   end
                   ) as bv50_bookedroomrevenuechange,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv1_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedroomrevenuechange
                   end
                   ) as bv1_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv2_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedroomrevenuechange
                   end
                   ) as bv2_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv3_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedroomrevenuechange
                   end
                   ) as bv3_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv4_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedroomrevenuechange
                   end
                   ) as bv4_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv5_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedroomrevenuechange
                   end
                   ) as bv5_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv6_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedroomrevenuechange
                   end
                   ) as bv6_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv7_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedroomrevenuechange
                   end
                   ) as bv7_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv8_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedroomrevenuechange
                   end
                   ) as bv8_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv9_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedroomrevenuechange
                   end
                   ) as bv9_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv10_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedroomrevenuechange
                   end
                   ) as bv10_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv11_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedroomrevenuechange
                   end
                   ) as bv11_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv12_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedroomrevenuechange
                   end
                   ) as bv12_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv13_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedroomrevenuechange
                   end
                   ) as bv13_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv14_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedroomrevenuechange
                   end
                   ) as bv14_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv15_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedroomrevenuechange
                   end
                   ) as bv15_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv16_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedroomrevenuechange
                   end
                   ) as bv16_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv17_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedroomrevenuechange
                   end
                   ) as bv17_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv18_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedroomrevenuechange
                   end
                   ) as bv18_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv19_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedroomrevenuechange
                   end
                   ) as bv19_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv20_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedroomrevenuechange
                   end
                   ) as bv20_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv21_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedroomrevenuechange
                   end
                   ) as bv21_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv22_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedroomrevenuechange
                   end
                   ) as bv22_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv23_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedroomrevenuechange
                   end
                   ) as bv23_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv24_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedroomrevenuechange
                   end
                   ) as bv24_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv25_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedroomrevenuechange
                   end
                   ) as bv25_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv26_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedroomrevenuechange
                   end
                   ) as bv26_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv27_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedroomrevenuechange
                   end
                   ) as bv27_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv28_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedroomrevenuechange
                   end
                   ) as bv28_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv29_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedroomrevenuechange
                   end
                   ) as bv29_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv30_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedroomrevenuechange
                   end
                   ) as bv30_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv31_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedroomrevenuechange
                   end
                   ) as bv31_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv32_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedroomrevenuechange
                   end
                   ) as bv32_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv33_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedroomrevenuechange
                   end
                   ) as bv33_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv34_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedroomrevenuechange
                   end
                   ) as bv34_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv35_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedroomrevenuechange
                   end
                   ) as bv35_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv36_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedroomrevenuechange
                   end
                   ) as bv36_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv37_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedroomrevenuechange
                   end
                   ) as bv37_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv38_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedroomrevenuechange
                   end
                   ) as bv38_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv39_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedroomrevenuechange
                   end
                   ) as bv39_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv40_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedroomrevenuechange
                   end
                   ) as bv40_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv41_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedroomrevenuechange
                   end
                   ) as bv41_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv42_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedroomrevenuechange
                   end
                   ) as bv42_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv43_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedroomrevenuechange
                   end
                   ) as bv43_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv44_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedroomrevenuechange
                   end
                   ) as bv44_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv45_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedroomrevenuechange
                   end
                   ) as bv45_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv46_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedroomrevenuechange
                   end
                   ) as bv46_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv47_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedroomrevenuechange
                   end
                   ) as bv47_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv48_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedroomrevenuechange
                   end
                   ) as bv48_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv49_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedroomrevenuechange
                   end
                   ) as bv49_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedroomrevenuecurrent
                   end
                   ) as bv50_fcstedroomrevenuecurrent,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedroomrevenuechange
                   end
                   ) as bv50_fcstedroomrevenuechange,
               (case Business_Group_ID
                    when @bv1 then
                        bookedadrcurrent
                   end
                   ) as bv1_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        bookedadrchange
                   end
                   ) as bv1_bookedadrchange,
               (case Business_Group_ID
                    when @bv2 then
                        bookedadrcurrent
                   end
                   ) as bv2_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        bookedadrchange
                   end
                   ) as bv2_bookedadrchange,
               (case Business_Group_ID
                    when @bv3 then
                        bookedadrcurrent
                   end
                   ) as bv3_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        bookedadrchange
                   end
                   ) as bv3_bookedadrchange,
               (case Business_Group_ID
                    when @bv4 then
                        bookedadrcurrent
                   end
                   ) as bv4_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        bookedadrchange
                   end
                   ) as bv4_bookedadrchange,
               (case Business_Group_ID
                    when @bv5 then
                        bookedadrcurrent
                   end
                   ) as bv5_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        bookedadrchange
                   end
                   ) as bv5_bookedadrchange,
               (case Business_Group_ID
                    when @bv6 then
                        bookedadrcurrent
                   end
                   ) as bv6_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        bookedadrchange
                   end
                   ) as bv6_bookedadrchange,
               (case Business_Group_ID
                    when @bv7 then
                        bookedadrcurrent
                   end
                   ) as bv7_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        bookedadrchange
                   end
                   ) as bv7_bookedadrchange,
               (case Business_Group_ID
                    when @bv8 then
                        bookedadrcurrent
                   end
                   ) as bv8_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        bookedadrchange
                   end
                   ) as bv8_bookedadrchange,
               (case Business_Group_ID
                    when @bv9 then
                        bookedadrcurrent
                   end
                   ) as bv9_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        bookedadrchange
                   end
                   ) as bv9_bookedadrchange,
               (case Business_Group_ID
                    when @bv10 then
                        bookedadrcurrent
                   end
                   ) as bv10_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        bookedadrchange
                   end
                   ) as bv10_bookedadrchange,
               (case Business_Group_ID
                    when @bv11 then
                        bookedadrcurrent
                   end
                   ) as bv11_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        bookedadrchange
                   end
                   ) as bv11_bookedadrchange,
               (case Business_Group_ID
                    when @bv12 then
                        bookedadrcurrent
                   end
                   ) as bv12_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        bookedadrchange
                   end
                   ) as bv12_bookedadrchange,
               (case Business_Group_ID
                    when @bv13 then
                        bookedadrcurrent
                   end
                   ) as bv13_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        bookedadrchange
                   end
                   ) as bv13_bookedadrchange,
               (case Business_Group_ID
                    when @bv14 then
                        bookedadrcurrent
                   end
                   ) as bv14_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        bookedadrchange
                   end
                   ) as bv14_bookedadrchange,
               (case Business_Group_ID
                    when @bv15 then
                        bookedadrcurrent
                   end
                   ) as bv15_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        bookedadrchange
                   end
                   ) as bv15_bookedadrchange,
               (case Business_Group_ID
                    when @bv16 then
                        bookedadrcurrent
                   end
                   ) as bv16_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        bookedadrchange
                   end
                   ) as bv16_bookedadrchange,
               (case Business_Group_ID
                    when @bv17 then
                        bookedadrcurrent
                   end
                   ) as bv17_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        bookedadrchange
                   end
                   ) as bv17_bookedadrchange,
               (case Business_Group_ID
                    when @bv18 then
                        bookedadrcurrent
                   end
                   ) as bv18_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        bookedadrchange
                   end
                   ) as bv18_bookedadrchange,
               (case Business_Group_ID
                    when @bv19 then
                        bookedadrcurrent
                   end
                   ) as bv19_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        bookedadrchange
                   end
                   ) as bv19_bookedadrchange,
               (case Business_Group_ID
                    when @bv20 then
                        bookedadrcurrent
                   end
                   ) as bv20_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        bookedadrchange
                   end
                   ) as bv20_bookedadrchange,
               (case Business_Group_ID
                    when @bv21 then
                        bookedadrcurrent
                   end
                   ) as bv21_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        bookedadrchange
                   end
                   ) as bv21_bookedadrchange,
               (case Business_Group_ID
                    when @bv22 then
                        bookedadrcurrent
                   end
                   ) as bv22_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        bookedadrchange
                   end
                   ) as bv22_bookedadrchange,
               (case Business_Group_ID
                    when @bv23 then
                        bookedadrcurrent
                   end
                   ) as bv23_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        bookedadrchange
                   end
                   ) as bv23_bookedadrchange,
               (case Business_Group_ID
                    when @bv24 then
                        bookedadrcurrent
                   end
                   ) as bv24_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        bookedadrchange
                   end
                   ) as bv24_bookedadrchange,
               (case Business_Group_ID
                    when @bv25 then
                        bookedadrcurrent
                   end
                   ) as bv25_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        bookedadrchange
                   end
                   ) as bv25_bookedadrchange,
               (case Business_Group_ID
                    when @bv26 then
                        bookedadrcurrent
                   end
                   ) as bv26_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        bookedadrchange
                   end
                   ) as bv26_bookedadrchange,
               (case Business_Group_ID
                    when @bv27 then
                        bookedadrcurrent
                   end
                   ) as bv27_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        bookedadrchange
                   end
                   ) as bv27_bookedadrchange,
               (case Business_Group_ID
                    when @bv28 then
                        bookedadrcurrent
                   end
                   ) as bv28_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        bookedadrchange
                   end
                   ) as bv28_bookedadrchange,
               (case Business_Group_ID
                    when @bv29 then
                        bookedadrcurrent
                   end
                   ) as bv29_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        bookedadrchange
                   end
                   ) as bv29_bookedadrchange,
               (case Business_Group_ID
                    when @bv30 then
                        bookedadrcurrent
                   end
                   ) as bv30_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        bookedadrchange
                   end
                   ) as bv30_bookedadrchange,
               (case Business_Group_ID
                    when @bv31 then
                        bookedadrcurrent
                   end
                   ) as bv31_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        bookedadrchange
                   end
                   ) as bv31_bookedadrchange,
               (case Business_Group_ID
                    when @bv32 then
                        bookedadrcurrent
                   end
                   ) as bv32_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        bookedadrchange
                   end
                   ) as bv32_bookedadrchange,
               (case Business_Group_ID
                    when @bv33 then
                        bookedadrcurrent
                   end
                   ) as bv33_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        bookedadrchange
                   end
                   ) as bv33_bookedadrchange,
               (case Business_Group_ID
                    when @bv34 then
                        bookedadrcurrent
                   end
                   ) as bv34_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        bookedadrchange
                   end
                   ) as bv34_bookedadrchange,
               (case Business_Group_ID
                    when @bv35 then
                        bookedadrcurrent
                   end
                   ) as bv35_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        bookedadrchange
                   end
                   ) as bv35_bookedadrchange,
               (case Business_Group_ID
                    when @bv36 then
                        bookedadrcurrent
                   end
                   ) as bv36_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        bookedadrchange
                   end
                   ) as bv36_bookedadrchange,
               (case Business_Group_ID
                    when @bv37 then
                        bookedadrcurrent
                   end
                   ) as bv37_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        bookedadrchange
                   end
                   ) as bv37_bookedadrchange,
               (case Business_Group_ID
                    when @bv38 then
                        bookedadrcurrent
                   end
                   ) as bv38_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        bookedadrchange
                   end
                   ) as bv38_bookedadrchange,
               (case Business_Group_ID
                    when @bv39 then
                        bookedadrcurrent
                   end
                   ) as bv39_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        bookedadrchange
                   end
                   ) as bv39_bookedadrchange,
               (case Business_Group_ID
                    when @bv40 then
                        bookedadrcurrent
                   end
                   ) as bv40_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        bookedadrchange
                   end
                   ) as bv40_bookedadrchange,
               (case Business_Group_ID
                    when @bv41 then
                        bookedadrcurrent
                   end
                   ) as bv41_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        bookedadrchange
                   end
                   ) as bv41_bookedadrchange,
               (case Business_Group_ID
                    when @bv42 then
                        bookedadrcurrent
                   end
                   ) as bv42_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        bookedadrchange
                   end
                   ) as bv42_bookedadrchange,
               (case Business_Group_ID
                    when @bv43 then
                        bookedadrcurrent
                   end
                   ) as bv43_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        bookedadrchange
                   end
                   ) as bv43_bookedadrchange,
               (case Business_Group_ID
                    when @bv44 then
                        bookedadrcurrent
                   end
                   ) as bv44_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        bookedadrchange
                   end
                   ) as bv44_bookedadrchange,
               (case Business_Group_ID
                    when @bv45 then
                        bookedadrcurrent
                   end
                   ) as bv45_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        bookedadrchange
                   end
                   ) as bv45_bookedadrchange,
               (case Business_Group_ID
                    when @bv46 then
                        bookedadrcurrent
                   end
                   ) as bv46_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        bookedadrchange
                   end
                   ) as bv46_bookedadrchange,
               (case Business_Group_ID
                    when @bv47 then
                        bookedadrcurrent
                   end
                   ) as bv47_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        bookedadrchange
                   end
                   ) as bv47_bookedadrchange,
               (case Business_Group_ID
                    when @bv48 then
                        bookedadrcurrent
                   end
                   ) as bv48_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        bookedadrchange
                   end
                   ) as bv48_bookedadrchange,
               (case Business_Group_ID
                    when @bv49 then
                        bookedadrcurrent
                   end
                   ) as bv49_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        bookedadrchange
                   end
                   ) as bv49_bookedadrchange,
               (case Business_Group_ID
                    when @bv50 then
                        bookedadrcurrent
                   end
                   ) as bv50_bookedadrcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        bookedadrchange
                   end
                   ) as bv50_bookedadrchange,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedadrcurrent
                   end
                   ) as bv1_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv1 then
                        fcstedadrchange
                   end
                   ) as bv1_fcstedadrchange,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedadrcurrent
                   end
                   ) as bv2_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv2 then
                        fcstedadrchange
                   end
                   ) as bv2_fcstedadrchange,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedadrcurrent
                   end
                   ) as bv3_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv3 then
                        fcstedadrchange
                   end
                   ) as bv3_fcstedadrchange,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedadrcurrent
                   end
                   ) as bv4_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv4 then
                        fcstedadrchange
                   end
                   ) as bv4_fcstedadrchange,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedadrcurrent
                   end
                   ) as bv5_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv5 then
                        fcstedadrchange
                   end
                   ) as bv5_fcstedadrchange,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedadrcurrent
                   end
                   ) as bv6_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv6 then
                        fcstedadrchange
                   end
                   ) as bv6_fcstedadrchange,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedadrcurrent
                   end
                   ) as bv7_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv7 then
                        fcstedadrchange
                   end
                   ) as bv7_fcstedadrchange,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedadrcurrent
                   end
                   ) as bv8_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv8 then
                        fcstedadrchange
                   end
                   ) as bv8_fcstedadrchange,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedadrcurrent
                   end
                   ) as bv9_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv9 then
                        fcstedadrchange
                   end
                   ) as bv9_fcstedadrchange,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedadrcurrent
                   end
                   ) as bv10_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv10 then
                        fcstedadrchange
                   end
                   ) as bv10_fcstedadrchange,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedadrcurrent
                   end
                   ) as bv11_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv11 then
                        fcstedadrchange
                   end
                   ) as bv11_fcstedadrchange,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedadrcurrent
                   end
                   ) as bv12_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv12 then
                        fcstedadrchange
                   end
                   ) as bv12_fcstedadrchange,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedadrcurrent
                   end
                   ) as bv13_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv13 then
                        fcstedadrchange
                   end
                   ) as bv13_fcstedadrchange,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedadrcurrent
                   end
                   ) as bv14_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv14 then
                        fcstedadrchange
                   end
                   ) as bv14_fcstedadrchange,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedadrcurrent
                   end
                   ) as bv15_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv15 then
                        fcstedadrchange
                   end
                   ) as bv15_fcstedadrchange,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedadrcurrent
                   end
                   ) as bv16_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv16 then
                        fcstedadrchange
                   end
                   ) as bv16_fcstedadrchange,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedadrcurrent
                   end
                   ) as bv17_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv17 then
                        fcstedadrchange
                   end
                   ) as bv17_fcstedadrchange,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedadrcurrent
                   end
                   ) as bv18_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv18 then
                        fcstedadrchange
                   end
                   ) as bv18_fcstedadrchange,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedadrcurrent
                   end
                   ) as bv19_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv19 then
                        fcstedadrchange
                   end
                   ) as bv19_fcstedadrchange,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedadrcurrent
                   end
                   ) as bv20_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv20 then
                        fcstedadrchange
                   end
                   ) as bv20_fcstedadrchange,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedadrcurrent
                   end
                   ) as bv21_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv21 then
                        fcstedadrchange
                   end
                   ) as bv21_fcstedadrchange,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedadrcurrent
                   end
                   ) as bv22_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv22 then
                        fcstedadrchange
                   end
                   ) as bv22_fcstedadrchange,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedadrcurrent
                   end
                   ) as bv23_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv23 then
                        fcstedadrchange
                   end
                   ) as bv23_fcstedadrchange,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedadrcurrent
                   end
                   ) as bv24_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv24 then
                        fcstedadrchange
                   end
                   ) as bv24_fcstedadrchange,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedadrcurrent
                   end
                   ) as bv25_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv25 then
                        fcstedadrchange
                   end
                   ) as bv25_fcstedadrchange,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedadrcurrent
                   end
                   ) as bv26_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv26 then
                        fcstedadrchange
                   end
                   ) as bv26_fcstedadrchange,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedadrcurrent
                   end
                   ) as bv27_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv27 then
                        fcstedadrchange
                   end
                   ) as bv27_fcstedadrchange,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedadrcurrent
                   end
                   ) as bv28_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv28 then
                        fcstedadrchange
                   end
                   ) as bv28_fcstedadrchange,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedadrcurrent
                   end
                   ) as bv29_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv29 then
                        fcstedadrchange
                   end
                   ) as bv29_fcstedadrchange,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedadrcurrent
                   end
                   ) as bv30_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv30 then
                        fcstedadrchange
                   end
                   ) as bv30_fcstedadrchange,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedadrcurrent
                   end
                   ) as bv31_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv31 then
                        fcstedadrchange
                   end
                   ) as bv31_fcstedadrchange,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedadrcurrent
                   end
                   ) as bv32_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv32 then
                        fcstedadrchange
                   end
                   ) as bv32_fcstedadrchange,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedadrcurrent
                   end
                   ) as bv33_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv33 then
                        fcstedadrchange
                   end
                   ) as bv33_fcstedadrchange,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedadrcurrent
                   end
                   ) as bv34_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv34 then
                        fcstedadrchange
                   end
                   ) as bv34_fcstedadrchange,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedadrcurrent
                   end
                   ) as bv35_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv35 then
                        fcstedadrchange
                   end
                   ) as bv35_fcstedadrchange,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedadrcurrent
                   end
                   ) as bv36_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv36 then
                        fcstedadrchange
                   end
                   ) as bv36_fcstedadrchange,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedadrcurrent
                   end
                   ) as bv37_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv37 then
                        fcstedadrchange
                   end
                   ) as bv37_fcstedadrchange,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedadrcurrent
                   end
                   ) as bv38_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv38 then
                        fcstedadrchange
                   end
                   ) as bv38_fcstedadrchange,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedadrcurrent
                   end
                   ) as bv39_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv39 then
                        fcstedadrchange
                   end
                   ) as bv39_fcstedadrchange,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedadrcurrent
                   end
                   ) as bv40_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv40 then
                        fcstedadrchange
                   end
                   ) as bv40_fcstedadrchange,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedadrcurrent
                   end
                   ) as bv41_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv41 then
                        fcstedadrchange
                   end
                   ) as bv41_fcstedadrchange,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedadrcurrent
                   end
                   ) as bv42_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv42 then
                        fcstedadrchange
                   end
                   ) as bv42_fcstedadrchange,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedadrcurrent
                   end
                   ) as bv43_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv43 then
                        fcstedadrchange
                   end
                   ) as bv43_fcstedadrchange,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedadrcurrent
                   end
                   ) as bv44_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv44 then
                        fcstedadrchange
                   end
                   ) as bv44_fcstedadrchange,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedadrcurrent
                   end
                   ) as bv45_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv45 then
                        fcstedadrchange
                   end
                   ) as bv45_fcstedadrchange,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedadrcurrent
                   end
                   ) as bv46_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv46 then
                        fcstedadrchange
                   end
                   ) as bv46_fcstedadrchange,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedadrcurrent
                   end
                   ) as bv47_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv47 then
                        fcstedadrchange
                   end
                   ) as bv47_fcstedadrchange,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedadrcurrent
                   end
                   ) as bv48_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv48 then
                        fcstedadrchange
                   end
                   ) as bv48_fcstedadrchange,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedadrcurrent
                   end
                   ) as bv49_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv49 then
                        fcstedadrchange
                   end
                   ) as bv49_fcstedadrchange,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedadrcurrent
                   end
                   ) as bv50_fcstedadrcurrent,
               (case Business_Group_ID
                    when @bv50 then
                        fcstedadrchange
                   end
                   ) as bv50_fcstedadrchange,
               --Archana added for group block and group pickup-Start
               (case Business_Group_ID
                    when @bv1 then
                        block
                   end
                   ) as bv1_block,
               (case Business_Group_ID
                    when @bv1 then
                        block_pickup
                   end
                   ) as bv1_block_pickup,
               (case Business_Group_ID
                    when @bv1 then
                        block_available
                   end
                   ) as bv1_block_available,
               (case Business_Group_ID
                    when @bv2 then
                        block
                   end
                   ) as bv2_block,
               (case Business_Group_ID
                    when @bv2 then
                        block_pickup
                   end
                   ) as bv2_block_pickup,
               (case Business_Group_ID
                    when @bv2 then
                        block_available
                   end
                   ) as bv2_block_available,
               (case Business_Group_ID
                    when @bv3 then
                        block
                   end
                   ) as bv3_block,
               (case Business_Group_ID
                    when @bv3 then
                        block_pickup
                   end
                   ) as bv3_block_pickup,
               (case Business_Group_ID
                    when @bv3 then
                        block_available
                   end
                   ) as bv3_block_available,
               (case Business_Group_ID
                    when @bv4 then
                        block
                   end
                   ) as bv4_block,
               (case Business_Group_ID
                    when @bv4 then
                        block_pickup
                   end
                   ) as bv4_block_pickup,
               (case Business_Group_ID
                    when @bv4 then
                        block_available
                   end
                   ) as bv4_block_available,
               (case Business_Group_ID
                    when @bv5 then
                        block
                   end
                   ) as bv5_block,
               (case Business_Group_ID
                    when @bv5 then
                        block_pickup
                   end
                   ) as bv5_block_pickup,
               (case Business_Group_ID
                    when @bv5 then
                        block_available
                   end
                   ) as bv5_block_available,
               (case Business_Group_ID
                    when @bv6 then
                        block
                   end
                   ) as bv6_block,
               (case Business_Group_ID
                    when @bv6 then
                        block_pickup
                   end
                   ) as bv6_block_pickup,
               (case Business_Group_ID
                    when @bv6 then
                        block_available
                   end
                   ) as bv6_block_available,
               (case Business_Group_ID
                    when @bv7 then
                        block
                   end
                   ) as bv7_block,
               (case Business_Group_ID
                    when @bv7 then
                        block_pickup
                   end
                   ) as bv7_block_pickup,
               (case Business_Group_ID
                    when @bv7 then
                        block_available
                   end
                   ) as bv7_block_available,
               (case Business_Group_ID
                    when @bv8 then
                        block
                   end
                   ) as bv8_block,
               (case Business_Group_ID
                    when @bv8 then
                        block_pickup
                   end
                   ) as bv8_block_pickup,
               (case Business_Group_ID
                    when @bv8 then
                        block_available
                   end
                   ) as bv8_block_available,
               (case Business_Group_ID
                    when @bv9 then
                        block
                   end
                   ) as bv9_block,
               (case Business_Group_ID
                    when @bv9 then
                        block_pickup
                   end
                   ) as bv9_block_pickup,
               (case Business_Group_ID
                    when @bv9 then
                        block_available
                   end
                   ) as bv9_block_available,
               (case Business_Group_ID
                    when @bv10 then
                        block
                   end
                   ) as bv10_block,
               (case Business_Group_ID
                    when @bv10 then
                        block_pickup
                   end
                   ) as bv10_block_pickup,
               (case Business_Group_ID
                    when @bv10 then
                        block_available
                   end
                   ) as bv10_block_available,
               (case Business_Group_ID
                    when @bv11 then
                        block
                   end
                   ) as bv11_block,
               (case Business_Group_ID
                    when @bv11 then
                        block_pickup
                   end
                   ) as bv11_block_pickup,
               (case Business_Group_ID
                    when @bv11 then
                        block_available
                   end
                   ) as bv11_block_available,
               (case Business_Group_ID
                    when @bv12 then
                        block
                   end
                   ) as bv12_block,
               (case Business_Group_ID
                    when @bv12 then
                        block_pickup
                   end
                   ) as bv12_block_pickup,
               (case Business_Group_ID
                    when @bv12 then
                        block_available
                   end
                   ) as bv12_block_available,
               (case Business_Group_ID
                    when @bv13 then
                        block
                   end
                   ) as bv13_block,
               (case Business_Group_ID
                    when @bv13 then
                        block_pickup
                   end
                   ) as bv13_block_pickup,
               (case Business_Group_ID
                    when @bv13 then
                        block_available
                   end
                   ) as bv13_block_available,
               (case Business_Group_ID
                    when @bv14 then
                        block
                   end
                   ) as bv14_block,
               (case Business_Group_ID
                    when @bv14 then
                        block_pickup
                   end
                   ) as bv14_block_pickup,
               (case Business_Group_ID
                    when @bv14 then
                        block_available
                   end
                   ) as bv14_block_available,
               (case Business_Group_ID
                    when @bv15 then
                        block
                   end
                   ) as bv15_block,
               (case Business_Group_ID
                    when @bv15 then
                        block_pickup
                   end
                   ) as bv15_block_pickup,
               (case Business_Group_ID
                    when @bv15 then
                        block_available
                   end
                   ) as bv15_block_available,
               (case Business_Group_ID
                    when @bv16 then
                        block
                   end
                   ) as bv16_block,
               (case Business_Group_ID
                    when @bv16 then
                        block_pickup
                   end
                   ) as bv16_block_pickup,
               (case Business_Group_ID
                    when @bv16 then
                        block_available
                   end
                   ) as bv16_block_available,
               (case Business_Group_ID
                    when @bv17 then
                        block
                   end
                   ) as bv17_block,
               (case Business_Group_ID
                    when @bv17 then
                        block_pickup
                   end
                   ) as bv17_block_pickup,
               (case Business_Group_ID
                    when @bv17 then
                        block_available
                   end
                   ) as bv17_block_available,
               (case Business_Group_ID
                    when @bv18 then
                        block
                   end
                   ) as bv18_block,
               (case Business_Group_ID
                    when @bv18 then
                        block_pickup
                   end
                   ) as bv18_block_pickup,
               (case Business_Group_ID
                    when @bv18 then
                        block_available
                   end
                   ) as bv18_block_available,
               (case Business_Group_ID
                    when @bv19 then
                        block
                   end
                   ) as bv19_block,
               (case Business_Group_ID
                    when @bv19 then
                        block_pickup
                   end
                   ) as bv19_block_pickup,
               (case Business_Group_ID
                    when @bv19 then
                        block_available
                   end
                   ) as bv19_block_available,
               (case Business_Group_ID
                    when @bv20 then
                        block
                   end
                   ) as bv20_block,
               (case Business_Group_ID
                    when @bv20 then
                        block_pickup
                   end
                   ) as bv20_block_pickup,
               (case Business_Group_ID
                    when @bv20 then
                        block_available
                   end
                   ) as bv20_block_available,
               (case Business_Group_ID
                    when @bv21 then
                        block
                   end
                   ) as bv21_block,
               (case Business_Group_ID
                    when @bv21 then
                        block_pickup
                   end
                   ) as bv21_block_pickup,
               (case Business_Group_ID
                    when @bv21 then
                        block_available
                   end
                   ) as bv21_block_available,
               (case Business_Group_ID
                    when @bv22 then
                        block
                   end
                   ) as bv22_block,
               (case Business_Group_ID
                    when @bv22 then
                        block_pickup
                   end
                   ) as bv22_block_pickup,
               (case Business_Group_ID
                    when @bv22 then
                        block_available
                   end
                   ) as bv22_block_available,
               (case Business_Group_ID
                    when @bv23 then
                        block
                   end
                   ) as bv23_block,
               (case Business_Group_ID
                    when @bv23 then
                        block_pickup
                   end
                   ) as bv23_block_pickup,
               (case Business_Group_ID
                    when @bv23 then
                        block_available
                   end
                   ) as bv23_block_available,
               (case Business_Group_ID
                    when @bv24 then
                        block
                   end
                   ) as bv24_block,
               (case Business_Group_ID
                    when @bv24 then
                        block_pickup
                   end
                   ) as bv24_block_pickup,
               (case Business_Group_ID
                    when @bv24 then
                        block_available
                   end
                   ) as bv24_block_available,
               (case Business_Group_ID
                    when @bv25 then
                        block
                   end
                   ) as bv25_block,
               (case Business_Group_ID
                    when @bv25 then
                        block_pickup
                   end
                   ) as bv25_block_pickup,
               (case Business_Group_ID
                    when @bv25 then
                        block_available
                   end
                   ) as bv25_block_available,
               (case Business_Group_ID
                    when @bv26 then
                        block
                   end
                   ) as bv26_block,
               (case Business_Group_ID
                    when @bv26 then
                        block_pickup
                   end
                   ) as bv26_block_pickup,
               (case Business_Group_ID
                    when @bv26 then
                        block_available
                   end
                   ) as bv26_block_available,
               (case Business_Group_ID
                    when @bv27 then
                        block
                   end
                   ) as bv27_block,
               (case Business_Group_ID
                    when @bv27 then
                        block_pickup
                   end
                   ) as bv27_block_pickup,
               (case Business_Group_ID
                    when @bv27 then
                        block_available
                   end
                   ) as bv27_block_available,
               (case Business_Group_ID
                    when @bv28 then
                        block
                   end
                   ) as bv28_block,
               (case Business_Group_ID
                    when @bv28 then
                        block_pickup
                   end
                   ) as bv28_block_pickup,
               (case Business_Group_ID
                    when @bv28 then
                        block_available
                   end
                   ) as bv28_block_available,
               (case Business_Group_ID
                    when @bv29 then
                        block
                   end
                   ) as bv29_block,
               (case Business_Group_ID
                    when @bv29 then
                        block_pickup
                   end
                   ) as bv29_block_pickup,
               (case Business_Group_ID
                    when @bv29 then
                        block_available
                   end
                   ) as bv29_block_available,
               (case Business_Group_ID
                    when @bv30 then
                        block
                   end
                   ) as bv30_block,
               (case Business_Group_ID
                    when @bv30 then
                        block_pickup
                   end
                   ) as bv30_block_pickup,
               (case Business_Group_ID
                    when @bv30 then
                        block_available
                   end
                   ) as bv30_block_available,
               (case Business_Group_ID
                    when @bv31 then
                        block
                   end
                   ) as bv31_block,
               (case Business_Group_ID
                    when @bv31 then
                        block_pickup
                   end
                   ) as bv31_block_pickup,
               (case Business_Group_ID
                    when @bv31 then
                        block_available
                   end
                   ) as bv31_block_available,
               (case Business_Group_ID
                    when @bv32 then
                        block
                   end
                   ) as bv32_block,
               (case Business_Group_ID
                    when @bv32 then
                        block_pickup
                   end
                   ) as bv32_block_pickup,
               (case Business_Group_ID
                    when @bv32 then
                        block_available
                   end
                   ) as bv32_block_available,
               (case Business_Group_ID
                    when @bv33 then
                        block
                   end
                   ) as bv33_block,
               (case Business_Group_ID
                    when @bv33 then
                        block_pickup
                   end
                   ) as bv33_block_pickup,
               (case Business_Group_ID
                    when @bv33 then
                        block_available
                   end
                   ) as bv33_block_available,
               (case Business_Group_ID
                    when @bv34 then
                        block
                   end
                   ) as bv34_block,
               (case Business_Group_ID
                    when @bv34 then
                        block_pickup
                   end
                   ) as bv34_block_pickup,
               (case Business_Group_ID
                    when @bv34 then
                        block_available
                   end
                   ) as bv34_block_available,
               (case Business_Group_ID
                    when @bv35 then
                        block
                   end
                   ) as bv35_block,
               (case Business_Group_ID
                    when @bv35 then
                        block_pickup
                   end
                   ) as bv35_block_pickup,
               (case Business_Group_ID
                    when @bv35 then
                        block_available
                   end
                   ) as bv35_block_available,
               (case Business_Group_ID
                    when @bv36 then
                        block
                   end
                   ) as bv36_block,
               (case Business_Group_ID
                    when @bv36 then
                        block_pickup
                   end
                   ) as bv36_block_pickup,
               (case Business_Group_ID
                    when @bv36 then
                        block_available
                   end
                   ) as bv36_block_available,
               (case Business_Group_ID
                    when @bv37 then
                        block
                   end
                   ) as bv37_block,
               (case Business_Group_ID
                    when @bv37 then
                        block_pickup
                   end
                   ) as bv37_block_pickup,
               (case Business_Group_ID
                    when @bv37 then
                        block_available
                   end
                   ) as bv37_block_available,
               (case Business_Group_ID
                    when @bv38 then
                        block
                   end
                   ) as bv38_block,
               (case Business_Group_ID
                    when @bv38 then
                        block_pickup
                   end
                   ) as bv38_block_pickup,
               (case Business_Group_ID
                    when @bv38 then
                        block_available
                   end
                   ) as bv38_block_available,
               (case Business_Group_ID
                    when @bv39 then
                        block
                   end
                   ) as bv39_block,
               (case Business_Group_ID
                    when @bv39 then
                        block_pickup
                   end
                   ) as bv39_block_pickup,
               (case Business_Group_ID
                    when @bv39 then
                        block_available
                   end
                   ) as bv39_block_available,
               (case Business_Group_ID
                    when @bv40 then
                        block
                   end
                   ) as bv40_block,
               (case Business_Group_ID
                    when @bv40 then
                        block_pickup
                   end
                   ) as bv40_block_pickup,
               (case Business_Group_ID
                    when @bv40 then
                        block_available
                   end
                   ) as bv40_block_available,
               (case Business_Group_ID
                    when @bv41 then
                        block
                   end
                   ) as bv41_block,
               (case Business_Group_ID
                    when @bv41 then
                        block_pickup
                   end
                   ) as bv41_block_pickup,
               (case Business_Group_ID
                    when @bv41 then
                        block_available
                   end
                   ) as bv41_block_available,
               (case Business_Group_ID
                    when @bv42 then
                        block
                   end
                   ) as bv42_block,
               (case Business_Group_ID
                    when @bv42 then
                        block_pickup
                   end
                   ) as bv42_block_pickup,
               (case Business_Group_ID
                    when @bv42 then
                        block_available
                   end
                   ) as bv42_block_available,
               (case Business_Group_ID
                    when @bv43 then
                        block
                   end
                   ) as bv43_block,
               (case Business_Group_ID
                    when @bv43 then
                        block_pickup
                   end
                   ) as bv43_block_pickup,
               (case Business_Group_ID
                    when @bv43 then
                        block_available
                   end
                   ) as bv43_block_available,
               (case Business_Group_ID
                    when @bv44 then
                        block
                   end
                   ) as bv44_block,
               (case Business_Group_ID
                    when @bv44 then
                        block_pickup
                   end
                   ) as bv44_block_pickup,
               (case Business_Group_ID
                    when @bv44 then
                        block_available
                   end
                   ) as bv44_block_available,
               (case Business_Group_ID
                    when @bv45 then
                        block
                   end
                   ) as bv45_block,
               (case Business_Group_ID
                    when @bv45 then
                        block_pickup
                   end
                   ) as bv45_block_pickup,
               (case Business_Group_ID
                    when @bv45 then
                        block_available
                   end
                   ) as bv45_block_available,
               (case Business_Group_ID
                    when @bv46 then
                        block
                   end
                   ) as bv46_block,
               (case Business_Group_ID
                    when @bv46 then
                        block_pickup
                   end
                   ) as bv46_block_pickup,
               (case Business_Group_ID
                    when @bv46 then
                        block_available
                   end
                   ) as bv46_block_available,
               (case Business_Group_ID
                    when @bv47 then
                        block
                   end
                   ) as bv47_block,
               (case Business_Group_ID
                    when @bv47 then
                        block_pickup
                   end
                   ) as bv47_block_pickup,
               (case Business_Group_ID
                    when @bv47 then
                        block_available
                   end
                   ) as bv47_block_available,
               (case Business_Group_ID
                    when @bv48 then
                        block
                   end
                   ) as bv48_block,
               (case Business_Group_ID
                    when @bv48 then
                        block_pickup
                   end
                   ) as bv48_block_pickup,
               (case Business_Group_ID
                    when @bv48 then
                        block_available
                   end
                   ) as bv48_block_available,
               (case Business_Group_ID
                    when @bv49 then
                        block
                   end
                   ) as bv49_block,
               (case Business_Group_ID
                    when @bv49 then
                        block_pickup
                   end
                   ) as bv49_block_pickup,
               (case Business_Group_ID
                    when @bv49 then
                        block_available
                   end
                   ) as bv49_block_available,
               (case Business_Group_ID
                    when @bv50 then
                        block
                   end
                   ) as bv50_block,
               (case Business_Group_ID
                    when @bv50 then
                        block_pickup
                   end
                   ) as bv50_block_pickup,
               (case Business_Group_ID
                    when @bv50 then
                        block_available
                   end
                   ) as bv50_block_available

               --Archana added for group block and group pickup-End

        from
            (
                select base.occupancy_dt,
                       base.Business_Group_ID,
                       base.Business_Group_Name,
                       datename(dw, base.occupancy_dt) as dow,
                       isnull(a.rooms_sold, 0.0) as roomsoldcurrent,
                       isnull(a.room_revenue, 0.0) as bookedroomrevenuecurrent,
                       isnull(a.adr, 0.0) as bookedadrcurrent,
                       isnull(b.occupancy_forecast_current, 0.0) as occfcstcurrent,
                       isnull(b.fcsted_room_revenue_current, 0.0) as fcstedroomrevenuecurrent,
                       isnull(b.estimated_adr_current, 0.0) as fcstedadrcurrent,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.rooms_sold, 0.0) - isnull(c.rooms_sold, isnull(a.rooms_sold, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.rooms_sold, 0.0) - isnull(c.rooms_sold, 0.0)) as numeric(19, 2))
                           end as roomssoldchange,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and d.occupancy_nbr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(b.occupancy_forecast_current, 0.0)
                                   - isnull(d.occupancy_nbr, isnull(b.occupancy_forecast_current, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(b.occupancy_forecast_current, 0.0) - isnull(d.occupancy_nbr, 0.0)) as numeric(19, 2))
                           end as occfcstchange,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.room_revenue, 0.0) - isnull(c.room_revenue, isnull(a.room_revenue, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.room_revenue, 0.0) - isnull(c.room_revenue, 0.0)) as numeric(19, 2))
                           end as bookedroomrevenuechange,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and d.revenue is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(b.fcsted_room_revenue_current, 0.0)
                                   - isnull(d.revenue, isnull(b.fcsted_room_revenue_current, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(b.fcsted_room_revenue_current, 0.0) - isnull(d.revenue, 0.0)) as numeric(19, 2))
                           end as fcstedroomrevenuechange,
                       case
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(a.adr, 0.0) - isnull(c.adr, isnull(a.adr, 0.0))) as numeric(19, 2))
                           else
                               cast((isnull(a.adr, 0.0) - isnull(c.adr, 0.0)) as numeric(19, 2))
                           end as bookedadrchange,
                       case
                           when @rolling_business_dt = 'LAST_OPTIMIZATION'
                               and d.adr is NULL then
                               0
                           when base.occupancy_dt <= @business_dt then
                               cast((isnull(b.estimated_adr_current, 0.0)
                                   - isnull(d.adr, isnull(b.estimated_adr_current, 0.0))
                                   ) as numeric(19, 2))
                           else
                               cast((isnull(b.estimated_adr_current, 0.0) - isnull(d.adr, 0.0)) as numeric(19, 2))
                           end as fcstedadrchange,
                       groupBlock.block,
                       groupBlock.block_pickup,
                       groupBlock.block_available
                from #calendare_temp base
                         left join #activity a
                                   on base.occupancy_dt = a.occupancy_dt
                                       and base.Business_Group_ID = a.Business_Group_ID
                         left join #forecast b
                                   on base.occupancy_dt = b.occupancy_dt
                                       and base.Business_Group_ID = b.Business_Group_ID
                         left join #activity_asof c
                                   on base.occupancy_dt = c.occupancy_dt
                                       and base.Business_Group_ID = c.Business_Group_ID
                         left join #forecast_asof d
                                   on base.occupancy_dt = d.occupancy_dt
                                       and base.Business_Group_ID = d.Business_Group_ID
                         left join #group_block groupBlock
                                   on a.occupancy_dt = groupBlock.occupancy_dt
                                       and a.Business_Group_ID = groupBlock.Business_Group_ID
            ) data
    ) data2
group by occupancy_dt,
         dow

select *
from #change_report_bv_comparative_view
order by occupancy_dt

drop table if exists #tempBV
drop table if exists #calendare_temp
drop table if exists #activity
drop table if exists #forecast
drop table if exists #activity_asof
drop table if exists #forecast_asof
drop table if exists #group_block
drop table if exists #change_report_bv_comparative_view

END