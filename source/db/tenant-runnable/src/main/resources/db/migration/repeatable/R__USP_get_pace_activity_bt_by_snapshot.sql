DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_activity_bt_by_snapshot]
/****** Object:  Stored Procedure [dbo].[usp_get_pace_activity_bt_by_snapshot]    Script Date: 01/23/2021 9:16:27 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

Stored Procedure Name: usp_get_pace_activity_bt_by_snapshot

Input Parameters : 
	@property_id --> property Id Associated with a property (e.g.,'BOSCO' id FROM the property table is 12)
	@start_date --> start date FROM which you need pace ('2011-07-01')
	@end_date --> end date till which you need pace ('2011-07-31')
	@past_start_date --> maximum past date till which we want pace FROM 
	@business_type_id --> business Type for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)
	
Ouput Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_activity_bt_by_snapshot 10027,1,'2012-09-20', '2012-10-30','2012-07-17', '0'

Purpose: The purpose of this stored procedure is to extract pace of Activity for a given property and business type by given date range.

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/23/2021		Anil				Borgude					Initial Version
06/30/2021      Shilpa              Shaha                   Fix for BHASK-1594 BAD Screen : Pace Data tab | 0 Diet
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/

CREATE PROCEDURE [dbo].[usp_get_pace_activity_bt_by_snapshot]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@business_type_ids nvarchar(10),
    @exclude_comp_rooms varchar(5)
)		
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Businessdate date 
	SET @Businessdate = DATEADD(DAY, -1, cast((SELECT  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) AS Date)) --> extract caughtup date for a property
	
		
	DECLARE @temp_end_date AS date = @end_date
	if(@Businessdate>@end_date)
	BEGIN
		SET @temp_end_date = @Businessdate
	END
	
	DECLARE @Business_Types table (Business_Type_ID int, Business_Type_Name varchar(100))
	INSERT @Business_Types SELECT Value, BT.Business_Type_Name FROM varcharToInt(@business_type_ids,',')
	INNER JOIN Business_Type BT on BT.Business_Type_ID = Value

	CREATE TABLE #temp_bt_activity
	(
		Business_Type_ID int,
		occupancy_dt date,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
	
	INSERT INTO #temp_bt_activity
	SELECT msd.Business_Type_ID, Occupancy_DT, sum(Rooms_Sold)Rooms_Sold, SUM(Room_Revenue) as Room_Revenue FROM 
	(
		SELECT Occupancy_DT,mkt.Mkt_Seg_ID,SUM(case when at.isComponentRoom = 'Y' then 0 else Rooms_Sold END) AS Rooms_Sold,
        SUM(case when at.isComponentRoom = 'Y' then 0.0 else Room_Revenue END) as Room_Revenue FROM Mkt_Accom_Activity mkt
        INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = mkt.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@exclude_comp_rooms, ','))
		INNER JOIN Accom_Type at on
				mkt.Accom_Type_ID = at.Accom_Type_ID and mkt.Property_ID = at.Property_ID 
		WHERE Occupancy_DT BETWEEN @start_date and @temp_end_date and mkt.Property_ID=@property_id
		GROUP BY Occupancy_DT,mkt.Mkt_Seg_ID
	) tba 
		INNER JOIN Mkt_Seg_Details msd on msd.Mkt_Seg_ID = tba.Mkt_Seg_ID
		INNER JOIN @Business_Types bt on bt.Business_Type_ID = msd.Business_Type_ID
		GROUP BY Occupancy_DT, msd.Business_Type_ID
	
	CREATE TABLE #temp_bt_activity_2
	(
		Business_Type_ID int,
		occupancy_dt date,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)

	Insert into #temp_bt_activity_2
	select Business_Type_ID, Occupancy_DT, Rooms_Sold, Room_Revenue from #temp_bt_activity
	WHERE Occupancy_DT BETWEEN @start_date AND @end_date

	CREATE table #PACE_Mkt_Activity (
		Business_Day_End_DT date,
		Business_Type_ID int,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
		)

	INSERT into #PACE_Mkt_Activity 
	SELECT pma.Business_Day_END_DT,msd.Business_Type_ID,SUM(pma.Rooms_Sold) Rooms_Sold,SUM(pma.Room_Revenue) as Room_Revenue 
	FROM PACE_Mkt_Activity pma
    INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = pma.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@exclude_comp_rooms, ','))
	INNER JOIN Mkt_Seg_Details msd on msd.Mkt_Seg_ID = ms.Mkt_Seg_ID
	INNER JOIN @Business_Types bt ON bt.Business_Type_ID = msd.Business_Type_ID
	WHERE pma.Occupancy_DT BETWEEN @start_date AND @end_date
		AND pma.Business_Day_END_DT BETWEEN @past_start_date AND @Businessdate
	group by pma.Business_Day_END_DT,msd.Business_Type_ID

	CREATE TABLE #pace_activity_bt
	( 
		Business_Day_End_DT date,
		business_type_id int,
		Business_Type_Name varchar(10),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
	INSERT INTO #pace_activity_bt
	SELECT Business_Day_End_DT, BT.Business_Type_ID,BT.Business_Type_Name, SUM(ISNULL(Rooms_Sold,0)) AS Rooms_Sold, SUM(ISNULL(Room_Revenue,0.0)) as Room_Revenue
	FROM ( 
			SELECT Business_Day_End_DT,Business_Type_ID, Rooms_Sold, Room_Revenue FROM #PACE_Mkt_Activity pma
		UNION ALL
		SELECT a.occupancy_dt,tba.Business_Type_ID, tba.Rooms_Sold, tba.Room_Revenue  
		FROM #temp_bt_activity_2 tba JOIN #temp_bt_activity a
			ON tba.Business_Type_ID = a.Business_Type_ID
			AND tba.occupancy_dt < a.occupancy_dt  
			WHERE a.occupancy_dt BETWEEN @start_date AND @Businessdate
			) AS base 
		 JOIN Business_Type BT on BT.Business_Type_ID = base.Business_Type_ID
		GROUP BY Business_Day_End_DT,BT.Business_Type_ID,BT.Business_Type_Name
	    ORDER BY Business_Day_End_DT,BT.Business_Type_ID,BT.Business_Type_Name

/** Retriving BDE Snapshot data from the FileMetadata to identify and map 0th records  **/
		SELECT ISNULL(pabt.Business_Day_End_DT, DATEADD(day, - 1, base.SnapShot_DT)) Business_Day_End_DT,
		ISNULL(pabt.Business_Type_ID, base.Business_Type_ID) Business_Type_ID, 
		ISNULL(pabt.Business_Type_Name, base.Business_Type_Name) Business_Type_Name, 
		pabt.Rooms_Sold AS Rooms_Sold,
        CAST(pabt.Room_Revenue as numeric(19, 2)) AS Room_Revenue,
               CASE
                   WHEN (pabt.Rooms_Sold IS NULL OR pabt.Rooms_Sold = 0) THEN NULL
                   WHEN (pabt.Room_Revenue IS NULL) THEN NULL
                   ELSE CAST(pabt.Room_Revenue / pabt.Rooms_Sold as numeric(19, 2)) END AS onBooks_ADR
		from
		(
				select @property_id Property_ID,CAST(SnapShot_DT as date) SnapShot_DT, max(fm.File_Metadata_ID) as File_Metadata_ID, bts.Business_Type_ID, bts.Business_Type_Name from File_Metadata fm
				INNER JOIN @Business_Types bts on Property_ID=@property_id
				where IsBDE = 1 and Record_Type_ID = 3 and SnapShot_DT> @past_start_date and Process_Status_ID=13
				group by SnapShot_DT,Property_ID, bts.Business_Type_ID, bts.Business_Type_Name
			 ) base
			 FULL OUTER JOIN  #pace_activity_bt pabt on pabt.Business_Day_End_DT = DATEADD(day, -1, base.SnapShot_DT) and base.Business_Type_ID = pabt.business_type_id
			  
		ORDER BY Business_Day_End_DT ASC

		Drop table #temp_bt_activity
		Drop table #temp_bt_activity_2
		Drop table #PACE_Mkt_Activity
		Drop table #pace_activity_bt
END
GO