DROP PROCEDURE IF EXISTS [dbo].[usp_fs_booking_sub_block_code_mapping]
GO

CREATE PROCEDURE [dbo].[usp_fs_booking_sub_block_code_mapping]
	@fsBookingMappingStartDt DATE,
    @grpMasterMappingStartDt DATE
AS
BEGIN
	set nocount on;

    select distinct fb.fs_booking_id
    into #mapped_fs_booking_id
    from FS_Booking fb
    inner join FS_Booking_Sub_Block_Code fbsbc
    on fb.FS_Booking_ID = fbsbc.FS_Booking_Id and fb.Block_Code = fbsbc.Sub_Block_Code
    where fb.Last_Updated_DTTM >= @fsBookingMappingStartDt and
          exists(select top 1 Group_Code from Group_Master where Group_Code = fb.Block_Code);

    create nonclustered index mapped_fs_booking_id_idx on #mapped_fs_booking_id(fs_booking_id);

    select ufbsbc.fs_booking_id, ufbsbc.sub_block_code
    into #group_code_sub_block_code_mapping
    from (
            select gm.Group_ID, gm.Group_Code
            from Group_Master gm
            left join Pace_Group_Master pgm
            ON gm.group_id = pgm.group_id
            where   pgm.Business_Day_End_DT is null or
                    pgm.Business_Day_End_DT >= @grpMasterMappingStartDt
            group by gm.Group_ID, gm.Group_Code
         ) fgm
             inner join
         (
            select distinct fbsbc.fs_booking_id, fbsbc.sub_block_code
            from FS_Booking fb
            inner join fs_booking_sub_block_code fbsbc
            on fbsbc.fs_booking_id = fb.FS_Booking_ID
            where	fb.Last_Updated_DTTM >= @fsBookingMappingStartDt and
                    not exists (select top 1 * from #mapped_fs_booking_id mfbi where fb.FS_Booking_ID = mfbi.FS_Booking_ID)
         ) ufbsbc
         on fgm.group_code = ufbsbc.sub_block_code;

    select sbcm.fs_booking_id, sbcm.sub_block_code
    into #sub_block_code_mapping
    from (SELECT
              gcsbcm.fs_booking_id,
              gcsbcm.sub_block_code,
              COUNT(*) OVER (PARTITION BY gcsbcm.fs_booking_id) AS fs_booking_count
          FROM #group_code_sub_block_code_mapping gcsbcm) sbcm
    where sbcm.fs_booking_count = 1;

    update fb
    set block_code = sbcm.sub_block_code
    from fs_booking fb
    inner join #sub_block_code_mapping sbcm
    on fb.fs_booking_id = sbcm.fs_booking_id;

    drop table if exists #mapped_fs_booking_id;
    drop table if exists #group_code_sub_block_code_mapping;
    drop table if exists #sub_block_code_mapping;
END;
