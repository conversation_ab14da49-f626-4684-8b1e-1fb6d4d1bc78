DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_activity_ms_by_snapshot]
/****** Object:  Stored Procedure [dbo].[usp_get_pace_activity_ms_by_snapshot]    Script date: 1/23/2021 1:02:23 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Stored Procedure Name: usp_get_pace_activity_ms_by_snapshot

Input Parameters : 
	@property_id --> property Id Associated with a property (e.g.,'XNAES' id FROM the property table is 010027)
	@start_date --> start date FROM which you need pace ('2020-05-20')
	@end_date --> end date till which you need pace ('2020-05-24')
	@past_start_date --> maximum past date till which we want pace FROM 
	@Mkt_Seg_IDs --> market segment ids for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)

Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_activity_ms_by_snapshot 10027,'2020-05-20', '2020-05-24','2020-05-23','1,2,3','0'

Purpose: The purpose of this Procedure is to extract pace of activity for a given property AND given date range based on market segments

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/23/2021		Anil				Borgude					Initial Version
06/30/2021      Shilpa              Shaha                   Fix for BHASK-1594 BAD Screen : Pace Data tab | 0 Diet
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_get_pace_activity_ms_by_snapshot]
(
	@property_id int,
	@start_date date,
	@end_date date,
	@past_start_date date,
	@Mkt_Seg_IDs nvarchar(4000),
    @exclude_comp_rooms varchar(5)
)		
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @Businessdate date 
	SET @Businessdate = dateADD(DAY, -1, CAST((SELECT dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) AS date)) --> extract caughtup date for a property
	
	DECLARE @temp_end_date AS date = @end_date
	if(@Businessdate>@end_date)
	BEGIN
		SET @temp_end_date = @Businessdate
	END

	DECLARE @Mkt_Segs table (Mkt_Seg_ID int, Mkt_Seg_Name varchar(100) )
	INSERT @Mkt_Segs
	    SELECT Value, ms.Mkt_Seg_Name
	    FROM varcharToInt(@Mkt_Seg_IDs,',')
	    INNER JOIN Mkt_Seg ms on ms.Mkt_Seg_ID = value AND Status_ID = 1 AND ms.Exclude_CompHouse_Data_Display IN (SELECT value FROM varcharToInt(@exclude_comp_rooms, ','))


	CREATE TABLE #temp_mkt_activity
	(
		Mkt_Seg_ID int,
		occupancy_dt date,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
	
	insert into #temp_mkt_activity
	SELECT mkt.Mkt_Seg_ID, mkt.Occupancy_DT,SUM(case when AT.isComponentRoom = 'Y' then 0 else Rooms_Sold END) AS Rooms_Sold,
           SUM(case when AT.isComponentRoom = 'Y' then 0.0 else Room_Revenue END) AS Room_Revenue
	FROM Mkt_Accom_Activity mkt 
	INNER JOIN Accom_Type AT ON
				mkt.Accom_Type_ID = AT.Accom_Type_ID AND mkt.Property_ID = AT.Property_ID
	INNER JOIN @Mkt_Segs ms ON ms.Mkt_Seg_ID = mkt.Mkt_Seg_ID
	WHERE Occupancy_DT BETWEEN @start_date AND @temp_end_date AND mkt.Property_ID=@property_id
	group by mkt.Mkt_Seg_ID, mkt.Occupancy_DT

	
	CREATE TABLE #temp_mkt_activity_2
	(
		Mkt_Seg_ID int,
		occupancy_dt date,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
	)
	
	INSERT into #temp_mkt_activity_2
	SELECT Mkt_Seg_ID,Occupancy_DT,Rooms_Sold,Room_Revenue FROM #temp_mkt_activity mkt 
	WHERE Occupancy_DT BETWEEN @start_date AND @end_date

	CREATE table #PACE_Mkt_Activity (
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
		)

	INSERT into #PACE_Mkt_Activity 
	SELECT pma.Business_Day_END_DT,pma.Mkt_Seg_ID,SUM(pma.Rooms_Sold) Rooms_Sold,SUM(pma.Room_Revenue) Room_Revenue 
	FROM PACE_Mkt_Activity pma
	INNER JOIN @Mkt_Segs ms ON ms.Mkt_Seg_ID = pma.Mkt_Seg_ID	
	WHERE pma.Occupancy_DT BETWEEN @start_date AND @end_date
		AND pma.Business_Day_END_DT BETWEEN @past_start_date AND @Businessdate
	group by pma.Business_Day_END_DT,pma.Mkt_Seg_ID

/** Calculating Rooms Sold for the given market segments from the PACE_Mkt_Activity **/	
	create table #pace_activity_ms (
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2)
		)
	INSERT into #pace_activity_ms
	SELECT Business_Day_End_DT, MS.Mkt_Seg_ID, MS.Mkt_Seg_Name, SUM(ISNULL(Rooms_Sold,0)) AS Rooms_Sold, SUM(ISNULL(Room_Revenue,0.0)) AS Room_Revenue
	FROM ( 
		SELECT Business_Day_END_DT,Mkt_Seg_ID,Rooms_Sold,Room_Revenue FROM #PACE_Mkt_Activity	
		UNION ALL
		SELECT a.occupancy_dt as Business_Day_END_DT, a.Mkt_Seg_ID, sum(tma.Rooms_Sold), sum(tma.Room_Revenue)
		FROM #temp_mkt_activity_2 tma JOIN #temp_mkt_activity a 
				ON tma.Mkt_Seg_ID = a.Mkt_Seg_ID	and tma.occupancy_dt < a.occupancy_dt 
		WHERE a.Occupancy_DT  BETWEEN @start_date AND @Businessdate 
		GROUP BY a.occupancy_dt, a.Mkt_Seg_ID
									 
		) AS c 
		JOIN Mkt_Seg MS ON c.Mkt_Seg_ID=MS.Mkt_Seg_ID
		
    GROUP BY Business_Day_END_DT, MS.Mkt_Seg_ID,MS.Mkt_Seg_Name
	ORDER BY Business_Day_END_DT,MS.Mkt_Seg_ID,MS.Mkt_Seg_Name

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	       SELECT ISNULL(pams.Business_Day_End_DT, DATEADD(day, - 1, base.SnapShot_DT)) Business_Day_End_DT,
		   ISNULL(pams.Mkt_Seg_ID, base.Mkt_Seg_ID) Mkt_Seg_ID, 
		   ISNULL(pams.Mkt_Seg_Name,base.Mkt_Seg_Name) Mkt_Seg_Name, 
		   pams.Rooms_Sold AS Rooms_Sold,
           CAST(pams.Room_Revenue as numeric(19, 2)) AS Room_Revenue,
                  CASE
                      WHEN (pams.Rooms_Sold IS NULL OR pams.Rooms_Sold = 0) THEN NULL
                      WHEN (pams.Room_Revenue IS NULL) THEN NULL
                      ELSE CAST(pams.Room_Revenue / pams.Rooms_Sold as numeric(19, 2)) END AS onBooks_ADR
              from
              ( 
                     select @property_id Property_ID,CAST(SnapShot_DT as date) SnapShot_DT, max(fm.File_Metadata_ID) as File_Metadata_ID, ms.Mkt_Seg_ID, ms.mkt_seg_name from File_Metadata  fm 
					 INNER JOIN @Mkt_Segs ms on Property_ID=@property_id
                     where IsBDE = 1 and Record_Type_ID = 3 and SnapShot_DT> @past_start_date and Process_Status_ID=13
					 group by SnapShot_DT,Property_ID, ms.Mkt_Seg_ID, ms.Mkt_Seg_Name
              ) base
              FULL OUTER JOIN #pace_activity_ms pams on pams.Business_Day_End_DT = DATEADD(day, -1, base.SnapShot_DT) and base.Mkt_Seg_ID =pams.Mkt_Seg_ID

              ORDER BY Business_Day_End_DT

		Drop table #temp_mkt_activity
		Drop table #temp_mkt_activity_2
		Drop table #PACE_Mkt_Activity
		Drop table #pace_activity_ms

END
GO