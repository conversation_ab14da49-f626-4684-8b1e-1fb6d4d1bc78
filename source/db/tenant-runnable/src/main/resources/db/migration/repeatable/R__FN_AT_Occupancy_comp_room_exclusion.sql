drop function if exists [dbo].[FN_AT_Occupancy_comp_room_exclusion]
GO
/****** Object:  UserDefinedFunction [dbo].[FN_AT_Occupancy_comp_room_exclusion]    Script Date: 12/15/2021 1:28:47 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*************************************************************************************

SQL Function Name: FN_AT_Occupancy_comp_room_exclusion

Input Parameters : 
	@caughtUpDate --> caughtup date of property
	@MktExcludeCompFlag --> wheather or not to remove comp room data (0 = comp data removed / 0,1 = all data included)
	
Ouput Parameter : N/A

Execution: this is just an example
	SELECT * FROM dbo.FN_AT_Occupancy_comp_room_exclusion '2017-10-10', '0,1'  

Purpose: The purpose of this function is to get the occupancy forecast data at room type level. 

Assumptions : NA
		 
Release Update:
Release_Dt		First_Name			Last_Name				   Release Comments
----------	----------------	-------------------		-------------------------------
12/15/2021		Shrey			Vegda					 Added parameter to exclude comp rooms    
***************************************************************************************/

CREATE FUNCTION [dbo].[FN_AT_Occupancy_comp_room_exclusion] (@caughtUpDate Date, @MktExcludeCompFlag VARCHAR(5)) 
RETURNS TABLE
AS 
RETURN
SELECT aa.Accom_Activity_ID, occ.Occupancy_DT, occ.Property_ID, 
	'Forecast' as Forecast_Actual,
	at.Accom_Class_ID, at.Accom_Type_ID,
	aa.Accom_Capacity as Accom_Capacity, 
	aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other as OOO,
	aa.Accom_Capacity - aa.Rooms_Not_Avail_Maint - aa.Rooms_Not_Avail_Other as rooms_available,
	sum(occ.Occupancy_NBR) as occupancy_nbr,
	Occupancy_percent = case (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other))
		when 0 then 0
		else 
			sum(occ.Occupancy_NBR) / (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other)) * 100  
		end,
	sum(revenue) as revenue,
	ADR = case (sum(occupancy_nbr)) when 0 then 0 else 
		sum(revenue) / sum(occupancy_nbr)
		end,
	RevPAR = case (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other) ) 
	when 0 then 0 else sum(revenue) / (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other)) end
FROM Occupancy_FCST AS occ 
	INNER JOIN Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID 
	INNER JOIN Accom_Activity AS aa ON occ.Property_ID = aa.Property_ID 
		AND occ.Occupancy_DT = aa.Occupancy_DT 	
		AND occ.Accom_Type_ID = aa.Accom_Type_ID
	INNER JOIN Mkt_Seg mkt ON mkt.Mkt_Seg_ID = occ.MKT_SEG_ID AND mkt.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag, ','))

WHERE aa.Occupancy_DT >= @caughtUpDate		
GROUP BY aa.Accom_Activity_ID, occ.Occupancy_DT, occ.Property_ID, at.Accom_Class_ID, at.accom_type_id, 
aa.Accom_Capacity, aa.Rooms_Not_Avail_Maint, aa.Rooms_Not_Avail_Other
UNION
SELECT aa.Accom_Activity_ID, aa.Occupancy_DT, aa.Property_ID, 
	'Actual' as Forecast_Actual,
	at.Accom_Class_ID, at.Accom_Type_ID,
	aa.Accom_Capacity as Accom_Capacity, 
	aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other as OOO,
	aa.Accom_Capacity - aa.Rooms_Not_Avail_Maint - aa.Rooms_Not_Avail_Other as rooms_available,
	aa.rooms_sold as occupancy_nbr,
	Occupancy_percent = case ((aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other))) when 0 then 0 else 
		aa.rooms_sold / (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other)) * 100 end,
	room_revenue as revenue,
	ADR = case (aa.rooms_sold) when 0 then 0 else 
		room_revenue / aa.rooms_sold end,
	RevPAR = case ((aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other))) when 0 then 0 else
		room_revenue / (aa.Accom_Capacity - (aa.Rooms_Not_Avail_Maint + aa.Rooms_Not_Avail_Other)) end
FROM (

	select aa.Accom_Activity_ID, aa.Occupancy_DT, aa.Property_ID,aa.Accom_Type_ID,
		aa.Accom_Capacity, aa.Rooms_Not_Avail_Maint, aa.Rooms_Not_Avail_Other,
		CASE
                     WHEN @MktExcludeCompFlag = '0'
                         THEN CAST(aa.Rooms_Sold - ISNULL(maa.Rooms_Sold, 0.0) as numeric(8,2))
                     ELSE
                         aa.Rooms_Sold
                     END AS Rooms_Sold,
     				CASE
                     WHEN @MktExcludeCompFlag = '0'
                         THEN aa.Room_Revenue - ISNULL(maa.Room_Revenue, 0.0)
                     ELSE
                         aa.Room_Revenue
                     END AS  Room_Revenue
					 
	 from Accom_Activity aa
		LEFT JOIN (
				SELECT mact.Occupancy_DT
					,mact.Accom_Type_ID
					,sum(mact.Rooms_Sold) AS Rooms_Sold
					,sum(mact.Room_Revenue) AS Room_Revenue
				FROM Mkt_Accom_Activity mact
				INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = mact.Mkt_Seg_ID
					AND ms.Exclude_CompHouse_Data_Display IN (1)
				where  mact.Occupancy_DT < @caughtUpDate
				GROUP BY mact.Occupancy_DT, mact.Accom_Type_ID
				) AS maa ON maa.Occupancy_DT = aa.Occupancy_DT and maa.Accom_Type_ID = aa.Accom_Type_ID
	) AS aa
	INNER JOIN Accom_Type AS at ON aa.Accom_Type_ID = at.Accom_Type_ID 
WHERE aa.Occupancy_DT < @caughtUpDate
