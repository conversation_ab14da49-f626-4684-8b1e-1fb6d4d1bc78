DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_data_bv_by_snapshot]
GO
/****** Object:  StoredProcedure [dbo].[usp_get_pace_data_bv_by_snapshot]    Script Date: 12/30/2020 6:53:37 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Stored Procedure Name: usp_get_pace_data_bv_by_snapshot

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> start date from which you need pace ('2020-05-20')
	@end_date --> end date till which you need pace ('2020-05-24')
	@past_start_date --> maximum past date till which we want pace from 
	@business_group_ids --> business view for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)

Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_data_bv_by_snapshot 10027,'2020-05-20', '2020-05-24','2020-05-23','1,2,3','0'

Purpose: The purpose of this Procedure is to extract pace of forecast and occupancy for a given property and given date range based on business view

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/04/2020		Anil				Borgude					Initial Version
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_get_pace_data_bv_by_snapshot] 
( 
	@property_id INT, 
	@start_date DATE, 
	@end_date DATE, 
	@past_start_date DATE, 
	@business_group_ids NVARCHAR(1000),
    @exclude_comp_rooms varchar(5)
)
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @business_view_Ids TABLE (bg_id INT)

	INSERT INTO @business_view_Ids
	SELECT *
	FROM varcharToInt(@business_group_ids, ',')

    DECLARE @Mkt_Segs table (Mkt_Seg_ID int)
    INSERT @Mkt_Segs
        SELECT ms.Mkt_Seg_ID
        FROM Mkt_Seg ms
        WHERE ms.Property_ID = @property_id AND ms.Status_ID = 1 AND ms.Exclude_CompHouse_Data_Display IN (SELECT value FROM varcharToInt(@exclude_comp_rooms, ','))

    DECLARE @bv_Id INT
	CREATE TABLE #business_view_mkt_seg (view_id INT, view_name VARCHAR(100), mkt_id INT)

	INSERT INTO #business_view_mkt_seg
		SELECT a.Business_Group_ID, a.Business_Group_Name, b.Mkt_Seg_Id 
		FROM Business_Group a 
		INNER JOIN @business_view_Ids bvi on bvi.bg_id = a.Business_Group_ID AND bvi.bg_id > 0
		INNER JOIN Mkt_Seg_Business_Group b on a.Business_Group_ID=b.Business_Group_ID
	    INNER JOIN @Mkt_Segs ms ON ms.Mkt_Seg_ID = b.Mkt_Seg_Id
		GROUP BY a.Business_Group_ID, Business_Group_Name,b.Mkt_Seg_Id
	
	INSERT INTO #business_view_mkt_seg
		SELECT -1, 'unassigned', Mkt_Seg_ID
		FROM @Mkt_Segs, @business_view_Ids bvi
	    WHERE Mkt_Seg_ID NOT IN (select Mkt_Seg_ID from Mkt_Seg_Business_Group)
		    AND bvi.bg_id = -1

	DECLARE @mkt_seg_ids NVARCHAR(4000)

	SELECT @mkt_seg_ids = STUFF((SELECT DISTINCT ', ' + CAST(mkt_id as varchar(100))
           FROM #business_view_mkt_seg b
           FOR XML PATH('')), 1, 2, '')

/** Getting Rooms Sold count from the Market Segment activity procedure **/
	CREATE table #pace_activity_ms (
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2),
        onBooks_ADR numeric(19,5)
	)
	insert into #pace_activity_ms EXECUTE dbo.usp_get_pace_activity_ms_by_snapshot @property_id,@start_date, @end_date,@past_start_date,@Mkt_Seg_IDs,@exclude_comp_rooms

/** Getting Occupancy NBR count from the Market Segment occupancy forecast procedure **/
	CREATE table #pace_occupancy_fcst(
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Occupancy_NBR numeric(8,2)
	)

	insert into #pace_occupancy_fcst EXECUTE dbo.usp_get_pace_occupancy_fcst_ms_by_snapshot @property_id,@start_date, @end_date,@past_start_date,@Mkt_Seg_IDs,@exclude_comp_rooms

/** Grouping Rooms Sold and Occupancy NBR values from the above tables **/
	create table #pace_combine(
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2),
        onBooks_ADR numeric(19,5),
		Occupancy_NBR numeric(8,2)
	)

	insert into #pace_combine
		SELECT activity.Business_Day_End_DT, activity.Mkt_Seg_ID, activity.Mkt_Seg_Name, activity.Rooms_Sold, activity.Room_Revenue,activity.onBooks_ADR, fcst.Occupancy_NBR
	FROM
		#pace_activity_ms activity
	LEFT JOIN
		#pace_occupancy_fcst fcst ON activity.Business_Day_End_DT=fcst.Business_Day_End_DT
	AND activity.Mkt_Seg_ID=fcst.Mkt_Seg_ID
        ORDER BY activity.Business_Day_End_DT, activity.Mkt_Seg_ID, activity.Mkt_Seg_Name

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	SELECT pacedata.Business_Day_End_DT, bv.view_id AS Business_Group_ID, bv.view_name AS Business_Group_Name, SUM(pacedata.Rooms_Sold) AS Rooms_Sold, CAST(SUM(pacedata.Room_Revenue) as numeric(19, 2)) AS Room_Revenue,
           CASE
               WHEN (SUM(pacedata.Rooms_Sold) IS NULL OR SUM(pacedata.Rooms_Sold) = 0) THEN NULL
               WHEN (SUM(pacedata.Room_Revenue) IS NULL) THEN NULL
               ELSE CAST(SUM(pacedata.Room_Revenue) / SUM(pacedata.Rooms_Sold) as numeric(19, 2)) END AS onBooks_ADR,
	       SUM(pacedata.Occupancy_NBR) AS Occupancy_NBR
	FROM (
			SELECT Business_Day_End_DT, Mkt_Seg_ID, Rooms_Sold, Room_Revenue, Occupancy_NBR
            FROM #pace_combine
          ) pacedata
	LEFT JOIN (
            SELECT view_id, view_name, mkt_id
            FROM #business_view_mkt_seg
           ) bv ON pacedata.Mkt_Seg_ID = bv.mkt_id

            GROUP BY pacedata.Business_Day_End_DT, bv.view_id, bv.view_name
            ORDER BY pacedata.Business_Day_End_DT, bv.view_id, bv.view_name

	Drop table #business_view_mkt_seg
	Drop table #pace_activity_ms
	Drop table #pace_occupancy_fcst
	Drop table #pace_combine
			
END
GO