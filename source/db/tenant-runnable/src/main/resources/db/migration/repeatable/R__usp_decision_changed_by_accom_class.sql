DROP PROCEDURE IF EXISTS [dbo].[usp_decision_changed_by_accom_class]

GO
/****** Object:  StoredProcedure [dbo].[usp_decision_changed_by_accom_class]    Script Date: 10/17/2022 5:46:17 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

Create procedure [dbo].[usp_decision_changed_by_accom_class]
(
    @inp_accom_class_id int,
    @startDate date,
    @endDate date,
    @productId int,
    @decisionTypeIds nvarchar(10),
    @propertyId int
)
as
begin

    drop table if exists #CP_Pace_Decision_Bar_Output
    drop table if exists #Decision_IDs_forTypes
    drop table if exists #Latest
    drop table if exists #previous

    SELECT a.CP_Pace_Decision_Bar_Output_ID,
           a.Arrival_DT,
           a.Accom_Type_ID,
           a.Final_BAR,
           a.Decision_ID,
           a.LOS
    INTO #CP_Pace_Decision_Bar_Output
    FROM CP_Pace_Decision_Bar_Output a
    WHERE a.Accom_Type_ID =
    (
        select Accom_Type_ID
        FROM CP_Cfg_AC
        WHERE Accom_Class_ID = @inp_accom_class_id
    )
          and a.Arrival_DT
          between @startDate and @endDate
          and a.Product_ID = @productId

    SELECT Decision_ID
    into #Decision_IDs_forTypes
    FROM Decision
    WHERE Decision_Type_ID in (
                                  SELECT Value FROM varcharToInt(@decisionTypeIds, ',')
                              )

    SELECT a.CP_Pace_Decision_Bar_Output_ID,
           a.Arrival_DT,
           a.Accom_Type_ID,
           a.Final_BAR,
           a.Decision_ID,
           a.LOS
    INTO #Latest
    FROM #CP_Pace_Decision_Bar_Output a
    WHERE a.Decision_ID =
    (
        select MAX(Decision_ID) from #Decision_IDs_forTypes
    )

    select b.Final_BAR,
           b.Arrival_DT
    into #previous
    FROM #CP_Pace_Decision_Bar_Output b
    WHERE b.Decision_ID =
    (
        select MAX(cpdbo.Decision_ID)
        FROM #CP_Pace_Decision_Bar_Output cpdbo
            join #Decision_IDs_forTypes d
                on d.Decision_ID = cpdbo.Decision_ID
        WHERE cpdbo.Decision_ID <>
        (
            select MAX(cpdbo.Decision_ID)
            FROM #CP_Pace_Decision_Bar_Output cpdbo
                join #Decision_IDs_forTypes d
                    on d.Decision_ID = cpdbo.Decision_ID
        )
    )

    SELECT latest.CP_Pace_Decision_Bar_Output_ID,
           @propertyId as property_id,
           latest.Arrival_DT,
           latest.Accom_Type_ID,
           latest.Final_BAR as latestFinalBar,
           latest.Decision_ID,
           previous.Final_BAR as previousFinalBar,
           latest.Final_BAR - previous.Final_BAR as diff,
           latest.LOS
    FROM #Latest latest
        JOIN #previous previous
            on latest.Arrival_DT = previous.Arrival_DT
               and latest.Final_BAR - previous.Final_BAR <> 0

end