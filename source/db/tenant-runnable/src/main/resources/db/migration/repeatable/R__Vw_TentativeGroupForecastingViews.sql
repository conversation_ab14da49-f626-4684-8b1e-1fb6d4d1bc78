/*************************************************************************************

View Name: Vw_Group_Master

  Execution:
-------------------------------------------
	select * from dbo.Vw_Group_Master

Currently, Analytics reads Groups data from Group_Master table. This table contains DEFINITE and CANCELLED Groups.
In addition DEFINITE and CANCELLED groups analytics need TENTATIVE Groups data.
The TENTATIVE groups data is present in FS_Booking table. This View is created to fetch required data from both
the tables Group_Master and FS_Booking. Both the tables can have same data. These two tables can be joined with
Group_Code column from Group_Master and Block_Code column from FS_Booking table.

  Assumptions
	1. View will fetch 5 years of history data and available future data.
	2. FS_Booking table do  not have below columns. Hence, these columns will be blank for groups present in FS_Booking
        but not in Group_Master
		- Master_Group_ID
		- Master_Group_Code
		- Group_Type_Code
		- Pickup_Type_Code
		- Booking_type
	3. Consider only Tentative, Definite and Cancelled group
    4. Include Groups data with with blank/null block_code in FS_Booking.
    5. FS_Booking can have multiple records for the same group. We have decided to ignore all such common records from FS_Booking
       in all views until we conclude on the criteria to match the records
    6. Consider Group_Code from Opera_Group_Block_Code table instead of Group_Code from Group_Master while
       comparing Group_Code with Block_Code in FS_Booking table .
	7. TGF views have data from both PMS as well as S&C. Currently, we have mapped FS_Booking_Id (FS_Booking) and Group_ID (Group_Master)
	   with Group_ID in Vw_Group_Master. So, we may have the same Group_ID in Vw_Group_Master view for two different groups.
	   To avoid this scenario, we have decided to make the group ids negative for group data present in FS_Booking tables.
    8. The TGF Views will includes Group Block data for which Guest Room Type to Accom type mappings are configured.
       This is to avoid failures in forecasting / optimization steps of BDE / CDP with null accom type ids.

  Group Status Mappings:
    Is_Final	Is_Inv_Deducted	Is_Prospect	    Status
        1	            1	        0	        DEFINITE
        0	            1	        0	        DEFINITE
        1	            0	        0	        CANCELLED

    Is_Final	Is_Inv_Deducted	Is_Tentative	Status
        0	            0	        1	        TENTATIVE

***************************************************************************************/
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_FS_Block_Codes_To_Ignore')
    DROP VIEW [DBO].[Vw_FS_Block_Codes_To_Ignore]
GO
CREATE VIEW Vw_FS_Block_Codes_To_Ignore AS
(
SELECT Block_Code
FROM FS_Booking
WHERE (LEN(ISNULL(Block_Code, '')) > 0)
  AND Block_Code IN (select distinct Group_Code from Group_Master)
GROUP BY Block_Code
HAVING COUNT(*) > 1
    );

GO
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_FS_G3_Common_Group_Mapping')
    DROP VIEW [DBO].[Vw_FS_G3_Common_Group_Mapping]
GO
CREATE VIEW Vw_FS_G3_Common_Group_Mapping AS
(
SELECT fsb.FS_Booking_ID AS FS_Booking_ID,
       gm.Group_ID       AS Group_ID
FROM (SELECT gm.Group_ID,
             ISNULL(ogbc.Group_Code, gm.Group_Code) AS Group_Code
      FROM [dbo].[Group_Master] gm
               LEFT JOIN [dbo].[Opera_Group_Block_Code] ogbc ON ogbc.Group_Id = gm.Group_ID
     ) AS gm
         INNER JOIN
     (SELECT fs_booking.FS_Booking_ID, fs_booking.Block_Code
      FROM [dbo].[FS_Booking] AS fs_booking
      WHERE (LEN(ISNULL(Block_Code, '')) > 0)
        AND Block_Code NOT IN (SELECT Block_Code FROM Vw_FS_Block_Codes_To_Ignore)
     ) AS fsb ON fsb.Block_Code = gm.Group_Code);

GO
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_Group_Master')
    DROP VIEW [DBO].[Vw_Group_Master]
GO
CREATE VIEW Vw_Group_Master AS
SELECT Group_ID,
       Property_ID,
       Group_Code,
       Group_Name,
       Group_Description,
       Master_Group_ID,
       Master_Group_Code,
       Group_Status_Code,
       Group_Type_Code,
       Mkt_Seg_ID,
       Start_DT,
       End_DT,
       Booking_DT,
       Pickup_Type_Code,
       Cancel_DT,
       Booking_type,
       Sales_Person,
       Cut_Off_date,
       Cut_Off_days
FROM [dbo].[Group_Master]
union
SELECT 0 - fsbinn.FS_Booking_ID                   AS Group_ID,
       fsbinn.Property_ID                         AS Property_ID,
       fsbinn.Block_Code                          AS Group_Code,
       fsbinn.Block_Name                          AS Group_Name,
       fsbinn.Block_Name                          AS Group_Description,
       NULL                                       AS Master_Group_ID,
       NULL                                       AS Master_Group_Code,
       CASE
           WHEN (Is_Inv_Deducted = 1 AND Is_Prospect = 0) THEN 'DEFINITE'
           WHEN (Is_Final = 0 AND Is_Inv_Deducted = 0 AND Is_Tentative = 1) THEN 'TENTATIVE'
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0) THEN 'CANCELLED'
           ELSE
               'PROSPECT' END                     AS Group_Status_Code,
       NULL                                       AS Group_Type_Code,
       mktseg.Mkt_Seg_ID                          AS Mkt_Seg_ID,
       fsbinn.Arrival_DT                          AS Start_DT,
       fsbinn.Departure_DT                        AS End_DT,
       CONVERT(DATE, fsbinn.SC_Created_DTTM)      AS Booking_DT,
       NULL                                       AS Pickup_Type_Code,
       CASE
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0)
               THEN CONVERT(DATE, fsbinn.SC_Last_Updated_DTTM)
           ELSE NULL END                     AS Cancel_DT,
       NULL                                       AS Booking_type,
       NULL                                       AS Sales_Person,
       fsbinn.Cutoff_DT                           AS Cut_Off_date,
       fsbinn.Cutoff_Days                         AS Cut_Off_days
FROM FS_Booking fsbinn
         LEFT JOIN [dbo].[FS_Cfg_Status] fscfgstatus ON fscfgstatus.FS_Cfg_Status_ID = fsbinn.FS_Cfg_Status_ID
         LEFT JOIN [dbo].[FS_Cfg_Mkt_Seg] fscfgmktseg ON fsbinn.FS_Cfg_Mkt_Seg_ID = fscfgmktseg.FS_Cfg_Mkt_Seg_ID
         LEFT JOIN [dbo].[Mkt_Seg] mktseg ON fscfgmktseg.Mkt_Seg_ID = mktseg.Mkt_Seg_ID
WHERE FS_Booking_ID not in (SELECT FS_Booking_ID FROM Vw_FS_G3_Common_Group_Mapping)
  and Block_Code not in (SELECT Block_Code FROM Vw_FS_Block_Codes_To_Ignore)
  and ((fscfgstatus.Is_Inv_Deducted = 1 AND fscfgstatus.Is_Prospect = 0)
    OR (fscfgstatus.Is_Final = 0 AND fscfgstatus.Is_Inv_Deducted = 0 AND fscfgstatus.Is_Tentative = 1)
    OR (fscfgstatus.Is_Final = 1 AND fscfgstatus.Is_Inv_Deducted = 0 AND fscfgstatus.Is_Prospect = 0))
  AND fsbinn.Arrival_DT > DATEADD(YEAR, -5, GETDATE())
GO

--Vw_Pace_Group_Master
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_Pace_Group_Master')
    DROP VIEW [DBO].[Vw_Pace_Group_Master]
GO
CREATE VIEW Vw_Pace_Group_Master AS
SELECT pgm.Group_ID,
       pgm.Business_Day_End_DT,
       pgm.Group_Code,
       pgm.Group_Name,
       pgm.Group_Description,
       pgm.Master_Group_ID,
       pgm.Master_Group_Code,
       pgm.Group_Status_Code,
       pgm.Group_Type_Code,
       pgm.Mkt_Seg_ID,
       pgm.Start_DT,
       pgm.End_DT,
       pgm.Booking_DT,
       pgm.Pickup_Type_Code,
       pgm.Cancel_DT,
       pgm.Booking_type,
       pgm.Sales_Person,
       pgm.Cut_Off_date,
       pgm.Cut_Off_days,
       pgm.File_Metadata_ID
FROM Pace_Group_Master pgm
UNION
SELECT 0 - fsb.FS_Booking_ID                   AS Group_ID,
       CONVERT(DATE, fsbp.Capture_DTTM)        AS Business_Day_End_DT,
       fsb.Block_Code                          AS Group_Code,
       fsb.Block_Name                          AS Group_Name,
       fsb.Block_Name                          AS Group_Description,
       NULL                                    AS Master_Group_ID,
       NULL                                    AS Master_Group_Code,
       CASE
           WHEN (Is_Inv_Deducted = 1 AND Is_Prospect = 0) THEN 'DEFINITE'
           WHEN (Is_Final = 0 AND Is_Inv_Deducted = 0 AND Is_Tentative = 1) THEN 'TENTATIVE'
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0) THEN 'CANCELLED'
           ELSE
               'PROSPECT' END                  AS Group_Status_Code,
       NULL                                    AS Group_Type_Code,
       mktseg.Mkt_Seg_ID                       AS Mkt_Seg_ID,
       fsb.Arrival_DT                          AS Start_DT,
       fsb.Departure_DT                        AS End_DT,
       CONVERT(DATE, fsb.SC_Created_DTTM)      AS Booking_DT,
       NULL                                    AS Pickup_Type_Code,
       CASE
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0)
               THEN CONVERT(DATE, fsb.SC_Last_Updated_DTTM)
           ELSE NULL END                  AS Cancel_DT,
       NULL                                    AS Booking_type,
       NULL                                    AS Sales_Person,
       fsb.Cutoff_DT                           AS Cut_Off_date,
       fsb.Cutoff_Days                         AS Cut_Off_days,
       NULL                                    AS File_Metadata_ID
FROM [dbo].[FS_Booking_Pace] fsbp
         INNER JOIN [dbo].[FS_Booking] fsb
                    ON fsbp.FS_Booking_ID = fsb.FS_Booking_ID
         LEFT JOIN [dbo].[FS_Cfg_Status] fscfgstatus ON fscfgstatus.FS_Cfg_Status_ID = fsbp.FS_Cfg_Status_ID
         LEFT JOIN [dbo].[FS_Cfg_Mkt_Seg] fscfgmktseg ON fsb.FS_Cfg_Mkt_Seg_ID = fscfgmktseg.FS_Cfg_Mkt_Seg_ID
         LEFT JOIN [dbo].[Mkt_Seg] mktseg ON fscfgmktseg.Mkt_Seg_ID = mktseg.Mkt_Seg_ID
WHERE EXISTS(SELECT Group_ID
             FROM VW_Group_Master vgm
             WHERE vgm.group_id = -fsbp.FS_Booking_ID)
UNION
SELECT common.Group_ID                         AS Group_ID,
       CONVERT(DATE, fsbp.Capture_DTTM)        AS Business_Day_End_DT,
       fsb.Block_Code                          AS Group_Code,
       fsb.Block_Name                          AS Group_Name,
       fsb.Block_Name                          AS Group_Description,
       NULL                                    AS Master_Group_ID,
       NULL                                    AS Master_Group_Code,
       CASE
           WHEN (Is_Inv_Deducted = 1 AND Is_Prospect = 0) THEN 'DEFINITE'
           WHEN (Is_Final = 0 AND Is_Inv_Deducted = 0 AND Is_Tentative = 1) THEN 'TENTATIVE'
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0) THEN 'CANCELLED'
           ELSE
               'PROSPECT' END                  AS Group_Status_Code,
       NULL                                    AS Group_Type_Code,
       mktseg.Mkt_Seg_ID                       AS Mkt_Seg_ID,
       fsb.Arrival_DT                          AS Start_DT,
       fsb.Departure_DT                        AS End_DT,
       CONVERT(DATE, fsb.SC_Created_DTTM)      AS Booking_DT,
       NULL                                    AS Pickup_Type_Code,
       CASE
           WHEN (Is_Final = 1 AND Is_Inv_Deducted = 0 AND Is_Prospect = 0)
               THEN CONVERT(DATE, fsb.SC_Last_Updated_DTTM)
           ELSE NULL END                  AS Cancel_DT,
       NULL                                    AS Booking_type,
       NULL                                    AS Sales_Person,
       fsb.Cutoff_DT                           AS Cut_Off_date,
       fsb.Cutoff_Days                         AS Cut_Off_days,
       NULL                                    AS File_Metadata_ID
FROM [dbo].[FS_Booking_Pace] fsbp
         INNER JOIN [dbo].[FS_Booking] fsb
                    ON fsbp.FS_Booking_ID = fsb.FS_Booking_ID
         INNER JOIN Vw_FS_G3_Common_Group_Mapping common ON common.FS_Booking_ID = fsbp.FS_Booking_ID
         LEFT JOIN [dbo].[FS_Cfg_Status] fscfgstatus ON fscfgstatus.FS_Cfg_Status_ID = fsbp.FS_Cfg_Status_ID
         LEFT JOIN [dbo].[FS_Cfg_Mkt_Seg] fscfgmktseg ON fsb.FS_Cfg_Mkt_Seg_ID = fscfgmktseg.FS_Cfg_Mkt_Seg_ID
         LEFT JOIN [dbo].[Mkt_Seg] mktseg ON fscfgmktseg.Mkt_Seg_ID = mktseg.Mkt_Seg_ID
GO

-- Vw_Group_Block
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_Group_Block')
    DROP VIEW [DBO].[Vw_Group_Block]
Go
CREATE VIEW Vw_Group_Block AS
(
    SELECT gb.Group_Block_ID,
           gb.Group_ID,
           gb.Occupancy_DT,
           gb.Accom_Type_ID,
           gb.Blocks,
           gb.Pickup,
           gb.Original_Blocks,
           gb.rate,
           gb.IsPeakNight
    FROM Group_Block gb
    UNION
    SELECT 0 - FS_Booking_Guest_Room_ID,
           0 - fsbgr.FS_Booking_ID,
           Occupancy_DT,
           at.Accom_Type_ID,
           ISNULL(Blocked_Single_Rooms, 0) + ISNULL(Blocked_Double_Rooms, 0) + ISNULL(Blocked_Triple_Rooms, 0) +
           ISNULL(Blocked_Quad_Rooms, 0) Blocks,
           Pickup_Rooms_Total,
           Contracted_Rooms_Total,
           Guest_Room_Rate_Single,
           null                          IsPeakNight
    FROM FS_Booking_Guest_Room fsbgr
             INNER JOIN FS_Booking fsb ON fsbgr.FS_Booking_ID = fsb.FS_Booking_ID
             LEFT JOIN FS_Cfg_Guest_Room_Category fscfggrc
                       ON fscfggrc.FS_Cfg_Guest_Room_Category_ID = fsbgr.FS_Cfg_Guest_Room_Category_ID
             LEFT JOIN Accom_Type at ON at.Accom_Type_ID = fscfggrc.Accom_Type_ID
    WHERE EXISTS(SELECT Group_ID FROM VW_Group_Master vgm WHERE vgm.group_id = -fsb.FS_Booking_ID) AND at.Accom_Type_ID IS NOT NULL)

UNION
SELECT 0 - FS_Booking_Guest_Room_ID,
       common.Group_ID,
       Occupancy_DT,
       at.Accom_Type_ID,
       ISNULL(Blocked_Single_Rooms, 0) + ISNULL(Blocked_Double_Rooms, 0) + ISNULL(Blocked_Triple_Rooms, 0) +
       ISNULL(Blocked_Quad_Rooms, 0) Blocks,
       Pickup_Rooms_Total,
       Contracted_Rooms_Total,
       Guest_Room_Rate_Single,
       null                          IsPeakNight
FROM FS_Booking_Guest_Room fsbgr
         INNER JOIN FS_Booking fsb ON fsbgr.FS_Booking_ID = fsb.FS_Booking_ID
         INNER JOIN Vw_FS_G3_Common_Group_Mapping common ON common.FS_Booking_ID = fsbgr.FS_Booking_ID
         LEFT JOIN FS_Cfg_Guest_Room_Category fscfggrc
                   ON fscfggrc.FS_Cfg_Guest_Room_Category_ID = fsbgr.FS_Cfg_Guest_Room_Category_ID
         LEFT JOIN Accom_Type at ON at.Accom_Type_ID = fscfggrc.Accom_Type_ID
WHERE at.Accom_Type_ID IS NOT NULL
go

--Vw_Pace_Group_Block
IF EXISTS(SELECT *
          FROM INFORMATION_SCHEMA.VIEWS
          WHERE TABLE_NAME = 'Vw_Pace_Group_Block')
    DROP VIEW [DBO].[Vw_Pace_Group_Block]
GO
CREATE VIEW vw_pace_group_block
AS
(SELECT pgb.group_id,
        pgb.occupancy_dt,
        pgb.accom_type_id,
        pgb.business_day_end_dt,
        pgb.blocks,
        pgb.pickup,
        pgb.original_blocks,
        pgb.rate
FROM   pace_group_block pgb
UNION
SELECT group_id,
       occupancy_dt,
       accom_type_id,
       business_day_end_dt,
       blocks,
       pickup,
       original_blocks,
       rate
FROM   (SELECT group_id,
               occupancy_dt,
               accom_type_id,
               business_day_end_dt,
               Sum(blocks)                 AS blocks,
               Sum(pickup_rooms_total)     AS pickup,
               Sum(contracted_rooms_total) AS original_blocks,
               Max(guest_room_rate_single) rate
        FROM   (SELECT 0 - fsbgrp.fs_booking_id           AS GROUP_ID,
                       fsbgrp.occupancy_dt,
                       at.accom_type_id,
                       CONVERT(DATE, fsbgrp.capture_dttm) AS
                                                             BUSINESS_DAY_END_dt
                        ,
                       Isnull(blocked_single_rooms, 0)
                           + Isnull(blocked_double_rooms, 0)
                           + Isnull(blocked_triple_rooms, 0)
                           + Isnull(blocked_quad_rooms, 0)    Blocks,
                       fsbgrp.pickup_rooms_total,
                       fsbgrp.contracted_rooms_total,
                       fsbgrp.guest_room_rate_single
                FROM   fs_booking_guest_room_pace fsbgrp
                           INNER JOIN fs_booking fsb
                                      ON fsbgrp.fs_booking_id = fsb.fs_booking_id
                           LEFT JOIN fs_cfg_guest_room_category fscfggrc
                                     ON fscfggrc.fs_cfg_guest_room_category_id =
                                        fsbgrp.fs_cfg_guest_room_category_id
                           LEFT JOIN accom_type at
                                     ON at.accom_type_id = fscfggrc.accom_type_id
                WHERE  EXISTS(SELECT group_id
                              FROM   vw_group_master vgm
                              WHERE  vgm.group_id = -fsb.fs_booking_id)
                  AND at.accom_type_id IS NOT NULL) AS b
        GROUP  BY group_id,
                  occupancy_dt,
                  accom_type_id,
                  business_day_end_dt) c
UNION
SELECT group_id,
       occupancy_dt,
       accom_type_id,
       business_day_end_dt,
       Sum(blocks)                 AS blocks,
       Sum(pickup_rooms_total)     AS pickup,
       Sum(contracted_rooms_total) AS original_blocks,
       Max(guest_room_rate_single) rate
FROM   (SELECT common.group_id,
               fsbgrp.occupancy_dt,
               at.accom_type_id,
               CONVERT(DATE, fsbgrp.capture_dttm) AS business_day_end_DT,
               Isnull(blocked_single_rooms, 0)
                   + Isnull(blocked_double_rooms, 0)
                   + Isnull(blocked_triple_rooms, 0)
                   + Isnull(blocked_quad_rooms, 0)    Blocks,
               fsbgrp.pickup_rooms_total,
               fsbgrp.contracted_rooms_total,
               fsbgrp.guest_room_rate_single
        FROM   fs_booking_guest_room_pace fsbgrp
                   INNER JOIN fs_booking fsb
                              ON fsbgrp.fs_booking_id = fsb.fs_booking_id
                   INNER JOIN vw_fs_g3_common_group_mapping common
                              ON common.fs_booking_id = fsbgrp.fs_booking_id
                   LEFT JOIN fs_cfg_guest_room_category fscfggrc
                             ON fscfggrc.fs_cfg_guest_room_category_id =
                                fsbgrp.fs_cfg_guest_room_category_id
                   LEFT JOIN accom_type at
                             ON at.accom_type_id = fscfggrc.accom_type_id
                   LEFT JOIN pace_group_block pgb
                             ON common.group_id = pgb.group_id
                                 AND fsbgrp.occupancy_dt = pgb.occupancy_dt
                                 AND CONVERT(DATE, fsbgrp.capture_dttm) =
                                     pgb.business_day_end_dt
                                 AND pgb.accom_type_id = at.accom_type_id
        WHERE  at.accom_type_id IS NOT NULL
          AND pgb.group_id IS NULL) A
GROUP  BY group_id,
          occupancy_dt,
          accom_type_id,
          business_day_end_dt);