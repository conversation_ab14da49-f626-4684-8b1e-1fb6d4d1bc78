DROP PROCEDURE IF EXISTS [dbo].[usp_Decision_Qualified_Fplos_Insert]
    GO

DROP TYPE IF EXISTS [dbo].[Decision_Qualified_Fplos_Batch]
    GO

CREATE TYPE dbo.Decision_Qualified_Fplos_Batch AS TABLE
    (
    [Decision_ID] bigint,
    [Property_ID] int,
    [Accom_Type_ID] int,
    [Rate_Qualified_ID] int,
    [Arrival_DT] date,
    [FPLOS] nvarchar(MAX),
    [CreateDate_DTTM] datetime
    );
    GO

CREATE PROCEDURE usp_Decision_Qualified_Fplos_Insert
    @Decision_Qualified_Fplos_Batch Decision_Qualified_Fplos_Batch READONLY
AS
BEGIN
MERGE INTO [dbo].[Decision_Qualified_FPLOS] AS Target
    USING @Decision_Qualified_Fplos_Batch AS Source
    ON (Target.[Accom_Type_ID] = Source.[Accom_Type_ID]
    AND Target.[Rate_Qualified_ID] = Source.[Rate_Qualified_ID]
    AND Target.[Arrival_DT] = Source.[Arrival_DT]
    AND Target.[Property_ID] = Source.[Property_ID])
    WHEN MATCHED AND Target.[FPLOS] <> Source.[FPLOS] THEN
UPDATE SET Target.[Decision_ID] = Source.[Decision_ID],
    Target.[FPLOS] = Source.[FPLOS],
    Target.[CreateDate_DTTM] = Source.[CreateDate_DTTM]
    WHEN NOT MATCHED THEN
INSERT ([Decision_ID], [Property_ID], [Accom_Type_ID], [Rate_Qualified_ID], [Arrival_DT], [FPLOS], [CreateDate_DTTM])
VALUES (Source.[Decision_ID], Source.[Property_ID], Source.[Accom_Type_ID], Source.[Rate_Qualified_ID], Source.[Arrival_DT], Source.[FPLOS], Source.[CreateDate_DTTM]);
END
GO
