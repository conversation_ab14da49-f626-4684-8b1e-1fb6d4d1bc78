
CREATE OR ALTER PROCEDURE [dbo].[usp_get_overbooking_property_level]
    @caughtUpDate DATETIME2,
    @propertyId INT,
    @startDate DATETIME2,
    @endDate DATETIME2
AS
BEGIN
    SET NOCOUNT ON;

DROP TABLE IF EXISTS #ForecastData
CREATE TABLE #ForecastData (
       Occupancy_Forecast_ID INT,
       Occupancy_DT DATE,
       Property_ID INT,
       OOO INT,
       Occupancy_Percent NUMERIC(18,4),
       Total_Accom_Capacity INT,
       Occupancy_NBR NUMERIC(18,4),
       Revenue NUMERIC(18,2),
       ADR NUMERIC(18,2),
       REVPAR NUMERIC(18,2),
       Forecast_Actual VARCHAR(10)
);

INSERT INTO #ForecastData
    EXEC dbo.USP_Occupancy_Forecast_Zero_Diet_In_Range
        @caughtUpDate = @caughtUpDate,
        @use_physical_capacity = 0,
        @endDate = @endDate;

SELECT
    forecast.Occupancy_DT,
    CASE activity.Total_Accom_Capacity
        WHEN 0 THEN 0
        ELSE ROUND(
                (decision.Overbooking_Decision +
                 (activity.Total_Accom_Capacity - (activity.Rooms_Not_Avail_Maint + activity.Rooms_Not_Avail_Other))
                    ) / activity.Total_Accom_Capacity * 100, 2)
        END AS Overbooking_Decision_Percentage,
    decision.Overbooking_Decision,
    activity.Total_Accom_Capacity
FROM #ForecastData forecast
         INNER JOIN dbo.Total_Activity activity
                    ON forecast.Occupancy_DT = activity.Occupancy_DT
                        AND forecast.Property_ID = activity.Property_ID
         INNER JOIN dbo.Decision_Ovrbk_Property decision
                    ON forecast.Property_ID = decision.Property_ID
                        AND forecast.Occupancy_DT = decision.Occupancy_DT
         INNER JOIN dbo.Wash_Property_FCST wash
                    ON forecast.Property_ID = wash.Property_ID
                        AND forecast.Occupancy_DT = wash.Occupancy_DT
WHERE forecast.Property_ID = @propertyId
  AND forecast.Occupancy_DT >= @startDate
  AND forecast.Occupancy_DT <= @endDate
ORDER BY forecast.Occupancy_DT;

DROP TABLE #ForecastData;
END