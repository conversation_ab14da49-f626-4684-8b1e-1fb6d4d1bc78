DROP PROCEDURE IF EXISTS [dbo].[usp_get_pace_activity_bv_by_snapshot]
/****** Object:  StoredProcedure [dbo].[usp_get_pace_activity_bv_by_snapshot]    Script Date: 1/23/2021 10:27:01 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

StoredProcedure Name: [dbo].[usp_get_pace_activity_bv_by_snapshot]

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> start date from which you need pace ('2020-05-20')
	@end_date --> end date till which you need pace ('2020-05-24')
	@past_start_date --> maximum past date till which we want pace from 
	@business_group_ids --> business groups for which we need pace
    @exclude_comp_rooms --> whether or not to remove comp room data ('0' = comp data removed / '0,1' = all data included)
	
Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_get_pace_activity_bv_by_snapshot 24,'2020-05-20', '2020-05-24','2020-05-23','1,2,3,4,5','0'

Purpose: The purpose of this StoredProcedure is to extract pace of Activity for a given property, business view and date range.

Assumptions : NA
		 
Author: Anil

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/23/2021		Anil				Borgude					Initial Version
12/24/2021      Rajratna            Awale                   Adds Comp Room Exclusion on Pace Data Tab
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_get_pace_activity_bv_by_snapshot] 
(
	@property_id INT, 
	@start_date DATE, 
	@end_date DATE, 
	@past_start_date DATE, 
	@business_group_ids NVARCHAR(1000),
    @exclude_comp_rooms varchar(5)
)
AS
BEGIN
	SET NOCOUNT ON
	DECLARE @business_view_Ids TABLE (bg_id INT)

	INSERT INTO @business_view_Ids
	SELECT * FROM varcharToInt(@business_group_ids, ',')

    DECLARE @Mkt_Segs table (Mkt_Seg_ID int)
    INSERT @Mkt_Segs
        SELECT ms.Mkt_Seg_ID
        FROM Mkt_Seg ms
        WHERE ms.Property_ID = @property_id AND ms.Status_ID = 1 AND ms.Exclude_CompHouse_Data_Display IN (SELECT value FROM varcharToInt(@exclude_comp_rooms, ','))

    DECLARE @bv_id INT
	CREATE TABLE #business_view_mkt_seg (view_id INT, view_name VARCHAR(100), mkt_id INT)

	INSERT INTO #business_view_mkt_seg
		SELECT a.Business_Group_ID, a.Business_Group_Name, b.Mkt_Seg_Id 
		FROM Business_Group a 
		INNER JOIN @business_view_Ids bvi on bvi.bg_id = a.Business_Group_ID AND bvi.bg_id > 0
		INNER JOIN Mkt_Seg_Business_Group b on a.Business_Group_ID=b.Business_Group_ID
        INNER JOIN @Mkt_Segs ms ON ms.Mkt_Seg_ID = b.Mkt_Seg_Id
		GROUP BY a.Business_Group_ID, a.Business_Group_Name, b.Mkt_Seg_Id
	
	INSERT INTO #business_view_mkt_seg
		SELECT -1, 'unassigned' , Mkt_Seg_ID 
		FROM @Mkt_Segs, @business_view_Ids bvi
	    WHERE Mkt_Seg_ID NOT IN (select Mkt_Seg_ID from Mkt_Seg_Business_Group)
		    AND bvi.bg_id = -1

	DECLARE @mkt_seg_ids NVARCHAR(4000)

	SELECT @mkt_seg_ids = STUFF((SELECT DISTINCT ', ' + CAST(mkt_id as varchar(100))
			FROM #business_view_mkt_seg b
			FOR XML PATH('')), 1, 2, '')

/** Getting Rooms Sold count from the Market Segment activity procedure and inserting into temp table **/
	CREATE table #pace_activity_bv (
		Business_Day_End_DT date,
		Mkt_Seg_ID int,
		Mkt_Seg_Name varchar(50),
		Rooms_Sold numeric(8,0),
        Room_Revenue numeric(19,2),
        onBooks_ADR numeric(19,5)
	)
	insert into #pace_activity_bv EXECUTE dbo.usp_get_pace_activity_ms_by_snapshot @property_id,@start_date, @end_date,@past_start_date,@Mkt_Seg_IDs,@exclude_comp_rooms

/** Retriving BDE Snapshot data from the FileMetadata to identify and map zeroth records  **/
	SELECT activity.Business_Day_End_DT, bv.view_id AS Business_Group_ID, bv.view_name AS Business_Group_Name,
	SUM(ISNULL(activity.Rooms_Sold, 0)) AS Rooms_Sold, CAST(SUM(ISNULL(activity.Room_Revenue, 0.0)) as numeric(19, 2)) AS Room_Revenue,
           CASE
               WHEN SUM(ISNULL(activity.Rooms_Sold, 0)) = 0 THEN 0
               ELSE CAST(SUM(ISNULL(activity.Room_Revenue, 0.0)) / SUM(ISNULL(activity.Rooms_Sold, 0)) as numeric(19, 2)) END AS onBooks_ADR
	FROM (
			SELECT Business_Day_End_DT, Mkt_Seg_ID, Rooms_Sold, Room_Revenue
            FROM #pace_activity_bv
          ) activity
	LEFT JOIN (
            SELECT view_id, view_name, mkt_id
            FROM #business_view_mkt_seg
           ) bv ON activity.Mkt_Seg_ID = bv.mkt_id
	GROUP BY activity.Business_Day_End_DT, bv.view_id, bv.view_name
	ORDER BY activity.Business_Day_End_DT, bv.view_id, bv.view_name

	Drop table #business_view_mkt_seg
	Drop table #pace_activity_bv

END
GO