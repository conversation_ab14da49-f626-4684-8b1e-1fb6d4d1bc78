if exists(select *
          from sys.objects
          where object_id = object_id(N'[dbo].[usp_revenue_total_by_business_views]'))
    drop procedure [dbo].[usp_revenue_total_by_business_views]

GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp_revenue_total_by_business_views]
(
	@startDate DATE,
	@endDate DATE,
	@caughtUpDate DATE,
	@excludeCompRooms VARCHAR(5),
    @businessViewIds VARCHAR(MAX)
)
as
begin

select a.Business_Group_ID, a.Mkt_Seg_ID
into #bg_ms
from (
         select ISNULL(msbg.Business_Group_ID, -1) Business_Group_ID, ms.Mkt_Seg_ID
         from Mkt_Seg ms
                  left join Mkt_Seg_Business_Group msbg on msbg.Mkt_Seg_ID = ms.Mkt_Seg_ID
         where ms.Status_ID = 1 and ms.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@excludeCompRooms, ','))
     ) as a
where a.Business_Group_ID in (select value from varcharToInt(@businessViewIds, ','))


SELECT ROUND(SUM(CASE WHEN maa.Occupancy_DT >= @caughtUpDate THEN	occ.revenue ELSE maa.Room_Revenue  END), 0) as Revenue
FROM Mkt_Accom_Activity AS maa
         INNER JOIN Accom_Type AS at ON maa.Accom_Type_ID = at.Accom_Type_ID
    and at.Status_ID=1
    and at.Display_Status_ID = 1
    and at.System_Default=0
    and at.isComponentRoom = 'N'
    INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID
    and ac.Status_ID=1
    and ac.System_Default=0
    INNER JOIN #bg_ms bgms on bgms.Mkt_Seg_ID = maa.MKT_SEG_ID
    INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON bgms.MKT_SEG_ID = msfg.Mkt_Seg_ID
    and msfg.Status_ID=1
    LEFT JOIN Occupancy_FCST AS occ ON occ.Occupancy_DT = maa.Occupancy_DT
    and occ.Accom_Type_ID = maa.Accom_Type_ID
    and occ.MKT_SEG_ID = maa.Mkt_Seg_ID
    and occ.Occupancy_DT >= @caughtUpDate
WHERE maa.Occupancy_DT between @startDate and @endDate

drop table #bg_ms

end
GO