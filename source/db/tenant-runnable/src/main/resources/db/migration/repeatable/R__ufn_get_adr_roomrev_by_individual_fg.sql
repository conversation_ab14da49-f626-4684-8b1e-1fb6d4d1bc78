if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_adr_roomrev_by_individual_fg]'))
drop function [ufn_get_adr_roomrev_by_individual_fg]
GO
/*************************************************************************************

Function Name: ufn_get_adr_roomrev_by_individual_fg

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@forecast_group_id --> forecast_group_id  associated with a market_segment for a given property
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
Ouput Parameter : NA

Execution: this is just an example
	Example 1: Report at single forecast group level
	-----------------------------------------------
	select * from dbo.ufn_get_adr_roomrev_by_individual_fg (18,'25','2011-07-01','2011-07-31')
	
	Example 2: Report at multiple forecast group levels
	--------------------------------------------------
	select * from dbo.ufn_get_adr_roomrev_by_fg (18,'25,28,29','2011-07-01','2011-07-31')

	
Purpose: The purpose of this function is to report 'rooms_sold','adr' and 'room_revenue' values by forecast_group. 

Assumptions : Please make sure to pass forecast_group_id as a string ('25').
              When you are passing multiple accomodation class ids, please enclose them in single quotes
              seperated by a comma - ('25,28,29'). Please refer to example #2.
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
05/21/2012		Atul				Shendye					Initial Version
07/14/2021		Adarsh									    Discontinued MS changes
***************************************************************************************/
CREATE function [dbo].[ufn_get_adr_roomrev_by_individual_fg]
(
		@property_id int,
		@forecast_group_id varchar(500),
		@start_date date,
		@end_date date
)		
returns  @adr_room_rev table
	(	
		occupancy_dt	date,
		property_id	int,
		forecast_group_id int,
		rooms_sold numeric(18,0),
		room_revenue numeric(19,5),
		adr	numeric(19,5)
	)
as
begin
		insert into @adr_room_rev
		select  
			mca.occupancy_dt,
			mca.property_id,
			Forecast_Group_ID,
			sum(mca.rooms_sold) as rooms_sold, 
			sum(mca.room_revenue) as room_revenue,
			adr =
				case (sum(mca.rooms_sold))
					when 0 then 0
				else
					sum(mca.room_revenue)/sum(mca.rooms_sold)
				end
		from mkt_accom_activity mca inner join Accom_Type at on mca.Accom_Type_Id = at.Accom_Type_ID and at.isComponentRoom = 'N'
			inner join mkt_seg msg on mca.mkt_seg_id = msg.mkt_seg_id and mca.property_id = msg.property_id and msg.Status_ID in (1,3)
			inner join mkt_seg_forecast_group msfg on msg.mkt_seg_id = msfg.mkt_seg_id and msfg.Status_ID=1
		where mca.property_id = @property_id
			and mca.occupancy_dt between @start_date and @end_date
			and forecast_group_id in (select value from varchartoint(@forecast_group_id,',')) --> parse forcast_group_id string to integers
		group by mca.property_id,mca.occupancy_dt,Forecast_Group_ID
		order by mca.occupancy_dt,Forecast_Group_ID
	return
end

GO