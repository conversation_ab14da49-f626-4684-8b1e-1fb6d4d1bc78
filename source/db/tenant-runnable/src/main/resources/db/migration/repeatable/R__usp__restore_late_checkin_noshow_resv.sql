DROP PROCEDURE IF EXISTS [dbo].[usp_restore_late_checkin_noshow_resv]
    GO

CREATE PROCEDURE [dbo].[usp_restore_late_checkin_noshow_resv]
    @file_metadata_id int,
    @property_id int
AS
drop table if exists #res_ms_at
    drop table if exists #res_ms_at_new_arr
drop table if exists #res_occ_new_arr_dt
    drop table if exists  #res_arr_dep_dt
select file_metadata_id,
       Reservation_Identifier,
       Individual_Status,
       Arrival_DT,
       Departure_DT,
       Booking_DT,
       Cancellation_DT,
       Booked_Accom_Type_Code,
       Accom_Type_ID,
       Mkt_Seg_ID,
       Room_Revenue,
       Food_Revenue,
       Beverage_Revenue,
       Telecom_Revenue,
       Other_Revenue,
       Total_Revenue,
       Source_Booking,
       Nationality,
       Rate_Code,
       Rate_Value,
       Room_Number,
       Booking_type,
       Number_Children,
       Number_Adults,
       change_DTTM,
       Confirmation_No,
       Channel,
       Booking_TM,
       Occupancy_DT,
       Persistent_Key,
       Analytics_Booking_Dt,
       Inv_Block_Code,
       Market_Code,
       Gross_Rate_Value,
       Gross_Room_Revenue,
       Total_Acquisition_Cost,
       Room_Revenue_Acquisition_Cost,
       Net_Revenue,
       Net_Rate into #to_be_restored
    from (select rnc.file_metadata_id
               , rnc.[Reservation_Identifier]
               , rnc.[Individual_Status]
               , rnc.[Arrival_DT]
               , rnc.[Departure_DT]
               , rnc.[Booking_DT]
               , rnc.[Cancellation_DT]
               , rnc.[Booked_Accom_Type_Code]
               , rnc.[Accom_Type_ID]
               , rnc.[Mkt_Seg_ID]
               , rnc.[Room_Revenue]
               , rnc.[Food_Revenue]
               , rnc.[Beverage_Revenue]
               , rnc.[Telecom_Revenue]
               , rnc.[Other_Revenue]
               , rnc.[Total_Revenue]
               , rnc.[Source_Booking]
               , rnc.[Nationality]
               , rnc.[Rate_Code]
               , rnc.[Rate_Value]
               , rnc.[Room_Number]
               , rnc.[Booking_type]
               , rnc.[Number_Children]
               , rnc.[Number_Adults]
               , rnc.change_DTTM
               , rnc.[Confirmation_No]
               , rnc.[Channel]
               , rnc.[Booking_TM]
               , rnc.[Occupancy_DT]
               , rnc.[Persistent_Key]
               , rnc.[Analytics_Booking_Dt]
               , rnc.[Inv_Block_Code]
               , rnc.[Market_Code]
               , rnc.[Gross_Rate_Value]
               , rnc.[Gross_Room_Revenue]
               , rnc.[Total_Acquisition_Cost]
               , rnc.[Room_Revenue_Acquisition_Cost]
               , rnc.[Net_Revenue]
               , rnc.[Net_Rate]
               , rank() over (partition by rnc.[persistent_key] order by rnc.file_metadata_id desc) rank
          from reservation_night_change rnc
              join (select reservation_identifier, DATEADD(D, -1, occupancy_DT) occupancy_DT
              from reservation_night
              where file_metadata_id = @file_metadata_id
              and individual_status in ('CHECKED_IN', 'CHECKED_OUT', 'CHECKED OUT', 'CI', 'CO', 'IN_HOUSE', 'CHECKED IN')) AS A on rnc.reservation_identifier = A.reservation_identifier and rnc.occupancy_DT = a.occupancy_DT
              left join restored_no_show_reservation rdur
              on rdur.reservation_identifier = rnc.Reservation_Identifier and  rdur.occupancy_DT = rnc.occupancy_DT
          where rnc.Individual_Status in ('NS', 'NO SHOW', 'NO_SHOW')
            and rdur.reservation_identifier is null) rriDr
where rank = 1

select rnc.reservation_identifier, rnc.occupancy_DT, rnc.persistent_key,rnc.room_revenue, rnc.food_revenue, rnc.Other_Revenue, rnc.total_Revenue
into #noshow_revenue
    from (
             select rnc.reservation_identifier, rnc.occupancy_DT, rnc.persistent_key,rnc.room_revenue, rnc.food_revenue, rnc.Other_Revenue, rnc.total_Revenue,
                    row_number() over (partition by rnc.persistent_key order by rnc.file_metadata_id desc) rowNum
             from reservation_night_change rnc join #to_be_restored tbr
                 on tbr.reservation_identifier = rnc.Reservation_Identifier and  tbr.occupancy_DT = rnc.occupancy_DT
                 and rnc.file_metadata_id < tbr.file_metadata_id
             where  rnc.room_revenue > 0 ) rnc where  rowNum =1

update tbr
set tbr.room_revenue = nrc.room_revenue
  ,tbr.food_revenue = nrc.food_revenue
  ,tbr.other_revenue = nrc.other_revenue
  ,tbr.total_revenue = nrc.total_revenue
    from #to_be_restored tbr join #noshow_revenue nrc on tbr.persistent_key = nrc.persistent_key


insert into restored_no_show_reservation (file_metadata_id
                                         ,[Reservation_Identifier]
                                         ,[Individual_Status]
                                         ,[Arrival_DT]
                                         ,[Departure_DT]
                                         ,[Booking_DT]
                                         ,[Cancellation_DT]
                                         ,[Booked_Accom_Type_Code]
                                         ,[Accom_Type_ID]
                                         ,[Mkt_Seg_ID]
                                         ,[Room_Revenue]
                                         ,[Food_Revenue]
                                         ,[Beverage_Revenue]
                                         ,[Telecom_Revenue]
                                         ,[Other_Revenue]
                                         ,[Total_Revenue]
                                         ,[Source_Booking]
                                         ,[Nationality]
                                         ,[Rate_Code]
                                         ,[Rate_Value]
                                         ,[Room_Number]
                                         ,[Booking_type]
                                         ,[Number_Children]
                                         ,[Number_Adults]
                                         ,Change_DTTM
                                         ,[Confirmation_No]
                                         ,[Channel]
                                         ,[Booking_TM]
                                         ,[Occupancy_DT]
                                         ,[Persistent_Key]
                                         ,[Analytics_Booking_Dt]
                                         ,[Inv_Block_Code]
                                         ,[Market_Code]
                                         ,[Gross_Rate_Value]
                                         ,[Gross_Room_Revenue]
                                         ,[Total_Acquisition_Cost]
                                         ,[Room_Revenue_Acquisition_Cost]
                                         ,[Net_Revenue]
                                         ,[Net_Rate]) select file_metadata_id
                                                           ,[Reservation_Identifier]
                                                           ,[Individual_Status]
                                                           ,[occupancy_DT]
                                                           ,[Departure_DT]
                                                           ,[Booking_DT]
                                                           ,[Cancellation_DT]
                                                           ,[Booked_Accom_Type_Code]
                                                           ,[Accom_Type_ID]
                                                           ,[Mkt_Seg_ID]
                                                           ,case when [Room_Revenue] > 0.5 then [Room_revenue] else rate_value end
                                                           ,[Food_Revenue]
                                                           ,[Beverage_Revenue]
                                                           ,[Telecom_Revenue]
                                                           ,[Other_Revenue]
                                                           ,case when [Total_Revenue] > 0.5 then [total_revenue] else rate_value end
                                                           ,[Source_Booking]
                                                           ,[Nationality]
                                                           ,[Rate_Code]
                                                           ,[Rate_Value]
                                                           ,[Room_Number]
                                                           ,[Booking_type]
                                                           ,[Number_Children]
                                                           ,[Number_Adults]
                                                           ,Change_DTTM
                                                           ,[Confirmation_No]
                                                           ,[Channel]
                                                           ,[Booking_TM]
                                                           ,[Occupancy_DT]
                                                           ,[Persistent_Key]
                                                           ,[Analytics_Booking_Dt]
                                                           ,[Inv_Block_Code]
                                                           ,[Market_Code]
                                                           ,[Gross_Rate_Value]
                                                           ,[Gross_Room_Revenue]
                                                           ,[Total_Acquisition_Cost]
                                                           ,[Room_Revenue_Acquisition_Cost]
                                                           ,[Net_Revenue]
                                                           ,[Net_Rate] from #to_be_restored

select reservation_identifier , min(arrival_DT) min_arr_dt,max(departure_DT) max_dep_dt into #dayUseReservations
from reservation_night
    where reservation_identifier in (select reservation_identifier from restored_no_show_reservation)
group by reservation_identifier
having min(arrival_DT) = max(departure_DT)


select file_metadata_id
     , rn.Reservation_Identifier
     , rn_status.individual_status
     , [Arrival_DT]
     , [Departure_DT]
     , [Booking_DT]
     , [Cancellation_DT]
     , [Booked_Accom_Type_Code]
     , [Accom_Type_ID]
     , [Mkt_Seg_ID]
     , [Room_Revenue]
     , [Food_Revenue]
     , [Beverage_Revenue]
     , [Telecom_Revenue]
     , [Other_Revenue]
     , [Total_Revenue]
     , [Source_Booking]
     , [Nationality]
     , [Rate_Code]
     , [Rate_Value]
     , [Room_Number]
     , [Booking_type]
     , [Number_Children]
     , [Number_Adults]
     , Change_DTTM
     , [Confirmation_No]
     , [Channel]
     , [Booking_TM]
     , [Occupancy_DT]
     , [Persistent_Key]
     , [Analytics_Booking_Dt]
     , [Inv_Block_Code]
     , [Market_Code]
     , [Gross_Rate_Value]
     , [Gross_Room_Revenue]
     , [Total_Acquisition_Cost]
     , [Room_Revenue_Acquisition_Cost]
     , [Net_Revenue]
     , [Net_Rate]
into #to_be_inserted_in_rn from restored_no_show_reservation rn
    join (select reservation_identifier, max(individual_status) individual_status from reservation_night where file_metadata_id = @file_metadata_id group by reservation_identifier) rn_status
on rn.reservation_identifier = rn_status.reservation_identifier
where rn.reservation_identifier not in (select reservation_identifier from #dayUseReservations)



insert into reservation_night (property_id,file_metadata_id
                              ,[Reservation_Identifier]
                              ,[Individual_Status]
                              ,[Arrival_DT]
                              ,[Departure_DT]
                              ,[Booking_DT]
                              ,[Cancellation_DT]
                              ,[Booked_Accom_Type_Code]
                              ,[Accom_Type_ID]
                              ,[Mkt_Seg_ID]
                              ,[Room_Revenue]
                              ,[Food_Revenue]
                              ,[Beverage_Revenue]
                              ,[Telecom_Revenue]
                              ,[Other_Revenue]
                              ,[Total_Revenue]
                              ,[Source_Booking]
                              ,[Nationality]
                              ,[Rate_Code]
                              ,[Rate_Value]
                              ,[Room_Number]
                              ,[Booking_type]
                              ,[Number_Children]
                              ,[Number_Adults]
                              ,[Confirmation_No]
                              ,[Channel]
                              ,[Booking_TM]
                              ,[Occupancy_DT]
                              ,[Persistent_Key]
                              ,[Analytics_Booking_Dt]
                              ,[Inv_Block_Code]
                              ,[Market_Code]
                              ,[Gross_Rate_Value]
                              ,[Gross_Room_Revenue]
                              ,[Total_Acquisition_Cost]
                              ,[Room_Revenue_Acquisition_Cost]
                              ,[Net_Revenue]
                              ,[Net_Rate]) select @property_id,@file_metadata_id
                                                ,[Reservation_Identifier]
                                                ,[Individual_Status]
                                                ,[Arrival_DT]
                                                ,[Departure_DT]
                                                ,[Booking_DT]
                                                ,[Cancellation_DT]
                                                ,[Booked_Accom_Type_Code]
                                                ,[Accom_Type_ID]
                                                ,[Mkt_Seg_ID]
                                                ,[Room_Revenue]
                                                ,[Food_Revenue]
                                                ,[Beverage_Revenue]
                                                ,[Telecom_Revenue]
                                                ,[Other_Revenue]
                                                ,[Total_Revenue]
                                                ,[Source_Booking]
                                                ,[Nationality]
                                                ,[Rate_Code]
                                                ,[Rate_Value]
                                                ,[Room_Number]
                                                ,[Booking_type]
                                                ,[Number_Children]
                                                ,[Number_Adults]
                                                ,[Confirmation_No]
                                                ,[Channel]
                                                ,[Booking_TM]
                                                ,[Occupancy_DT]
                                                ,[Persistent_Key]
                                                ,[Analytics_Booking_Dt]
                                                ,[Inv_Block_Code]
                                                ,[Market_Code]
                                                ,[Gross_Rate_Value]
                                                ,[Gross_Room_Revenue]
                                                ,[Total_Acquisition_Cost]
                                                ,[Room_Revenue_Acquisition_Cost]
                                                ,[Net_Revenue]
                                                ,[Net_Rate] from #to_be_inserted_in_rn
  where persistent_key not in (select persistent_key from Reservation_Night)


select reservation_identifier, occupancy_DT, mkt_seg_id, accom_type_id into #res_ms_at
    from reservation_night where reservation_identifier in (select reservation_identifier from #to_be_inserted_in_rn)


    select reservation_identifier, occupancy_DT, mkt_seg_id,accom_type_id,
    case when (last_mkt is null and last_at is null) or (mkt_seg_id <> last_mkt or accom_type_id <> last_at) then occupancy_DT else null end as new_arr_t
    into #res_ms_at_new_arr
    from (
    select reservation_identifier, occupancy_DT, mkt_seg_id, accom_type_id, lag(mkt_seg_id) over (partition by reservation_identifier order by occupancy_DT) as last_mkt,
    lag(accom_type_id) over (partition by reservation_identifier order by occupancy_DT) as last_at
    from #res_ms_at res_ms_at
    ) A

    select res_ms_at.Reservation_Identifier,res_ms_at.Occupancy_DT,max(res_ms_at_new_arr.new_arr_t) new_arr_dt
    into #res_occ_new_arr_dt
    from #res_ms_at res_ms_at join #res_ms_at_new_arr res_ms_at_new_arr on res_ms_at.Reservation_Identifier = res_ms_at_new_arr.Reservation_Identifier and res_ms_at.Occupancy_DT>=res_ms_at_new_arr.Occupancy_DT
                             and res_ms_at_new_arr.new_arr_t is not null
    group by res_ms_at.Reservation_Identifier,res_ms_at.Occupancy_DT

    select reservation_identifier, new_arr_DT, DATEADD(D,1,max(occupancy_DT)) as new_Dep_Dt
    into #res_arr_dep_dt
    from #res_occ_new_arr_dt
    group by Reservation_Identifier, new_arr_dt

    update rn
    set arrival_DT = rodt.new_arr_dt, departure_DT = new_dep_dt, file_metadata_id = @file_metadata_id
    from reservation_night rn join #res_occ_new_arr_dt rodt on rn.Reservation_Identifier = rodt.Reservation_Identifier and rn.Occupancy_DT = rodt.Occupancy_DT
    join #res_arr_dep_dt rad on rodt.Reservation_Identifier = rad.Reservation_Identifier and rodt.new_arr_dt = rad.new_arr_dt

    update reservation_night set arrival_DT = DATEADD(D,-1,arrival_DT), occupancy_DT = DATEADD(D,-1,occupancy_DT),file_metadata_id = @file_metadata_id,
    persistent_key = concat(reservation_identifier,',',DATEADD(D,-1,occupancy_DT))
    where reservation_identifier in (select reservation_identifier from #dayUseReservations)
    GO
