IF EXISTS (
		SELECT *
		FROM sys.objects
		WHERE object_id = object_id(N'[dbo].[usp_delete_zero_filled_occupancy_fcst]')
		)
	DROP PROCEDURE [dbo].[usp_delete_zero_filled_occupancy_fcst]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/****** Object:  StoredProcedure [dbo].[usp_delete_zero_filled_occupancy_fcst]   Script Date: 02/08/2023 3:25:58 PM ******/
/*************************************************************************************

Stored Procedure Name: usp_delete_zero_filled_occupancy_fcst

Input Parameters : NA

Output Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_delete_zero_filled_occupancy_fcst

Purpose: The purpose of this stored procedure is to delete existing zero filled data from Occupancy_FCST table. This is part of zero-diet portfolio. It will help improve performance

Assumptions : NA

Author: Shrey

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
02/08/2023		Shrey				Vegda					Initial Version
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_delete_zero_filled_occupancy_fcst]
AS
SET NOCOUNT ON

DECLARE @finalResult VARCHAR(4000)

BEGIN TRY
	BEGIN TRAN

	IF OBJECT_ID('Occupancy_FCST') IS NOT NULL
		DROP TABLE IF EXISTS Occupancy_FCST_Stage

	SELECT *
	INTO Occupancy_FCST_Stage
	FROM Occupancy_FCST
	WHERE NOT (
			Occupancy_NBR = 0
            AND Revenue = 0
            AND COALESCE(Profit, 0) = 0
			)

	EXEC(
		'ALTER TABLE [dbo].[Occupancy_FCST_Stage] ADD CONSTRAINT [PK_Occupancy_FCST_Stage] PRIMARY KEY CLUSTERED ([Occupancy_FCST_ID] ASC)
					WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

		ALTER TABLE [dbo].[Occupancy_FCST_Stage] ADD  CONSTRAINT [DF_Occupancy_FCST_Stage_CreateDate_DTTM]  DEFAULT (getdate())
		FOR [CreateDate_DTTM]

		ALTER TABLE [dbo].[Occupancy_FCST_Stage] ADD  DEFAULT (NULL)
		FOR [Profit]

		DROP TABLE Occupancy_FCST'
	)

	EXEC sp_rename 'Occupancy_FCST_Stage', 'Occupancy_FCST'

	EXEC sp_rename 'PK_Occupancy_FCST_Stage', 'PK_Occupancy_FCST_1', 'object'

	EXEC sp_rename 'DF_Occupancy_FCST_Stage_CreateDate_DTTM', 'DF_Occupancy_FCST_CreateDate_DTTM', 'object'

	CREATE NONCLUSTERED INDEX [NC_Decision_ID_MS_AT_Occupancy_DT] ON [dbo].[Occupancy_FCST]
(
	[Decision_ID] ASC,
	[MKT_SEG_ID] ASC,
	[Accom_Type_ID] ASC,
	[Occupancy_DT] ASC,
	[Property_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

CREATE NONCLUSTERED INDEX [NC_Occupancy_DT_AT_MS] ON [dbo].[Occupancy_FCST]
(
	[Occupancy_DT] ASC,
	[Accom_Type_ID] ASC,
	[MKT_SEG_ID] ASC,
	[Property_ID] ASC
)
INCLUDE ( 	[Occupancy_FCST_ID],
	[Decision_ID],
	[Occupancy_NBR],
	[Revenue],
	[Month_ID],
	[Year_ID],
	[CreateDate_DTTM],
	[Profit]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]

	SET @finalResult = 'SUCCESS'

	COMMIT TRAN
END TRY

BEGIN CATCH
	ROLLBACK TRAN
	DECLARE @ErrorMessage NVARCHAR(4000);
	DECLARE @ErrorSeverity INT;
	DECLARE @ErrorState INT;

	SELECT ERROR_NUMBER() AS ErrorNumber, ERROR_SEVERITY() AS ErrorSeverity, ERROR_STATE() AS ErrorState, ERROR_PROCEDURE() AS ErrorProcedure, ERROR_LINE() AS ErrorLine, ERROR_MESSAGE() AS ErrorMessage;

	SET @ErrorMessage = ERROR_MESSAGE()
	SET @ErrorSeverity = ERROR_SEVERITY()
	SET @ErrorState = ERROR_STATE()

	SET @finalResult = CONCAT('Message : ', @ErrorMessage, ' Severity : ', @ErrorSeverity, ' State : ', @ErrorState)

	RAISERROR (
			@ErrorMessage, -- Message text.
			@ErrorSeverity, -- Severity.
			@ErrorState -- State.
			);

END CATCH

IF @@TRANCOUNT > 0
	ROLLBACK TRAN

SELECT @finalResult
RETURN
GO