if exists(select *
          from sys.objects
          where object_id = object_id(N'[dbo].[usp_rooms_sold_by_market_segment]'))
    drop procedure [dbo].[usp_rooms_sold_by_market_segment]

GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp_rooms_sold_by_market_segment]
(
	@startDate DATE,
	@endDate DATE,
	@caughtUpDate DATE,
    @excludeCompRooms VARCHAR(5)
)
as
begin

CREATE TABLE #Mkt_Segs_Accom_Types (Mkt_Seg_ID INT, Accom_Type_ID INT, Mkt_Seg_Code nvarchar(50))

    INSERT #Mkt_Segs_Accom_Types
SELECT a.Mkt_Seg_ID, b.Accom_Type_ID, a.Mkt_Seg_Code
FROM
    (
        SELECT ms.Mkt_Seg_ID, ms.Mkt_Seg_Code
        FROM Mkt_Seg AS ms
                 INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON ms.MKT_SEG_ID = msfg.Mkt_Seg_ID AND msfg.Status_ID=1
        WHERE ms.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@excludeCompRooms,','))
    ) a
        CROSS JOIN
    (
        SELECT at.Accom_Type_ID
        FROM Accom_Type AS at
INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID
        WHERE  at.Status_ID=1
          AND at.Display_Status_ID = 1
          AND at.System_Default=0
          AND at.isComponentRoom = 'N'
          AND ac.Status_ID=1
          AND ac.System_Default=0
    ) b


SELECT total.Mkt_Seg_Code, SUM(total.RoomSolds) as RoomSolds
FROM
    (
        SELECT msat.Mkt_Seg_Code
             , CAST(SUM(maa.Rooms_Sold) as Numeric(18,5)) as RoomSolds
        FROM Mkt_Accom_Activity AS maa
                 INNER JOIN #Mkt_Segs_Accom_Types msat ON maa.Mkt_Seg_ID = msat.Mkt_Seg_ID AND maa.Accom_Type_ID = msat.Accom_Type_ID
        WHERE maa.Occupancy_DT >= @startDate
          AND maa.Occupancy_DT <= @endDate
          AND maa.Occupancy_DT < @caughtUpDate
        GROUP BY msat.Mkt_Seg_Code
        UNION ALL
        SELECT msat.Mkt_Seg_Code
             , SUM(occ.Occupancy_NBR) as RoomSolds
        FROM Occupancy_FCST AS occ
                 INNER JOIN #Mkt_Segs_Accom_Types msat ON occ.Mkt_Seg_ID = msat.Mkt_Seg_ID AND occ.Accom_Type_ID = msat.Accom_Type_ID
        WHERE occ.Occupancy_DT >= @caughtUpDate
          AND occ.Occupancy_DT >= @startDate
          AND occ.Occupancy_DT <= @endDate
        GROUP BY msat.Mkt_Seg_Code
    ) as total
GROUP BY total.Mkt_Seg_Code
    OPTION (RECOMPILE)	-- because of parameter sniffing issue, due to @caughtUpDate sometimes being 2999-01-01

DROP TABLE IF EXISTS #Mkt_Segs_Accom_Types

end
GO