
drop function if exists [dbo].[ufn_get_inventory_history_report]
go
create function [dbo].[ufn_get_inventory_history_report](
    @property_id int,
    @start_date date,
    @end_date date,
    @decision_type nvarchar(20),
    @level nvarchar(10),
    @rate_level_list nvarchar(max),
    @srp_list nvarchar(max),
    @room_type_list nvarchar(max),
    @isSrpFpLosAtHotel int,
    @isLV0 int,
    @isRollingDate int,
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50),
    @includeSuccessfulControls tinyint,
    @includeFailures tinyint,
    @includePace tinyint,
    @includeConsortia tinyint,
    @physicalPropertiesCount int,
    @isInventoryLimitFeatureEnable int,
	@systemMode nvarchar(50),
	@twoWayModeDate date
)
    returns @inventory_history table
                               (
                                   Return_ID                 int IDENTITY (1,1) NOT NULL,
                                   Property_Name             nvarchar(50),
                                   Decision_Type             nvarchar(50),
                                   Rate_Level                nvarchar(50),
                                   SRP_Name                  nvarchar(50),
                                   Room_Type                 nvarchar(50),
                                   Occupancy_Date            date,
                                   Decision                  nvarchar(50),
                                   Decision_Date_Time        datetime,
                                   Acknowledgement_Date_Time datetime,
                                   ack_status                nvarchar(50),
                                   Error_Messages            nvarchar(1000),
                                   rate_type                 nvarchar(100)
                               )
as
begin
    DECLARE @property_Code nvarchar(50)
    SELECT @property_Code = Property_Code FROM Property WHERE Property_ID = @property_id

    declare @caughtupdate date
    set @caughtupdate =
            (select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)) --> extract caughtup date for a property
    if (@isRollingDate = 1)
        begin
            set @start_date = (select absolute_date
                               from ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate))
            set @end_date = (select absolute_date
                             from ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate))
        end

    declare @rackRateId int
    set @rackRateId =
            (select Rate_Qualified_id from Rate_Qualified where Property_ID = @property_id and Rate_Code_Name = 'LV0')

    declare @RL_inventory_history table
                                  (
                                      Decision_Type             nvarchar(50),
                                      Rate_Level                nvarchar(50),
                                      SRP_Name                  nvarchar(50),
                                      Room_Type                 nvarchar(50),
                                      Occupancy_Date            date,
                                      Decision                  nvarchar(50),
                                      Decision_Date_Time        datetime,
                                      Acknowledgement_Date_Time datetime,
                                      ack_status                nvarchar(50),
                                      Error_Messages            nvarchar(1000),
                                      rate_type                 nvarchar(100)
                                  )

    declare @LV0_inventory_history table
                                   (
                                       Decision_Type             nvarchar(50),
                                       Rate_Level                nvarchar(50),
                                       SRP_Name                  nvarchar(50),
                                       Room_Type                 nvarchar(50),
                                       Occupancy_Date            date,
                                       Decision                  nvarchar(50),
                                       Decision_Date_Time        datetime,
                                       Acknowledgement_Date_Time datetime,
                                       ack_status                nvarchar(50),
                                       Error_Messages            nvarchar(1000),
                                       rate_type                 nvarchar(100)
                                   )

    declare @SRP_inventory_history table
                                   (
                                       Decision_Type             nvarchar(50),
                                       Rate_Level                nvarchar(50),
                                       SRP_Name                  nvarchar(50),
                                       Room_Type                 nvarchar(50),
                                       Occupancy_Date            date,
                                       Decision                  nvarchar(50),
                                       Decision_Date_Time        datetime,
                                       Acknowledgement_Date_Time datetime,
                                       ack_status                nvarchar(50),
                                       Error_Messages            nvarchar(1000),
                                       rate_type                 nvarchar(100)
                                   )
    declare @Prop_ovbk_inventory_history table
                                         (
                                             Decision_Type             nvarchar(50),
                                             Rate_Level                nvarchar(50),
                                             SRP_Name                  nvarchar(50),
                                             Room_Type                 nvarchar(50),
                                             Occupancy_Date            date,
                                             Decision                  nvarchar(50),
                                             Decision_Date_Time        datetime,
                                             Acknowledgement_Date_Time datetime,
                                             ack_status                nvarchar(50),
                                             Error_Messages            nvarchar(1000),
                                             rate_type                 nvarchar(100),
                                             transaction_id            nvarchar(50)
                                         )

    declare @Prop_inventory_limit_history table
                                         (
                                             Decision_Type             nvarchar(50),
                                             Rate_Level                nvarchar(50),
                                             SRP_Name                  nvarchar(50),
                                             Room_Type                 nvarchar(50),
                                             Occupancy_Date            date,
                                             Decision                  nvarchar(50),
                                             Decision_Date_Time        datetime,
                                             Acknowledgement_Date_Time datetime,
                                             ack_status                nvarchar(50),
                                             Error_Messages            nvarchar(1000),
                                             rate_type                 nvarchar(100),
                                             transaction_id            nvarchar(50)
                                         )

    declare @RT_Ovbk_inventory_history table
                                       (
                                           Decision_Type             nvarchar(50),
                                           Rate_Level                nvarchar(50),
                                           SRP_Name                  nvarchar(50),
                                           Room_Type                 nvarchar(50),
                                           Occupancy_Date            date,
                                           Decision                  nvarchar(50),
                                           Decision_Date_Time        datetime,
                                           Acknowledgement_Date_Time datetime,
                                           ack_status                nvarchar(50),
                                           Error_Messages            nvarchar(1000),
                                           rate_type                 nvarchar(100)
                                       )

    declare @isProduct as tinyint = 0
    declare @isRateLevel as tinyint = 0
    declare @isSrpLevel as tinyint = 0
    declare @isRoomType as tinyint = 0
    declare @isPropOvbk as tinyint = 0
    declare @isInventoryLimit as tinyint = 0
    declare @isRTOvbk as tinyint = 0
    declare @isAll as tinyint = 0

    declare @temp_Rate_detail table
                              (
                                  Rate_Unqualified_ID int,
                                  Rate_Code_Name      nvarchar(50),
                                  UNIQUE CLUSTERED (Rate_Unqualified_ID)
                              )

    declare @temp_RateUnqualified_DailyBAR_detail table
                                                  (
                                                      Rate_Unqualified_ID int,
                                                      Rate_Code_Name      nvarchar(50),
                                                      UNIQUE CLUSTERED (Rate_Unqualified_ID)
                                                  )

    declare @temp_Srp_detail table
                             (
                                 Rate_Qualified_ID int,
                                 Rate_Code_Name    nvarchar(50),
                                 UNIQUE CLUSTERED (Rate_Qualified_ID)
                             )

    declare @temp_roomtype_detail table
                                  (
                                      Accom_Type_ID   int,
                                      Accom_Type_Code nvarchar(50)
                                          UNIQUE CLUSTERED (Accom_Type_ID)
                                  )

    declare @inventory_history_table table
                                     (
                                         Decision_Ack_Status_ID    bigint,
                                         Property_ID               int,
                                         Occupancy_Date            date,
                                         Rate_Unqualified_ID       int,
                                         Rate_Qualified_ID         int,
                                         Accom_Type_ID             int,
                                         Decision_Type             nvarchar(50),
                                         Decision                  nvarchar(50),
                                         Decision_Date_Time        datetime,
                                         Acknowledgement_Date_Time datetime,
                                         Error_Code                nvarchar(50),
                                         Error_Description         nvarchar(1000),
                                         rate_type                 nvarchar(100),
                                         transaction_id            nvarchar(50),
                                         UNIQUE CLUSTERED (Property_ID, Decision_Type, Occupancy_Date, Decision_Ack_Status_ID)
                                     )

    if (PATINDEX('%all%', @decision_type) > 0)
        begin
            set @isAll = 1
        end

    if (@decision_type = 'PRICING')
        begin
            set @decision_type = 'DailyBar'
        end

    if (@level = 'PRODUCT' or @isAll = 1)
        begin
            set @isProduct = 1
        end

    if (@level = 'RL' or @level = 'RLSRP' or @isAll = 1)
        begin
            set @isRateLevel = 1
        end

    if (@level = 'SRP' or @level = 'RLSRP' or @isAll = 1)
        begin
            set @isSrpLevel = 1
        end

    if (@level = 'HOUSE' or @level = 'RTHOUSE' or @isAll = 1)
        begin
            set @isPropOvbk = 1
        end

    if (@isInventoryLimitFeatureEnable = 1 and (@level = 'HOUSE' or @isAll = 1))
        begin
            set @isInventoryLimit = 1
        end

    if (@level = 'RT' or @level = 'RTHOUSE' or @isAll = 1)
        begin
            set @isRTOvbk = 1
        end

    -- Rate level
    if (PATINDEX('%-1%', @rate_level_list) = 0 AND (@level = 'RL' or @level = 'RLSRP' or @isAll = 1))
        begin
            set @isRateLevel = 1
            if (PATINDEX('%all%', @rate_level_list) > 0 or @isAll = 1)
                insert into @temp_Rate_detail
                select Rate_Unqualified_ID, Rate_Code_Name
                from Rate_UnQualified
                WHERE Property_ID = @property_id
                  AND Rate_Code_Name != 'LV0'
            else
                insert into @temp_Rate_detail
                select Rate_Unqualified_ID, Rate_Code_Name
                from Rate_UnQualified
                where Property_ID = @property_id
                  AND Rate_Unqualified_ID in (select value from varchartoint(@rate_level_list, ','))
                  AND Rate_Code_Name != 'LV0'
        end

    -- Daily BAR level
    if (PATINDEX('%-1%', @rate_level_list) = 0 AND (@level = 'PRODUCT' or @isAll = 1))
        begin
            set @isProduct = 1
            if (PATINDEX('%all%', @rate_level_list) > 0 or @isAll = 1)
                insert into @temp_RateUnqualified_DailyBAR_detail
                select Rate_Unqualified_ID, Rate_Code_Name
                from Rate_UnQualified
                WHERE Property_ID = @property_id
                  and Rate_Code_Description <> 'consortia'
                  and Yieldable <> 0
            else
                insert into @temp_RateUnqualified_DailyBAR_detail
                select Rate_Unqualified_ID, ruq.Rate_Code_Name
                from Rate_UnQualified ruq
                         join Product p on ruq.Rate_Code_Name = p.Name
                where Property_ID = @property_id
                  AND p.Product_ID in (select value from varchartoint(@rate_level_list, ','))
                  and Rate_Code_Description <> 'consortia'
                  and Yieldable <> 0
        end


    -- SRP
    if (PATINDEX('%-1%', @srp_list) = 0 AND (@level = 'SRP' or @level = 'RLSRP' or @isAll = 1))
        begin
            set @isSrpLevel = 1
            if (PATINDEX('%all%', @srp_list) > 0 or @isAll = 1)
                insert into @temp_Srp_detail
                select Rate_qualified_ID, Rate_Code_Name
                from Rate_Qualified
                WHERE Property_ID = @property_id
            else
                insert into @temp_Srp_detail
                select Rate_qualified_ID, Rate_Code_Name
                from Rate_Qualified
                where Property_ID = @property_id
                  AND Rate_qualified_ID in (select value from varchartoint(@srp_list, ','))
        end


    -- Room type
    if (PATINDEX('%-1%', @room_type_list) = 0)
        begin
            set @isRoomType = 1
            if (PATINDEX('%all%', @room_type_list) > 0 or @isAll = 1)
                insert into @temp_roomtype_detail
                select Accom_Type_ID, Accom_Type_Name
                from Accom_Type
                WHERE Property_ID = @property_id
            else
                insert into @temp_roomtype_detail
                select Accom_Type_ID, Accom_Type_Name
                from Accom_Type
                where Property_ID = @property_id
                  AND Accom_Type_ID in (select value from varchartoint(@room_type_list, ','))
        end

    -- filter out date on basis of occupancy  date and decision type
    if (@includePace = 1)
        begin
            insert into @inventory_history_table
            select Decision_Ack_Status_ID,
                   Property_ID,
                   Occupancy_Date,
                   Rate_Unqualified_ID,
                   Rate_Qualified_ID,
                   Accom_Type_ID,
                   Decision_Type,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code,
                   Error_Description,
                   Rate_Type,
                   transaction_id
            from Decision_Ack_Status base
            where Property_ID = @property_id
              AND Occupancy_Date between @start_date and @end_date
              AND (@isAll = 1 or Decision_Type = @decision_type)
              and ((@includeSuccessfulControls = 1 and Error_Code = 0)
                or (@includeFailures = 1 and Error_Code != 0))
        end
    else
        begin
            insert into @inventory_history_table
            select Decision_Ack_Status_ID,
                   Property_ID,
                   Occupancy_Date,
                   Rate_Unqualified_ID,
                   Rate_Qualified_ID,
                   Accom_Type_ID,
                   Decision_Type,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code,
                   Error_Description,
                   Rate_Type,
                   transaction_id
            from (select Decision_Ack_Status_ID,
                         Property_ID,
                         Occupancy_Date,
                         Rate_Unqualified_ID,
                         Rate_Qualified_ID,
                         Accom_Type_ID,
                         Decision_Type,
                         Decision,
                         Decision_Date_Time,
                         Acknowledgement_Date_Time,
                         Error_Code,
                         Error_Description,
                         Rate_Type,
                         transaction_id,
                         ROW_NUMBER() over (PARTITION BY occupancy_date,rate_unqualified_id,rate_qualified_id,accom_type_id,decision_type order by Decision_Date_Time desc)
                             as row_num
                  from Decision_Ack_Status
                  where Property_ID = @property_id
                    AND Occupancy_Date between @start_date and @end_date
                    AND (@isAll = 1 or Decision_Type = @decision_type)) base
            where row_num <=
                  case
                      when accom_type_id is null then @physicalPropertiesCount
                      else 1
                      end
              and ((@includeSuccessfulControls = 1 and Error_Code = 0) or (@includeFailures = 1 and Error_Code != 0))
        end

    -- RL
    if (@isRateLevel = 1)
        begin
            insert into @RL_inventory_history
            select Decision_Type,
                   rate.Rate_Code_Name Rate_Level,
                   ''                  SRP_Name,
                   ''                  Room_Type,
                   Occupancy_Date,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code          ack_status,
                   Error_Description,
                   rate_type
            from (select *
                  from @inventory_history_table
                  where Property_ID = @property_id
                    AND Decision_Type = 'FPLOS'
                    and Rate_Unqualified_ID is not NULL) base
                     inner join
                 @temp_Rate_detail as rate on base.Rate_Unqualified_ID = rate.Rate_Unqualified_ID
            ORDER by Decision_Type, Rate_Level, Occupancy_Date, Decision_Date_Time DESC
        end

    /* Consortia */
    if (@includeConsortia = 1)
        begin
            insert into @RL_inventory_history
            select 'Pricing'             Decision_Type,
                   ''                    Rate_Level,
                   rates.Rate_Code_Name  SRP_Name,
                   rooms.Accom_Type_code Room_Type,
                   base.Occupancy_Date,
                   base.Decision,
                   base.Decision_Date_Time,
                   base.Acknowledgement_Date_Time,
                   base.Error_Code       ack_status,
                   base.Error_Description,
                   rate_type
            from (select *
                  from @inventory_history_table
                  where Property_ID = @property_id
                    AND Decision_Type = 'DailyBar'
                    and Rate_Unqualified_ID is not NULL
                    and Accom_Type_ID is not null) base
                     join Rate_Unqualified rates
                          on rates.Rate_Unqualified_ID = base.Rate_Unqualified_ID
                              and rates.Status_ID = 2
                              and rates.Rate_Code_Description = 'consortia'
                              and rates.Yieldable = 0
                              and rates.Ranking_Level = 0

                     join Accom_Type as rooms
                          on base.Accom_Type_ID = rooms.Accom_Type_ID

                     join Rate_Code_Vendor_Mapping vm
                          on rates.Rate_Code_Name = vm.Vendor_Rate_Code
                              and lower(vm.Vendor) in ('hilstar', 'pcrs')

                     join Accom_Type_Vendor_Mapping rtm
                          on rooms.Accom_Type_Code = rtm.Vendor_Accom_Type_Code

            where base.Property_ID = @property_id
              and base.Occupancy_Date between @start_date and @end_date
              and (@isAll = 1 or Decision_Type = @decision_type)
              and ((@includeSuccessfulControls = 1 and base.Error_Code = 0) or
                   (@includeFailures = 1 and base.Error_Code <> 0))

            order by Decision_Type, Room_Type, Occupancy_Date, Decision_Date_Time desc
        end

    /* DAILYBAR*/
    if (@isProduct = 1)
        begin
            insert into @RL_inventory_history
            select 'Pricing'                                                                     Decision_Type,
                   case when rate.Rate_Code_Name = 'LV0' then rate.Rate_Code_Name else '' end as Rate_Level,
                   case when rate.Rate_Code_Name = 'LV0' then '' else rate.Rate_Code_Name end    SRP_Name,
                   Accom_Type_code                                                               Room_Type,
                   Occupancy_Date,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code                                                                    ack_status,
                   Error_Description,
                   rate_type
            from (select *
                  from @inventory_history_table
                  where Property_ID = @property_id
                    AND Decision_Type = 'DailyBar'
                    and Rate_Unqualified_ID is not NULL
                    and Accom_Type_ID is not null) base
                     inner join
                 @temp_RateUnqualified_DailyBAR_detail as rate
                 on base.Rate_Unqualified_ID = rate.Rate_Unqualified_ID
                     inner join
                 @temp_roomtype_detail as room
                 on base.Accom_Type_ID = room.Accom_Type_ID
            ORDER by Decision_Type, Room_Type, Occupancy_Date, Decision_Date_Time DESC
        end

    -- SRP
    -- @isSrpFpLosAtHotel=1
    if ( @isSrpLevel = 1)
        begin
            if (@isSrpFpLosAtHotel = 1)
                begin
                    insert into @SRP_inventory_history
                    select 'FPLOS' as Decision_Type,
                           ''         Rate_Level,
                           Rates.Rate_Code_Name,
                           ''         Accom_Type_Code,
                           Results.Occupancy_Date,
                           Results.Decision,
                           Results.Decision_Date_Time,
                           Results.Acknowledgement_Date_Time,
                           Results.Error_Code,
                           Results.Error_Description,
                           rate_type
                    from (select Rate_Qualified_ID,
                                 Occupancy_Date,
                                 Decision,
                                 Decision_Date_Time,
                                 Acknowledgement_Date_Time,
                                 Error_Code,
                                 Error_Description,
                                 Rate_type,
                                 ROW_NUMBER() over (partition by Rate_Qualified_ID, Occupancy_Date order by Decision_Date_Time desc) rank
                          from (select ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID,
                                       ISNULL(VW.Occupancy_Date, PACE.Arrival_DT)           as Occupancy_Date,
                                       ISNULL(VW.Decision, PACE.FPLOS)                      as Decision,
                                       ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM)  as Decision_Date_Time,
                                       case
                                           when VW.Rate_Qualified_ID is not null then VW.Acknowledgement_Date_Time
                                           else PACE.CreateDate_DTTM end                    as Acknowledgement_Date_Time,
                                       ISNULL(VW.Error_Code, 0)                                Error_Code,
                                       ISNULL(VW.Error_Description, 'Success')                 Error_Description,
                                       rate_type
                                from (select Decision_ID,
                                             Rate_Qualified_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_Qualified_FPLOS
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_From_Service
                                                               where Decision_ID = Pace_Qualified_FPLOS.Decision_ID
                                                               and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID
                                                               and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT
                                                               and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM
                                                     )
                                      union all
                                      select Decision_ID,
                                             Rate_Qualified_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_From_Service
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_Qualified_FPLOS
                                                               where Decision_ID = Pace_From_Service.Decision_ID
                                                               and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID
                                                               and Arrival_DT = Pace_From_Service.Arrival_DT
                                                               and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM
                                                     )
                                     ) PACE
                                         full outer join
                                     (select Decision_ID,
                                             Decision,
                                             Rate_Qualified_ID,
                                             Occupancy_Date,
                                             Decision_Date_Time,
                                             Acknowledgement_Date_Time,
                                             Error_Code,
                                             Error_Description,
                                             rate_type
                                      from Vw_Qualified_FPLOS_Ack_Status
                                      where Occupancy_Date between @start_date and @end_date) VW
                                     on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID
                                         and PACE.Arrival_DT = VW.Occupancy_Date
                                         and PACE.Decision_ID = VW.Decision_ID) A) Results
                             join Rate_Qualified Rates on Rates.Rate_Qualified_ID = Results.Rate_Qualified_ID
                             join @temp_Srp_detail as tempRates
                                  on tempRates.Rate_Qualified_ID = Results.Rate_Qualified_ID
                    where (@includePace = 1 or Results.rank = 1)
                      and ((@includeSuccessfulControls = 1 and Results.Error_Code = 0) or
                           (@includeFailures = 1 and Results.Error_Code <> 0))
                      and Rates.Rate_Code_Name <> 'LV0'
                    ORDER by Decision_Type, Accom_Type_Code, Occupancy_Date, Decision_Date_Time DESC
                end
            else
                begin
                    insert into @SRP_inventory_history
                    select 'FPLOS' as Decision_Type,
                           ''         Rate_Level,
                           Rates.Rate_Code_Name,
                           Accom_Type_Code,
                           Results.Occupancy_Date,
                           Results.Decision,
                           Results.Decision_Date_Time,
                           Results.Acknowledgement_Date_Time,
                           Results.Error_Code,
                           Results.Error_Description,
                           rate_type
                    from (select Rate_Qualified_ID,
                                 Accom_Type_ID,
                                 Occupancy_Date,
                                 Decision,
                                 Decision_Date_Time,
                                 Acknowledgement_Date_Time,
                                 Error_Code,
                                 Error_Description,
                                 Rate_type,
                                 ROW_NUMBER() over (partition by Rate_Qualified_ID, Accom_Type_ID, Occupancy_Date order by Decision_Date_Time desc) rank
                          from (select ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID,
                                       ISNULL(VW.Accom_Type_ID, PACE.Accom_Type_ID)         as Accom_Type_ID,
                                       ISNULL(VW.Occupancy_Date, PACE.Arrival_DT)           as Occupancy_Date,
                                       ISNULL(VW.Decision, PACE.FPLOS)                      as Decision,
                                       ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM)  as Decision_Date_Time,
                                       case
                                           when VW.Rate_Qualified_ID is not null then VW.Acknowledgement_Date_Time
                                           else PACE.CreateDate_DTTM end                    as Acknowledgement_Date_Time,
                                       ISNULL(VW.Error_Code, 0)                                Error_Code,
                                       ISNULL(VW.Error_Description, 'Success')                 Error_Description,
                                       Rate_type
                                from (select Decision_ID,
                                             Rate_Qualified_ID,
                                             Accom_Type_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_Qualified_FPLOS
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_From_Service
                                                               where Decision_ID = Pace_Qualified_FPLOS.Decision_ID
                                                               and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID
                                                               and Accom_Type_ID = Pace_Qualified_FPLOS.Accom_Type_ID
                                                               and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT
                                                               and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM
                                                     )
                                      union all
                                      select Decision_ID,
                                             Rate_Qualified_ID,
                                             Accom_Type_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_From_Service
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_Qualified_FPLOS
                                                               where Decision_ID = Pace_From_Service.Decision_ID
                                                               and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID
                                                               and Accom_Type_ID = Pace_From_Service.Accom_Type_ID
                                                               and Arrival_DT = Pace_From_Service.Arrival_DT
                                                               and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM
                                                     )
                                     ) PACE
                                         full outer join
                                     (select Decision_ID,
                                             Decision,
                                             Rate_Qualified_ID,
                                             Accom_Type_ID,
                                             Occupancy_Date,
                                             Decision_Date_Time,
                                             Acknowledgement_Date_Time,
                                             Error_Code,
                                             Error_Description,
                                             Rate_Type
                                      from Vw_Qualified_FPLOS_Ack_Status
                                      where Occupancy_Date between @start_date and @end_date) VW
                                     on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID
                                         and PACE.Accom_Type_ID = VW.Accom_Type_ID
                                         and PACE.Arrival_DT = VW.Occupancy_Date
                                         and PACE.Decision_ID = VW.Decision_ID) A) Results
                             join Rate_Qualified Rates on Rates.Rate_Qualified_ID = Results.Rate_Qualified_ID
                             join @temp_Srp_detail as tempRates
                                  on tempRates.Rate_Qualified_ID = Results.Rate_Qualified_ID
                             join @temp_roomtype_detail rooms on rooms.Accom_Type_ID = Results.Accom_Type_ID
                             left join Limit_Total_Rate_Qualified limitTotalSRPs
                                       on Results.Rate_Qualified_ID = limitTotalSRPs.Limit_Total_Rate_Qualified_ID
                    where limitTotalSRPs.Limit_Total_Rate_Qualified_ID is null
                      and (@includePace = 1 or Results.rank = 1)
                      and ((@includeSuccessfulControls = 1 and Results.Error_Code = 0) or
                           (@includeFailures = 1 and Results.Error_Code <> 0))
                      and Rates.Rate_Code_Name <> 'LV0'
                    ORDER by Decision_Type, Accom_Type_Code, Occupancy_Date, Decision_Date_Time DESC

                    insert into @SRP_inventory_history
                    select 'FPLOS' as Decision_Type,
                           ''         Rate_Level,
                           tempRates.Rate_Code_Name,
                           ''      as Accom_Type_Code,
                           Results.Occupancy_Date,
                           Results.Decision,
                           Results.Decision_Date_Time,
                           Results.Acknowledgement_Date_Time,
                           Results.Error_Code,
                           Results.Error_Description,
                           Rate_Type
                    from (select Rate_Qualified_ID,
                                 Occupancy_Date,
                                 Decision,
                                 Decision_Date_Time,
                                 Acknowledgement_Date_Time,
                                 Error_Code,
                                 Error_Description,
                                 Rate_Type,
                                 ROW_NUMBER() over (partition by Rate_Qualified_ID, Occupancy_Date order by Decision_Date_Time desc) rank
                          from (select ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID,
                                       ISNULL(VW.Occupancy_Date, PACE.Arrival_DT)           as Occupancy_Date,
                                       ISNULL(VW.Decision, PACE.FPLOS)                      as Decision,
                                       ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM)  as Decision_Date_Time,
                                       case
                                           when VW.Rate_Qualified_ID is not null then VW.Acknowledgement_Date_Time
                                           else PACE.CreateDate_DTTM end                    as Acknowledgement_Date_Time,
                                       ISNULL(VW.Error_Code, 0)                                Error_Code,
                                       ISNULL(VW.Error_Description, 'Success')                 Error_Description,
                                       Rate_Type
                                from (select Decision_ID,
                                             Rate_Qualified_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_Qualified_FPLOS
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_From_Service
                                                               where Decision_ID = Pace_Qualified_FPLOS.Decision_ID
                                                               and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID
                                                               and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT
                                                               and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM
                                                     )
                                      union all
                                      select Decision_ID,
                                             Rate_Qualified_ID,
                                             Arrival_DT,
                                             FPLOS,
                                             CreateDate_DTTM
                                      from Pace_From_Service
                                      where Arrival_DT between @start_date and @end_date
                                      and not exists (select 1 from Pace_Qualified_FPLOS
                                                               where Decision_ID = Pace_From_Service.Decision_ID
                                                               and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID
                                                               and Arrival_DT = Pace_From_Service.Arrival_DT
                                                               and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM
                                                     )
                                     ) PACE
                                         full outer join
                                     (select Decision_ID,
                                             Decision,
                                             Rate_Qualified_ID,
                                             Occupancy_Date,
                                             Decision_Date_Time,
                                             Acknowledgement_Date_Time,
                                             Error_Code,
                                             Error_Description,
                                             Rate_Type
                                      from Vw_Qualified_FPLOS_Ack_Status
                                      where Occupancy_Date between @start_date and @end_date) VW
                                     on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID
                                         and PACE.Arrival_DT = VW.Occupancy_Date
                                         and PACE.Decision_ID = VW.Decision_ID) A) Results
                             join @temp_Srp_detail as tempRates
                                  on tempRates.Rate_Qualified_ID = Results.Rate_Qualified_ID
                             left join Limit_Total_Rate_Qualified limitTotalSRPs
                                       on Results.Rate_Qualified_ID = limitTotalSRPs.Limit_Total_Rate_Qualified_ID
                    where limitTotalSRPs.Limit_Total_Rate_Qualified_ID is not null
                      and (@includePace = 1 or Results.rank = 1)
                      and ((@includeSuccessfulControls = 1 and Results.Error_Code = 0) or
                           (@includeFailures = 1 and Results.Error_Code <> 0))
                      and tempRates.Rate_Code_Name <> 'LV0'
                    ORDER by Decision_Type, Occupancy_Date, Decision_Date_Time DESC

                end
        end

    -- prop ovbk
    if (@isPropOvbk = 1)
        begin
            insert into @Prop_ovbk_inventory_history
            select Decision_Type,
                   'House'    Rate_Level,
                   ''         SRP_Name,
                   ''         Room_Type,
                   Occupancy_Date,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code ack_status,
                   Error_Description,
                   Rate_Type,
                   transaction_id
            from (select *
                  from @inventory_history_table
                  where Property_ID = @property_id
                    AND Decision_Type = 'Overbooking'
                    and Accom_Type_ID is NULL) base
            ORDER by Decision_Type, Occupancy_Date, Decision_Date_Time DESC
        end

    -- Inventory Limit
        if (@isInventoryLimit = 1)
        begin
            insert into @Prop_inventory_limit_history
            select Decision_Type,
            'House'    Rate_Level,
            ''         SRP_Name,
            ''         Room_Type,
            Occupancy_Date,
            Decision,
            Decision_Date_Time,
            Acknowledgement_Date_Time,
            Error_Code ack_status,
            Error_Description,
            Rate_Type,
            transaction_id
                from (select *
                    from @inventory_history_table
                    where Property_ID = @property_id
                    AND Decision_Type = 'InventoryLimit'
                    and Accom_Type_ID is NULL) base
                    ORDER by Decision_Type, Occupancy_Date, Decision_Date_Time DESC
        end

    -- RT ovbk
    if (@isRTOvbk = 1)
        begin
            insert into @RT_Ovbk_inventory_history
            select Decision_Type,
                   ''              Rate_Level,
                   ''              SRP_Name,
                   Accom_Type_Code Room_Type,
                   Occupancy_Date,
                   Decision,
                   Decision_Date_Time,
                   Acknowledgement_Date_Time,
                   Error_Code      ack_status,
                   Error_Description,
                   Rate_Type
            from (select *
                  from @inventory_history_table
                  where Property_ID = @property_id
                    AND Decision_Type = 'Overbooking'
                    and Accom_Type_ID is not NULL) base
                     inner join
                 @temp_roomtype_detail as room on base.Accom_Type_ID = room.Accom_Type_ID
            ORDER by Decision_Type, Room_Type, Occupancy_Date, Decision_Date_Time DESC
        end
    -- LV0
    if (@isLV0 = 1 AND (@level = 'RL' or @level = 'RLSRP' or @isAll = 1))
        begin
            begin
                insert into @LV0_inventory_history
                select 'FPLOS' as      Decision_Type,
                       'LV0'           Rate_Level,
                       ''              SRP_Name,
                       Accom_Type_code Room_Type,
                       Occupancy_Date,
                       Decision,
                       Decision_Date_Time,
                       Acknowledgement_Date_Time,
                       Error_Code      ack_status,
                       Error_Description,
                       Rate_Type
                from (select Rate_Qualified_ID,
                             Accom_Type_ID,
                             Occupancy_Date,
                             Decision,
                             Decision_Date_Time,
                             Acknowledgement_Date_Time,
                             Error_Code,
                             Error_Description,
                             Rate_Type,
                             ROW_NUMBER() over (partition by Rate_Qualified_ID, Accom_Type_ID, Occupancy_Date order by Decision_Date_Time desc) rank
                      from (select ISNULL(VW.Rate_Qualified_ID, PACE.Rate_Qualified_ID) as Rate_Qualified_ID,
                                   ISNULL(VW.Accom_Type_ID, PACE.Accom_Type_ID)         as Accom_Type_ID,
                                   ISNULL(VW.Occupancy_Date, PACE.Arrival_DT)           as Occupancy_Date,
                                   ISNULL(VW.Decision, PACE.FPLOS)                      as Decision,
                                   ISNULL(VW.Decision_Date_Time, PACE.CreateDate_DTTM)  as Decision_Date_Time,
                                   case
                                       when VW.Rate_Qualified_ID is not null then VW.Acknowledgement_Date_Time
                                       else PACE.CreateDate_DTTM end                    as Acknowledgement_Date_Time,
                                   ISNULL(VW.Error_Code, 0)                                Error_Code,
                                   ISNULL(VW.Error_Description, 'Success')                 Error_Description,
                                   Rate_Type
                            from (select Decision_ID,
                                         Rate_Qualified_ID,
                                         Accom_Type_ID,
                                         Arrival_DT,
                                         FPLOS,
                                         CreateDate_DTTM
                                  from Pace_Qualified_FPLOS
                                  where Arrival_DT between @start_date and @end_date
                                  and Rate_Qualified_ID = @rackRateId
                                  and not exists (select 1 from Pace_From_Service
                                                           where Decision_ID = Pace_Qualified_FPLOS.Decision_ID
                                                           and Rate_Qualified_ID = Pace_Qualified_FPLOS.Rate_Qualified_ID
                                                           and Accom_Type_ID = Pace_Qualified_FPLOS.Accom_Type_ID
                                                           and Arrival_DT = Pace_Qualified_FPLOS.Arrival_DT
                                                           and CreateDate_DTTM > Pace_Qualified_FPLOS.CreateDate_DTTM
                                                  )
                                  union all
                                  select Decision_ID,
                                         Rate_Qualified_ID,
                                         Accom_Type_ID,
                                         Arrival_DT,
                                         FPLOS,
                                         CreateDate_DTTM
                                  from Pace_From_Service
                                  where Arrival_DT between @start_date and @end_date
                                  and Rate_Qualified_ID = @rackRateId
                                  and not exists (select 1 from Pace_Qualified_FPLOS
                                                           where Decision_ID = Pace_From_Service.Decision_ID
                                                           and Rate_Qualified_ID = Pace_From_Service.Rate_Qualified_ID
                                                           and Accom_Type_ID = Pace_From_Service.Accom_Type_ID
                                                           and Arrival_DT = Pace_From_Service.Arrival_DT
                                                           and CreateDate_DTTM > Pace_From_Service.CreateDate_DTTM
                                                 )
                                ) PACE
                                     full outer join
                                 (select Decision_ID,
                                         Decision,
                                         Rate_Qualified_ID,
                                         Accom_Type_ID,
                                         Occupancy_Date,
                                         Decision_Date_Time,
                                         Acknowledgement_Date_Time,
                                         Error_Code,
                                         Error_Description,
                                         Rate_Type
                                  from Vw_Qualified_FPLOS_Ack_Status
                                  where Occupancy_Date between @start_date and @end_date
                                    and Rate_Qualified_ID = @rackRateId) VW
                                 on PACE.Rate_Qualified_ID = VW.Rate_Qualified_ID
                                     and PACE.Arrival_DT = VW.Occupancy_Date
                                     and PACE.Accom_Type_ID = VW.Accom_Type_ID
                                     and PACE.Decision_ID = VW.Decision_ID) A) Results
                         join @temp_roomtype_detail as room on Results.Accom_Type_ID = room.Accom_Type_ID
                where (@includePace = 1 or Results.rank = 1)
                  and ((@includeSuccessfulControls = 1 and Results.Error_Code = 0) or
                       (@includeFailures = 1 and Results.Error_Code <> 0))
                ORDER by Decision_Type, Room_Type, Occupancy_Date, Decision_Date_Time DESC
            end
        end


    insert into @inventory_history
    SELECT @property_Code                                                                       as property_name,
           Decision_Type,
           Rate_Level,
           SRP_Name,
           Room_Type,
           Occupancy_Date,
           Decision,
           Decision_Date_Time,
           Acknowledgement_Date_Time,
           case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
           case
               when ack_status = 0 then ''
               else
                   (select translate(replace(replace(replace(replace(
                                                                     replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''),
                                                                     'notifications:', ', notifications:'),
                                                             'ErrorDetail', ''), 'message:', ', message:'), 'class',
                                             ''), '{}', '[]'))
               end                                                                              as Error_Messages,
           rate_type
    FROM (select *
          from @LV0_inventory_history where @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable

    insert into @inventory_history
    SELECT @property_Code                                                                       as property_name,
           Decision_Type,
           Rate_Level,
           SRP_Name,
           Room_Type,
           Occupancy_Date,
           Decision,
           Decision_Date_Time,
           Acknowledgement_Date_Time,
           case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
           case
               when ack_status = 0 then ''
               else
                   (select translate(replace(replace(replace(replace(
                                                                     replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''),
                                                                     'notifications:', ', notifications:'),
                                                             'ErrorDetail', ''), 'message:', ', message:'), 'class',
                                             ''), '{}', '[]'))
               end                                                                              as Error_Messages,
           Rate_Type
    FROM (select *
          from @RL_inventory_history where @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable

    insert into @inventory_history
    SELECT @property_Code                                                                       as property_name,
           Decision_Type,
           Rate_Level,
           SRP_Name,
           Room_Type,
           Occupancy_Date,
           Decision,
           Decision_Date_Time,
           Acknowledgement_Date_Time,
          case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
           case
               when ack_status = 0 then ''
               else
                   (select translate(replace(replace(replace(replace(
                                                                     replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''),
                                                                     'notifications:', ', notifications:'),
                                                             'ErrorDetail', ''), 'message:', ', message:'), 'class',
                                             ''), '{}', '[]'))
               end                                                                              as Error_Messages,
           Rate_Type
    FROM (select *
          from @SRP_inventory_history WHERE  @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable

    insert into @inventory_history
    SELECT case
               when len(transaction_id) > 12 then SUBSTRING(transaction_id, 14, 15)
               else @property_Code end                                                          as property_name,
           Decision_Type,
           Rate_Level,
           SRP_Name,
           Room_Type,
           Occupancy_Date,
           Decision,
           Decision_Date_Time,
           Acknowledgement_Date_Time,
           case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
           case
               when ack_status = 0 then ''
               else
                   (select translate(replace(replace(replace(replace(
                                                                     replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''),
                                                                     'notifications:', ', notifications:'),
                                                             'ErrorDetail', ''), 'message:', ', message:'), 'class',
                                             ''), '{}', '[]'))
               end                                                                              as Error_Messages,
           Rate_Type
    FROM (select *
          from @Prop_ovbk_inventory_history where @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable

    insert into @inventory_history
        SELECT case
            when len(transaction_id) > 12 then SUBSTRING(transaction_id, 14, 15)
            else @property_Code end                                                          as property_name,
            Decision_Type,
            Rate_Level,
            SRP_Name,
            Room_Type,
            Occupancy_Date,
            Decision,
            Decision_Date_Time,
            Acknowledgement_Date_Time,
            case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
            case
                when ack_status = 0 then ''
                else
            (select translate(replace(replace(replace(replace(
                replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''), 'notifications:', ', notifications:'),
                        'ErrorDetail', ''), 'message:', ', message:'), 'class', ''), '{}', '[]'))
                end  as Error_Messages,
            Rate_Type
            FROM (select *
                from @Prop_inventory_limit_history where @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable

    insert into @inventory_history
    SELECT @property_Code                                                                       as property_name,
           Decision_Type,
           Rate_Level,
           SRP_Name,
           Room_Type,
           Occupancy_Date,
           Decision,
           Decision_Date_Time,
           Acknowledgement_Date_Time,
           case ack_status WHEN -1 THEN 'not received' when 0 then 'success' else 'failure' end as ack_status,
           case
               when ack_status = 0 then ''
               else
                   (select translate(replace(replace(replace(replace(
                                                                     replace(replace(Error_Messages, ' code:', ', code:'), '  ', ''),
                                                                     'notifications:', ', notifications:'),
                                                             'ErrorDetail', ''), 'message:', ', message:'), 'class',
                                             ''), '{}', '[]'))
               end                                                                              as Error_Messages,
           Rate_Type
    FROM (select *
          from @RT_Ovbk_inventory_history where @systemMode='TwoWay' AND Acknowledgement_Date_Time >= @twoWayModeDate) AS outerTable
    update @inventory_history
    set rate_type =
            case
                when rate_type = 1 then 'AMOUNTOFFRACK'
                when rate_type = 2 then 'AMOUNTADDRACK'
                when rate_type = 3 then 'PERCENTOFFRACK'
                when rate_type = 4 then 'PERCENTADDRACK'
                when rate_type = 0 or rate_type is null then ''
                end

    return
end

