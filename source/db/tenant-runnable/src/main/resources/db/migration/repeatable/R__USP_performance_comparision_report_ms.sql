DROP PROCEDURE IF EXISTS [usp_performance_comparision_report_ms]
GO

/*************************************************************************************

Procedure Name: usp_performance_comparision_report_ms

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@pace_days --> number of pace days
	@analysis_start_date --> occupancy_start_date ('2011-07-01')
	@analysis_end_date --> occupancy_end_date ('2011-07-31')
	@comparision_start_date
	@comparision_end_date
	@MktSeg_ID --> MS for which you want to compare the performance.
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_analysis_start_date --> used when @isRollingDate is 1, else, this can be blank
	@rolling_analysis_end_date --> used when @isRollingDate is 1, else, this can be blank
	@rolling_comparision_start_date  --> used when @isRollingDate is 1, else, this can be blank

Ouput Parameter : NA

Execution: this is just an example
	Example 1: usp_performance_comparision_report_ms
	-----------------------------------------
	exec dbo.usp_performance_comparision_report_ms 10016,10,'2011-10-01','2011-10-10','2011-09-01','2011-09-10',183
	
	
Purpose: The purpose of this procedure is to get performance comparision report @ ms
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
08/06/2012		Atul				Shendye					Initial Version
20/07/2017	 Venukoushik			Atmakuri				Query Optimization
07/19/2019     Animesh              Banerjee                Convert to stored proc + Query optimization
***************************************************************************************/

create procedure usp_performance_comparision_report_ms @property_id int,
                                                       @pace_days int,
                                                       @analysis_start_date date,
                                                       @analysis_end_date date,
                                                       @comparision_start_date date,
                                                       @comparision_end_date date,
                                                       @MktSeg_ID int,
                                                       @isRollingDate int,
                                                       @rolling_analysis_start_date nvarchar(50),
                                                       @rolling_analysis_end_date nvarchar(50),
                                                       @rolling_comparision_start_date nvarchar(50),
                                                       @rolling_comparision_end_date nvarchar(50)
as
begin
    -- This is needed in order to avoid "com.microsoft.sqlserver.jdbc.SQLServerException: The statement did not return a result set."
    set nocount on

    declare
        @caughtupdate date
    set @caughtupdate =
                (select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)) --> extract caughtup date for a property

    if (@isRollingDate = 1)
        begin
            set @analysis_start_date = (select absolute_date
                                        from dbo.ufn_get_absolute_dates_from_rolling_dates(@rolling_analysis_start_date,
                                                                                       @caughtupdate))
            set @analysis_end_date = (select absolute_date
                                      from dbo.ufn_get_absolute_dates_from_rolling_dates(@rolling_analysis_end_date,
                                                                                     @caughtupdate))
            set @comparision_start_date = (select absolute_date
                                           from dbo.ufn_get_absolute_dates_from_rolling_dates(
                                                        @rolling_comparision_start_date, @caughtupdate))

            -- based on @comparision_start_date and diff between @analysis_start_date and @analysis_end_date, calculate @comparision_end_date
            declare
                @numDays int
            set @numDays = (SELECT DATEDIFF(day, @analysis_start_date, @analysis_end_date))
            if (@rolling_comparision_end_date = '')
                begin
                    set @comparision_end_date = (select DATEADD(day, @numDays, @comparision_start_date))
                end
            else
                begin
                    set @comparision_end_date = (select absolute_date
                                                 from dbo.ufn_get_absolute_dates_from_rolling_dates(
                                                              @rolling_comparision_end_date, @caughtupdate))
                end
        end

    declare
        @business_date as date = DATEADD(day, -1, @caughtupdate)

    declare
        @num_days_analysis as int = DateDIFF(day, @business_date, @analysis_end_date)
    declare
        @num_days_comparision as int = DateDIFF(day, @business_date, @comparision_end_date)

    DROP TABLE IF EXISTS #a_sold
    DROP TABLE IF EXISTS #fcst
    DROP TABLE IF EXISTS #c_sold
    DROP TABLE IF EXISTS #performance_comparision_report_ms

    create table #a_sold
    (
        daystoArrival int,
        analysis_sold Numeric(18, 0),
        Room_Revenue  Numeric(19, 5),
        Room_ADR      Numeric(19, 5)
    )

    create table #fcst
    (
        daystoArrival int,
        Occupancy_NBR Numeric(18, 5),
        Room_Revenue  Numeric(19, 5),
        Room_ADR      Numeric(19, 5)
    )

    create table #c_sold
    (
        daystoArrival    int,
        comparision_sold Numeric(18, 0),
        Room_Revenue     Numeric(19, 5),
        Room_ADR         Numeric(19, 5)
    )

    insert #a_sold
        select DATEDIFF(day, Business_Day_End_DT, Occupancy_DT) as daystoArrival,
        sum(Rooms_Sold) analysis_sold,
        sum(Room_Revenue) Room_Revenue,
        case sum(Rooms_Sold)
            when 0 then 0
            else
                sum(Room_Revenue) / sum(Rooms_Sold)
            end as Room_ADR
        from dbo.PACE_Mkt_Activity PMA
        where Occupancy_DT between @analysis_start_date and @analysis_end_date
        and PMA.Property_ID = @property_id
        and Business_Day_End_DT between DATEADD(DAY, -@pace_days, Occupancy_DT) and Occupancy_DT
        and Mkt_Seg_ID = @MktSeg_ID
        group by DATEDIFF(day, Business_Day_End_DT, Occupancy_DT)

    insert #fcst
        select DATEDIFF(day, Business_DT, Occupancy_DT) as daystoArrival,
        SUM(ISNULL(Occupancy_NBR,0)) Occupancy_NBR,
        sum(Revenue) Room_Revenue,
        case sum(Occupancy_NBR)
            when 0 then 0
            else
                sum(Revenue) / sum(Occupancy_NBR)
            end as Room_ADR
        from dbo.PACE_Mkt_Occupancy_FCST POF
        inner join dbo.Decision DE
        on POF.Decision_ID = DE.Decision_ID and
           POF.Property_ID = DE.Property_ID and Mkt_Seg_ID = @MktSeg_ID
            and
           Business_DT between DATEADD(DAY, -@pace_days, Occupancy_DT) and Occupancy_DT AND
           DE.Decision_Type_ID = 1
        where POF.Occupancy_DT between @analysis_start_date and @analysis_end_date
        group by DATEDIFF(day, Business_DT, Occupancy_DT)

    insert #c_sold
        select DATEDIFF(day, Business_Day_End_DT, Occupancy_DT) as daystoArrival,
        sum(Rooms_Sold) comparision_sold,
        sum(Room_Revenue) Room_Revenue,
        case sum(Rooms_Sold)
            when 0 then 0
            else
                sum(Room_Revenue) / sum(Rooms_Sold)
            end as Room_ADR
        from dbo.PACE_Mkt_Activity PMA
        where Occupancy_DT between @comparision_start_date and @comparision_end_date
        and PMA.Property_ID = @property_id
        and Business_Day_End_DT between DATEADD(DAY, -@pace_days, Occupancy_DT) and Occupancy_DT
        and Mkt_Seg_ID = @MktSeg_ID
        group by DATEDIFF(day, Business_Day_End_DT, Occupancy_DT)

    create table #performance_comparision_report_ms
    (
        daystoArrival          int,
        analysis_sold          numeric(18, 0),
        comparision_sold       numeric(18, 0),
        analysis_Occupancy_NBR numeric(8, 2),
        analysis_revenue       numeric(19, 2),
        comparison_revenue     numeric(19, 2),
        analysis_ADR           numeric(19, 2),
        comparison_ADR         numeric(19, 2),
        Forecasted_Revenue     numeric(19, 2),
        Forecasted_ADR         numeric(19, 2),
        mkt_seg_name           nvarchar(150)
    )
    insert into #performance_comparision_report_ms
        select Base.daystoArrival,
        A_SOLD.analysis_sold as analysis_sold ,
        C_SOLD.comparision_sold as comparision_sold,
        FCST.Occupancy_NBR as Occupancy_NBR,
        A_SOLD.Room_Revenue as analysis_Room_Revenue ,
        C_SOLD.Room_Revenue as comparision_revenue,
        A_SOLD.Room_ADR as analysis_ADR ,
        C_SOLD.Room_ADR as comparison_ADR,
        FCST.Room_Revenue as Forecasted_Revenue,
        FCST.Room_ADR as Forecasted_ADR,
        null
        from
        (
            select 0 as daystoArrival
            union
            select cal_sk as daystoArrival
            from dbo.calendar_dim
            where cal_sk <= @pace_days
        ) BASE
            left join
            #a_sold as A_SOLD on BASE.daystoArrival = A_SOLD.daystoArrival
            left join
            #fcst as FCST on BASE.daystoArrival = FCST.daystoArrival
            left join
            #c_sold as C_SOLD on BASE.daystoArrival = C_SOLD.daystoArrival
        order by Base.daystoArrival

    if (@num_days_analysis > 0)
        begin
            update #performance_comparision_report_ms set analysis_sold = null,analysis_Occupancy_NBR = null,analysis_revenue = null,analysis_ADR = null,Forecasted_Revenue = null,Forecasted_ADR = null where daystoArrival < @num_days_analysis
        end

    if (@num_days_comparision > 0)
        begin
            update #performance_comparision_report_ms set comparision_sold = null,comparison_revenue = null,comparison_ADR = null where daystoArrival < @num_days_comparision
        end

    update #performance_comparision_report_ms set mkt_seg_name =
                (select mkt_seg_name from dbo.Mkt_Seg where mkt_seg_ID = @MktSeg_ID)

    select * from #performance_comparision_report_ms order by daystoArrival

    drop table #a_sold
    drop table #fcst
    drop table #c_sold
    drop table #performance_comparision_report_ms
end
go
