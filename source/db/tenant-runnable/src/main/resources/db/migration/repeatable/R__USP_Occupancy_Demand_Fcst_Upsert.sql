DROP PROCEDURE IF EXISTS [dbo].[usp_Occupancy_Demand_FCST_Upsert]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Occupancy_Demand_FCST_Update]
GO
DROP PROCEDURE IF EXISTS [dbo].[usp_Occupancy_Demand_FCST_Insert]
GO

DROP TYPE IF EXISTS [dbo].[Occupancy_Demand_FCST_Batch]
CREATE TYPE dbo.Occupancy_Demand_FCST_Batch AS TABLE
(
    [Decision_ID]           [bigint],
    [Property_ID]           [int],
    [Forecast_Group_ID]     [int],
    [Accom_Class_ID]        [int],
    [Occupancy_DT]            [date],
    [Peak_Demand]           [numeric](18, 2),
    [Remaining_Demand]      [numeric](18, 2),
    [Deviation]             [numeric](18, 2),
    [User_Remaining_Demand] [numeric](18, 2),
    [CreateDate_DTTM]       [datetime],
    [Rate_Unqualified_ID]   [int],
    [Rate_Value]            [numeric](19, 5),
    [Product_ID]            [bigint],
    [Rate_Fcst]             [numeric](19, 5)
);
GO

CREATE PROCEDURE usp_Occupancy_Demand_FCST_Upsert @Occupancy_Demand_FCST_Batch Occupancy_Demand_FCST_Batch READONLY AS
BEGIN
    MERGE INTO [dbo].[Occupancy_Demand_FCST] AS Target
    USING @Occupancy_Demand_FCST_Batch AS Source
    ON (Target.[Forecast_Group_ID] = Source.[Forecast_Group_ID]
        AND Target.[Accom_Class_ID] = Source.[Accom_Class_ID]
        AND Target.[Occupancy_DT] = Source.[Occupancy_DT]
        AND Target.[Property_ID] = Source.[Property_ID])
    WHEN MATCHED THEN
        UPDATE
        SET Target.[Decision_ID]           = Source.[Decision_ID],
            Target.[Property_ID]           = Source.[Property_ID],
            Target.[Peak_Demand]           = Source.[Peak_Demand],
            Target.[Remaining_Demand]      = Source.[Remaining_Demand],
            Target.[Deviation]             = Source.[Deviation],
            Target.[User_Remaining_Demand] = Source.[User_Remaining_Demand],
            Target.[CreateDate_DTTM]       = Source.[CreateDate_DTTM],
            Target.[Rate_Unqualified_ID]   = Source.[Rate_Unqualified_ID],
            Target.[Rate_Value]            = Source.[Rate_Value],
            Target.[Product_ID]            = Source.[Product_ID],
            Target.[Rate_Fcst]             = Source.[Rate_Fcst]

    WHEN NOT MATCHED THEN
        INSERT ( [Decision_ID]
               , [Property_ID]
               , [Forecast_Group_ID]
               , [Accom_Class_ID]
               , [Occupancy_DT]
               , [Peak_Demand]
               , [Remaining_Demand]
               , [Deviation]
               , [User_Remaining_Demand]
               , [CreateDate_DTTM]
               , [Rate_Unqualified_ID]
               , [Rate_Value]
               , [Product_ID]
               , [Rate_Fcst])
        VALUES ( Source.[Decision_ID]
               , Source.[Property_ID]
               , Source.[Forecast_Group_ID]
               , Source.[Accom_Class_ID]
               , Source.[Occupancy_DT]
               , Source.[Peak_Demand]
               , Source.[Remaining_Demand]
               , Source.[Deviation]
               , Source.[User_Remaining_Demand]
               , Source.[CreateDate_DTTM]
               , Source.[Rate_Unqualified_ID]
               , Source.[Rate_Value]
               , Source.[Product_ID]
               , Source.[Rate_Fcst]);
END
GO

CREATE PROCEDURE usp_Occupancy_Demand_FCST_Update @Occupancy_Demand_FCST_Batch Occupancy_Demand_FCST_Batch READONLY AS
BEGIN
UPDATE [dbo].[Occupancy_Demand_FCST] SET
        [Decision_ID]           = Source.[Decision_ID],
        [Property_ID]           = Source.[Property_ID],
        [Peak_Demand]           = Source.[Peak_Demand],
        [Remaining_Demand]      = Source.[Remaining_Demand],
        [Deviation]             = Source.[Deviation],
        [User_Remaining_Demand] = Source.[User_Remaining_Demand],
        [CreateDate_DTTM]       = Source.[CreateDate_DTTM],
        [Rate_Unqualified_ID]   = Source.[Rate_Unqualified_ID],
        [Rate_Value]            = Source.[Rate_Value],
        [Product_ID]            = Source.[Product_ID],
        [Rate_Fcst]             = Source.[Rate_Fcst]
    FROM @Occupancy_Demand_FCST_Batch AS Source
    WHERE (
        [Occupancy_Demand_FCST].[Forecast_Group_ID] = Source.[Forecast_Group_ID] AND
        [Occupancy_Demand_FCST].[Accom_Class_ID] = Source.[Accom_Class_ID] AND
        [Occupancy_Demand_FCST].[Occupancy_DT] = Source.[Occupancy_DT] AND
        [Occupancy_Demand_FCST].[Property_ID] = Source.[Property_ID]
    )
END
GO

CREATE PROCEDURE usp_Occupancy_Demand_FCST_Insert @Occupancy_Demand_FCST_Batch Occupancy_Demand_FCST_Batch READONLY AS
BEGIN
INSERT INTO [dbo].[Occupancy_Demand_FCST] ([Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [Peak_Demand], [Remaining_Demand],
            [Deviation], [User_Remaining_Demand], [CreateDate_DTTM], [Rate_Unqualified_ID], [Rate_Value], [Product_ID], [Rate_Fcst])
SELECT [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT],
        [Peak_Demand], [Remaining_Demand], [Deviation], [User_Remaining_Demand], [CreateDate_DTTM],
        [Rate_Unqualified_ID], [Rate_Value], [Product_ID], [Rate_Fcst]
FROM @Occupancy_Demand_FCST_Batch Occupancy_Demand_FCST_Batch;
END
GO




