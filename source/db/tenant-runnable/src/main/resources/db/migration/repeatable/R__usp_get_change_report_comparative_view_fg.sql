DROP PROCEDURE IF EXISTS [dbo].[usp_get_change_report_comparative_view_fg]
GO

CREATE PROCEDURE [dbo].[usp_get_change_report_comparative_view_fg]
(
	@property_id int,
	@forecast_group_id varchar(500),
	@business_dt date,
	@start_date date,
	@end_date date,
	@isRollingDate int,
	@rolling_business_dt nvarchar(50),
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50)
)
as

begin
		declare @caughtupdate date
		declare @occupancy_dt date
		declare @dow varchar(10)
		declare @fg1_roomsoldcurrent int
		declare @fg1_roomssoldchange int
		declare @fg2_roomsoldcurrent int
		declare @fg2_roomssoldchange int
		declare @fg3_roomsoldcurrent int
		declare @fg3_roomssoldchange int
		declare @fg4_roomsoldcurrent int
		declare @fg4_roomssoldchange int
		declare @fg5_roomsoldcurrent int
		declare @fg5_roomssoldchange int
		declare @fg6_roomsoldcurrent int
		declare @fg6_roomssoldchange int
		declare @fg7_roomsoldcurrent int
		declare @fg7_roomssoldchange int
		declare @fg8_roomsoldcurrent int
		declare @fg8_roomssoldchange int
		declare @fg9_roomsoldcurrent int
		declare @fg9_roomssoldchange int
		declare @fg10_roomsoldcurrent int
		declare @fg10_roomssoldchange int
		declare @fg11_roomsoldcurrent int
		declare @fg11_roomssoldchange int
		declare @fg12_roomsoldcurrent int
		declare @fg12_roomssoldchange int
		declare @fg13_roomsoldcurrent int
		declare @fg13_roomssoldchange int
		declare @fg14_roomsoldcurrent int
		declare @fg14_roomssoldchange int
		declare @fg15_roomsoldcurrent int
		declare @fg15_roomssoldchange int
		declare @fg16_roomsoldcurrent int
		declare @fg16_roomssoldchange int
		declare @fg17_roomsoldcurrent int
		declare @fg17_roomssoldchange int
		declare @fg18_roomsoldcurrent int
		declare @fg18_roomssoldchange int
		declare @fg19_roomsoldcurrent int
		declare @fg19_roomssoldchange int
		declare @fg20_roomsoldcurrent int
		declare @fg20_roomssoldchange int
		declare @fg21_roomsoldcurrent int
		declare @fg21_roomssoldchange int
		declare @fg22_roomsoldcurrent int
		declare @fg22_roomssoldchange int
		declare @fg23_roomsoldcurrent int
		declare @fg23_roomssoldchange int
		declare @fg24_roomsoldcurrent int
		declare @fg24_roomssoldchange int
		declare @fg25_roomsoldcurrent int
		declare @fg25_roomssoldchange int
        declare @fg26_roomsoldcurrent int
        declare @fg26_roomssoldchange int
        declare @fg27_roomsoldcurrent int
        declare @fg27_roomssoldchange int
        declare @fg28_roomsoldcurrent int
        declare @fg28_roomssoldchange int
        declare @fg29_roomsoldcurrent int
        declare @fg29_roomssoldchange int
        declare @fg30_roomsoldcurrent int
        declare @fg30_roomssoldchange int
        declare @fg31_roomsoldcurrent int
        declare @fg31_roomssoldchange int
        declare @fg32_roomsoldcurrent int
        declare @fg32_roomssoldchange int
        declare @fg33_roomsoldcurrent int
        declare @fg33_roomssoldchange int
        declare @fg34_roomsoldcurrent int
        declare @fg34_roomssoldchange int
        declare @fg35_roomsoldcurrent int
        declare @fg35_roomssoldchange int
        declare @fg36_roomsoldcurrent int
        declare @fg36_roomssoldchange int
        declare @fg37_roomsoldcurrent int
        declare @fg37_roomssoldchange int
        declare @fg38_roomsoldcurrent int
        declare @fg38_roomssoldchange int
        declare @fg39_roomsoldcurrent int
        declare @fg39_roomssoldchange int
        declare @fg40_roomsoldcurrent int
        declare @fg40_roomssoldchange int
        declare @fg41_roomsoldcurrent int
        declare @fg41_roomssoldchange int
        declare @fg42_roomsoldcurrent int
        declare @fg42_roomssoldchange int
        declare @fg43_roomsoldcurrent int
        declare @fg43_roomssoldchange int
        declare @fg44_roomsoldcurrent int
        declare @fg44_roomssoldchange int
        declare @fg45_roomsoldcurrent int
        declare @fg45_roomssoldchange int
        declare @fg46_roomsoldcurrent int
        declare @fg46_roomssoldchange int
        declare @fg47_roomsoldcurrent int
        declare @fg47_roomssoldchange int
        declare @fg48_roomsoldcurrent int
        declare @fg48_roomssoldchange int
        declare @fg49_roomsoldcurrent int
        declare @fg49_roomssoldchange int
        declare @fg50_roomsoldcurrent int
        declare @fg50_roomssoldchange int

		declare @fg1_occfcstcurrent numeric(8,2)
		declare @fg1_occfcstchange numeric(8,2)
		declare @fg2_occfcstcurrent numeric(8,2)
		declare @fg2_occfcstchange numeric(8,2)
		declare @fg3_occfcstcurrent numeric(8,2)
		declare @fg3_occfcstchange numeric(8,2)
		declare @fg4_occfcstcurrent numeric(8,2)
		declare @fg4_occfcstchange numeric(8,2)
		declare @fg5_occfcstcurrent numeric(8,2)
		declare @fg5_occfcstchange numeric(8,2)
		declare @fg6_occfcstcurrent numeric(8,2)
		declare @fg6_occfcstchange numeric(8,2)
		declare @fg7_occfcstcurrent numeric(8,2)
		declare @fg7_occfcstchange numeric(8,2)
		declare @fg8_occfcstcurrent numeric(8,2)
		declare @fg8_occfcstchange numeric(8,2)
		declare @fg9_occfcstcurrent numeric(8,2)
		declare @fg9_occfcstchange numeric(8,2)
		declare @fg10_occfcstcurrent numeric(8,2)
		declare @fg10_occfcstchange numeric(8,2)
		declare @fg11_occfcstcurrent numeric(8,2)
		declare @fg11_occfcstchange numeric(8,2)
		declare @fg12_occfcstcurrent numeric(8,2)
		declare @fg12_occfcstchange numeric(8,2)
		declare @fg13_occfcstcurrent numeric(8,2)
		declare @fg13_occfcstchange numeric(8,2)
		declare @fg14_occfcstcurrent numeric(8,2)
		declare @fg14_occfcstchange numeric(8,2)
		declare @fg15_occfcstcurrent numeric(8,2)
		declare @fg15_occfcstchange numeric(8,2)
		declare @fg16_occfcstcurrent numeric(8,2)
		declare @fg16_occfcstchange numeric(8,2)
		declare @fg17_occfcstcurrent numeric(8,2)
		declare @fg17_occfcstchange numeric(8,2)
		declare @fg18_occfcstcurrent numeric(8,2)
		declare @fg18_occfcstchange numeric(8,2)
		declare @fg19_occfcstcurrent numeric(8,2)
		declare @fg19_occfcstchange numeric(8,2)
		declare @fg20_occfcstcurrent numeric(8,2)
		declare @fg20_occfcstchange numeric(8,2)
		declare @fg21_occfcstcurrent numeric(8,2)
		declare @fg21_occfcstchange numeric(8,2)
		declare @fg22_occfcstcurrent numeric(8,2)
		declare @fg22_occfcstchange numeric(8,2)
		declare @fg23_occfcstcurrent numeric(8,2)
		declare @fg23_occfcstchange numeric(8,2)
		declare @fg24_occfcstcurrent numeric(8,2)
		declare @fg24_occfcstchange numeric(8,2)
		declare @fg25_occfcstcurrent numeric(8,2)
		declare @fg25_occfcstchange numeric(8,2)
        declare @fg26_occfcstcurrent numeric(8,2)
        declare @fg26_occfcstchange numeric(8,2)
        declare @fg27_occfcstcurrent numeric(8,2)
        declare @fg27_occfcstchange numeric(8,2)
        declare @fg28_occfcstcurrent numeric(8,2)
        declare @fg28_occfcstchange numeric(8,2)
        declare @fg29_occfcstcurrent numeric(8,2)
        declare @fg29_occfcstchange numeric(8,2)
        declare @fg30_occfcstcurrent numeric(8,2)
        declare @fg30_occfcstchange numeric(8,2)
        declare @fg31_occfcstcurrent numeric(8,2)
        declare @fg31_occfcstchange numeric(8,2)
        declare @fg32_occfcstcurrent numeric(8,2)
        declare @fg32_occfcstchange numeric(8,2)
        declare @fg33_occfcstcurrent numeric(8,2)
        declare @fg33_occfcstchange numeric(8,2)
        declare @fg34_occfcstcurrent numeric(8,2)
        declare @fg34_occfcstchange numeric(8,2)
        declare @fg35_occfcstcurrent numeric(8,2)
        declare @fg35_occfcstchange numeric(8,2)
        declare @fg36_occfcstcurrent numeric(8,2)
        declare @fg36_occfcstchange numeric(8,2)
        declare @fg37_occfcstcurrent numeric(8,2)
        declare @fg37_occfcstchange numeric(8,2)
        declare @fg38_occfcstcurrent numeric(8,2)
        declare @fg38_occfcstchange numeric(8,2)
        declare @fg39_occfcstcurrent numeric(8,2)
        declare @fg39_occfcstchange numeric(8,2)
        declare @fg40_occfcstcurrent numeric(8,2)
        declare @fg40_occfcstchange numeric(8,2)
        declare @fg41_occfcstcurrent numeric(8,2)
        declare @fg41_occfcstchange numeric(8,2)
        declare @fg42_occfcstcurrent numeric(8,2)
        declare @fg42_occfcstchange numeric(8,2)
        declare @fg43_occfcstcurrent numeric(8,2)
        declare @fg43_occfcstchange numeric(8,2)
        declare @fg44_occfcstcurrent numeric(8,2)
        declare @fg44_occfcstchange numeric(8,2)
        declare @fg45_occfcstcurrent numeric(8,2)
        declare @fg45_occfcstchange numeric(8,2)
        declare @fg46_occfcstcurrent numeric(8,2)
        declare @fg46_occfcstchange numeric(8,2)
        declare @fg47_occfcstcurrent numeric(8,2)
        declare @fg47_occfcstchange numeric(8,2)
        declare @fg48_occfcstcurrent numeric(8,2)
        declare @fg48_occfcstchange numeric(8,2)
        declare @fg49_occfcstcurrent numeric(8,2)
        declare @fg49_occfcstchange numeric(8,2)
        declare @fg50_occfcstcurrent numeric(8,2)
        declare @fg50_occfcstchange numeric(8,2)

		declare @fg1_bookedroomrevenuecurrent numeric(19,5)
		declare @fg1_bookedroomrevenuechange numeric(19,5)
		declare @fg2_bookedroomrevenuecurrent numeric(19,5)
		declare @fg2_bookedroomrevenuechange numeric(19,5)
		declare @fg3_bookedroomrevenuecurrent numeric(19,5)
		declare @fg3_bookedroomrevenuechange numeric(19,5)
		declare @fg4_bookedroomrevenuecurrent numeric(19,5)
		declare @fg4_bookedroomrevenuechange numeric(19,5)
		declare @fg5_bookedroomrevenuecurrent numeric(19,5)
		declare @fg5_bookedroomrevenuechange numeric(19,5)
		declare @fg6_bookedroomrevenuecurrent numeric(19,5)
		declare @fg6_bookedroomrevenuechange numeric(19,5)
		declare @fg7_bookedroomrevenuecurrent numeric(19,5)
		declare @fg7_bookedroomrevenuechange numeric(19,5)
		declare @fg8_bookedroomrevenuecurrent numeric(19,5)
		declare @fg8_bookedroomrevenuechange numeric(19,5)
		declare @fg9_bookedroomrevenuecurrent numeric(19,5)
		declare @fg9_bookedroomrevenuechange numeric(19,5)
		declare @fg10_bookedroomrevenuecurrent numeric(19,5)
		declare @fg10_bookedroomrevenuechange numeric(19,5)
		declare @fg11_bookedroomrevenuecurrent numeric(19,5)
		declare @fg11_bookedroomrevenuechange numeric(19,5)
		declare @fg12_bookedroomrevenuecurrent numeric(19,5)
		declare @fg12_bookedroomrevenuechange numeric(19,5)
		declare @fg13_bookedroomrevenuecurrent numeric(19,5)
		declare @fg13_bookedroomrevenuechange numeric(19,5)
		declare @fg14_bookedroomrevenuecurrent numeric(19,5)
		declare @fg14_bookedroomrevenuechange numeric(19,5)
		declare @fg15_bookedroomrevenuecurrent numeric(19,5)
		declare @fg15_bookedroomrevenuechange numeric(19,5)
		declare @fg16_bookedroomrevenuecurrent numeric(19,5)
		declare @fg16_bookedroomrevenuechange numeric(19,5)
		declare @fg17_bookedroomrevenuecurrent numeric(19,5)
		declare @fg17_bookedroomrevenuechange numeric(19,5)
		declare @fg18_bookedroomrevenuecurrent numeric(19,5)
		declare @fg18_bookedroomrevenuechange numeric(19,5)
		declare @fg19_bookedroomrevenuecurrent numeric(19,5)
		declare @fg19_bookedroomrevenuechange numeric(19,5)
		declare @fg20_bookedroomrevenuecurrent numeric(19,5)
		declare @fg20_bookedroomrevenuechange numeric(19,5)
		declare @fg21_bookedroomrevenuecurrent numeric(19,5)
		declare @fg21_bookedroomrevenuechange numeric(19,5)
		declare @fg22_bookedroomrevenuecurrent numeric(19,5)
		declare @fg22_bookedroomrevenuechange numeric(19,5)
		declare @fg23_bookedroomrevenuecurrent numeric(19,5)
		declare @fg23_bookedroomrevenuechange numeric(19,5)
		declare @fg24_bookedroomrevenuecurrent numeric(19,5)
		declare @fg24_bookedroomrevenuechange numeric(19,5)
		declare @fg25_bookedroomrevenuecurrent numeric(19,5)
		declare @fg25_bookedroomrevenuechange numeric(19,5)
        declare @fg26_bookedroomrevenuecurrent numeric(19,5)
        declare @fg26_bookedroomrevenuechange numeric(19,5)
        declare @fg27_bookedroomrevenuecurrent numeric(19,5)
        declare @fg27_bookedroomrevenuechange numeric(19,5)
        declare @fg28_bookedroomrevenuecurrent numeric(19,5)
        declare @fg28_bookedroomrevenuechange numeric(19,5)
        declare @fg29_bookedroomrevenuecurrent numeric(19,5)
        declare @fg29_bookedroomrevenuechange numeric(19,5)
        declare @fg30_bookedroomrevenuecurrent numeric(19,5)
        declare @fg30_bookedroomrevenuechange numeric(19,5)
        declare @fg31_bookedroomrevenuecurrent numeric(19,5)
        declare @fg31_bookedroomrevenuechange numeric(19,5)
        declare @fg32_bookedroomrevenuecurrent numeric(19,5)
        declare @fg32_bookedroomrevenuechange numeric(19,5)
        declare @fg33_bookedroomrevenuecurrent numeric(19,5)
        declare @fg33_bookedroomrevenuechange numeric(19,5)
        declare @fg34_bookedroomrevenuecurrent numeric(19,5)
        declare @fg34_bookedroomrevenuechange numeric(19,5)
        declare @fg35_bookedroomrevenuecurrent numeric(19,5)
        declare @fg35_bookedroomrevenuechange numeric(19,5)
        declare @fg36_bookedroomrevenuecurrent numeric(19,5)
        declare @fg36_bookedroomrevenuechange numeric(19,5)
        declare @fg37_bookedroomrevenuecurrent numeric(19,5)
        declare @fg37_bookedroomrevenuechange numeric(19,5)
        declare @fg38_bookedroomrevenuecurrent numeric(19,5)
        declare @fg38_bookedroomrevenuechange numeric(19,5)
        declare @fg39_bookedroomrevenuecurrent numeric(19,5)
        declare @fg39_bookedroomrevenuechange numeric(19,5)
        declare @fg40_bookedroomrevenuecurrent numeric(19,5)
        declare @fg40_bookedroomrevenuechange numeric(19,5)
        declare @fg41_bookedroomrevenuecurrent numeric(19,5)
        declare @fg41_bookedroomrevenuechange numeric(19,5)
        declare @fg42_bookedroomrevenuecurrent numeric(19,5)
        declare @fg42_bookedroomrevenuechange numeric(19,5)
        declare @fg43_bookedroomrevenuecurrent numeric(19,5)
        declare @fg43_bookedroomrevenuechange numeric(19,5)
        declare @fg44_bookedroomrevenuecurrent numeric(19,5)
        declare @fg44_bookedroomrevenuechange numeric(19,5)
        declare @fg45_bookedroomrevenuecurrent numeric(19,5)
        declare @fg45_bookedroomrevenuechange numeric(19,5)
        declare @fg46_bookedroomrevenuecurrent numeric(19,5)
        declare @fg46_bookedroomrevenuechange numeric(19,5)
        declare @fg47_bookedroomrevenuecurrent numeric(19,5)
        declare @fg47_bookedroomrevenuechange numeric(19,5)
        declare @fg48_bookedroomrevenuecurrent numeric(19,5)
        declare @fg48_bookedroomrevenuechange numeric(19,5)
        declare @fg49_bookedroomrevenuecurrent numeric(19,5)
        declare @fg49_bookedroomrevenuechange numeric(19,5)
        declare @fg50_bookedroomrevenuecurrent numeric(19,5)
        declare @fg50_bookedroomrevenuechange numeric(19,5)

		declare @fg1_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg1_fcstedroomrevenuechange numeric(19,5)
		declare @fg2_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg2_fcstedroomrevenuechange numeric(19,5)
		declare @fg3_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg3_fcstedroomrevenuechange numeric(19,5)
		declare @fg4_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg4_fcstedroomrevenuechange numeric(19,5)
		declare @fg5_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg5_fcstedroomrevenuechange numeric(19,5)
		declare @fg6_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg6_fcstedroomrevenuechange numeric(19,5)
		declare @fg7_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg7_fcstedroomrevenuechange numeric(19,5)
		declare @fg8_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg8_fcstedroomrevenuechange numeric(19,5)
		declare @fg9_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg9_fcstedroomrevenuechange numeric(19,5)
		declare @fg10_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg10_fcstedroomrevenuechange numeric(19,5)
		declare @fg11_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg11_fcstedroomrevenuechange numeric(19,5)
		declare @fg12_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg12_fcstedroomrevenuechange numeric(19,5)
		declare @fg13_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg13_fcstedroomrevenuechange numeric(19,5)
		declare @fg14_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg14_fcstedroomrevenuechange numeric(19,5)
		declare @fg15_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg15_fcstedroomrevenuechange numeric(19,5)
		declare @fg16_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg16_fcstedroomrevenuechange numeric(19,5)
		declare @fg17_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg17_fcstedroomrevenuechange numeric(19,5)
		declare @fg18_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg18_fcstedroomrevenuechange numeric(19,5)
		declare @fg19_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg19_fcstedroomrevenuechange numeric(19,5)
		declare @fg20_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg20_fcstedroomrevenuechange numeric(19,5)
		declare @fg21_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg21_fcstedroomrevenuechange numeric(19,5)
		declare @fg22_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg22_fcstedroomrevenuechange numeric(19,5)
		declare @fg23_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg23_fcstedroomrevenuechange numeric(19,5)
		declare @fg24_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg24_fcstedroomrevenuechange numeric(19,5)
		declare @fg25_fcstedroomrevenuecurrent numeric(19,5)
		declare @fg25_fcstedroomrevenuechange numeric(19,5)
        declare @fg26_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg26_fcstedroomrevenuechange numeric(19,5)
        declare @fg27_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg27_fcstedroomrevenuechange numeric(19,5)
        declare @fg28_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg28_fcstedroomrevenuechange numeric(19,5)
        declare @fg29_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg29_fcstedroomrevenuechange numeric(19,5)
        declare @fg30_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg30_fcstedroomrevenuechange numeric(19,5)
        declare @fg31_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg31_fcstedroomrevenuechange numeric(19,5)
        declare @fg32_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg32_fcstedroomrevenuechange numeric(19,5)
        declare @fg33_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg33_fcstedroomrevenuechange numeric(19,5)
        declare @fg34_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg34_fcstedroomrevenuechange numeric(19,5)
        declare @fg35_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg35_fcstedroomrevenuechange numeric(19,5)
        declare @fg36_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg36_fcstedroomrevenuechange numeric(19,5)
        declare @fg37_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg37_fcstedroomrevenuechange numeric(19,5)
        declare @fg38_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg38_fcstedroomrevenuechange numeric(19,5)
        declare @fg39_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg39_fcstedroomrevenuechange numeric(19,5)
        declare @fg40_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg40_fcstedroomrevenuechange numeric(19,5)
        declare @fg41_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg41_fcstedroomrevenuechange numeric(19,5)
        declare @fg42_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg42_fcstedroomrevenuechange numeric(19,5)
        declare @fg43_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg43_fcstedroomrevenuechange numeric(19,5)
        declare @fg44_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg44_fcstedroomrevenuechange numeric(19,5)
        declare @fg45_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg45_fcstedroomrevenuechange numeric(19,5)
        declare @fg46_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg46_fcstedroomrevenuechange numeric(19,5)
        declare @fg47_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg47_fcstedroomrevenuechange numeric(19,5)
        declare @fg48_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg48_fcstedroomrevenuechange numeric(19,5)
        declare @fg49_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg49_fcstedroomrevenuechange numeric(19,5)
        declare @fg50_fcstedroomrevenuecurrent numeric(19,5)
        declare @fg50_fcstedroomrevenuechange numeric(19,5)

		declare @fg1_bookedadrcurrent numeric(19,5)
		declare @fg1_bookedadrchange numeric(19,5)
		declare @fg2_bookedadrcurrent numeric(19,5)
		declare @fg2_bookedadrchange numeric(19,5)
		declare @fg3_bookedadrcurrent numeric(19,5)
		declare @fg3_bookedadrchange numeric(19,5)
		declare @fg4_bookedadrcurrent numeric(19,5)
		declare @fg4_bookedadrchange numeric(19,5)
		declare @fg5_bookedadrcurrent numeric(19,5)
		declare @fg5_bookedadrchange numeric(19,5)
		declare @fg6_bookedadrcurrent numeric(19,5)
		declare @fg6_bookedadrchange numeric(19,5)
		declare @fg7_bookedadrcurrent numeric(19,5)
		declare @fg7_bookedadrchange numeric(19,5)
		declare @fg8_bookedadrcurrent numeric(19,5)
		declare @fg8_bookedadrchange numeric(19,5)
		declare @fg9_bookedadrcurrent numeric(19,5)
		declare @fg9_bookedadrchange numeric(19,5)
		declare @fg10_bookedadrcurrent numeric(19,5)
		declare @fg10_bookedadrchange numeric(19,5)
		declare @fg11_bookedadrcurrent numeric(19,5)
		declare @fg11_bookedadrchange numeric(19,5)
		declare @fg12_bookedadrcurrent numeric(19,5)
		declare @fg12_bookedadrchange numeric(19,5)
		declare @fg13_bookedadrcurrent numeric(19,5)
		declare @fg13_bookedadrchange numeric(19,5)
		declare @fg14_bookedadrcurrent numeric(19,5)
		declare @fg14_bookedadrchange numeric(19,5)
		declare @fg15_bookedadrcurrent numeric(19,5)
		declare @fg15_bookedadrchange numeric(19,5)
		declare @fg16_bookedadrcurrent numeric(19,5)
		declare @fg16_bookedadrchange numeric(19,5)
		declare @fg17_bookedadrcurrent numeric(19,5)
		declare @fg17_bookedadrchange numeric(19,5)
		declare @fg18_bookedadrcurrent numeric(19,5)
		declare @fg18_bookedadrchange numeric(19,5)
		declare @fg19_bookedadrcurrent numeric(19,5)
		declare @fg19_bookedadrchange numeric(19,5)
		declare @fg20_bookedadrcurrent numeric(19,5)
		declare @fg20_bookedadrchange numeric(19,5)
		declare @fg21_bookedadrcurrent numeric(19,5)
		declare @fg21_bookedadrchange numeric(19,5)
		declare @fg22_bookedadrcurrent numeric(19,5)
		declare @fg22_bookedadrchange numeric(19,5)
		declare @fg23_bookedadrcurrent numeric(19,5)
		declare @fg23_bookedadrchange numeric(19,5)
		declare @fg24_bookedadrcurrent numeric(19,5)
		declare @fg24_bookedadrchange numeric(19,5)
		declare @fg25_bookedadrcurrent numeric(19,5)
		declare @fg25_bookedadrchange numeric(19,5)
        declare @fg26_bookedadrcurrent numeric(19,5)
        declare @fg26_bookedadrchange numeric(19,5)
        declare @fg27_bookedadrcurrent numeric(19,5)
        declare @fg27_bookedadrchange numeric(19,5)
        declare @fg28_bookedadrcurrent numeric(19,5)
        declare @fg28_bookedadrchange numeric(19,5)
        declare @fg29_bookedadrcurrent numeric(19,5)
        declare @fg29_bookedadrchange numeric(19,5)
        declare @fg30_bookedadrcurrent numeric(19,5)
        declare @fg30_bookedadrchange numeric(19,5)
        declare @fg31_bookedadrcurrent numeric(19,5)
        declare @fg31_bookedadrchange numeric(19,5)
        declare @fg32_bookedadrcurrent numeric(19,5)
        declare @fg32_bookedadrchange numeric(19,5)
        declare @fg33_bookedadrcurrent numeric(19,5)
        declare @fg33_bookedadrchange numeric(19,5)
        declare @fg34_bookedadrcurrent numeric(19,5)
        declare @fg34_bookedadrchange numeric(19,5)
        declare @fg35_bookedadrcurrent numeric(19,5)
        declare @fg35_bookedadrchange numeric(19,5)
        declare @fg36_bookedadrcurrent numeric(19,5)
        declare @fg36_bookedadrchange numeric(19,5)
        declare @fg37_bookedadrcurrent numeric(19,5)
        declare @fg37_bookedadrchange numeric(19,5)
        declare @fg38_bookedadrcurrent numeric(19,5)
        declare @fg38_bookedadrchange numeric(19,5)
        declare @fg39_bookedadrcurrent numeric(19,5)
        declare @fg39_bookedadrchange numeric(19,5)
        declare @fg40_bookedadrcurrent numeric(19,5)
        declare @fg40_bookedadrchange numeric(19,5)
        declare @fg41_bookedadrcurrent numeric(19,5)
        declare @fg41_bookedadrchange numeric(19,5)
        declare @fg42_bookedadrcurrent numeric(19,5)
        declare @fg42_bookedadrchange numeric(19,5)
        declare @fg43_bookedadrcurrent numeric(19,5)
        declare @fg43_bookedadrchange numeric(19,5)
        declare @fg44_bookedadrcurrent numeric(19,5)
        declare @fg44_bookedadrchange numeric(19,5)
        declare @fg45_bookedadrcurrent numeric(19,5)
        declare @fg45_bookedadrchange numeric(19,5)
        declare @fg46_bookedadrcurrent numeric(19,5)
        declare @fg46_bookedadrchange numeric(19,5)
        declare @fg47_bookedadrcurrent numeric(19,5)
        declare @fg47_bookedadrchange numeric(19,5)
        declare @fg48_bookedadrcurrent numeric(19,5)
        declare @fg48_bookedadrchange numeric(19,5)
        declare @fg49_bookedadrcurrent numeric(19,5)
        declare @fg49_bookedadrchange numeric(19,5)
        declare @fg50_bookedadrcurrent numeric(19,5)
        declare @fg50_bookedadrchange numeric(19,5)

		declare @fg1_fcstedadrcurrent numeric(19,5)
		declare @fg1_fcstedadrchange numeric(19,5)
		declare @fg2_fcstedadrcurrent numeric(19,5)
		declare @fg2_fcstedadrchange numeric(19,5)
		declare @fg3_fcstedadrcurrent numeric(19,5)
		declare @fg3_fcstedadrchange numeric(19,5)
		declare @fg4_fcstedadrcurrent numeric(19,5)
		declare @fg4_fcstedadrchange numeric(19,5)
		declare @fg5_fcstedadrcurrent numeric(19,5)
		declare @fg5_fcstedadrchange numeric(19,5)
		declare @fg6_fcstedadrcurrent numeric(19,5)
		declare @fg6_fcstedadrchange numeric(19,5)
		declare @fg7_fcstedadrcurrent numeric(19,5)
		declare @fg7_fcstedadrchange numeric(19,5)
		declare @fg8_fcstedadrcurrent numeric(19,5)
		declare @fg8_fcstedadrchange numeric(19,5)
		declare @fg9_fcstedadrcurrent numeric(19,5)
		declare @fg9_fcstedadrchange numeric(19,5)
		declare @fg10_fcstedadrcurrent numeric(19,5)
		declare @fg10_fcstedadrchange numeric(19,5)
		declare @fg11_fcstedadrcurrent numeric(19,5)
		declare @fg11_fcstedadrchange numeric(19,5)
		declare @fg12_fcstedadrcurrent numeric(19,5)
		declare @fg12_fcstedadrchange numeric(19,5)
		declare @fg13_fcstedadrcurrent numeric(19,5)
		declare @fg13_fcstedadrchange numeric(19,5)
		declare @fg14_fcstedadrcurrent numeric(19,5)
		declare @fg14_fcstedadrchange numeric(19,5)
		declare @fg15_fcstedadrcurrent numeric(19,5)
		declare @fg15_fcstedadrchange numeric(19,5)
		declare @fg16_fcstedadrcurrent numeric(19,5)
		declare @fg16_fcstedadrchange numeric(19,5)
		declare @fg17_fcstedadrcurrent numeric(19,5)
		declare @fg17_fcstedadrchange numeric(19,5)
		declare @fg18_fcstedadrcurrent numeric(19,5)
		declare @fg18_fcstedadrchange numeric(19,5)
		declare @fg19_fcstedadrcurrent numeric(19,5)
		declare @fg19_fcstedadrchange numeric(19,5)
		declare @fg20_fcstedadrcurrent numeric(19,5)
		declare @fg20_fcstedadrchange numeric(19,5)
		declare @fg21_fcstedadrcurrent numeric(19,5)
		declare @fg21_fcstedadrchange numeric(19,5)
		declare @fg22_fcstedadrcurrent numeric(19,5)
		declare @fg22_fcstedadrchange numeric(19,5)
		declare @fg23_fcstedadrcurrent numeric(19,5)
		declare @fg23_fcstedadrchange numeric(19,5)
		declare @fg24_fcstedadrcurrent numeric(19,5)
		declare @fg24_fcstedadrchange numeric(19,5)
		declare @fg25_fcstedadrcurrent numeric(19,5)
		declare @fg25_fcstedadrchange numeric(19,5)
        declare @fg26_fcstedadrcurrent numeric(19,5)
        declare @fg26_fcstedadrchange numeric(19,5)
        declare @fg27_fcstedadrcurrent numeric(19,5)
        declare @fg27_fcstedadrchange numeric(19,5)
        declare @fg28_fcstedadrcurrent numeric(19,5)
        declare @fg28_fcstedadrchange numeric(19,5)
        declare @fg29_fcstedadrcurrent numeric(19,5)
        declare @fg29_fcstedadrchange numeric(19,5)
        declare @fg30_fcstedadrcurrent numeric(19,5)
        declare @fg30_fcstedadrchange numeric(19,5)
        declare @fg31_fcstedadrcurrent numeric(19,5)
        declare @fg31_fcstedadrchange numeric(19,5)
        declare @fg32_fcstedadrcurrent numeric(19,5)
        declare @fg32_fcstedadrchange numeric(19,5)
        declare @fg33_fcstedadrcurrent numeric(19,5)
        declare @fg33_fcstedadrchange numeric(19,5)
        declare @fg34_fcstedadrcurrent numeric(19,5)
        declare @fg34_fcstedadrchange numeric(19,5)
        declare @fg35_fcstedadrcurrent numeric(19,5)
        declare @fg35_fcstedadrchange numeric(19,5)
        declare @fg36_fcstedadrcurrent numeric(19,5)
        declare @fg36_fcstedadrchange numeric(19,5)
        declare @fg37_fcstedadrcurrent numeric(19,5)
        declare @fg37_fcstedadrchange numeric(19,5)
        declare @fg38_fcstedadrcurrent numeric(19,5)
        declare @fg38_fcstedadrchange numeric(19,5)
        declare @fg39_fcstedadrcurrent numeric(19,5)
        declare @fg39_fcstedadrchange numeric(19,5)
        declare @fg40_fcstedadrcurrent numeric(19,5)
        declare @fg40_fcstedadrchange numeric(19,5)
        declare @fg41_fcstedadrcurrent numeric(19,5)
        declare @fg41_fcstedadrchange numeric(19,5)
        declare @fg42_fcstedadrcurrent numeric(19,5)
        declare @fg42_fcstedadrchange numeric(19,5)
        declare @fg43_fcstedadrcurrent numeric(19,5)
        declare @fg43_fcstedadrchange numeric(19,5)
        declare @fg44_fcstedadrcurrent numeric(19,5)
        declare @fg44_fcstedadrchange numeric(19,5)
        declare @fg45_fcstedadrcurrent numeric(19,5)
        declare @fg45_fcstedadrchange numeric(19,5)
        declare @fg46_fcstedadrcurrent numeric(19,5)
        declare @fg46_fcstedadrchange numeric(19,5)
        declare @fg47_fcstedadrcurrent numeric(19,5)
        declare @fg47_fcstedadrchange numeric(19,5)
        declare @fg48_fcstedadrcurrent numeric(19,5)
        declare @fg48_fcstedadrchange numeric(19,5)
        declare @fg49_fcstedadrcurrent numeric(19,5)
        declare @fg49_fcstedadrchange numeric(19,5)
        declare @fg50_fcstedadrcurrent numeric(19,5)
        declare @fg50_fcstedadrchange numeric(19,5)

		declare @fg1_block int
		declare @fg1_block_available int
		declare @fg1_block_pickup int

		declare @fg2_block int
		declare @fg2_block_available int
		declare @fg2_block_pickup int

		declare @fg3_block int
		declare @fg3_block_available int
		declare @fg3_block_pickup int

		declare @fg4_block int
		declare @fg4_block_available int
		declare @fg4_block_pickup int

		declare @fg5_block int
		declare @fg5_block_available int
		declare @fg5_block_pickup int

		declare @fg6_block int
		declare @fg6_block_available int
		declare @fg6_block_pickup int

		declare @fg7_block int
		declare @fg7_block_available int
		declare @fg7_block_pickup int

		declare @fg8_block int
		declare @fg8_block_available int
		declare @fg8_block_pickup int

		declare @fg9_block int
		declare @fg9_block_available int
		declare @fg9_block_pickup int

		declare @fg10_block int
		declare @fg10_block_available int
		declare @fg10_block_pickup int

		declare @fg11_block int
		declare @fg11_block_available int
		declare @fg11_block_pickup int

		declare @fg12_block int
		declare @fg12_block_available int
		declare @fg12_block_pickup int

		declare @fg13_block int
		declare @fg13_block_available int
		declare @fg13_block_pickup int

		declare @fg14_block int
		declare @fg14_block_available int
		declare @fg14_block_pickup int

		declare @fg15_block int
		declare @fg15_block_available int
		declare @fg15_block_pickup int

		declare @fg16_block int
		declare @fg16_block_available int
		declare @fg16_block_pickup int

		declare @fg17_block int
		declare @fg17_block_available int
		declare @fg17_block_pickup int

		declare @fg18_block int
		declare @fg18_block_available int
		declare @fg18_block_pickup int

		declare @fg19_block int
		declare @fg19_block_available int
		declare @fg19_block_pickup int

		declare @fg20_block int
		declare @fg20_block_available int
		declare @fg20_block_pickup int

		declare @fg21_block int
		declare @fg21_block_available int
		declare @fg21_block_pickup int

		declare @fg22_block int
		declare @fg22_block_available int
		declare @fg22_block_pickup int

		declare @fg23_block int
		declare @fg23_block_available int
		declare @fg23_block_pickup int

		declare @fg24_block int
		declare @fg24_block_available int
		declare @fg24_block_pickup int

		declare @fg25_block int
		declare @fg25_block_available int
		declare @fg25_block_pickup int

		declare @fg26_block int
        declare @fg26_block_available int
        declare @fg26_block_pickup int

        declare @fg27_block int
        declare @fg27_block_available int
        declare @fg27_block_pickup int

        declare @fg28_block int
        declare @fg28_block_available int
        declare @fg28_block_pickup int

        declare @fg29_block int
        declare @fg29_block_available int
        declare @fg29_block_pickup int

        declare @fg30_block int
        declare @fg30_block_available int
        declare @fg30_block_pickup int

        declare @fg31_block int
        declare @fg31_block_available int
        declare @fg31_block_pickup int

        declare @fg32_block int
        declare @fg32_block_available int
        declare @fg32_block_pickup int

        declare @fg33_block int
        declare @fg33_block_available int
        declare @fg33_block_pickup int

        declare @fg34_block int
        declare @fg34_block_available int
        declare @fg34_block_pickup int

        declare @fg35_block int
        declare @fg35_block_available int
        declare @fg35_block_pickup int

        declare @fg36_block int
        declare @fg36_block_available int
        declare @fg36_block_pickup int

        declare @fg37_block int
        declare @fg37_block_available int
        declare @fg37_block_pickup int

        declare @fg38_block int
        declare @fg38_block_available int
        declare @fg38_block_pickup int

        declare @fg39_block int
        declare @fg39_block_available int
        declare @fg39_block_pickup int

        declare @fg40_block int
        declare @fg40_block_available int
        declare @fg40_block_pickup int

        declare @fg41_block int
        declare @fg41_block_available int
        declare @fg41_block_pickup int

        declare @fg42_block int
        declare @fg42_block_available int
        declare @fg42_block_pickup int

        declare @fg43_block int
        declare @fg43_block_available int
        declare @fg43_block_pickup int

        declare @fg44_block int
        declare @fg44_block_available int
        declare @fg44_block_pickup int

        declare @fg45_block int
        declare @fg45_block_available int
        declare @fg45_block_pickup int

        declare @fg46_block int
        declare @fg46_block_available int
        declare @fg46_block_pickup int

        declare @fg47_block int
        declare @fg47_block_available int
        declare @fg47_block_pickup int

        declare @fg48_block int
        declare @fg48_block_available int
        declare @fg48_block_pickup int

        declare @fg49_block int
        declare @fg49_block_available int
        declare @fg49_block_pickup int

        declare @fg50_block int
        declare @fg50_block_available int
        declare @fg50_block_pickup int
		declare @fg1_profit_onBooks_current numeric(19,5)
		declare @fg1_profit_onBooks_change numeric(19,5)
		declare @fg2_profit_onBooks_current numeric(19,5)
		declare @fg2_profit_onBooks_change numeric(19,5)
		declare @fg3_profit_onBooks_current numeric(19,5)
		declare @fg3_profit_onBooks_change numeric(19,5)
		declare @fg4_profit_onBooks_current numeric(19,5)
		declare @fg4_profit_onBooks_change numeric(19,5)
		declare @fg5_profit_onBooks_current numeric(19,5)
		declare @fg5_profit_onBooks_change numeric(19,5)
		declare @fg6_profit_onBooks_current numeric(19,5)
		declare @fg6_profit_onBooks_change numeric(19,5)
		declare @fg7_profit_onBooks_current numeric(19,5)
		declare @fg7_profit_onBooks_change numeric(19,5)
		declare @fg8_profit_onBooks_current numeric(19,5)
		declare @fg8_profit_onBooks_change numeric(19,5)
		declare @fg9_profit_onBooks_current numeric(19,5)
		declare @fg9_profit_onBooks_change numeric(19,5)
		declare @fg10_profit_onBooks_current numeric(19,5)
		declare @fg10_profit_onBooks_change numeric(19,5)
		declare @fg11_profit_onBooks_current numeric(19,5)
		declare @fg11_profit_onBooks_change numeric(19,5)
		declare @fg12_profit_onBooks_current numeric(19,5)
		declare @fg12_profit_onBooks_change numeric(19,5)
		declare @fg13_profit_onBooks_current numeric(19,5)
		declare @fg13_profit_onBooks_change numeric(19,5)
		declare @fg14_profit_onBooks_current numeric(19,5)
		declare @fg14_profit_onBooks_change numeric(19,5)
		declare @fg15_profit_onBooks_current numeric(19,5)
		declare @fg15_profit_onBooks_change numeric(19,5)
		declare @fg16_profit_onBooks_current numeric(19,5)
		declare @fg16_profit_onBooks_change numeric(19,5)
		declare @fg17_profit_onBooks_current numeric(19,5)
		declare @fg17_profit_onBooks_change numeric(19,5)
		declare @fg18_profit_onBooks_current numeric(19,5)
		declare @fg18_profit_onBooks_change numeric(19,5)
		declare @fg19_profit_onBooks_current numeric(19,5)
		declare @fg19_profit_onBooks_change numeric(19,5)
		declare @fg20_profit_onBooks_current numeric(19,5)
		declare @fg20_profit_onBooks_change numeric(19,5)
		declare @fg21_profit_onBooks_current numeric(19,5)
		declare @fg21_profit_onBooks_change numeric(19,5)
		declare @fg22_profit_onBooks_current numeric(19,5)
		declare @fg22_profit_onBooks_change numeric(19,5)
		declare @fg23_profit_onBooks_current numeric(19,5)
		declare @fg23_profit_onBooks_change numeric(19,5)
		declare @fg24_profit_onBooks_current numeric(19,5)
		declare @fg24_profit_onBooks_change numeric(19,5)
		declare @fg25_profit_onBooks_current numeric(19,5)
		declare @fg25_profit_onBooks_change numeric(19,5)
        declare @fg26_profit_onBooks_current numeric(19,5)
        declare @fg26_profit_onBooks_change numeric(19,5)
        declare @fg27_profit_onBooks_current numeric(19,5)
        declare @fg27_profit_onBooks_change numeric(19,5)
        declare @fg28_profit_onBooks_current numeric(19,5)
        declare @fg28_profit_onBooks_change numeric(19,5)
        declare @fg29_profit_onBooks_current numeric(19,5)
        declare @fg29_profit_onBooks_change numeric(19,5)
        declare @fg30_profit_onBooks_current numeric(19,5)
        declare @fg30_profit_onBooks_change numeric(19,5)
        declare @fg31_profit_onBooks_current numeric(19,5)
        declare @fg31_profit_onBooks_change numeric(19,5)
        declare @fg32_profit_onBooks_current numeric(19,5)
        declare @fg32_profit_onBooks_change numeric(19,5)
        declare @fg33_profit_onBooks_current numeric(19,5)
        declare @fg33_profit_onBooks_change numeric(19,5)
        declare @fg34_profit_onBooks_current numeric(19,5)
        declare @fg34_profit_onBooks_change numeric(19,5)
        declare @fg35_profit_onBooks_current numeric(19,5)
        declare @fg35_profit_onBooks_change numeric(19,5)
        declare @fg36_profit_onBooks_current numeric(19,5)
        declare @fg36_profit_onBooks_change numeric(19,5)
        declare @fg37_profit_onBooks_current numeric(19,5)
        declare @fg37_profit_onBooks_change numeric(19,5)
        declare @fg38_profit_onBooks_current numeric(19,5)
        declare @fg38_profit_onBooks_change numeric(19,5)
        declare @fg39_profit_onBooks_current numeric(19,5)
        declare @fg39_profit_onBooks_change numeric(19,5)
        declare @fg40_profit_onBooks_current numeric(19,5)
        declare @fg40_profit_onBooks_change numeric(19,5)
        declare @fg41_profit_onBooks_current numeric(19,5)
        declare @fg41_profit_onBooks_change numeric(19,5)
        declare @fg42_profit_onBooks_current numeric(19,5)
        declare @fg42_profit_onBooks_change numeric(19,5)
        declare @fg43_profit_onBooks_current numeric(19,5)
        declare @fg43_profit_onBooks_change numeric(19,5)
        declare @fg44_profit_onBooks_current numeric(19,5)
        declare @fg44_profit_onBooks_change numeric(19,5)
        declare @fg45_profit_onBooks_current numeric(19,5)
        declare @fg45_profit_onBooks_change numeric(19,5)
        declare @fg46_profit_onBooks_current numeric(19,5)
        declare @fg46_profit_onBooks_change numeric(19,5)
        declare @fg47_profit_onBooks_current numeric(19,5)
        declare @fg47_profit_onBooks_change numeric(19,5)
        declare @fg48_profit_onBooks_current numeric(19,5)
        declare @fg48_profit_onBooks_change numeric(19,5)
        declare @fg49_profit_onBooks_current numeric(19,5)
        declare @fg49_profit_onBooks_change numeric(19,5)
        declare @fg50_profit_onBooks_current numeric(19,5)
        declare @fg50_profit_onBooks_change numeric(19,5)

		declare @fg1_proPOR_onBooks_current numeric(19,5)
		declare @fg1_proPOR_onBooks_change numeric(19,5)
		declare @fg2_proPOR_onBooks_current numeric(19,5)
		declare @fg2_proPOR_onBooks_change numeric(19,5)
		declare @fg3_proPOR_onBooks_current numeric(19,5)
		declare @fg3_proPOR_onBooks_change numeric(19,5)
		declare @fg4_proPOR_onBooks_current numeric(19,5)
		declare @fg4_proPOR_onBooks_change numeric(19,5)
		declare @fg5_proPOR_onBooks_current numeric(19,5)
		declare @fg5_proPOR_onBooks_change numeric(19,5)
		declare @fg6_proPOR_onBooks_current numeric(19,5)
		declare @fg6_proPOR_onBooks_change numeric(19,5)
		declare @fg7_proPOR_onBooks_current numeric(19,5)
		declare @fg7_proPOR_onBooks_change numeric(19,5)
		declare @fg8_proPOR_onBooks_current numeric(19,5)
		declare @fg8_proPOR_onBooks_change numeric(19,5)
		declare @fg9_proPOR_onBooks_current numeric(19,5)
		declare @fg9_proPOR_onBooks_change numeric(19,5)
		declare @fg10_proPOR_onBooks_current numeric(19,5)
		declare @fg10_proPOR_onBooks_change numeric(19,5)
		declare @fg11_proPOR_onBooks_current numeric(19,5)
		declare @fg11_proPOR_onBooks_change numeric(19,5)
		declare @fg12_proPOR_onBooks_current numeric(19,5)
		declare @fg12_proPOR_onBooks_change numeric(19,5)
		declare @fg13_proPOR_onBooks_current numeric(19,5)
		declare @fg13_proPOR_onBooks_change numeric(19,5)
		declare @fg14_proPOR_onBooks_current numeric(19,5)
		declare @fg14_proPOR_onBooks_change numeric(19,5)
		declare @fg15_proPOR_onBooks_current numeric(19,5)
		declare @fg15_proPOR_onBooks_change numeric(19,5)
		declare @fg16_proPOR_onBooks_current numeric(19,5)
		declare @fg16_proPOR_onBooks_change numeric(19,5)
		declare @fg17_proPOR_onBooks_current numeric(19,5)
		declare @fg17_proPOR_onBooks_change numeric(19,5)
		declare @fg18_proPOR_onBooks_current numeric(19,5)
		declare @fg18_proPOR_onBooks_change numeric(19,5)
		declare @fg19_proPOR_onBooks_current numeric(19,5)
		declare @fg19_proPOR_onBooks_change numeric(19,5)
		declare @fg20_proPOR_onBooks_current numeric(19,5)
		declare @fg20_proPOR_onBooks_change numeric(19,5)
		declare @fg21_proPOR_onBooks_current numeric(19,5)
		declare @fg21_proPOR_onBooks_change numeric(19,5)
		declare @fg22_proPOR_onBooks_current numeric(19,5)
		declare @fg22_proPOR_onBooks_change numeric(19,5)
		declare @fg23_proPOR_onBooks_current numeric(19,5)
		declare @fg23_proPOR_onBooks_change numeric(19,5)
		declare @fg24_proPOR_onBooks_current numeric(19,5)
		declare @fg24_proPOR_onBooks_change numeric(19,5)
		declare @fg25_proPOR_onBooks_current numeric(19,5)
		declare @fg25_proPOR_onBooks_change numeric(19,5)
        declare @fg26_proPOR_onBooks_current numeric(19,5)
        declare @fg26_proPOR_onBooks_change numeric(19,5)
        declare @fg27_proPOR_onBooks_current numeric(19,5)
        declare @fg27_proPOR_onBooks_change numeric(19,5)
        declare @fg28_proPOR_onBooks_current numeric(19,5)
        declare @fg28_proPOR_onBooks_change numeric(19,5)
        declare @fg29_proPOR_onBooks_current numeric(19,5)
        declare @fg29_proPOR_onBooks_change numeric(19,5)
        declare @fg30_proPOR_onBooks_current numeric(19,5)
        declare @fg30_proPOR_onBooks_change numeric(19,5)
        declare @fg31_proPOR_onBooks_current numeric(19,5)
        declare @fg31_proPOR_onBooks_change numeric(19,5)
        declare @fg32_proPOR_onBooks_current numeric(19,5)
        declare @fg32_proPOR_onBooks_change numeric(19,5)
        declare @fg33_proPOR_onBooks_current numeric(19,5)
        declare @fg33_proPOR_onBooks_change numeric(19,5)
        declare @fg34_proPOR_onBooks_current numeric(19,5)
        declare @fg34_proPOR_onBooks_change numeric(19,5)
        declare @fg35_proPOR_onBooks_current numeric(19,5)
        declare @fg35_proPOR_onBooks_change numeric(19,5)
        declare @fg36_proPOR_onBooks_current numeric(19,5)
        declare @fg36_proPOR_onBooks_change numeric(19,5)
        declare @fg37_proPOR_onBooks_current numeric(19,5)
        declare @fg37_proPOR_onBooks_change numeric(19,5)
        declare @fg38_proPOR_onBooks_current numeric(19,5)
        declare @fg38_proPOR_onBooks_change numeric(19,5)
        declare @fg39_proPOR_onBooks_current numeric(19,5)
        declare @fg39_proPOR_onBooks_change numeric(19,5)
        declare @fg40_proPOR_onBooks_current numeric(19,5)
        declare @fg40_proPOR_onBooks_change numeric(19,5)
        declare @fg41_proPOR_onBooks_current numeric(19,5)
        declare @fg41_proPOR_onBooks_change numeric(19,5)
        declare @fg42_proPOR_onBooks_current numeric(19,5)
        declare @fg42_proPOR_onBooks_change numeric(19,5)
        declare @fg43_proPOR_onBooks_current numeric(19,5)
        declare @fg43_proPOR_onBooks_change numeric(19,5)
        declare @fg44_proPOR_onBooks_current numeric(19,5)
        declare @fg44_proPOR_onBooks_change numeric(19,5)
        declare @fg45_proPOR_onBooks_current numeric(19,5)
        declare @fg45_proPOR_onBooks_change numeric(19,5)
        declare @fg46_proPOR_onBooks_current numeric(19,5)
        declare @fg46_proPOR_onBooks_change numeric(19,5)
        declare @fg47_proPOR_onBooks_current numeric(19,5)
        declare @fg47_proPOR_onBooks_change numeric(19,5)
        declare @fg48_proPOR_onBooks_current numeric(19,5)
        declare @fg48_proPOR_onBooks_change numeric(19,5)
        declare @fg49_proPOR_onBooks_current numeric(19,5)
        declare @fg49_proPOR_onBooks_change numeric(19,5)
        declare @fg50_proPOR_onBooks_current numeric(19,5)
        declare @fg50_proPOR_onBooks_change numeric(19,5)

		declare @fg1_profit_forecast_current numeric(19,5)
		declare @fg1_profit_forecast_change numeric(19,5)
		declare @fg2_profit_forecast_current numeric(19,5)
		declare @fg2_profit_forecast_change numeric(19,5)
		declare @fg3_profit_forecast_current numeric(19,5)
		declare @fg3_profit_forecast_change numeric(19,5)
		declare @fg4_profit_forecast_current numeric(19,5)
		declare @fg4_profit_forecast_change numeric(19,5)
		declare @fg5_profit_forecast_current numeric(19,5)
		declare @fg5_profit_forecast_change numeric(19,5)
		declare @fg6_profit_forecast_current numeric(19,5)
		declare @fg6_profit_forecast_change numeric(19,5)
		declare @fg7_profit_forecast_current numeric(19,5)
		declare @fg7_profit_forecast_change numeric(19,5)
		declare @fg8_profit_forecast_current numeric(19,5)
		declare @fg8_profit_forecast_change numeric(19,5)
		declare @fg9_profit_forecast_current numeric(19,5)
		declare @fg9_profit_forecast_change numeric(19,5)
		declare @fg10_profit_forecast_current numeric(19,5)
		declare @fg10_profit_forecast_change numeric(19,5)
		declare @fg11_profit_forecast_current numeric(19,5)
		declare @fg11_profit_forecast_change numeric(19,5)
		declare @fg12_profit_forecast_current numeric(19,5)
		declare @fg12_profit_forecast_change numeric(19,5)
		declare @fg13_profit_forecast_current numeric(19,5)
		declare @fg13_profit_forecast_change numeric(19,5)
		declare @fg14_profit_forecast_current numeric(19,5)
		declare @fg14_profit_forecast_change numeric(19,5)
		declare @fg15_profit_forecast_current numeric(19,5)
		declare @fg15_profit_forecast_change numeric(19,5)
		declare @fg16_profit_forecast_current numeric(19,5)
		declare @fg16_profit_forecast_change numeric(19,5)
		declare @fg17_profit_forecast_current numeric(19,5)
		declare @fg17_profit_forecast_change numeric(19,5)
		declare @fg18_profit_forecast_current numeric(19,5)
		declare @fg18_profit_forecast_change numeric(19,5)
		declare @fg19_profit_forecast_current numeric(19,5)
		declare @fg19_profit_forecast_change numeric(19,5)
		declare @fg20_profit_forecast_current numeric(19,5)
		declare @fg20_profit_forecast_change numeric(19,5)
		declare @fg21_profit_forecast_current numeric(19,5)
		declare @fg21_profit_forecast_change numeric(19,5)
		declare @fg22_profit_forecast_current numeric(19,5)
		declare @fg22_profit_forecast_change numeric(19,5)
		declare @fg23_profit_forecast_current numeric(19,5)
		declare @fg23_profit_forecast_change numeric(19,5)
		declare @fg24_profit_forecast_current numeric(19,5)
		declare @fg24_profit_forecast_change numeric(19,5)
		declare @fg25_profit_forecast_current numeric(19,5)
		declare @fg25_profit_forecast_change numeric(19,5)
        declare @fg26_profit_forecast_current numeric(19,5)
        declare @fg26_profit_forecast_change numeric(19,5)
        declare @fg27_profit_forecast_current numeric(19,5)
        declare @fg27_profit_forecast_change numeric(19,5)
        declare @fg28_profit_forecast_current numeric(19,5)
        declare @fg28_profit_forecast_change numeric(19,5)
        declare @fg29_profit_forecast_current numeric(19,5)
        declare @fg29_profit_forecast_change numeric(19,5)
        declare @fg30_profit_forecast_current numeric(19,5)
        declare @fg30_profit_forecast_change numeric(19,5)
        declare @fg31_profit_forecast_current numeric(19,5)
        declare @fg31_profit_forecast_change numeric(19,5)
        declare @fg32_profit_forecast_current numeric(19,5)
        declare @fg32_profit_forecast_change numeric(19,5)
        declare @fg33_profit_forecast_current numeric(19,5)
        declare @fg33_profit_forecast_change numeric(19,5)
        declare @fg34_profit_forecast_current numeric(19,5)
        declare @fg34_profit_forecast_change numeric(19,5)
        declare @fg35_profit_forecast_current numeric(19,5)
        declare @fg35_profit_forecast_change numeric(19,5)
        declare @fg36_profit_forecast_current numeric(19,5)
        declare @fg36_profit_forecast_change numeric(19,5)
        declare @fg37_profit_forecast_current numeric(19,5)
        declare @fg37_profit_forecast_change numeric(19,5)
        declare @fg38_profit_forecast_current numeric(19,5)
        declare @fg38_profit_forecast_change numeric(19,5)
        declare @fg39_profit_forecast_current numeric(19,5)
        declare @fg39_profit_forecast_change numeric(19,5)
        declare @fg40_profit_forecast_current numeric(19,5)
        declare @fg40_profit_forecast_change numeric(19,5)
        declare @fg41_profit_forecast_current numeric(19,5)
        declare @fg41_profit_forecast_change numeric(19,5)
        declare @fg42_profit_forecast_current numeric(19,5)
        declare @fg42_profit_forecast_change numeric(19,5)
        declare @fg43_profit_forecast_current numeric(19,5)
        declare @fg43_profit_forecast_change numeric(19,5)
        declare @fg44_profit_forecast_current numeric(19,5)
        declare @fg44_profit_forecast_change numeric(19,5)
        declare @fg45_profit_forecast_current numeric(19,5)
        declare @fg45_profit_forecast_change numeric(19,5)
        declare @fg46_profit_forecast_current numeric(19,5)
        declare @fg46_profit_forecast_change numeric(19,5)
        declare @fg47_profit_forecast_current numeric(19,5)
        declare @fg47_profit_forecast_change numeric(19,5)
        declare @fg48_profit_forecast_current numeric(19,5)
        declare @fg48_profit_forecast_change numeric(19,5)
        declare @fg49_profit_forecast_current numeric(19,5)
        declare @fg49_profit_forecast_change numeric(19,5)
        declare @fg50_profit_forecast_current numeric(19,5)
        declare @fg50_profit_forecast_change numeric(19,5)

		declare @fg1_proPOR_forecast_current numeric(19,5)
		declare @fg1_proPOR_forecast_change numeric(19,5)
		declare @fg2_proPOR_forecast_current numeric(19,5)
		declare @fg2_proPOR_forecast_change numeric(19,5)
		declare @fg3_proPOR_forecast_current numeric(19,5)
		declare @fg3_proPOR_forecast_change numeric(19,5)
		declare @fg4_proPOR_forecast_current numeric(19,5)
		declare @fg4_proPOR_forecast_change numeric(19,5)
		declare @fg5_proPOR_forecast_current numeric(19,5)
		declare @fg5_proPOR_forecast_change numeric(19,5)
		declare @fg6_proPOR_forecast_current numeric(19,5)
		declare @fg6_proPOR_forecast_change numeric(19,5)
		declare @fg7_proPOR_forecast_current numeric(19,5)
		declare @fg7_proPOR_forecast_change numeric(19,5)
		declare @fg8_proPOR_forecast_current numeric(19,5)
		declare @fg8_proPOR_forecast_change numeric(19,5)
		declare @fg9_proPOR_forecast_current numeric(19,5)
		declare @fg9_proPOR_forecast_change numeric(19,5)
		declare @fg10_proPOR_forecast_current numeric(19,5)
		declare @fg10_proPOR_forecast_change numeric(19,5)
		declare @fg11_proPOR_forecast_current numeric(19,5)
		declare @fg11_proPOR_forecast_change numeric(19,5)
		declare @fg12_proPOR_forecast_current numeric(19,5)
		declare @fg12_proPOR_forecast_change numeric(19,5)
		declare @fg13_proPOR_forecast_current numeric(19,5)
		declare @fg13_proPOR_forecast_change numeric(19,5)
		declare @fg14_proPOR_forecast_current numeric(19,5)
		declare @fg14_proPOR_forecast_change numeric(19,5)
		declare @fg15_proPOR_forecast_current numeric(19,5)
		declare @fg15_proPOR_forecast_change numeric(19,5)
		declare @fg16_proPOR_forecast_current numeric(19,5)
		declare @fg16_proPOR_forecast_change numeric(19,5)
		declare @fg17_proPOR_forecast_current numeric(19,5)
		declare @fg17_proPOR_forecast_change numeric(19,5)
		declare @fg18_proPOR_forecast_current numeric(19,5)
		declare @fg18_proPOR_forecast_change numeric(19,5)
		declare @fg19_proPOR_forecast_current numeric(19,5)
		declare @fg19_proPOR_forecast_change numeric(19,5)
		declare @fg20_proPOR_forecast_current numeric(19,5)
		declare @fg20_proPOR_forecast_change numeric(19,5)
		declare @fg21_proPOR_forecast_current numeric(19,5)
		declare @fg21_proPOR_forecast_change numeric(19,5)
		declare @fg22_proPOR_forecast_current numeric(19,5)
		declare @fg22_proPOR_forecast_change numeric(19,5)
		declare @fg23_proPOR_forecast_current numeric(19,5)
		declare @fg23_proPOR_forecast_change numeric(19,5)
		declare @fg24_proPOR_forecast_current numeric(19,5)
		declare @fg24_proPOR_forecast_change numeric(19,5)
		declare @fg25_proPOR_forecast_current numeric(19,5)
		declare @fg25_proPOR_forecast_change numeric(19,5)
        declare @fg26_proPOR_forecast_current numeric(19,5)
        declare @fg26_proPOR_forecast_change numeric(19,5)
        declare @fg27_proPOR_forecast_current numeric(19,5)
        declare @fg27_proPOR_forecast_change numeric(19,5)
        declare @fg28_proPOR_forecast_current numeric(19,5)
        declare @fg28_proPOR_forecast_change numeric(19,5)
        declare @fg29_proPOR_forecast_current numeric(19,5)
        declare @fg29_proPOR_forecast_change numeric(19,5)
        declare @fg30_proPOR_forecast_current numeric(19,5)
        declare @fg30_proPOR_forecast_change numeric(19,5)
        declare @fg31_proPOR_forecast_current numeric(19,5)
        declare @fg31_proPOR_forecast_change numeric(19,5)
        declare @fg32_proPOR_forecast_current numeric(19,5)
        declare @fg32_proPOR_forecast_change numeric(19,5)
        declare @fg33_proPOR_forecast_current numeric(19,5)
        declare @fg33_proPOR_forecast_change numeric(19,5)
        declare @fg34_proPOR_forecast_current numeric(19,5)
        declare @fg34_proPOR_forecast_change numeric(19,5)
        declare @fg35_proPOR_forecast_current numeric(19,5)
        declare @fg35_proPOR_forecast_change numeric(19,5)
        declare @fg36_proPOR_forecast_current numeric(19,5)
        declare @fg36_proPOR_forecast_change numeric(19,5)
        declare @fg37_proPOR_forecast_current numeric(19,5)
        declare @fg37_proPOR_forecast_change numeric(19,5)
        declare @fg38_proPOR_forecast_current numeric(19,5)
        declare @fg38_proPOR_forecast_change numeric(19,5)
        declare @fg39_proPOR_forecast_current numeric(19,5)
        declare @fg39_proPOR_forecast_change numeric(19,5)
        declare @fg40_proPOR_forecast_current numeric(19,5)
        declare @fg40_proPOR_forecast_change numeric(19,5)
        declare @fg41_proPOR_forecast_current numeric(19,5)
        declare @fg41_proPOR_forecast_change numeric(19,5)
        declare @fg42_proPOR_forecast_current numeric(19,5)
        declare @fg42_proPOR_forecast_change numeric(19,5)
        declare @fg43_proPOR_forecast_current numeric(19,5)
        declare @fg43_proPOR_forecast_change numeric(19,5)
        declare @fg44_proPOR_forecast_current numeric(19,5)
        declare @fg44_proPOR_forecast_change numeric(19,5)
        declare @fg45_proPOR_forecast_current numeric(19,5)
        declare @fg45_proPOR_forecast_change numeric(19,5)
        declare @fg46_proPOR_forecast_current numeric(19,5)
        declare @fg46_proPOR_forecast_change numeric(19,5)
        declare @fg47_proPOR_forecast_current numeric(19,5)
        declare @fg47_proPOR_forecast_change numeric(19,5)
        declare @fg48_proPOR_forecast_current numeric(19,5)
        declare @fg48_proPOR_forecast_change numeric(19,5)
        declare @fg49_proPOR_forecast_current numeric(19,5)
        declare @fg49_proPOR_forecast_change numeric(19,5)
        declare @fg50_proPOR_forecast_current numeric(19,5)
        declare @fg50_proPOR_forecast_change numeric(19,5)
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
begin
			set @business_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_dt ,@caughtupdate))
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
end

		declare @fg1 int
		declare @fg2 int
		declare @fg3 int
		declare @fg4 int
		declare @fg5 int
		declare @fg6 int
		declare @fg7 int
		declare @fg8 int
		declare @fg9 int
		declare @fg10 int
		declare @fg11 int
		declare @fg12 int
		declare @fg13 int
		declare @fg14 int
		declare @fg15 int
		declare @fg16 int
		declare @fg17 int
		declare @fg18 int
		declare @fg19 int
		declare @fg20 int
		declare @fg21 int
		declare @fg22 int
		declare @fg23 int
		declare @fg24 int
		declare @fg25 int
        declare @fg26 int
        declare @fg27 int
        declare @fg28 int
        declare @fg29 int
        declare @fg30 int
        declare @fg31 int
        declare @fg32 int
        declare @fg33 int
        declare @fg34 int
        declare @fg35 int
        declare @fg36 int
        declare @fg37 int
        declare @fg38 int
        declare @fg39 int
        declare @fg40 int
        declare @fg41 int
        declare @fg42 int
        declare @fg43 int
        declare @fg44 int
        declare @fg45 int
        declare @fg46 int
        declare @fg47 int
        declare @fg48 int
        declare @fg49 int
        declare @fg50 int


		declare @tempFG table
		(
			number int,
			Forecast_Group_id int
		)
		insert into @tempFG
select number = ROW_NUMBER() OVER (ORDER BY Forecast_Group_ID),Forecast_Group_id
from Forecast_Group where Property_ID=@property_id and Forecast_Group_ID in (SELECT Value FROM varcharToInt(@forecast_group_id,','))
                      and Status_ID=1

    set @fg1 = (Select Forecast_Group_id from @tempFG where number=1)
set @fg2 = (Select Forecast_Group_id from @tempFG where number=2)
set @fg3 = (Select Forecast_Group_id from @tempFG where number=3)
set @fg4 = (Select Forecast_Group_id from @tempFG where number=4)
set @fg5 = (Select Forecast_Group_id from @tempFG where number=5)
set @fg6 = (Select Forecast_Group_id from @tempFG where number=6)
set @fg7 = (Select Forecast_Group_id from @tempFG where number=7)
set @fg8 = (Select Forecast_Group_id from @tempFG where number=8)
set @fg9 = (Select Forecast_Group_id from @tempFG where number=9)
set @fg10 = (Select Forecast_Group_id from @tempFG where number=10)
set @fg11 = (Select Forecast_Group_id from @tempFG where number=11)
set @fg12 = (Select Forecast_Group_id from @tempFG where number=12)
set @fg13 = (Select Forecast_Group_id from @tempFG where number=13)
set @fg14 = (Select Forecast_Group_id from @tempFG where number=14)
set @fg15 = (Select Forecast_Group_id from @tempFG where number=15)
set @fg16 = (Select Forecast_Group_id from @tempFG where number=16)
set @fg17 = (Select Forecast_Group_id from @tempFG where number=17)
set @fg18 = (Select Forecast_Group_id from @tempFG where number=18)
set @fg19 = (Select Forecast_Group_id from @tempFG where number=19)
set @fg20 = (Select Forecast_Group_id from @tempFG where number=20)
set @fg21 = (Select Forecast_Group_id from @tempFG where number=21)
set @fg22 = (Select Forecast_Group_id from @tempFG where number=22)
set @fg23 = (Select Forecast_Group_id from @tempFG where number=23)
set @fg24 = (Select Forecast_Group_id from @tempFG where number=24)
set @fg25 = (Select Forecast_Group_id from @tempFG where number=25)
set @fg26 = (Select Forecast_Group_id from @tempFG where number=26)
set @fg27 = (Select Forecast_Group_id from @tempFG where number=27)
set @fg28 = (Select Forecast_Group_id from @tempFG where number=28)
set @fg29 = (Select Forecast_Group_id from @tempFG where number=29)
set @fg30 = (Select Forecast_Group_id from @tempFG where number=30)
set @fg31 = (Select Forecast_Group_id from @tempFG where number=31)
set @fg32 = (Select Forecast_Group_id from @tempFG where number=32)
set @fg33 = (Select Forecast_Group_id from @tempFG where number=33)
set @fg34 = (Select Forecast_Group_id from @tempFG where number=34)
set @fg35 = (Select Forecast_Group_id from @tempFG where number=35)
set @fg36 = (Select Forecast_Group_id from @tempFG where number=36)
set @fg37 = (Select Forecast_Group_id from @tempFG where number=37)
set @fg38 = (Select Forecast_Group_id from @tempFG where number=38)
set @fg39 = (Select Forecast_Group_id from @tempFG where number=39)
set @fg40 = (Select Forecast_Group_id from @tempFG where number=40)
set @fg41 = (Select Forecast_Group_id from @tempFG where number=41)
set @fg42 = (Select Forecast_Group_id from @tempFG where number=42)
set @fg43 = (Select Forecast_Group_id from @tempFG where number=43)
set @fg44 = (Select Forecast_Group_id from @tempFG where number=44)
set @fg45 = (Select Forecast_Group_id from @tempFG where number=45)
set @fg46 = (Select Forecast_Group_id from @tempFG where number=46)
set @fg47 = (Select Forecast_Group_id from @tempFG where number=47)
set @fg48 = (Select Forecast_Group_id from @tempFG where number=48)
set @fg49 = (Select Forecast_Group_id from @tempFG where number=49)
set @fg50 = (Select Forecast_Group_id from @tempFG where number=50)


--- extract report metrics
select
    occupancy_dt,dow
     ,isnull(MAX(fg1_roomsoldcurrent),0.0) as fg1_roomsoldcurrent,isnull(MAX(fg1_roomssoldchange),0.0) as fg1_roomssoldchange
     ,isnull(MAX(fg2_roomsoldcurrent),0.0) as fg2_roomsoldcurrent,isnull(MAX(fg2_roomssoldchange),0.0) as fg2_roomssoldchange
     ,isnull(MAX(fg3_roomsoldcurrent),0.0) as fg3_roomsoldcurrent,isnull(MAX(fg3_roomssoldchange),0.0) as fg3_roomssoldchange
     ,isnull(MAX(fg4_roomsoldcurrent),0.0) as fg4_roomsoldcurrent,isnull(MAX(fg4_roomssoldchange),0.0) as fg4_roomssoldchange
     ,isnull(MAX(fg5_roomsoldcurrent),0.0) as fg5_roomsoldcurrent,isnull(MAX(fg5_roomssoldchange),0.0) as fg5_roomssoldchange
     ,isnull(MAX(fg6_roomsoldcurrent),0.0) as fg6_roomsoldcurrent,isnull(MAX(fg6_roomssoldchange),0.0) as fg6_roomssoldchange
     ,isnull(MAX(fg7_roomsoldcurrent),0.0) as fg7_roomsoldcurrent,isnull(MAX(fg7_roomssoldchange),0.0) as fg7_roomssoldchange
     ,isnull(MAX(fg8_roomsoldcurrent),0.0) as fg8_roomsoldcurrent,isnull(MAX(fg8_roomssoldchange),0.0) as fg8_roomssoldchange
     ,isnull(MAX(fg9_roomsoldcurrent),0.0) as fg9_roomsoldcurrent,isnull(MAX(fg9_roomssoldchange),0.0) as fg9_roomssoldchange
     ,isnull(MAX(fg10_roomsoldcurrent),0.0) as fg10_roomsoldcurrent,isnull(MAX(fg10_roomssoldchange),0.0) as fg10_roomssoldchange
     ,isnull(MAX(fg11_roomsoldcurrent),0.0) as fg11_roomsoldcurrent,isnull(MAX(fg11_roomssoldchange),0.0) as fg11_roomssoldchange
     ,isnull(MAX(fg12_roomsoldcurrent),0.0) as fg12_roomsoldcurrent,isnull(MAX(fg12_roomssoldchange),0.0) as fg12_roomssoldchange
     ,isnull(MAX(fg13_roomsoldcurrent),0.0) as fg13_roomsoldcurrent,isnull(MAX(fg13_roomssoldchange),0.0) as fg13_roomssoldchange
     ,isnull(MAX(fg14_roomsoldcurrent),0.0) as fg14_roomsoldcurrent,isnull(MAX(fg14_roomssoldchange),0.0) as fg14_roomssoldchange
     ,isnull(MAX(fg15_roomsoldcurrent),0.0) as fg15_roomsoldcurrent,isnull(MAX(fg15_roomssoldchange),0.0) as fg15_roomssoldchange
     ,isnull(MAX(fg16_roomsoldcurrent),0.0) as fg16_roomsoldcurrent,isnull(MAX(fg16_roomssoldchange),0.0) as fg16_roomssoldchange
     ,isnull(MAX(fg17_roomsoldcurrent),0.0) as fg17_roomsoldcurrent,isnull(MAX(fg17_roomssoldchange),0.0) as fg17_roomssoldchange
     ,isnull(MAX(fg18_roomsoldcurrent),0.0) as fg18_roomsoldcurrent,isnull(MAX(fg18_roomssoldchange),0.0) as fg18_roomssoldchange
     ,isnull(MAX(fg19_roomsoldcurrent),0.0) as fg19_roomsoldcurrent,isnull(MAX(fg19_roomssoldchange),0.0) as fg19_roomssoldchange
     ,isnull(MAX(fg20_roomsoldcurrent),0.0) as fg20_roomsoldcurrent,isnull(MAX(fg20_roomssoldchange),0.0) as fg20_roomssoldchange
     ,isnull(MAX(fg21_roomsoldcurrent),0.0) as fg21_roomsoldcurrent,isnull(MAX(fg21_roomssoldchange),0.0) as fg21_roomssoldchange
     ,isnull(MAX(fg22_roomsoldcurrent),0.0) as fg22_roomsoldcurrent,isnull(MAX(fg22_roomssoldchange),0.0) as fg22_roomssoldchange
     ,isnull(MAX(fg23_roomsoldcurrent),0.0) as fg23_roomsoldcurrent,isnull(MAX(fg23_roomssoldchange),0.0) as fg23_roomssoldchange
     ,isnull(MAX(fg24_roomsoldcurrent),0.0) as fg24_roomsoldcurrent,isnull(MAX(fg24_roomssoldchange),0.0) as fg24_roomssoldchange
     ,isnull(MAX(fg25_roomsoldcurrent),0.0) as fg25_roomsoldcurrent,isnull(MAX(fg25_roomssoldchange),0.0) as fg25_roomssoldchange
     ,isnull(MAX(fg26_roomsoldcurrent),0.0) as fg26_roomsoldcurrent,isnull(MAX(fg26_roomssoldchange),0.0) as fg26_roomssoldchange
     ,isnull(MAX(fg27_roomsoldcurrent),0.0) as fg27_roomsoldcurrent,isnull(MAX(fg27_roomssoldchange),0.0) as fg27_roomssoldchange
     ,isnull(MAX(fg28_roomsoldcurrent),0.0) as fg28_roomsoldcurrent,isnull(MAX(fg28_roomssoldchange),0.0) as fg28_roomssoldchange
     ,isnull(MAX(fg29_roomsoldcurrent),0.0) as fg29_roomsoldcurrent,isnull(MAX(fg29_roomssoldchange),0.0) as fg29_roomssoldchange
     ,isnull(MAX(fg30_roomsoldcurrent),0.0) as fg30_roomsoldcurrent,isnull(MAX(fg30_roomssoldchange),0.0) as fg30_roomssoldchange
     ,isnull(MAX(fg31_roomsoldcurrent),0.0) as fg31_roomsoldcurrent,isnull(MAX(fg31_roomssoldchange),0.0) as fg31_roomssoldchange
     ,isnull(MAX(fg32_roomsoldcurrent),0.0) as fg32_roomsoldcurrent,isnull(MAX(fg32_roomssoldchange),0.0) as fg32_roomssoldchange
     ,isnull(MAX(fg33_roomsoldcurrent),0.0) as fg33_roomsoldcurrent,isnull(MAX(fg33_roomssoldchange),0.0) as fg33_roomssoldchange
     ,isnull(MAX(fg34_roomsoldcurrent),0.0) as fg34_roomsoldcurrent,isnull(MAX(fg34_roomssoldchange),0.0) as fg34_roomssoldchange
     ,isnull(MAX(fg35_roomsoldcurrent),0.0) as fg35_roomsoldcurrent,isnull(MAX(fg35_roomssoldchange),0.0) as fg35_roomssoldchange
     ,isnull(MAX(fg36_roomsoldcurrent),0.0) as fg36_roomsoldcurrent,isnull(MAX(fg36_roomssoldchange),0.0) as fg36_roomssoldchange
     ,isnull(MAX(fg37_roomsoldcurrent),0.0) as fg37_roomsoldcurrent,isnull(MAX(fg37_roomssoldchange),0.0) as fg37_roomssoldchange
     ,isnull(MAX(fg38_roomsoldcurrent),0.0) as fg38_roomsoldcurrent,isnull(MAX(fg38_roomssoldchange),0.0) as fg38_roomssoldchange
     ,isnull(MAX(fg39_roomsoldcurrent),0.0) as fg39_roomsoldcurrent,isnull(MAX(fg39_roomssoldchange),0.0) as fg39_roomssoldchange
     ,isnull(MAX(fg40_roomsoldcurrent),0.0) as fg40_roomsoldcurrent,isnull(MAX(fg40_roomssoldchange),0.0) as fg40_roomssoldchange
     ,isnull(MAX(fg41_roomsoldcurrent),0.0) as fg41_roomsoldcurrent,isnull(MAX(fg41_roomssoldchange),0.0) as fg41_roomssoldchange
     ,isnull(MAX(fg42_roomsoldcurrent),0.0) as fg42_roomsoldcurrent,isnull(MAX(fg42_roomssoldchange),0.0) as fg42_roomssoldchange
     ,isnull(MAX(fg43_roomsoldcurrent),0.0) as fg43_roomsoldcurrent,isnull(MAX(fg43_roomssoldchange),0.0) as fg43_roomssoldchange
     ,isnull(MAX(fg44_roomsoldcurrent),0.0) as fg44_roomsoldcurrent,isnull(MAX(fg44_roomssoldchange),0.0) as fg44_roomssoldchange
     ,isnull(MAX(fg45_roomsoldcurrent),0.0) as fg45_roomsoldcurrent,isnull(MAX(fg45_roomssoldchange),0.0) as fg45_roomssoldchange
     ,isnull(MAX(fg46_roomsoldcurrent),0.0) as fg46_roomsoldcurrent,isnull(MAX(fg46_roomssoldchange),0.0) as fg46_roomssoldchange
     ,isnull(MAX(fg47_roomsoldcurrent),0.0) as fg47_roomsoldcurrent,isnull(MAX(fg47_roomssoldchange),0.0) as fg47_roomssoldchange
     ,isnull(MAX(fg48_roomsoldcurrent),0.0) as fg48_roomsoldcurrent,isnull(MAX(fg48_roomssoldchange),0.0) as fg48_roomssoldchange
     ,isnull(MAX(fg49_roomsoldcurrent),0.0) as fg49_roomsoldcurrent,isnull(MAX(fg49_roomssoldchange),0.0) as fg49_roomssoldchange
     ,isnull(MAX(fg50_roomsoldcurrent),0.0) as fg50_roomsoldcurrent,isnull(MAX(fg50_roomssoldchange),0.0) as fg50_roomssoldchange


     ,isnull(MAX(fg1_occfcstcurrent),0.0) as fg1_occfcstcurrent,isnull(MAX(fg1_occfcstchange),0.0) as fg1_occfcstchange
     ,isnull(MAX(fg2_occfcstcurrent),0.0) as fg2_occfcstcurrent,isnull(MAX(fg2_occfcstchange),0.0) as fg2_occfcstchange
     ,isnull(MAX(fg3_occfcstcurrent),0.0) as fg3_occfcstcurrent,isnull(MAX(fg3_occfcstchange),0.0) as fg3_occfcstchange
     ,isnull(MAX(fg4_occfcstcurrent),0.0) as fg4_occfcstcurrent,isnull(MAX(fg4_occfcstchange),0.0) as fg4_occfcstchange
     ,isnull(MAX(fg5_occfcstcurrent),0.0) as fg5_occfcstcurrent,isnull(MAX(fg5_occfcstchange),0.0) as fg5_occfcstchange
     ,isnull(MAX(fg6_occfcstcurrent),0.0) as fg6_occfcstcurrent,isnull(MAX(fg6_occfcstchange),0.0) as fg6_occfcstchange
     ,isnull(MAX(fg7_occfcstcurrent),0.0) as fg7_occfcstcurrent,isnull(MAX(fg7_occfcstchange),0.0) as fg7_occfcstchange
     ,isnull(MAX(fg8_occfcstcurrent),0.0) as fg8_occfcstcurrent,isnull(MAX(fg8_occfcstchange),0.0) as fg8_occfcstchange
     ,isnull(MAX(fg9_occfcstcurrent),0.0) as fg9_occfcstcurrent,isnull(MAX(fg9_occfcstchange),0.0) as fg9_occfcstchange
     ,isnull(MAX(fg10_occfcstcurrent),0.0) as fg10_occfcstcurrent,isnull(MAX(fg10_occfcstchange),0.0) as fg10_occfcstchange
     ,isnull(MAX(fg11_occfcstcurrent),0.0) as fg11_occfcstcurrent,isnull(MAX(fg11_occfcstchange),0.0) as fg11_occfcstchange
     ,isnull(MAX(fg12_occfcstcurrent),0.0) as fg12_occfcstcurrent,isnull(MAX(fg12_occfcstchange),0.0) as fg12_occfcstchange
     ,isnull(MAX(fg13_occfcstcurrent),0.0) as fg13_occfcstcurrent,isnull(MAX(fg13_occfcstchange),0.0) as fg13_occfcstchange
     ,isnull(MAX(fg14_occfcstcurrent),0.0) as fg14_occfcstcurrent,isnull(MAX(fg14_occfcstchange),0.0) as fg14_occfcstchange
     ,isnull(MAX(fg15_occfcstcurrent),0.0) as fg15_occfcstcurrent,isnull(MAX(fg15_occfcstchange),0.0) as fg15_occfcstchange
     ,isnull(MAX(fg16_occfcstcurrent),0.0) as fg16_occfcstcurrent,isnull(MAX(fg16_occfcstchange),0.0) as fg16_occfcstchange
     ,isnull(MAX(fg17_occfcstcurrent),0.0) as fg17_occfcstcurrent,isnull(MAX(fg17_occfcstchange),0.0) as fg17_occfcstchange
     ,isnull(MAX(fg18_occfcstcurrent),0.0) as fg18_occfcstcurrent,isnull(MAX(fg18_occfcstchange),0.0) as fg18_occfcstchange
     ,isnull(MAX(fg19_occfcstcurrent),0.0) as fg19_occfcstcurrent,isnull(MAX(fg19_occfcstchange),0.0) as fg19_occfcstchange
     ,isnull(MAX(fg20_occfcstcurrent),0.0) as fg20_occfcstcurrent,isnull(MAX(fg20_occfcstchange),0.0) as fg20_occfcstchange
     ,isnull(MAX(fg21_occfcstcurrent),0.0) as fg21_occfcstcurrent,isnull(MAX(fg21_occfcstchange),0.0) as fg21_occfcstchange
     ,isnull(MAX(fg22_occfcstcurrent),0.0) as fg22_occfcstcurrent,isnull(MAX(fg22_occfcstchange),0.0) as fg22_occfcstchange
     ,isnull(MAX(fg23_occfcstcurrent),0.0) as fg23_occfcstcurrent,isnull(MAX(fg23_occfcstchange),0.0) as fg23_occfcstchange
     ,isnull(MAX(fg24_occfcstcurrent),0.0) as fg24_occfcstcurrent,isnull(MAX(fg24_occfcstchange),0.0) as fg24_occfcstchange
     ,isnull(MAX(fg25_occfcstcurrent),0.0) as fg25_occfcstcurrent,isnull(MAX(fg25_occfcstchange),0.0) as fg25_occfcstchange
     ,isnull(MAX(fg26_occfcstcurrent),0.0) as fg26_occfcstcurrent,isnull(MAX(fg26_occfcstchange),0.0) as fg26_occfcstchange
     ,isnull(MAX(fg27_occfcstcurrent),0.0) as fg27_occfcstcurrent,isnull(MAX(fg27_occfcstchange),0.0) as fg27_occfcstchange
     ,isnull(MAX(fg28_occfcstcurrent),0.0) as fg28_occfcstcurrent,isnull(MAX(fg28_occfcstchange),0.0) as fg28_occfcstchange
     ,isnull(MAX(fg29_occfcstcurrent),0.0) as fg29_occfcstcurrent,isnull(MAX(fg29_occfcstchange),0.0) as fg29_occfcstchange
     ,isnull(MAX(fg30_occfcstcurrent),0.0) as fg30_occfcstcurrent,isnull(MAX(fg30_occfcstchange),0.0) as fg30_occfcstchange
     ,isnull(MAX(fg31_occfcstcurrent),0.0) as fg31_occfcstcurrent,isnull(MAX(fg31_occfcstchange),0.0) as fg31_occfcstchange
     ,isnull(MAX(fg32_occfcstcurrent),0.0) as fg32_occfcstcurrent,isnull(MAX(fg32_occfcstchange),0.0) as fg32_occfcstchange
     ,isnull(MAX(fg33_occfcstcurrent),0.0) as fg33_occfcstcurrent,isnull(MAX(fg33_occfcstchange),0.0) as fg33_occfcstchange
     ,isnull(MAX(fg34_occfcstcurrent),0.0) as fg34_occfcstcurrent,isnull(MAX(fg34_occfcstchange),0.0) as fg34_occfcstchange
     ,isnull(MAX(fg35_occfcstcurrent),0.0) as fg35_occfcstcurrent,isnull(MAX(fg35_occfcstchange),0.0) as fg35_occfcstchange
     ,isnull(MAX(fg36_occfcstcurrent),0.0) as fg36_occfcstcurrent,isnull(MAX(fg36_occfcstchange),0.0) as fg36_occfcstchange
     ,isnull(MAX(fg37_occfcstcurrent),0.0) as fg37_occfcstcurrent,isnull(MAX(fg37_occfcstchange),0.0) as fg37_occfcstchange
     ,isnull(MAX(fg38_occfcstcurrent),0.0) as fg38_occfcstcurrent,isnull(MAX(fg38_occfcstchange),0.0) as fg38_occfcstchange
     ,isnull(MAX(fg39_occfcstcurrent),0.0) as fg39_occfcstcurrent,isnull(MAX(fg39_occfcstchange),0.0) as fg39_occfcstchange
     ,isnull(MAX(fg40_occfcstcurrent),0.0) as fg40_occfcstcurrent,isnull(MAX(fg40_occfcstchange),0.0) as fg40_occfcstchange
     ,isnull(MAX(fg41_occfcstcurrent),0.0) as fg41_occfcstcurrent,isnull(MAX(fg41_occfcstchange),0.0) as fg41_occfcstchange
     ,isnull(MAX(fg42_occfcstcurrent),0.0) as fg42_occfcstcurrent,isnull(MAX(fg42_occfcstchange),0.0) as fg42_occfcstchange
     ,isnull(MAX(fg43_occfcstcurrent),0.0) as fg43_occfcstcurrent,isnull(MAX(fg43_occfcstchange),0.0) as fg43_occfcstchange
     ,isnull(MAX(fg44_occfcstcurrent),0.0) as fg44_occfcstcurrent,isnull(MAX(fg44_occfcstchange),0.0) as fg44_occfcstchange
     ,isnull(MAX(fg45_occfcstcurrent),0.0) as fg45_occfcstcurrent,isnull(MAX(fg45_occfcstchange),0.0) as fg45_occfcstchange
     ,isnull(MAX(fg46_occfcstcurrent),0.0) as fg46_occfcstcurrent,isnull(MAX(fg46_occfcstchange),0.0) as fg46_occfcstchange
     ,isnull(MAX(fg47_occfcstcurrent),0.0) as fg47_occfcstcurrent,isnull(MAX(fg47_occfcstchange),0.0) as fg47_occfcstchange
     ,isnull(MAX(fg48_occfcstcurrent),0.0) as fg48_occfcstcurrent,isnull(MAX(fg48_occfcstchange),0.0) as fg48_occfcstchange
     ,isnull(MAX(fg49_occfcstcurrent),0.0) as fg49_occfcstcurrent,isnull(MAX(fg49_occfcstchange),0.0) as fg49_occfcstchange
     ,isnull(MAX(fg50_occfcstcurrent),0.0) as fg50_occfcstcurrent,isnull(MAX(fg50_occfcstchange),0.0) as fg50_occfcstchange


     ,isnull(MAX(fg1_bookedroomrevenuecurrent),0.0) as fg1_bookedroomrevenuecurrent,isnull(MAX(fg1_bookedroomrevenuechange),0.0) as fg1_bookedroomrevenuechange
     ,isnull(MAX(fg2_bookedroomrevenuecurrent),0.0) as fg2_bookedroomrevenuecurrent,isnull(MAX(fg2_bookedroomrevenuechange),0.0) as fg2_bookedroomrevenuechange
     ,isnull(MAX(fg3_bookedroomrevenuecurrent),0.0) as fg3_bookedroomrevenuecurrent,isnull(MAX(fg3_bookedroomrevenuechange),0.0) as fg3_bookedroomrevenuechange
     ,isnull(MAX(fg4_bookedroomrevenuecurrent),0.0) as fg4_bookedroomrevenuecurrent,isnull(MAX(fg4_bookedroomrevenuechange),0.0) as fg4_bookedroomrevenuechange
     ,isnull(MAX(fg5_bookedroomrevenuecurrent),0.0) as fg5_bookedroomrevenuecurrent,isnull(MAX(fg5_bookedroomrevenuechange),0.0) as fg5_bookedroomrevenuechange
     ,isnull(MAX(fg6_bookedroomrevenuecurrent),0.0) as fg6_bookedroomrevenuecurrent,isnull(MAX(fg6_bookedroomrevenuechange),0.0) as fg6_bookedroomrevenuechange
     ,isnull(MAX(fg7_bookedroomrevenuecurrent),0.0) as fg7_bookedroomrevenuecurrent,isnull(MAX(fg7_bookedroomrevenuechange),0.0) as fg7_bookedroomrevenuechange
     ,isnull(MAX(fg8_bookedroomrevenuecurrent),0.0) as fg8_bookedroomrevenuecurrent,isnull(MAX(fg8_bookedroomrevenuechange),0.0) as fg8_bookedroomrevenuechange
     ,isnull(MAX(fg9_bookedroomrevenuecurrent),0.0) as fg9_bookedroomrevenuecurrent,isnull(MAX(fg9_bookedroomrevenuechange),0.0) as fg9_bookedroomrevenuechange
     ,isnull(MAX(fg10_bookedroomrevenuecurrent),0.0) as fg10_bookedroomrevenuecurrent,isnull(MAX(fg10_bookedroomrevenuechange),0.0) as fg10_bookedroomrevenuechange
     ,isnull(MAX(fg11_bookedroomrevenuecurrent),0.0) as fg11_bookedroomrevenuecurrent,isnull(MAX(fg11_bookedroomrevenuechange),0.0) as fg11_bookedroomrevenuechange
     ,isnull(MAX(fg12_bookedroomrevenuecurrent),0.0) as fg12_bookedroomrevenuecurrent,isnull(MAX(fg12_bookedroomrevenuechange),0.0) as fg12_bookedroomrevenuechange
     ,isnull(MAX(fg13_bookedroomrevenuecurrent),0.0) as fg13_bookedroomrevenuecurrent,isnull(MAX(fg13_bookedroomrevenuechange),0.0) as fg13_bookedroomrevenuechange
     ,isnull(MAX(fg14_bookedroomrevenuecurrent),0.0) as fg14_bookedroomrevenuecurrent,isnull(MAX(fg14_bookedroomrevenuechange),0.0) as fg14_bookedroomrevenuechange
     ,isnull(MAX(fg15_bookedroomrevenuecurrent),0.0) as fg15_bookedroomrevenuecurrent,isnull(MAX(fg15_bookedroomrevenuechange),0.0) as fg15_bookedroomrevenuechange
     ,isnull(MAX(fg16_bookedroomrevenuecurrent),0.0) as fg16_bookedroomrevenuecurrent,isnull(MAX(fg16_bookedroomrevenuechange),0.0) as fg16_bookedroomrevenuechange
     ,isnull(MAX(fg17_bookedroomrevenuecurrent),0.0) as fg17_bookedroomrevenuecurrent,isnull(MAX(fg17_bookedroomrevenuechange),0.0) as fg17_bookedroomrevenuechange
     ,isnull(MAX(fg18_bookedroomrevenuecurrent),0.0) as fg18_bookedroomrevenuecurrent,isnull(MAX(fg18_bookedroomrevenuechange),0.0) as fg18_bookedroomrevenuechange
     ,isnull(MAX(fg19_bookedroomrevenuecurrent),0.0) as fg19_bookedroomrevenuecurrent,isnull(MAX(fg19_bookedroomrevenuechange),0.0) as fg19_bookedroomrevenuechange
     ,isnull(MAX(fg20_bookedroomrevenuecurrent),0.0) as fg20_bookedroomrevenuecurrent,isnull(MAX(fg20_bookedroomrevenuechange),0.0) as fg20_bookedroomrevenuechange
     ,isnull(MAX(fg21_bookedroomrevenuecurrent),0.0) as fg21_bookedroomrevenuecurrent,isnull(MAX(fg21_bookedroomrevenuechange),0.0) as fg21_bookedroomrevenuechange
     ,isnull(MAX(fg22_bookedroomrevenuecurrent),0.0) as fg22_bookedroomrevenuecurrent,isnull(MAX(fg22_bookedroomrevenuechange),0.0) as fg22_bookedroomrevenuechange
     ,isnull(MAX(fg23_bookedroomrevenuecurrent),0.0) as fg23_bookedroomrevenuecurrent,isnull(MAX(fg23_bookedroomrevenuechange),0.0) as fg23_bookedroomrevenuechange
     ,isnull(MAX(fg24_bookedroomrevenuecurrent),0.0) as fg24_bookedroomrevenuecurrent,isnull(MAX(fg24_bookedroomrevenuechange),0.0) as fg24_bookedroomrevenuechange
     ,isnull(MAX(fg25_bookedroomrevenuecurrent),0.0) as fg25_bookedroomrevenuecurrent,isnull(MAX(fg25_bookedroomrevenuechange),0.0) as fg25_bookedroomrevenuechange
     ,isnull(MAX(fg26_bookedroomrevenuecurrent),0.0) as fg26_bookedroomrevenuecurrent,isnull(MAX(fg26_bookedroomrevenuechange),0.0) as fg26_bookedroomrevenuechange
     ,isnull(MAX(fg27_bookedroomrevenuecurrent),0.0) as fg27_bookedroomrevenuecurrent,isnull(MAX(fg27_bookedroomrevenuechange),0.0) as fg27_bookedroomrevenuechange
     ,isnull(MAX(fg28_bookedroomrevenuecurrent),0.0) as fg28_bookedroomrevenuecurrent,isnull(MAX(fg28_bookedroomrevenuechange),0.0) as fg28_bookedroomrevenuechange
     ,isnull(MAX(fg29_bookedroomrevenuecurrent),0.0) as fg29_bookedroomrevenuecurrent,isnull(MAX(fg29_bookedroomrevenuechange),0.0) as fg29_bookedroomrevenuechange
     ,isnull(MAX(fg30_bookedroomrevenuecurrent),0.0) as fg30_bookedroomrevenuecurrent,isnull(MAX(fg30_bookedroomrevenuechange),0.0) as fg30_bookedroomrevenuechange
     ,isnull(MAX(fg31_bookedroomrevenuecurrent),0.0) as fg31_bookedroomrevenuecurrent,isnull(MAX(fg31_bookedroomrevenuechange),0.0) as fg31_bookedroomrevenuechange
     ,isnull(MAX(fg32_bookedroomrevenuecurrent),0.0) as fg32_bookedroomrevenuecurrent,isnull(MAX(fg32_bookedroomrevenuechange),0.0) as fg32_bookedroomrevenuechange
     ,isnull(MAX(fg33_bookedroomrevenuecurrent),0.0) as fg33_bookedroomrevenuecurrent,isnull(MAX(fg33_bookedroomrevenuechange),0.0) as fg33_bookedroomrevenuechange
     ,isnull(MAX(fg34_bookedroomrevenuecurrent),0.0) as fg34_bookedroomrevenuecurrent,isnull(MAX(fg34_bookedroomrevenuechange),0.0) as fg34_bookedroomrevenuechange
     ,isnull(MAX(fg35_bookedroomrevenuecurrent),0.0) as fg35_bookedroomrevenuecurrent,isnull(MAX(fg35_bookedroomrevenuechange),0.0) as fg35_bookedroomrevenuechange
     ,isnull(MAX(fg36_bookedroomrevenuecurrent),0.0) as fg36_bookedroomrevenuecurrent,isnull(MAX(fg36_bookedroomrevenuechange),0.0) as fg36_bookedroomrevenuechange
     ,isnull(MAX(fg37_bookedroomrevenuecurrent),0.0) as fg37_bookedroomrevenuecurrent,isnull(MAX(fg37_bookedroomrevenuechange),0.0) as fg37_bookedroomrevenuechange
     ,isnull(MAX(fg38_bookedroomrevenuecurrent),0.0) as fg38_bookedroomrevenuecurrent,isnull(MAX(fg38_bookedroomrevenuechange),0.0) as fg38_bookedroomrevenuechange
     ,isnull(MAX(fg39_bookedroomrevenuecurrent),0.0) as fg39_bookedroomrevenuecurrent,isnull(MAX(fg39_bookedroomrevenuechange),0.0) as fg39_bookedroomrevenuechange
     ,isnull(MAX(fg40_bookedroomrevenuecurrent),0.0) as fg40_bookedroomrevenuecurrent,isnull(MAX(fg40_bookedroomrevenuechange),0.0) as fg40_bookedroomrevenuechange
     ,isnull(MAX(fg41_bookedroomrevenuecurrent),0.0) as fg41_bookedroomrevenuecurrent,isnull(MAX(fg41_bookedroomrevenuechange),0.0) as fg41_bookedroomrevenuechange
     ,isnull(MAX(fg42_bookedroomrevenuecurrent),0.0) as fg42_bookedroomrevenuecurrent,isnull(MAX(fg42_bookedroomrevenuechange),0.0) as fg42_bookedroomrevenuechange
     ,isnull(MAX(fg43_bookedroomrevenuecurrent),0.0) as fg43_bookedroomrevenuecurrent,isnull(MAX(fg43_bookedroomrevenuechange),0.0) as fg43_bookedroomrevenuechange
     ,isnull(MAX(fg44_bookedroomrevenuecurrent),0.0) as fg44_bookedroomrevenuecurrent,isnull(MAX(fg44_bookedroomrevenuechange),0.0) as fg44_bookedroomrevenuechange
     ,isnull(MAX(fg45_bookedroomrevenuecurrent),0.0) as fg45_bookedroomrevenuecurrent,isnull(MAX(fg45_bookedroomrevenuechange),0.0) as fg45_bookedroomrevenuechange
     ,isnull(MAX(fg46_bookedroomrevenuecurrent),0.0) as fg46_bookedroomrevenuecurrent,isnull(MAX(fg46_bookedroomrevenuechange),0.0) as fg46_bookedroomrevenuechange
     ,isnull(MAX(fg47_bookedroomrevenuecurrent),0.0) as fg47_bookedroomrevenuecurrent,isnull(MAX(fg47_bookedroomrevenuechange),0.0) as fg47_bookedroomrevenuechange
     ,isnull(MAX(fg48_bookedroomrevenuecurrent),0.0) as fg48_bookedroomrevenuecurrent,isnull(MAX(fg48_bookedroomrevenuechange),0.0) as fg48_bookedroomrevenuechange
     ,isnull(MAX(fg49_bookedroomrevenuecurrent),0.0) as fg49_bookedroomrevenuecurrent,isnull(MAX(fg49_bookedroomrevenuechange),0.0) as fg49_bookedroomrevenuechange
     ,isnull(MAX(fg50_bookedroomrevenuecurrent),0.0) as fg50_bookedroomrevenuecurrent,isnull(MAX(fg50_bookedroomrevenuechange),0.0) as fg50_bookedroomrevenuechange


     ,isnull(MAX(fg1_fcstedroomrevenuecurrent),0.0) as fg1_fcstedroomrevenuecurrent,isnull(MAX(fg1_fcstedroomrevenuechange),0.0) as fg1_fcstedroomrevenuechange
     ,isnull(MAX(fg2_fcstedroomrevenuecurrent),0.0) as fg2_fcstedroomrevenuecurrent,isnull(MAX(fg2_fcstedroomrevenuechange),0.0) as fg2_fcstedroomrevenuechange
     ,isnull(MAX(fg3_fcstedroomrevenuecurrent),0.0) as fg3_fcstedroomrevenuecurrent,isnull(MAX(fg3_fcstedroomrevenuechange),0.0) as fg3_fcstedroomrevenuechange
     ,isnull(MAX(fg4_fcstedroomrevenuecurrent),0.0) as fg4_fcstedroomrevenuecurrent,isnull(MAX(fg4_fcstedroomrevenuechange),0.0) as fg4_fcstedroomrevenuechange
     ,isnull(MAX(fg5_fcstedroomrevenuecurrent),0.0) as fg5_fcstedroomrevenuecurrent,isnull(MAX(fg5_fcstedroomrevenuechange),0.0) as fg5_fcstedroomrevenuechange
     ,isnull(MAX(fg6_fcstedroomrevenuecurrent),0.0) as fg6_fcstedroomrevenuecurrent,isnull(MAX(fg6_fcstedroomrevenuechange),0.0) as fg6_fcstedroomrevenuechange
     ,isnull(MAX(fg7_fcstedroomrevenuecurrent),0.0) as fg7_fcstedroomrevenuecurrent,isnull(MAX(fg7_fcstedroomrevenuechange),0.0) as fg7_fcstedroomrevenuechange
     ,isnull(MAX(fg8_fcstedroomrevenuecurrent),0.0) as fg8_fcstedroomrevenuecurrent,isnull(MAX(fg8_fcstedroomrevenuechange),0.0) as fg8_fcstedroomrevenuechange
     ,isnull(MAX(fg9_fcstedroomrevenuecurrent),0.0) as fg9_fcstedroomrevenuecurrent,isnull(MAX(fg9_fcstedroomrevenuechange),0.0) as fg9_fcstedroomrevenuechange
     ,isnull(MAX(fg10_fcstedroomrevenuecurrent),0.0) as fg10_fcstedroomrevenuecurrent,isnull(MAX(fg10_fcstedroomrevenuechange),0.0) as fg10_fcstedroomrevenuechange
     ,isnull(MAX(fg11_fcstedroomrevenuecurrent),0.0) as fg11_fcstedroomrevenuecurrent,isnull(MAX(fg11_fcstedroomrevenuechange),0.0) as fg11_fcstedroomrevenuechange
     ,isnull(MAX(fg12_fcstedroomrevenuecurrent),0.0) as fg12_fcstedroomrevenuecurrent,isnull(MAX(fg12_fcstedroomrevenuechange),0.0) as fg12_fcstedroomrevenuechange
     ,isnull(MAX(fg13_fcstedroomrevenuecurrent),0.0) as fg13_fcstedroomrevenuecurrent,isnull(MAX(fg13_fcstedroomrevenuechange),0.0) as fg13_fcstedroomrevenuechange
     ,isnull(MAX(fg14_fcstedroomrevenuecurrent),0.0) as fg14_fcstedroomrevenuecurrent,isnull(MAX(fg14_fcstedroomrevenuechange),0.0) as fg14_fcstedroomrevenuechange
     ,isnull(MAX(fg15_fcstedroomrevenuecurrent),0.0) as fg15_fcstedroomrevenuecurrent,isnull(MAX(fg15_fcstedroomrevenuechange),0.0) as fg15_fcstedroomrevenuechange
     ,isnull(MAX(fg16_fcstedroomrevenuecurrent),0.0) as fg16_fcstedroomrevenuecurrent,isnull(MAX(fg16_fcstedroomrevenuechange),0.0) as fg16_fcstedroomrevenuechange
     ,isnull(MAX(fg17_fcstedroomrevenuecurrent),0.0) as fg17_fcstedroomrevenuecurrent,isnull(MAX(fg17_fcstedroomrevenuechange),0.0) as fg17_fcstedroomrevenuechange
     ,isnull(MAX(fg18_fcstedroomrevenuecurrent),0.0) as fg18_fcstedroomrevenuecurrent,isnull(MAX(fg18_fcstedroomrevenuechange),0.0) as fg18_fcstedroomrevenuechange
     ,isnull(MAX(fg19_fcstedroomrevenuecurrent),0.0) as fg19_fcstedroomrevenuecurrent,isnull(MAX(fg19_fcstedroomrevenuechange),0.0) as fg19_fcstedroomrevenuechange
     ,isnull(MAX(fg20_fcstedroomrevenuecurrent),0.0) as fg20_fcstedroomrevenuecurrent,isnull(MAX(fg20_fcstedroomrevenuechange),0.0) as fg20_fcstedroomrevenuechange
     ,isnull(MAX(fg21_fcstedroomrevenuecurrent),0.0) as fg21_fcstedroomrevenuecurrent,isnull(MAX(fg21_fcstedroomrevenuechange),0.0) as fg21_fcstedroomrevenuechange
     ,isnull(MAX(fg22_fcstedroomrevenuecurrent),0.0) as fg22_fcstedroomrevenuecurrent,isnull(MAX(fg22_fcstedroomrevenuechange),0.0) as fg22_fcstedroomrevenuechange
     ,isnull(MAX(fg23_fcstedroomrevenuecurrent),0.0) as fg23_fcstedroomrevenuecurrent,isnull(MAX(fg23_fcstedroomrevenuechange),0.0) as fg23_fcstedroomrevenuechange
     ,isnull(MAX(fg24_fcstedroomrevenuecurrent),0.0) as fg24_fcstedroomrevenuecurrent,isnull(MAX(fg24_fcstedroomrevenuechange),0.0) as fg24_fcstedroomrevenuechange
     ,isnull(MAX(fg25_fcstedroomrevenuecurrent),0.0) as fg25_fcstedroomrevenuecurrent,isnull(MAX(fg25_fcstedroomrevenuechange),0.0) as fg25_fcstedroomrevenuechange
     ,isnull(MAX(fg26_fcstedroomrevenuecurrent),0.0) as fg26_fcstedroomrevenuecurrent,isnull(MAX(fg26_fcstedroomrevenuechange),0.0) as fg26_fcstedroomrevenuechange
     ,isnull(MAX(fg27_fcstedroomrevenuecurrent),0.0) as fg27_fcstedroomrevenuecurrent,isnull(MAX(fg27_fcstedroomrevenuechange),0.0) as fg27_fcstedroomrevenuechange
     ,isnull(MAX(fg28_fcstedroomrevenuecurrent),0.0) as fg28_fcstedroomrevenuecurrent,isnull(MAX(fg28_fcstedroomrevenuechange),0.0) as fg28_fcstedroomrevenuechange
     ,isnull(MAX(fg29_fcstedroomrevenuecurrent),0.0) as fg29_fcstedroomrevenuecurrent,isnull(MAX(fg29_fcstedroomrevenuechange),0.0) as fg29_fcstedroomrevenuechange
     ,isnull(MAX(fg30_fcstedroomrevenuecurrent),0.0) as fg30_fcstedroomrevenuecurrent,isnull(MAX(fg30_fcstedroomrevenuechange),0.0) as fg30_fcstedroomrevenuechange
     ,isnull(MAX(fg31_fcstedroomrevenuecurrent),0.0) as fg31_fcstedroomrevenuecurrent,isnull(MAX(fg31_fcstedroomrevenuechange),0.0) as fg31_fcstedroomrevenuechange
     ,isnull(MAX(fg32_fcstedroomrevenuecurrent),0.0) as fg32_fcstedroomrevenuecurrent,isnull(MAX(fg32_fcstedroomrevenuechange),0.0) as fg32_fcstedroomrevenuechange
     ,isnull(MAX(fg33_fcstedroomrevenuecurrent),0.0) as fg33_fcstedroomrevenuecurrent,isnull(MAX(fg33_fcstedroomrevenuechange),0.0) as fg33_fcstedroomrevenuechange
     ,isnull(MAX(fg34_fcstedroomrevenuecurrent),0.0) as fg34_fcstedroomrevenuecurrent,isnull(MAX(fg34_fcstedroomrevenuechange),0.0) as fg34_fcstedroomrevenuechange
     ,isnull(MAX(fg35_fcstedroomrevenuecurrent),0.0) as fg35_fcstedroomrevenuecurrent,isnull(MAX(fg35_fcstedroomrevenuechange),0.0) as fg35_fcstedroomrevenuechange
     ,isnull(MAX(fg36_fcstedroomrevenuecurrent),0.0) as fg36_fcstedroomrevenuecurrent,isnull(MAX(fg36_fcstedroomrevenuechange),0.0) as fg36_fcstedroomrevenuechange
     ,isnull(MAX(fg37_fcstedroomrevenuecurrent),0.0) as fg37_fcstedroomrevenuecurrent,isnull(MAX(fg37_fcstedroomrevenuechange),0.0) as fg37_fcstedroomrevenuechange
     ,isnull(MAX(fg38_fcstedroomrevenuecurrent),0.0) as fg38_fcstedroomrevenuecurrent,isnull(MAX(fg38_fcstedroomrevenuechange),0.0) as fg38_fcstedroomrevenuechange
     ,isnull(MAX(fg39_fcstedroomrevenuecurrent),0.0) as fg39_fcstedroomrevenuecurrent,isnull(MAX(fg39_fcstedroomrevenuechange),0.0) as fg39_fcstedroomrevenuechange
     ,isnull(MAX(fg40_fcstedroomrevenuecurrent),0.0) as fg40_fcstedroomrevenuecurrent,isnull(MAX(fg40_fcstedroomrevenuechange),0.0) as fg40_fcstedroomrevenuechange
     ,isnull(MAX(fg41_fcstedroomrevenuecurrent),0.0) as fg41_fcstedroomrevenuecurrent,isnull(MAX(fg41_fcstedroomrevenuechange),0.0) as fg41_fcstedroomrevenuechange
     ,isnull(MAX(fg42_fcstedroomrevenuecurrent),0.0) as fg42_fcstedroomrevenuecurrent,isnull(MAX(fg42_fcstedroomrevenuechange),0.0) as fg42_fcstedroomrevenuechange
     ,isnull(MAX(fg43_fcstedroomrevenuecurrent),0.0) as fg43_fcstedroomrevenuecurrent,isnull(MAX(fg43_fcstedroomrevenuechange),0.0) as fg43_fcstedroomrevenuechange
     ,isnull(MAX(fg44_fcstedroomrevenuecurrent),0.0) as fg44_fcstedroomrevenuecurrent,isnull(MAX(fg44_fcstedroomrevenuechange),0.0) as fg44_fcstedroomrevenuechange
     ,isnull(MAX(fg45_fcstedroomrevenuecurrent),0.0) as fg45_fcstedroomrevenuecurrent,isnull(MAX(fg45_fcstedroomrevenuechange),0.0) as fg45_fcstedroomrevenuechange
     ,isnull(MAX(fg46_fcstedroomrevenuecurrent),0.0) as fg46_fcstedroomrevenuecurrent,isnull(MAX(fg46_fcstedroomrevenuechange),0.0) as fg46_fcstedroomrevenuechange
     ,isnull(MAX(fg47_fcstedroomrevenuecurrent),0.0) as fg47_fcstedroomrevenuecurrent,isnull(MAX(fg47_fcstedroomrevenuechange),0.0) as fg47_fcstedroomrevenuechange
     ,isnull(MAX(fg48_fcstedroomrevenuecurrent),0.0) as fg48_fcstedroomrevenuecurrent,isnull(MAX(fg48_fcstedroomrevenuechange),0.0) as fg48_fcstedroomrevenuechange
     ,isnull(MAX(fg49_fcstedroomrevenuecurrent),0.0) as fg49_fcstedroomrevenuecurrent,isnull(MAX(fg49_fcstedroomrevenuechange),0.0) as fg49_fcstedroomrevenuechange
     ,isnull(MAX(fg50_fcstedroomrevenuecurrent),0.0) as fg50_fcstedroomrevenuecurrent,isnull(MAX(fg50_fcstedroomrevenuechange),0.0) as fg50_fcstedroomrevenuechange

     ,isnull(MAX(fg1_bookedadrcurrent),0.0) as fg1_bookedadrcurrent,isnull(MAX(fg1_bookedadrchange),0.0) as fg1_bookedadrchange
     ,isnull(MAX(fg2_bookedadrcurrent),0.0) as fg2_bookedadrcurrent,isnull(MAX(fg2_bookedadrchange),0.0) as fg2_bookedadrchange
     ,isnull(MAX(fg3_bookedadrcurrent),0.0) as fg3_bookedadrcurrent,isnull(MAX(fg3_bookedadrchange),0.0) as fg3_bookedadrchange
     ,isnull(MAX(fg4_bookedadrcurrent),0.0) as fg4_bookedadrcurrent,isnull(MAX(fg4_bookedadrchange),0.0) as fg4_bookedadrchange
     ,isnull(MAX(fg5_bookedadrcurrent),0.0) as fg5_bookedadrcurrent,isnull(MAX(fg5_bookedadrchange),0.0) as fg5_bookedadrchange
     ,isnull(MAX(fg6_bookedadrcurrent),0.0) as fg6_bookedadrcurrent,isnull(MAX(fg6_bookedadrchange),0.0) as fg6_bookedadrchange
     ,isnull(MAX(fg7_bookedadrcurrent),0.0) as fg7_bookedadrcurrent,isnull(MAX(fg7_bookedadrchange),0.0) as fg7_bookedadrchange
     ,isnull(MAX(fg8_bookedadrcurrent),0.0) as fg8_bookedadrcurrent,isnull(MAX(fg8_bookedadrchange),0.0) as fg8_bookedadrchange
     ,isnull(MAX(fg9_bookedadrcurrent),0.0) as fg9_bookedadrcurrent,isnull(MAX(fg9_bookedadrchange),0.0) as fg9_bookedadrchange
     ,isnull(MAX(fg10_bookedadrcurrent),0.0) as fg10_bookedadrcurrent,isnull(MAX(fg10_bookedadrchange),0.0) as fg10_bookedadrchange
     ,isnull(MAX(fg11_bookedadrcurrent),0.0) as fg11_bookedadrcurrent,isnull(MAX(fg11_bookedadrchange),0.0) as fg11_bookedadrchange
     ,isnull(MAX(fg12_bookedadrcurrent),0.0) as fg12_bookedadrcurrent,isnull(MAX(fg12_bookedadrchange),0.0) as fg12_bookedadrchange
     ,isnull(MAX(fg13_bookedadrcurrent),0.0) as fg13_bookedadrcurrent,isnull(MAX(fg13_bookedadrchange),0.0) as fg13_bookedadrchange
     ,isnull(MAX(fg14_bookedadrcurrent),0.0) as fg14_bookedadrcurrent,isnull(MAX(fg14_bookedadrchange),0.0) as fg14_bookedadrchange
     ,isnull(MAX(fg15_bookedadrcurrent),0.0) as fg15_bookedadrcurrent,isnull(MAX(fg15_bookedadrchange),0.0) as fg15_bookedadrchange
     ,isnull(MAX(fg16_bookedadrcurrent),0.0) as fg16_bookedadrcurrent,isnull(MAX(fg16_bookedadrchange),0.0) as fg16_bookedadrchange
     ,isnull(MAX(fg17_bookedadrcurrent),0.0) as fg17_bookedadrcurrent,isnull(MAX(fg17_bookedadrchange),0.0) as fg17_bookedadrchange
     ,isnull(MAX(fg18_bookedadrcurrent),0.0) as fg18_bookedadrcurrent,isnull(MAX(fg18_bookedadrchange),0.0) as fg18_bookedadrchange
     ,isnull(MAX(fg19_bookedadrcurrent),0.0) as fg19_bookedadrcurrent,isnull(MAX(fg19_bookedadrchange),0.0) as fg19_bookedadrchange
     ,isnull(MAX(fg20_bookedadrcurrent),0.0) as fg20_bookedadrcurrent,isnull(MAX(fg20_bookedadrchange),0.0) as fg20_bookedadrchange
     ,isnull(MAX(fg21_bookedadrcurrent),0.0) as fg21_bookedadrcurrent,isnull(MAX(fg21_bookedadrchange),0.0) as fg21_bookedadrchange
     ,isnull(MAX(fg22_bookedadrcurrent),0.0) as fg22_bookedadrcurrent,isnull(MAX(fg22_bookedadrchange),0.0) as fg22_bookedadrchange
     ,isnull(MAX(fg23_bookedadrcurrent),0.0) as fg23_bookedadrcurrent,isnull(MAX(fg23_bookedadrchange),0.0) as fg23_bookedadrchange
     ,isnull(MAX(fg24_bookedadrcurrent),0.0) as fg24_bookedadrcurrent,isnull(MAX(fg24_bookedadrchange),0.0) as fg24_bookedadrchange
     ,isnull(MAX(fg25_bookedadrcurrent),0.0) as fg25_bookedadrcurrent,isnull(MAX(fg25_bookedadrchange),0.0) as fg25_bookedadrchange
     ,isnull(MAX(fg26_bookedadrcurrent),0.0) as fg26_bookedadrcurrent,isnull(MAX(fg26_bookedadrchange),0.0) as fg26_bookedadrchange
     ,isnull(MAX(fg27_bookedadrcurrent),0.0) as fg27_bookedadrcurrent,isnull(MAX(fg27_bookedadrchange),0.0) as fg27_bookedadrchange
     ,isnull(MAX(fg28_bookedadrcurrent),0.0) as fg28_bookedadrcurrent,isnull(MAX(fg28_bookedadrchange),0.0) as fg28_bookedadrchange
     ,isnull(MAX(fg29_bookedadrcurrent),0.0) as fg29_bookedadrcurrent,isnull(MAX(fg29_bookedadrchange),0.0) as fg29_bookedadrchange
     ,isnull(MAX(fg30_bookedadrcurrent),0.0) as fg30_bookedadrcurrent,isnull(MAX(fg30_bookedadrchange),0.0) as fg30_bookedadrchange
     ,isnull(MAX(fg31_bookedadrcurrent),0.0) as fg31_bookedadrcurrent,isnull(MAX(fg31_bookedadrchange),0.0) as fg31_bookedadrchange
     ,isnull(MAX(fg32_bookedadrcurrent),0.0) as fg32_bookedadrcurrent,isnull(MAX(fg32_bookedadrchange),0.0) as fg32_bookedadrchange
     ,isnull(MAX(fg33_bookedadrcurrent),0.0) as fg33_bookedadrcurrent,isnull(MAX(fg33_bookedadrchange),0.0) as fg33_bookedadrchange
     ,isnull(MAX(fg34_bookedadrcurrent),0.0) as fg34_bookedadrcurrent,isnull(MAX(fg34_bookedadrchange),0.0) as fg34_bookedadrchange
     ,isnull(MAX(fg35_bookedadrcurrent),0.0) as fg35_bookedadrcurrent,isnull(MAX(fg35_bookedadrchange),0.0) as fg35_bookedadrchange
     ,isnull(MAX(fg36_bookedadrcurrent),0.0) as fg36_bookedadrcurrent,isnull(MAX(fg36_bookedadrchange),0.0) as fg36_bookedadrchange
     ,isnull(MAX(fg37_bookedadrcurrent),0.0) as fg37_bookedadrcurrent,isnull(MAX(fg37_bookedadrchange),0.0) as fg37_bookedadrchange
     ,isnull(MAX(fg38_bookedadrcurrent),0.0) as fg38_bookedadrcurrent,isnull(MAX(fg38_bookedadrchange),0.0) as fg38_bookedadrchange
     ,isnull(MAX(fg39_bookedadrcurrent),0.0) as fg39_bookedadrcurrent,isnull(MAX(fg39_bookedadrchange),0.0) as fg39_bookedadrchange
     ,isnull(MAX(fg40_bookedadrcurrent),0.0) as fg40_bookedadrcurrent,isnull(MAX(fg40_bookedadrchange),0.0) as fg40_bookedadrchange
     ,isnull(MAX(fg41_bookedadrcurrent),0.0) as fg41_bookedadrcurrent,isnull(MAX(fg41_bookedadrchange),0.0) as fg41_bookedadrchange
     ,isnull(MAX(fg42_bookedadrcurrent),0.0) as fg42_bookedadrcurrent,isnull(MAX(fg42_bookedadrchange),0.0) as fg42_bookedadrchange
     ,isnull(MAX(fg43_bookedadrcurrent),0.0) as fg43_bookedadrcurrent,isnull(MAX(fg43_bookedadrchange),0.0) as fg43_bookedadrchange
     ,isnull(MAX(fg44_bookedadrcurrent),0.0) as fg44_bookedadrcurrent,isnull(MAX(fg44_bookedadrchange),0.0) as fg44_bookedadrchange
     ,isnull(MAX(fg45_bookedadrcurrent),0.0) as fg45_bookedadrcurrent,isnull(MAX(fg45_bookedadrchange),0.0) as fg45_bookedadrchange
     ,isnull(MAX(fg46_bookedadrcurrent),0.0) as fg46_bookedadrcurrent,isnull(MAX(fg46_bookedadrchange),0.0) as fg46_bookedadrchange
     ,isnull(MAX(fg47_bookedadrcurrent),0.0) as fg47_bookedadrcurrent,isnull(MAX(fg47_bookedadrchange),0.0) as fg47_bookedadrchange
     ,isnull(MAX(fg48_bookedadrcurrent),0.0) as fg48_bookedadrcurrent,isnull(MAX(fg48_bookedadrchange),0.0) as fg48_bookedadrchange
     ,isnull(MAX(fg49_bookedadrcurrent),0.0) as fg49_bookedadrcurrent,isnull(MAX(fg49_bookedadrchange),0.0) as fg49_bookedadrchange
     ,isnull(MAX(fg50_bookedadrcurrent),0.0) as fg50_bookedadrcurrent,isnull(MAX(fg50_bookedadrchange),0.0) as fg50_bookedadrchange

     ,isnull(MAX(fg1_fcstedadrcurrent),0.0) as fg1_fcstedadrcurrent,isnull(MAX(fg1_fcstedadrchange),0.0) as fg1_fcstedadrchange
     ,isnull(MAX(fg2_fcstedadrcurrent),0.0) as fg2_fcstedadrcurrent,isnull(MAX(fg2_fcstedadrchange),0.0) as fg2_fcstedadrchange
     ,isnull(MAX(fg3_fcstedadrcurrent),0.0) as fg3_fcstedadrcurrent,isnull(MAX(fg3_fcstedadrchange),0.0) as fg3_fcstedadrchange
     ,isnull(MAX(fg4_fcstedadrcurrent),0.0) as fg4_fcstedadrcurrent,isnull(MAX(fg4_fcstedadrchange),0.0) as fg4_fcstedadrchange
     ,isnull(MAX(fg5_fcstedadrcurrent),0.0) as fg5_fcstedadrcurrent,isnull(MAX(fg5_fcstedadrchange),0.0) as fg5_fcstedadrchange
     ,isnull(MAX(fg6_fcstedadrcurrent),0.0) as fg6_fcstedadrcurrent,isnull(MAX(fg6_fcstedadrchange),0.0) as fg6_fcstedadrchange
     ,isnull(MAX(fg7_fcstedadrcurrent),0.0) as fg7_fcstedadrcurrent,isnull(MAX(fg7_fcstedadrchange),0.0) as fg7_fcstedadrchange
     ,isnull(MAX(fg8_fcstedadrcurrent),0.0) as fg8_fcstedadrcurrent,isnull(MAX(fg8_fcstedadrchange),0.0) as fg8_fcstedadrchange
     ,isnull(MAX(fg9_fcstedadrcurrent),0.0) as fg9_fcstedadrcurrent,isnull(MAX(fg9_fcstedadrchange),0.0) as fg9_fcstedadrchange
     ,isnull(MAX(fg10_fcstedadrcurrent),0.0) as fg10_fcstedadrcurrent,isnull(MAX(fg10_fcstedadrchange),0.0) as fg10_fcstedadrchange
     ,isnull(MAX(fg11_fcstedadrcurrent),0.0) as fg11_fcstedadrcurrent,isnull(MAX(fg11_fcstedadrchange),0.0) as fg11_fcstedadrchange
     ,isnull(MAX(fg12_fcstedadrcurrent),0.0) as fg12_fcstedadrcurrent,isnull(MAX(fg12_fcstedadrchange),0.0) as fg12_fcstedadrchange
     ,isnull(MAX(fg13_fcstedadrcurrent),0.0) as fg13_fcstedadrcurrent,isnull(MAX(fg13_fcstedadrchange),0.0) as fg13_fcstedadrchange
     ,isnull(MAX(fg14_fcstedadrcurrent),0.0) as fg14_fcstedadrcurrent,isnull(MAX(fg14_fcstedadrchange),0.0) as fg14_fcstedadrchange
     ,isnull(MAX(fg15_fcstedadrcurrent),0.0) as fg15_fcstedadrcurrent,isnull(MAX(fg15_fcstedadrchange),0.0) as fg15_fcstedadrchange
     ,isnull(MAX(fg16_fcstedadrcurrent),0.0) as fg16_fcstedadrcurrent,isnull(MAX(fg16_fcstedadrchange),0.0) as fg16_fcstedadrchange
     ,isnull(MAX(fg17_fcstedadrcurrent),0.0) as fg17_fcstedadrcurrent,isnull(MAX(fg17_fcstedadrchange),0.0) as fg17_fcstedadrchange
     ,isnull(MAX(fg18_fcstedadrcurrent),0.0) as fg18_fcstedadrcurrent,isnull(MAX(fg18_fcstedadrchange),0.0) as fg18_fcstedadrchange
     ,isnull(MAX(fg19_fcstedadrcurrent),0.0) as fg19_fcstedadrcurrent,isnull(MAX(fg19_fcstedadrchange),0.0) as fg19_fcstedadrchange
     ,isnull(MAX(fg20_fcstedadrcurrent),0.0) as fg20_fcstedadrcurrent,isnull(MAX(fg20_fcstedadrchange),0.0) as fg20_fcstedadrchange
     ,isnull(MAX(fg21_fcstedadrcurrent),0.0) as fg21_fcstedadrcurrent,isnull(MAX(fg21_fcstedadrchange),0.0) as fg21_fcstedadrchange
     ,isnull(MAX(fg22_fcstedadrcurrent),0.0) as fg22_fcstedadrcurrent,isnull(MAX(fg22_fcstedadrchange),0.0) as fg22_fcstedadrchange
     ,isnull(MAX(fg23_fcstedadrcurrent),0.0) as fg23_fcstedadrcurrent,isnull(MAX(fg23_fcstedadrchange),0.0) as fg23_fcstedadrchange
     ,isnull(MAX(fg24_fcstedadrcurrent),0.0) as fg24_fcstedadrcurrent,isnull(MAX(fg24_fcstedadrchange),0.0) as fg24_fcstedadrchange
     ,isnull(MAX(fg25_fcstedadrcurrent),0.0) as fg25_fcstedadrcurrent,isnull(MAX(fg25_fcstedadrchange),0.0) as fg25_fcstedadrchange
     ,isnull(MAX(fg26_fcstedadrcurrent),0.0) as fg26_fcstedadrcurrent,isnull(MAX(fg26_fcstedadrchange),0.0) as fg26_fcstedadrchange
     ,isnull(MAX(fg27_fcstedadrcurrent),0.0) as fg27_fcstedadrcurrent,isnull(MAX(fg27_fcstedadrchange),0.0) as fg27_fcstedadrchange
     ,isnull(MAX(fg28_fcstedadrcurrent),0.0) as fg28_fcstedadrcurrent,isnull(MAX(fg28_fcstedadrchange),0.0) as fg28_fcstedadrchange
     ,isnull(MAX(fg29_fcstedadrcurrent),0.0) as fg29_fcstedadrcurrent,isnull(MAX(fg29_fcstedadrchange),0.0) as fg29_fcstedadrchange
     ,isnull(MAX(fg30_fcstedadrcurrent),0.0) as fg30_fcstedadrcurrent,isnull(MAX(fg30_fcstedadrchange),0.0) as fg30_fcstedadrchange
     ,isnull(MAX(fg31_fcstedadrcurrent),0.0) as fg31_fcstedadrcurrent,isnull(MAX(fg31_fcstedadrchange),0.0) as fg31_fcstedadrchange
     ,isnull(MAX(fg32_fcstedadrcurrent),0.0) as fg32_fcstedadrcurrent,isnull(MAX(fg32_fcstedadrchange),0.0) as fg32_fcstedadrchange
     ,isnull(MAX(fg33_fcstedadrcurrent),0.0) as fg33_fcstedadrcurrent,isnull(MAX(fg33_fcstedadrchange),0.0) as fg33_fcstedadrchange
     ,isnull(MAX(fg34_fcstedadrcurrent),0.0) as fg34_fcstedadrcurrent,isnull(MAX(fg34_fcstedadrchange),0.0) as fg34_fcstedadrchange
     ,isnull(MAX(fg35_fcstedadrcurrent),0.0) as fg35_fcstedadrcurrent,isnull(MAX(fg35_fcstedadrchange),0.0) as fg35_fcstedadrchange
     ,isnull(MAX(fg36_fcstedadrcurrent),0.0) as fg36_fcstedadrcurrent,isnull(MAX(fg36_fcstedadrchange),0.0) as fg36_fcstedadrchange
     ,isnull(MAX(fg37_fcstedadrcurrent),0.0) as fg37_fcstedadrcurrent,isnull(MAX(fg37_fcstedadrchange),0.0) as fg37_fcstedadrchange
     ,isnull(MAX(fg38_fcstedadrcurrent),0.0) as fg38_fcstedadrcurrent,isnull(MAX(fg38_fcstedadrchange),0.0) as fg38_fcstedadrchange
     ,isnull(MAX(fg39_fcstedadrcurrent),0.0) as fg39_fcstedadrcurrent,isnull(MAX(fg39_fcstedadrchange),0.0) as fg39_fcstedadrchange
     ,isnull(MAX(fg40_fcstedadrcurrent),0.0) as fg40_fcstedadrcurrent,isnull(MAX(fg40_fcstedadrchange),0.0) as fg40_fcstedadrchange
     ,isnull(MAX(fg41_fcstedadrcurrent),0.0) as fg41_fcstedadrcurrent,isnull(MAX(fg41_fcstedadrchange),0.0) as fg41_fcstedadrchange
     ,isnull(MAX(fg42_fcstedadrcurrent),0.0) as fg42_fcstedadrcurrent,isnull(MAX(fg42_fcstedadrchange),0.0) as fg42_fcstedadrchange
     ,isnull(MAX(fg43_fcstedadrcurrent),0.0) as fg43_fcstedadrcurrent,isnull(MAX(fg43_fcstedadrchange),0.0) as fg43_fcstedadrchange
     ,isnull(MAX(fg44_fcstedadrcurrent),0.0) as fg44_fcstedadrcurrent,isnull(MAX(fg44_fcstedadrchange),0.0) as fg44_fcstedadrchange
     ,isnull(MAX(fg45_fcstedadrcurrent),0.0) as fg45_fcstedadrcurrent,isnull(MAX(fg45_fcstedadrchange),0.0) as fg45_fcstedadrchange
     ,isnull(MAX(fg46_fcstedadrcurrent),0.0) as fg46_fcstedadrcurrent,isnull(MAX(fg46_fcstedadrchange),0.0) as fg46_fcstedadrchange
     ,isnull(MAX(fg47_fcstedadrcurrent),0.0) as fg47_fcstedadrcurrent,isnull(MAX(fg47_fcstedadrchange),0.0) as fg47_fcstedadrchange
     ,isnull(MAX(fg48_fcstedadrcurrent),0.0) as fg48_fcstedadrcurrent,isnull(MAX(fg48_fcstedadrchange),0.0) as fg48_fcstedadrchange
     ,isnull(MAX(fg49_fcstedadrcurrent),0.0) as fg49_fcstedadrcurrent,isnull(MAX(fg49_fcstedadrchange),0.0) as fg49_fcstedadrchange
     ,isnull(MAX(fg50_fcstedadrcurrent),0.0) as fg50_fcstedadrcurrent,isnull(MAX(fg50_fcstedadrchange),0.0) as fg50_fcstedadrchange

     ,isnull(MAX(fg1_block),0.0)  as fg1_block, isnull(MAX(fg1_block_available),0.0)  as fg1_block_available ,isnull(MAX(fg1_block_pickup),0.0)  as fg1_block_pickup
     ,isnull(MAX(fg2_block),0.0)  as fg2_block, isnull(MAX(fg2_block_available),0.0)  as fg2_block_available ,isnull(MAX(fg2_block_pickup),0.0)  as fg2_block_pickup
     ,isnull(MAX(fg3_block),0.0)  as fg3_block, isnull(MAX(fg3_block_available),0.0)  as fg3_block_available ,isnull(MAX(fg3_block_pickup),0.0)  as fg3_block_pickup
     ,isnull(MAX(fg4_block),0.0)  as fg4_block, isnull(MAX(fg4_block_available),0.0)  as fg4_block_available ,isnull(MAX(fg4_block_pickup),0.0)  as fg4_block_pickup
     ,isnull(MAX(fg5_block),0.0)  as fg5_block, isnull(MAX(fg5_block_available),0.0)  as fg5_block_available ,isnull(MAX(fg5_block_pickup),0.0)  as fg5_block_pickup
     ,isnull(MAX(fg6_block),0.0)  as fg6_block, isnull(MAX(fg6_block_available),0.0)  as fg6_block_available ,isnull(MAX(fg6_block_pickup),0.0)  as fg6_block_pickup
     ,isnull(MAX(fg7_block),0.0)  as fg7_block, isnull(MAX(fg7_block_available),0.0)  as fg7_block_available ,isnull(MAX(fg7_block_pickup),0.0)  as fg7_block_pickup
     ,isnull(MAX(fg8_block),0.0)  as fg8_block, isnull(MAX(fg8_block_available),0.0)  as fg8_block_available ,isnull(MAX(fg8_block_pickup),0.0)  as fg8_block_pickup
     ,isnull(MAX(fg9_block),0.0)  as fg9_block, isnull(MAX(fg9_block_available),0.0)  as fg9_block_available ,isnull(MAX(fg9_block_pickup),0.0)  as fg9_block_pickup
     ,isnull(MAX(fg10_block),0.0) as fg10_block,isnull(MAX(fg10_block_available),0.0) as fg10_block_available,isnull(MAX(fg10_block_pickup),0.0) as fg10_block_pickup
     ,isnull(MAX(fg11_block),0.0) as fg11_block,isnull(MAX(fg11_block_available),0.0) as fg11_block_available,isnull(MAX(fg11_block_pickup),0.0) as fg11_block_pickup
     ,isnull(MAX(fg12_block),0.0) as fg12_block,isnull(MAX(fg12_block_available),0.0) as fg12_block_available,isnull(MAX(fg12_block_pickup),0.0) as fg12_block_pickup
     ,isnull(MAX(fg13_block),0.0) as fg13_block,isnull(MAX(fg13_block_available),0.0) as fg13_block_available,isnull(MAX(fg13_block_pickup),0.0) as fg13_block_pickup
     ,isnull(MAX(fg14_block),0.0) as fg14_block,isnull(MAX(fg14_block_available),0.0) as fg14_block_available,isnull(MAX(fg14_block_pickup),0.0) as fg14_block_pickup
     ,isnull(MAX(fg15_block),0.0) as fg15_block,isnull(MAX(fg15_block_available),0.0) as fg15_block_available,isnull(MAX(fg15_block_pickup),0.0) as fg15_block_pickup
     ,isnull(MAX(fg16_block),0.0) as fg16_block,isnull(MAX(fg16_block_available),0.0) as fg16_block_available,isnull(MAX(fg16_block_pickup),0.0) as fg16_block_pickup
     ,isnull(MAX(fg17_block),0.0) as fg17_block,isnull(MAX(fg17_block_available),0.0) as fg17_block_available,isnull(MAX(fg17_block_pickup),0.0) as fg17_block_pickup
     ,isnull(MAX(fg18_block),0.0) as fg18_block,isnull(MAX(fg18_block_available),0.0) as fg18_block_available,isnull(MAX(fg18_block_pickup),0.0) as fg18_block_pickup
     ,isnull(MAX(fg19_block),0.0) as fg19_block,isnull(MAX(fg19_block_available),0.0) as fg19_block_available,isnull(MAX(fg19_block_pickup),0.0) as fg19_block_pickup
     ,isnull(MAX(fg20_block),0.0) as fg20_block,isnull(MAX(fg20_block_available),0.0) as fg20_block_available,isnull(MAX(fg20_block_pickup),0.0) as fg20_block_pickup
     ,isnull(MAX(fg21_block),0.0) as fg21_block,isnull(MAX(fg21_block_available),0.0) as fg21_block_available,isnull(MAX(fg21_block_pickup),0.0) as fg21_block_pickup
     ,isnull(MAX(fg22_block),0.0) as fg22_block,isnull(MAX(fg22_block_available),0.0) as fg22_block_available,isnull(MAX(fg22_block_pickup),0.0) as fg22_block_pickup
     ,isnull(MAX(fg23_block),0.0) as fg23_block,isnull(MAX(fg23_block_available),0.0) as fg23_block_available,isnull(MAX(fg23_block_pickup),0.0) as fg23_block_pickup
     ,isnull(MAX(fg24_block),0.0) as fg24_block,isnull(MAX(fg24_block_available),0.0) as fg24_block_available,isnull(MAX(fg24_block_pickup),0.0) as fg24_block_pickup
     ,isnull(MAX(fg25_block),0.0) as fg25_block,isnull(MAX(fg25_block_available),0.0) as fg25_block_available,isnull(MAX(fg25_block_pickup),0.0) as fg25_block_pickup
     ,isnull(MAX(fg26_block),0.0) as fg26_block,isnull(MAX(fg26_block_available),0.0) as fg26_block_available,isnull(MAX(fg26_block_pickup),0.0) as fg26_block_pickup
     ,isnull(MAX(fg27_block),0.0) as fg27_block,isnull(MAX(fg27_block_available),0.0) as fg27_block_available,isnull(MAX(fg27_block_pickup),0.0) as fg27_block_pickup
     ,isnull(MAX(fg28_block),0.0) as fg28_block,isnull(MAX(fg28_block_available),0.0) as fg28_block_available,isnull(MAX(fg28_block_pickup),0.0) as fg28_block_pickup
     ,isnull(MAX(fg29_block),0.0) as fg29_block,isnull(MAX(fg29_block_available),0.0) as fg29_block_available,isnull(MAX(fg29_block_pickup),0.0) as fg29_block_pickup
     ,isnull(MAX(fg30_block),0.0) as fg30_block,isnull(MAX(fg30_block_available),0.0) as fg30_block_available,isnull(MAX(fg30_block_pickup),0.0) as fg30_block_pickup
     ,isnull(MAX(fg31_block),0.0) as fg31_block,isnull(MAX(fg31_block_available),0.0) as fg31_block_available,isnull(MAX(fg31_block_pickup),0.0) as fg31_block_pickup
     ,isnull(MAX(fg32_block),0.0) as fg32_block,isnull(MAX(fg32_block_available),0.0) as fg32_block_available,isnull(MAX(fg32_block_pickup),0.0) as fg32_block_pickup
     ,isnull(MAX(fg33_block),0.0) as fg33_block,isnull(MAX(fg33_block_available),0.0) as fg33_block_available,isnull(MAX(fg33_block_pickup),0.0) as fg33_block_pickup
     ,isnull(MAX(fg34_block),0.0) as fg34_block,isnull(MAX(fg34_block_available),0.0) as fg34_block_available,isnull(MAX(fg34_block_pickup),0.0) as fg34_block_pickup
     ,isnull(MAX(fg35_block),0.0) as fg35_block,isnull(MAX(fg35_block_available),0.0) as fg35_block_available,isnull(MAX(fg35_block_pickup),0.0) as fg35_block_pickup
     ,isnull(MAX(fg36_block),0.0) as fg36_block,isnull(MAX(fg36_block_available),0.0) as fg36_block_available,isnull(MAX(fg36_block_pickup),0.0) as fg36_block_pickup
     ,isnull(MAX(fg37_block),0.0) as fg37_block,isnull(MAX(fg37_block_available),0.0) as fg37_block_available,isnull(MAX(fg37_block_pickup),0.0) as fg37_block_pickup
     ,isnull(MAX(fg38_block),0.0) as fg38_block,isnull(MAX(fg38_block_available),0.0) as fg38_block_available,isnull(MAX(fg38_block_pickup),0.0) as fg38_block_pickup
     ,isnull(MAX(fg39_block),0.0) as fg39_block,isnull(MAX(fg39_block_available),0.0) as fg39_block_available,isnull(MAX(fg39_block_pickup),0.0) as fg39_block_pickup
     ,isnull(MAX(fg40_block),0.0) as fg40_block,isnull(MAX(fg40_block_available),0.0) as fg40_block_available,isnull(MAX(fg40_block_pickup),0.0) as fg40_block_pickup
     ,isnull(MAX(fg41_block),0.0) as fg41_block,isnull(MAX(fg41_block_available),0.0) as fg41_block_available,isnull(MAX(fg41_block_pickup),0.0) as fg41_block_pickup
     ,isnull(MAX(fg42_block),0.0) as fg42_block,isnull(MAX(fg42_block_available),0.0) as fg42_block_available,isnull(MAX(fg42_block_pickup),0.0) as fg42_block_pickup
     ,isnull(MAX(fg43_block),0.0) as fg43_block,isnull(MAX(fg43_block_available),0.0) as fg43_block_available,isnull(MAX(fg43_block_pickup),0.0) as fg43_block_pickup
     ,isnull(MAX(fg44_block),0.0) as fg44_block,isnull(MAX(fg44_block_available),0.0) as fg44_block_available,isnull(MAX(fg44_block_pickup),0.0) as fg44_block_pickup
     ,isnull(MAX(fg45_block),0.0) as fg45_block,isnull(MAX(fg45_block_available),0.0) as fg45_block_available,isnull(MAX(fg45_block_pickup),0.0) as fg45_block_pickup
     ,isnull(MAX(fg46_block),0.0) as fg46_block,isnull(MAX(fg46_block_available),0.0) as fg46_block_available,isnull(MAX(fg46_block_pickup),0.0) as fg46_block_pickup
     ,isnull(MAX(fg47_block),0.0) as fg47_block,isnull(MAX(fg47_block_available),0.0) as fg47_block_available,isnull(MAX(fg47_block_pickup),0.0) as fg47_block_pickup
     ,isnull(MAX(fg48_block),0.0) as fg48_block,isnull(MAX(fg48_block_available),0.0) as fg48_block_available,isnull(MAX(fg48_block_pickup),0.0) as fg48_block_pickup
     ,isnull(MAX(fg49_block),0.0) as fg49_block,isnull(MAX(fg49_block_available),0.0) as fg49_block_available,isnull(MAX(fg49_block_pickup),0.0) as fg49_block_pickup
     ,isnull(MAX(fg50_block),0.0) as fg50_block,isnull(MAX(fg50_block_available),0.0) as fg50_block_available,isnull(MAX(fg50_block_pickup),0.0) as fg50_block_pickup


     ,isnull(MAX(fg1_profit_onBooks_current),0.0) as fg1_profit_onBooks_current,isnull(MAX(fg1_profit_onBooks_change),0.0) as fg1_profit_onBooks_change
     ,isnull(MAX(fg2_profit_onBooks_current),0.0) as fg2_profit_onBooks_current,isnull(MAX(fg2_profit_onBooks_change),0.0) as fg2_profit_onBooks_change
     ,isnull(MAX(fg3_profit_onBooks_current),0.0) as fg3_profit_onBooks_current,isnull(MAX(fg3_profit_onBooks_change),0.0) as fg3_profit_onBooks_change
     ,isnull(MAX(fg4_profit_onBooks_current),0.0) as fg4_profit_onBooks_current,isnull(MAX(fg4_profit_onBooks_change),0.0) as fg4_profit_onBooks_change
     ,isnull(MAX(fg5_profit_onBooks_current),0.0) as fg5_profit_onBooks_current,isnull(MAX(fg5_profit_onBooks_change),0.0) as fg5_profit_onBooks_change
     ,isnull(MAX(fg6_profit_onBooks_current),0.0) as fg6_profit_onBooks_current,isnull(MAX(fg6_profit_onBooks_change),0.0) as fg6_profit_onBooks_change
     ,isnull(MAX(fg7_profit_onBooks_current),0.0) as fg7_profit_onBooks_current,isnull(MAX(fg7_profit_onBooks_change),0.0) as fg7_profit_onBooks_change
     ,isnull(MAX(fg8_profit_onBooks_current),0.0) as fg8_profit_onBooks_current,isnull(MAX(fg8_profit_onBooks_change),0.0) as fg8_profit_onBooks_change
     ,isnull(MAX(fg9_profit_onBooks_current),0.0) as fg9_profit_onBooks_current,isnull(MAX(fg9_profit_onBooks_change),0.0) as fg9_profit_onBooks_change
     ,isnull(MAX(fg10_profit_onBooks_current),0.0) as fg10_profit_onBooks_current,isnull(MAX(fg10_profit_onBooks_change),0.0) as fg10_profit_onBooks_change
     ,isnull(MAX(fg11_profit_onBooks_current),0.0) as fg11_profit_onBooks_current,isnull(MAX(fg11_profit_onBooks_change),0.0) as fg11_profit_onBooks_change
     ,isnull(MAX(fg12_profit_onBooks_current),0.0) as fg12_profit_onBooks_current,isnull(MAX(fg12_profit_onBooks_change),0.0) as fg12_profit_onBooks_change
     ,isnull(MAX(fg13_profit_onBooks_current),0.0) as fg13_profit_onBooks_current,isnull(MAX(fg13_profit_onBooks_change),0.0) as fg13_profit_onBooks_change
     ,isnull(MAX(fg14_profit_onBooks_current),0.0) as fg14_profit_onBooks_current,isnull(MAX(fg14_profit_onBooks_change),0.0) as fg14_profit_onBooks_change
     ,isnull(MAX(fg15_profit_onBooks_current),0.0) as fg15_profit_onBooks_current,isnull(MAX(fg15_profit_onBooks_change),0.0) as fg15_profit_onBooks_change
     ,isnull(MAX(fg16_profit_onBooks_current),0.0) as fg16_profit_onBooks_current,isnull(MAX(fg16_profit_onBooks_change),0.0) as fg16_profit_onBooks_change
     ,isnull(MAX(fg17_profit_onBooks_current),0.0) as fg17_profit_onBooks_current,isnull(MAX(fg17_profit_onBooks_change),0.0) as fg17_profit_onBooks_change
     ,isnull(MAX(fg18_profit_onBooks_current),0.0) as fg18_profit_onBooks_current,isnull(MAX(fg18_profit_onBooks_change),0.0) as fg18_profit_onBooks_change
     ,isnull(MAX(fg19_profit_onBooks_current),0.0) as fg19_profit_onBooks_current,isnull(MAX(fg19_profit_onBooks_change),0.0) as fg19_profit_onBooks_change
     ,isnull(MAX(fg20_profit_onBooks_current),0.0) as fg20_profit_onBooks_current,isnull(MAX(fg20_profit_onBooks_change),0.0) as fg20_profit_onBooks_change
     ,isnull(MAX(fg21_profit_onBooks_current),0.0) as fg21_profit_onBooks_current,isnull(MAX(fg21_profit_onBooks_change),0.0) as fg21_profit_onBooks_change
     ,isnull(MAX(fg22_profit_onBooks_current),0.0) as fg22_profit_onBooks_current,isnull(MAX(fg22_profit_onBooks_change),0.0) as fg22_profit_onBooks_change
     ,isnull(MAX(fg23_profit_onBooks_current),0.0) as fg23_profit_onBooks_current,isnull(MAX(fg23_profit_onBooks_change),0.0) as fg23_profit_onBooks_change
     ,isnull(MAX(fg24_profit_onBooks_current),0.0) as fg24_profit_onBooks_current,isnull(MAX(fg24_profit_onBooks_change),0.0) as fg24_profit_onBooks_change
     ,isnull(MAX(fg25_profit_onBooks_current),0.0) as fg25_profit_onBooks_current,isnull(MAX(fg25_profit_onBooks_change),0.0) as fg25_profit_onBooks_change
     ,isnull(MAX(fg26_profit_onBooks_current),0.0) as fg26_profit_onBooks_current,isnull(MAX(fg26_profit_onBooks_change),0.0) as fg26_profit_onBooks_change
     ,isnull(MAX(fg27_profit_onBooks_current),0.0) as fg27_profit_onBooks_current,isnull(MAX(fg27_profit_onBooks_change),0.0) as fg27_profit_onBooks_change
     ,isnull(MAX(fg28_profit_onBooks_current),0.0) as fg28_profit_onBooks_current,isnull(MAX(fg28_profit_onBooks_change),0.0) as fg28_profit_onBooks_change
     ,isnull(MAX(fg29_profit_onBooks_current),0.0) as fg29_profit_onBooks_current,isnull(MAX(fg29_profit_onBooks_change),0.0) as fg29_profit_onBooks_change
     ,isnull(MAX(fg30_profit_onBooks_current),0.0) as fg30_profit_onBooks_current,isnull(MAX(fg30_profit_onBooks_change),0.0) as fg30_profit_onBooks_change
     ,isnull(MAX(fg31_profit_onBooks_current),0.0) as fg31_profit_onBooks_current,isnull(MAX(fg31_profit_onBooks_change),0.0) as fg31_profit_onBooks_change
     ,isnull(MAX(fg32_profit_onBooks_current),0.0) as fg32_profit_onBooks_current,isnull(MAX(fg32_profit_onBooks_change),0.0) as fg32_profit_onBooks_change
     ,isnull(MAX(fg33_profit_onBooks_current),0.0) as fg33_profit_onBooks_current,isnull(MAX(fg33_profit_onBooks_change),0.0) as fg33_profit_onBooks_change
     ,isnull(MAX(fg34_profit_onBooks_current),0.0) as fg34_profit_onBooks_current,isnull(MAX(fg34_profit_onBooks_change),0.0) as fg34_profit_onBooks_change
     ,isnull(MAX(fg35_profit_onBooks_current),0.0) as fg35_profit_onBooks_current,isnull(MAX(fg35_profit_onBooks_change),0.0) as fg35_profit_onBooks_change
     ,isnull(MAX(fg36_profit_onBooks_current),0.0) as fg36_profit_onBooks_current,isnull(MAX(fg36_profit_onBooks_change),0.0) as fg36_profit_onBooks_change
     ,isnull(MAX(fg37_profit_onBooks_current),0.0) as fg37_profit_onBooks_current,isnull(MAX(fg37_profit_onBooks_change),0.0) as fg37_profit_onBooks_change
     ,isnull(MAX(fg38_profit_onBooks_current),0.0) as fg38_profit_onBooks_current,isnull(MAX(fg38_profit_onBooks_change),0.0) as fg38_profit_onBooks_change
     ,isnull(MAX(fg39_profit_onBooks_current),0.0) as fg39_profit_onBooks_current,isnull(MAX(fg39_profit_onBooks_change),0.0) as fg39_profit_onBooks_change
     ,isnull(MAX(fg40_profit_onBooks_current),0.0) as fg40_profit_onBooks_current,isnull(MAX(fg40_profit_onBooks_change),0.0) as fg40_profit_onBooks_change
     ,isnull(MAX(fg41_profit_onBooks_current),0.0) as fg41_profit_onBooks_current,isnull(MAX(fg41_profit_onBooks_change),0.0) as fg41_profit_onBooks_change
     ,isnull(MAX(fg42_profit_onBooks_current),0.0) as fg42_profit_onBooks_current,isnull(MAX(fg42_profit_onBooks_change),0.0) as fg42_profit_onBooks_change
     ,isnull(MAX(fg43_profit_onBooks_current),0.0) as fg43_profit_onBooks_current,isnull(MAX(fg43_profit_onBooks_change),0.0) as fg43_profit_onBooks_change
     ,isnull(MAX(fg44_profit_onBooks_current),0.0) as fg44_profit_onBooks_current,isnull(MAX(fg44_profit_onBooks_change),0.0) as fg44_profit_onBooks_change
     ,isnull(MAX(fg45_profit_onBooks_current),0.0) as fg45_profit_onBooks_current,isnull(MAX(fg45_profit_onBooks_change),0.0) as fg45_profit_onBooks_change
     ,isnull(MAX(fg46_profit_onBooks_current),0.0) as fg46_profit_onBooks_current,isnull(MAX(fg46_profit_onBooks_change),0.0) as fg46_profit_onBooks_change
     ,isnull(MAX(fg47_profit_onBooks_current),0.0) as fg47_profit_onBooks_current,isnull(MAX(fg47_profit_onBooks_change),0.0) as fg47_profit_onBooks_change
     ,isnull(MAX(fg48_profit_onBooks_current),0.0) as fg48_profit_onBooks_current,isnull(MAX(fg48_profit_onBooks_change),0.0) as fg48_profit_onBooks_change
     ,isnull(MAX(fg49_profit_onBooks_current),0.0) as fg49_profit_onBooks_current,isnull(MAX(fg49_profit_onBooks_change),0.0) as fg49_profit_onBooks_change
     ,isnull(MAX(fg50_profit_onBooks_current),0.0) as fg50_profit_onBooks_current,isnull(MAX(fg50_profit_onBooks_change),0.0) as fg50_profit_onBooks_change

     ,isnull(MAX(fg1_proPOR_onBooks_current),0.0) as fg1_proPOR_onBooks_current,isnull(MAX(fg1_proPOR_onBooks_change),0.0) as fg1_proPOR_onBooks_change
     ,isnull(MAX(fg2_proPOR_onBooks_current),0.0) as fg2_proPOR_onBooks_current,isnull(MAX(fg2_proPOR_onBooks_change),0.0) as fg2_proPOR_onBooks_change
     ,isnull(MAX(fg3_proPOR_onBooks_current),0.0) as fg3_proPOR_onBooks_current,isnull(MAX(fg3_proPOR_onBooks_change),0.0) as fg3_proPOR_onBooks_change
     ,isnull(MAX(fg4_proPOR_onBooks_current),0.0) as fg4_proPOR_onBooks_current,isnull(MAX(fg4_proPOR_onBooks_change),0.0) as fg4_proPOR_onBooks_change
     ,isnull(MAX(fg5_proPOR_onBooks_current),0.0) as fg5_proPOR_onBooks_current,isnull(MAX(fg5_proPOR_onBooks_change),0.0) as fg5_proPOR_onBooks_change
     ,isnull(MAX(fg6_proPOR_onBooks_current),0.0) as fg6_proPOR_onBooks_current,isnull(MAX(fg6_proPOR_onBooks_change),0.0) as fg6_proPOR_onBooks_change
     ,isnull(MAX(fg7_proPOR_onBooks_current),0.0) as fg7_proPOR_onBooks_current,isnull(MAX(fg7_proPOR_onBooks_change),0.0) as fg7_proPOR_onBooks_change
     ,isnull(MAX(fg8_proPOR_onBooks_current),0.0) as fg8_proPOR_onBooks_current,isnull(MAX(fg8_proPOR_onBooks_change),0.0) as fg8_proPOR_onBooks_change
     ,isnull(MAX(fg9_proPOR_onBooks_current),0.0) as fg9_proPOR_onBooks_current,isnull(MAX(fg9_proPOR_onBooks_change),0.0) as fg9_proPOR_onBooks_change
     ,isnull(MAX(fg10_proPOR_onBooks_current),0.0) as fg10_proPOR_onBooks_current,isnull(MAX(fg10_proPOR_onBooks_change),0.0) as fg10_proPOR_onBooks_change
     ,isnull(MAX(fg11_proPOR_onBooks_current),0.0) as fg11_proPOR_onBooks_current,isnull(MAX(fg11_proPOR_onBooks_change),0.0) as fg11_proPOR_onBooks_change
     ,isnull(MAX(fg12_proPOR_onBooks_current),0.0) as fg12_proPOR_onBooks_current,isnull(MAX(fg12_proPOR_onBooks_change),0.0) as fg12_proPOR_onBooks_change
     ,isnull(MAX(fg13_proPOR_onBooks_current),0.0) as fg13_proPOR_onBooks_current,isnull(MAX(fg13_proPOR_onBooks_change),0.0) as fg13_proPOR_onBooks_change
     ,isnull(MAX(fg14_proPOR_onBooks_current),0.0) as fg14_proPOR_onBooks_current,isnull(MAX(fg14_proPOR_onBooks_change),0.0) as fg14_proPOR_onBooks_change
     ,isnull(MAX(fg15_proPOR_onBooks_current),0.0) as fg15_proPOR_onBooks_current,isnull(MAX(fg15_proPOR_onBooks_change),0.0) as fg15_proPOR_onBooks_change
     ,isnull(MAX(fg16_proPOR_onBooks_current),0.0) as fg16_proPOR_onBooks_current,isnull(MAX(fg16_proPOR_onBooks_change),0.0) as fg16_proPOR_onBooks_change
     ,isnull(MAX(fg17_proPOR_onBooks_current),0.0) as fg17_proPOR_onBooks_current,isnull(MAX(fg17_proPOR_onBooks_change),0.0) as fg17_proPOR_onBooks_change
     ,isnull(MAX(fg18_proPOR_onBooks_current),0.0) as fg18_proPOR_onBooks_current,isnull(MAX(fg18_proPOR_onBooks_change),0.0) as fg18_proPOR_onBooks_change
     ,isnull(MAX(fg19_proPOR_onBooks_current),0.0) as fg19_proPOR_onBooks_current,isnull(MAX(fg19_proPOR_onBooks_change),0.0) as fg19_proPOR_onBooks_change
     ,isnull(MAX(fg20_proPOR_onBooks_current),0.0) as fg20_proPOR_onBooks_current,isnull(MAX(fg20_proPOR_onBooks_change),0.0) as fg20_proPOR_onBooks_change
     ,isnull(MAX(fg21_proPOR_onBooks_current),0.0) as fg21_proPOR_onBooks_current,isnull(MAX(fg21_proPOR_onBooks_change),0.0) as fg21_proPOR_onBooks_change
     ,isnull(MAX(fg22_proPOR_onBooks_current),0.0) as fg22_proPOR_onBooks_current,isnull(MAX(fg22_proPOR_onBooks_change),0.0) as fg22_proPOR_onBooks_change
     ,isnull(MAX(fg23_proPOR_onBooks_current),0.0) as fg23_proPOR_onBooks_current,isnull(MAX(fg23_proPOR_onBooks_change),0.0) as fg23_proPOR_onBooks_change
     ,isnull(MAX(fg24_proPOR_onBooks_current),0.0) as fg24_proPOR_onBooks_current,isnull(MAX(fg24_proPOR_onBooks_change),0.0) as fg24_proPOR_onBooks_change
     ,isnull(MAX(fg25_proPOR_onBooks_current),0.0) as fg25_proPOR_onBooks_current,isnull(MAX(fg25_proPOR_onBooks_change),0.0) as fg25_proPOR_onBooks_change
     ,isnull(MAX(fg26_proPOR_onBooks_current),0.0) as fg26_proPOR_onBooks_current,isnull(MAX(fg26_proPOR_onBooks_change),0.0) as fg26_proPOR_onBooks_change
     ,isnull(MAX(fg27_proPOR_onBooks_current),0.0) as fg27_proPOR_onBooks_current,isnull(MAX(fg27_proPOR_onBooks_change),0.0) as fg27_proPOR_onBooks_change
     ,isnull(MAX(fg28_proPOR_onBooks_current),0.0) as fg28_proPOR_onBooks_current,isnull(MAX(fg28_proPOR_onBooks_change),0.0) as fg28_proPOR_onBooks_change
     ,isnull(MAX(fg29_proPOR_onBooks_current),0.0) as fg29_proPOR_onBooks_current,isnull(MAX(fg29_proPOR_onBooks_change),0.0) as fg29_proPOR_onBooks_change
     ,isnull(MAX(fg30_proPOR_onBooks_current),0.0) as fg30_proPOR_onBooks_current,isnull(MAX(fg30_proPOR_onBooks_change),0.0) as fg30_proPOR_onBooks_change
     ,isnull(MAX(fg31_proPOR_onBooks_current),0.0) as fg31_proPOR_onBooks_current,isnull(MAX(fg31_proPOR_onBooks_change),0.0) as fg31_proPOR_onBooks_change
     ,isnull(MAX(fg32_proPOR_onBooks_current),0.0) as fg32_proPOR_onBooks_current,isnull(MAX(fg32_proPOR_onBooks_change),0.0) as fg32_proPOR_onBooks_change
     ,isnull(MAX(fg33_proPOR_onBooks_current),0.0) as fg33_proPOR_onBooks_current,isnull(MAX(fg33_proPOR_onBooks_change),0.0) as fg33_proPOR_onBooks_change
     ,isnull(MAX(fg34_proPOR_onBooks_current),0.0) as fg34_proPOR_onBooks_current,isnull(MAX(fg34_proPOR_onBooks_change),0.0) as fg34_proPOR_onBooks_change
     ,isnull(MAX(fg35_proPOR_onBooks_current),0.0) as fg35_proPOR_onBooks_current,isnull(MAX(fg35_proPOR_onBooks_change),0.0) as fg35_proPOR_onBooks_change
     ,isnull(MAX(fg36_proPOR_onBooks_current),0.0) as fg36_proPOR_onBooks_current,isnull(MAX(fg36_proPOR_onBooks_change),0.0) as fg36_proPOR_onBooks_change
     ,isnull(MAX(fg37_proPOR_onBooks_current),0.0) as fg37_proPOR_onBooks_current,isnull(MAX(fg37_proPOR_onBooks_change),0.0) as fg37_proPOR_onBooks_change
     ,isnull(MAX(fg38_proPOR_onBooks_current),0.0) as fg38_proPOR_onBooks_current,isnull(MAX(fg38_proPOR_onBooks_change),0.0) as fg38_proPOR_onBooks_change
     ,isnull(MAX(fg39_proPOR_onBooks_current),0.0) as fg39_proPOR_onBooks_current,isnull(MAX(fg39_proPOR_onBooks_change),0.0) as fg39_proPOR_onBooks_change
     ,isnull(MAX(fg40_proPOR_onBooks_current),0.0) as fg40_proPOR_onBooks_current,isnull(MAX(fg40_proPOR_onBooks_change),0.0) as fg40_proPOR_onBooks_change
     ,isnull(MAX(fg41_proPOR_onBooks_current),0.0) as fg41_proPOR_onBooks_current,isnull(MAX(fg41_proPOR_onBooks_change),0.0) as fg41_proPOR_onBooks_change
     ,isnull(MAX(fg42_proPOR_onBooks_current),0.0) as fg42_proPOR_onBooks_current,isnull(MAX(fg42_proPOR_onBooks_change),0.0) as fg42_proPOR_onBooks_change
     ,isnull(MAX(fg43_proPOR_onBooks_current),0.0) as fg43_proPOR_onBooks_current,isnull(MAX(fg43_proPOR_onBooks_change),0.0) as fg43_proPOR_onBooks_change
     ,isnull(MAX(fg44_proPOR_onBooks_current),0.0) as fg44_proPOR_onBooks_current,isnull(MAX(fg44_proPOR_onBooks_change),0.0) as fg44_proPOR_onBooks_change
     ,isnull(MAX(fg45_proPOR_onBooks_current),0.0) as fg45_proPOR_onBooks_current,isnull(MAX(fg45_proPOR_onBooks_change),0.0) as fg45_proPOR_onBooks_change
     ,isnull(MAX(fg46_proPOR_onBooks_current),0.0) as fg46_proPOR_onBooks_current,isnull(MAX(fg46_proPOR_onBooks_change),0.0) as fg46_proPOR_onBooks_change
     ,isnull(MAX(fg47_proPOR_onBooks_current),0.0) as fg47_proPOR_onBooks_current,isnull(MAX(fg47_proPOR_onBooks_change),0.0) as fg47_proPOR_onBooks_change
     ,isnull(MAX(fg48_proPOR_onBooks_current),0.0) as fg48_proPOR_onBooks_current,isnull(MAX(fg48_proPOR_onBooks_change),0.0) as fg48_proPOR_onBooks_change
     ,isnull(MAX(fg49_proPOR_onBooks_current),0.0) as fg49_proPOR_onBooks_current,isnull(MAX(fg49_proPOR_onBooks_change),0.0) as fg49_proPOR_onBooks_change
     ,isnull(MAX(fg50_proPOR_onBooks_current),0.0) as fg50_proPOR_onBooks_current,isnull(MAX(fg50_proPOR_onBooks_change),0.0) as fg50_proPOR_onBooks_change


     ,isnull(MAX(fg1_profit_forecast_current),0.0) as fg1_profit_forecast_current,isnull(MAX(fg1_profit_forecast_change),0.0) as fg1_profit_forecast_change
     ,isnull(MAX(fg2_profit_forecast_current),0.0) as fg2_profit_forecast_current,isnull(MAX(fg2_profit_forecast_change),0.0) as fg2_profit_forecast_change
     ,isnull(MAX(fg3_profit_forecast_current),0.0) as fg3_profit_forecast_current,isnull(MAX(fg3_profit_forecast_change),0.0) as fg3_profit_forecast_change
     ,isnull(MAX(fg4_profit_forecast_current),0.0) as fg4_profit_forecast_current,isnull(MAX(fg4_profit_forecast_change),0.0) as fg4_profit_forecast_change
     ,isnull(MAX(fg5_profit_forecast_current),0.0) as fg5_profit_forecast_current,isnull(MAX(fg5_profit_forecast_change),0.0) as fg5_profit_forecast_change
     ,isnull(MAX(fg6_profit_forecast_current),0.0) as fg6_profit_forecast_current,isnull(MAX(fg6_profit_forecast_change),0.0) as fg6_profit_forecast_change
     ,isnull(MAX(fg7_profit_forecast_current),0.0) as fg7_profit_forecast_current,isnull(MAX(fg7_profit_forecast_change),0.0) as fg7_profit_forecast_change
     ,isnull(MAX(fg8_profit_forecast_current),0.0) as fg8_profit_forecast_current,isnull(MAX(fg8_profit_forecast_change),0.0) as fg8_profit_forecast_change
     ,isnull(MAX(fg9_profit_forecast_current),0.0) as fg9_profit_forecast_current,isnull(MAX(fg9_profit_forecast_change),0.0) as fg9_profit_forecast_change
     ,isnull(MAX(fg10_profit_forecast_current),0.0) as fg10_profit_forecast_current,isnull(MAX(fg10_profit_forecast_change),0.0) as fg10_profit_forecast_change
     ,isnull(MAX(fg11_profit_forecast_current),0.0) as fg11_profit_forecast_current,isnull(MAX(fg11_profit_forecast_change),0.0) as fg11_profit_forecast_change
     ,isnull(MAX(fg12_profit_forecast_current),0.0) as fg12_profit_forecast_current,isnull(MAX(fg12_profit_forecast_change),0.0) as fg12_profit_forecast_change
     ,isnull(MAX(fg13_profit_forecast_current),0.0) as fg13_profit_forecast_current,isnull(MAX(fg13_profit_forecast_change),0.0) as fg13_profit_forecast_change
     ,isnull(MAX(fg14_profit_forecast_current),0.0) as fg14_profit_forecast_current,isnull(MAX(fg14_profit_forecast_change),0.0) as fg14_profit_forecast_change
     ,isnull(MAX(fg15_profit_forecast_current),0.0) as fg15_profit_forecast_current,isnull(MAX(fg15_profit_forecast_change),0.0) as fg15_profit_forecast_change
     ,isnull(MAX(fg16_profit_forecast_current),0.0) as fg16_profit_forecast_current,isnull(MAX(fg16_profit_forecast_change),0.0) as fg16_profit_forecast_change
     ,isnull(MAX(fg17_profit_forecast_current),0.0) as fg17_profit_forecast_current,isnull(MAX(fg17_profit_forecast_change),0.0) as fg17_profit_forecast_change
     ,isnull(MAX(fg18_profit_forecast_current),0.0) as fg18_profit_forecast_current,isnull(MAX(fg18_profit_forecast_change),0.0) as fg18_profit_forecast_change
     ,isnull(MAX(fg19_profit_forecast_current),0.0) as fg19_profit_forecast_current,isnull(MAX(fg19_profit_forecast_change),0.0) as fg19_profit_forecast_change
     ,isnull(MAX(fg20_profit_forecast_current),0.0) as fg20_profit_forecast_current,isnull(MAX(fg20_profit_forecast_change),0.0) as fg20_profit_forecast_change
     ,isnull(MAX(fg21_profit_forecast_current),0.0) as fg21_profit_forecast_current,isnull(MAX(fg21_profit_forecast_change),0.0) as fg21_profit_forecast_change
     ,isnull(MAX(fg22_profit_forecast_current),0.0) as fg22_profit_forecast_current,isnull(MAX(fg22_profit_forecast_change),0.0) as fg22_profit_forecast_change
     ,isnull(MAX(fg23_profit_forecast_current),0.0) as fg23_profit_forecast_current,isnull(MAX(fg23_profit_forecast_change),0.0) as fg23_profit_forecast_change
     ,isnull(MAX(fg24_profit_forecast_current),0.0) as fg24_profit_forecast_current,isnull(MAX(fg24_profit_forecast_change),0.0) as fg24_profit_forecast_change
     ,isnull(MAX(fg25_profit_forecast_current),0.0) as fg25_profit_forecast_current,isnull(MAX(fg25_profit_forecast_change),0.0) as fg25_profit_forecast_change
     ,isnull(MAX(fg26_profit_forecast_current),0.0) as fg26_profit_forecast_current,isnull(MAX(fg26_profit_forecast_change),0.0) as fg26_profit_forecast_change
     ,isnull(MAX(fg27_profit_forecast_current),0.0) as fg27_profit_forecast_current,isnull(MAX(fg27_profit_forecast_change),0.0) as fg27_profit_forecast_change
     ,isnull(MAX(fg28_profit_forecast_current),0.0) as fg28_profit_forecast_current,isnull(MAX(fg28_profit_forecast_change),0.0) as fg28_profit_forecast_change
     ,isnull(MAX(fg29_profit_forecast_current),0.0) as fg29_profit_forecast_current,isnull(MAX(fg29_profit_forecast_change),0.0) as fg29_profit_forecast_change
     ,isnull(MAX(fg30_profit_forecast_current),0.0) as fg30_profit_forecast_current,isnull(MAX(fg30_profit_forecast_change),0.0) as fg30_profit_forecast_change
     ,isnull(MAX(fg31_profit_forecast_current),0.0) as fg31_profit_forecast_current,isnull(MAX(fg31_profit_forecast_change),0.0) as fg31_profit_forecast_change
     ,isnull(MAX(fg32_profit_forecast_current),0.0) as fg32_profit_forecast_current,isnull(MAX(fg32_profit_forecast_change),0.0) as fg32_profit_forecast_change
     ,isnull(MAX(fg33_profit_forecast_current),0.0) as fg33_profit_forecast_current,isnull(MAX(fg33_profit_forecast_change),0.0) as fg33_profit_forecast_change
     ,isnull(MAX(fg34_profit_forecast_current),0.0) as fg34_profit_forecast_current,isnull(MAX(fg34_profit_forecast_change),0.0) as fg34_profit_forecast_change
     ,isnull(MAX(fg35_profit_forecast_current),0.0) as fg35_profit_forecast_current,isnull(MAX(fg35_profit_forecast_change),0.0) as fg35_profit_forecast_change
     ,isnull(MAX(fg36_profit_forecast_current),0.0) as fg36_profit_forecast_current,isnull(MAX(fg36_profit_forecast_change),0.0) as fg36_profit_forecast_change
     ,isnull(MAX(fg37_profit_forecast_current),0.0) as fg37_profit_forecast_current,isnull(MAX(fg37_profit_forecast_change),0.0) as fg37_profit_forecast_change
     ,isnull(MAX(fg38_profit_forecast_current),0.0) as fg38_profit_forecast_current,isnull(MAX(fg38_profit_forecast_change),0.0) as fg38_profit_forecast_change
     ,isnull(MAX(fg39_profit_forecast_current),0.0) as fg39_profit_forecast_current,isnull(MAX(fg39_profit_forecast_change),0.0) as fg39_profit_forecast_change
     ,isnull(MAX(fg40_profit_forecast_current),0.0) as fg40_profit_forecast_current,isnull(MAX(fg40_profit_forecast_change),0.0) as fg40_profit_forecast_change
     ,isnull(MAX(fg41_profit_forecast_current),0.0) as fg41_profit_forecast_current,isnull(MAX(fg41_profit_forecast_change),0.0) as fg41_profit_forecast_change
     ,isnull(MAX(fg42_profit_forecast_current),0.0) as fg42_profit_forecast_current,isnull(MAX(fg42_profit_forecast_change),0.0) as fg42_profit_forecast_change
     ,isnull(MAX(fg43_profit_forecast_current),0.0) as fg43_profit_forecast_current,isnull(MAX(fg43_profit_forecast_change),0.0) as fg43_profit_forecast_change
     ,isnull(MAX(fg44_profit_forecast_current),0.0) as fg44_profit_forecast_current,isnull(MAX(fg44_profit_forecast_change),0.0) as fg44_profit_forecast_change
     ,isnull(MAX(fg45_profit_forecast_current),0.0) as fg45_profit_forecast_current,isnull(MAX(fg45_profit_forecast_change),0.0) as fg45_profit_forecast_change
     ,isnull(MAX(fg46_profit_forecast_current),0.0) as fg46_profit_forecast_current,isnull(MAX(fg46_profit_forecast_change),0.0) as fg46_profit_forecast_change
     ,isnull(MAX(fg47_profit_forecast_current),0.0) as fg47_profit_forecast_current,isnull(MAX(fg47_profit_forecast_change),0.0) as fg47_profit_forecast_change
     ,isnull(MAX(fg48_profit_forecast_current),0.0) as fg48_profit_forecast_current,isnull(MAX(fg48_profit_forecast_change),0.0) as fg48_profit_forecast_change
     ,isnull(MAX(fg49_profit_forecast_current),0.0) as fg49_profit_forecast_current,isnull(MAX(fg49_profit_forecast_change),0.0) as fg49_profit_forecast_change
     ,isnull(MAX(fg50_profit_forecast_current),0.0) as fg50_profit_forecast_current,isnull(MAX(fg50_profit_forecast_change),0.0) as fg50_profit_forecast_change

     ,isnull(MAX(fg1_proPOR_forecast_current),0.0) as fg1_proPOR_forecast_current,isnull(MAX(fg1_proPOR_forecast_change),0.0) as fg1_proPOR_forecast_change
     ,isnull(MAX(fg2_proPOR_forecast_current),0.0) as fg2_proPOR_forecast_current,isnull(MAX(fg2_proPOR_forecast_change),0.0) as fg2_proPOR_forecast_change
     ,isnull(MAX(fg3_proPOR_forecast_current),0.0) as fg3_proPOR_forecast_current,isnull(MAX(fg3_proPOR_forecast_change),0.0) as fg3_proPOR_forecast_change
     ,isnull(MAX(fg4_proPOR_forecast_current),0.0) as fg4_proPOR_forecast_current,isnull(MAX(fg4_proPOR_forecast_change),0.0) as fg4_proPOR_forecast_change
     ,isnull(MAX(fg5_proPOR_forecast_current),0.0) as fg5_proPOR_forecast_current,isnull(MAX(fg5_proPOR_forecast_change),0.0) as fg5_proPOR_forecast_change
     ,isnull(MAX(fg6_proPOR_forecast_current),0.0) as fg6_proPOR_forecast_current,isnull(MAX(fg6_proPOR_forecast_change),0.0) as fg6_proPOR_forecast_change
     ,isnull(MAX(fg7_proPOR_forecast_current),0.0) as fg7_proPOR_forecast_current,isnull(MAX(fg7_proPOR_forecast_change),0.0) as fg7_proPOR_forecast_change
     ,isnull(MAX(fg8_proPOR_forecast_current),0.0) as fg8_proPOR_forecast_current,isnull(MAX(fg8_proPOR_forecast_change),0.0) as fg8_proPOR_forecast_change
     ,isnull(MAX(fg9_proPOR_forecast_current),0.0) as fg9_proPOR_forecast_current,isnull(MAX(fg9_proPOR_forecast_change),0.0) as fg9_proPOR_forecast_change
     ,isnull(MAX(fg10_proPOR_forecast_current),0.0) as fg10_proPOR_forecast_current,isnull(MAX(fg10_proPOR_forecast_change),0.0) as fg10_proPOR_forecast_change
     ,isnull(MAX(fg11_proPOR_forecast_current),0.0) as fg11_proPOR_forecast_current,isnull(MAX(fg11_proPOR_forecast_change),0.0) as fg11_proPOR_forecast_change
     ,isnull(MAX(fg12_proPOR_forecast_current),0.0) as fg12_proPOR_forecast_current,isnull(MAX(fg12_proPOR_forecast_change),0.0) as fg12_proPOR_forecast_change
     ,isnull(MAX(fg13_proPOR_forecast_current),0.0) as fg13_proPOR_forecast_current,isnull(MAX(fg13_proPOR_forecast_change),0.0) as fg13_proPOR_forecast_change
     ,isnull(MAX(fg14_proPOR_forecast_current),0.0) as fg14_proPOR_forecast_current,isnull(MAX(fg14_proPOR_forecast_change),0.0) as fg14_proPOR_forecast_change
     ,isnull(MAX(fg15_proPOR_forecast_current),0.0) as fg15_proPOR_forecast_current,isnull(MAX(fg15_proPOR_forecast_change),0.0) as fg15_proPOR_forecast_change
     ,isnull(MAX(fg16_proPOR_forecast_current),0.0) as fg16_proPOR_forecast_current,isnull(MAX(fg16_proPOR_forecast_change),0.0) as fg16_proPOR_forecast_change
     ,isnull(MAX(fg17_proPOR_forecast_current),0.0) as fg17_proPOR_forecast_current,isnull(MAX(fg17_proPOR_forecast_change),0.0) as fg17_proPOR_forecast_change
     ,isnull(MAX(fg18_proPOR_forecast_current),0.0) as fg18_proPOR_forecast_current,isnull(MAX(fg18_proPOR_forecast_change),0.0) as fg18_proPOR_forecast_change
     ,isnull(MAX(fg19_proPOR_forecast_current),0.0) as fg19_proPOR_forecast_current,isnull(MAX(fg19_proPOR_forecast_change),0.0) as fg19_proPOR_forecast_change
     ,isnull(MAX(fg20_proPOR_forecast_current),0.0) as fg20_proPOR_forecast_current,isnull(MAX(fg20_proPOR_forecast_change),0.0) as fg20_proPOR_forecast_change
     ,isnull(MAX(fg21_proPOR_forecast_current),0.0) as fg21_proPOR_forecast_current,isnull(MAX(fg21_proPOR_forecast_change),0.0) as fg21_proPOR_forecast_change
     ,isnull(MAX(fg22_proPOR_forecast_current),0.0) as fg22_proPOR_forecast_current,isnull(MAX(fg22_proPOR_forecast_change),0.0) as fg22_proPOR_forecast_change
     ,isnull(MAX(fg23_proPOR_forecast_current),0.0) as fg23_proPOR_forecast_current,isnull(MAX(fg23_proPOR_forecast_change),0.0) as fg23_proPOR_forecast_change
     ,isnull(MAX(fg24_proPOR_forecast_current),0.0) as fg24_proPOR_forecast_current,isnull(MAX(fg24_proPOR_forecast_change),0.0) as fg24_proPOR_forecast_change
     ,isnull(MAX(fg25_proPOR_forecast_current),0.0) as fg25_proPOR_forecast_current,isnull(MAX(fg25_proPOR_forecast_change),0.0) as fg25_proPOR_forecast_change
     ,isnull(MAX(fg26_proPOR_forecast_current),0.0) as fg26_proPOR_forecast_current,isnull(MAX(fg26_proPOR_forecast_change),0.0) as fg26_proPOR_forecast_change
     ,isnull(MAX(fg27_proPOR_forecast_current),0.0) as fg27_proPOR_forecast_current,isnull(MAX(fg27_proPOR_forecast_change),0.0) as fg27_proPOR_forecast_change
     ,isnull(MAX(fg28_proPOR_forecast_current),0.0) as fg28_proPOR_forecast_current,isnull(MAX(fg28_proPOR_forecast_change),0.0) as fg28_proPOR_forecast_change
     ,isnull(MAX(fg29_proPOR_forecast_current),0.0) as fg29_proPOR_forecast_current,isnull(MAX(fg29_proPOR_forecast_change),0.0) as fg29_proPOR_forecast_change
     ,isnull(MAX(fg30_proPOR_forecast_current),0.0) as fg30_proPOR_forecast_current,isnull(MAX(fg30_proPOR_forecast_change),0.0) as fg30_proPOR_forecast_change
     ,isnull(MAX(fg31_proPOR_forecast_current),0.0) as fg31_proPOR_forecast_current,isnull(MAX(fg31_proPOR_forecast_change),0.0) as fg31_proPOR_forecast_change
     ,isnull(MAX(fg32_proPOR_forecast_current),0.0) as fg32_proPOR_forecast_current,isnull(MAX(fg32_proPOR_forecast_change),0.0) as fg32_proPOR_forecast_change
     ,isnull(MAX(fg33_proPOR_forecast_current),0.0) as fg33_proPOR_forecast_current,isnull(MAX(fg33_proPOR_forecast_change),0.0) as fg33_proPOR_forecast_change
     ,isnull(MAX(fg34_proPOR_forecast_current),0.0) as fg34_proPOR_forecast_current,isnull(MAX(fg34_proPOR_forecast_change),0.0) as fg34_proPOR_forecast_change
     ,isnull(MAX(fg35_proPOR_forecast_current),0.0) as fg35_proPOR_forecast_current,isnull(MAX(fg35_proPOR_forecast_change),0.0) as fg35_proPOR_forecast_change
     ,isnull(MAX(fg36_proPOR_forecast_current),0.0) as fg36_proPOR_forecast_current,isnull(MAX(fg36_proPOR_forecast_change),0.0) as fg36_proPOR_forecast_change
     ,isnull(MAX(fg37_proPOR_forecast_current),0.0) as fg37_proPOR_forecast_current,isnull(MAX(fg37_proPOR_forecast_change),0.0) as fg37_proPOR_forecast_change
     ,isnull(MAX(fg38_proPOR_forecast_current),0.0) as fg38_proPOR_forecast_current,isnull(MAX(fg38_proPOR_forecast_change),0.0) as fg38_proPOR_forecast_change
     ,isnull(MAX(fg39_proPOR_forecast_current),0.0) as fg39_proPOR_forecast_current,isnull(MAX(fg39_proPOR_forecast_change),0.0) as fg39_proPOR_forecast_change
     ,isnull(MAX(fg40_proPOR_forecast_current),0.0) as fg40_proPOR_forecast_current,isnull(MAX(fg40_proPOR_forecast_change),0.0) as fg40_proPOR_forecast_change
     ,isnull(MAX(fg41_proPOR_forecast_current),0.0) as fg41_proPOR_forecast_current,isnull(MAX(fg41_proPOR_forecast_change),0.0) as fg41_proPOR_forecast_change
     ,isnull(MAX(fg42_proPOR_forecast_current),0.0) as fg42_proPOR_forecast_current,isnull(MAX(fg42_proPOR_forecast_change),0.0) as fg42_proPOR_forecast_change
     ,isnull(MAX(fg43_proPOR_forecast_current),0.0) as fg43_proPOR_forecast_current,isnull(MAX(fg43_proPOR_forecast_change),0.0) as fg43_proPOR_forecast_change
     ,isnull(MAX(fg44_proPOR_forecast_current),0.0) as fg44_proPOR_forecast_current,isnull(MAX(fg44_proPOR_forecast_change),0.0) as fg44_proPOR_forecast_change
     ,isnull(MAX(fg45_proPOR_forecast_current),0.0) as fg45_proPOR_forecast_current,isnull(MAX(fg45_proPOR_forecast_change),0.0) as fg45_proPOR_forecast_change
     ,isnull(MAX(fg46_proPOR_forecast_current),0.0) as fg46_proPOR_forecast_current,isnull(MAX(fg46_proPOR_forecast_change),0.0) as fg46_proPOR_forecast_change
     ,isnull(MAX(fg47_proPOR_forecast_current),0.0) as fg47_proPOR_forecast_current,isnull(MAX(fg47_proPOR_forecast_change),0.0) as fg47_proPOR_forecast_change
     ,isnull(MAX(fg48_proPOR_forecast_current),0.0) as fg48_proPOR_forecast_current,isnull(MAX(fg48_proPOR_forecast_change),0.0) as fg48_proPOR_forecast_change
     ,isnull(MAX(fg49_proPOR_forecast_current),0.0) as fg49_proPOR_forecast_current,isnull(MAX(fg49_proPOR_forecast_change),0.0) as fg49_proPOR_forecast_change
     ,isnull(MAX(fg50_proPOR_forecast_current),0.0) as fg50_proPOR_forecast_current,isnull(MAX(fg50_proPOR_forecast_change),0.0) as fg50_proPOR_forecast_change

from
    (
        select
            occupancy_dt,dow
             ,(case forecast_group_id when @fg1 then roomsoldcurrent end) as fg1_roomsoldcurrent
             ,(case forecast_group_id when @fg1 then roomssoldchange end) as fg1_roomssoldchange
             ,(case forecast_group_id when @fg2 then roomsoldcurrent end) as fg2_roomsoldcurrent
             ,(case forecast_group_id when @fg2 then roomssoldchange end) as fg2_roomssoldchange
             ,(case forecast_group_id when @fg3 then roomsoldcurrent end) as fg3_roomsoldcurrent
             ,(case forecast_group_id when @fg3 then roomssoldchange end) as fg3_roomssoldchange
             ,(case forecast_group_id when @fg4 then roomsoldcurrent end) as fg4_roomsoldcurrent
             ,(case forecast_group_id when @fg4 then roomssoldchange end) as fg4_roomssoldchange
             ,(case forecast_group_id when @fg5 then roomsoldcurrent end) as fg5_roomsoldcurrent
             ,(case forecast_group_id when @fg5 then roomssoldchange end) as fg5_roomssoldchange
             ,(case forecast_group_id when @fg6 then roomsoldcurrent end) as fg6_roomsoldcurrent
             ,(case forecast_group_id when @fg6 then roomssoldchange end) as fg6_roomssoldchange
             ,(case forecast_group_id when @fg7 then roomsoldcurrent end) as fg7_roomsoldcurrent
             ,(case forecast_group_id when @fg7 then roomssoldchange end) as fg7_roomssoldchange
             ,(case forecast_group_id when @fg8 then roomsoldcurrent end) as fg8_roomsoldcurrent
             ,(case forecast_group_id when @fg8 then roomssoldchange end) as fg8_roomssoldchange
             ,(case forecast_group_id when @fg9 then roomsoldcurrent end) as fg9_roomsoldcurrent
             ,(case forecast_group_id when @fg9 then roomssoldchange end) as fg9_roomssoldchange
             ,(case forecast_group_id when @fg10 then roomsoldcurrent end) as fg10_roomsoldcurrent
             ,(case forecast_group_id when @fg10 then roomssoldchange end) as fg10_roomssoldchange
             ,(case forecast_group_id when @fg11 then roomsoldcurrent end) as fg11_roomsoldcurrent
             ,(case forecast_group_id when @fg11 then roomssoldchange end) as fg11_roomssoldchange
             ,(case forecast_group_id when @fg12 then roomsoldcurrent end) as fg12_roomsoldcurrent
             ,(case forecast_group_id when @fg12 then roomssoldchange end) as fg12_roomssoldchange
             ,(case forecast_group_id when @fg13 then roomsoldcurrent end) as fg13_roomsoldcurrent
             ,(case forecast_group_id when @fg13 then roomssoldchange end) as fg13_roomssoldchange
             ,(case forecast_group_id when @fg14 then roomsoldcurrent end) as fg14_roomsoldcurrent
             ,(case forecast_group_id when @fg14 then roomssoldchange end) as fg14_roomssoldchange
             ,(case forecast_group_id when @fg15 then roomsoldcurrent end) as fg15_roomsoldcurrent
             ,(case forecast_group_id when @fg15 then roomssoldchange end) as fg15_roomssoldchange
             ,(case forecast_group_id when @fg16 then roomsoldcurrent end) as fg16_roomsoldcurrent
             ,(case forecast_group_id when @fg16 then roomssoldchange end) as fg16_roomssoldchange
             ,(case forecast_group_id when @fg17 then roomsoldcurrent end) as fg17_roomsoldcurrent
             ,(case forecast_group_id when @fg17 then roomssoldchange end) as fg17_roomssoldchange
             ,(case forecast_group_id when @fg18 then roomsoldcurrent end) as fg18_roomsoldcurrent
             ,(case forecast_group_id when @fg18 then roomssoldchange end) as fg18_roomssoldchange
             ,(case forecast_group_id when @fg19 then roomsoldcurrent end) as fg19_roomsoldcurrent
             ,(case forecast_group_id when @fg19 then roomssoldchange end) as fg19_roomssoldchange
             ,(case forecast_group_id when @fg20 then roomsoldcurrent end) as fg20_roomsoldcurrent
             ,(case forecast_group_id when @fg20 then roomssoldchange end) as fg20_roomssoldchange
             ,(case forecast_group_id when @fg21 then roomsoldcurrent end) as fg21_roomsoldcurrent
             ,(case forecast_group_id when @fg21 then roomssoldchange end) as fg21_roomssoldchange
             ,(case forecast_group_id when @fg22 then roomsoldcurrent end) as fg22_roomsoldcurrent
             ,(case forecast_group_id when @fg22 then roomssoldchange end) as fg22_roomssoldchange
             ,(case forecast_group_id when @fg23 then roomsoldcurrent end) as fg23_roomsoldcurrent
             ,(case forecast_group_id when @fg23 then roomssoldchange end) as fg23_roomssoldchange
             ,(case forecast_group_id when @fg24 then roomsoldcurrent end) as fg24_roomsoldcurrent
             ,(case forecast_group_id when @fg24 then roomssoldchange end) as fg24_roomssoldchange
             ,(case forecast_group_id when @fg25 then roomsoldcurrent end) as fg25_roomsoldcurrent
             ,(case forecast_group_id when @fg25 then roomssoldchange end) as fg25_roomssoldchange
             ,(case forecast_group_id when @fg26 then roomsoldcurrent end) as fg26_roomsoldcurrent
             ,(case forecast_group_id when @fg26 then roomssoldchange end) as fg26_roomssoldchange
             ,(case forecast_group_id when @fg27 then roomsoldcurrent end) as fg27_roomsoldcurrent
             ,(case forecast_group_id when @fg27 then roomssoldchange end) as fg27_roomssoldchange
             ,(case forecast_group_id when @fg28 then roomsoldcurrent end) as fg28_roomsoldcurrent
             ,(case forecast_group_id when @fg28 then roomssoldchange end) as fg28_roomssoldchange
             ,(case forecast_group_id when @fg29 then roomsoldcurrent end) as fg29_roomsoldcurrent
             ,(case forecast_group_id when @fg29 then roomssoldchange end) as fg29_roomssoldchange
             ,(case forecast_group_id when @fg30 then roomsoldcurrent end) as fg30_roomsoldcurrent
             ,(case forecast_group_id when @fg30 then roomssoldchange end) as fg30_roomssoldchange
             ,(case forecast_group_id when @fg31 then roomsoldcurrent end) as fg31_roomsoldcurrent
             ,(case forecast_group_id when @fg31 then roomssoldchange end) as fg31_roomssoldchange
             ,(case forecast_group_id when @fg32 then roomsoldcurrent end) as fg32_roomsoldcurrent
             ,(case forecast_group_id when @fg32 then roomssoldchange end) as fg32_roomssoldchange
             ,(case forecast_group_id when @fg33 then roomsoldcurrent end) as fg33_roomsoldcurrent
             ,(case forecast_group_id when @fg33 then roomssoldchange end) as fg33_roomssoldchange
             ,(case forecast_group_id when @fg34 then roomsoldcurrent end) as fg34_roomsoldcurrent
             ,(case forecast_group_id when @fg34 then roomssoldchange end) as fg34_roomssoldchange
             ,(case forecast_group_id when @fg35 then roomsoldcurrent end) as fg35_roomsoldcurrent
             ,(case forecast_group_id when @fg35 then roomssoldchange end) as fg35_roomssoldchange
             ,(case forecast_group_id when @fg36 then roomsoldcurrent end) as fg36_roomsoldcurrent
             ,(case forecast_group_id when @fg36 then roomssoldchange end) as fg36_roomssoldchange
             ,(case forecast_group_id when @fg37 then roomsoldcurrent end) as fg37_roomsoldcurrent
             ,(case forecast_group_id when @fg37 then roomssoldchange end) as fg37_roomssoldchange
             ,(case forecast_group_id when @fg38 then roomsoldcurrent end) as fg38_roomsoldcurrent
             ,(case forecast_group_id when @fg38 then roomssoldchange end) as fg38_roomssoldchange
             ,(case forecast_group_id when @fg39 then roomsoldcurrent end) as fg39_roomsoldcurrent
             ,(case forecast_group_id when @fg39 then roomssoldchange end) as fg39_roomssoldchange
             ,(case forecast_group_id when @fg40 then roomsoldcurrent end) as fg40_roomsoldcurrent
             ,(case forecast_group_id when @fg40 then roomssoldchange end) as fg40_roomssoldchange
             ,(case forecast_group_id when @fg41 then roomsoldcurrent end) as fg41_roomsoldcurrent
             ,(case forecast_group_id when @fg41 then roomssoldchange end) as fg41_roomssoldchange
             ,(case forecast_group_id when @fg42 then roomsoldcurrent end) as fg42_roomsoldcurrent
             ,(case forecast_group_id when @fg42 then roomssoldchange end) as fg42_roomssoldchange
             ,(case forecast_group_id when @fg43 then roomsoldcurrent end) as fg43_roomsoldcurrent
             ,(case forecast_group_id when @fg43 then roomssoldchange end) as fg43_roomssoldchange
             ,(case forecast_group_id when @fg44 then roomsoldcurrent end) as fg44_roomsoldcurrent
             ,(case forecast_group_id when @fg44 then roomssoldchange end) as fg44_roomssoldchange
             ,(case forecast_group_id when @fg45 then roomsoldcurrent end) as fg45_roomsoldcurrent
             ,(case forecast_group_id when @fg45 then roomssoldchange end) as fg45_roomssoldchange
             ,(case forecast_group_id when @fg46 then roomsoldcurrent end) as fg46_roomsoldcurrent
             ,(case forecast_group_id when @fg46 then roomssoldchange end) as fg46_roomssoldchange
             ,(case forecast_group_id when @fg47 then roomsoldcurrent end) as fg47_roomsoldcurrent
             ,(case forecast_group_id when @fg47 then roomssoldchange end) as fg47_roomssoldchange
             ,(case forecast_group_id when @fg48 then roomsoldcurrent end) as fg48_roomsoldcurrent
             ,(case forecast_group_id when @fg48 then roomssoldchange end) as fg48_roomssoldchange
             ,(case forecast_group_id when @fg49 then roomsoldcurrent end) as fg49_roomsoldcurrent
             ,(case forecast_group_id when @fg49 then roomssoldchange end) as fg49_roomssoldchange
             ,(case forecast_group_id when @fg50 then roomsoldcurrent end) as fg50_roomsoldcurrent
             ,(case forecast_group_id when @fg50 then roomssoldchange end) as fg50_roomssoldchange


             ,(case forecast_group_id when @fg1 then occfcstcurrent end) as fg1_occfcstcurrent
             ,(case forecast_group_id when @fg1 then occfcstchange end) as fg1_occfcstchange
             ,(case forecast_group_id when @fg2 then occfcstcurrent end) as fg2_occfcstcurrent
             ,(case forecast_group_id when @fg2 then occfcstchange end) as fg2_occfcstchange
             ,(case forecast_group_id when @fg3 then occfcstcurrent end) as fg3_occfcstcurrent
             ,(case forecast_group_id when @fg3 then occfcstchange end) as fg3_occfcstchange
             ,(case forecast_group_id when @fg4 then occfcstcurrent end) as fg4_occfcstcurrent
             ,(case forecast_group_id when @fg4 then occfcstchange end) as fg4_occfcstchange
             ,(case forecast_group_id when @fg5 then occfcstcurrent end) as fg5_occfcstcurrent
             ,(case forecast_group_id when @fg5 then occfcstchange end) as fg5_occfcstchange
             ,(case forecast_group_id when @fg6 then occfcstcurrent end) as fg6_occfcstcurrent
             ,(case forecast_group_id when @fg6 then occfcstchange end) as fg6_occfcstchange
             ,(case forecast_group_id when @fg7 then occfcstcurrent end) as fg7_occfcstcurrent
             ,(case forecast_group_id when @fg7 then occfcstchange end) as fg7_occfcstchange
             ,(case forecast_group_id when @fg8 then occfcstcurrent end) as fg8_occfcstcurrent
             ,(case forecast_group_id when @fg8 then occfcstchange end) as fg8_occfcstchange
             ,(case forecast_group_id when @fg9 then occfcstcurrent end) as fg9_occfcstcurrent
             ,(case forecast_group_id when @fg9 then occfcstchange end) as fg9_occfcstchange
             ,(case forecast_group_id when @fg10 then occfcstcurrent end) as fg10_occfcstcurrent
             ,(case forecast_group_id when @fg10 then occfcstchange end) as fg10_occfcstchange
             ,(case forecast_group_id when @fg11 then occfcstcurrent end) as fg11_occfcstcurrent
             ,(case forecast_group_id when @fg11 then occfcstchange end) as fg11_occfcstchange
             ,(case forecast_group_id when @fg12 then occfcstcurrent end) as fg12_occfcstcurrent
             ,(case forecast_group_id when @fg12 then occfcstchange end) as fg12_occfcstchange
             ,(case forecast_group_id when @fg13 then occfcstcurrent end) as fg13_occfcstcurrent
             ,(case forecast_group_id when @fg13 then occfcstchange end) as fg13_occfcstchange
             ,(case forecast_group_id when @fg14 then occfcstcurrent end) as fg14_occfcstcurrent
             ,(case forecast_group_id when @fg14 then occfcstchange end) as fg14_occfcstchange
             ,(case forecast_group_id when @fg15 then occfcstcurrent end) as fg15_occfcstcurrent
             ,(case forecast_group_id when @fg15 then occfcstchange end) as fg15_occfcstchange
             ,(case forecast_group_id when @fg16 then occfcstcurrent end) as fg16_occfcstcurrent
             ,(case forecast_group_id when @fg16 then occfcstchange end) as fg16_occfcstchange
             ,(case forecast_group_id when @fg17 then occfcstcurrent end) as fg17_occfcstcurrent
             ,(case forecast_group_id when @fg17 then occfcstchange end) as fg17_occfcstchange
             ,(case forecast_group_id when @fg18 then occfcstcurrent end) as fg18_occfcstcurrent
             ,(case forecast_group_id when @fg18 then occfcstchange end) as fg18_occfcstchange
             ,(case forecast_group_id when @fg19 then occfcstcurrent end) as fg19_occfcstcurrent
             ,(case forecast_group_id when @fg19 then occfcstchange end) as fg19_occfcstchange
             ,(case forecast_group_id when @fg20 then occfcstcurrent end) as fg20_occfcstcurrent
             ,(case forecast_group_id when @fg20 then occfcstchange end) as fg20_occfcstchange
             ,(case forecast_group_id when @fg21 then occfcstcurrent end) as fg21_occfcstcurrent
             ,(case forecast_group_id when @fg21 then occfcstchange end) as fg21_occfcstchange
             ,(case forecast_group_id when @fg22 then occfcstcurrent end) as fg22_occfcstcurrent
             ,(case forecast_group_id when @fg22 then occfcstchange end) as fg22_occfcstchange
             ,(case forecast_group_id when @fg23 then occfcstcurrent end) as fg23_occfcstcurrent
             ,(case forecast_group_id when @fg23 then occfcstchange end) as fg23_occfcstchange
             ,(case forecast_group_id when @fg24 then occfcstcurrent end) as fg24_occfcstcurrent
             ,(case forecast_group_id when @fg24 then occfcstchange end) as fg24_occfcstchange
             ,(case forecast_group_id when @fg25 then occfcstcurrent end) as fg25_occfcstcurrent
             ,(case forecast_group_id when @fg25 then occfcstchange end) as fg25_occfcstchange
             ,(case forecast_group_id when @fg26 then occfcstcurrent end) as fg26_occfcstcurrent
             ,(case forecast_group_id when @fg26 then occfcstchange end) as fg26_occfcstchange
             ,(case forecast_group_id when @fg27 then occfcstcurrent end) as fg27_occfcstcurrent
             ,(case forecast_group_id when @fg27 then occfcstchange end) as fg27_occfcstchange
             ,(case forecast_group_id when @fg28 then occfcstcurrent end) as fg28_occfcstcurrent
             ,(case forecast_group_id when @fg28 then occfcstchange end) as fg28_occfcstchange
             ,(case forecast_group_id when @fg29 then occfcstcurrent end) as fg29_occfcstcurrent
             ,(case forecast_group_id when @fg29 then occfcstchange end) as fg29_occfcstchange
             ,(case forecast_group_id when @fg30 then occfcstcurrent end) as fg30_occfcstcurrent
             ,(case forecast_group_id when @fg30 then occfcstchange end) as fg30_occfcstchange
             ,(case forecast_group_id when @fg31 then occfcstcurrent end) as fg31_occfcstcurrent
             ,(case forecast_group_id when @fg31 then occfcstchange end) as fg31_occfcstchange
             ,(case forecast_group_id when @fg32 then occfcstcurrent end) as fg32_occfcstcurrent
             ,(case forecast_group_id when @fg32 then occfcstchange end) as fg32_occfcstchange
             ,(case forecast_group_id when @fg33 then occfcstcurrent end) as fg33_occfcstcurrent
             ,(case forecast_group_id when @fg33 then occfcstchange end) as fg33_occfcstchange
             ,(case forecast_group_id when @fg34 then occfcstcurrent end) as fg34_occfcstcurrent
             ,(case forecast_group_id when @fg34 then occfcstchange end) as fg34_occfcstchange
             ,(case forecast_group_id when @fg35 then occfcstcurrent end) as fg35_occfcstcurrent
             ,(case forecast_group_id when @fg35 then occfcstchange end) as fg35_occfcstchange
             ,(case forecast_group_id when @fg36 then occfcstcurrent end) as fg36_occfcstcurrent
             ,(case forecast_group_id when @fg36 then occfcstchange end) as fg36_occfcstchange
             ,(case forecast_group_id when @fg37 then occfcstcurrent end) as fg37_occfcstcurrent
             ,(case forecast_group_id when @fg37 then occfcstchange end) as fg37_occfcstchange
             ,(case forecast_group_id when @fg38 then occfcstcurrent end) as fg38_occfcstcurrent
             ,(case forecast_group_id when @fg38 then occfcstchange end) as fg38_occfcstchange
             ,(case forecast_group_id when @fg39 then occfcstcurrent end) as fg39_occfcstcurrent
             ,(case forecast_group_id when @fg39 then occfcstchange end) as fg39_occfcstchange
             ,(case forecast_group_id when @fg40 then occfcstcurrent end) as fg40_occfcstcurrent
             ,(case forecast_group_id when @fg40 then occfcstchange end) as fg40_occfcstchange
             ,(case forecast_group_id when @fg41 then occfcstcurrent end) as fg41_occfcstcurrent
             ,(case forecast_group_id when @fg41 then occfcstchange end) as fg41_occfcstchange
             ,(case forecast_group_id when @fg42 then occfcstcurrent end) as fg42_occfcstcurrent
             ,(case forecast_group_id when @fg42 then occfcstchange end) as fg42_occfcstchange
             ,(case forecast_group_id when @fg43 then occfcstcurrent end) as fg43_occfcstcurrent
             ,(case forecast_group_id when @fg43 then occfcstchange end) as fg43_occfcstchange
             ,(case forecast_group_id when @fg44 then occfcstcurrent end) as fg44_occfcstcurrent
             ,(case forecast_group_id when @fg44 then occfcstchange end) as fg44_occfcstchange
             ,(case forecast_group_id when @fg45 then occfcstcurrent end) as fg45_occfcstcurrent
             ,(case forecast_group_id when @fg45 then occfcstchange end) as fg45_occfcstchange
             ,(case forecast_group_id when @fg46 then occfcstcurrent end) as fg46_occfcstcurrent
             ,(case forecast_group_id when @fg46 then occfcstchange end) as fg46_occfcstchange
             ,(case forecast_group_id when @fg47 then occfcstcurrent end) as fg47_occfcstcurrent
             ,(case forecast_group_id when @fg47 then occfcstchange end) as fg47_occfcstchange
             ,(case forecast_group_id when @fg48 then occfcstcurrent end) as fg48_occfcstcurrent
             ,(case forecast_group_id when @fg48 then occfcstchange end) as fg48_occfcstchange
             ,(case forecast_group_id when @fg49 then occfcstcurrent end) as fg49_occfcstcurrent
             ,(case forecast_group_id when @fg49 then occfcstchange end) as fg49_occfcstchange
             ,(case forecast_group_id when @fg50 then occfcstcurrent end) as fg50_occfcstcurrent
             ,(case forecast_group_id when @fg50 then occfcstchange end) as fg50_occfcstchange


             ,(case forecast_group_id when @fg1 then bookedroomrevenuecurrent end) as fg1_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg1 then bookedroomrevenuechange end) as fg1_bookedroomrevenuechange
             ,(case forecast_group_id when @fg2 then bookedroomrevenuecurrent end) as fg2_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg2 then bookedroomrevenuechange end) as fg2_bookedroomrevenuechange
             ,(case forecast_group_id when @fg3 then bookedroomrevenuecurrent end) as fg3_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg3 then bookedroomrevenuechange end) as fg3_bookedroomrevenuechange
             ,(case forecast_group_id when @fg4 then bookedroomrevenuecurrent end) as fg4_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg4 then bookedroomrevenuechange end) as fg4_bookedroomrevenuechange
             ,(case forecast_group_id when @fg5 then bookedroomrevenuecurrent end) as fg5_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg5 then bookedroomrevenuechange end) as fg5_bookedroomrevenuechange
             ,(case forecast_group_id when @fg6 then bookedroomrevenuechange end) as fg6_bookedroomrevenuechange
             ,(case forecast_group_id when @fg6 then bookedroomrevenuecurrent end) as fg6_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg7 then bookedroomrevenuecurrent end) as fg7_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg7 then bookedroomrevenuechange end) as fg7_bookedroomrevenuechange
             ,(case forecast_group_id when @fg8 then bookedroomrevenuecurrent end) as fg8_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg8 then bookedroomrevenuechange end) as fg8_bookedroomrevenuechange
             ,(case forecast_group_id when @fg9 then bookedroomrevenuecurrent end) as fg9_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg9 then bookedroomrevenuechange end) as fg9_bookedroomrevenuechange
             ,(case forecast_group_id when @fg10 then bookedroomrevenuecurrent end) as fg10_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg10 then bookedroomrevenuechange end) as fg10_bookedroomrevenuechange
             ,(case forecast_group_id when @fg11 then bookedroomrevenuecurrent end) as fg11_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg11 then bookedroomrevenuechange end) as fg11_bookedroomrevenuechange
             ,(case forecast_group_id when @fg12 then bookedroomrevenuecurrent end) as fg12_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg12 then bookedroomrevenuechange end) as fg12_bookedroomrevenuechange
             ,(case forecast_group_id when @fg13 then bookedroomrevenuecurrent end) as fg13_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg13 then bookedroomrevenuechange end) as fg13_bookedroomrevenuechange
             ,(case forecast_group_id when @fg14 then bookedroomrevenuecurrent end) as fg14_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg14 then bookedroomrevenuechange end) as fg14_bookedroomrevenuechange
             ,(case forecast_group_id when @fg15 then bookedroomrevenuecurrent end) as fg15_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg15 then bookedroomrevenuechange end) as fg15_bookedroomrevenuechange
             ,(case forecast_group_id when @fg16 then bookedroomrevenuechange end) as fg16_bookedroomrevenuechange
             ,(case forecast_group_id when @fg16 then bookedroomrevenuecurrent end) as fg16_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg17 then bookedroomrevenuecurrent end) as fg17_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg17 then bookedroomrevenuechange end) as fg17_bookedroomrevenuechange
             ,(case forecast_group_id when @fg18 then bookedroomrevenuecurrent end) as fg18_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg18 then bookedroomrevenuechange end) as fg18_bookedroomrevenuechange
             ,(case forecast_group_id when @fg19 then bookedroomrevenuecurrent end) as fg19_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg19 then bookedroomrevenuechange end) as fg19_bookedroomrevenuechange
             ,(case forecast_group_id when @fg20 then bookedroomrevenuecurrent end) as fg20_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg20 then bookedroomrevenuechange end) as fg20_bookedroomrevenuechange
             ,(case forecast_group_id when @fg21 then bookedroomrevenuecurrent end) as fg21_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg21 then bookedroomrevenuechange end) as fg21_bookedroomrevenuechange
             ,(case forecast_group_id when @fg22 then bookedroomrevenuecurrent end) as fg22_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg22 then bookedroomrevenuechange end) as fg22_bookedroomrevenuechange
             ,(case forecast_group_id when @fg23 then bookedroomrevenuecurrent end) as fg23_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg23 then bookedroomrevenuechange end) as fg23_bookedroomrevenuechange
             ,(case forecast_group_id when @fg24 then bookedroomrevenuecurrent end) as fg24_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg24 then bookedroomrevenuechange end) as fg24_bookedroomrevenuechange
             ,(case forecast_group_id when @fg25 then bookedroomrevenuecurrent end) as fg25_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg25 then bookedroomrevenuechange end) as fg25_bookedroomrevenuechange
             ,(case forecast_group_id when @fg26 then bookedroomrevenuecurrent end) as fg26_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg26 then bookedroomrevenuechange end) as fg26_bookedroomrevenuechange
             ,(case forecast_group_id when @fg27 then bookedroomrevenuecurrent end) as fg27_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg27 then bookedroomrevenuechange end) as fg27_bookedroomrevenuechange
             ,(case forecast_group_id when @fg28 then bookedroomrevenuecurrent end) as fg28_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg28 then bookedroomrevenuechange end) as fg28_bookedroomrevenuechange
             ,(case forecast_group_id when @fg29 then bookedroomrevenuecurrent end) as fg29_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg29 then bookedroomrevenuechange end) as fg29_bookedroomrevenuechange
             ,(case forecast_group_id when @fg30 then bookedroomrevenuecurrent end) as fg30_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg30 then bookedroomrevenuechange end) as fg30_bookedroomrevenuechange
             ,(case forecast_group_id when @fg31 then bookedroomrevenuecurrent end) as fg31_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg31 then bookedroomrevenuechange end) as fg31_bookedroomrevenuechange
             ,(case forecast_group_id when @fg32 then bookedroomrevenuecurrent end) as fg32_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg32 then bookedroomrevenuechange end) as fg32_bookedroomrevenuechange
             ,(case forecast_group_id when @fg33 then bookedroomrevenuecurrent end) as fg33_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg33 then bookedroomrevenuechange end) as fg33_bookedroomrevenuechange
             ,(case forecast_group_id when @fg34 then bookedroomrevenuecurrent end) as fg34_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg34 then bookedroomrevenuechange end) as fg34_bookedroomrevenuechange
             ,(case forecast_group_id when @fg35 then bookedroomrevenuecurrent end) as fg35_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg35 then bookedroomrevenuechange end) as fg35_bookedroomrevenuechange
             ,(case forecast_group_id when @fg36 then bookedroomrevenuecurrent end) as fg36_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg36 then bookedroomrevenuechange end) as fg36_bookedroomrevenuechange
             ,(case forecast_group_id when @fg37 then bookedroomrevenuecurrent end) as fg37_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg37 then bookedroomrevenuechange end) as fg37_bookedroomrevenuechange
             ,(case forecast_group_id when @fg38 then bookedroomrevenuecurrent end) as fg38_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg38 then bookedroomrevenuechange end) as fg38_bookedroomrevenuechange
             ,(case forecast_group_id when @fg39 then bookedroomrevenuecurrent end) as fg39_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg39 then bookedroomrevenuechange end) as fg39_bookedroomrevenuechange
             ,(case forecast_group_id when @fg40 then bookedroomrevenuecurrent end) as fg40_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg40 then bookedroomrevenuechange end) as fg40_bookedroomrevenuechange
             ,(case forecast_group_id when @fg41 then bookedroomrevenuecurrent end) as fg41_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg41 then bookedroomrevenuechange end) as fg41_bookedroomrevenuechange
             ,(case forecast_group_id when @fg42 then bookedroomrevenuecurrent end) as fg42_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg42 then bookedroomrevenuechange end) as fg42_bookedroomrevenuechange
             ,(case forecast_group_id when @fg43 then bookedroomrevenuecurrent end) as fg43_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg43 then bookedroomrevenuechange end) as fg43_bookedroomrevenuechange
             ,(case forecast_group_id when @fg44 then bookedroomrevenuecurrent end) as fg44_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg44 then bookedroomrevenuechange end) as fg44_bookedroomrevenuechange
             ,(case forecast_group_id when @fg45 then bookedroomrevenuecurrent end) as fg45_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg45 then bookedroomrevenuechange end) as fg45_bookedroomrevenuechange
             ,(case forecast_group_id when @fg46 then bookedroomrevenuecurrent end) as fg46_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg46 then bookedroomrevenuechange end) as fg46_bookedroomrevenuechange
             ,(case forecast_group_id when @fg47 then bookedroomrevenuecurrent end) as fg47_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg47 then bookedroomrevenuechange end) as fg47_bookedroomrevenuechange
             ,(case forecast_group_id when @fg48 then bookedroomrevenuecurrent end) as fg48_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg48 then bookedroomrevenuechange end) as fg48_bookedroomrevenuechange
             ,(case forecast_group_id when @fg49 then bookedroomrevenuecurrent end) as fg49_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg49 then bookedroomrevenuechange end) as fg49_bookedroomrevenuechange
             ,(case forecast_group_id when @fg50 then bookedroomrevenuecurrent end) as fg50_bookedroomrevenuecurrent
             ,(case forecast_group_id when @fg50 then bookedroomrevenuechange end) as fg50_bookedroomrevenuechange


             ,(case forecast_group_id when @fg1 then fcstedroomrevenuecurrent end) as fg1_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg1 then fcstedroomrevenuechange end) as fg1_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg2 then fcstedroomrevenuecurrent end) as fg2_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg2 then fcstedroomrevenuechange end) as fg2_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg3 then fcstedroomrevenuecurrent end) as fg3_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg3 then fcstedroomrevenuechange end) as fg3_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg4 then fcstedroomrevenuecurrent end) as fg4_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg4 then fcstedroomrevenuechange end) as fg4_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg5 then fcstedroomrevenuecurrent end) as fg5_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg5 then fcstedroomrevenuechange end) as fg5_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg6 then fcstedroomrevenuecurrent end) as fg6_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg6 then fcstedroomrevenuechange end) as fg6_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg7 then fcstedroomrevenuecurrent end) as fg7_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg7 then fcstedroomrevenuechange end) as fg7_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg8 then fcstedroomrevenuecurrent end) as fg8_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg8 then fcstedroomrevenuechange end) as fg8_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg9 then fcstedroomrevenuecurrent end) as fg9_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg9 then fcstedroomrevenuechange end) as fg9_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg10 then fcstedroomrevenuecurrent end) as fg10_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg10 then fcstedroomrevenuechange end) as fg10_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg11 then fcstedroomrevenuecurrent end) as fg11_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg11 then fcstedroomrevenuechange end) as fg11_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg12 then fcstedroomrevenuecurrent end) as fg12_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg12 then fcstedroomrevenuechange end) as fg12_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg13 then fcstedroomrevenuecurrent end) as fg13_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg13 then fcstedroomrevenuechange end) as fg13_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg14 then fcstedroomrevenuecurrent end) as fg14_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg14 then fcstedroomrevenuechange end) as fg14_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg15 then fcstedroomrevenuecurrent end) as fg15_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg15 then fcstedroomrevenuechange end) as fg15_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg16 then fcstedroomrevenuecurrent end) as fg16_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg16 then fcstedroomrevenuechange end) as fg16_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg17 then fcstedroomrevenuecurrent end) as fg17_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg17 then fcstedroomrevenuechange end) as fg17_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg18 then fcstedroomrevenuecurrent end) as fg18_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg18 then fcstedroomrevenuechange end) as fg18_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg19 then fcstedroomrevenuecurrent end) as fg19_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg19 then fcstedroomrevenuechange end) as fg19_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg20 then fcstedroomrevenuecurrent end) as fg20_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg20 then fcstedroomrevenuechange end) as fg20_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg21 then fcstedroomrevenuecurrent end) as fg21_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg21 then fcstedroomrevenuechange end) as fg21_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg22 then fcstedroomrevenuecurrent end) as fg22_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg22 then fcstedroomrevenuechange end) as fg22_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg23 then fcstedroomrevenuecurrent end) as fg23_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg23 then fcstedroomrevenuechange end) as fg23_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg24 then fcstedroomrevenuecurrent end) as fg24_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg24 then fcstedroomrevenuechange end) as fg24_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg25 then fcstedroomrevenuecurrent end) as fg25_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg25 then fcstedroomrevenuechange end) as fg25_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg26 then fcstedroomrevenuecurrent end) as fg26_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg26 then fcstedroomrevenuechange end) as fg26_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg27 then fcstedroomrevenuecurrent end) as fg27_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg27 then fcstedroomrevenuechange end) as fg27_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg28 then fcstedroomrevenuecurrent end) as fg28_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg28 then fcstedroomrevenuechange end) as fg28_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg29 then fcstedroomrevenuecurrent end) as fg29_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg29 then fcstedroomrevenuechange end) as fg29_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg30 then fcstedroomrevenuecurrent end) as fg30_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg30 then fcstedroomrevenuechange end) as fg30_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg31 then fcstedroomrevenuecurrent end) as fg31_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg31 then fcstedroomrevenuechange end) as fg31_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg32 then fcstedroomrevenuecurrent end) as fg32_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg32 then fcstedroomrevenuechange end) as fg32_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg33 then fcstedroomrevenuecurrent end) as fg33_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg33 then fcstedroomrevenuechange end) as fg33_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg34 then fcstedroomrevenuecurrent end) as fg34_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg34 then fcstedroomrevenuechange end) as fg34_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg35 then fcstedroomrevenuecurrent end) as fg35_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg35 then fcstedroomrevenuechange end) as fg35_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg36 then fcstedroomrevenuecurrent end) as fg36_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg36 then fcstedroomrevenuechange end) as fg36_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg37 then fcstedroomrevenuecurrent end) as fg37_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg37 then fcstedroomrevenuechange end) as fg37_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg38 then fcstedroomrevenuecurrent end) as fg38_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg38 then fcstedroomrevenuechange end) as fg38_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg39 then fcstedroomrevenuecurrent end) as fg39_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg39 then fcstedroomrevenuechange end) as fg39_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg40 then fcstedroomrevenuecurrent end) as fg40_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg40 then fcstedroomrevenuechange end) as fg40_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg41 then fcstedroomrevenuecurrent end) as fg41_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg41 then fcstedroomrevenuechange end) as fg41_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg42 then fcstedroomrevenuecurrent end) as fg42_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg42 then fcstedroomrevenuechange end) as fg42_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg43 then fcstedroomrevenuecurrent end) as fg43_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg43 then fcstedroomrevenuechange end) as fg43_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg44 then fcstedroomrevenuecurrent end) as fg44_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg44 then fcstedroomrevenuechange end) as fg44_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg45 then fcstedroomrevenuecurrent end) as fg45_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg45 then fcstedroomrevenuechange end) as fg45_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg46 then fcstedroomrevenuecurrent end) as fg46_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg46 then fcstedroomrevenuechange end) as fg46_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg47 then fcstedroomrevenuecurrent end) as fg47_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg47 then fcstedroomrevenuechange end) as fg47_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg48 then fcstedroomrevenuecurrent end) as fg48_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg48 then fcstedroomrevenuechange end) as fg48_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg49 then fcstedroomrevenuecurrent end) as fg49_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg49 then fcstedroomrevenuechange end) as fg49_fcstedroomrevenuechange
             ,(case forecast_group_id when @fg50 then fcstedroomrevenuecurrent end) as fg50_fcstedroomrevenuecurrent
             ,(case forecast_group_id when @fg50 then fcstedroomrevenuechange end) as fg50_fcstedroomrevenuechange


             ,(case forecast_group_id when @fg1 then bookedadrcurrent end) as fg1_bookedadrcurrent
             ,(case forecast_group_id when @fg1 then bookedadrchange end) as fg1_bookedadrchange
             ,(case forecast_group_id when @fg2 then bookedadrcurrent end) as fg2_bookedadrcurrent
             ,(case forecast_group_id when @fg2 then bookedadrchange end) as fg2_bookedadrchange
             ,(case forecast_group_id when @fg3 then bookedadrcurrent end) as fg3_bookedadrcurrent
             ,(case forecast_group_id when @fg3 then bookedadrchange end) as fg3_bookedadrchange
             ,(case forecast_group_id when @fg4 then bookedadrcurrent end) as fg4_bookedadrcurrent
             ,(case forecast_group_id when @fg4 then bookedadrchange end) as fg4_bookedadrchange
             ,(case forecast_group_id when @fg5 then bookedadrcurrent end) as fg5_bookedadrcurrent
             ,(case forecast_group_id when @fg5 then bookedadrchange end) as fg5_bookedadrchange
             ,(case forecast_group_id when @fg6 then bookedadrcurrent end) as fg6_bookedadrcurrent
             ,(case forecast_group_id when @fg6 then bookedadrchange end) as fg6_bookedadrchange
             ,(case forecast_group_id when @fg7 then bookedadrcurrent end) as fg7_bookedadrcurrent
             ,(case forecast_group_id when @fg7 then bookedadrchange end) as fg7_bookedadrchange
             ,(case forecast_group_id when @fg8 then bookedadrcurrent end) as fg8_bookedadrcurrent
             ,(case forecast_group_id when @fg8 then bookedadrchange end) as fg8_bookedadrchange
             ,(case forecast_group_id when @fg9 then bookedadrcurrent end) as fg9_bookedadrcurrent
             ,(case forecast_group_id when @fg9 then bookedadrchange end) as fg9_bookedadrchange
             ,(case forecast_group_id when @fg10 then bookedadrcurrent end) as fg10_bookedadrcurrent
             ,(case forecast_group_id when @fg10 then bookedadrchange end) as fg10_bookedadrchange
             ,(case forecast_group_id when @fg11 then bookedadrcurrent end) as fg11_bookedadrcurrent
             ,(case forecast_group_id when @fg11 then bookedadrchange end) as fg11_bookedadrchange
             ,(case forecast_group_id when @fg12 then bookedadrcurrent end) as fg12_bookedadrcurrent
             ,(case forecast_group_id when @fg12 then bookedadrchange end) as fg12_bookedadrchange
             ,(case forecast_group_id when @fg13 then bookedadrcurrent end) as fg13_bookedadrcurrent
             ,(case forecast_group_id when @fg13 then bookedadrchange end) as fg13_bookedadrchange
             ,(case forecast_group_id when @fg14 then bookedadrcurrent end) as fg14_bookedadrcurrent
             ,(case forecast_group_id when @fg14 then bookedadrchange end) as fg14_bookedadrchange
             ,(case forecast_group_id when @fg15 then bookedadrcurrent end) as fg15_bookedadrcurrent
             ,(case forecast_group_id when @fg15 then bookedadrchange end) as fg15_bookedadrchange
             ,(case forecast_group_id when @fg16 then bookedadrcurrent end) as fg16_bookedadrcurrent
             ,(case forecast_group_id when @fg16 then bookedadrchange end) as fg16_bookedadrchange
             ,(case forecast_group_id when @fg17 then bookedadrcurrent end) as fg17_bookedadrcurrent
             ,(case forecast_group_id when @fg17 then bookedadrchange end) as fg17_bookedadrchange
             ,(case forecast_group_id when @fg18 then bookedadrcurrent end) as fg18_bookedadrcurrent
             ,(case forecast_group_id when @fg18 then bookedadrchange end) as fg18_bookedadrchange
             ,(case forecast_group_id when @fg19 then bookedadrcurrent end) as fg19_bookedadrcurrent
             ,(case forecast_group_id when @fg19 then bookedadrchange end) as fg19_bookedadrchange
             ,(case forecast_group_id when @fg20 then bookedadrcurrent end) as fg20_bookedadrcurrent
             ,(case forecast_group_id when @fg20 then bookedadrchange end) as fg20_bookedadrchange
             ,(case forecast_group_id when @fg21 then bookedadrcurrent end) as fg21_bookedadrcurrent
             ,(case forecast_group_id when @fg21 then bookedadrchange end) as fg21_bookedadrchange
             ,(case forecast_group_id when @fg22 then bookedadrcurrent end) as fg22_bookedadrcurrent
             ,(case forecast_group_id when @fg22 then bookedadrchange end) as fg22_bookedadrchange
             ,(case forecast_group_id when @fg23 then bookedadrcurrent end) as fg23_bookedadrcurrent
             ,(case forecast_group_id when @fg23 then bookedadrchange end) as fg23_bookedadrchange
             ,(case forecast_group_id when @fg24 then bookedadrcurrent end) as fg24_bookedadrcurrent
             ,(case forecast_group_id when @fg24 then bookedadrchange end) as fg24_bookedadrchange
             ,(case forecast_group_id when @fg25 then bookedadrcurrent end) as fg25_bookedadrcurrent
             ,(case forecast_group_id when @fg25 then bookedadrchange end) as fg25_bookedadrchange
             ,(case forecast_group_id when @fg26 then bookedadrcurrent end) as fg26_bookedadrcurrent
             ,(case forecast_group_id when @fg26 then bookedadrchange end) as fg26_bookedadrchange
             ,(case forecast_group_id when @fg27 then bookedadrcurrent end) as fg27_bookedadrcurrent
             ,(case forecast_group_id when @fg27 then bookedadrchange end) as fg27_bookedadrchange
             ,(case forecast_group_id when @fg28 then bookedadrcurrent end) as fg28_bookedadrcurrent
             ,(case forecast_group_id when @fg28 then bookedadrchange end) as fg28_bookedadrchange
             ,(case forecast_group_id when @fg29 then bookedadrcurrent end) as fg29_bookedadrcurrent
             ,(case forecast_group_id when @fg29 then bookedadrchange end) as fg29_bookedadrchange
             ,(case forecast_group_id when @fg30 then bookedadrcurrent end) as fg30_bookedadrcurrent
             ,(case forecast_group_id when @fg30 then bookedadrchange end) as fg30_bookedadrchange
             ,(case forecast_group_id when @fg31 then bookedadrcurrent end) as fg31_bookedadrcurrent
             ,(case forecast_group_id when @fg31 then bookedadrchange end) as fg31_bookedadrchange
             ,(case forecast_group_id when @fg32 then bookedadrcurrent end) as fg32_bookedadrcurrent
             ,(case forecast_group_id when @fg32 then bookedadrchange end) as fg32_bookedadrchange
             ,(case forecast_group_id when @fg33 then bookedadrcurrent end) as fg33_bookedadrcurrent
             ,(case forecast_group_id when @fg33 then bookedadrchange end) as fg33_bookedadrchange
             ,(case forecast_group_id when @fg34 then bookedadrcurrent end) as fg34_bookedadrcurrent
             ,(case forecast_group_id when @fg34 then bookedadrchange end) as fg34_bookedadrchange
             ,(case forecast_group_id when @fg35 then bookedadrcurrent end) as fg35_bookedadrcurrent
             ,(case forecast_group_id when @fg35 then bookedadrchange end) as fg35_bookedadrchange
             ,(case forecast_group_id when @fg36 then bookedadrcurrent end) as fg36_bookedadrcurrent
             ,(case forecast_group_id when @fg36 then bookedadrchange end) as fg36_bookedadrchange
             ,(case forecast_group_id when @fg37 then bookedadrcurrent end) as fg37_bookedadrcurrent
             ,(case forecast_group_id when @fg37 then bookedadrchange end) as fg37_bookedadrchange
             ,(case forecast_group_id when @fg38 then bookedadrcurrent end) as fg38_bookedadrcurrent
             ,(case forecast_group_id when @fg38 then bookedadrchange end) as fg38_bookedadrchange
             ,(case forecast_group_id when @fg39 then bookedadrcurrent end) as fg39_bookedadrcurrent
             ,(case forecast_group_id when @fg39 then bookedadrchange end) as fg39_bookedadrchange
             ,(case forecast_group_id when @fg40 then bookedadrcurrent end) as fg40_bookedadrcurrent
             ,(case forecast_group_id when @fg40 then bookedadrchange end) as fg40_bookedadrchange
             ,(case forecast_group_id when @fg41 then bookedadrcurrent end) as fg41_bookedadrcurrent
             ,(case forecast_group_id when @fg41 then bookedadrchange end) as fg41_bookedadrchange
             ,(case forecast_group_id when @fg42 then bookedadrcurrent end) as fg42_bookedadrcurrent
             ,(case forecast_group_id when @fg42 then bookedadrchange end) as fg42_bookedadrchange
             ,(case forecast_group_id when @fg43 then bookedadrcurrent end) as fg43_bookedadrcurrent
             ,(case forecast_group_id when @fg43 then bookedadrchange end) as fg43_bookedadrchange
             ,(case forecast_group_id when @fg44 then bookedadrcurrent end) as fg44_bookedadrcurrent
             ,(case forecast_group_id when @fg44 then bookedadrchange end) as fg44_bookedadrchange
             ,(case forecast_group_id when @fg45 then bookedadrcurrent end) as fg45_bookedadrcurrent
             ,(case forecast_group_id when @fg45 then bookedadrchange end) as fg45_bookedadrchange
             ,(case forecast_group_id when @fg46 then bookedadrcurrent end) as fg46_bookedadrcurrent
             ,(case forecast_group_id when @fg46 then bookedadrchange end) as fg46_bookedadrchange
             ,(case forecast_group_id when @fg47 then bookedadrcurrent end) as fg47_bookedadrcurrent
             ,(case forecast_group_id when @fg47 then bookedadrchange end) as fg47_bookedadrchange
             ,(case forecast_group_id when @fg48 then bookedadrcurrent end) as fg48_bookedadrcurrent
             ,(case forecast_group_id when @fg48 then bookedadrchange end) as fg48_bookedadrchange
             ,(case forecast_group_id when @fg49 then bookedadrcurrent end) as fg49_bookedadrcurrent
             ,(case forecast_group_id when @fg49 then bookedadrchange end) as fg49_bookedadrchange
             ,(case forecast_group_id when @fg50 then bookedadrcurrent end) as fg50_bookedadrcurrent
             ,(case forecast_group_id when @fg50 then bookedadrchange end) as fg50_bookedadrchange


             ,(case forecast_group_id when @fg1 then fcstedadrcurrent end) as fg1_fcstedadrcurrent
             ,(case forecast_group_id when @fg1 then fcstedadrchange end) as fg1_fcstedadrchange
             ,(case forecast_group_id when @fg2 then fcstedadrcurrent end) as fg2_fcstedadrcurrent
             ,(case forecast_group_id when @fg2 then fcstedadrchange end) as fg2_fcstedadrchange
             ,(case forecast_group_id when @fg3 then fcstedadrcurrent end) as fg3_fcstedadrcurrent
             ,(case forecast_group_id when @fg3 then fcstedadrchange end) as fg3_fcstedadrchange
             ,(case forecast_group_id when @fg4 then fcstedadrcurrent end) as fg4_fcstedadrcurrent
             ,(case forecast_group_id when @fg4 then fcstedadrchange end) as fg4_fcstedadrchange
             ,(case forecast_group_id when @fg5 then fcstedadrcurrent end) as fg5_fcstedadrcurrent
             ,(case forecast_group_id when @fg5 then fcstedadrchange end) as fg5_fcstedadrchange
             ,(case forecast_group_id when @fg6 then fcstedadrcurrent end) as fg6_fcstedadrcurrent
             ,(case forecast_group_id when @fg6 then fcstedadrchange end) as fg6_fcstedadrchange
             ,(case forecast_group_id when @fg7 then fcstedadrcurrent end) as fg7_fcstedadrcurrent
             ,(case forecast_group_id when @fg7 then fcstedadrchange end) as fg7_fcstedadrchange
             ,(case forecast_group_id when @fg8 then fcstedadrcurrent end) as fg8_fcstedadrcurrent
             ,(case forecast_group_id when @fg8 then fcstedadrchange end) as fg8_fcstedadrchange
             ,(case forecast_group_id when @fg9 then fcstedadrcurrent end) as fg9_fcstedadrcurrent
             ,(case forecast_group_id when @fg9 then fcstedadrchange end) as fg9_fcstedadrchange
             ,(case forecast_group_id when @fg10 then fcstedadrcurrent end) as fg10_fcstedadrcurrent
             ,(case forecast_group_id when @fg10 then fcstedadrchange end) as fg10_fcstedadrchange
             ,(case forecast_group_id when @fg11 then fcstedadrcurrent end) as fg11_fcstedadrcurrent
             ,(case forecast_group_id when @fg11 then fcstedadrchange end) as fg11_fcstedadrchange
             ,(case forecast_group_id when @fg12 then fcstedadrcurrent end) as fg12_fcstedadrcurrent
             ,(case forecast_group_id when @fg12 then fcstedadrchange end) as fg12_fcstedadrchange
             ,(case forecast_group_id when @fg13 then fcstedadrcurrent end) as fg13_fcstedadrcurrent
             ,(case forecast_group_id when @fg13 then fcstedadrchange end) as fg13_fcstedadrchange
             ,(case forecast_group_id when @fg14 then fcstedadrcurrent end) as fg14_fcstedadrcurrent
             ,(case forecast_group_id when @fg14 then fcstedadrchange end) as fg14_fcstedadrchange
             ,(case forecast_group_id when @fg15 then fcstedadrcurrent end) as fg15_fcstedadrcurrent
             ,(case forecast_group_id when @fg15 then fcstedadrchange end) as fg15_fcstedadrchange
             ,(case forecast_group_id when @fg16 then fcstedadrcurrent end) as fg16_fcstedadrcurrent
             ,(case forecast_group_id when @fg16 then fcstedadrchange end) as fg16_fcstedadrchange
             ,(case forecast_group_id when @fg17 then fcstedadrcurrent end) as fg17_fcstedadrcurrent
             ,(case forecast_group_id when @fg17 then fcstedadrchange end) as fg17_fcstedadrchange
             ,(case forecast_group_id when @fg18 then fcstedadrcurrent end) as fg18_fcstedadrcurrent
             ,(case forecast_group_id when @fg18 then fcstedadrchange end) as fg18_fcstedadrchange
             ,(case forecast_group_id when @fg19 then fcstedadrcurrent end) as fg19_fcstedadrcurrent
             ,(case forecast_group_id when @fg19 then fcstedadrchange end) as fg19_fcstedadrchange
             ,(case forecast_group_id when @fg20 then fcstedadrcurrent end) as fg20_fcstedadrcurrent
             ,(case forecast_group_id when @fg20 then fcstedadrchange end) as fg20_fcstedadrchange
             ,(case forecast_group_id when @fg21 then fcstedadrcurrent end) as fg21_fcstedadrcurrent
             ,(case forecast_group_id when @fg21 then fcstedadrchange end) as fg21_fcstedadrchange
             ,(case forecast_group_id when @fg22 then fcstedadrcurrent end) as fg22_fcstedadrcurrent
             ,(case forecast_group_id when @fg22 then fcstedadrchange end) as fg22_fcstedadrchange
             ,(case forecast_group_id when @fg23 then fcstedadrcurrent end) as fg23_fcstedadrcurrent
             ,(case forecast_group_id when @fg23 then fcstedadrchange end) as fg23_fcstedadrchange
             ,(case forecast_group_id when @fg24 then fcstedadrcurrent end) as fg24_fcstedadrcurrent
             ,(case forecast_group_id when @fg24 then fcstedadrchange end) as fg24_fcstedadrchange
             ,(case forecast_group_id when @fg25 then fcstedadrcurrent end) as fg25_fcstedadrcurrent
             ,(case forecast_group_id when @fg25 then fcstedadrchange end) as fg25_fcstedadrchange
             ,(case forecast_group_id when @fg26 then fcstedadrcurrent end) as fg26_fcstedadrcurrent
             ,(case forecast_group_id when @fg26 then fcstedadrchange end) as fg26_fcstedadrchange
             ,(case forecast_group_id when @fg27 then fcstedadrcurrent end) as fg27_fcstedadrcurrent
             ,(case forecast_group_id when @fg27 then fcstedadrchange end) as fg27_fcstedadrchange
             ,(case forecast_group_id when @fg28 then fcstedadrcurrent end) as fg28_fcstedadrcurrent
             ,(case forecast_group_id when @fg28 then fcstedadrchange end) as fg28_fcstedadrchange
             ,(case forecast_group_id when @fg29 then fcstedadrcurrent end) as fg29_fcstedadrcurrent
             ,(case forecast_group_id when @fg29 then fcstedadrchange end) as fg29_fcstedadrchange
             ,(case forecast_group_id when @fg30 then fcstedadrcurrent end) as fg30_fcstedadrcurrent
             ,(case forecast_group_id when @fg30 then fcstedadrchange end) as fg30_fcstedadrchange
             ,(case forecast_group_id when @fg31 then fcstedadrcurrent end) as fg31_fcstedadrcurrent
             ,(case forecast_group_id when @fg31 then fcstedadrchange end) as fg31_fcstedadrchange
             ,(case forecast_group_id when @fg32 then fcstedadrcurrent end) as fg32_fcstedadrcurrent
             ,(case forecast_group_id when @fg32 then fcstedadrchange end) as fg32_fcstedadrchange
             ,(case forecast_group_id when @fg33 then fcstedadrcurrent end) as fg33_fcstedadrcurrent
             ,(case forecast_group_id when @fg33 then fcstedadrchange end) as fg33_fcstedadrchange
             ,(case forecast_group_id when @fg34 then fcstedadrcurrent end) as fg34_fcstedadrcurrent
             ,(case forecast_group_id when @fg34 then fcstedadrchange end) as fg34_fcstedadrchange
             ,(case forecast_group_id when @fg35 then fcstedadrcurrent end) as fg35_fcstedadrcurrent
             ,(case forecast_group_id when @fg35 then fcstedadrchange end) as fg35_fcstedadrchange
             ,(case forecast_group_id when @fg36 then fcstedadrcurrent end) as fg36_fcstedadrcurrent
             ,(case forecast_group_id when @fg36 then fcstedadrchange end) as fg36_fcstedadrchange
             ,(case forecast_group_id when @fg37 then fcstedadrcurrent end) as fg37_fcstedadrcurrent
             ,(case forecast_group_id when @fg37 then fcstedadrchange end) as fg37_fcstedadrchange
             ,(case forecast_group_id when @fg38 then fcstedadrcurrent end) as fg38_fcstedadrcurrent
             ,(case forecast_group_id when @fg38 then fcstedadrchange end) as fg38_fcstedadrchange
             ,(case forecast_group_id when @fg39 then fcstedadrcurrent end) as fg39_fcstedadrcurrent
             ,(case forecast_group_id when @fg39 then fcstedadrchange end) as fg39_fcstedadrchange
             ,(case forecast_group_id when @fg40 then fcstedadrcurrent end) as fg40_fcstedadrcurrent
             ,(case forecast_group_id when @fg40 then fcstedadrchange end) as fg40_fcstedadrchange
             ,(case forecast_group_id when @fg41 then fcstedadrcurrent end) as fg41_fcstedadrcurrent
             ,(case forecast_group_id when @fg41 then fcstedadrchange end) as fg41_fcstedadrchange
             ,(case forecast_group_id when @fg42 then fcstedadrcurrent end) as fg42_fcstedadrcurrent
             ,(case forecast_group_id when @fg42 then fcstedadrchange end) as fg42_fcstedadrchange
             ,(case forecast_group_id when @fg43 then fcstedadrcurrent end) as fg43_fcstedadrcurrent
             ,(case forecast_group_id when @fg43 then fcstedadrchange end) as fg43_fcstedadrchange
             ,(case forecast_group_id when @fg44 then fcstedadrcurrent end) as fg44_fcstedadrcurrent
             ,(case forecast_group_id when @fg44 then fcstedadrchange end) as fg44_fcstedadrchange
             ,(case forecast_group_id when @fg45 then fcstedadrcurrent end) as fg45_fcstedadrcurrent
             ,(case forecast_group_id when @fg45 then fcstedadrchange end) as fg45_fcstedadrchange
             ,(case forecast_group_id when @fg46 then fcstedadrcurrent end) as fg46_fcstedadrcurrent
             ,(case forecast_group_id when @fg46 then fcstedadrchange end) as fg46_fcstedadrchange
             ,(case forecast_group_id when @fg47 then fcstedadrcurrent end) as fg47_fcstedadrcurrent
             ,(case forecast_group_id when @fg47 then fcstedadrchange end) as fg47_fcstedadrchange
             ,(case forecast_group_id when @fg48 then fcstedadrcurrent end) as fg48_fcstedadrcurrent
             ,(case forecast_group_id when @fg48 then fcstedadrchange end) as fg48_fcstedadrchange
             ,(case forecast_group_id when @fg49 then fcstedadrcurrent end) as fg49_fcstedadrcurrent
             ,(case forecast_group_id when @fg49 then fcstedadrchange end) as fg49_fcstedadrchange
             ,(case forecast_group_id when @fg50 then fcstedadrcurrent end) as fg50_fcstedadrcurrent
             ,(case forecast_group_id when @fg50 then fcstedadrchange end) as fg50_fcstedadrchange


             --Archana-Added group block,group pick up-Start
             ,(case forecast_group_id when @fg1 then block end) as fg1_block
             ,(case forecast_group_id when @fg1 then block_pickup end) as fg1_block_pickup
             ,(case forecast_group_id when @fg1 then block_available end) as fg1_block_available

             ,(case forecast_group_id when @fg2 then block end) as fg2_block
             ,(case forecast_group_id when @fg2 then block_pickup end) as fg2_block_pickup
             ,(case forecast_group_id when @fg2 then block_available end) as fg2_block_available

             ,(case forecast_group_id when @fg3 then block end) as fg3_block
             ,(case forecast_group_id when @fg3 then block_pickup end) as fg3_block_pickup
             ,(case forecast_group_id when @fg3 then block_available end) as fg3_block_available

             ,(case forecast_group_id when @fg4 then block end) as fg4_block
             ,(case forecast_group_id when @fg4 then block_pickup end) as fg4_block_pickup
             ,(case forecast_group_id when @fg4 then block_available end) as fg4_block_available

             ,(case forecast_group_id when @fg5 then block end) as fg5_block
             ,(case forecast_group_id when @fg5 then block_pickup end) as fg5_block_pickup
             ,(case forecast_group_id when @fg5 then block_available end) as fg5_block_available

             ,(case forecast_group_id when @fg6 then block end) as fg6_block
             ,(case forecast_group_id when @fg6 then block_pickup end) as fg6_block_pickup
             ,(case forecast_group_id when @fg6 then block_available end) as fg6_block_available

             ,(case forecast_group_id when @fg7 then block end) as fg7_block
             ,(case forecast_group_id when @fg7 then block_pickup end) as fg7_block_pickup
             ,(case forecast_group_id when @fg7 then block_available end) as fg7_block_available

             ,(case forecast_group_id when @fg8 then block end) as fg8_block
             ,(case forecast_group_id when @fg8 then block_pickup end) as fg8_block_pickup
             ,(case forecast_group_id when @fg8 then block_available end) as fg8_block_available

             ,(case forecast_group_id when @fg9 then block end) as fg9_block
             ,(case forecast_group_id when @fg9 then block_pickup end) as fg9_block_pickup
             ,(case forecast_group_id when @fg9 then block_available end) as fg9_block_available

             ,(case forecast_group_id when @fg10 then block end) as fg10_block
             ,(case forecast_group_id when @fg10 then block_pickup end) as fg10_block_pickup
             ,(case forecast_group_id when @fg10 then block_available end) as fg10_block_available

             ,(case forecast_group_id when @fg11 then block end) as fg11_block
             ,(case forecast_group_id when @fg11 then block_pickup end) as fg11_block_pickup
             ,(case forecast_group_id when @fg11 then block_available end) as fg11_block_available

             ,(case forecast_group_id when @fg12 then block end) as fg12_block
             ,(case forecast_group_id when @fg12 then block_pickup end) as fg12_block_pickup
             ,(case forecast_group_id when @fg12 then block_available end) as fg12_block_available

             ,(case forecast_group_id when @fg13 then block end) as fg13_block
             ,(case forecast_group_id when @fg13 then block_pickup end) as fg13_block_pickup
             ,(case forecast_group_id when @fg13 then block_available end) as fg13_block_available

             ,(case forecast_group_id when @fg14 then block end) as fg14_block
             ,(case forecast_group_id when @fg14 then block_pickup end) as fg14_block_pickup
             ,(case forecast_group_id when @fg14 then block_available end) as fg14_block_available

             ,(case forecast_group_id when @fg15 then block end) as fg15_block
             ,(case forecast_group_id when @fg15 then block_pickup end) as fg15_block_pickup
             ,(case forecast_group_id when @fg15 then block_available end) as fg15_block_available

             ,(case forecast_group_id when @fg16 then block end) as fg16_block
             ,(case forecast_group_id when @fg16 then block_pickup end) as fg16_block_pickup
             ,(case forecast_group_id when @fg16 then block_available end) as fg16_block_available

             ,(case forecast_group_id when @fg17 then block end) as fg17_block
             ,(case forecast_group_id when @fg17 then block_pickup end) as fg17_block_pickup
             ,(case forecast_group_id when @fg17 then block_available end) as fg17_block_available

             ,(case forecast_group_id when @fg18 then block end) as fg18_block
             ,(case forecast_group_id when @fg18 then block_pickup end) as fg18_block_pickup
             ,(case forecast_group_id when @fg18 then block_available end) as fg18_block_available

             ,(case forecast_group_id when @fg19 then block end) as fg19_block
             ,(case forecast_group_id when @fg19 then block_pickup end) as fg19_block_pickup
             ,(case forecast_group_id when @fg19 then block_available end) as fg19_block_available

             ,(case forecast_group_id when @fg20 then block end) as fg20_block
             ,(case forecast_group_id when @fg20 then block_pickup end) as fg20_block_pickup
             ,(case forecast_group_id when @fg20 then block_available end) as fg20_block_available

             ,(case forecast_group_id when @fg21 then block end) as fg21_block
             ,(case forecast_group_id when @fg21 then block_pickup end) as fg21_block_pickup
             ,(case forecast_group_id when @fg21 then block_available end) as fg21_block_available

             ,(case forecast_group_id when @fg22 then block end) as fg22_block
             ,(case forecast_group_id when @fg22 then block_pickup end) as fg22_block_pickup
             ,(case forecast_group_id when @fg22 then block_available end) as fg22_block_available

             ,(case forecast_group_id when @fg23 then block end) as fg23_block
             ,(case forecast_group_id when @fg23 then block_pickup end) as fg23_block_pickup
             ,(case forecast_group_id when @fg23 then block_available end) as fg23_block_available

             ,(case forecast_group_id when @fg24 then block end) as fg24_block
             ,(case forecast_group_id when @fg24 then block_pickup end) as fg24_block_pickup
             ,(case forecast_group_id when @fg24 then block_available end) as fg24_block_available

             ,(case forecast_group_id when @fg25 then block end) as fg25_block
             ,(case forecast_group_id when @fg25 then block_pickup end) as fg25_block_pickup
             ,(case forecast_group_id when @fg25 then block_available end) as fg25_block_available

             ,(case forecast_group_id when @fg26 then block end) as fg26_block
             ,(case forecast_group_id when @fg26 then block_pickup end) as fg26_block_pickup
             ,(case forecast_group_id when @fg26 then block_available end) as fg26_block_available
             ,(case forecast_group_id when @fg27 then block end) as fg27_block
             ,(case forecast_group_id when @fg27 then block_pickup end) as fg27_block_pickup
             ,(case forecast_group_id when @fg27 then block_available end) as fg27_block_available
             ,(case forecast_group_id when @fg28 then block end) as fg28_block
             ,(case forecast_group_id when @fg28 then block_pickup end) as fg28_block_pickup
             ,(case forecast_group_id when @fg28 then block_available end) as fg28_block_available
             ,(case forecast_group_id when @fg29 then block end) as fg29_block
             ,(case forecast_group_id when @fg29 then block_pickup end) as fg29_block_pickup
             ,(case forecast_group_id when @fg29 then block_available end) as fg29_block_available
             ,(case forecast_group_id when @fg30 then block end) as fg30_block
             ,(case forecast_group_id when @fg30 then block_pickup end) as fg30_block_pickup
             ,(case forecast_group_id when @fg30 then block_available end) as fg30_block_available
             ,(case forecast_group_id when @fg31 then block end) as fg31_block
             ,(case forecast_group_id when @fg31 then block_pickup end) as fg31_block_pickup
             ,(case forecast_group_id when @fg31 then block_available end) as fg31_block_available
             ,(case forecast_group_id when @fg32 then block end) as fg32_block
             ,(case forecast_group_id when @fg32 then block_pickup end) as fg32_block_pickup
             ,(case forecast_group_id when @fg32 then block_available end) as fg32_block_available
             ,(case forecast_group_id when @fg33 then block end) as fg33_block
             ,(case forecast_group_id when @fg33 then block_pickup end) as fg33_block_pickup
             ,(case forecast_group_id when @fg33 then block_available end) as fg33_block_available
             ,(case forecast_group_id when @fg34 then block end) as fg34_block
             ,(case forecast_group_id when @fg34 then block_pickup end) as fg34_block_pickup
             ,(case forecast_group_id when @fg34 then block_available end) as fg34_block_available
             ,(case forecast_group_id when @fg35 then block end) as fg35_block
             ,(case forecast_group_id when @fg35 then block_pickup end) as fg35_block_pickup
             ,(case forecast_group_id when @fg35 then block_available end) as fg35_block_available
             ,(case forecast_group_id when @fg36 then block end) as fg36_block
             ,(case forecast_group_id when @fg36 then block_pickup end) as fg36_block_pickup
             ,(case forecast_group_id when @fg36 then block_available end) as fg36_block_available
             ,(case forecast_group_id when @fg37 then block end) as fg37_block
             ,(case forecast_group_id when @fg37 then block_pickup end) as fg37_block_pickup
             ,(case forecast_group_id when @fg37 then block_available end) as fg37_block_available
             ,(case forecast_group_id when @fg38 then block end) as fg38_block
             ,(case forecast_group_id when @fg38 then block_pickup end) as fg38_block_pickup
             ,(case forecast_group_id when @fg38 then block_available end) as fg38_block_available
             ,(case forecast_group_id when @fg39 then block end) as fg39_block
             ,(case forecast_group_id when @fg39 then block_pickup end) as fg39_block_pickup
             ,(case forecast_group_id when @fg39 then block_available end) as fg39_block_available
             ,(case forecast_group_id when @fg40 then block end) as fg40_block
             ,(case forecast_group_id when @fg40 then block_pickup end) as fg40_block_pickup
             ,(case forecast_group_id when @fg40 then block_available end) as fg40_block_available
             ,(case forecast_group_id when @fg41 then block end) as fg41_block
             ,(case forecast_group_id when @fg41 then block_pickup end) as fg41_block_pickup
             ,(case forecast_group_id when @fg41 then block_available end) as fg41_block_available
             ,(case forecast_group_id when @fg42 then block end) as fg42_block
             ,(case forecast_group_id when @fg42 then block_pickup end) as fg42_block_pickup
             ,(case forecast_group_id when @fg42 then block_available end) as fg42_block_available
             ,(case forecast_group_id when @fg43 then block end) as fg43_block
             ,(case forecast_group_id when @fg43 then block_pickup end) as fg43_block_pickup
             ,(case forecast_group_id when @fg43 then block_available end) as fg43_block_available
             ,(case forecast_group_id when @fg44 then block end) as fg44_block
             ,(case forecast_group_id when @fg44 then block_pickup end) as fg44_block_pickup
             ,(case forecast_group_id when @fg44 then block_available end) as fg44_block_available
             ,(case forecast_group_id when @fg45 then block end) as fg45_block
             ,(case forecast_group_id when @fg45 then block_pickup end) as fg45_block_pickup
             ,(case forecast_group_id when @fg45 then block_available end) as fg45_block_available
             ,(case forecast_group_id when @fg46 then block end) as fg46_block
             ,(case forecast_group_id when @fg46 then block_pickup end) as fg46_block_pickup
             ,(case forecast_group_id when @fg46 then block_available end) as fg46_block_available
             ,(case forecast_group_id when @fg47 then block end) as fg47_block
             ,(case forecast_group_id when @fg47 then block_pickup end) as fg47_block_pickup
             ,(case forecast_group_id when @fg47 then block_available end) as fg47_block_available
             ,(case forecast_group_id when @fg48 then block end) as fg48_block
             ,(case forecast_group_id when @fg48 then block_pickup end) as fg48_block_pickup
             ,(case forecast_group_id when @fg48 then block_available end) as fg48_block_available
             ,(case forecast_group_id when @fg49 then block end) as fg49_block
             ,(case forecast_group_id when @fg49 then block_pickup end) as fg49_block_pickup
             ,(case forecast_group_id when @fg49 then block_available end) as fg49_block_available
             ,(case forecast_group_id when @fg50 then block end) as fg50_block
             ,(case forecast_group_id when @fg50 then block_pickup end) as fg50_block_pickup
             ,(case forecast_group_id when @fg50 then block_available end) as fg50_block_available

             --Archana-Added group block,group pick up-End


             ,(case forecast_group_id when @fg1 then profit_onBooks_current end) as fg1_profit_onBooks_current
             ,(case forecast_group_id when @fg1 then profit_onBooks_change end) as fg1_profit_onBooks_change
             ,(case forecast_group_id when @fg2 then profit_onBooks_current end) as fg2_profit_onBooks_current
             ,(case forecast_group_id when @fg2 then profit_onBooks_change end) as fg2_profit_onBooks_change
             ,(case forecast_group_id when @fg3 then profit_onBooks_current end) as fg3_profit_onBooks_current
             ,(case forecast_group_id when @fg3 then profit_onBooks_change end) as fg3_profit_onBooks_change
             ,(case forecast_group_id when @fg4 then profit_onBooks_current end) as fg4_profit_onBooks_current
             ,(case forecast_group_id when @fg4 then profit_onBooks_change end) as fg4_profit_onBooks_change
             ,(case forecast_group_id when @fg5 then profit_onBooks_current end) as fg5_profit_onBooks_current
             ,(case forecast_group_id when @fg5 then profit_onBooks_change end) as fg5_profit_onBooks_change
             ,(case forecast_group_id when @fg6 then profit_onBooks_current end) as fg6_profit_onBooks_current
             ,(case forecast_group_id when @fg6 then profit_onBooks_change end) as fg6_profit_onBooks_change
             ,(case forecast_group_id when @fg7 then profit_onBooks_current end) as fg7_profit_onBooks_current
             ,(case forecast_group_id when @fg7 then profit_onBooks_change end) as fg7_profit_onBooks_change
             ,(case forecast_group_id when @fg8 then profit_onBooks_current end) as fg8_profit_onBooks_current
             ,(case forecast_group_id when @fg8 then profit_onBooks_change end) as fg8_profit_onBooks_change
             ,(case forecast_group_id when @fg9 then profit_onBooks_current end) as fg9_profit_onBooks_current
             ,(case forecast_group_id when @fg9 then profit_onBooks_change end) as fg9_profit_onBooks_change
             ,(case forecast_group_id when @fg10 then profit_onBooks_current end) as fg10_profit_onBooks_current
             ,(case forecast_group_id when @fg10 then profit_onBooks_change end) as fg10_profit_onBooks_change
             ,(case forecast_group_id when @fg11 then profit_onBooks_current end) as fg11_profit_onBooks_current
             ,(case forecast_group_id when @fg11 then profit_onBooks_change end) as fg11_profit_onBooks_change
             ,(case forecast_group_id when @fg12 then profit_onBooks_current end) as fg12_profit_onBooks_current
             ,(case forecast_group_id when @fg12 then profit_onBooks_change end) as fg12_profit_onBooks_change
             ,(case forecast_group_id when @fg13 then profit_onBooks_current end) as fg13_profit_onBooks_current
             ,(case forecast_group_id when @fg13 then profit_onBooks_change end) as fg13_profit_onBooks_change
             ,(case forecast_group_id when @fg14 then profit_onBooks_current end) as fg14_profit_onBooks_current
             ,(case forecast_group_id when @fg14 then profit_onBooks_change end) as fg14_profit_onBooks_change
             ,(case forecast_group_id when @fg15 then profit_onBooks_current end) as fg15_profit_onBooks_current
             ,(case forecast_group_id when @fg15 then profit_onBooks_change end) as fg15_profit_onBooks_change
             ,(case forecast_group_id when @fg16 then profit_onBooks_current end) as fg16_profit_onBooks_current
             ,(case forecast_group_id when @fg16 then profit_onBooks_change end) as fg16_profit_onBooks_change
             ,(case forecast_group_id when @fg17 then profit_onBooks_current end) as fg17_profit_onBooks_current
             ,(case forecast_group_id when @fg17 then profit_onBooks_change end) as fg17_profit_onBooks_change
             ,(case forecast_group_id when @fg18 then profit_onBooks_current end) as fg18_profit_onBooks_current
             ,(case forecast_group_id when @fg18 then profit_onBooks_change end) as fg18_profit_onBooks_change
             ,(case forecast_group_id when @fg19 then profit_onBooks_current end) as fg19_profit_onBooks_current
             ,(case forecast_group_id when @fg19 then profit_onBooks_change end) as fg19_profit_onBooks_change
             ,(case forecast_group_id when @fg20 then profit_onBooks_current end) as fg20_profit_onBooks_current
             ,(case forecast_group_id when @fg20 then profit_onBooks_change end) as fg20_profit_onBooks_change
             ,(case forecast_group_id when @fg21 then profit_onBooks_current end) as fg21_profit_onBooks_current
             ,(case forecast_group_id when @fg21 then profit_onBooks_change end) as fg21_profit_onBooks_change
             ,(case forecast_group_id when @fg22 then profit_onBooks_current end) as fg22_profit_onBooks_current
             ,(case forecast_group_id when @fg22 then profit_onBooks_change end) as fg22_profit_onBooks_change
             ,(case forecast_group_id when @fg23 then profit_onBooks_current end) as fg23_profit_onBooks_current
             ,(case forecast_group_id when @fg23 then profit_onBooks_change end) as fg23_profit_onBooks_change
             ,(case forecast_group_id when @fg24 then profit_onBooks_current end) as fg24_profit_onBooks_current
             ,(case forecast_group_id when @fg24 then profit_onBooks_change end) as fg24_profit_onBooks_change
             ,(case forecast_group_id when @fg25 then profit_onBooks_current end) as fg25_profit_onBooks_current
             ,(case forecast_group_id when @fg25 then profit_onBooks_change end) as fg25_profit_onBooks_change
             ,(case forecast_group_id when @fg26 then profit_onBooks_current end) as fg26_profit_onBooks_current
             ,(case forecast_group_id when @fg26 then profit_onBooks_change end) as fg26_profit_onBooks_change
             ,(case forecast_group_id when @fg27 then profit_onBooks_current end) as fg27_profit_onBooks_current
             ,(case forecast_group_id when @fg27 then profit_onBooks_change end) as fg27_profit_onBooks_change
             ,(case forecast_group_id when @fg28 then profit_onBooks_current end) as fg28_profit_onBooks_current
             ,(case forecast_group_id when @fg28 then profit_onBooks_change end) as fg28_profit_onBooks_change
             ,(case forecast_group_id when @fg29 then profit_onBooks_current end) as fg29_profit_onBooks_current
             ,(case forecast_group_id when @fg29 then profit_onBooks_change end) as fg29_profit_onBooks_change
             ,(case forecast_group_id when @fg30 then profit_onBooks_current end) as fg30_profit_onBooks_current
             ,(case forecast_group_id when @fg30 then profit_onBooks_change end) as fg30_profit_onBooks_change
             ,(case forecast_group_id when @fg31 then profit_onBooks_current end) as fg31_profit_onBooks_current
             ,(case forecast_group_id when @fg31 then profit_onBooks_change end) as fg31_profit_onBooks_change
             ,(case forecast_group_id when @fg32 then profit_onBooks_current end) as fg32_profit_onBooks_current
             ,(case forecast_group_id when @fg32 then profit_onBooks_change end) as fg32_profit_onBooks_change
             ,(case forecast_group_id when @fg33 then profit_onBooks_current end) as fg33_profit_onBooks_current
             ,(case forecast_group_id when @fg33 then profit_onBooks_change end) as fg33_profit_onBooks_change
             ,(case forecast_group_id when @fg34 then profit_onBooks_current end) as fg34_profit_onBooks_current
             ,(case forecast_group_id when @fg34 then profit_onBooks_change end) as fg34_profit_onBooks_change
             ,(case forecast_group_id when @fg35 then profit_onBooks_current end) as fg35_profit_onBooks_current
             ,(case forecast_group_id when @fg35 then profit_onBooks_change end) as fg35_profit_onBooks_change
             ,(case forecast_group_id when @fg36 then profit_onBooks_current end) as fg36_profit_onBooks_current
             ,(case forecast_group_id when @fg36 then profit_onBooks_change end) as fg36_profit_onBooks_change
             ,(case forecast_group_id when @fg37 then profit_onBooks_current end) as fg37_profit_onBooks_current
             ,(case forecast_group_id when @fg37 then profit_onBooks_change end) as fg37_profit_onBooks_change
             ,(case forecast_group_id when @fg38 then profit_onBooks_current end) as fg38_profit_onBooks_current
             ,(case forecast_group_id when @fg38 then profit_onBooks_change end) as fg38_profit_onBooks_change
             ,(case forecast_group_id when @fg39 then profit_onBooks_current end) as fg39_profit_onBooks_current
             ,(case forecast_group_id when @fg39 then profit_onBooks_change end) as fg39_profit_onBooks_change
             ,(case forecast_group_id when @fg40 then profit_onBooks_current end) as fg40_profit_onBooks_current
             ,(case forecast_group_id when @fg40 then profit_onBooks_change end) as fg40_profit_onBooks_change
             ,(case forecast_group_id when @fg41 then profit_onBooks_current end) as fg41_profit_onBooks_current
             ,(case forecast_group_id when @fg41 then profit_onBooks_change end) as fg41_profit_onBooks_change
             ,(case forecast_group_id when @fg42 then profit_onBooks_current end) as fg42_profit_onBooks_current
             ,(case forecast_group_id when @fg42 then profit_onBooks_change end) as fg42_profit_onBooks_change
             ,(case forecast_group_id when @fg43 then profit_onBooks_current end) as fg43_profit_onBooks_current
             ,(case forecast_group_id when @fg43 then profit_onBooks_change end) as fg43_profit_onBooks_change
             ,(case forecast_group_id when @fg44 then profit_onBooks_current end) as fg44_profit_onBooks_current
             ,(case forecast_group_id when @fg44 then profit_onBooks_change end) as fg44_profit_onBooks_change
             ,(case forecast_group_id when @fg45 then profit_onBooks_current end) as fg45_profit_onBooks_current
             ,(case forecast_group_id when @fg45 then profit_onBooks_change end) as fg45_profit_onBooks_change
             ,(case forecast_group_id when @fg46 then profit_onBooks_current end) as fg46_profit_onBooks_current
             ,(case forecast_group_id when @fg46 then profit_onBooks_change end) as fg46_profit_onBooks_change
             ,(case forecast_group_id when @fg47 then profit_onBooks_current end) as fg47_profit_onBooks_current
             ,(case forecast_group_id when @fg47 then profit_onBooks_change end) as fg47_profit_onBooks_change
             ,(case forecast_group_id when @fg48 then profit_onBooks_current end) as fg48_profit_onBooks_current
             ,(case forecast_group_id when @fg48 then profit_onBooks_change end) as fg48_profit_onBooks_change
             ,(case forecast_group_id when @fg49 then profit_onBooks_current end) as fg49_profit_onBooks_current
             ,(case forecast_group_id when @fg49 then profit_onBooks_change end) as fg49_profit_onBooks_change
             ,(case forecast_group_id when @fg50 then profit_onBooks_current end) as fg50_profit_onBooks_current
             ,(case forecast_group_id when @fg50 then profit_onBooks_change end) as fg50_profit_onBooks_change


             ,(case forecast_group_id when @fg1 then proPOR_onBooks_current end) as fg1_proPOR_onBooks_current
             ,(case forecast_group_id when @fg1 then proPOR_onBooks_change end) as fg1_proPOR_onBooks_change
             ,(case forecast_group_id when @fg2 then proPOR_onBooks_current end) as fg2_proPOR_onBooks_current
             ,(case forecast_group_id when @fg2 then proPOR_onBooks_change end) as fg2_proPOR_onBooks_change
             ,(case forecast_group_id when @fg3 then proPOR_onBooks_current end) as fg3_proPOR_onBooks_current
             ,(case forecast_group_id when @fg3 then proPOR_onBooks_change end) as fg3_proPOR_onBooks_change
             ,(case forecast_group_id when @fg4 then proPOR_onBooks_current end) as fg4_proPOR_onBooks_current
             ,(case forecast_group_id when @fg4 then proPOR_onBooks_change end) as fg4_proPOR_onBooks_change
             ,(case forecast_group_id when @fg5 then proPOR_onBooks_current end) as fg5_proPOR_onBooks_current
             ,(case forecast_group_id when @fg5 then proPOR_onBooks_change end) as fg5_proPOR_onBooks_change
             ,(case forecast_group_id when @fg6 then proPOR_onBooks_current end) as fg6_proPOR_onBooks_current
             ,(case forecast_group_id when @fg6 then proPOR_onBooks_change end) as fg6_proPOR_onBooks_change
             ,(case forecast_group_id when @fg7 then proPOR_onBooks_current end) as fg7_proPOR_onBooks_current
             ,(case forecast_group_id when @fg7 then proPOR_onBooks_change end) as fg7_proPOR_onBooks_change
             ,(case forecast_group_id when @fg8 then proPOR_onBooks_current end) as fg8_proPOR_onBooks_current
             ,(case forecast_group_id when @fg8 then proPOR_onBooks_change end) as fg8_proPOR_onBooks_change
             ,(case forecast_group_id when @fg9 then proPOR_onBooks_current end) as fg9_proPOR_onBooks_current
             ,(case forecast_group_id when @fg9 then proPOR_onBooks_change end) as fg9_proPOR_onBooks_change
             ,(case forecast_group_id when @fg10 then proPOR_onBooks_current end) as fg10_proPOR_onBooks_current
             ,(case forecast_group_id when @fg10 then proPOR_onBooks_change end) as fg10_proPOR_onBooks_change
             ,(case forecast_group_id when @fg11 then proPOR_onBooks_current end) as fg11_proPOR_onBooks_current
             ,(case forecast_group_id when @fg11 then proPOR_onBooks_change end) as fg11_proPOR_onBooks_change
             ,(case forecast_group_id when @fg12 then proPOR_onBooks_current end) as fg12_proPOR_onBooks_current
             ,(case forecast_group_id when @fg12 then proPOR_onBooks_change end) as fg12_proPOR_onBooks_change
             ,(case forecast_group_id when @fg13 then proPOR_onBooks_current end) as fg13_proPOR_onBooks_current
             ,(case forecast_group_id when @fg13 then proPOR_onBooks_change end) as fg13_proPOR_onBooks_change
             ,(case forecast_group_id when @fg14 then proPOR_onBooks_current end) as fg14_proPOR_onBooks_current
             ,(case forecast_group_id when @fg14 then proPOR_onBooks_change end) as fg14_proPOR_onBooks_change
             ,(case forecast_group_id when @fg15 then proPOR_onBooks_current end) as fg15_proPOR_onBooks_current
             ,(case forecast_group_id when @fg15 then proPOR_onBooks_change end) as fg15_proPOR_onBooks_change
             ,(case forecast_group_id when @fg16 then proPOR_onBooks_current end) as fg16_proPOR_onBooks_current
             ,(case forecast_group_id when @fg16 then proPOR_onBooks_change end) as fg16_proPOR_onBooks_change
             ,(case forecast_group_id when @fg17 then proPOR_onBooks_current end) as fg17_proPOR_onBooks_current
             ,(case forecast_group_id when @fg17 then proPOR_onBooks_change end) as fg17_proPOR_onBooks_change
             ,(case forecast_group_id when @fg18 then proPOR_onBooks_current end) as fg18_proPOR_onBooks_current
             ,(case forecast_group_id when @fg18 then proPOR_onBooks_change end) as fg18_proPOR_onBooks_change
             ,(case forecast_group_id when @fg19 then proPOR_onBooks_current end) as fg19_proPOR_onBooks_current
             ,(case forecast_group_id when @fg19 then proPOR_onBooks_change end) as fg19_proPOR_onBooks_change
             ,(case forecast_group_id when @fg20 then proPOR_onBooks_current end) as fg20_proPOR_onBooks_current
             ,(case forecast_group_id when @fg20 then proPOR_onBooks_change end) as fg20_proPOR_onBooks_change
             ,(case forecast_group_id when @fg21 then proPOR_onBooks_current end) as fg21_proPOR_onBooks_current
             ,(case forecast_group_id when @fg21 then proPOR_onBooks_change end) as fg21_proPOR_onBooks_change
             ,(case forecast_group_id when @fg22 then proPOR_onBooks_current end) as fg22_proPOR_onBooks_current
             ,(case forecast_group_id when @fg22 then proPOR_onBooks_change end) as fg22_proPOR_onBooks_change
             ,(case forecast_group_id when @fg23 then proPOR_onBooks_current end) as fg23_proPOR_onBooks_current
             ,(case forecast_group_id when @fg23 then proPOR_onBooks_change end) as fg23_proPOR_onBooks_change
             ,(case forecast_group_id when @fg24 then proPOR_onBooks_current end) as fg24_proPOR_onBooks_current
             ,(case forecast_group_id when @fg24 then proPOR_onBooks_change end) as fg24_proPOR_onBooks_change
             ,(case forecast_group_id when @fg25 then proPOR_onBooks_current end) as fg25_proPOR_onBooks_current
             ,(case forecast_group_id when @fg25 then proPOR_onBooks_change end) as fg25_proPOR_onBooks_change
             ,(case forecast_group_id when @fg26 then proPOR_onBooks_current end) as fg26_proPOR_onBooks_current
             ,(case forecast_group_id when @fg26 then proPOR_onBooks_change end) as fg26_proPOR_onBooks_change
             ,(case forecast_group_id when @fg27 then proPOR_onBooks_current end) as fg27_proPOR_onBooks_current
             ,(case forecast_group_id when @fg27 then proPOR_onBooks_change end) as fg27_proPOR_onBooks_change
             ,(case forecast_group_id when @fg28 then proPOR_onBooks_current end) as fg28_proPOR_onBooks_current
             ,(case forecast_group_id when @fg28 then proPOR_onBooks_change end) as fg28_proPOR_onBooks_change
             ,(case forecast_group_id when @fg29 then proPOR_onBooks_current end) as fg29_proPOR_onBooks_current
             ,(case forecast_group_id when @fg29 then proPOR_onBooks_change end) as fg29_proPOR_onBooks_change
             ,(case forecast_group_id when @fg30 then proPOR_onBooks_current end) as fg30_proPOR_onBooks_current
             ,(case forecast_group_id when @fg30 then proPOR_onBooks_change end) as fg30_proPOR_onBooks_change
             ,(case forecast_group_id when @fg31 then proPOR_onBooks_current end) as fg31_proPOR_onBooks_current
             ,(case forecast_group_id when @fg31 then proPOR_onBooks_change end) as fg31_proPOR_onBooks_change
             ,(case forecast_group_id when @fg32 then proPOR_onBooks_current end) as fg32_proPOR_onBooks_current
             ,(case forecast_group_id when @fg32 then proPOR_onBooks_change end) as fg32_proPOR_onBooks_change
             ,(case forecast_group_id when @fg33 then proPOR_onBooks_current end) as fg33_proPOR_onBooks_current
             ,(case forecast_group_id when @fg33 then proPOR_onBooks_change end) as fg33_proPOR_onBooks_change
             ,(case forecast_group_id when @fg34 then proPOR_onBooks_current end) as fg34_proPOR_onBooks_current
             ,(case forecast_group_id when @fg34 then proPOR_onBooks_change end) as fg34_proPOR_onBooks_change
             ,(case forecast_group_id when @fg35 then proPOR_onBooks_current end) as fg35_proPOR_onBooks_current
             ,(case forecast_group_id when @fg35 then proPOR_onBooks_change end) as fg35_proPOR_onBooks_change
             ,(case forecast_group_id when @fg36 then proPOR_onBooks_current end) as fg36_proPOR_onBooks_current
             ,(case forecast_group_id when @fg36 then proPOR_onBooks_change end) as fg36_proPOR_onBooks_change
             ,(case forecast_group_id when @fg37 then proPOR_onBooks_current end) as fg37_proPOR_onBooks_current
             ,(case forecast_group_id when @fg37 then proPOR_onBooks_change end) as fg37_proPOR_onBooks_change
             ,(case forecast_group_id when @fg38 then proPOR_onBooks_current end) as fg38_proPOR_onBooks_current
             ,(case forecast_group_id when @fg38 then proPOR_onBooks_change end) as fg38_proPOR_onBooks_change
             ,(case forecast_group_id when @fg39 then proPOR_onBooks_current end) as fg39_proPOR_onBooks_current
             ,(case forecast_group_id when @fg39 then proPOR_onBooks_change end) as fg39_proPOR_onBooks_change
             ,(case forecast_group_id when @fg40 then proPOR_onBooks_current end) as fg40_proPOR_onBooks_current
             ,(case forecast_group_id when @fg40 then proPOR_onBooks_change end) as fg40_proPOR_onBooks_change
             ,(case forecast_group_id when @fg41 then proPOR_onBooks_current end) as fg41_proPOR_onBooks_current
             ,(case forecast_group_id when @fg41 then proPOR_onBooks_change end) as fg41_proPOR_onBooks_change
             ,(case forecast_group_id when @fg42 then proPOR_onBooks_current end) as fg42_proPOR_onBooks_current
             ,(case forecast_group_id when @fg42 then proPOR_onBooks_change end) as fg42_proPOR_onBooks_change
             ,(case forecast_group_id when @fg43 then proPOR_onBooks_current end) as fg43_proPOR_onBooks_current
             ,(case forecast_group_id when @fg43 then proPOR_onBooks_change end) as fg43_proPOR_onBooks_change
             ,(case forecast_group_id when @fg44 then proPOR_onBooks_current end) as fg44_proPOR_onBooks_current
             ,(case forecast_group_id when @fg44 then proPOR_onBooks_change end) as fg44_proPOR_onBooks_change
             ,(case forecast_group_id when @fg45 then proPOR_onBooks_current end) as fg45_proPOR_onBooks_current
             ,(case forecast_group_id when @fg45 then proPOR_onBooks_change end) as fg45_proPOR_onBooks_change
             ,(case forecast_group_id when @fg46 then proPOR_onBooks_current end) as fg46_proPOR_onBooks_current
             ,(case forecast_group_id when @fg46 then proPOR_onBooks_change end) as fg46_proPOR_onBooks_change
             ,(case forecast_group_id when @fg47 then proPOR_onBooks_current end) as fg47_proPOR_onBooks_current
             ,(case forecast_group_id when @fg47 then proPOR_onBooks_change end) as fg47_proPOR_onBooks_change
             ,(case forecast_group_id when @fg48 then proPOR_onBooks_current end) as fg48_proPOR_onBooks_current
             ,(case forecast_group_id when @fg48 then proPOR_onBooks_change end) as fg48_proPOR_onBooks_change
             ,(case forecast_group_id when @fg49 then proPOR_onBooks_current end) as fg49_proPOR_onBooks_current
             ,(case forecast_group_id when @fg49 then proPOR_onBooks_change end) as fg49_proPOR_onBooks_change
             ,(case forecast_group_id when @fg50 then proPOR_onBooks_current end) as fg50_proPOR_onBooks_current
             ,(case forecast_group_id when @fg50 then proPOR_onBooks_change end) as fg50_proPOR_onBooks_change



             ,(case forecast_group_id when @fg1 then profit_forecast_current end) as fg1_profit_forecast_current
             ,(case forecast_group_id when @fg1 then profit_forecast_change end) as fg1_profit_forecast_change
             ,(case forecast_group_id when @fg2 then profit_forecast_current end) as fg2_profit_forecast_current
             ,(case forecast_group_id when @fg2 then profit_forecast_change end) as fg2_profit_forecast_change
             ,(case forecast_group_id when @fg3 then profit_forecast_current end) as fg3_profit_forecast_current
             ,(case forecast_group_id when @fg3 then profit_forecast_change end) as fg3_profit_forecast_change
             ,(case forecast_group_id when @fg4 then profit_forecast_current end) as fg4_profit_forecast_current
             ,(case forecast_group_id when @fg4 then profit_forecast_change end) as fg4_profit_forecast_change
             ,(case forecast_group_id when @fg5 then profit_forecast_current end) as fg5_profit_forecast_current
             ,(case forecast_group_id when @fg5 then profit_forecast_change end) as fg5_profit_forecast_change
             ,(case forecast_group_id when @fg6 then profit_forecast_current end) as fg6_profit_forecast_current
             ,(case forecast_group_id when @fg6 then profit_forecast_change end) as fg6_profit_forecast_change
             ,(case forecast_group_id when @fg7 then profit_forecast_current end) as fg7_profit_forecast_current
             ,(case forecast_group_id when @fg7 then profit_forecast_change end) as fg7_profit_forecast_change
             ,(case forecast_group_id when @fg8 then profit_forecast_current end) as fg8_profit_forecast_current
             ,(case forecast_group_id when @fg8 then profit_forecast_change end) as fg8_profit_forecast_change
             ,(case forecast_group_id when @fg9 then profit_forecast_current end) as fg9_profit_forecast_current
             ,(case forecast_group_id when @fg9 then profit_forecast_change end) as fg9_profit_forecast_change
             ,(case forecast_group_id when @fg10 then profit_forecast_current end) as fg10_profit_forecast_current
             ,(case forecast_group_id when @fg10 then profit_forecast_change end) as fg10_profit_forecast_change
             ,(case forecast_group_id when @fg11 then profit_forecast_current end) as fg11_profit_forecast_current
             ,(case forecast_group_id when @fg11 then profit_forecast_change end) as fg11_profit_forecast_change
             ,(case forecast_group_id when @fg12 then profit_forecast_current end) as fg12_profit_forecast_current
             ,(case forecast_group_id when @fg12 then profit_forecast_change end) as fg12_profit_forecast_change
             ,(case forecast_group_id when @fg13 then profit_forecast_current end) as fg13_profit_forecast_current
             ,(case forecast_group_id when @fg13 then profit_forecast_change end) as fg13_profit_forecast_change
             ,(case forecast_group_id when @fg14 then profit_forecast_current end) as fg14_profit_forecast_current
             ,(case forecast_group_id when @fg14 then profit_forecast_change end) as fg14_profit_forecast_change
             ,(case forecast_group_id when @fg15 then profit_forecast_current end) as fg15_profit_forecast_current
             ,(case forecast_group_id when @fg15 then profit_forecast_change end) as fg15_profit_forecast_change
             ,(case forecast_group_id when @fg16 then profit_forecast_current end) as fg16_profit_forecast_current
             ,(case forecast_group_id when @fg16 then profit_forecast_change end) as fg16_profit_forecast_change
             ,(case forecast_group_id when @fg17 then profit_forecast_current end) as fg17_profit_forecast_current
             ,(case forecast_group_id when @fg17 then profit_forecast_change end) as fg17_profit_forecast_change
             ,(case forecast_group_id when @fg18 then profit_forecast_current end) as fg18_profit_forecast_current
             ,(case forecast_group_id when @fg18 then profit_forecast_change end) as fg18_profit_forecast_change
             ,(case forecast_group_id when @fg19 then profit_forecast_current end) as fg19_profit_forecast_current
             ,(case forecast_group_id when @fg19 then profit_forecast_change end) as fg19_profit_forecast_change
             ,(case forecast_group_id when @fg20 then profit_forecast_current end) as fg20_profit_forecast_current
             ,(case forecast_group_id when @fg20 then profit_forecast_change end) as fg20_profit_forecast_change
             ,(case forecast_group_id when @fg21 then profit_forecast_current end) as fg21_profit_forecast_current
             ,(case forecast_group_id when @fg21 then profit_forecast_change end) as fg21_profit_forecast_change
             ,(case forecast_group_id when @fg22 then profit_forecast_current end) as fg22_profit_forecast_current
             ,(case forecast_group_id when @fg22 then profit_forecast_change end) as fg22_profit_forecast_change
             ,(case forecast_group_id when @fg23 then profit_forecast_current end) as fg23_profit_forecast_current
             ,(case forecast_group_id when @fg23 then profit_forecast_change end) as fg23_profit_forecast_change
             ,(case forecast_group_id when @fg24 then profit_forecast_current end) as fg24_profit_forecast_current
             ,(case forecast_group_id when @fg24 then profit_forecast_change end) as fg24_profit_forecast_change
             ,(case forecast_group_id when @fg25 then profit_forecast_current end) as fg25_profit_forecast_current
             ,(case forecast_group_id when @fg25 then profit_forecast_change end) as fg25_profit_forecast_change
             ,(case forecast_group_id when @fg26 then profit_forecast_current end) as fg26_profit_forecast_current
             ,(case forecast_group_id when @fg26 then profit_forecast_change end) as fg26_profit_forecast_change
             ,(case forecast_group_id when @fg27 then profit_forecast_current end) as fg27_profit_forecast_current
             ,(case forecast_group_id when @fg27 then profit_forecast_change end) as fg27_profit_forecast_change
             ,(case forecast_group_id when @fg28 then profit_forecast_current end) as fg28_profit_forecast_current
             ,(case forecast_group_id when @fg28 then profit_forecast_change end) as fg28_profit_forecast_change
             ,(case forecast_group_id when @fg29 then profit_forecast_current end) as fg29_profit_forecast_current
             ,(case forecast_group_id when @fg29 then profit_forecast_change end) as fg29_profit_forecast_change
             ,(case forecast_group_id when @fg30 then profit_forecast_current end) as fg30_profit_forecast_current
             ,(case forecast_group_id when @fg30 then profit_forecast_change end) as fg30_profit_forecast_change
             ,(case forecast_group_id when @fg31 then profit_forecast_current end) as fg31_profit_forecast_current
             ,(case forecast_group_id when @fg31 then profit_forecast_change end) as fg31_profit_forecast_change
             ,(case forecast_group_id when @fg32 then profit_forecast_current end) as fg32_profit_forecast_current
             ,(case forecast_group_id when @fg32 then profit_forecast_change end) as fg32_profit_forecast_change
             ,(case forecast_group_id when @fg33 then profit_forecast_current end) as fg33_profit_forecast_current
             ,(case forecast_group_id when @fg33 then profit_forecast_change end) as fg33_profit_forecast_change
             ,(case forecast_group_id when @fg34 then profit_forecast_current end) as fg34_profit_forecast_current
             ,(case forecast_group_id when @fg34 then profit_forecast_change end) as fg34_profit_forecast_change
             ,(case forecast_group_id when @fg35 then profit_forecast_current end) as fg35_profit_forecast_current
             ,(case forecast_group_id when @fg35 then profit_forecast_change end) as fg35_profit_forecast_change
             ,(case forecast_group_id when @fg36 then profit_forecast_current end) as fg36_profit_forecast_current
             ,(case forecast_group_id when @fg36 then profit_forecast_change end) as fg36_profit_forecast_change
             ,(case forecast_group_id when @fg37 then profit_forecast_current end) as fg37_profit_forecast_current
             ,(case forecast_group_id when @fg37 then profit_forecast_change end) as fg37_profit_forecast_change
             ,(case forecast_group_id when @fg38 then profit_forecast_current end) as fg38_profit_forecast_current
             ,(case forecast_group_id when @fg38 then profit_forecast_change end) as fg38_profit_forecast_change
             ,(case forecast_group_id when @fg39 then profit_forecast_current end) as fg39_profit_forecast_current
             ,(case forecast_group_id when @fg39 then profit_forecast_change end) as fg39_profit_forecast_change
             ,(case forecast_group_id when @fg40 then profit_forecast_current end) as fg40_profit_forecast_current
             ,(case forecast_group_id when @fg40 then profit_forecast_change end) as fg40_profit_forecast_change
             ,(case forecast_group_id when @fg41 then profit_forecast_current end) as fg41_profit_forecast_current
             ,(case forecast_group_id when @fg41 then profit_forecast_change end) as fg41_profit_forecast_change
             ,(case forecast_group_id when @fg42 then profit_forecast_current end) as fg42_profit_forecast_current
             ,(case forecast_group_id when @fg42 then profit_forecast_change end) as fg42_profit_forecast_change
             ,(case forecast_group_id when @fg43 then profit_forecast_current end) as fg43_profit_forecast_current
             ,(case forecast_group_id when @fg43 then profit_forecast_change end) as fg43_profit_forecast_change
             ,(case forecast_group_id when @fg44 then profit_forecast_current end) as fg44_profit_forecast_current
             ,(case forecast_group_id when @fg44 then profit_forecast_change end) as fg44_profit_forecast_change
             ,(case forecast_group_id when @fg45 then profit_forecast_current end) as fg45_profit_forecast_current
             ,(case forecast_group_id when @fg45 then profit_forecast_change end) as fg45_profit_forecast_change
             ,(case forecast_group_id when @fg46 then profit_forecast_current end) as fg46_profit_forecast_current
             ,(case forecast_group_id when @fg46 then profit_forecast_change end) as fg46_profit_forecast_change
             ,(case forecast_group_id when @fg47 then profit_forecast_current end) as fg47_profit_forecast_current
             ,(case forecast_group_id when @fg47 then profit_forecast_change end) as fg47_profit_forecast_change
             ,(case forecast_group_id when @fg48 then profit_forecast_current end) as fg48_profit_forecast_current
             ,(case forecast_group_id when @fg48 then profit_forecast_change end) as fg48_profit_forecast_change
             ,(case forecast_group_id when @fg49 then profit_forecast_current end) as fg49_profit_forecast_current
             ,(case forecast_group_id when @fg49 then profit_forecast_change end) as fg49_profit_forecast_change
             ,(case forecast_group_id when @fg50 then profit_forecast_current end) as fg50_profit_forecast_current
             ,(case forecast_group_id when @fg50 then profit_forecast_change end) as fg50_profit_forecast_change


             ,(case forecast_group_id when @fg1 then proPOR_forecast_current end) as fg1_proPOR_forecast_current
             ,(case forecast_group_id when @fg1 then proPOR_forecast_change end) as fg1_proPOR_forecast_change
             ,(case forecast_group_id when @fg2 then proPOR_forecast_current end) as fg2_proPOR_forecast_current
             ,(case forecast_group_id when @fg2 then proPOR_forecast_change end) as fg2_proPOR_forecast_change
             ,(case forecast_group_id when @fg3 then proPOR_forecast_current end) as fg3_proPOR_forecast_current
             ,(case forecast_group_id when @fg3 then proPOR_forecast_change end) as fg3_proPOR_forecast_change
             ,(case forecast_group_id when @fg4 then proPOR_forecast_current end) as fg4_proPOR_forecast_current
             ,(case forecast_group_id when @fg4 then proPOR_forecast_change end) as fg4_proPOR_forecast_change
             ,(case forecast_group_id when @fg5 then proPOR_forecast_current end) as fg5_proPOR_forecast_current
             ,(case forecast_group_id when @fg5 then proPOR_forecast_change end) as fg5_proPOR_forecast_change
             ,(case forecast_group_id when @fg6 then proPOR_forecast_current end) as fg6_proPOR_forecast_current
             ,(case forecast_group_id when @fg6 then proPOR_forecast_change end) as fg6_proPOR_forecast_change
             ,(case forecast_group_id when @fg7 then proPOR_forecast_current end) as fg7_proPOR_forecast_current
             ,(case forecast_group_id when @fg7 then proPOR_forecast_change end) as fg7_proPOR_forecast_change
             ,(case forecast_group_id when @fg8 then proPOR_forecast_current end) as fg8_proPOR_forecast_current
             ,(case forecast_group_id when @fg8 then proPOR_forecast_change end) as fg8_proPOR_forecast_change
             ,(case forecast_group_id when @fg9 then proPOR_forecast_current end) as fg9_proPOR_forecast_current
             ,(case forecast_group_id when @fg9 then proPOR_forecast_change end) as fg9_proPOR_forecast_change
             ,(case forecast_group_id when @fg10 then proPOR_forecast_current end) as fg10_proPOR_forecast_current
             ,(case forecast_group_id when @fg10 then proPOR_forecast_change end) as fg10_proPOR_forecast_change
             ,(case forecast_group_id when @fg11 then proPOR_forecast_current end) as fg11_proPOR_forecast_current
             ,(case forecast_group_id when @fg11 then proPOR_forecast_change end) as fg11_proPOR_forecast_change
             ,(case forecast_group_id when @fg12 then proPOR_forecast_current end) as fg12_proPOR_forecast_current
             ,(case forecast_group_id when @fg12 then proPOR_forecast_change end) as fg12_proPOR_forecast_change
             ,(case forecast_group_id when @fg13 then proPOR_forecast_current end) as fg13_proPOR_forecast_current
             ,(case forecast_group_id when @fg13 then proPOR_forecast_change end) as fg13_proPOR_forecast_change
             ,(case forecast_group_id when @fg14 then proPOR_forecast_current end) as fg14_proPOR_forecast_current
             ,(case forecast_group_id when @fg14 then proPOR_forecast_change end) as fg14_proPOR_forecast_change
             ,(case forecast_group_id when @fg15 then proPOR_forecast_current end) as fg15_proPOR_forecast_current
             ,(case forecast_group_id when @fg15 then proPOR_forecast_change end) as fg15_proPOR_forecast_change
             ,(case forecast_group_id when @fg16 then proPOR_forecast_current end) as fg16_proPOR_forecast_current
             ,(case forecast_group_id when @fg16 then proPOR_forecast_change end) as fg16_proPOR_forecast_change
             ,(case forecast_group_id when @fg17 then proPOR_forecast_current end) as fg17_proPOR_forecast_current
             ,(case forecast_group_id when @fg17 then proPOR_forecast_change end) as fg17_proPOR_forecast_change
             ,(case forecast_group_id when @fg18 then proPOR_forecast_current end) as fg18_proPOR_forecast_current
             ,(case forecast_group_id when @fg18 then proPOR_forecast_change end) as fg18_proPOR_forecast_change
             ,(case forecast_group_id when @fg19 then proPOR_forecast_current end) as fg19_proPOR_forecast_current
             ,(case forecast_group_id when @fg19 then proPOR_forecast_change end) as fg19_proPOR_forecast_change
             ,(case forecast_group_id when @fg20 then proPOR_forecast_current end) as fg20_proPOR_forecast_current
             ,(case forecast_group_id when @fg20 then proPOR_forecast_change end) as fg20_proPOR_forecast_change
             ,(case forecast_group_id when @fg21 then proPOR_forecast_current end) as fg21_proPOR_forecast_current
             ,(case forecast_group_id when @fg21 then proPOR_forecast_change end) as fg21_proPOR_forecast_change
             ,(case forecast_group_id when @fg22 then proPOR_forecast_current end) as fg22_proPOR_forecast_current
             ,(case forecast_group_id when @fg22 then proPOR_forecast_change end) as fg22_proPOR_forecast_change
             ,(case forecast_group_id when @fg23 then proPOR_forecast_current end) as fg23_proPOR_forecast_current
             ,(case forecast_group_id when @fg23 then proPOR_forecast_change end) as fg23_proPOR_forecast_change
             ,(case forecast_group_id when @fg24 then proPOR_forecast_current end) as fg24_proPOR_forecast_current
             ,(case forecast_group_id when @fg24 then proPOR_forecast_change end) as fg24_proPOR_forecast_change
             ,(case forecast_group_id when @fg25 then proPOR_forecast_current end) as fg25_proPOR_forecast_current
             ,(case forecast_group_id when @fg25 then proPOR_forecast_change end) as fg25_proPOR_forecast_change
             ,(case forecast_group_id when @fg26 then proPOR_forecast_current end) as fg26_proPOR_forecast_current
             ,(case forecast_group_id when @fg26 then proPOR_forecast_change end) as fg26_proPOR_forecast_change
             ,(case forecast_group_id when @fg27 then proPOR_forecast_current end) as fg27_proPOR_forecast_current
             ,(case forecast_group_id when @fg27 then proPOR_forecast_change end) as fg27_proPOR_forecast_change
             ,(case forecast_group_id when @fg28 then proPOR_forecast_current end) as fg28_proPOR_forecast_current
             ,(case forecast_group_id when @fg28 then proPOR_forecast_change end) as fg28_proPOR_forecast_change
             ,(case forecast_group_id when @fg29 then proPOR_forecast_current end) as fg29_proPOR_forecast_current
             ,(case forecast_group_id when @fg29 then proPOR_forecast_change end) as fg29_proPOR_forecast_change
             ,(case forecast_group_id when @fg30 then proPOR_forecast_current end) as fg30_proPOR_forecast_current
             ,(case forecast_group_id when @fg30 then proPOR_forecast_change end) as fg30_proPOR_forecast_change
             ,(case forecast_group_id when @fg31 then proPOR_forecast_current end) as fg31_proPOR_forecast_current
             ,(case forecast_group_id when @fg31 then proPOR_forecast_change end) as fg31_proPOR_forecast_change
             ,(case forecast_group_id when @fg32 then proPOR_forecast_current end) as fg32_proPOR_forecast_current
             ,(case forecast_group_id when @fg32 then proPOR_forecast_change end) as fg32_proPOR_forecast_change
             ,(case forecast_group_id when @fg33 then proPOR_forecast_current end) as fg33_proPOR_forecast_current
             ,(case forecast_group_id when @fg33 then proPOR_forecast_change end) as fg33_proPOR_forecast_change
             ,(case forecast_group_id when @fg34 then proPOR_forecast_current end) as fg34_proPOR_forecast_current
             ,(case forecast_group_id when @fg34 then proPOR_forecast_change end) as fg34_proPOR_forecast_change
             ,(case forecast_group_id when @fg35 then proPOR_forecast_current end) as fg35_proPOR_forecast_current
             ,(case forecast_group_id when @fg35 then proPOR_forecast_change end) as fg35_proPOR_forecast_change
             ,(case forecast_group_id when @fg36 then proPOR_forecast_current end) as fg36_proPOR_forecast_current
             ,(case forecast_group_id when @fg36 then proPOR_forecast_change end) as fg36_proPOR_forecast_change
             ,(case forecast_group_id when @fg37 then proPOR_forecast_current end) as fg37_proPOR_forecast_current
             ,(case forecast_group_id when @fg37 then proPOR_forecast_change end) as fg37_proPOR_forecast_change
             ,(case forecast_group_id when @fg38 then proPOR_forecast_current end) as fg38_proPOR_forecast_current
             ,(case forecast_group_id when @fg38 then proPOR_forecast_change end) as fg38_proPOR_forecast_change
             ,(case forecast_group_id when @fg39 then proPOR_forecast_current end) as fg39_proPOR_forecast_current
             ,(case forecast_group_id when @fg39 then proPOR_forecast_change end) as fg39_proPOR_forecast_change
             ,(case forecast_group_id when @fg40 then proPOR_forecast_current end) as fg40_proPOR_forecast_current
             ,(case forecast_group_id when @fg40 then proPOR_forecast_change end) as fg40_proPOR_forecast_change
             ,(case forecast_group_id when @fg41 then proPOR_forecast_current end) as fg41_proPOR_forecast_current
             ,(case forecast_group_id when @fg41 then proPOR_forecast_change end) as fg41_proPOR_forecast_change
             ,(case forecast_group_id when @fg42 then proPOR_forecast_current end) as fg42_proPOR_forecast_current
             ,(case forecast_group_id when @fg42 then proPOR_forecast_change end) as fg42_proPOR_forecast_change
             ,(case forecast_group_id when @fg43 then proPOR_forecast_current end) as fg43_proPOR_forecast_current
             ,(case forecast_group_id when @fg43 then proPOR_forecast_change end) as fg43_proPOR_forecast_change
             ,(case forecast_group_id when @fg44 then proPOR_forecast_current end) as fg44_proPOR_forecast_current
             ,(case forecast_group_id when @fg44 then proPOR_forecast_change end) as fg44_proPOR_forecast_change
             ,(case forecast_group_id when @fg45 then proPOR_forecast_current end) as fg45_proPOR_forecast_current
             ,(case forecast_group_id when @fg45 then proPOR_forecast_change end) as fg45_proPOR_forecast_change
             ,(case forecast_group_id when @fg46 then proPOR_forecast_current end) as fg46_proPOR_forecast_current
             ,(case forecast_group_id when @fg46 then proPOR_forecast_change end) as fg46_proPOR_forecast_change
             ,(case forecast_group_id when @fg47 then proPOR_forecast_current end) as fg47_proPOR_forecast_current
             ,(case forecast_group_id when @fg47 then proPOR_forecast_change end) as fg47_proPOR_forecast_change
             ,(case forecast_group_id when @fg48 then proPOR_forecast_current end) as fg48_proPOR_forecast_current
             ,(case forecast_group_id when @fg48 then proPOR_forecast_change end) as fg48_proPOR_forecast_change
             ,(case forecast_group_id when @fg49 then proPOR_forecast_current end) as fg49_proPOR_forecast_current
             ,(case forecast_group_id when @fg49 then proPOR_forecast_change end) as fg49_proPOR_forecast_change
             ,(case forecast_group_id when @fg50 then proPOR_forecast_current end) as fg50_proPOR_forecast_current
             ,(case forecast_group_id when @fg50 then proPOR_forecast_change end) as fg50_proPOR_forecast_change


        from
            (
                select
                    base.occupancy_dt,
                    base.forecast_group_id,
                    datename(dw,base.occupancy_dt) as dow,
                    isnull(a.rooms_sold,0.0) as roomsoldcurrent,
                    isnull(a.room_revenue,0.0) as bookedroomrevenuecurrent,
                    isnull(a.adr,0.0) as bookedadrcurrent,
                    isnull(b.occupancy_forecast_current,0.0) as occfcstcurrent,
                    isnull(b.fcsted_room_revenue_current,0.0) as fcstedroomrevenuecurrent,
                    isnull(b.fcsted_adr_current,0.0) as fcstedadrcurrent,
                    case when base.occupancy_dt <= @business_dt then
                             cast((isnull(a.rooms_sold,0.0) - isnull(c.rooms_sold,isnull(a.rooms_sold,0.0))) as numeric(19,2))
                         else
                             cast((isnull(a.rooms_sold,0.0) - isnull(c.rooms_sold,0.0)) as numeric(19,2))
                        end as roomssoldchange,

                    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and d.occupancy_nbr is NULL then 0
                         when base.occupancy_dt <= @business_dt then
                             cast((isnull(b.occupancy_forecast_current,0.0) - isnull(d.occupancy_nbr,isnull(b.occupancy_forecast_current,0.0))) as numeric(19,2))
                         else
                             cast((isnull(b.occupancy_forecast_current,0.0) - isnull(d.occupancy_nbr,0.0)) as numeric(19,2))
                        end as occfcstchange,

                    case when base.occupancy_dt <= @business_dt then
                             cast((isnull(a.room_revenue,0.0) - isnull(c.room_revenue,isnull(a.room_revenue,0.0))) as numeric(19,2))
                         else
                             cast((isnull(a.room_revenue,0.0) - isnull(c.room_revenue,0.0)) as numeric(19,2))
                        end as bookedroomrevenuechange,

                    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and d.revenue is NULL then 0
                         when base.occupancy_dt <= @business_dt then
                             cast((isnull(b.fcsted_room_revenue_current,0.0) - isnull(d.revenue,isnull(b.fcsted_room_revenue_current,0.0))) as numeric(19,2))
                         else
                             cast((isnull(b.fcsted_room_revenue_current,0.0) - isnull(d.revenue,0.0)) as numeric(19,2))
                        end as fcstedroomrevenuechange,

                    case when base.occupancy_dt <= @business_dt then
                             cast((isnull(a.adr,0.0)- isnull(c.adr,isnull(a.adr,0.0))) as numeric(19,2))
                         else
                             cast((isnull(a.adr,0.0)- isnull(c.adr,0.0)) as numeric(19,2))
                        end as bookedadrchange,

                    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and d.adr is NULL then 0
                         when base.occupancy_dt <= @business_dt then
                             cast((isnull(b.fcsted_adr_current,0.0) - isnull(d.adr,isnull(b.fcsted_adr_current,0.0)) ) as numeric(19,2))
                         else
                             cast((isnull(b.fcsted_adr_current,0.0) - isnull(d.adr,0.0) ) as numeric(19,2))
                        end as fcstedadrchange,

                    groupBlock.block,
                    groupBlock.block_pickup,
                    groupBlock.block_available,


                    isnull(onBooksProfitCurrent.profit,0.0) as profit_onBooks_current,
                    case when base.occupancy_dt <= @business_dt then
                             cast(isnull(onBooksProfitCurrent.profit,0.0) - ISNULL(onBooksProfitPrev.profit, isnull(onBooksProfitCurrent.profit,0.0)) as numeric (19,2))
                         else
                             cast(isnull(onBooksProfitCurrent.profit,0.0) - ISNULL(onBooksProfitPrev.profit, 0.0) as numeric (19,2))
                        end as profit_onBooks_change,

                    isnull(onBooksProfitCurrent.proPOR,0.0) as proPOR_onBooks_current,
                    case when base.occupancy_dt <= @business_dt then
                             cast(isnull(onBooksProfitCurrent.proPOR,0.0) - ISNULL(onBooksProfitPrev.proPOR, isnull(onBooksProfitCurrent.proPOR,0.0)) as numeric (19,2))
                         else
                             cast(isnull(onBooksProfitCurrent.proPOR,0.0) - ISNULL(onBooksProfitPrev.proPOR, 0.0) as numeric (19,2))
                        end as proPOR_onBooks_change,

                    isnull(forecastProfitCurrent.profit,0.0) as profit_forecast_current,
                    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and forecastProfitPrev.profit is NULL then 0
                         when base.occupancy_dt <= @business_dt then
                             cast(isnull(forecastProfitCurrent.profit,0.0) - ISNULL(forecastProfitPrev.profit, isnull(forecastProfitCurrent.profit,0.0)) as numeric (19,2))
                         else
                             cast(isnull(forecastProfitCurrent.profit,0.0) - ISNULL(forecastProfitPrev.profit, 0.0) as numeric (19,2))
                        end as profit_forecast_change,

                    isnull(forecastProfitCurrent.proPOR,0.0) as proPOR_forecast_current,
                    case when @rolling_business_dt = 'LAST_OPTIMIZATION' and forecastProfitPrev.proPOR is NULL then 0
                         when base.occupancy_dt <= @business_dt then
                             cast(isnull(forecastProfitCurrent.proPOR,0.0) - ISNULL(forecastProfitPrev.proPOR, isnull(forecastProfitCurrent.proPOR,0.0)) as numeric (19,2))
                         else
                             cast(isnull(forecastProfitCurrent.proPOR,0.0) - ISNULL(forecastProfitPrev.proPOR, 0.0) as numeric (19,2))
                        end as proPOR_forecast_change
                from
                    (
                        select @property_id property_id,CAST(calendar_date as date) Occupancy_DT,Forecast_Group_id  from calendar_dim left join @tempFG on Forecast_Group_ID is not null
                        where calendar_date between @start_date and @end_date
                    )base left join
                    (
                        select * from dbo.ufn_get_adr_roomrev_by_individual_fg (@property_id,@forecast_group_id,@start_date,@end_date)
                    ) as a on base.property_id=a.property_id and base.occupancy_dt=a.occupancy_dt and base.forecast_group_id=a.forecast_group_id
                          left join
                    (
                        select * from dbo.ufn_get_occupancy_forecast_by_individual_fg (@property_id,3,13,@forecast_group_id,@start_date,@end_date)
                    ) as b on base.property_id=b.property_id and base.occupancy_dt=b.occupancy_dt and base.forecast_group_id=b.forecast_group_id
                          left join
                    (
                        select
                            occupancy_dt,
                            property_id,
                            Forecast_Group_Id,
                            rooms_sold,
                            room_revenue,
                            adr
                        from dbo.ufn_get_adr_roomrev_asof_lastOptimization_or_businessdate_by_individual_fg (@property_id,@forecast_group_id,@business_dt,@start_date,@end_date,@rolling_business_dt)
                    ) as c on base.property_id=c.property_id and base.occupancy_dt=c.occupancy_dt and base.forecast_group_id=c.forecast_group_id
                          left join
                    (
                        select * from dbo.ufn_get_occupancy_forecast_asof_lastoptimization_or_businessdate_by_Individual_fg (@property_id,@forecast_group_id,@business_dt,@start_date,@end_date,@rolling_business_dt)
                    ) as d on base.property_id=d.property_id and base.occupancy_dt=d.occupancy_dt and base.forecast_group_id=d.forecast_group_id

                          left join
                    (
                        select * from dbo.ufn_get_onBooks_profit_metrics_by_individual_fg (@property_id,@forecast_group_id,@start_date,@end_date)
                    ) as onBooksProfitCurrent on base.property_id=onBooksProfitCurrent.property_id and base.occupancy_dt=onBooksProfitCurrent.occupancy_dt and base.forecast_group_id=onBooksProfitCurrent.forecast_group_id
                          left join
                    (
                        select * from dbo.ufn_get_onBooks_profit_metrics_asof_lastoptimization_or_businessdate_by_individual_fg (@property_id,@forecast_group_id,@business_dt,@start_date,@end_date,@rolling_business_dt)
                    ) as onBooksProfitPrev on base.property_id=onBooksProfitPrev.property_id and base.occupancy_dt=onBooksProfitPrev.occupancy_dt and base.forecast_group_id=onBooksProfitPrev.forecast_group_id

                          left join
                    (
                        select * from dbo.ufn_get_forecast_profit_metrics_by_individual_fg (@property_id,3,13,@forecast_group_id,@start_date,@end_date)
                    ) as forecastProfitCurrent on base.property_id=forecastProfitCurrent.property_id and base.occupancy_dt=forecastProfitCurrent.occupancy_dt and base.forecast_group_id=forecastProfitCurrent.forecast_group_id
                          left join
                    (
                        select * from dbo.ufn_get_forecast_profit_metrics_asof_lastoptimization_or_businessdate_by_individual_fg (@property_id,@forecast_group_id,@business_dt,@start_date,@end_date,@rolling_business_dt)
                    ) as forecastProfitPrev on base.property_id=forecastProfitPrev.property_id and base.occupancy_dt=forecastProfitPrev.occupancy_dt and base.forecast_group_id=forecastProfitPrev.forecast_group_id

                        --Archana added group block-Aggregated-START
                          left join
                    (
                        select * from ufn_get_groupblock_grouppickup_by_ForecastGroup (@property_id,@forecast_group_id,@start_date, @end_date,0)
                    ) as groupBlock on
                                a.property_id=groupBlock.property_id and a.occupancy_dt=groupBlock.occupancy_dt and a.forecast_group_id=groupBlock.forecast_group_Id

                --Archana added group block-Aggregated-END
            ) data
    )data2
group by occupancy_dt,dow
order by occupancy_dt
    return
end