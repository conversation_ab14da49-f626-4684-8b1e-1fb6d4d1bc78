GO
/****** Object@  StoredProcedure [dbo].[usp_OOO_Affecting_Demand_Exception]    Script Date@ 3/18/2024******/
SET ANSI_NULLS ON
 GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_OOO_Affecting_Demand_Exception]'))
BEGIN
		DROP PROCEDURE [dbo].[usp_OOO_Affecting_Demand_Exception]
END
GO
/****************************************************************************
Procedure@  usp_OOO_Affecting_Demand_Exception
Author@     Akshay Kore
Date@       3/18/2024

*****************************************************************************/

CREATE PROCEDURE [dbo].[usp_OOO_Affecting_Demand_Exception]
	@propertyId Int,
	@accomClassId Int,
	@startDate date,
	@barWindowEndDate date,
	@costlyOutOfOrderThreshold numeric (10,5),
	@decisionId int,
	@isSnoozeEnabled int,
    @oooThresholdForException Int
AS
BEGIN
WITH oootable AS (
    SELECT Property_ID, occupancy_dt, OOO, cap, @accomClassId AS accomClassID
    FROM (
             SELECT AA.Property_ID AS Property_ID, occupancy_dt, sum((Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other)) AS OOO, sum(Accom_Capacity) AS cap
             FROM accom_activity AA
                      left join Out_Of_Order_Override oooo
                                on AA.occupancy_dt = oooo.Override_Date
                                    and AA.Accom_Type_ID = oooo.Accom_Type_ID
                      INNER JOIN Accom_type AT ON (AT.accom_type_ID = AA.Accom_Type_ID)
             WHERE AA.Property_ID = @propertyId AND AT.accom_class_id = @accomClassId and AT.Accom_Type_Capacity > 0 AND Occupancy_DT BETWEEN @startDate AND @barWindowEndDate
               and oooo.Accom_Type_ID is null
               and oooo.Override_Date is null
             GROUP BY AA.Property_ID, AA.Occupancy_DT, AT.accom_class_id
         ) AS b
    WHERE ooo > ((@oooThresholdForException * cap)/100.0)
),
 accomTypeTable AS (
     SELECT DISTINCT (Accom_Class_ID), property_id, Accom_Type_ID
     FROM Accom_Type
     WHERE Property_ID = @propertyId AND Accom_Type_Capacity > 0
 ),
 queryTable AS (
     SELECT rq.Rate_qualified_ID, AT.Accom_Type_ID, oootable.OOO,
            CASE (DATEPART(WEEKDAY, Occupancy_DT))
                WHEN 1 THEN (Sunday)
                WHEN 2 THEN (Monday)
                WHEN 3 THEN (Tuesday)
                WHEN 4 THEN (Wednesday)
                WHEN 5 THEN (Thursday)
                WHEN 6 THEN (Friday)
                WHEN 7 THEN (Saturday)
                END AS Rate_value
             ,Occupancy_DT
    FROM [Rate_Qualified] rq
    INNER JOIN Rate_Qualified_Details rqd ON rq.Rate_Qualified_ID = rqd.Rate_Qualified_ID AND
    (
        (rqd.Start_Date_DT >= @startDate
        AND rqd.End_Date_DT <= @barWindowEndDate)
        OR (
        rqd.Start_Date_DT <= @startDate
        AND rqd.End_Date_DT >= @startDate
        )
        OR (
        rqd.Start_Date_DT <= @barWindowEndDate
        AND rqd.End_Date_DT >= @barWindowEndDate
        )
    )
    INNER JOIN accomTypeTable AS at
        ON at.Accom_Type_ID = rqd.Accom_Type_ID AND at.Property_ID = @propertyId AND at.Accom_Class_ID = @accomClassId
    INNER JOIN oootable
        ON at.Property_ID = oooTable.Property_ID AND oootable.Occupancy_DT BETWEEN rqd.Start_Date_DT AND rqd.End_Date_DT
    where rq.Rate_Qualified_Type_Id =3
),
rateTable AS (
    SELECT @propertyId AS property_ID, Occupancy_DT, min(accomRateValue) AS lowestQualifiedrate, tempTable.OOO
    FROM (
        SELECT Occupancy_DT, min(Rate_value) AS accomRateValue, queryTable.OOO, rate_qualified_id
        FROM queryTable
        WHERE Rate_value > - 1
        GROUP BY Occupancy_DT, Rate_qualified_ID, OOO
        ) AS tempTable
    GROUP BY Occupancy_DT, OOO
),
remainingDemand As (
    SELECT odf.Occupancy_DT, sum(user_remaining_demand) AS user_remaining_demand
        FROM Occupancy_Demand_FCST odf
        WHERE odf.Occupancy_DT between @startDate and @barWindowEndDate
        and odf.Accom_Class_ID = @accomClassId and odf.Decision_ID=@decisionId
    GROUP BY odf.Occupancy_DT
)
SELECT result.property_id, result.accom_class_id, result.occupancy_dt, result.lrv, lowestqualifiedrate, result.ooo, SN.Info_Mgr_Costly_Ooo_Excep_Snooze_ID,
       CASE WHEN result.ooo < result.user_remaining_demand THEN result.ooo * result.LRV ELSE result.user_remaining_demand  * result.LRV END displaced_revenue
    FROM (
         SELECT dl.Property_ID, Accom_Class_ID, oootable.occupancy_dt, LRV, lowestQualifiedrate, oootable.OOO, IsNull(rd.user_remaining_demand,0) user_remaining_demand
         FROM Decision_LRV dl
                  INNER JOIN oootable ON oootable.Occupancy_DT = dl.Occupancy_DT AND oootable.property_ID = dl.Property_ID
             AND dl.Accom_Class_ID = @accomClassId
                  LEFT JOIN rateTable ON rateTable.Occupancy_DT = dl.Occupancy_DT AND rateTable.property_ID = dl.Property_ID
             AND dl.Accom_Class_ID = @accomClassId
                  Left JOIN remainingDemand rd ON rd.Occupancy_DT=oootable.Occupancy_DT
         WHERE Decision_ID = @decisionId AND dl.Property_ID = @propertyId AND LRV > ALL (
             SELECT Maximum = CASE
                                  WHEN lowestQualifiedrate > @costlyOutOfOrderThreshold
                                      THEN lowestQualifiedrate
                                  ELSE @costlyOutOfOrderThreshold
                 END
         )
    ) AS result
         LEFT OUTER JOIN Info_mgr_Costly_ooo_Excep_Snooze SN ON result.occupancy_dt = SN.occupancy_date AND result.accom_class_id = SN.accom_class_id
    WHERE (
                  0 = @isSnoozeEnabled OR SN.OOO_Rooms IS NULL OR
                  (result.OOO > SN.OOO_Rooms AND result.LRV >= SN.LRV)
          );
END