if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_activity_by_individual_bv]'))
drop function [ufn_get_activity_by_individual_bv]
GO
/***************************************************************************************
Function Name: ufn_get_activity_by_individual_bv

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@Business_Group_ID --> list of comma seperated BVs
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
Ouput Parameter : NA

Execution: this is just an example
	Example 1: Report at Business view level 
	-----------------------------------------
	select * from dbo.ufn_get_activity_by_individual_bv (18,'84','2011-07-01','2011-07-31')
	
	select * from dbo.ufn_get_activity_by_individual_bv (18,'81,84','2011-07-01','2011-07-31')
	

	
Purpose: The purpose of this function is to report 'rooms_sold','adr' and 'room_revenue' values by 
		 market segment for a given occupancy date range. 

Assumptions : Please enclose the Business_Group_ID  in single quotes.
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/07/2012		Atul				Shendye					Initial Version
07/14/2021		Adarsh									    Discontinued MS changes
22/03/2022		Vikas 			 Shivankar					FEYNMAN-1009 :Increase the size of BV input parameter to 1000 characters
***************************************************************************************/

CREATE function [dbo].[ufn_get_activity_by_individual_bv]

(
		@property_id int,
		@Business_Group_ID varchar(1000),
		@start_date date,
		@end_date date
)		
returns  @adr_room_rev table
	(	
		occupancy_dt date,
		property_id	int,
		Business_Group_ID int,
		rooms_sold	numeric(18,0),
		room_revenue numeric(19,5),
		adr	numeric(19,5)
	)
as
begin
		insert into @adr_room_rev
		select Occupancy_DT,maa.Property_ID,msbg.Business_Group_ID, SUM(Rooms_Sold) as Rooms_Sold, SUM(Room_Revenue) as Room_Revenue,
			ADR =
			case (SUM(Rooms_Sold))
				when 0 then 0
			else
				SUM(Room_Revenue)/SUM(Rooms_Sold)
			end
			from Mkt_Accom_Activity maa inner join Accom_Type at on maa.Accom_Type_Id = at.Accom_Type_ID and at.isComponentRoom = 'N'
			inner join
			(
				select distinct ms.Mkt_Seg_ID,bg.Business_Group_ID from Business_Group BG inner join Mkt_Seg_Business_Group MSBG on bg.Business_Group_ID=msbg.Business_Group_ID
				inner join Mkt_Seg MS on MS.Mkt_Seg_ID=msbg.Mkt_Seg_ID
				where BG.Property_ID=@property_id and MS.Property_ID=@property_id and bg.Status_ID=1 and ms.Status_ID in (1,3)
				and bg.Business_Group_ID in (SELECT Value FROM varcharToInt(@Business_Group_ID,','))		
			) msbg on maa.Mkt_Seg_ID=msbg.Mkt_Seg_ID
			where maa.Property_ID=@property_id and Occupancy_DT between @start_date and @end_date
			group by maa.Property_ID,Occupancy_DT,msbg.Business_Group_ID
	return
end

GO