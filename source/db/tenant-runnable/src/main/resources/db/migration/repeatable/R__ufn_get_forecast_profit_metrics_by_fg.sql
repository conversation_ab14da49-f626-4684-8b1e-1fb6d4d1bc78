if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_forecast_profit_metrics_by_fg]'))
drop function [ufn_get_forecast_profit_metrics_by_fg]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create function [dbo].[ufn_get_forecast_profit_metrics_by_fg]

(
		@property_id int,
		@record_type_id int,
		@process_status_id int,
		@forecast_group_id varchar(500),
		@start_date date,
		@end_date date
)
returns  @profit_metrics table
	(
		occupancy_dt	date,
		property_id	int,
		profit	numeric(19,5),
		proPOR	numeric(19,5)
	)
as
begin
		declare @caughtupdate date
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,@record_type_id,@process_status_id))
		declare @includeZeroCapacityRT int
		set @includeZeroCapacityRT = 0

		insert into @profit_metrics
		select
			occupancy_dt,
			@property_id,
			sum(profit) as profit,
			proPOR =
				case (sum(occupancy_nbr))
					when 0 then 0
				else
					sum(profit)/sum(occupancy_nbr)
				end
		from
		(
			select Occupancy_DT,Occupancy_NBR,profit
			from
			(
			    SELECT occ.Occupancy_DT, occ.Property_ID,
                    msfg.Forecast_Group_ID,
                    occ.Occupancy_NBR,
                    occ.profit
                FROM Occupancy_FCST AS occ
                    INNER JOIN Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID=1 and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end) and at.System_Default=0
                        and at.isComponentRoom = 'N'
                    INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID=1
                WHERE Occupancy_DT >= @caughtUpDate
                UNION ALL
                SELECT maa.Occupancy_DT, maa.Property_ID,
                    msfg.Forecast_Group_ID,
                    maa.Rooms_Sold as Occupancy_NBR,
                    maa.total_profit as profit
                FROM Mkt_Accom_Activity AS maa
                    INNER JOIN Accom_Type AS at ON maa.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID=1 and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end) and at.System_Default=0
                        and at.isComponentRoom = 'N'
                    INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID and ac.Status_ID=1 and ac.System_Default=0
                    INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON maa.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID=1
                WHERE maa.Occupancy_DT < @caughtUpDate

			)
			forecastData
			where property_id=@property_id and occupancy_dt between @start_date and @end_date
				and forecast_group_id in (select value from varchartoint(@forecast_group_id,','))
		) ocf  group by occupancy_dt
		order by occupancy_dt
	return
end

GO