IF EXISTS(SELECT *
          FROM sys.objects
          WHERE object_id = object_id(N'[usp_populate_profit_adjustment_pace]'))
    DROP PROCEDURE [usp_populate_profit_adjustment_pace]
GO
/*******************************************************************************************************************************************

Store Procedure Name: usp_populate_profit_adjustment_pace

Output Parameter : Populate Profit adjustment differential pace in Pace_Profit_Adjustment table.

Execution: this is just an example
	Example 1: usp_populate_profit_adjustment_pace
	-----------------------------------------
	exec dbo.usp_populate_profit_adjustment_pace

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/19/2024		Sachin 				Kale					Initial Version
07/22/2024		Sachin 				Kale					Support for season dates
09/02/2024		Sachin 				Kale					Season dates as Month start/end date
09/21/2024		Sachin 				Kale					Updated adjustment calculation logic
09/25/2024		Sachin 				Kale					Added 'Unset' type
28/11/2024      Sachin              Kale                    Converted CTE to temp table
25/01/2024      Vaibhav             Javadekar               Unset TypeId used from existing pace. remove createDTTM during union to avoid duplicates
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_populate_profit_adjustment_pace]
(
    @start_date DATE,
    @end_date DATE
)
AS
BEGIN

    DECLARE @FinalSeasonDates TABLE (Month INT, StartDate DATE, EndDate DATE);
    CREATE TABLE #CurrentPace
    (
        [Market_Code]               [NVARCHAR](50) NULL,
        [Rate_Code]                 [NVARCHAR](50) NULL,
        [Profit_Adjustment_Type_ID] INT            NOT NULL,
        [Month]                     [INT]          NOT NULL,
        [Accom_Class_ID]            [INT]          NOT NULL,
        [Sunday_Adjust]             NUMERIC(19, 5) NOT NULL,
        [Monday_Adjust]             NUMERIC(19, 5) NOT NULL,
        [Tuesday_Adjust]            NUMERIC(19, 5) NOT NULL,
        [Wednesday_Adjust]          NUMERIC(19, 5) NOT NULL,
        [Thursday_Adjust]           NUMERIC(19, 5) NOT NULL,
        [Friday_Adjust]             NUMERIC(19, 5) NOT NULL,
        [Saturday_Adjust]           NUMERIC(19, 5) NOT NULL,
        [CreateDate_DTTM]           [DATETIME]     NOT NULL
    );
    CREATE TABLE #ExistingPace
    (
        [rownum]                    [BIGINT],
        [Pace_Profit_Adjustment_ID] [BIGINT],
        [Market_Code]               [NVARCHAR](50) NULL,
        [Rate_Code]                 [NVARCHAR](50) NULL,
        [Profit_Adjustment_Type_ID] INT            NOT NULL,
        [Month]                     [INT]          NOT NULL,
        [Accom_Class_ID]            [INT]          NOT NULL,
        [Sunday_Adjust]             NUMERIC(19, 5) NULL,
        [Monday_Adjust]             NUMERIC(19, 5) NULL,
        [Tuesday_Adjust]            NUMERIC(19, 5) NULL,
        [Wednesday_Adjust]          NUMERIC(19, 5) NULL,
        [Thursday_Adjust]           NUMERIC(19, 5) NULL,
        [Friday_Adjust]             NUMERIC(19, 5) NULL,
        [Saturday_Adjust]           NUMERIC(19, 5) NULL,
        [CreateDate_DTTM]           [DATETIME]     NOT NULL,
        [Season_Start_Date]         [DATE]         NOT NULL,
        [Season_End_Date]           [DATE]         NOT NULL
    );
    WITH SeasonDatesForMonth AS (
        SELECT
            DATEADD(DAY, 1 - DAY(@start_date), @start_date) AS StartDate,
            EOMONTH(@start_date) AS EndDate,
            MONTH(@start_date) AS Month
        UNION ALL
        SELECT
            DATEADD(DAY, 1, EndDate),
            EOMONTH(DATEADD(MONTH, 1, StartDate)),
            MONTH(DATEADD(MONTH, 1, StartDate))
        FROM SeasonDatesForMonth
        WHERE DATEADD(MONTH, 1, StartDate) <= @end_date
    )

    INSERT INTO @FinalSeasonDates
    SELECT Month, StartDate, EndDate
    FROM SeasonDatesForMonth

    DECLARE @AdjustmentId INT = (SELECT Profit_Adjustment_Type_ID
                                 FROM [Profit_Adjustment_Type]
                                 WHERE Profit_Adjustment_Type_Description = 'Adjustments');
    DECLARE @ReplacementId INT = (SELECT Profit_Adjustment_Type_ID
                                  FROM [Profit_Adjustment_Type]
                                  WHERE Profit_Adjustment_Type_Description = 'Replacement');

    INSERT INTO #CurrentPace
    SELECT ''                                                                          Market_Code,
           CONCAT(Tier_Name, tier)                                                     Rate_Code,
           CASE WHEN tier_name LIKE 'CASH%' THEN @AdjustmentId ELSE @ReplacementId END Profit_Adjustment_Type_ID,
           Month,
           Accom_Class_id,
           Sunday_Adjust,
           Monday_Adjust,
           Tuesday_Adjust,
           Wednesday_Adjust,
           Thursday_Adjust,
           Friday_Adjust,
           Saturday_Adjust,
           GETDATE()                                                                   [CreateDate_DTTM]
    FROM (SELECT SUBSTRING(rate_code, 5, LEN(rate_code))            Tier,
                 Month,
                 Accom_Class_id,
                 AVG(Sunday_Rate_Adjust + Sunday_Profit_Adjust)     Sunday_Adjust,
                 AVG(Monday_Rate_Adjust + Monday_Profit_Adjust)       Monday_Adjust,
                 AVG(Tuesday_Rate_Adjust + Tuesday_Profit_Adjust)     Tuesday_Adjust,
                 AVG(Wednesday_Rate_Adjust + Wednesday_Profit_Adjust) Wednesday_Adjust,
                 AVG(Thursday_Rate_Adjust + Thursday_Profit_Adjust)   Thursday_Adjust,
                 AVG(Friday_Rate_Adjust + Friday_Profit_Adjust)       Friday_Adjust,
                 AVG(Saturday_Rate_Adjust + Saturday_Profit_Adjust)   Saturday_Adjust
          FROM Profit_Adj_Fcst paf
                   JOIN Rate_Qualified rq
                        ON rq.Rate_Code_Name = paf.Rate_Code AND rq.Yieldable = 1 AND
                           rq.Status_ID = 1 AND rq.Managed_In_G3 = 0
                   JOIN Accom_type at ON at.Accom_Type_ID = paf.Accom_Type_ID
          WHERE ISNUMERIC(SUBSTRING(rate_code, 5, LEN(rate_code))) = 1
          GROUP BY SUBSTRING(rate_code, 5, LEN(rate_code)), Month,
                   at.Accom_Class_id) ProfirAdjustmentOnTiersOne
             CROSS JOIN (SELECT 'CASHS' AS Tier_Name
                         UNION ALL
                         SELECT 'CASHT'
                         UNION ALL
                         SELECT 'COMPS'
                         UNION ALL
                         SELECT 'COMPT') AS Tiers

    UNION

    SELECT ''                                                                         Market_Code,
           ProfirAdjustmentOnTiersTwo.Rate_code,
           CASE WHEN ams.complimentary = 1 THEN @ReplacementId ELSE @AdjustmentId END Profit_Adjustment_Type_ID,
           Month,
           Accom_Class_ID,
           Sunday_Adjust,
           Monday_Adjust,
           Tuesday_Adjust,
           Wednesday_Adjust,
           Thursday_Adjust,
           Friday_Adjust,
           Saturday_Adjust,
           GETDATE()                                                                  [CreateDate_DTTM]
    FROM (SELECT ''                                                 Market_Code,
                 paf.rate_code,
                 Month,
                 Accom_Class_ID,
                 AVG(Sunday_Rate_Adjust + Sunday_Profit_Adjust)     Sunday_Adjust,
                 AVG(Monday_Rate_Adjust + Monday_Profit_Adjust)       Monday_Adjust,
                 AVG(Tuesday_Rate_Adjust + Tuesday_Profit_Adjust)     Tuesday_Adjust,
                 AVG(Wednesday_Rate_Adjust + Wednesday_Profit_Adjust) Wednesday_Adjust,
                 AVG(Thursday_Rate_Adjust + Thursday_Profit_Adjust)   Thursday_Adjust,
                 AVG(Friday_Rate_Adjust + Friday_Profit_Adjust)       Friday_Adjust,
                 AVG(Saturday_Rate_Adjust + Saturday_Profit_Adjust)   Saturday_Adjust
          FROM Profit_Adj_Fcst paf
                   JOIN Rate_Qualified rq
                        ON rq.Rate_Code_Name = paf.Rate_Code AND rq.Yieldable = 1 AND
                           rq.Status_ID = 1 AND rq.Managed_In_G3 = 0
                   JOIN Accom_type at ON at.Accom_Type_ID = paf.Accom_Type_ID
          WHERE paf.rate_code IS NOT NULL
            AND ISNUMERIC(SUBSTRING(paf.rate_code, 5, LEN(paf.rate_code))) <> 1
          GROUP BY paf.rate_code, Month, at.Accom_Class_ID) ProfirAdjustmentOnTiersTwo
             JOIN Analytical_mkt_seg ams ON ams.Rate_Code = ProfirAdjustmentOnTiersTwo.rate_code

    UNION

    SELECT DISTINCT ProfirAdjustmentOnTiersThree.Market_Code,
                    ''                                                                         Rate_code,
                    CASE WHEN ams.complimentary = 1 THEN @ReplacementId ELSE @AdjustmentId END Profit_Adjustment_Type_ID,
                    Month,
                    Accom_Class_ID,
                    Sunday_Adjust,
                    Monday_Adjust,
                    Tuesday_Adjust,
                    Wednesday_Adjust,
                    Thursday_Adjust,
                    Friday_Adjust,
                    Saturday_Adjust,
                    GETDATE()                                                                  [CreateDate_DTTM]
    FROM (SELECT Month,
                 Accom_Class_ID,
                 Market_Code,
                 AVG(Sunday_Rate_Adjust + Sunday_Profit_Adjust)     Sunday_Adjust,
                 AVG(Monday_Rate_Adjust + Monday_Profit_Adjust)       Monday_Adjust,
                 AVG(Tuesday_Rate_Adjust + Tuesday_Profit_Adjust)     Tuesday_Adjust,
                 AVG(Wednesday_Rate_Adjust + Wednesday_Profit_Adjust) Wednesday_Adjust,
                 AVG(Thursday_Rate_Adjust + Thursday_Profit_Adjust)   Thursday_Adjust,
                 AVG(Friday_Rate_Adjust + Friday_Profit_Adjust)       Friday_Adjust,
                 AVG(Saturday_Rate_Adjust + Saturday_Profit_Adjust)   Saturday_Adjust
          FROM Profit_Adj_Fcst paf
                   JOIN Accom_type at ON at.Accom_Type_ID = paf.Accom_Type_ID
          WHERE paf.rate_code IS NULL
            AND paf.Market_Code IS NOT NULL
          GROUP BY paf.Market_Code, Month, at.Accom_Class_ID) ProfirAdjustmentOnTiersThree
             JOIN Analytical_Mkt_Seg ams
                  ON ProfirAdjustmentOnTiersThree.Market_code = ams.Market_Code;

    INSERT INTO #ExistingPace
    SELECT row_number() OVER (PARTITION BY Market_Code, Rate_Code, Profit_Adjustment_Type_ID, Accom_Class_ID, Season_Start_Date, Season_End_Date ORDER BY CreateDate_DTTM DESC) AS rownum, *
    FROM Pace_Profit_Adjustment


    INSERT INTO Pace_Profit_Adjustment
    SELECT
        finalUnion.Market_Code, finalUnion.Rate_code, finalUnion.Profit_Adjustment_Type_ID,
        finalUnion.Month, finalUnion.Accom_Class_ID, finalUnion.Sunday_Adjust, finalUnion.Monday_Adjust,
        finalUnion.Tuesday_Adjust, finalUnion.Wednesday_Adjust, finalUnion.Thursday_Adjust,
        finalUnion.Friday_Adjust, finalUnion.Saturday_Adjust,
        Getdate()              CreateDate_DTTM,
        finalUnion.StartDate, finalUnion.EndDate
    FROM (SELECT cp.Market_Code,
                 cp.Rate_code,
                 cp.Profit_Adjustment_Type_ID,
                 cp.Month,
                 cp.Accom_Class_ID,
                 cp.Sunday_Adjust,
                 cp.Monday_Adjust,
                 cp.Tuesday_Adjust,
                 cp.Wednesday_Adjust,
                 cp.Thursday_Adjust,
                 cp.Friday_Adjust,
                 cp.Saturday_Adjust,
                 season.StartDate,
                 season.EndDate
          FROM #CurrentPace cp
                   INNER JOIN @FinalSeasonDates season on cp.Month = season.Month
                   INNER JOIN #ExistingPace existingPace
                              ON cp.Accom_Class_ID = existingPace.Accom_Class_ID
                                  AND season.StartDate = existingPace.Season_Start_Date
                                  AND season.EndDate = existingPace.Season_End_Date
                                  AND cp.Profit_Adjustment_Type_ID = existingPace.Profit_Adjustment_Type_ID
                                  AND ISNULL(cp.Market_Code, '') = ISNULL(existingPace.Market_Code, '')
                                  AND ISNULL(cp.Rate_Code, '') = ISNULL(existingPace.Rate_Code, '')
                                  AND (ISNULL(ROUND(CP.Sunday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Sunday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Monday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Monday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Tuesday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Tuesday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Wednesday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Wednesday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Thursday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Thursday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Friday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Friday_Adjust, 5), 0)
                                      OR ISNULL(ROUND(CP.Saturday_Adjust, 5), 0) != ISNULL(ROUND(existingPace.Saturday_Adjust, 5), 0))
          WHERE existingPace.rownum = 1
          UNION
          SELECT cp.Market_Code,
                 cp.Rate_code,
                 cp.Profit_Adjustment_Type_ID,
                 cp.Month,
                 cp.Accom_Class_ID,
                 cp.Sunday_Adjust,
                 cp.Monday_Adjust,
                 cp.Tuesday_Adjust,
                 cp.Wednesday_Adjust,
                 cp.Thursday_Adjust,
                 cp.Friday_Adjust,
                 cp.Saturday_Adjust,
                 season.StartDate,
                 season.EndDate
          FROM #CurrentPace cp
                   INNER JOIN @FinalSeasonDates season on cp.Month = season.Month
                   LEFT JOIN #ExistingPace existingPace
                             ON cp.Accom_Class_ID = existingPace.Accom_Class_ID
                                 AND season.StartDate = existingPace.Season_Start_Date
                                 AND season.EndDate = existingPace.Season_End_Date
                                 AND cp.Profit_Adjustment_Type_ID = existingPace.Profit_Adjustment_Type_ID
                                 AND ISNULL(cp.Market_Code, '') = ISNULL(existingPace.Market_Code, '')
                                 AND ISNULL(cp.Rate_Code, '') = ISNULL(existingPace.Rate_Code, '')
          WHERE existingPace.Accom_Class_ID IS NULL
            AND existingPace.rownum IS NULL
          UNION
          SELECT existingPace.market_code    Market_Code,
                 existingPace.rate_code      Rate_code,
                 existingPace.Profit_Adjustment_Type_ID Profit_Adjustment_Type_ID,
                 existingPace.month Month,
                 existingPace.accom_class_id Accom_Class_ID,
                 null   Sunday_Adjust,
                 null   Monday_Adjust,
                 null   Tuesday_Adjust,
                 null   Wednesday_Adjust,
                 null   Thursday_Adjust,
                 null   Friday_Adjust,
                 null   Saturday_Adjust,
                 season.startdate            Season_Start_Date,
                 season.enddate              Season_End_Date
          FROM #Existingpace existingPace
                   LEFT JOIN @FinalSeasonDates season
                             ON season.startdate = existingPace.season_start_date
                                 AND season.enddate = existingPace.season_end_date
                   LEFT JOIN #Currentpace cp
                             ON ISNULL(cp.market_code, '') = ISNULL(existingPace.market_code, '')
                                 AND ISNULL(cp.rate_code, '') = ISNULL(existingPace.rate_code, '')
                                 AND cp.profit_adjustment_type_id = existingPace.profit_adjustment_type_id
                                 AND cp.accom_class_id = existingPace.accom_class_id
                                 AND cp.month = season.month
          WHERE NOT EXISTS (SELECT 1
                            FROM #Currentpace cp_sub
                            WHERE ISNULL(cp_sub.Market_Code, '') = ISNULL(existingPace.Market_Code, '')
                              AND ISNULL(cp_sub.Rate_Code, '') = ISNULL(existingPace.Rate_Code, ''))) AS finalUnion
             LEFT JOIN Pace_Profit_Adjustment ppa on
                ISNULL(finalUnion.Market_Code, '') = ISNULL(ppa.Market_Code, '') AND
                ISNULL(finalUnion.Rate_Code, '') = ISNULL(ppa.Rate_Code, '') AND
                finalUnion.Profit_Adjustment_Type_ID =  ppa.Profit_Adjustment_Type_ID AND
                finalUnion.Accom_Class_ID =  ppa.Accom_Class_ID AND
                ISNULL(ROUND(finalUnion.Sunday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Sunday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Monday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Monday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Tuesday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Tuesday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Wednesday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Wednesday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Thursday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Thursday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Friday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Friday_Adjust, 5), 0) AND
                ISNULL(ROUND(finalUnion.Saturday_Adjust, 5), 0) = ISNULL(ROUND(ppa.Saturday_Adjust, 5), 0) AND
                finalUnion.StartDate =  ppa.Season_Start_Date AND
                finalUnion.EndDate =  ppa.Season_End_Date
    where ppa.Profit_Adjustment_Type_ID is NULL;

    DROP TABLE #CurrentPace;
    DROP TABLE #ExistingPace;
END;