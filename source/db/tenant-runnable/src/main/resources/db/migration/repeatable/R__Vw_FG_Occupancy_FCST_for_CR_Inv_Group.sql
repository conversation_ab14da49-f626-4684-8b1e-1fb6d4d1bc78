DROP VIEW IF EXISTS dbo.[Vw_FG_Occupancy_FCST_for_CR_Inv_Group]
GO
CREATE VIEW [dbo].[Vw_FG_Occupancy_FCST_for_CR_Inv_Group] AS
SELECT df.Occupancy_Demand_FCST_ID AS Occupancy_FCST_ID
		,dateWiseMSRT.Occupancy_DT, dateWiseMSRT.Property_ID,msfg.Forecast_Group_ID, at.Accom_Class_ID
		,SUM(case when at.isComponentRoom='Y' then 0 else ISNULL(occ.Occupancy_NBR,0) end) AS Occupancy_NBR
        ,SUM(case when at.isComponentRoom='Y' then 0 else maa.Rooms_Sold end) AS Rooms_Sold
        ,df.Peak_Demand, df.Remaining_Demand, df.User_Remaining_Demand
        ,igd.Inventory_Group_ID
FROM (SELECT CAST(calendar_date AS DATE) as Occupancy_DT, at.Accom_Type_ID, at.Property_ID ,ms.MKT_SEG_ID FROM calendar_dim cal 
		CROSS JOIN Accom_Type at CROSS JOIN Mkt_Seg ms WHERE at.Status_ID = 1 AND ms.Status_ID = 1) as dateWiseMSRT LEFT JOIN 
		dbo.Occupancy_FCST AS occ ON occ.Occupancy_DT = dateWiseMSRT.Occupancy_DT  
		AND occ.Accom_Type_ID = dateWiseMSRT.Accom_Type_ID AND dateWiseMSRT.Mkt_Seg_ID = occ.MKT_SEG_ID INNER JOIN
		dbo.Accom_Type AS at ON dateWiseMSRT.Accom_Type_ID = at.Accom_Type_ID INNER JOIN 
		Inventory_Group_Details as igd ON igd.Accom_Class_ID = at.Accom_Class_ID INNER JOIN
		dbo.Mkt_Seg_Forecast_Group AS msfg ON dateWiseMSRT.MKT_SEG_ID = msfg.Mkt_Seg_ID LEFT JOIN
		dbo.Occupancy_Demand_FCST AS df ON dateWiseMSRT.Property_ID = df.Property_ID AND dateWiseMSRT.Occupancy_DT = df.Occupancy_DT AND msfg.Forecast_Group_ID = df.Forecast_Group_ID 
		AND igd.Accom_Class_ID = df.Accom_Class_ID INNER JOIN 
		dbo.Mkt_Accom_Activity AS maa ON dateWiseMSRT.Property_ID = maa.Property_ID AND dateWiseMSRT.Occupancy_DT = maa.Occupancy_DT AND maa.Accom_Type_ID = at.Accom_Type_ID
		AND maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID WHERE msfg.Status_ID = 1 
GROUP BY dateWiseMSRT.Occupancy_DT, dateWiseMSRT.Property_ID, msfg.Forecast_Group_ID, df.Occupancy_Demand_FCST_ID,at.Accom_Class_ID, df.Peak_Demand, df.Remaining_Demand, df.User_Remaining_Demand, igd.Inventory_Group_ID
GO

