IF EXISTS
(
    SELECT *
    FROM sys.objects
    WHERE object_id = OBJECT_ID(N'[dbo].[usp_bad_load_property_details_rc_level]')
)
    DROP PROCEDURE [dbo].[usp_bad_load_property_details_rc_level]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[usp_bad_load_property_details_rc_level]
(
    @property_id int,
    @start_date date,
    @end_date date,
    @buisness_date date,
    @is_physical_capacity_enabled int,
    @comp_rooms_filter_flag int,
    @showDisplayStatusRts varchar(10)
)
as
begin
    declare @caughtupdate date

    -- extract caughtup date for a property
    set @caughtupdate =
    (
        select dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
    )

    declare @exclude_comp_room varchar(5) = '0,1';
    if (@comp_rooms_filter_flag = 1)
        set @exclude_comp_room = '0';

    --- get data
select activity.Property_ID,
       p.Property_Name,
       activity.Rooms_Sold,
       cast(System_Demand as numeric(19, 2)) System_Demand,
       cast(User_Remaining_Demand as numeric(19, 2)) User_Remaining_Demand,
       cast(Occupancy_Forecast as numeric(8, 1)) Occupancy_Forecast,
       cast(ADR_OnBooks as numeric(19, 2)) ADR_OnBooks,
       cast(ADR_Forecast as numeric(19, 2)) ADR_Forecast,
       cast(Revenue as numeric(19, 2)) Revenue,
       cast(Revenue_Forecast as numeric(19, 2)) Revenue_Forecast,
       cast(REVPAR_OnBooks as numeric(19, 2)) REVPAR_OnBooks,
       cast(REVPAR_Forecast as numeric(19, 2)) REVPAR_Forecast,
       (activity.Rooms_Sold - ISNULL(paceActivity.rooms_sold, 0)) as Rooms_Sold_Pickup,
       cast(budget_data.Rooms as numeric(19, 2)) as Budget_Rooms,
       cast(budget_data.Budget_Revenue as numeric(19, 2)) as Budget_Revenue,
       cast(budget_data.Budget_ADR as numeric(19, 2)) as Budget_ADR,
       cast(budget_data.Budget_REVPAR as numeric(19, 2)) as Budget_REVPAR,
       cast(user_forecast_data.Rooms as numeric(19, 2)) as User_Forecast_Rooms,
       cast(user_forecast_data.User_Forecast_Revenue as numeric(19, 2)) as User_Forecast_Revenue,
       cast(user_forecast_data.User_Forecast_ADR as numeric(19, 2)) as User_Forecast_ADR,
       cast(user_forecast_data.User_Forecast_REVPAR as numeric(19, 2)) as User_Forecast_REVPAR,
       (activity.Revenue - ISNULL(paceActivity.room_revenue, 0)) as Room_Revenue_Pickup,
       cast((CASE
                 WHEN ISNULL(activity.Rooms_Sold, 0) > 0 THEN
                     activity.Revenue / activity.Rooms_Sold
                 ELSE
                     0
           END
                ) - (CASE
                         WHEN ISNULL(paceActivity.Rooms_Sold, 0) > 0 THEN
                             ISNULL(paceActivity.room_revenue, 0) / paceActivity.Rooms_Sold
                         ELSE
                             0
           END
                ) as numeric(19, 2)) as Adr_Pickup,
       Capacity AS Physical_Capacity,
       Capacity - OOO As Effective_Capacity
from
    (
        -- Activity data
        select Property_ID,
               Rooms_Sold,
               Capacity,
               ADR_OnBooks = case (Rooms_Sold)
                                 when 0 then
                                     0
                                 else
                                     Room_Revenue / Rooms_Sold
                   end,
               REVPAR_OnBooks = case (@is_physical_capacity_enabled)
                                    when 1 then
                                        CASE (capacity)
                                            WHEN 0 THEN
                                                0
                                            ELSE
                                                (Room_Revenue / capacity)
                                            END
                                    else
                                        case (Capacity - OOO)
                                            when 0 then
                                                0
                                            else
                                                Room_Revenue / (Capacity - OOO)
                                            end
                   end,
               OOO,
               Room_Revenue Revenue
        from
            (
                select Property_ID,
                       sum(Total_Accom_Capacity) - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Accom_Capacity), 0), 0) Capacity,
                       (sum(ta.Rooms_Sold) - iif(@comp_rooms_filter_flag = 1, isNull(sum(maa.Rooms_Sold), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Rooms_Sold), 0), 0)
                           ) as Rooms_Sold,
                       (sum(ta.Room_Revenue) - iif(@comp_rooms_filter_flag = 1, isNull(sum(maa.Room_Revenue), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Room_Revenue), 0), 0)
                           ) as Room_Revenue,
                       (sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.OOO), 0), 0)
                           ) as OOO
                from Total_Activity ta
                         left join
                     (
                         select aact.Occupancy_DT,
                                sum(aact.Accom_Capacity) as Accom_Capacity,
                                sum(aact.Rooms_Sold) as Rooms_Sold,
                                sum(aact.Room_Revenue) as Room_Revenue,
                                sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) OOO
                         from Accom_Activity aact
                                  inner join Accom_Type at
                         on aact.Accom_Type_ID = at.Accom_Type_ID
                         where Occupancy_DT
                             between @start_date and @end_date
                           and at.Display_Status_ID in (
                             select items from Split('2', ',')
                             )
                         group by aact.Occupancy_DT
                     ) as aa
                     on aa.Occupancy_DT = ta.Occupancy_DT
                         left join
                     (
                         select mact.Occupancy_DT,
                                sum(mact.Rooms_Sold) as Rooms_Sold,
                                sum(mact.Room_Revenue) as Room_Revenue
                         from Mkt_Accom_Activity mact
                                  inner join Mkt_Seg ms
                                             on ms.Mkt_Seg_ID = mact.Mkt_Seg_ID
                                                 and ms.Exclude_CompHouse_Data_Display IN ( 1 )
                         where mact.Occupancy_DT
                                   between @start_date and @end_date
                         group by mact.Occupancy_DT
                     ) as maa
                     on maa.Occupancy_DT = ta.Occupancy_DT
                where Property_Id = @property_id
                  and ta.Occupancy_DT
                    between @start_date and @end_date
                group by Property_ID
            ) data
    ) as activity
        left join
    (
        select property_id,
               sum(Rooms_Sold) Rooms_Sold,
               sum(Room_Revenue) Room_Revenue
        from
            (
                select pa.Property_ID,
                       (sum(pa.Rooms_Sold) - iif(@comp_rooms_filter_flag = 1, isNull(sum(pma.Rooms_Sold), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Rooms_Sold), 0), 0)
                           ) as Rooms_Sold,
                       (sum(pa.Room_Revenue) - iif(@comp_rooms_filter_flag = 1, isNull(sum(pma.Room_Revenue), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Room_Revenue), 0), 0)
                           ) as Room_Revenue
                from
                    (
                        select property_id,
                               sum(Rooms_Sold) Rooms_Sold,
                               sum(Room_Revenue) Room_Revenue
                        from pace_total_activity
                        where business_day_end_dt = @buisness_date
                          and occupancy_dt
                            between @start_date and @end_date
                          and occupancy_dt > @buisness_date
                          and -- @start_date or @buisness_date whichever will be higher will be picked.
                            property_id = @property_id
                        group by property_id
                    ) pa
                        left join
                    (
                        select p.property_id,
                               sum(Rooms_Sold) Rooms_Sold,
                               sum(Room_Revenue) Room_Revenue
                        from PACE_Mkt_Activity p
                                 inner join Mkt_Seg ms
                                            on ms.Mkt_Seg_ID = p.Mkt_Seg_ID
                                                and ms.Exclude_CompHouse_Data_Display IN ( 1 )
                        where business_day_end_dt = @buisness_date
                          and occupancy_dt
                            between @start_date and @end_date
                          and occupancy_dt > @buisness_date
                          and -- @start_date or @buisness_date whichever will be higher will be picked.
                            p.property_id = @property_id
                        group by p.property_id
                    ) pma
                    on pma.Property_ID = pa.Property_ID
                        left join
                    (
                        select aact.Property_ID,
                               sum(aact.Rooms_Sold) as Rooms_Sold,
                               sum(aact.Room_Revenue) as Room_Revenue
                        from Pace_Accom_Activity aact
                                 inner join Accom_Type at
                        on aact.Accom_Type_ID = at.Accom_Type_ID
                        where business_day_end_dt = @buisness_date
                            and Occupancy_DT
                                between @start_date and @end_date
                            and occupancy_dt > @buisness_date
                            and at.Display_Status_ID in (select items from Split('2', ','))
                        group by aact.Property_ID
                    ) as aa
                    on aa.Property_ID = pa.Property_ID
                group by pa.Property_ID
                union all
                select property_id,
                       (sum(ta.Rooms_Sold) - iif(@comp_rooms_filter_flag = 1, isNull(sum(maa.Rooms_Sold), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Rooms_Sold), 0), 0)
                           ) as Rooms_Sold,
                       (sum(ta.Room_Revenue) - iif(@comp_rooms_filter_flag = 1, isNull(sum(maa.Room_Revenue), 0), 0)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Room_Revenue), 0), 0)
                           ) as Room_Revenue
                from Total_Activity ta
                         left join
                     (
                         select aact.Occupancy_DT,
                                sum(aact.Rooms_Sold) as Rooms_Sold,
                                sum(aact.Room_Revenue) as Room_Revenue
                         from Accom_Activity aact
                                  inner join Accom_Type at
                         on aact.Accom_Type_ID = at.Accom_Type_ID
                        --  where business_day_end_dt <= @buisness_date
                         where Occupancy_DT
                             between @start_date and @end_date
                           and at.Display_Status_ID in (
                             select items from Split('2', ',')
                             )
                         group by aact.Occupancy_DT
                     ) as aa
                     on aa.Occupancy_DT = ta.Occupancy_DT
                         left join
                     (
                         select mact.Occupancy_DT,
                                sum(mact.Rooms_Sold) as Rooms_Sold,
                                sum(mact.Room_Revenue) as Room_Revenue
                         from Mkt_Accom_Activity mact
                                  inner join Mkt_Seg ms
                                             on ms.Mkt_Seg_ID = mact.Mkt_Seg_ID
                                                 and ms.Exclude_CompHouse_Data_Display IN ( 1 )
                         where mact.Occupancy_DT
                                   between @start_date and @end_date
                         group by mact.Occupancy_DT
                     ) as maa
                     on maa.Occupancy_DT = ta.Occupancy_DT
                where ta.occupancy_dt
                    between @start_date and @end_date
                  and ta.Occupancy_DT <= @buisness_date
                  and -- @end_date or @buisness_date whichever will be lower will be picked.
                    property_id = @property_id
                group by property_id
            ) temp
        group by property_id
    ) as paceActivity
    on activity.Property_ID = paceActivity.property_id
        left join
    (
        select Property_ID,
               Occupancy_Forecast,
               ADR_Forecast = case (Occupancy_Forecast)
                                  when 0 then
                                      0
                                  else
                                      Room_Revenue / Occupancy_Forecast
                   end,
               REVPAR_Forecast = case (@is_physical_capacity_enabled)
                                     when 1 then
                                         CASE (capacity)
                                             WHEN 0 THEN
                                                 0
                                             ELSE
                                                 (Room_Revenue / capacity)
                                             end
                                     else
                                         case (Capacity - OOO)
                                             when 0 then
                                                 0
                                             else
                                                 Room_Revenue / (Capacity - OOO)
                                             end
                   end,
               Room_Revenue Revenue_Forecast
        from
            (
                select Property_ID,
                       sum(Total_Accom_Capacity) Capacity,
                       sum(Occupancy_NBR) Occupancy_Forecast,
                       sum(Revenue) Room_Revenue,
                       sum(OOO) OOO
                from FN_Occupancy_Forecast_comp_room_exclusion_rc_level(
                        @caughtupdate,
                        @is_physical_capacity_enabled,
                        @exclude_comp_room,
                        @showDisplayStatusRts
                     )
                where Property_Id = @property_id
                  and Occupancy_DT
                    between @start_date and @end_date
                group by Property_ID
            ) data
    ) as occ_forecast
    on occ_forecast.Property_ID = activity.Property_ID
        left join
    (
        select Property_ID,
               User_Remaining_Demand = SUM(   case
                                                  when Occupancy_DT >= @caughtupdate then
                                                      User_Remaining_Demand
                                                  else
                                                      0
                   end
                                       ),
               System_Demand = SUM(   case
                                          when Occupancy_DT >= @caughtupdate then
                                              Remaining_Demand
                                          else
                                              0
                   end
                               )
        from Occupancy_Demand_FCST
        where Property_ID = @property_id
          and Occupancy_DT
            between @start_date and @end_date
        group by Property_ID
    ) as demand
    on demand.Property_ID = activity.Property_ID
        left join
    (
        select budget.Rooms,
               budget.Budget_ADR,
               budget.Budget_Revenue,
               budget.Property_ID,
               case (@is_physical_capacity_enabled)
                   when 1 then
                       case (cap.Capacity)
                           when 0 then
                               0
                           else
                               budget.Budget_Revenue / cap.Capacity
                           end
                   else
                       case (cap.Capacity - cap.OOO)
                           when 0 then
                               0
                           else
                               budget.Budget_Revenue / (cap.Capacity - cap.OOO)
                           end
                   end as Budget_REVPAR
        from
            (
                select SUM(bd.Rooms_Sold) as Rooms,
                       SUM(bd.Room_Revenue) as Budget_Revenue,
                       case (SUM(bd.Rooms_Sold))
                           when 0 then
                               0
                           else
                               SUM(bd.Room_Revenue) / SUM(bd.Rooms_Sold)
                           end as Budget_ADR,
                       @property_id as Property_ID
                from Budget_Data as bd
                where Occupancy_Date
                          between @start_date and @end_date
            ) as budget
                inner join
            (
                select sum(Total_Accom_Capacity)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Accom_Capacity), 0), 0) Capacity,
                       Property_ID,
                       (sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.OOO), 0), 0)
                           ) as OOO
                from Total_Activity ta
                         left join
                     (
                         select aact.Occupancy_DT,
                                sum(aact.Accom_Capacity) Accom_Capacity,
                                sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) OOO
                         from Accom_Activity aact
                                  inner join Accom_Type at
                         on aact.Accom_Type_ID = at.Accom_Type_ID
                         where Occupancy_DT
                             between @start_date and @end_date
                           and at.Display_Status_ID in (
                             select items from Split('2', ',')
                             )
                         group by aact.Occupancy_DT
                     ) as aa
                     on aa.Occupancy_DT = ta.Occupancy_DT
                where ta.Occupancy_DT
                          between @start_date and @end_date
                group by Property_ID
            ) as cap
            on budget.property_id = cap.Property_ID
    ) as budget_data
    on budget_data.Property_ID = activity.Property_ID
        left join
    (
        select user_forecast.Rooms,
               user_forecast.User_Forecast_ADR,
               user_forecast.User_Forecast_Revenue,
               user_forecast.Property_ID,
               case (@is_physical_capacity_enabled)
                   when 1 then
                       case (cap.Capacity)
                           when 0 then
                               0
                           else
                               user_forecast.User_Forecast_Revenue / cap.Capacity
                           end
                   else
                       case (cap.Capacity - cap.OOO)
                           when 0 then
                               0
                           else
                               user_forecast.User_Forecast_Revenue / (cap.Capacity - cap.OOO)
                           end
                   end as User_Forecast_REVPAR
        from
            (
                select SUM(ufd.Rooms_Sold) as Rooms,
                       SUM(ufd.Room_Revenue) as User_Forecast_Revenue,
                       case (SUM(ufd.Rooms_Sold))
                           when 0 then
                               0
                           else
                               SUM(ufd.Room_Revenue) / SUM(ufd.Rooms_Sold)
                           end as User_Forecast_ADR,
                       @property_id as Property_ID
                from User_Forecast_Data as ufd
                where Occupancy_Date
                          between @start_date and @end_date
            ) as user_forecast
                inner join
            (
                select sum(Total_Accom_Capacity)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.Accom_Capacity), 0), 0) Capacity,
                       Property_ID,
                       (sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other)
                           - iif(@showDisplayStatusRts = '1', isNull(sum(aa.OOO), 0), 0)
                           ) as OOO
                from Total_Activity ta
                         left join
                     (
                         select aact.Occupancy_DT,
                                sum(aact.Accom_Capacity) Accom_Capacity,
                                sum(Rooms_Not_Avail_Maint + Rooms_Not_Avail_Other) OOO
                         from Accom_Activity aact
                                  inner join Accom_Type at
                         on aact.Accom_Type_ID = at.Accom_Type_ID
                         where Occupancy_DT
                             between @start_date and @end_date
                           and at.Display_Status_ID in (
                             select items from Split('2', ',')
                             )
                         group by aact.Occupancy_DT
                     ) as aa
                     on aa.Occupancy_DT = ta.Occupancy_DT
                where ta.Occupancy_DT
                          between @start_date and @end_date
                group by Property_ID
            ) as cap
            on user_forecast.property_id = cap.Property_ID
    ) as user_forecast_data
    on user_forecast_data.Property_ID = activity.Property_ID
        inner join Property p
                   on activity.Property_ID = p.Property_ID
end