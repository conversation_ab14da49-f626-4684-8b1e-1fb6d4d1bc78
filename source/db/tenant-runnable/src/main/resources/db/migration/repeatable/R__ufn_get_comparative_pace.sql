if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_comparative_pace]'))
    drop function [ufn_get_comparative_pace]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


/*************************************************************************************

Function Name: ufn_get_comparative_pace

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12). pass -1 if no data is needed
	@d1 to @d8 --> occupancy_dates currently we are supporting max 8 dates. pass '1900-01-01' as dates if less than 8 dates are needed.
	@isHotel --> 1 if you need data at Hotel level
	@isTransient --> 1 if you need data at transient level
	@isGroup --> 1 if you need data at group level
	@fg1 ... @fg25 --> actual forecast group id if we need data for this forecast group, else -1 
	@pace_days --> number of pace days
	@isSold --> 1 if sold is needed else send 0
	@isLrv --> 1 if lrv is needed else send 0
	@isOcFcst --> 1 if occfcst is needed else send 0
	@bv1 ... @bv8 --> actual business view id if we need data for this business view, else -1 
	@rc1 ... @rc8 --> actual accom class id if we need data for this accom class, else -1 
	
Ouput Parameter : NA

Execution: this is just an example
	select * from ufn_get_comparative_pace(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@isHotel,@isTransient,@isGroup,
											@fg1,@fg2,@fg3,@fg4,@fg5,@fg6,@fg7,@fg8,@pace_days,@isSold,@isLrv,@isOcFcst,@bv1,@bv2,@bv3,@bv4,@bv5,@bv6,@bv7,@bv8,
											@rc1,@rc2,@rc3,@rc4,@rc5,@rc6,@rc7,@rc8)

	Purpose: The purpose of this function is to get comparative pace selected dates for selected pace days. 

Assumptions : NA
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
04/02/2012		Atul				Shendye					Initial Version
31/07/2023		Maksood				Shaikh					KANADA-741	
06/08/2023		Maksood				Shaikh					KANADA-742	
***************************************************************************************/
CREATE function [dbo].[ufn_get_comparative_pace]
(
		@property_id int,
		@d1 date,
		@d2 date,
		@d3 date,
		@d4 date,
		@d5 date,
		@d6 date,
		@d7 date,
		@d8 date,
		@isHotel int,
		@isTransient int,
		@isGroup int,
		@fg1 int,
		@fg2 int,
		@fg3 int,
		@fg4 int,
		@fg5 int,
		@fg6 int,
		@fg7 int,
		@fg8 int,
		@fg9 int,
		@fg10 int,
		@fg11 int,
		@fg12 int,
		@fg13 int,
		@fg14 int,
		@fg15 int,
		@fg16 int,
		@fg17 int,
		@fg18 int,
		@fg19 int,
		@fg20 int,
		@fg21 int,
		@fg22 int,
		@fg23 int,
		@fg24 int,
		@fg25 int,
		@pace_days int,
		@isSold int,
		@isLrv int,
		@isOcFcst int,
		@bv1 int,
		@bv2 int,
		@bv3 int,
		@bv4 int,
		@bv5 int,
		@bv6 int,
		@bv7 int,
		@bv8 int,
		@bv9 int,
		@bv10 int,
		@bv11 int,
		@bv12 int,
		@rc1 int,
		@rc2 int,
		@rc3 int,
		@rc4 int,
		@rc5 int,
		@rc6 int,
		@rc7 int,
		@rc8 int,
		@rc9 int,
		@rc10 int,
		@rc11 int,
		@rc12 int,
		@includeInactiveRT int
)		
returns  @comparative_pace table
	(	
			daystoArrival int,
			d1_prop_sold int,
			d2_prop_sold int,
			d3_prop_sold int,
			d4_prop_sold int,
			d5_prop_sold int,
			d6_prop_sold int,
			d7_prop_sold int,
			d8_prop_sold int,
			d1_prop_lrv numeric(19,5),
			d2_prop_lrv numeric(19,5),
			d3_prop_lrv numeric(19,5),
			d4_prop_lrv numeric(19,5),
			d5_prop_lrv numeric(19,5),
			d6_prop_lrv numeric(19,5),
			d7_prop_lrv numeric(19,5),
			d8_prop_lrv numeric(19,5),
			d1_prop_occfcst numeric(8,2),
			d2_prop_occfcst numeric(8,2),
			d3_prop_occfcst numeric(8,2),
			d4_prop_occfcst numeric(8,2),
			d5_prop_occfcst numeric(8,2),
			d6_prop_occfcst numeric(8,2),
			d7_prop_occfcst numeric(8,2),
			d8_prop_occfcst numeric(8,2),
			d1_prop_occfcst_perc numeric(8,2),
			d2_prop_occfcst_perc numeric(8,2),
			d3_prop_occfcst_perc numeric(8,2),
			d4_prop_occfcst_perc numeric(8,2),
			d5_prop_occfcst_perc numeric(8,2),
			d6_prop_occfcst_perc numeric(8,2),
			d7_prop_occfcst_perc numeric(8,2),
			d8_prop_occfcst_perc numeric(8,2),
			d1_prop_occfcst_PhyCap_perc numeric(8,2),
			d2_prop_occfcst_PhyCap_perc numeric(8,2),
			d3_prop_occfcst_PhyCap_perc numeric(8,2),
			d4_prop_occfcst_PhyCap_perc numeric(8,2),
			d5_prop_occfcst_PhyCap_perc numeric(8,2),
			d6_prop_occfcst_PhyCap_perc numeric(8,2),
			d7_prop_occfcst_PhyCap_perc numeric(8,2),
			d8_prop_occfcst_PhyCap_perc numeric(8,2),
			
			d1_trans_sold int,
			d2_trans_sold int,
			d3_trans_sold int,
			d4_trans_sold int,
			d5_trans_sold int,
			d6_trans_sold int,
			d7_trans_sold int,
			d8_trans_sold int,
			d1_trans_occfcst numeric(8,2),
			d2_trans_occfcst numeric(8,2),
			d3_trans_occfcst numeric(8,2),
			d4_trans_occfcst numeric(8,2),
			d5_trans_occfcst numeric(8,2),
			d6_trans_occfcst numeric(8,2),
			d7_trans_occfcst numeric(8,2),
			d8_trans_occfcst numeric(8,2),
			
			d1_grp_sold int,
			d2_grp_sold int,
			d3_grp_sold int,
			d4_grp_sold int,
			d5_grp_sold int,
			d6_grp_sold int,
			d7_grp_sold int,
			d8_grp_sold int,
			d1_grp_occfcst numeric(8,2),
			d2_grp_occfcst numeric(8,2),
			d3_grp_occfcst numeric(8,2),
			d4_grp_occfcst numeric(8,2),
			d5_grp_occfcst numeric(8,2),
			d6_grp_occfcst numeric(8,2),
			d7_grp_occfcst numeric(8,2),
			d8_grp_occfcst numeric(8,2),
						
			d1_fg1_sold int,
			d2_fg1_sold int,
			d3_fg1_sold int,
			d4_fg1_sold int,
			d5_fg1_sold int,
			d6_fg1_sold int,
			d7_fg1_sold int,
			d8_fg1_sold int,
			d1_fg1_occfcst numeric(8,2),
			d2_fg1_occfcst numeric(8,2),
			d3_fg1_occfcst numeric(8,2),
			d4_fg1_occfcst numeric(8,2),
			d5_fg1_occfcst numeric(8,2),
			d6_fg1_occfcst numeric(8,2),
			d7_fg1_occfcst numeric(8,2),
			d8_fg1_occfcst numeric(8,2),
			
			d1_fg2_sold int,
			d2_fg2_sold int,
			d3_fg2_sold int,
			d4_fg2_sold int,
			d5_fg2_sold int,
			d6_fg2_sold int,
			d7_fg2_sold int,
			d8_fg2_sold int,
			d1_fg2_occfcst numeric(8,2),
			d2_fg2_occfcst numeric(8,2),
			d3_fg2_occfcst numeric(8,2),
			d4_fg2_occfcst numeric(8,2),
			d5_fg2_occfcst numeric(8,2),
			d6_fg2_occfcst numeric(8,2),
			d7_fg2_occfcst numeric(8,2),
			d8_fg2_occfcst numeric(8,2),
			
			
			d1_fg3_sold int,
			d2_fg3_sold int,
			d3_fg3_sold int,
			d4_fg3_sold int,
			d5_fg3_sold int,
			d6_fg3_sold int,
			d7_fg3_sold int,
			d8_fg3_sold int,
			d1_fg3_occfcst numeric(8,2),
			d2_fg3_occfcst numeric(8,2),
			d3_fg3_occfcst numeric(8,2),
			d4_fg3_occfcst numeric(8,2),
			d5_fg3_occfcst numeric(8,2),
			d6_fg3_occfcst numeric(8,2),
			d7_fg3_occfcst numeric(8,2),
			d8_fg3_occfcst numeric(8,2),
			
			d1_fg4_sold int,
			d2_fg4_sold int,
			d3_fg4_sold int,
			d4_fg4_sold int,
			d5_fg4_sold int,
			d6_fg4_sold int,
			d7_fg4_sold int,
			d8_fg4_sold int,
			d1_fg4_occfcst numeric(8,2),
			d2_fg4_occfcst numeric(8,2),
			d3_fg4_occfcst numeric(8,2),
			d4_fg4_occfcst numeric(8,2),
			d5_fg4_occfcst numeric(8,2),
			d6_fg4_occfcst numeric(8,2),
			d7_fg4_occfcst numeric(8,2),
			d8_fg4_occfcst numeric(8,2),
	
			d1_fg5_sold int,
			d2_fg5_sold int,
			d3_fg5_sold int,
			d4_fg5_sold int,
			d5_fg5_sold int,
			d6_fg5_sold int,
			d7_fg5_sold int,
			d8_fg5_sold int,
			d1_fg5_occfcst numeric(8,2),
			d2_fg5_occfcst numeric(8,2),
			d3_fg5_occfcst numeric(8,2),
			d4_fg5_occfcst numeric(8,2),
			d5_fg5_occfcst numeric(8,2),
			d6_fg5_occfcst numeric(8,2),
			d7_fg5_occfcst numeric(8,2),
			d8_fg5_occfcst numeric(8,2),
			
			
			d1_fg6_sold int,
			d2_fg6_sold int,
			d3_fg6_sold int,
			d4_fg6_sold int,
			d5_fg6_sold int,
			d6_fg6_sold int,
			d7_fg6_sold int,
			d8_fg6_sold int,
			d1_fg6_occfcst numeric(8,2),
			d2_fg6_occfcst numeric(8,2),
			d3_fg6_occfcst numeric(8,2),
			d4_fg6_occfcst numeric(8,2),
			d5_fg6_occfcst numeric(8,2),
			d6_fg6_occfcst numeric(8,2),
			d7_fg6_occfcst numeric(8,2),
			d8_fg6_occfcst numeric(8,2),
			
			d1_fg7_sold int,
			d2_fg7_sold int,
			d3_fg7_sold int,
			d4_fg7_sold int,
			d5_fg7_sold int,
			d6_fg7_sold int,
			d7_fg7_sold int,
			d8_fg7_sold int,
			d1_fg7_occfcst numeric(8,2),
			d2_fg7_occfcst numeric(8,2),
			d3_fg7_occfcst numeric(8,2),
			d4_fg7_occfcst numeric(8,2),
			d5_fg7_occfcst numeric(8,2),
			d6_fg7_occfcst numeric(8,2),
			d7_fg7_occfcst numeric(8,2),
			d8_fg7_occfcst numeric(8,2),
	
			d1_fg8_sold int,
			d2_fg8_sold int,
			d3_fg8_sold int,
			d4_fg8_sold int,
			d5_fg8_sold int,
			d6_fg8_sold int,
			d7_fg8_sold int,
			d8_fg8_sold int,
			d1_fg8_occfcst numeric(8,2),
			d2_fg8_occfcst numeric(8,2),
			d3_fg8_occfcst numeric(8,2),
			d4_fg8_occfcst numeric(8,2),
			d5_fg8_occfcst numeric(8,2),
			d6_fg8_occfcst numeric(8,2),
			d7_fg8_occfcst numeric(8,2),
			d8_fg8_occfcst numeric(8,2),
			
			d1_fg9_sold int,
			d2_fg9_sold int,
			d3_fg9_sold int,
			d4_fg9_sold int,
			d5_fg9_sold int,
			d6_fg9_sold int,
			d7_fg9_sold int,
			d8_fg9_sold int,
			d1_fg9_occfcst numeric(8,2),
			d2_fg9_occfcst numeric(8,2),
			d3_fg9_occfcst numeric(8,2),
			d4_fg9_occfcst numeric(8,2),
			d5_fg9_occfcst numeric(8,2),
			d6_fg9_occfcst numeric(8,2),
			d7_fg9_occfcst numeric(8,2),
			d8_fg9_occfcst numeric(8,2),
			
			d1_fg10_sold int,
			d2_fg10_sold int,
			d3_fg10_sold int,
			d4_fg10_sold int,
			d5_fg10_sold int,
			d6_fg10_sold int,
			d7_fg10_sold int,
			d8_fg10_sold int,
			d1_fg10_occfcst numeric(8,2),
			d2_fg10_occfcst numeric(8,2),
			d3_fg10_occfcst numeric(8,2),
			d4_fg10_occfcst numeric(8,2),
			d5_fg10_occfcst numeric(8,2),
			d6_fg10_occfcst numeric(8,2),
			d7_fg10_occfcst numeric(8,2),
			d8_fg10_occfcst numeric(8,2),
			
			d1_fg11_sold int,
			d2_fg11_sold int,
			d3_fg11_sold int,
			d4_fg11_sold int,
			d5_fg11_sold int,
			d6_fg11_sold int,
			d7_fg11_sold int,
			d8_fg11_sold int,
			d1_fg11_occfcst numeric(8,2),
			d2_fg11_occfcst numeric(8,2),
			d3_fg11_occfcst numeric(8,2),
			d4_fg11_occfcst numeric(8,2),
			d5_fg11_occfcst numeric(8,2),
			d6_fg11_occfcst numeric(8,2),
			d7_fg11_occfcst numeric(8,2),
			d8_fg11_occfcst numeric(8,2),
			
			d1_fg12_sold int,
			d2_fg12_sold int,
			d3_fg12_sold int,
			d4_fg12_sold int,
			d5_fg12_sold int,
			d6_fg12_sold int,
			d7_fg12_sold int,
			d8_fg12_sold int,
			d1_fg12_occfcst numeric(8,2),
			d2_fg12_occfcst numeric(8,2),
			d3_fg12_occfcst numeric(8,2),
			d4_fg12_occfcst numeric(8,2),
			d5_fg12_occfcst numeric(8,2),
			d6_fg12_occfcst numeric(8,2),
			d7_fg12_occfcst numeric(8,2),
			d8_fg12_occfcst numeric(8,2),
			
			d1_fg13_sold int,
			d2_fg13_sold int,
			d3_fg13_sold int,
			d4_fg13_sold int,
			d5_fg13_sold int,
			d6_fg13_sold int,
			d7_fg13_sold int,
			d8_fg13_sold int,
			d1_fg13_occfcst numeric(8,2),
			d2_fg13_occfcst numeric(8,2),
			d3_fg13_occfcst numeric(8,2),
			d4_fg13_occfcst numeric(8,2),
			d5_fg13_occfcst numeric(8,2),
			d6_fg13_occfcst numeric(8,2),
			d7_fg13_occfcst numeric(8,2),
			d8_fg13_occfcst numeric(8,2),
			
			d1_fg14_sold int,
			d2_fg14_sold int,
			d3_fg14_sold int,
			d4_fg14_sold int,
			d5_fg14_sold int,
			d6_fg14_sold int,
			d7_fg14_sold int,
			d8_fg14_sold int,
			d1_fg14_occfcst numeric(8,2),
			d2_fg14_occfcst numeric(8,2),
			d3_fg14_occfcst numeric(8,2),
			d4_fg14_occfcst numeric(8,2),
			d5_fg14_occfcst numeric(8,2),
			d6_fg14_occfcst numeric(8,2),
			d7_fg14_occfcst numeric(8,2),
			d8_fg14_occfcst numeric(8,2),
			
			d1_fg15_sold int,
			d2_fg15_sold int,
			d3_fg15_sold int,
			d4_fg15_sold int,
			d5_fg15_sold int,
			d6_fg15_sold int,
			d7_fg15_sold int,
			d8_fg15_sold int,
			d1_fg15_occfcst numeric(8,2),
			d2_fg15_occfcst numeric(8,2),
			d3_fg15_occfcst numeric(8,2),
			d4_fg15_occfcst numeric(8,2),
			d5_fg15_occfcst numeric(8,2),
			d6_fg15_occfcst numeric(8,2),
			d7_fg15_occfcst numeric(8,2),
			d8_fg15_occfcst numeric(8,2),
			
			d1_fg16_sold int,
			d2_fg16_sold int,
			d3_fg16_sold int,
			d4_fg16_sold int,
			d5_fg16_sold int,
			d6_fg16_sold int,
			d7_fg16_sold int,
			d8_fg16_sold int,
			d1_fg16_occfcst numeric(8,2),
			d2_fg16_occfcst numeric(8,2),
			d3_fg16_occfcst numeric(8,2),
			d4_fg16_occfcst numeric(8,2),
			d5_fg16_occfcst numeric(8,2),
			d6_fg16_occfcst numeric(8,2),
			d7_fg16_occfcst numeric(8,2),
			d8_fg16_occfcst numeric(8,2),
			
			d1_fg17_sold int,
			d2_fg17_sold int,
			d3_fg17_sold int,
			d4_fg17_sold int,
			d5_fg17_sold int,
			d6_fg17_sold int,
			d7_fg17_sold int,
			d8_fg17_sold int,
			d1_fg17_occfcst numeric(8,2),
			d2_fg17_occfcst numeric(8,2),
			d3_fg17_occfcst numeric(8,2),
			d4_fg17_occfcst numeric(8,2),
			d5_fg17_occfcst numeric(8,2),
			d6_fg17_occfcst numeric(8,2),
			d7_fg17_occfcst numeric(8,2),
			d8_fg17_occfcst numeric(8,2),
			
			d1_fg18_sold int,
			d2_fg18_sold int,
			d3_fg18_sold int,
			d4_fg18_sold int,
			d5_fg18_sold int,
			d6_fg18_sold int,
			d7_fg18_sold int,
			d8_fg18_sold int,
			d1_fg18_occfcst numeric(8,2),
			d2_fg18_occfcst numeric(8,2),
			d3_fg18_occfcst numeric(8,2),
			d4_fg18_occfcst numeric(8,2),
			d5_fg18_occfcst numeric(8,2),
			d6_fg18_occfcst numeric(8,2),
			d7_fg18_occfcst numeric(8,2),
			d8_fg18_occfcst numeric(8,2),
			
			d1_fg19_sold int,
			d2_fg19_sold int,
			d3_fg19_sold int,
			d4_fg19_sold int,
			d5_fg19_sold int,
			d6_fg19_sold int,
			d7_fg19_sold int,
			d8_fg19_sold int,
			d1_fg19_occfcst numeric(8,2),
			d2_fg19_occfcst numeric(8,2),
			d3_fg19_occfcst numeric(8,2),
			d4_fg19_occfcst numeric(8,2),
			d5_fg19_occfcst numeric(8,2),
			d6_fg19_occfcst numeric(8,2),
			d7_fg19_occfcst numeric(8,2),
			d8_fg19_occfcst numeric(8,2),
			
			d1_fg20_sold int,
			d2_fg20_sold int,
			d3_fg20_sold int,
			d4_fg20_sold int,
			d5_fg20_sold int,
			d6_fg20_sold int,
			d7_fg20_sold int,
			d8_fg20_sold int,
			d1_fg20_occfcst numeric(8,2),
			d2_fg20_occfcst numeric(8,2),
			d3_fg20_occfcst numeric(8,2),
			d4_fg20_occfcst numeric(8,2),
			d5_fg20_occfcst numeric(8,2),
			d6_fg20_occfcst numeric(8,2),
			d7_fg20_occfcst numeric(8,2),
			d8_fg20_occfcst numeric(8,2),
			
			d1_fg21_sold int,
			d2_fg21_sold int,
			d3_fg21_sold int,
			d4_fg21_sold int,
			d5_fg21_sold int,
			d6_fg21_sold int,
			d7_fg21_sold int,
			d8_fg21_sold int,
			d1_fg21_occfcst numeric(8,2),
			d2_fg21_occfcst numeric(8,2),
			d3_fg21_occfcst numeric(8,2),
			d4_fg21_occfcst numeric(8,2),
			d5_fg21_occfcst numeric(8,2),
			d6_fg21_occfcst numeric(8,2),
			d7_fg21_occfcst numeric(8,2),
			d8_fg21_occfcst numeric(8,2),
			
			d1_fg22_sold int,
			d2_fg22_sold int,
			d3_fg22_sold int,
			d4_fg22_sold int,
			d5_fg22_sold int,
			d6_fg22_sold int,
			d7_fg22_sold int,
			d8_fg22_sold int,
			d1_fg22_occfcst numeric(8,2),
			d2_fg22_occfcst numeric(8,2),
			d3_fg22_occfcst numeric(8,2),
			d4_fg22_occfcst numeric(8,2),
			d5_fg22_occfcst numeric(8,2),
			d6_fg22_occfcst numeric(8,2),
			d7_fg22_occfcst numeric(8,2),
			d8_fg22_occfcst numeric(8,2),
			
			d1_fg23_sold int,
			d2_fg23_sold int,
			d3_fg23_sold int,
			d4_fg23_sold int,
			d5_fg23_sold int,
			d6_fg23_sold int,
			d7_fg23_sold int,
			d8_fg23_sold int,
			d1_fg23_occfcst numeric(8,2),
			d2_fg23_occfcst numeric(8,2),
			d3_fg23_occfcst numeric(8,2),
			d4_fg23_occfcst numeric(8,2),
			d5_fg23_occfcst numeric(8,2),
			d6_fg23_occfcst numeric(8,2),
			d7_fg23_occfcst numeric(8,2),
			d8_fg23_occfcst numeric(8,2),
			
			d1_fg24_sold int,
			d2_fg24_sold int,
			d3_fg24_sold int,
			d4_fg24_sold int,
			d5_fg24_sold int,
			d6_fg24_sold int,
			d7_fg24_sold int,
			d8_fg24_sold int,
			d1_fg24_occfcst numeric(8,2),
			d2_fg24_occfcst numeric(8,2),
			d3_fg24_occfcst numeric(8,2),
			d4_fg24_occfcst numeric(8,2),
			d5_fg24_occfcst numeric(8,2),
			d6_fg24_occfcst numeric(8,2),
			d7_fg24_occfcst numeric(8,2),
			d8_fg24_occfcst numeric(8,2),
			
			d1_fg25_sold int,
			d2_fg25_sold int,
			d3_fg25_sold int,
			d4_fg25_sold int,
			d5_fg25_sold int,
			d6_fg25_sold int,
			d7_fg25_sold int,
			d8_fg25_sold int,
			d1_fg25_occfcst numeric(8,2),
			d2_fg25_occfcst numeric(8,2),
			d3_fg25_occfcst numeric(8,2),
			d4_fg25_occfcst numeric(8,2),
			d5_fg25_occfcst numeric(8,2),
			d6_fg25_occfcst numeric(8,2),
			d7_fg25_occfcst numeric(8,2),
			d8_fg25_occfcst numeric(8,2),

			d1_bv1_sold int,
			d2_bv1_sold int,
			d3_bv1_sold int,
			d4_bv1_sold int,
			d5_bv1_sold int,
			d6_bv1_sold int,
			d7_bv1_sold int,
			d8_bv1_sold int,
			d1_bv1_occfcst numeric(8,2),
			d2_bv1_occfcst numeric(8,2),
			d3_bv1_occfcst numeric(8,2),
			d4_bv1_occfcst numeric(8,2),
			d5_bv1_occfcst numeric(8,2),
			d6_bv1_occfcst numeric(8,2),
			d7_bv1_occfcst numeric(8,2),
			d8_bv1_occfcst numeric(8,2),
			
			d1_bv2_sold int,
			d2_bv2_sold int,
			d3_bv2_sold int,
			d4_bv2_sold int,
			d5_bv2_sold int,
			d6_bv2_sold int,
			d7_bv2_sold int,
			d8_bv2_sold int,
			d1_bv2_occfcst numeric(8,2),
			d2_bv2_occfcst numeric(8,2),
			d3_bv2_occfcst numeric(8,2),
			d4_bv2_occfcst numeric(8,2),
			d5_bv2_occfcst numeric(8,2),
			d6_bv2_occfcst numeric(8,2),
			d7_bv2_occfcst numeric(8,2),
			d8_bv2_occfcst numeric(8,2),
			
			
			d1_bv3_sold int,
			d2_bv3_sold int,
			d3_bv3_sold int,
			d4_bv3_sold int,
			d5_bv3_sold int,
			d6_bv3_sold int,
			d7_bv3_sold int,
			d8_bv3_sold int,
			d1_bv3_occfcst numeric(8,2),
			d2_bv3_occfcst numeric(8,2),
			d3_bv3_occfcst numeric(8,2),
			d4_bv3_occfcst numeric(8,2),
			d5_bv3_occfcst numeric(8,2),
			d6_bv3_occfcst numeric(8,2),
			d7_bv3_occfcst numeric(8,2),
			d8_bv3_occfcst numeric(8,2),
			
			d1_bv4_sold int,
			d2_bv4_sold int,
			d3_bv4_sold int,
			d4_bv4_sold int,
			d5_bv4_sold int,
			d6_bv4_sold int,
			d7_bv4_sold int,
			d8_bv4_sold int,
			d1_bv4_occfcst numeric(8,2),
			d2_bv4_occfcst numeric(8,2),
			d3_bv4_occfcst numeric(8,2),
			d4_bv4_occfcst numeric(8,2),
			d5_bv4_occfcst numeric(8,2),
			d6_bv4_occfcst numeric(8,2),
			d7_bv4_occfcst numeric(8,2),
			d8_bv4_occfcst numeric(8,2),
	
			d1_bv5_sold int,
			d2_bv5_sold int,
			d3_bv5_sold int,
			d4_bv5_sold int,
			d5_bv5_sold int,
			d6_bv5_sold int,
			d7_bv5_sold int,
			d8_bv5_sold int,
			d1_bv5_occfcst numeric(8,2),
			d2_bv5_occfcst numeric(8,2),
			d3_bv5_occfcst numeric(8,2),
			d4_bv5_occfcst numeric(8,2),
			d5_bv5_occfcst numeric(8,2),
			d6_bv5_occfcst numeric(8,2),
			d7_bv5_occfcst numeric(8,2),
			d8_bv5_occfcst numeric(8,2),
			
			
			d1_bv6_sold int,
			d2_bv6_sold int,
			d3_bv6_sold int,
			d4_bv6_sold int,
			d5_bv6_sold int,
			d6_bv6_sold int,
			d7_bv6_sold int,
			d8_bv6_sold int,
			d1_bv6_occfcst numeric(8,2),
			d2_bv6_occfcst numeric(8,2),
			d3_bv6_occfcst numeric(8,2),
			d4_bv6_occfcst numeric(8,2),
			d5_bv6_occfcst numeric(8,2),
			d6_bv6_occfcst numeric(8,2),
			d7_bv6_occfcst numeric(8,2),
			d8_bv6_occfcst numeric(8,2),
			
			d1_bv7_sold int,
			d2_bv7_sold int,
			d3_bv7_sold int,
			d4_bv7_sold int,
			d5_bv7_sold int,
			d6_bv7_sold int,
			d7_bv7_sold int,
			d8_bv7_sold int,
			d1_bv7_occfcst numeric(8,2),
			d2_bv7_occfcst numeric(8,2),
			d3_bv7_occfcst numeric(8,2),
			d4_bv7_occfcst numeric(8,2),
			d5_bv7_occfcst numeric(8,2),
			d6_bv7_occfcst numeric(8,2),
			d7_bv7_occfcst numeric(8,2),
			d8_bv7_occfcst numeric(8,2),
	
			d1_bv8_sold int,
			d2_bv8_sold int,
			d3_bv8_sold int,
			d4_bv8_sold int,
			d5_bv8_sold int,
			d6_bv8_sold int,
			d7_bv8_sold int,
			d8_bv8_sold int,
			d1_bv8_occfcst numeric(8,2),
			d2_bv8_occfcst numeric(8,2),
			d3_bv8_occfcst numeric(8,2),
			d4_bv8_occfcst numeric(8,2),
			d5_bv8_occfcst numeric(8,2),
			d6_bv8_occfcst numeric(8,2),
			d7_bv8_occfcst numeric(8,2),
			d8_bv8_occfcst numeric(8,2),
			
			d1_bv9_sold int,
			d2_bv9_sold int,
			d3_bv9_sold int,
			d4_bv9_sold int,
			d5_bv9_sold int,
			d6_bv9_sold int,
			d7_bv9_sold int,
			d8_bv9_sold int,
			d1_bv9_occfcst numeric(8,2),
			d2_bv9_occfcst numeric(8,2),
			d3_bv9_occfcst numeric(8,2),
			d4_bv9_occfcst numeric(8,2),
			d5_bv9_occfcst numeric(8,2),
			d6_bv9_occfcst numeric(8,2),
			d7_bv9_occfcst numeric(8,2),
			d8_bv9_occfcst numeric(8,2),
			
			d1_bv10_sold int,
			d2_bv10_sold int,
			d3_bv10_sold int,
			d4_bv10_sold int,
			d5_bv10_sold int,
			d6_bv10_sold int,
			d7_bv10_sold int,
			d8_bv10_sold int,
			d1_bv10_occfcst numeric(8,2),
			d2_bv10_occfcst numeric(8,2),
			d3_bv10_occfcst numeric(8,2),
			d4_bv10_occfcst numeric(8,2),
			d5_bv10_occfcst numeric(8,2),
			d6_bv10_occfcst numeric(8,2),
			d7_bv10_occfcst numeric(8,2),
			d8_bv10_occfcst numeric(8,2),
			
			d1_bv11_sold int,
			d2_bv11_sold int,
			d3_bv11_sold int,
			d4_bv11_sold int,
			d5_bv11_sold int,
			d6_bv11_sold int,
			d7_bv11_sold int,
			d8_bv11_sold int,
			d1_bv11_occfcst numeric(8,2),
			d2_bv11_occfcst numeric(8,2),
			d3_bv11_occfcst numeric(8,2),
			d4_bv11_occfcst numeric(8,2),
			d5_bv11_occfcst numeric(8,2),
			d6_bv11_occfcst numeric(8,2),
			d7_bv11_occfcst numeric(8,2),
			d8_bv11_occfcst numeric(8,2),
			
			d1_bv12_sold int,
			d2_bv12_sold int,
			d3_bv12_sold int,
			d4_bv12_sold int,
			d5_bv12_sold int,
			d6_bv12_sold int,
			d7_bv12_sold int,
			d8_bv12_sold int,
			d1_bv12_occfcst numeric(8,2),
			d2_bv12_occfcst numeric(8,2),
			d3_bv12_occfcst numeric(8,2),
			d4_bv12_occfcst numeric(8,2),
			d5_bv12_occfcst numeric(8,2),
			d6_bv12_occfcst numeric(8,2),
			d7_bv12_occfcst numeric(8,2),
			d8_bv12_occfcst numeric(8,2),

			d1_rc1_sold int,
			d2_rc1_sold int,
			d3_rc1_sold int,
			d4_rc1_sold int,
			d5_rc1_sold int,
			d6_rc1_sold int,
			d7_rc1_sold int,
			d8_rc1_sold int,
			d1_rc1_occfcst numeric(8,2),
			d2_rc1_occfcst numeric(8,2),
			d3_rc1_occfcst numeric(8,2),
			d4_rc1_occfcst numeric(8,2),
			d5_rc1_occfcst numeric(8,2),
			d6_rc1_occfcst numeric(8,2),
			d7_rc1_occfcst numeric(8,2),
			d8_rc1_occfcst numeric(8,2),
			d1_rc1_lrv numeric(19,5),
			d2_rc1_lrv numeric(19,5),
			d3_rc1_lrv numeric(19,5),
			d4_rc1_lrv numeric(19,5),
			d5_rc1_lrv numeric(19,5),
			d6_rc1_lrv numeric(19,5),
			d7_rc1_lrv numeric(19,5),
			d8_rc1_lrv numeric(19,5),
			
			d1_rc2_sold int,
			d2_rc2_sold int,
			d3_rc2_sold int,
			d4_rc2_sold int,
			d5_rc2_sold int,
			d6_rc2_sold int,
			d7_rc2_sold int,
			d8_rc2_sold int,
			d1_rc2_occfcst numeric(8,2),
			d2_rc2_occfcst numeric(8,2),
			d3_rc2_occfcst numeric(8,2),
			d4_rc2_occfcst numeric(8,2),
			d5_rc2_occfcst numeric(8,2),
			d6_rc2_occfcst numeric(8,2),
			d7_rc2_occfcst numeric(8,2),
			d8_rc2_occfcst numeric(8,2),
			d1_rc2_lrv numeric(19,5),
			d2_rc2_lrv numeric(19,5),
			d3_rc2_lrv numeric(19,5),
			d4_rc2_lrv numeric(19,5),
			d5_rc2_lrv numeric(19,5),
			d6_rc2_lrv numeric(19,5),
			d7_rc2_lrv numeric(19,5),
			d8_rc2_lrv numeric(19,5),
			
			
			d1_rc3_sold int,
			d2_rc3_sold int,
			d3_rc3_sold int,
			d4_rc3_sold int,
			d5_rc3_sold int,
			d6_rc3_sold int,
			d7_rc3_sold int,
			d8_rc3_sold int,
			d1_rc3_occfcst numeric(8,2),
			d2_rc3_occfcst numeric(8,2),
			d3_rc3_occfcst numeric(8,2),
			d4_rc3_occfcst numeric(8,2),
			d5_rc3_occfcst numeric(8,2),
			d6_rc3_occfcst numeric(8,2),
			d7_rc3_occfcst numeric(8,2),
			d8_rc3_occfcst numeric(8,2),
			d1_rc3_lrv numeric(19,5),
			d2_rc3_lrv numeric(19,5),
			d3_rc3_lrv numeric(19,5),
			d4_rc3_lrv numeric(19,5),
			d5_rc3_lrv numeric(19,5),
			d6_rc3_lrv numeric(19,5),
			d7_rc3_lrv numeric(19,5),
			d8_rc3_lrv numeric(19,5),
			
			d1_rc4_sold int,
			d2_rc4_sold int,
			d3_rc4_sold int,
			d4_rc4_sold int,
			d5_rc4_sold int,
			d6_rc4_sold int,
			d7_rc4_sold int,
			d8_rc4_sold int,
			d1_rc4_occfcst numeric(8,2),
			d2_rc4_occfcst numeric(8,2),
			d3_rc4_occfcst numeric(8,2),
			d4_rc4_occfcst numeric(8,2),
			d5_rc4_occfcst numeric(8,2),
			d6_rc4_occfcst numeric(8,2),
			d7_rc4_occfcst numeric(8,2),
			d8_rc4_occfcst numeric(8,2),
			d1_rc4_lrv numeric(19,5),
			d2_rc4_lrv numeric(19,5),
			d3_rc4_lrv numeric(19,5),
			d4_rc4_lrv numeric(19,5),
			d5_rc4_lrv numeric(19,5),
			d6_rc4_lrv numeric(19,5),
			d7_rc4_lrv numeric(19,5),
			d8_rc4_lrv numeric(19,5),
	
			d1_rc5_sold int,
			d2_rc5_sold int,
			d3_rc5_sold int,
			d4_rc5_sold int,
			d5_rc5_sold int,
			d6_rc5_sold int,
			d7_rc5_sold int,
			d8_rc5_sold int,
			d1_rc5_occfcst numeric(8,2),
			d2_rc5_occfcst numeric(8,2),
			d3_rc5_occfcst numeric(8,2),
			d4_rc5_occfcst numeric(8,2),
			d5_rc5_occfcst numeric(8,2),
			d6_rc5_occfcst numeric(8,2),
			d7_rc5_occfcst numeric(8,2),
			d8_rc5_occfcst numeric(8,2),
			d1_rc5_lrv numeric(19,5),
			d2_rc5_lrv numeric(19,5),
			d3_rc5_lrv numeric(19,5),
			d4_rc5_lrv numeric(19,5),
			d5_rc5_lrv numeric(19,5),
			d6_rc5_lrv numeric(19,5),
			d7_rc5_lrv numeric(19,5),
			d8_rc5_lrv numeric(19,5),
			
			
			d1_rc6_sold int,
			d2_rc6_sold int,
			d3_rc6_sold int,
			d4_rc6_sold int,
			d5_rc6_sold int,
			d6_rc6_sold int,
			d7_rc6_sold int,
			d8_rc6_sold int,
			d1_rc6_occfcst numeric(8,2),
			d2_rc6_occfcst numeric(8,2),
			d3_rc6_occfcst numeric(8,2),
			d4_rc6_occfcst numeric(8,2),
			d5_rc6_occfcst numeric(8,2),
			d6_rc6_occfcst numeric(8,2),
			d7_rc6_occfcst numeric(8,2),
			d8_rc6_occfcst numeric(8,2),
			d1_rc6_lrv numeric(19,5),
			d2_rc6_lrv numeric(19,5),
			d3_rc6_lrv numeric(19,5),
			d4_rc6_lrv numeric(19,5),
			d5_rc6_lrv numeric(19,5),
			d6_rc6_lrv numeric(19,5),
			d7_rc6_lrv numeric(19,5),
			d8_rc6_lrv numeric(19,5),
			
			d1_rc7_sold int,
			d2_rc7_sold int,
			d3_rc7_sold int,
			d4_rc7_sold int,
			d5_rc7_sold int,
			d6_rc7_sold int,
			d7_rc7_sold int,
			d8_rc7_sold int,
			d1_rc7_occfcst numeric(8,2),
			d2_rc7_occfcst numeric(8,2),
			d3_rc7_occfcst numeric(8,2),
			d4_rc7_occfcst numeric(8,2),
			d5_rc7_occfcst numeric(8,2),
			d6_rc7_occfcst numeric(8,2),
			d7_rc7_occfcst numeric(8,2),
			d8_rc7_occfcst numeric(8,2),
			d1_rc7_lrv numeric(19,5),
			d2_rc7_lrv numeric(19,5),
			d3_rc7_lrv numeric(19,5),
			d4_rc7_lrv numeric(19,5),
			d5_rc7_lrv numeric(19,5),
			d6_rc7_lrv numeric(19,5),
			d7_rc7_lrv numeric(19,5),
			d8_rc7_lrv numeric(19,5),
	
			d1_rc8_sold int,
			d2_rc8_sold int,
			d3_rc8_sold int,
			d4_rc8_sold int,
			d5_rc8_sold int,
			d6_rc8_sold int,
			d7_rc8_sold int,
			d8_rc8_sold int,
			d1_rc8_occfcst numeric(8,2),
			d2_rc8_occfcst numeric(8,2),
			d3_rc8_occfcst numeric(8,2),
			d4_rc8_occfcst numeric(8,2),
			d5_rc8_occfcst numeric(8,2),
			d6_rc8_occfcst numeric(8,2),
			d7_rc8_occfcst numeric(8,2),
			d8_rc8_occfcst numeric(8,2),
			d1_rc8_lrv numeric(19,5),
			d2_rc8_lrv numeric(19,5),
			d3_rc8_lrv numeric(19,5),
			d4_rc8_lrv numeric(19,5),
			d5_rc8_lrv numeric(19,5),
			d6_rc8_lrv numeric(19,5),
			d7_rc8_lrv numeric(19,5),
			d8_rc8_lrv numeric(19,5),

			d1_rc9_sold int,
			d2_rc9_sold int,
			d3_rc9_sold int,
			d4_rc9_sold int,
			d5_rc9_sold int,
			d6_rc9_sold int,
			d7_rc9_sold int,
			d8_rc9_sold int,
			d1_rc9_occfcst numeric(8,2),
			d2_rc9_occfcst numeric(8,2),
			d3_rc9_occfcst numeric(8,2),
			d4_rc9_occfcst numeric(8,2),
			d5_rc9_occfcst numeric(8,2),
			d6_rc9_occfcst numeric(8,2),
			d7_rc9_occfcst numeric(8,2),
			d8_rc9_occfcst numeric(8,2),
			d1_rc9_lrv numeric(19,5),
			d2_rc9_lrv numeric(19,5),
			d3_rc9_lrv numeric(19,5),
			d4_rc9_lrv numeric(19,5),
			d5_rc9_lrv numeric(19,5),
			d6_rc9_lrv numeric(19,5),
			d7_rc9_lrv numeric(19,5),
			d8_rc9_lrv numeric(19,5),
			
			d1_rc10_sold int,
			d2_rc10_sold int,
			d3_rc10_sold int,
			d4_rc10_sold int,
			d5_rc10_sold int,
			d6_rc10_sold int,
			d7_rc10_sold int,
			d8_rc10_sold int,
			d1_rc10_occfcst numeric(8,2),
			d2_rc10_occfcst numeric(8,2),
			d3_rc10_occfcst numeric(8,2),
			d4_rc10_occfcst numeric(8,2),
			d5_rc10_occfcst numeric(8,2),
			d6_rc10_occfcst numeric(8,2),
			d7_rc10_occfcst numeric(8,2),
			d8_rc10_occfcst numeric(8,2),
			d1_rc10_lrv numeric(19,5),
			d2_rc10_lrv numeric(19,5),
			d3_rc10_lrv numeric(19,5),
			d4_rc10_lrv numeric(19,5),
			d5_rc10_lrv numeric(19,5),
			d6_rc10_lrv numeric(19,5),
			d7_rc10_lrv numeric(19,5),
			d8_rc10_lrv numeric(19,5),				
			
			d1_rc11_sold int,
			d2_rc11_sold int,
			d3_rc11_sold int,
			d4_rc11_sold int,
			d5_rc11_sold int,
			d6_rc11_sold int,
			d7_rc11_sold int,
			d8_rc11_sold int,
			d1_rc11_occfcst numeric(8,2),
			d2_rc11_occfcst numeric(8,2),
			d3_rc11_occfcst numeric(8,2),
			d4_rc11_occfcst numeric(8,2),
			d5_rc11_occfcst numeric(8,2),
			d6_rc11_occfcst numeric(8,2),
			d7_rc11_occfcst numeric(8,2),
			d8_rc11_occfcst numeric(8,2),
			d1_rc11_lrv numeric(19,5),
			d2_rc11_lrv numeric(19,5),
			d3_rc11_lrv numeric(19,5),
			d4_rc11_lrv numeric(19,5),
			d5_rc11_lrv numeric(19,5),
			d6_rc11_lrv numeric(19,5),
			d7_rc11_lrv numeric(19,5),
			d8_rc11_lrv numeric(19,5),				
			
			d1_rc12_sold int,
			d2_rc12_sold int,
			d3_rc12_sold int,
			d4_rc12_sold int,
			d5_rc12_sold int,
			d6_rc12_sold int,
			d7_rc12_sold int,
			d8_rc12_sold int,
			d1_rc12_occfcst numeric(8,2),
			d2_rc12_occfcst numeric(8,2),
			d3_rc12_occfcst numeric(8,2),
			d4_rc12_occfcst numeric(8,2),
			d5_rc12_occfcst numeric(8,2),
			d6_rc12_occfcst numeric(8,2),
			d7_rc12_occfcst numeric(8,2),
			d8_rc12_occfcst numeric(8,2),
			d1_rc12_lrv numeric(19,5),
			d2_rc12_lrv numeric(19,5),
			d3_rc12_lrv numeric(19,5),
			d4_rc12_lrv numeric(19,5),
			d5_rc12_lrv numeric(19,5),
			d6_rc12_lrv numeric(19,5),
			d7_rc12_lrv numeric(19,5),
			d8_rc12_lrv numeric(19,5)
			
									
	)
as

	begin
		
		declare	@transient_type varchar(10)
		select @transient_type = case @isTransient when 1 then 'transient' else 'none' end

		declare	@group_type varchar(10)
		select @group_type = case @isGroup when 1 then 'group' else 'none' end

		insert into @comparative_pace
		select 
			base.daystoArrival,
			hotel.d1_sold d1_prop_sold,
			hotel.d2_sold d2_prop_sold,
			hotel.d3_sold d3_prop_sold,
			hotel.d4_sold d4_prop_sold,
			hotel.d5_sold d5_prop_sold,
			hotel.d6_sold d6_prop_sold,
			hotel.d7_sold d7_prop_sold,
			hotel.d8_sold d8_prop_sold,
			hotel.d1_lrv d1_prop_lrv,
			hotel.d2_lrv d2_prop_lrv,
			hotel.d3_lrv d3_prop_lrv,
			hotel.d4_lrv d4_prop_lrv,
			hotel.d5_lrv d5_prop_lrv,
			hotel.d6_lrv d6_prop_lrv,
			hotel.d7_lrv d7_prop_lrv,
			hotel.d8_lrv d8_prop_lrv,
			hotel.d1_occupancy_NBR d1_prop_occfcst,
			hotel.d2_occupancy_NBR d2_prop_occfcst,
			hotel.d3_occupancy_NBR d3_prop_occfcst,
			hotel.d4_occupancy_NBR d4_prop_occfcst,
			hotel.d5_occupancy_NBR d5_prop_occfcst,
			hotel.d6_occupancy_NBR d6_prop_occfcst,
			hotel.d7_occupancy_NBR d7_prop_occfcst,
			hotel.d8_occupancy_NBR d8_prop_occfcst,
			hotel.d1_occupancy_perc d1_prop_occfcst_perc,
			hotel.d2_occupancy_perc d2_prop_occfcst_perc,
			hotel.d3_occupancy_perc d3_prop_occfcst_perc,
			hotel.d4_occupancy_perc d4_prop_occfcst_perc,
			hotel.d5_occupancy_perc d5_prop_occfcst_perc,
			hotel.d6_occupancy_perc d6_prop_occfcst_perc,
			hotel.d7_occupancy_perc d7_prop_occfcst_perc,
			hotel.d8_occupancy_perc d8_prop_occfcst_perc,
			hotel.d1_occupancy_PhyCap_perc d1_prop_occfcst_PhyCap_perc,
			hotel.d2_occupancy_PhyCap_perc d2_prop_occfcst_PhyCap_perc,
			hotel.d3_occupancy_PhyCap_perc d3_prop_occfcst_PhyCap_perc,
			hotel.d4_occupancy_PhyCap_perc d4_prop_occfcst_PhyCap_perc,
			hotel.d5_occupancy_PhyCap_perc d5_prop_occfcst_PhyCap_perc,
			hotel.d6_occupancy_PhyCap_perc d6_prop_occfcst_PhyCap_perc,
			hotel.d7_occupancy_PhyCap_perc d7_prop_occfcst_PhyCap_perc,
			hotel.d8_occupancy_PhyCap_perc d8_prop_occfcst_PhyCap_perc,
			
			trans.d1_sold d1_trans_sold,
			trans.d2_sold d2_trans_sold,
			trans.d3_sold d3_trans_sold,
			trans.d4_sold d4_trans_sold,
			trans.d5_sold d5_trans_sold,
			trans.d6_sold d6_trans_sold,
			trans.d7_sold d7_trans_sold,
			trans.d8_sold d8_trans_sold,
			trans.d1_occupancy_NBR d1_trans_occfcst,
			trans.d2_occupancy_NBR d2_trans_occfcst,
			trans.d3_occupancy_NBR d3_trans_occfcst,
			trans.d4_occupancy_NBR d4_trans_occfcst,
			trans.d5_occupancy_NBR d5_trans_occfcst,
			trans.d6_occupancy_NBR d6_trans_occfcst,
			trans.d7_occupancy_NBR d7_trans_occfcst,
			trans.d8_occupancy_NBR d8_trans_occfcst,
			
			grp.d1_sold d1_grp_sold,
			grp.d2_sold d2_grp_sold,
			grp.d3_sold d3_grp_sold,
			grp.d4_sold d4_grp_sold,
			grp.d5_sold d5_grp_sold,
			grp.d6_sold d6_grp_sold,
			grp.d7_sold d7_grp_sold,
			grp.d8_sold d8_grp_sold,
			grp.d1_occupancy_NBR d1_grp_occfcst,
			grp.d2_occupancy_NBR d2_grp_occfcst,
			grp.d3_occupancy_NBR d3_grp_occfcst,
			grp.d4_occupancy_NBR d4_grp_occfcst,
			grp.d5_occupancy_NBR d5_grp_occfcst,
			grp.d6_occupancy_NBR d6_grp_occfcst,
			grp.d7_occupancy_NBR d7_grp_occfcst,
			grp.d8_occupancy_NBR d8_grp_occfcst,
						
			fg1.d1_sold d1_fg1_sold,
			fg1.d2_sold d2_fg1_sold,
			fg1.d3_sold d3_fg1_sold,
			fg1.d4_sold d4_fg1_sold,
			fg1.d5_sold d5_fg1_sold,
			fg1.d6_sold d6_fg1_sold,
			fg1.d7_sold d7_fg1_sold,
			fg1.d8_sold d8_fg1_sold,
			fg1.d1_occupancy_NBR d1_fg1_occfcst,
			fg1.d2_occupancy_NBR d2_fg1_occfcst,
			fg1.d3_occupancy_NBR d3_fg1_occfcst,
			fg1.d4_occupancy_NBR d4_fg1_occfcst,
			fg1.d5_occupancy_NBR d5_fg1_occfcst,
			fg1.d6_occupancy_NBR d6_fg1_occfcst,
			fg1.d7_occupancy_NBR d7_fg1_occfcst,
			fg1.d8_occupancy_NBR d8_fg1_occfcst,
			

			fg2.d1_sold d1_fg2_sold,
			fg2.d2_sold d2_fg2_sold,
			fg2.d3_sold d3_fg2_sold,
			fg2.d4_sold d4_fg2_sold,
			fg2.d5_sold d5_fg2_sold,
			fg2.d6_sold d6_fg2_sold,
			fg2.d7_sold d7_fg2_sold,
			fg2.d8_sold d8_fg2_sold,
			fg2.d1_occupancy_NBR d1_fg2_occfcst,
			fg2.d2_occupancy_NBR d2_fg2_occfcst,
			fg2.d3_occupancy_NBR d3_fg2_occfcst,
			fg2.d4_occupancy_NBR d4_fg2_occfcst,
			fg2.d5_occupancy_NBR d5_fg2_occfcst,
			fg2.d6_occupancy_NBR d6_fg2_occfcst,
			fg2.d7_occupancy_NBR d7_fg2_occfcst,
			fg2.d8_occupancy_NBR d8_fg2_occfcst,


			fg3.d1_sold d1_fg3_sold,
			fg3.d2_sold d2_fg3_sold,
			fg3.d3_sold d3_fg3_sold,
			fg3.d4_sold d4_fg3_sold,
			fg3.d5_sold d5_fg3_sold,
			fg3.d6_sold d6_fg3_sold,
			fg3.d7_sold d7_fg3_sold,
			fg3.d8_sold d8_fg3_sold,
			fg3.d1_occupancy_NBR d1_fg3_occfcst,
			fg3.d2_occupancy_NBR d2_fg3_occfcst,
			fg3.d3_occupancy_NBR d3_fg3_occfcst,
			fg3.d4_occupancy_NBR d4_fg3_occfcst,
			fg3.d5_occupancy_NBR d5_fg3_occfcst,
			fg3.d6_occupancy_NBR d6_fg3_occfcst,
			fg3.d7_occupancy_NBR d7_fg3_occfcst,
			fg3.d8_occupancy_NBR d8_fg3_occfcst,
			
			fg4.d1_sold d1_fg4_sold,
			fg4.d2_sold d2_fg4_sold,
			fg4.d3_sold d3_fg4_sold,
			fg4.d4_sold d4_fg4_sold,
			fg4.d5_sold d5_fg4_sold,
			fg4.d6_sold d6_fg4_sold,
			fg4.d7_sold d7_fg4_sold,
			fg4.d8_sold d8_fg4_sold,
			fg4.d1_occupancy_NBR d1_fg4_occfcst,
			fg4.d2_occupancy_NBR d2_fg4_occfcst,
			fg4.d3_occupancy_NBR d3_fg4_occfcst,
			fg4.d4_occupancy_NBR d4_fg4_occfcst,
			fg4.d5_occupancy_NBR d5_fg4_occfcst,
			fg4.d6_occupancy_NBR d6_fg4_occfcst,
			fg4.d7_occupancy_NBR d7_fg4_occfcst,
			fg4.d8_occupancy_NBR d8_fg4_occfcst,

			fg5.d1_sold d1_fg5_sold,
			fg5.d2_sold d2_fg5_sold,
			fg5.d3_sold d3_fg5_sold,
			fg5.d4_sold d4_fg5_sold,
			fg5.d5_sold d5_fg5_sold,
			fg5.d6_sold d6_fg5_sold,
			fg5.d7_sold d7_fg5_sold,
			fg5.d8_sold d8_fg5_sold,
			fg5.d1_occupancy_NBR d1_fg5_occfcst,
			fg5.d2_occupancy_NBR d2_fg5_occfcst,
			fg5.d3_occupancy_NBR d3_fg5_occfcst,
			fg5.d4_occupancy_NBR d4_fg5_occfcst,
			fg5.d5_occupancy_NBR d5_fg5_occfcst,
			fg5.d6_occupancy_NBR d6_fg5_occfcst,
			fg5.d7_occupancy_NBR d7_fg5_occfcst,
			fg5.d8_occupancy_NBR d8_fg5_occfcst,

			fg6.d1_sold d1_fg6_sold,
			fg6.d2_sold d2_fg6_sold,
			fg6.d3_sold d3_fg6_sold,
			fg6.d4_sold d4_fg6_sold,
			fg6.d5_sold d5_fg6_sold,
			fg6.d6_sold d6_fg6_sold,
			fg6.d7_sold d7_fg6_sold,
			fg6.d8_sold d8_fg6_sold,
			fg6.d1_occupancy_NBR d1_fg6_occfcst,
			fg6.d2_occupancy_NBR d2_fg6_occfcst,
			fg6.d3_occupancy_NBR d3_fg6_occfcst,
			fg6.d4_occupancy_NBR d4_fg6_occfcst,
			fg6.d5_occupancy_NBR d5_fg6_occfcst,
			fg6.d6_occupancy_NBR d6_fg6_occfcst,
			fg6.d7_occupancy_NBR d7_fg6_occfcst,
			fg6.d8_occupancy_NBR d8_fg6_occfcst,

			fg7.d1_sold d1_fg7_sold,
			fg7.d2_sold d2_fg7_sold,
			fg7.d3_sold d3_fg7_sold,
			fg7.d4_sold d4_fg7_sold,
			fg7.d5_sold d5_fg7_sold,
			fg7.d6_sold d6_fg7_sold,
			fg7.d7_sold d7_fg7_sold,
			fg7.d8_sold d8_fg7_sold,
			fg7.d1_occupancy_NBR d1_fg7_occfcst,
			fg7.d2_occupancy_NBR d2_fg7_occfcst,
			fg7.d3_occupancy_NBR d3_fg7_occfcst,
			fg7.d4_occupancy_NBR d4_fg7_occfcst,
			fg7.d5_occupancy_NBR d5_fg7_occfcst,
			fg7.d6_occupancy_NBR d6_fg7_occfcst,
			fg7.d7_occupancy_NBR d7_fg7_occfcst,
			fg7.d8_occupancy_NBR d8_fg7_occfcst,

			fg8.d1_sold d1_fg8_sold,
			fg8.d2_sold d2_fg8_sold,
			fg8.d3_sold d3_fg8_sold,
			fg8.d4_sold d4_fg8_sold,
			fg8.d5_sold d5_fg8_sold,
			fg8.d6_sold d6_fg8_sold,
			fg8.d7_sold d7_fg8_sold,
			fg8.d8_sold d8_fg8_sold,
			fg8.d1_occupancy_NBR d1_fg8_occfcst,
			fg8.d2_occupancy_NBR d2_fg8_occfcst,
			fg8.d3_occupancy_NBR d3_fg8_occfcst,
			fg8.d4_occupancy_NBR d4_fg8_occfcst,
			fg8.d5_occupancy_NBR d5_fg8_occfcst,
			fg8.d6_occupancy_NBR d6_fg8_occfcst,
			fg8.d7_occupancy_NBR d7_fg8_occfcst,
			fg8.d8_occupancy_NBR d8_fg8_occfcst,

			fg9.d1_sold d1_fg9_sold,
			fg9.d2_sold d2_fg9_sold,
			fg9.d3_sold d3_fg9_sold,
			fg9.d4_sold d4_fg9_sold,
			fg9.d5_sold d5_fg9_sold,
			fg9.d6_sold d6_fg9_sold,
			fg9.d7_sold d7_fg9_sold,
			fg9.d8_sold d8_fg9_sold,
			fg9.d1_occupancy_NBR d1_fg9_occfcst,
			fg9.d2_occupancy_NBR d2_fg9_occfcst,
			fg9.d3_occupancy_NBR d3_fg9_occfcst,
			fg9.d4_occupancy_NBR d4_fg9_occfcst,
			fg9.d5_occupancy_NBR d5_fg9_occfcst,
			fg9.d6_occupancy_NBR d6_fg9_occfcst,
			fg9.d7_occupancy_NBR d7_fg9_occfcst,
			fg9.d8_occupancy_NBR d8_fg9_occfcst,
			
			fg10.d1_sold d1_fg10_sold,
			fg10.d2_sold d2_fg10_sold,
			fg10.d3_sold d3_fg10_sold,
			fg10.d4_sold d4_fg10_sold,
			fg10.d5_sold d5_fg10_sold,
			fg10.d6_sold d6_fg10_sold,
			fg10.d7_sold d7_fg10_sold,
			fg10.d8_sold d8_fg10_sold,
			fg10.d1_occupancy_NBR d1_fg10_occfcst,
			fg10.d2_occupancy_NBR d2_fg10_occfcst,
			fg10.d3_occupancy_NBR d3_fg10_occfcst,
			fg10.d4_occupancy_NBR d4_fg10_occfcst,
			fg10.d5_occupancy_NBR d5_fg10_occfcst,
			fg10.d6_occupancy_NBR d6_fg10_occfcst,
			fg10.d7_occupancy_NBR d7_fg10_occfcst,
			fg10.d8_occupancy_NBR d8_fg10_occfcst,
			
			fg11.d1_sold d1_fg11_sold,
			fg11.d2_sold d2_fg11_sold,
			fg11.d3_sold d3_fg11_sold,
			fg11.d4_sold d4_fg11_sold,
			fg11.d5_sold d5_fg11_sold,
			fg11.d6_sold d6_fg11_sold,
			fg11.d7_sold d7_fg11_sold,
			fg11.d8_sold d8_fg11_sold,
			fg11.d1_occupancy_NBR d1_fg11_occfcst,
			fg11.d2_occupancy_NBR d2_fg11_occfcst,
			fg11.d3_occupancy_NBR d3_fg11_occfcst,
			fg11.d4_occupancy_NBR d4_fg11_occfcst,
			fg11.d5_occupancy_NBR d5_fg11_occfcst,
			fg11.d6_occupancy_NBR d6_fg11_occfcst,
			fg11.d7_occupancy_NBR d7_fg11_occfcst,
			fg11.d8_occupancy_NBR d8_fg11_occfcst,
			
			fg12.d1_sold d1_fg12_sold,
			fg12.d2_sold d2_fg12_sold,
			fg12.d3_sold d3_fg12_sold,
			fg12.d4_sold d4_fg12_sold,
			fg12.d5_sold d5_fg12_sold,
			fg12.d6_sold d6_fg12_sold,
			fg12.d7_sold d7_fg12_sold,
			fg12.d8_sold d8_fg12_sold,
			fg12.d1_occupancy_NBR d1_fg12_occfcst,
			fg12.d2_occupancy_NBR d2_fg12_occfcst,
			fg12.d3_occupancy_NBR d3_fg12_occfcst,
			fg12.d4_occupancy_NBR d4_fg12_occfcst,
			fg12.d5_occupancy_NBR d5_fg12_occfcst,
			fg12.d6_occupancy_NBR d6_fg12_occfcst,
			fg12.d7_occupancy_NBR d7_fg12_occfcst,
			fg12.d8_occupancy_NBR d8_fg12_occfcst,
			
			fg13.d1_sold d1_fg13_sold,
			fg13.d2_sold d2_fg13_sold,
			fg13.d3_sold d3_fg13_sold,
			fg13.d4_sold d4_fg13_sold,
			fg13.d5_sold d5_fg13_sold,
			fg13.d6_sold d6_fg13_sold,
			fg13.d7_sold d7_fg13_sold,
			fg13.d8_sold d8_fg13_sold,
			fg13.d1_occupancy_NBR d1_fg13_occfcst,
			fg13.d2_occupancy_NBR d2_fg13_occfcst,
			fg13.d3_occupancy_NBR d3_fg13_occfcst,
			fg13.d4_occupancy_NBR d4_fg13_occfcst,
			fg13.d5_occupancy_NBR d5_fg13_occfcst,
			fg13.d6_occupancy_NBR d6_fg13_occfcst,
			fg13.d7_occupancy_NBR d7_fg13_occfcst,
			fg13.d8_occupancy_NBR d8_fg13_occfcst,
			
			fg14.d1_sold d1_fg14_sold,
			fg14.d2_sold d2_fg14_sold,
			fg14.d3_sold d3_fg14_sold,
			fg14.d4_sold d4_fg14_sold,
			fg14.d5_sold d5_fg14_sold,
			fg14.d6_sold d6_fg14_sold,
			fg14.d7_sold d7_fg14_sold,
			fg14.d8_sold d8_fg14_sold,
			fg14.d1_occupancy_NBR d1_fg14_occfcst,
			fg14.d2_occupancy_NBR d2_fg14_occfcst,
			fg14.d3_occupancy_NBR d3_fg14_occfcst,
			fg14.d4_occupancy_NBR d4_fg14_occfcst,
			fg14.d5_occupancy_NBR d5_fg14_occfcst,
			fg14.d6_occupancy_NBR d6_fg14_occfcst,
			fg14.d7_occupancy_NBR d7_fg14_occfcst,
			fg14.d8_occupancy_NBR d8_fg14_occfcst,
			
			fg15.d1_sold d1_fg15_sold,
			fg15.d2_sold d2_fg15_sold,
			fg15.d3_sold d3_fg15_sold,
			fg15.d4_sold d4_fg15_sold,
			fg15.d5_sold d5_fg15_sold,
			fg15.d6_sold d6_fg15_sold,
			fg15.d7_sold d7_fg15_sold,
			fg15.d8_sold d8_fg15_sold,
			fg15.d1_occupancy_NBR d1_fg15_occfcst,
			fg15.d2_occupancy_NBR d2_fg15_occfcst,
			fg15.d3_occupancy_NBR d3_fg15_occfcst,
			fg15.d4_occupancy_NBR d4_fg15_occfcst,
			fg15.d5_occupancy_NBR d5_fg15_occfcst,
			fg15.d6_occupancy_NBR d6_fg15_occfcst,
			fg15.d7_occupancy_NBR d7_fg15_occfcst,
			fg15.d8_occupancy_NBR d8_fg15_occfcst,
			
			fg16.d1_sold d1_fg16_sold,
			fg16.d2_sold d2_fg16_sold,
			fg16.d3_sold d3_fg16_sold,
			fg16.d4_sold d4_fg16_sold,
			fg16.d5_sold d5_fg16_sold,
			fg16.d6_sold d6_fg16_sold,
			fg16.d7_sold d7_fg16_sold,
			fg16.d8_sold d8_fg16_sold,
			fg16.d1_occupancy_NBR d1_fg16_occfcst,
			fg16.d2_occupancy_NBR d2_fg16_occfcst,
			fg16.d3_occupancy_NBR d3_fg16_occfcst,
			fg16.d4_occupancy_NBR d4_fg16_occfcst,
			fg16.d5_occupancy_NBR d5_fg16_occfcst,
			fg16.d6_occupancy_NBR d6_fg16_occfcst,
			fg16.d7_occupancy_NBR d7_fg16_occfcst,
			fg16.d8_occupancy_NBR d8_fg16_occfcst,
			
			fg17.d1_sold d1_fg17_sold,
			fg17.d2_sold d2_fg17_sold,
			fg17.d3_sold d3_fg17_sold,
			fg17.d4_sold d4_fg17_sold,
			fg17.d5_sold d5_fg17_sold,
			fg17.d6_sold d6_fg17_sold,
			fg17.d7_sold d7_fg17_sold,
			fg17.d8_sold d8_fg17_sold,
			fg17.d1_occupancy_NBR d1_fg17_occfcst,
			fg17.d2_occupancy_NBR d2_fg17_occfcst,
			fg17.d3_occupancy_NBR d3_fg17_occfcst,
			fg17.d4_occupancy_NBR d4_fg17_occfcst,
			fg17.d5_occupancy_NBR d5_fg17_occfcst,
			fg17.d6_occupancy_NBR d6_fg17_occfcst,
			fg17.d7_occupancy_NBR d7_fg17_occfcst,
			fg17.d8_occupancy_NBR d8_fg17_occfcst,
			
			fg18.d1_sold d1_fg18_sold,
			fg18.d2_sold d2_fg18_sold,
			fg18.d3_sold d3_fg18_sold,
			fg18.d4_sold d4_fg18_sold,
			fg18.d5_sold d5_fg18_sold,
			fg18.d6_sold d6_fg18_sold,
			fg18.d7_sold d7_fg18_sold,
			fg18.d8_sold d8_fg18_sold,
			fg18.d1_occupancy_NBR d1_fg18_occfcst,
			fg18.d2_occupancy_NBR d2_fg18_occfcst,
			fg18.d3_occupancy_NBR d3_fg18_occfcst,
			fg18.d4_occupancy_NBR d4_fg18_occfcst,
			fg18.d5_occupancy_NBR d5_fg18_occfcst,
			fg18.d6_occupancy_NBR d6_fg18_occfcst,
			fg18.d7_occupancy_NBR d7_fg18_occfcst,
			fg18.d8_occupancy_NBR d8_fg18_occfcst,
			
			fg19.d1_sold d1_fg19_sold,
			fg19.d2_sold d2_fg19_sold,
			fg19.d3_sold d3_fg19_sold,
			fg19.d4_sold d4_fg19_sold,
			fg19.d5_sold d5_fg19_sold,
			fg19.d6_sold d6_fg19_sold,
			fg19.d7_sold d7_fg19_sold,
			fg19.d8_sold d8_fg19_sold,
			fg19.d1_occupancy_NBR d1_fg19_occfcst,
			fg19.d2_occupancy_NBR d2_fg19_occfcst,
			fg19.d3_occupancy_NBR d3_fg19_occfcst,
			fg19.d4_occupancy_NBR d4_fg19_occfcst,
			fg19.d5_occupancy_NBR d5_fg19_occfcst,
			fg19.d6_occupancy_NBR d6_fg19_occfcst,
			fg19.d7_occupancy_NBR d7_fg19_occfcst,
			fg19.d8_occupancy_NBR d8_fg19_occfcst,
			
			fg20.d1_sold d1_fg20_sold,
			fg20.d2_sold d2_fg20_sold,
			fg20.d3_sold d3_fg20_sold,
			fg20.d4_sold d4_fg20_sold,
			fg20.d5_sold d5_fg20_sold,
			fg20.d6_sold d6_fg20_sold,
			fg20.d7_sold d7_fg20_sold,
			fg20.d8_sold d8_fg20_sold,
			fg20.d1_occupancy_NBR d1_fg20_occfcst,
			fg20.d2_occupancy_NBR d2_fg20_occfcst,
			fg20.d3_occupancy_NBR d3_fg20_occfcst,
			fg20.d4_occupancy_NBR d4_fg20_occfcst,
			fg20.d5_occupancy_NBR d5_fg20_occfcst,
			fg20.d6_occupancy_NBR d6_fg20_occfcst,
			fg20.d7_occupancy_NBR d7_fg20_occfcst,
			fg20.d8_occupancy_NBR d8_fg20_occfcst,
			
			fg21.d1_sold d1_fg21_sold,
			fg21.d2_sold d2_fg21_sold,
			fg21.d3_sold d3_fg21_sold,
			fg21.d4_sold d4_fg21_sold,
			fg21.d5_sold d5_fg21_sold,
			fg21.d6_sold d6_fg21_sold,
			fg21.d7_sold d7_fg21_sold,
			fg21.d8_sold d8_fg21_sold,
			fg21.d1_occupancy_NBR d1_fg21_occfcst,
			fg21.d2_occupancy_NBR d2_fg21_occfcst,
			fg21.d3_occupancy_NBR d3_fg21_occfcst,
			fg21.d4_occupancy_NBR d4_fg21_occfcst,
			fg21.d5_occupancy_NBR d5_fg21_occfcst,
			fg21.d6_occupancy_NBR d6_fg21_occfcst,
			fg21.d7_occupancy_NBR d7_fg21_occfcst,
			fg21.d8_occupancy_NBR d8_fg21_occfcst,
			
			fg22.d1_sold d1_fg22_sold,
			fg22.d2_sold d2_fg22_sold,
			fg22.d3_sold d3_fg22_sold,
			fg22.d4_sold d4_fg22_sold,
			fg22.d5_sold d5_fg22_sold,
			fg22.d6_sold d6_fg22_sold,
			fg22.d7_sold d7_fg22_sold,
			fg22.d8_sold d8_fg22_sold,
			fg22.d1_occupancy_NBR d1_fg22_occfcst,
			fg22.d2_occupancy_NBR d2_fg22_occfcst,
			fg22.d3_occupancy_NBR d3_fg22_occfcst,
			fg22.d4_occupancy_NBR d4_fg22_occfcst,
			fg22.d5_occupancy_NBR d5_fg22_occfcst,
			fg22.d6_occupancy_NBR d6_fg22_occfcst,
			fg22.d7_occupancy_NBR d7_fg22_occfcst,
			fg22.d8_occupancy_NBR d8_fg22_occfcst,
			
			fg23.d1_sold d1_fg23_sold,
			fg23.d2_sold d2_fg23_sold,
			fg23.d3_sold d3_fg23_sold,
			fg23.d4_sold d4_fg23_sold,
			fg23.d5_sold d5_fg23_sold,
			fg23.d6_sold d6_fg23_sold,
			fg23.d7_sold d7_fg23_sold,
			fg23.d8_sold d8_fg23_sold,
			fg23.d1_occupancy_NBR d1_fg23_occfcst,
			fg23.d2_occupancy_NBR d2_fg23_occfcst,
			fg23.d3_occupancy_NBR d3_fg23_occfcst,
			fg23.d4_occupancy_NBR d4_fg23_occfcst,
			fg23.d5_occupancy_NBR d5_fg23_occfcst,
			fg23.d6_occupancy_NBR d6_fg23_occfcst,
			fg23.d7_occupancy_NBR d7_fg23_occfcst,
			fg23.d8_occupancy_NBR d8_fg23_occfcst,
			
			fg24.d1_sold d1_fg24_sold,
			fg24.d2_sold d2_fg24_sold,
			fg24.d3_sold d3_fg24_sold,
			fg24.d4_sold d4_fg24_sold,
			fg24.d5_sold d5_fg24_sold,
			fg24.d6_sold d6_fg24_sold,
			fg24.d7_sold d7_fg24_sold,
			fg24.d8_sold d8_fg24_sold,
			fg24.d1_occupancy_NBR d1_fg24_occfcst,
			fg24.d2_occupancy_NBR d2_fg24_occfcst,
			fg24.d3_occupancy_NBR d3_fg24_occfcst,
			fg24.d4_occupancy_NBR d4_fg24_occfcst,
			fg24.d5_occupancy_NBR d5_fg24_occfcst,
			fg24.d6_occupancy_NBR d6_fg24_occfcst,
			fg24.d7_occupancy_NBR d7_fg24_occfcst,
			fg24.d8_occupancy_NBR d8_fg24_occfcst,
			
			fg25.d1_sold d1_fg25_sold,
			fg25.d2_sold d2_fg25_sold,
			fg25.d3_sold d3_fg25_sold,
			fg25.d4_sold d4_fg25_sold,
			fg25.d5_sold d5_fg25_sold,
			fg25.d6_sold d6_fg25_sold,
			fg25.d7_sold d7_fg25_sold,
			fg25.d8_sold d8_fg25_sold,
			fg25.d1_occupancy_NBR d1_fg25_occfcst,
			fg25.d2_occupancy_NBR d2_fg25_occfcst,
			fg25.d3_occupancy_NBR d3_fg25_occfcst,
			fg25.d4_occupancy_NBR d4_fg25_occfcst,
			fg25.d5_occupancy_NBR d5_fg25_occfcst,
			fg25.d6_occupancy_NBR d6_fg25_occfcst,
			fg25.d7_occupancy_NBR d7_fg25_occfcst,
			fg25.d8_occupancy_NBR d8_fg25_occfcst,

			bv1.d1_sold d1_bv1_sold,
			bv1.d2_sold d2_bv1_sold,
			bv1.d3_sold d3_bv1_sold,
			bv1.d4_sold d4_bv1_sold,
			bv1.d5_sold d5_bv1_sold,
			bv1.d6_sold d6_bv1_sold,
			bv1.d7_sold d7_bv1_sold,
			bv1.d8_sold d8_bv1_sold,
			bv1.d1_occupancy_NBR d1_bv1_occfcst,
			bv1.d2_occupancy_NBR d2_bv1_occfcst,
			bv1.d3_occupancy_NBR d3_bv1_occfcst,
			bv1.d4_occupancy_NBR d4_bv1_occfcst,
			bv1.d5_occupancy_NBR d5_bv1_occfcst,
			bv1.d6_occupancy_NBR d6_bv1_occfcst,
			bv1.d7_occupancy_NBR d7_bv1_occfcst,
			bv1.d8_occupancy_NBR d8_bv1_occfcst,
			

			bv2.d1_sold d1_bv2_sold,
			bv2.d2_sold d2_bv2_sold,
			bv2.d3_sold d3_bv2_sold,
			bv2.d4_sold d4_bv2_sold,
			bv2.d5_sold d5_bv2_sold,
			bv2.d6_sold d6_bv2_sold,
			bv2.d7_sold d7_bv2_sold,
			bv2.d8_sold d8_bv2_sold,
			bv2.d1_occupancy_NBR d1_bv2_occfcst,
			bv2.d2_occupancy_NBR d2_bv2_occfcst,
			bv2.d3_occupancy_NBR d3_bv2_occfcst,
			bv2.d4_occupancy_NBR d4_bv2_occfcst,
			bv2.d5_occupancy_NBR d5_bv2_occfcst,
			bv2.d6_occupancy_NBR d6_bv2_occfcst,
			bv2.d7_occupancy_NBR d7_bv2_occfcst,
			bv2.d8_occupancy_NBR d8_bv2_occfcst,


			bv3.d1_sold d1_bv3_sold,
			bv3.d2_sold d2_bv3_sold,
			bv3.d3_sold d3_bv3_sold,
			bv3.d4_sold d4_bv3_sold,
			bv3.d5_sold d5_bv3_sold,
			bv3.d6_sold d6_bv3_sold,
			bv3.d7_sold d7_bv3_sold,
			bv3.d8_sold d8_bv3_sold,
			bv3.d1_occupancy_NBR d1_bv3_occfcst,
			bv3.d2_occupancy_NBR d2_bv3_occfcst,
			bv3.d3_occupancy_NBR d3_bv3_occfcst,
			bv3.d4_occupancy_NBR d4_bv3_occfcst,
			bv3.d5_occupancy_NBR d5_bv3_occfcst,
			bv3.d6_occupancy_NBR d6_bv3_occfcst,
			bv3.d7_occupancy_NBR d7_bv3_occfcst,
			bv3.d8_occupancy_NBR d8_bv3_occfcst,
			
			bv4.d1_sold d1_bv4_sold,
			bv4.d2_sold d2_bv4_sold,
			bv4.d3_sold d3_bv4_sold,
			bv4.d4_sold d4_bv4_sold,
			bv4.d5_sold d5_bv4_sold,
			bv4.d6_sold d6_bv4_sold,
			bv4.d7_sold d7_bv4_sold,
			bv4.d8_sold d8_bv4_sold,
			bv4.d1_occupancy_NBR d1_bv4_occfcst,
			bv4.d2_occupancy_NBR d2_bv4_occfcst,
			bv4.d3_occupancy_NBR d3_bv4_occfcst,
			bv4.d4_occupancy_NBR d4_bv4_occfcst,
			bv4.d5_occupancy_NBR d5_bv4_occfcst,
			bv4.d6_occupancy_NBR d6_bv4_occfcst,
			bv4.d7_occupancy_NBR d7_bv4_occfcst,
			bv4.d8_occupancy_NBR d8_bv4_occfcst,

			bv5.d1_sold d1_bv5_sold,
			bv5.d2_sold d2_bv5_sold,
			bv5.d3_sold d3_bv5_sold,
			bv5.d4_sold d4_bv5_sold,
			bv5.d5_sold d5_bv5_sold,
			bv5.d6_sold d6_bv5_sold,
			bv5.d7_sold d7_bv5_sold,
			bv5.d8_sold d8_bv5_sold,
			bv5.d1_occupancy_NBR d1_bv5_occfcst,
			bv5.d2_occupancy_NBR d2_bv5_occfcst,
			bv5.d3_occupancy_NBR d3_bv5_occfcst,
			bv5.d4_occupancy_NBR d4_bv5_occfcst,
			bv5.d5_occupancy_NBR d5_bv5_occfcst,
			bv5.d6_occupancy_NBR d6_bv5_occfcst,
			bv5.d7_occupancy_NBR d7_bv5_occfcst,
			bv5.d8_occupancy_NBR d8_bv5_occfcst,

			bv6.d1_sold d1_bv6_sold,
			bv6.d2_sold d2_bv6_sold,
			bv6.d3_sold d3_bv6_sold,
			bv6.d4_sold d4_bv6_sold,
			bv6.d5_sold d5_bv6_sold,
			bv6.d6_sold d6_bv6_sold,
			bv6.d7_sold d7_bv6_sold,
			bv6.d8_sold d8_bv6_sold,
			bv6.d1_occupancy_NBR d1_bv6_occfcst,
			bv6.d2_occupancy_NBR d2_bv6_occfcst,
			bv6.d3_occupancy_NBR d3_bv6_occfcst,
			bv6.d4_occupancy_NBR d4_bv6_occfcst,
			bv6.d5_occupancy_NBR d5_bv6_occfcst,
			bv6.d6_occupancy_NBR d6_bv6_occfcst,
			bv6.d7_occupancy_NBR d7_bv6_occfcst,
			bv6.d8_occupancy_NBR d8_bv6_occfcst,

			bv7.d1_sold d1_bv7_sold,
			bv7.d2_sold d2_bv7_sold,
			bv7.d3_sold d3_bv7_sold,
			bv7.d4_sold d4_bv7_sold,
			bv7.d5_sold d5_bv7_sold,
			bv7.d6_sold d6_bv7_sold,
			bv7.d7_sold d7_bv7_sold,
			bv7.d8_sold d8_bv7_sold,
			bv7.d1_occupancy_NBR d1_bv7_occfcst,
			bv7.d2_occupancy_NBR d2_bv7_occfcst,
			bv7.d3_occupancy_NBR d3_bv7_occfcst,
			bv7.d4_occupancy_NBR d4_bv7_occfcst,
			bv7.d5_occupancy_NBR d5_bv7_occfcst,
			bv7.d6_occupancy_NBR d6_bv7_occfcst,
			bv7.d7_occupancy_NBR d7_bv7_occfcst,
			bv7.d8_occupancy_NBR d8_bv7_occfcst,

			bv8.d1_sold d1_bv8_sold,
			bv8.d2_sold d2_bv8_sold,
			bv8.d3_sold d3_bv8_sold,
			bv8.d4_sold d4_bv8_sold,
			bv8.d5_sold d5_bv8_sold,
			bv8.d6_sold d6_bv8_sold,
			bv8.d7_sold d7_bv8_sold,
			bv8.d8_sold d8_bv8_sold,
			bv8.d1_occupancy_NBR d1_bv8_occfcst,
			bv8.d2_occupancy_NBR d2_bv8_occfcst,
			bv8.d3_occupancy_NBR d3_bv8_occfcst,
			bv8.d4_occupancy_NBR d4_bv8_occfcst,
			bv8.d5_occupancy_NBR d5_bv8_occfcst,
			bv8.d6_occupancy_NBR d6_bv8_occfcst,
			bv8.d7_occupancy_NBR d7_bv8_occfcst,
			bv8.d8_occupancy_NBR d8_bv8_occfcst,
			
			bv9.d1_sold d1_bv9_sold,
			bv9.d2_sold d2_bv9_sold,
			bv9.d3_sold d3_bv9_sold,
			bv9.d4_sold d4_bv9_sold,
			bv9.d5_sold d5_bv9_sold,
			bv9.d6_sold d6_bv9_sold,
			bv9.d7_sold d7_bv9_sold,
			bv9.d8_sold d8_bv9_sold,
			bv9.d1_occupancy_NBR d1_bv9_occfcst,
			bv9.d2_occupancy_NBR d2_bv9_occfcst,
			bv9.d3_occupancy_NBR d3_bv9_occfcst,
			bv9.d4_occupancy_NBR d4_bv9_occfcst,
			bv9.d5_occupancy_NBR d5_bv9_occfcst,
			bv9.d6_occupancy_NBR d6_bv9_occfcst,
			bv9.d7_occupancy_NBR d7_bv9_occfcst,
			bv9.d8_occupancy_NBR d8_bv9_occfcst,
			
			bv10.d1_sold d1_bv10_sold,
			bv10.d2_sold d2_bv10_sold,
			bv10.d3_sold d3_bv10_sold,
			bv10.d4_sold d4_bv10_sold,
			bv10.d5_sold d5_bv10_sold,
			bv10.d6_sold d6_bv10_sold,
			bv10.d7_sold d7_bv10_sold,
			bv10.d8_sold d8_bv10_sold,
			bv10.d1_occupancy_NBR d1_bv10_occfcst,
			bv10.d2_occupancy_NBR d2_bv10_occfcst,
			bv10.d3_occupancy_NBR d3_bv10_occfcst,
			bv10.d4_occupancy_NBR d4_bv10_occfcst,
			bv10.d5_occupancy_NBR d5_bv10_occfcst,
			bv10.d6_occupancy_NBR d6_bv10_occfcst,
			bv10.d7_occupancy_NBR d7_bv10_occfcst,
			bv10.d8_occupancy_NBR d8_bv10_occfcst,
			
			bv11.d1_sold d1_bv11_sold,
			bv11.d2_sold d2_bv11_sold,
			bv11.d3_sold d3_bv11_sold,
			bv11.d4_sold d4_bv11_sold,
			bv11.d5_sold d5_bv11_sold,
			bv11.d6_sold d6_bv11_sold,
			bv11.d7_sold d7_bv11_sold,
			bv11.d8_sold d8_bv11_sold,
			bv11.d1_occupancy_NBR d1_bv11_occfcst,
			bv11.d2_occupancy_NBR d2_bv11_occfcst,
			bv11.d3_occupancy_NBR d3_bv11_occfcst,
			bv11.d4_occupancy_NBR d4_bv11_occfcst,
			bv11.d5_occupancy_NBR d5_bv11_occfcst,
			bv11.d6_occupancy_NBR d6_bv11_occfcst,
			bv11.d7_occupancy_NBR d7_bv11_occfcst,
			bv11.d8_occupancy_NBR d8_bv11_occfcst,
			
			bv12.d1_sold d1_bv12_sold,
			bv12.d2_sold d2_bv12_sold,
			bv12.d3_sold d3_bv12_sold,
			bv12.d4_sold d4_bv12_sold,
			bv12.d5_sold d5_bv12_sold,
			bv12.d6_sold d6_bv12_sold,
			bv12.d7_sold d7_bv12_sold,
			bv12.d8_sold d8_bv12_sold,
			bv12.d1_occupancy_NBR d1_bv12_occfcst,
			bv12.d2_occupancy_NBR d2_bv12_occfcst,
			bv12.d3_occupancy_NBR d3_bv12_occfcst,
			bv12.d4_occupancy_NBR d4_bv12_occfcst,
			bv12.d5_occupancy_NBR d5_bv12_occfcst,
			bv12.d6_occupancy_NBR d6_bv12_occfcst,
			bv12.d7_occupancy_NBR d7_bv12_occfcst,
			bv12.d8_occupancy_NBR d8_bv12_occfcst,
			
			rc1.d1_sold d1_rc1_sold,
			rc1.d2_sold d2_rc1_sold,
			rc1.d3_sold d3_rc1_sold,
			rc1.d4_sold d4_rc1_sold,
			rc1.d5_sold d5_rc1_sold,
			rc1.d6_sold d6_rc1_sold,
			rc1.d7_sold d7_rc1_sold,
			rc1.d8_sold d8_rc1_sold,
			rc1.d1_occupancy_NBR d1_rc1_occfcst,
			rc1.d2_occupancy_NBR d2_rc1_occfcst,
			rc1.d3_occupancy_NBR d3_rc1_occfcst,
			rc1.d4_occupancy_NBR d4_rc1_occfcst,
			rc1.d5_occupancy_NBR d5_rc1_occfcst,
			rc1.d6_occupancy_NBR d6_rc1_occfcst,
			rc1.d7_occupancy_NBR d7_rc1_occfcst,
			rc1.d8_occupancy_NBR d8_rc1_occfcst,
			rc1.d1_lrv d1_rc1_lrv,
			rc1.d2_lrv d2_rc1_lrv,
			rc1.d3_lrv d3_rc1_lrv,
			rc1.d4_lrv d4_rc1_lrv,
			rc1.d5_lrv d5_rc1_lrv,
			rc1.d6_lrv d6_rc1_lrv,
			rc1.d7_lrv d7_rc1_lrv,
			rc1.d8_lrv d8_rc1_lrv,
			
			rc2.d1_sold d1_rc2_sold,
			rc2.d2_sold d2_rc2_sold,
			rc2.d3_sold d3_rc2_sold,
			rc2.d4_sold d4_rc2_sold,
			rc2.d5_sold d5_rc2_sold,
			rc2.d6_sold d6_rc2_sold,
			rc2.d7_sold d7_rc2_sold,
			rc2.d8_sold d8_rc2_sold,
			rc2.d1_occupancy_NBR d1_rc2_occfcst,
			rc2.d2_occupancy_NBR d2_rc2_occfcst,
			rc2.d3_occupancy_NBR d3_rc2_occfcst,
			rc2.d4_occupancy_NBR d4_rc2_occfcst,
			rc2.d5_occupancy_NBR d5_rc2_occfcst,
			rc2.d6_occupancy_NBR d6_rc2_occfcst,
			rc2.d7_occupancy_NBR d7_rc2_occfcst,
			rc2.d8_occupancy_NBR d8_rc2_occfcst,
			rc2.d1_lrv d1_rc2_lrv,
			rc2.d2_lrv d2_rc2_lrv,
			rc2.d3_lrv d3_rc2_lrv,
			rc2.d4_lrv d4_rc2_lrv,
			rc2.d5_lrv d5_rc2_lrv,
			rc2.d6_lrv d6_rc2_lrv,
			rc2.d7_lrv d7_rc2_lrv,
			rc2.d8_lrv d8_rc2_lrv,
			
			rc3.d1_sold d1_rc3_sold,
			rc3.d2_sold d2_rc3_sold,
			rc3.d3_sold d3_rc3_sold,
			rc3.d4_sold d4_rc3_sold,
			rc3.d5_sold d5_rc3_sold,
			rc3.d6_sold d6_rc3_sold,
			rc3.d7_sold d7_rc3_sold,
			rc3.d8_sold d8_rc3_sold,
			rc3.d1_occupancy_NBR d1_rc3_occfcst,
			rc3.d2_occupancy_NBR d2_rc3_occfcst,
			rc3.d3_occupancy_NBR d3_rc3_occfcst,
			rc3.d4_occupancy_NBR d4_rc3_occfcst,
			rc3.d5_occupancy_NBR d5_rc3_occfcst,
			rc3.d6_occupancy_NBR d6_rc3_occfcst,
			rc3.d7_occupancy_NBR d7_rc3_occfcst,
			rc3.d8_occupancy_NBR d8_rc3_occfcst,
			rc3.d1_lrv d1_rc3_lrv,
			rc3.d2_lrv d2_rc3_lrv,
			rc3.d3_lrv d3_rc3_lrv,
			rc3.d4_lrv d4_rc3_lrv,
			rc3.d5_lrv d5_rc3_lrv,
			rc3.d6_lrv d6_rc3_lrv,
			rc3.d7_lrv d7_rc3_lrv,
			rc3.d8_lrv d8_rc3_lrv,
			
			rc4.d1_sold d1_rc4_sold,
			rc4.d2_sold d2_rc4_sold,
			rc4.d3_sold d3_rc4_sold,
			rc4.d4_sold d4_rc4_sold,
			rc4.d5_sold d5_rc4_sold,
			rc4.d6_sold d6_rc4_sold,
			rc4.d7_sold d7_rc4_sold,
			rc4.d8_sold d8_rc4_sold,
			rc4.d1_occupancy_NBR d1_rc4_occfcst,
			rc4.d2_occupancy_NBR d2_rc4_occfcst,
			rc4.d3_occupancy_NBR d3_rc4_occfcst,
			rc4.d4_occupancy_NBR d4_rc4_occfcst,
			rc4.d5_occupancy_NBR d5_rc4_occfcst,
			rc4.d6_occupancy_NBR d6_rc4_occfcst,
			rc4.d7_occupancy_NBR d7_rc4_occfcst,
			rc4.d8_occupancy_NBR d8_rc4_occfcst,
			rc4.d1_lrv d1_rc4_lrv,
			rc4.d2_lrv d2_rc4_lrv,
			rc4.d3_lrv d3_rc4_lrv,
			rc4.d4_lrv d4_rc4_lrv,
			rc4.d5_lrv d5_rc4_lrv,
			rc4.d6_lrv d6_rc4_lrv,
			rc4.d7_lrv d7_rc4_lrv,
			rc4.d8_lrv d8_rc4_lrv,
			
			rc5.d1_sold d1_rc5_sold,
			rc5.d2_sold d2_rc5_sold,
			rc5.d3_sold d3_rc5_sold,
			rc5.d4_sold d4_rc5_sold,
			rc5.d5_sold d5_rc5_sold,
			rc5.d6_sold d6_rc5_sold,
			rc5.d7_sold d7_rc5_sold,
			rc5.d8_sold d8_rc5_sold,
			rc5.d1_occupancy_NBR d1_rc5_occfcst,
			rc5.d2_occupancy_NBR d2_rc5_occfcst,
			rc5.d3_occupancy_NBR d3_rc5_occfcst,
			rc5.d4_occupancy_NBR d4_rc5_occfcst,
			rc5.d5_occupancy_NBR d5_rc5_occfcst,
			rc5.d6_occupancy_NBR d6_rc5_occfcst,
			rc5.d7_occupancy_NBR d7_rc5_occfcst,
			rc5.d8_occupancy_NBR d8_rc5_occfcst,
			rc5.d1_lrv d1_rc5_lrv,
			rc5.d2_lrv d2_rc5_lrv,
			rc5.d3_lrv d3_rc5_lrv,
			rc5.d4_lrv d4_rc5_lrv,
			rc5.d5_lrv d5_rc5_lrv,
			rc5.d6_lrv d6_rc5_lrv,
			rc5.d7_lrv d7_rc5_lrv,
			rc5.d8_lrv d8_rc5_lrv,
			
			rc6.d1_sold d1_rc6_sold,
			rc6.d2_sold d2_rc6_sold,
			rc6.d3_sold d3_rc6_sold,
			rc6.d4_sold d4_rc6_sold,
			rc6.d5_sold d5_rc6_sold,
			rc6.d6_sold d6_rc6_sold,
			rc6.d7_sold d7_rc6_sold,
			rc6.d8_sold d8_rc6_sold,
			rc6.d1_occupancy_NBR d1_rc6_occfcst,
			rc6.d2_occupancy_NBR d2_rc6_occfcst,
			rc6.d3_occupancy_NBR d3_rc6_occfcst,
			rc6.d4_occupancy_NBR d4_rc6_occfcst,
			rc6.d5_occupancy_NBR d5_rc6_occfcst,
			rc6.d6_occupancy_NBR d6_rc6_occfcst,
			rc6.d7_occupancy_NBR d7_rc6_occfcst,
			rc6.d8_occupancy_NBR d8_rc6_occfcst,
			rc6.d1_lrv d1_rc6_lrv,
			rc6.d2_lrv d2_rc6_lrv,
			rc6.d3_lrv d3_rc6_lrv,
			rc6.d4_lrv d4_rc6_lrv,
			rc6.d5_lrv d5_rc6_lrv,
			rc6.d6_lrv d6_rc6_lrv,
			rc6.d7_lrv d7_rc6_lrv,
			rc6.d8_lrv d8_rc6_lrv,
			
			rc7.d1_sold d1_rc7_sold,
			rc7.d2_sold d2_rc7_sold,
			rc7.d3_sold d3_rc7_sold,
			rc7.d4_sold d4_rc7_sold,
			rc7.d5_sold d5_rc7_sold,
			rc7.d6_sold d6_rc7_sold,
			rc7.d7_sold d7_rc7_sold,
			rc7.d8_sold d8_rc7_sold,
			rc7.d1_occupancy_NBR d1_rc7_occfcst,
			rc7.d2_occupancy_NBR d2_rc7_occfcst,
			rc7.d3_occupancy_NBR d3_rc7_occfcst,
			rc7.d4_occupancy_NBR d4_rc7_occfcst,
			rc7.d5_occupancy_NBR d5_rc7_occfcst,
			rc7.d6_occupancy_NBR d6_rc7_occfcst,
			rc7.d7_occupancy_NBR d7_rc7_occfcst,
			rc7.d8_occupancy_NBR d8_rc7_occfcst,
			rc7.d1_lrv d1_rc7_lrv,
			rc7.d2_lrv d2_rc7_lrv,
			rc7.d3_lrv d3_rc7_lrv,
			rc7.d4_lrv d4_rc7_lrv,
			rc7.d5_lrv d5_rc7_lrv,
			rc7.d6_lrv d6_rc7_lrv,
			rc7.d7_lrv d7_rc7_lrv,
			rc7.d8_lrv d8_rc7_lrv,
			
			rc8.d1_sold d1_rc8_sold,
			rc8.d2_sold d2_rc8_sold,
			rc8.d3_sold d3_rc8_sold,
			rc8.d4_sold d4_rc8_sold,
			rc8.d5_sold d5_rc8_sold,
			rc8.d6_sold d6_rc8_sold,
			rc8.d7_sold d7_rc8_sold,
			rc8.d8_sold d8_rc8_sold,
			rc8.d1_occupancy_NBR d1_rc8_occfcst,
			rc8.d2_occupancy_NBR d2_rc8_occfcst,
			rc8.d3_occupancy_NBR d3_rc8_occfcst,
			rc8.d4_occupancy_NBR d4_rc8_occfcst,
			rc8.d5_occupancy_NBR d5_rc8_occfcst,
			rc8.d6_occupancy_NBR d6_rc8_occfcst,
			rc8.d7_occupancy_NBR d7_rc8_occfcst,
			rc8.d8_occupancy_NBR d8_rc8_occfcst,
			rc8.d1_lrv d1_rc8_lrv,
			rc8.d2_lrv d2_rc8_lrv,
			rc8.d3_lrv d3_rc8_lrv,
			rc8.d4_lrv d4_rc8_lrv,
			rc8.d5_lrv d5_rc8_lrv,
			rc8.d6_lrv d6_rc8_lrv,
			rc8.d7_lrv d7_rc8_lrv,
			rc8.d8_lrv d8_rc8_lrv,
			
			rc9.d1_sold d1_rc9_sold,
			rc9.d2_sold d2_rc9_sold,
			rc9.d3_sold d3_rc9_sold,
			rc9.d4_sold d4_rc9_sold,
			rc9.d5_sold d5_rc9_sold,
			rc9.d6_sold d6_rc9_sold,
			rc9.d7_sold d7_rc9_sold,
			rc9.d8_sold d8_rc9_sold,
			rc9.d1_occupancy_NBR d1_rc9_occfcst,
			rc9.d2_occupancy_NBR d2_rc9_occfcst,
			rc9.d3_occupancy_NBR d3_rc9_occfcst,
			rc9.d4_occupancy_NBR d4_rc9_occfcst,
			rc9.d5_occupancy_NBR d5_rc9_occfcst,
			rc9.d6_occupancy_NBR d6_rc9_occfcst,
			rc9.d7_occupancy_NBR d7_rc9_occfcst,
			rc9.d8_occupancy_NBR d8_rc9_occfcst,
			rc9.d1_lrv d1_rc9_lrv,
			rc9.d2_lrv d2_rc9_lrv,
			rc9.d3_lrv d3_rc9_lrv,
			rc9.d4_lrv d4_rc9_lrv,
			rc9.d5_lrv d5_rc9_lrv,
			rc9.d6_lrv d6_rc9_lrv,
			rc9.d7_lrv d7_rc9_lrv,
			rc9.d8_lrv d8_rc9_lrv,
			
			rc10.d1_sold d1_rc10_sold,
			rc10.d2_sold d2_rc10_sold,
			rc10.d3_sold d3_rc10_sold,
			rc10.d4_sold d4_rc10_sold,
			rc10.d5_sold d5_rc10_sold,
			rc10.d6_sold d6_rc10_sold,
			rc10.d7_sold d7_rc10_sold,
			rc10.d8_sold d8_rc10_sold,
			rc10.d1_occupancy_NBR d1_rc10_occfcst,
			rc10.d2_occupancy_NBR d2_rc10_occfcst,
			rc10.d3_occupancy_NBR d3_rc10_occfcst,
			rc10.d4_occupancy_NBR d4_rc10_occfcst,
			rc10.d5_occupancy_NBR d5_rc10_occfcst,
			rc10.d6_occupancy_NBR d6_rc10_occfcst,
			rc10.d7_occupancy_NBR d7_rc10_occfcst,
			rc10.d8_occupancy_NBR d8_rc10_occfcst,
			rc10.d1_lrv d1_rc10_lrv,
			rc10.d2_lrv d2_rc10_lrv,
			rc10.d3_lrv d3_rc10_lrv,
			rc10.d4_lrv d4_rc10_lrv,
			rc10.d5_lrv d5_rc10_lrv,
			rc10.d6_lrv d6_rc10_lrv,
			rc10.d7_lrv d7_rc10_lrv,
			rc10.d8_lrv d8_rc10_lrv,
			
			rc11.d1_sold d1_rc11_sold,
			rc11.d2_sold d2_rc11_sold,
			rc11.d3_sold d3_rc11_sold,
			rc11.d4_sold d4_rc11_sold,
			rc11.d5_sold d5_rc11_sold,
			rc11.d6_sold d6_rc11_sold,
			rc11.d7_sold d7_rc11_sold,
			rc11.d8_sold d8_rc11_sold,
			rc11.d1_occupancy_NBR d1_rc11_occfcst,
			rc11.d2_occupancy_NBR d2_rc11_occfcst,
			rc11.d3_occupancy_NBR d3_rc11_occfcst,
			rc11.d4_occupancy_NBR d4_rc11_occfcst,
			rc11.d5_occupancy_NBR d5_rc11_occfcst,
			rc11.d6_occupancy_NBR d6_rc11_occfcst,
			rc11.d7_occupancy_NBR d7_rc11_occfcst,
			rc11.d8_occupancy_NBR d8_rc11_occfcst,
			rc11.d1_lrv d1_rc11_lrv,
			rc11.d2_lrv d2_rc11_lrv,
			rc11.d3_lrv d3_rc11_lrv,
			rc11.d4_lrv d4_rc11_lrv,
			rc11.d5_lrv d5_rc11_lrv,
			rc11.d6_lrv d6_rc11_lrv,
			rc11.d7_lrv d7_rc11_lrv,
			rc11.d8_lrv d8_rc11_lrv,
			
			rc12.d1_sold d1_rc12_sold,
			rc12.d2_sold d2_rc12_sold,
			rc12.d3_sold d3_rc12_sold,
			rc12.d4_sold d4_rc12_sold,
			rc12.d5_sold d5_rc12_sold,
			rc12.d6_sold d6_rc12_sold,
			rc12.d7_sold d7_rc12_sold,
			rc12.d8_sold d8_rc12_sold,
			rc12.d1_occupancy_NBR d1_rc12_occfcst,
			rc12.d2_occupancy_NBR d2_rc12_occfcst,
			rc12.d3_occupancy_NBR d3_rc12_occfcst,
			rc12.d4_occupancy_NBR d4_rc12_occfcst,
			rc12.d5_occupancy_NBR d5_rc12_occfcst,
			rc12.d6_occupancy_NBR d6_rc12_occfcst,
			rc12.d7_occupancy_NBR d7_rc12_occfcst,
			rc12.d8_occupancy_NBR d8_rc12_occfcst,
			rc12.d1_lrv d1_rc12_lrv,
			rc12.d2_lrv d2_rc12_lrv,
			rc12.d3_lrv d3_rc12_lrv,
			rc12.d4_lrv d4_rc12_lrv,
			rc12.d5_lrv d5_rc12_lrv,
			rc12.d6_lrv d6_rc12_lrv,
			rc12.d7_lrv d7_rc12_lrv,
			rc12.d8_lrv d8_rc12_lrv

		from 
		(
			select distinct(0) as daystoArrival from Property 
			union  
			select cal_sk as daystoArrival from calendar_dim where cal_sk <=@pace_days
		) base
		left join
		(
			select * from ufn_get_comparative_pace_property(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@pace_days,@isSold,@isLrv,@isOcFcst) where @isHotel>0
		) hotel on base.daystoArrival = hotel.daystoArrival
		left join	
		(
			select * from ufn_get_comparative_pace_bt(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@transient_type,@pace_days,@isSold,@isOcFcst) where @isTransient>0
		) trans on base.daystoArrival=trans.daystoArrival
		left join	
		(
			select * from ufn_get_comparative_pace_bt(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@group_type,@pace_days,@isSold,@isOcFcst) where @isGroup>0
		) grp on base.daystoArrival=grp.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg1,@pace_days,@isSold,@isOcFcst) where @fg1>0
		) fg1 on base.daystoArrival=fg1.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg2,@pace_days,@isSold,@isOcFcst) where @fg2>0
		) fg2 on base.daystoArrival=fg2.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg3,@pace_days,@isSold,@isOcFcst) where @fg3>0
		) fg3 on base.daystoArrival=fg3.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg4,@pace_days,@isSold,@isOcFcst) where @fg4>0
		) fg4 on base.daystoArrival=fg4.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg5,@pace_days,@isSold,@isOcFcst) where @fg5>0
		) fg5 on base.daystoArrival=fg5.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg6,@pace_days,@isSold,@isOcFcst) where @fg6>0
		) fg6 on base.daystoArrival=fg6.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg7,@pace_days,@isSold,@isOcFcst) where @fg7>0
		) fg7 on base.daystoArrival=fg7.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg8,@pace_days,@isSold,@isOcFcst) where @fg8>0
		) fg8 on base.daystoArrival=fg8.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg9,@pace_days,@isSold,@isOcFcst) where @fg9>0
		) fg9 on base.daystoArrival=fg9.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg10,@pace_days,@isSold,@isOcFcst) where @fg10>0
		) fg10 on base.daystoArrival=fg10.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg11,@pace_days,@isSold,@isOcFcst) where @fg11>0
		) fg11 on base.daystoArrival=fg11.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg12,@pace_days,@isSold,@isOcFcst) where @fg12>0
		) fg12 on base.daystoArrival=fg12.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg13,@pace_days,@isSold,@isOcFcst) where @fg13>0
		) fg13 on base.daystoArrival=fg13.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg14,@pace_days,@isSold,@isOcFcst) where @fg14>0
		) fg14 on base.daystoArrival=fg14.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg15,@pace_days,@isSold,@isOcFcst) where @fg15>0
		) fg15 on base.daystoArrival=fg15.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg16,@pace_days,@isSold,@isOcFcst) where @fg16>0
		) fg16 on base.daystoArrival=fg16.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg17,@pace_days,@isSold,@isOcFcst) where @fg17>0
		) fg17 on base.daystoArrival=fg17.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg18,@pace_days,@isSold,@isOcFcst) where @fg18>0
		) fg18 on base.daystoArrival=fg18.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg19,@pace_days,@isSold,@isOcFcst) where @fg19>0
		) fg19 on base.daystoArrival=fg19.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg20,@pace_days,@isSold,@isOcFcst) where @fg20>0
		) fg20 on base.daystoArrival=fg20.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg21,@pace_days,@isSold,@isOcFcst) where @fg21>0
		) fg21 on base.daystoArrival=fg21.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg22,@pace_days,@isSold,@isOcFcst) where @fg22>0
		) fg22 on base.daystoArrival=fg22.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg23,@pace_days,@isSold,@isOcFcst) where @fg23>0
		) fg23 on base.daystoArrival=fg23.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg24,@pace_days,@isSold,@isOcFcst) where @fg24>0
		) fg24 on base.daystoArrival=fg24.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_fg(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@fg25,@pace_days,@isSold,@isOcFcst) where @fg25>0
		) fg25 on base.daystoArrival=fg25.daystoArrival	
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv1,@pace_days,@isSold,@isOcFcst) where @bv1>0
		) bv1 on base.daystoArrival=bv1.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv2,@pace_days,@isSold,@isOcFcst) where @bv2>0
		) bv2 on base.daystoArrival=bv2.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv3,@pace_days,@isSold,@isOcFcst) where @bv3>0
		) bv3 on base.daystoArrival=bv3.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv4,@pace_days,@isSold,@isOcFcst) where @bv4>0
		) bv4 on base.daystoArrival=bv4.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv5,@pace_days,@isSold,@isOcFcst) where @bv5>0
		) bv5 on base.daystoArrival=bv5.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv6,@pace_days,@isSold,@isOcFcst) where @bv6>0
		) bv6 on base.daystoArrival=bv6.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv7,@pace_days,@isSold,@isOcFcst) where @bv7>0
		) bv7 on base.daystoArrival=bv7.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv8,@pace_days,@isSold,@isOcFcst) where @bv8>0
		) bv8 on base.daystoArrival=bv8.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv9,@pace_days,@isSold,@isOcFcst) where @bv9>0
		) bv9 on base.daystoArrival=bv9.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv10,@pace_days,@isSold,@isOcFcst) where @bv10>0
		) bv10 on base.daystoArrival=bv10.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv11,@pace_days,@isSold,@isOcFcst) where @bv11>0
		) bv11 on base.daystoArrival=bv11.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_bv(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@bv12,@pace_days,@isSold,@isOcFcst) where @bv12>0
		) bv12 on base.daystoArrival=bv12.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc1,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc1>0
		) rc1 on base.daystoArrival=rc1.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc2,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc2>0
		) rc2 on base.daystoArrival=rc2.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc3,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc3>0
		) rc3 on base.daystoArrival=rc3.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc4,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc4>0
		) rc4 on base.daystoArrival=rc4.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc5,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc5>0
		) rc5 on base.daystoArrival=rc5.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc6,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc6>0
		) rc6 on base.daystoArrival=rc6.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc7,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc7>0
		) rc7 on base.daystoArrival=rc7.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc8,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc8>0
		) rc8 on base.daystoArrival=rc8.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc9,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc9>0
		) rc9 on base.daystoArrival=rc9.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc10,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc10>0
		) rc10 on base.daystoArrival=rc10.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc11,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc11>0
		) rc11 on base.daystoArrival=rc11.daystoArrival
		left join
		(
			select * from ufn_get_comparative_pace_rc(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@rc12,@pace_days,@isSold,@isOcFcst,@isLrv,@includeInactiveRT) where @rc12>0
		) rc12 on base.daystoArrival=rc12.daystoArrival
		
		

	return
end
