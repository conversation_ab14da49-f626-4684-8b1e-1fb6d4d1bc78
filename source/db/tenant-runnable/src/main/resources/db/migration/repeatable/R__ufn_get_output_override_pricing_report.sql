if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_output_override_pricing_report]'))
drop function [ufn_get_output_override_pricing_report]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE function [dbo].[ufn_get_output_override_pricing_report]
	(
		@property_id int,
		@start_date date,
		@end_date date,
		@isRollingDate int,
		@rolling_start_date nvarchar(50),
		@rolling_end_date nvarchar(50),
		@isContinuousPricing nvarchar(10),
		@isHighestBarRestricted nvarchar(10),
		@products nvarchar(500)
	)
	returns  @output_override_pricing_report table
	(
		dow varchar (10),
		Arrival_DT date,
		Property_Name nvarchar(150),
		Accom_Class_Name nvarchar(150),
		los int,
		New_Override nvarchar(50),
		Rate_Code_Name nvarchar(50),
		User_Name nvarchar(50),
		CreateDate_DTTM datetime,
		Old_Rate_Code_Name nvarchar(50),
		Old_Override nvarchar(50),
		notes nvarchar(max),
		is<PERSON><PERSON> nvarchar(10),
		roomTypeName nvarchar(150),
		Accom_Class_Code nvarchar(150),
		Email_Address nvarchar(150),
		Accom_Type_Code nvarchar(150),
		Product_Name nvarchar(500),
		display_order int,
		Optimized_Offset_Value_Floor nvarchar(50),
		Optimized_Offset_Value_Ceil nvarchar(50)
	)
as
	begin

		declare @caughtupdate date
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
			begin
				set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
				set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
			end

		if(upper(@isContinuousPricing) = 'TRUE')
			begin

				declare @selectedRoomTypesTable table (Accom_Class_ID int, Accom_Class_Name nvarchar(500), Accom_Class_Code nvarchar(500), Accom_Type_ID int, Accom_Type_Name nvarchar(500), Accom_Type_Code nvarchar(500))
				insert into @selectedRoomTypesTable
					select
						c.Accom_Class_ID, c.Accom_Class_Name, c.Accom_Class_Code, t.Accom_Type_ID, t.Accom_Type_Name, t.Accom_Type_Code
					from
						Accom_Class c
						join
						Accom_Type t
							on
								c.Accom_Class_ID = t.Accom_Class_ID

				declare @selectedProductsOverrideTable table (Product_ID int, Product_Name nvarchar(500), Accom_Class_ID int, Occupancy_DT date, Offset_Method int, Offset_Value numeric(19,2), Created_DTTM date,
				                                              User_Name nvarchar(500), Email_Address nvarchar(500), display_order int, Optimized_Offset_Value_Floor numeric(19,2), Optimized_Offset_Value_Ceil numeric(19,2))
				insert into @selectedProductsOverrideTable
					select
						a.Product_ID,
						a.Product_Name,
						a.Accom_Class_ID,
						a.Occupancy_DT,
						a.Offset_Method,
						a.Offset_Value,
						a.Created_DTTM,
						a.User_Name,
						a.Email_Address,
						a.display_order,
                        a.Optimized_Offset_Value_Floor,
                        a.Optimized_Offset_Value_Ceil
					from
						(
							select
								product.Product_ID,
								product.Name as Product_Name,
								ovr.Accom_Class_ID,
								ovr.Occupancy_DT,
								ovr.Offset_Method,
								ovr.Offset_Value,
								ovr.Created_DTTM,
								users.User_Name,
								users.Email_Address,
								product.Display_Order,
                                CASE product.is_optimized WHEN 1 THEN ovr.Offset_Value_Floor ELSE NULL END as Optimized_Offset_Value_Floor,
                                CASE product.is_optimized WHEN 1 THEN ovr.Offset_Value_Ceil ELSE NULL END as Optimized_Offset_Value_Ceil,
								ROW_NUMBER() over (partition by ovr.Product_ID, ovr.Accom_Class_ID, ovr.Occupancy_DT, ovr.Offset_Method order by ovr.Created_DTTM desc) rank
							from
								Product_Rate_Offset_OVR as ovr
								join
								Product as product
									on
										ovr.Product_ID = product.Product_ID
								join
								Users as users
									on
										users.User_ID = ovr.Created_by_User_ID
							where Occupancy_DT <= @end_date and Occupancy_DT >= @start_date
							      and product.Product_ID IN (SELECT Value FROM varcharToInt(@products,','))
								  and ovr.Status_ID = 1
						) a where rank = 1

				insert into @output_override_pricing_report
					SELECT
						DATENAME(dw,Arrival_DT) as DOW,
						Arrival_DT,
						Property_Name,
						Accom_Class_Name,
						LOS,
						New_Override =	CASE New_Override
						                  WHEN 'User' THEN 'Specific'
						                  ELSE New_Override
						                  END,
						Rate_Code_Name,
						USER_NAME,
						CreateDate,
						Old_BAR,
						Old_Override,
						Notes,
						isLOS,
						Accom_Type_Name,
						Accom_Class_Code,
						EMAIL_ADDRESS,
						Accom_Type_Code,
						Product_Name,
						Display_Order,
                        null as Optimized_Offset_Value_Floor , null as Optimized_Offset_Value_Ceil
					FROM
						(
							SELECT
								main.Arrival_DT,
								main.Property_Name,
								main.Accom_Class_Name,
								main.LOS,
								main.New_Override,
								main.Rate_Code_Name,
								main.USER_NAME,
								main.CreateDate,
								main.Old_BAR,
								main.Old_Override,
								notes.Notes,
								byLOS.isLOS,
								main.Accom_Type_Name,
								main.Accom_Class_Code,
								main.EMAIL_ADDRESS,
								main.Accom_Type_Code,
								main.Product_Name as Product_Name,
								main.display_order as Display_Order
							FROM
								(
									SELECT
										cdboo.Arrival_DT,
										cdboo.Accom_Type_Id,
										cdboo.LOS,
                                        cdboo.Product_ID,
										MAX(cdboo.Decision_Id) Decision_Id
									FROM [dbo].[CP_Decision_Bar_Output_OVR] cdboo
									WHERE cdboo.Property_ID = @property_id and cdboo.Arrival_DT between @start_date AND @end_date
									GROUP BY cdboo.Arrival_DT, cdboo.Accom_Type_Id, cdboo.LOS, cdboo.Product_ID
								) maxDecisionOverrides
								INNER JOIN
								(
									SELECT
										cdboo.Arrival_DT,
										p.Property_ID,
										p.Property_Name,
										cdboo.Accom_Type_ID,
										cdboo.Decision_ID,
										ac.Accom_Class_Name,
										cdboo.LOS,
										cdboo.New_Override,
										ac.Accom_Class_Code,
										CASE
											WHEN cdboo.New_Override = 'FLOORANDCEIL' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
														(CAST(CAST(cdboo.New_Floor_Rate * (1+(ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20)) + ',' +
														 CAST(CAST(cdboo.New_Ceil_Rate *(1+(ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20)))
													ELSE
														(CAST(CAST(cdboo.New_Floor_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20)) + ',' +
														 CAST(CAST(cdboo.New_Ceil_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20)))
													END
											WHEN cdboo.New_Override = 'CEIL' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
														CAST(CAST(cdboo.New_Ceil_Rate * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
													ELSE
														CAST(CAST(cdboo.New_Ceil_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20))
													END
											WHEN cdboo.New_Override = 'FLOOR' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
														CAST(CAST(cdboo.New_Floor_Rate * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
													ELSE
														CAST(CAST(cdboo.New_Floor_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20))
													END
											WHEN cdboo.New_Override = 'User' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
														CAST(CAST(cdboo.New_BAR * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
													ELSE
														CAST(CAST(cdboo.New_BAR + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20))
													END
											WHEN cdboo.New_Override = 'GPFLOORANDCEIL' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
															CAST(CAST(cdboo.New_Floor_Rate * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20)) + ',' +
															CAST(CAST(cdboo.New_Ceil_Rate * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
													ELSE
														(CAST(CAST(cdboo.New_Floor_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20)) + ',' +
														 CAST(CAST(cdboo.New_Ceil_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20)))
													END
											WHEN cdboo.New_Override = 'GPFLOOR' THEN
												CASE
													WHEN supplements.Supplement_Method = 1 THEN
														CAST(CAST(cdboo.New_Floor_Rate * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
													ELSE
														CAST(CAST(cdboo.New_Floor_Rate + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20))
													END
											END AS Rate_Code_Name,
										u.User_Name,
										cdboo.CreateDate,
										CASE
											WHEN supplements.Supplement_Method = 1 THEN
												CAST(CAST(cdboo.Old_BAR * (1 + (ISNULL(supplements.Supplement_Value, 0)/100)) AS NUMERIC(12,2)) AS NVARCHAR(20))
											ELSE
												CAST(CAST(cdboo.Old_BAR + ISNULL(supplements.Supplement_Value, 0) AS NUMERIC(12,2)) AS NVARCHAR(20))
										END AS Old_BAR,
										cdboo.Old_Override,
										at.Accom_Type_Name,
										u.EMAIL_ADDRESS,
										at.Accom_Type_Code,
										product.name as Product_Name,
										product.Display_Order
									FROM [dbo].[CP_Decision_Bar_Output_OVR] cdboo
										INNER JOIN [dbo].[Property] p ON cdboo.Property_Id = p.Property_ID
										INNER JOIN [dbo].[Accom_Type] at ON cdboo.Accom_Type_ID = at.Accom_Type_ID
										INNER JOIN [dbo].[Accom_Class] ac ON at.Accom_Class_ID = ac.Accom_Class_ID
										INNER JOIN [dbo].[Users] u ON cdboo.User_ID = u.User_ID
										INNER JOIN [dbo].[CP_Decision_Bar_Output] cdbo ON cdboo.Property_ID = cdbo.Property_ID AND cdboo.Product_ID = cdbo.Product_ID AND cdboo.Arrival_DT = cdbo.Arrival_DT AND cdboo.Accom_Type_ID = cdbo.Accom_Type_ID AND cdboo.LOS = cdbo.LOS AND cdbo.Override <> 'NONE'
										LEFT JOIN [dbo].[Product] product ON cdbo.Product_ID = product.Product_ID
										LEFT JOIN ufn_get_accom_type_supplements(@property_id,@start_date,@end_date) supplements ON cdboo.Arrival_DT = supplements.Arrival_DT and cdboo.Accom_Type_ID = supplements.Accom_Type_ID  and cdboo.Product_ID = supplements.Product_ID and supplements.Occupancy_Type = 1
									WHERE cdboo.Property_ID = @property_id AND cdboo.Arrival_DT between @start_date AND @end_date AND cdboo.New_Override <> 'Pending' AND cdbo.Product_ID IN (SELECT Value FROM varcharToInt(@products,','))
								) main
									ON
										maxDecisionOverrides.Arrival_DT = main.Arrival_DT and maxDecisionOverrides.Accom_Type_ID = main.Accom_Type_ID AND maxDecisionOverrides.LOS = main.LOS AND maxDecisionOverrides.Decision_Id = main.Decision_ID
								LEFT OUTER JOIN
								(
									SELECT Property_Id, Arrival_DT, Notes FROM ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Pricing')
								) AS notes ON main.Property_ID = notes.Property_Id AND main.Arrival_DT = notes.Arrival_DT
								LEFT JOIN (
									          SELECT
										          cdbo.Property_ID,
											          isLOS = CASE(COUNT(DISTINCT cdbo.LOS))
											                  WHEN 1 THEN 'false'
											                  ELSE 'true'
											                  END
									          FROM [dbo].[CP_Decision_Bar_Output] cdbo
									          WHERE cdbo.Property_ID = @property_id AND cdbo.Arrival_DT BETWEEN @start_date AND @end_date
									          GROUP BY cdbo.Property_ID
								          )AS byLOS ON main.Property_ID = byLOS.Property_ID
						) results_bar
					union all
					select
						DATENAME(dw,selectedProductsOverride.Occupancy_DT) as DOW,
						selectedProductsOverride.Occupancy_DT as Arrival_DT,
						(select p.Property_Name from Property p where p.Property_ID = @property_id) as Property_Name,
						selectedRoomTypes.Accom_Class_Name,
						-1 as LOS,
						'Specific' as New_Override,
						case when selectedProductsOverride.Offset_Method = 1 then CONCAT(selectedProductsOverride.Offset_Value, '%') else CONCAT(selectedProductsOverride.Offset_Value, '') end as Rate_Code_Name,
						selectedProductsOverride.User_Name,
						selectedProductsOverride.Created_DTTM as CreateDate,
						CAST(cpdbo.Final_BAR AS NUMERIC(12,2)) Old_BAR,
						cpdbo.Override as Old_Override,
						notes.notes as Notes,
						'false' as isLos,
						selectedRoomTypes.Accom_Type_Name,
						selectedRoomTypes.Accom_Class_Code,
						selectedProductsOverride.Email_Address,
						selectedRoomTypes.Accom_Type_Code,
						selectedProductsOverride.Product_Name,
						selectedProductsOverride.display_order,
                        selectedProductsOverride.Optimized_Offset_Value_Floor,
                        selectedProductsOverride.Optimized_Offset_Value_Ceil
					from
						(
								@selectedRoomTypesTable as selectedRoomTypes
								join
								@selectedProductsOverrideTable selectedProductsOverride
									on
										selectedRoomTypes.Accom_Class_ID = selectedProductsOverride.Accom_Class_ID
								join
								CP_Decision_Bar_Output cpdbo
									on
										cpdbo.Accom_Type_ID = selectedRoomTypes.Accom_Type_ID and
										cpdbo.Arrival_DT = selectedProductsOverride.Occupancy_DT and
										cpdbo.Product_ID = selectedProductsOverride.Product_ID
								LEFT OUTER JOIN
								(
									SELECT Property_Id, Arrival_DT, Notes FROM ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Pricing')
								) AS notes ON selectedProductsOverride.Occupancy_DT= notes.Arrival_DT and notes.property_id = @property_id
						)
						union all
						select DOW,Arrival_DT, Property_Name, Accom_Class_Name, LOS, New_Override, Rate_Code_Name, USER_NAME, CreateDate_DTTM as CreateDate, null as Old_BAR, Old_Override, Notes, 'false' as isLOS, roomTypeName as Accom_Type_Name,
							    Accom_Class_Code, EMAIL_ADDRESS, Accom_Type_Code, '' as Product_Name, null as display_order	, null as Optimized_Offset_Value_Floor , null as Optimized_Offset_Value_Ceil
							from ufn_get_main_restrict_highest_bar_data(@property_id,@start_date,@end_date,@isRollingDate,@rolling_start_date,@rolling_end_date) where upper(@isHighestBarRestricted) = 'TRUE'
			end
		else
			begin
				insert into @output_override_pricing_report
					select DATENAME(dw,finalData.Arrival_DT) as dow,
						finalData.Arrival_DT,
						p.Property_Name,
						AC.Accom_Class_Name,
						finalData.los,
						finalData.New_Override,
						   case when finalData.New_Override = 'FLOORANDCEIL'
							   then
								   rateUnqualified.Rate_Code_Name + ',' +
								   (
									   select Rate_Code_Name from Rate_Unqualified Rate_Unqualified
									   where finalData.new_ceil_rate_unqualified_ID=Rate_Unqualified.Rate_Unqualified_ID
								   )
						   else
							   rateUnqualified.Rate_Code_Name
						   end
						                                     as Rate_Code_Name,
						U.User_Name,
						finalData.CreateDate_DTTM,

						   (
						       select Rate_Code_Name  from  Rate_Unqualified Rate_Unqualified
						       where finalData.New_Rate_Unqualified_ID=Rate_Unqualified.Rate_Unqualified_ID
					       )

						                                     as Old_Rate_Code_Name,
						Old_Override,
						notesModule.notes,
						   isLos as isLOS,
						   '' as roomTypeName,
						AC.Accom_Class_Code,
						U.EMAIL_ADDRESS,
						   '' as Accom_Type_Code,
						   '' as Product_Name,
						   null as Display_order,
                           null as Optimized_Offset_Value_Floor , null as Optimized_Offset_Value_Ceil
					from
						(
							select allDataFromMainTable.Property_ID,allDataFromMainTable.Arrival_DT,allDataFromMainTable.LOS,allDataFromMainTable.Accom_Class_ID,
								 '' as Accom_Type_ID,
								 New_Override = CASE New_Override
								                WHEN 'User' THEN 'Specific'
								                else New_Override
								                END
								,Old_Override
								,Rate_Unqualified_ID = CASE New_Override
								                       WHEN 'FLOOR' THEN New_Floor_Rate_Unqualified_ID
								                       WHEN 'CEIL' THEN New_Ceil_Rate_Unqualified_ID
								                       WHEN 'FLOORANDCEIL' THEN New_Floor_Rate_Unqualified_ID
								                       else New_Rate_Unqualified_ID
								                       END
								,New_Rate_Unqualified_ID
								,allDataFromMainTable.User_ID
								,CreateDate_DTTM,
								 old_ceil_rate_unqualified_ID = Case old_Override
								                                WHEN 'FLOORANDCEIL' THEN Old_CEIL_Rate_Unqualified_ID
								                                else null
								                                end,
								 new_ceil_rate_unqualified_ID = Case new_Override
								                                WHEN 'FLOORANDCEIL' THEN new_CEIL_Rate_Unqualified_ID
								                                else null
								                                end
							from
								(
									select Arrival_DT,Accom_Class_Id,LOS, MAX(Decision_Id) Decision_Id
									from Decision_Bar_Output_OVR
									where Property_ID=@property_id and Arrival_DT between @start_date and @end_date
									group by Arrival_DT,Accom_Class_Id,LOS
								)
								mainData
								inner join
								(
									select * from Decision_Bar_Output_OVR where Property_ID=@property_id and Arrival_DT between @start_date and @end_date and New_Override <> 'Pending'
								)
								allDataFromMainTable
									on	mainData.Accom_Class_ID = allDataFromMainTable.Accom_Class_ID
									      and mainData.Arrival_DT = allDataFromMainTable.Arrival_DT
									      and mainData.Decision_Id = allDataFromMainTable.Decision_ID
									      and mainData.LOS = allDataFromMainTable.LOS
						)finalData
						inner join
						(
							select User_ID,USER_NAME,EMAIL_ADDRESS from Users
						) U on finalData.User_ID=U.User_ID
						inner join
						(
							select Accom_Class_ID,Accom_Class_Name,Accom_Class_Code from Accom_Class where Property_ID =@property_id
						) AC on finalData.Accom_Class_ID=AC.Accom_Class_ID
						inner join
						(
							select Property_Id,Property_Name from Property where Property_ID=@property_id
						) P on finalData.Property_ID=P.Property_ID
						inner join
						(
							select Rate_Unqualified_ID,Rate_Code_Name from Rate_Unqualified
						)rateUnqualified on finalData.Rate_Unqualified_ID=rateUnqualified.Rate_Unqualified_ID
						left outer join
						(
							select * from ufn_get_notes_by_module (@property_id,@start_date, @end_date,'Pricing')
						) as notesModule on finalData.Arrival_DT=notesModule.Arrival_DT and finalData.Property_ID=notesModule.property_id
						left join
						(
							select Property_ID,isLOS = case(count(distinct los ))
							                           when 1 then 'false'
							                           else 'true'
							                           end
							from Decision_Bar_Output
							where Arrival_DT between  @start_date and @end_date and Property_ID=@property_id
							group by Property_ID
						) as byLOS on finalData.Property_ID=byLOS.Property_ID
					union
					(
                    select *, '' as Product_Name, null as Display_Order, null as Optimized_Offset_Value_Floor , null as Optimized_Offset_Value_Ceil from ufn_get_main_restrict_highest_bar_data(@property_id,@start_date,@end_date,@isRollingDate,@rolling_start_date,@rolling_end_date) where upper(@isHighestBarRestricted) = 'TRUE'
					)
					order by Arrival_DT,Accom_Class_Name,los,CreateDate_DTTM desc
			end
		return
	end