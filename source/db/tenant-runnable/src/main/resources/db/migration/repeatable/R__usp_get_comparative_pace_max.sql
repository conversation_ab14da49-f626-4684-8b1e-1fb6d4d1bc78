if exists (select * from sys.objects where object_id = object_id(N'[usp_get_comparative_pace_max]'))
drop procedure usp_get_comparative_pace_max
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/***********************************************************************************************************************

Procedure Name: usp_get_comparative_pace_max

Migrate function (usp_get_comparative_pace_max) into stored procedure

Author: Maksood

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01/11/2023		Maksood				Shaikh					KANADA-927
18/01/2024		Maksood				Shaikh					KANADA-1301
----------	----------------	-------------------		-------------------------------
************************************************************************************************************************/
CREATE PROCEDURE [dbo].[usp_get_comparative_pace_max]

(
		@property_id int,
		@d1 date,
		@d2 date,
		@d3 date,
		@d4 date,
		@d5 date,
		@d6 date,
		@d7 date,
		@d8 date,
		@isHotel int,
		@isTransient int,
		@isGroup int,
		@fg1 int,
		@fg2 int,
		@fg3 int,
		@fg4 int,
		@fg5 int,
		@fg6 int,
		@fg7 int,
		@fg8 int,
		@fg9 int,
		@fg10 int,
		@fg11 int,
		@fg12 int,
		@fg13 int,
		@fg14 int,
		@fg15 int,
		@fg16 int,
		@fg17 int,
		@fg18 int,
		@fg19 int,
		@fg20 int,
		@fg21 int,
		@fg22 int,
		@fg23 int,
		@fg24 int,
		@fg25 int,
		@pace_days int,
		@isSold int,
		@isLrv int,
		@isOcFcst int,
		@bv1 int,
		@bv2 int,
		@bv3 int,
		@bv4 int,
		@bv5 int,
		@bv6 int,
		@bv7 int,
		@bv8 int,
		@bv9 int,
		@bv10 int,
		@bv11 int,
		@bv12 int,
		@bv13 int,
		@bv14 int,
		@bv15 int,
		@bv16 int,
		@bv17 int,
		@bv18 int,
		@bv19 int,
		@bv20 int,
		@bv21 int,
		@bv22 int,
		@bv23 int,
		@bv24 int,
		@bv25 int,
		@rc1 int,
		@rc2 int,
		@rc3 int,
		@rc4 int,
		@rc5 int,
		@rc6 int,
		@rc7 int,
		@rc8 int,
		@rc9 int,
		@rc10 int,
		@rc11 int,
		@rc12 int,
		@rc13 int,
		@rc14 int,
		@rc15 int,
		@rc16 int,
		@rc17 int,
		@rc18 int,
		@rc19 int,
		@rc20 int,
		@rc21 int,
		@rc22 int,
		@rc23 int,
		@rc24 int,
		@rc25 int,
		@includeInactiveRT int
)
			as
begin
			declare @daystoArrival int
			declare @d1_prop_sold int
			declare @d2_prop_sold int
			declare @d3_prop_sold int
			declare @d4_prop_sold int
			declare @d5_prop_sold int
			declare @d6_prop_sold int
			declare @d7_prop_sold int
			declare @d8_prop_sold int
			declare @d1_prop_lrv numeric(38)
			declare @d2_prop_lrv numeric(38)
			declare @d3_prop_lrv numeric(38)
			declare @d4_prop_lrv numeric(38)
			declare @d5_prop_lrv numeric(38)
			declare @d6_prop_lrv numeric(38)
			declare @d7_prop_lrv numeric(38)
			declare @d8_prop_lrv numeric(38)
			declare @d1_prop_occfcst numeric(38)
			declare @d2_prop_occfcst numeric(38)
			declare @d3_prop_occfcst numeric(38)
			declare @d4_prop_occfcst numeric(38)
			declare @d5_prop_occfcst numeric(38)
			declare @d6_prop_occfcst numeric(38)
			declare @d7_prop_occfcst numeric(38)
			declare @d8_prop_occfcst numeric(38)
			declare @d1_prop_occfcst_perc numeric(38)
			declare @d2_prop_occfcst_perc numeric(38)
			declare @d3_prop_occfcst_perc numeric(38)
			declare @d4_prop_occfcst_perc numeric(38)
			declare @d5_prop_occfcst_perc numeric(38)
			declare @d6_prop_occfcst_perc numeric(38)
			declare @d7_prop_occfcst_perc numeric(38)
			declare @d8_prop_occfcst_perc numeric(38)
			declare @d1_prop_occfcst_PhyCap_perc numeric(38)
			declare @d2_prop_occfcst_PhyCap_perc numeric(38)
			declare @d3_prop_occfcst_PhyCap_perc numeric(38)
			declare @d4_prop_occfcst_PhyCap_perc numeric(38)
			declare @d5_prop_occfcst_PhyCap_perc numeric(38)
			declare @d6_prop_occfcst_PhyCap_perc numeric(38)
			declare @d7_prop_occfcst_PhyCap_perc numeric(38)
			declare @d8_prop_occfcst_PhyCap_perc numeric(38)
			declare @d1_trans_sold int
			declare @d2_trans_sold int
			declare @d3_trans_sold int
			declare @d4_trans_sold int
			declare @d5_trans_sold int
			declare @d6_trans_sold int
			declare @d7_trans_sold int
			declare @d8_trans_sold int
			declare @d1_trans_occfcst numeric(38)
			declare @d2_trans_occfcst numeric(38)
			declare @d3_trans_occfcst numeric(38)
			declare @d4_trans_occfcst numeric(38)
			declare @d5_trans_occfcst numeric(38)
			declare @d6_trans_occfcst numeric(38)
			declare @d7_trans_occfcst numeric(38)
			declare @d8_trans_occfcst numeric(38)
			declare @d1_grp_sold int
			declare @d2_grp_sold int
			declare @d3_grp_sold int
			declare @d4_grp_sold int
			declare @d5_grp_sold int
			declare @d6_grp_sold int
			declare @d7_grp_sold int
			declare @d8_grp_sold int
			declare @d1_grp_occfcst numeric(38)
			declare @d2_grp_occfcst numeric(38)
			declare @d3_grp_occfcst numeric(38)
			declare @d4_grp_occfcst numeric(38)
			declare @d5_grp_occfcst numeric(38)
			declare @d6_grp_occfcst numeric(38)
			declare @d7_grp_occfcst numeric(38)
			declare @d8_grp_occfcst numeric(38)
			declare @d1_fg1_sold int
			declare @d2_fg1_sold int
			declare @d3_fg1_sold int
			declare @d4_fg1_sold int
			declare @d5_fg1_sold int
			declare @d6_fg1_sold int
			declare @d7_fg1_sold int
			declare @d8_fg1_sold int
			declare @d1_fg1_occfcst numeric(38)
			declare @d2_fg1_occfcst numeric(38)
			declare @d3_fg1_occfcst numeric(38)
			declare @d4_fg1_occfcst numeric(38)
			declare @d5_fg1_occfcst numeric(38)
			declare @d6_fg1_occfcst numeric(38)
			declare @d7_fg1_occfcst numeric(38)
			declare @d8_fg1_occfcst numeric(38)
			declare @d1_fg2_sold int
			declare @d2_fg2_sold int
			declare @d3_fg2_sold int
			declare @d4_fg2_sold int
			declare @d5_fg2_sold int
			declare @d6_fg2_sold int
			declare @d7_fg2_sold int
			declare @d8_fg2_sold int
			declare @d1_fg2_occfcst numeric(38)
			declare @d2_fg2_occfcst numeric(38)
			declare @d3_fg2_occfcst numeric(38)
			declare @d4_fg2_occfcst numeric(38)
			declare @d5_fg2_occfcst numeric(38)
			declare @d6_fg2_occfcst numeric(38)
			declare @d7_fg2_occfcst numeric(38)
			declare @d8_fg2_occfcst numeric(38)
			declare @d1_fg3_sold int
			declare @d2_fg3_sold int
			declare @d3_fg3_sold int
			declare @d4_fg3_sold int
			declare @d5_fg3_sold int
			declare @d6_fg3_sold int
			declare @d7_fg3_sold int
			declare @d8_fg3_sold int
			declare @d1_fg3_occfcst numeric(38)
			declare @d2_fg3_occfcst numeric(38)
			declare @d3_fg3_occfcst numeric(38)
			declare @d4_fg3_occfcst numeric(38)
			declare @d5_fg3_occfcst numeric(38)
			declare @d6_fg3_occfcst numeric(38)
			declare @d7_fg3_occfcst numeric(38)
			declare @d8_fg3_occfcst numeric(38)
			declare @d1_fg4_sold int
			declare @d2_fg4_sold int
			declare @d3_fg4_sold int
			declare @d4_fg4_sold int
			declare @d5_fg4_sold int
			declare @d6_fg4_sold int
			declare @d7_fg4_sold int
			declare @d8_fg4_sold int
			declare @d1_fg4_occfcst numeric(38)
			declare @d2_fg4_occfcst numeric(38)
			declare @d3_fg4_occfcst numeric(38)
			declare @d4_fg4_occfcst numeric(38)
			declare @d5_fg4_occfcst numeric(38)
			declare @d6_fg4_occfcst numeric(38)
			declare @d7_fg4_occfcst numeric(38)
			declare @d8_fg4_occfcst numeric(38)
			declare @d1_fg5_sold int
			declare @d2_fg5_sold int
			declare @d3_fg5_sold int
			declare @d4_fg5_sold int
			declare @d5_fg5_sold int
			declare @d6_fg5_sold int
			declare @d7_fg5_sold int
			declare @d8_fg5_sold int
			declare @d1_fg5_occfcst numeric(38)
			declare @d2_fg5_occfcst numeric(38)
			declare @d3_fg5_occfcst numeric(38)
			declare @d4_fg5_occfcst numeric(38)
			declare @d5_fg5_occfcst numeric(38)
			declare @d6_fg5_occfcst numeric(38)
			declare @d7_fg5_occfcst numeric(38)
			declare @d8_fg5_occfcst numeric(38)
			declare @d1_fg6_sold int
			declare @d2_fg6_sold int
			declare @d3_fg6_sold int
			declare @d4_fg6_sold int
			declare @d5_fg6_sold int
			declare @d6_fg6_sold int
			declare @d7_fg6_sold int
			declare @d8_fg6_sold int
			declare @d1_fg6_occfcst numeric(38)
			declare @d2_fg6_occfcst numeric(38)
			declare @d3_fg6_occfcst numeric(38)
			declare @d4_fg6_occfcst numeric(38)
			declare @d5_fg6_occfcst numeric(38)
			declare @d6_fg6_occfcst numeric(38)
			declare @d7_fg6_occfcst numeric(38)
			declare @d8_fg6_occfcst numeric(38)
			declare @d1_fg7_sold int
			declare @d2_fg7_sold int
			declare @d3_fg7_sold int
			declare @d4_fg7_sold int
			declare @d5_fg7_sold int
			declare @d6_fg7_sold int
			declare @d7_fg7_sold int
			declare @d8_fg7_sold int
			declare @d1_fg7_occfcst numeric(38)
			declare @d2_fg7_occfcst numeric(38)
			declare @d3_fg7_occfcst numeric(38)
			declare @d4_fg7_occfcst numeric(38)
			declare @d5_fg7_occfcst numeric(38)
			declare @d6_fg7_occfcst numeric(38)
			declare @d7_fg7_occfcst numeric(38)
			declare @d8_fg7_occfcst numeric(38)
			declare @d1_fg8_sold int
			declare @d2_fg8_sold int
			declare @d3_fg8_sold int
			declare @d4_fg8_sold int
			declare @d5_fg8_sold int
			declare @d6_fg8_sold int
			declare @d7_fg8_sold int
			declare @d8_fg8_sold int
			declare @d1_fg8_occfcst numeric(38)
			declare @d2_fg8_occfcst numeric(38)
			declare @d3_fg8_occfcst numeric(38)
			declare @d4_fg8_occfcst numeric(38)
			declare @d5_fg8_occfcst numeric(38)
			declare @d6_fg8_occfcst numeric(38)
			declare @d7_fg8_occfcst numeric(38)
			declare @d8_fg8_occfcst numeric(38)
			declare @d1_fg9_sold int
			declare @d2_fg9_sold int
			declare @d3_fg9_sold int
			declare @d4_fg9_sold int
			declare @d5_fg9_sold int
			declare @d6_fg9_sold int
			declare @d7_fg9_sold int
			declare @d8_fg9_sold int
			declare @d1_fg9_occfcst numeric(38)
			declare @d2_fg9_occfcst numeric(38)
			declare @d3_fg9_occfcst numeric(38)
			declare @d4_fg9_occfcst numeric(38)
			declare @d5_fg9_occfcst numeric(38)
			declare @d6_fg9_occfcst numeric(38)
			declare @d7_fg9_occfcst numeric(38)
			declare @d8_fg9_occfcst numeric(38)
			declare @d1_fg10_sold int
			declare @d2_fg10_sold int
			declare @d3_fg10_sold int
			declare @d4_fg10_sold int
			declare @d5_fg10_sold int
			declare @d6_fg10_sold int
			declare @d7_fg10_sold int
			declare @d8_fg10_sold int
			declare @d1_fg10_occfcst numeric(38)
			declare @d2_fg10_occfcst numeric(38)
			declare @d3_fg10_occfcst numeric(38)
			declare @d4_fg10_occfcst numeric(38)
			declare @d5_fg10_occfcst numeric(38)
			declare @d6_fg10_occfcst numeric(38)
			declare @d7_fg10_occfcst numeric(38)
			declare @d8_fg10_occfcst numeric(38)
			declare @d1_fg11_sold int
			declare @d2_fg11_sold int
			declare @d3_fg11_sold int
			declare @d4_fg11_sold int
			declare @d5_fg11_sold int
			declare @d6_fg11_sold int
			declare @d7_fg11_sold int
			declare @d8_fg11_sold int
			declare @d1_fg11_occfcst numeric(38)
			declare @d2_fg11_occfcst numeric(38)
			declare @d3_fg11_occfcst numeric(38)
			declare @d4_fg11_occfcst numeric(38)
			declare @d5_fg11_occfcst numeric(38)
			declare @d6_fg11_occfcst numeric(38)
			declare @d7_fg11_occfcst numeric(38)
			declare @d8_fg11_occfcst numeric(38)
			declare @d1_fg12_sold int
			declare @d2_fg12_sold int
			declare @d3_fg12_sold int
			declare @d4_fg12_sold int
			declare @d5_fg12_sold int
			declare @d6_fg12_sold int
			declare @d7_fg12_sold int
			declare @d8_fg12_sold int
			declare @d1_fg12_occfcst numeric(38)
			declare @d2_fg12_occfcst numeric(38)
			declare @d3_fg12_occfcst numeric(38)
			declare @d4_fg12_occfcst numeric(38)
			declare @d5_fg12_occfcst numeric(38)
			declare @d6_fg12_occfcst numeric(38)
			declare @d7_fg12_occfcst numeric(38)
			declare @d8_fg12_occfcst numeric(38)
			declare @d1_fg13_sold int
			declare @d2_fg13_sold int
			declare @d3_fg13_sold int
			declare @d4_fg13_sold int
			declare @d5_fg13_sold int
			declare @d6_fg13_sold int
			declare @d7_fg13_sold int
			declare @d8_fg13_sold int
			declare @d1_fg13_occfcst numeric(38)
			declare @d2_fg13_occfcst numeric(38)
			declare @d3_fg13_occfcst numeric(38)
			declare @d4_fg13_occfcst numeric(38)
			declare @d5_fg13_occfcst numeric(38)
			declare @d6_fg13_occfcst numeric(38)
			declare @d7_fg13_occfcst numeric(38)
			declare @d8_fg13_occfcst numeric(38)
			declare @d1_fg14_sold int
			declare @d2_fg14_sold int
			declare @d3_fg14_sold int
			declare @d4_fg14_sold int
			declare @d5_fg14_sold int
			declare @d6_fg14_sold int
			declare @d7_fg14_sold int
			declare @d8_fg14_sold int
			declare @d1_fg14_occfcst numeric(38)
			declare @d2_fg14_occfcst numeric(38)
			declare @d3_fg14_occfcst numeric(38)
			declare @d4_fg14_occfcst numeric(38)
			declare @d5_fg14_occfcst numeric(38)
			declare @d6_fg14_occfcst numeric(38)
			declare @d7_fg14_occfcst numeric(38)
			declare @d8_fg14_occfcst numeric(38)
			declare @d1_fg15_sold int
			declare @d2_fg15_sold int
			declare @d3_fg15_sold int
			declare @d4_fg15_sold int
			declare @d5_fg15_sold int
			declare @d6_fg15_sold int
			declare @d7_fg15_sold int
			declare @d8_fg15_sold int
			declare @d1_fg15_occfcst numeric(38)
			declare @d2_fg15_occfcst numeric(38)
			declare @d3_fg15_occfcst numeric(38)
			declare @d4_fg15_occfcst numeric(38)
			declare @d5_fg15_occfcst numeric(38)
			declare @d6_fg15_occfcst numeric(38)
			declare @d7_fg15_occfcst numeric(38)
			declare @d8_fg15_occfcst numeric(38)
			declare @d1_fg16_sold int
			declare @d2_fg16_sold int
			declare @d3_fg16_sold int
			declare @d4_fg16_sold int
			declare @d5_fg16_sold int
			declare @d6_fg16_sold int
			declare @d7_fg16_sold int
			declare @d8_fg16_sold int
			declare @d1_fg16_occfcst numeric(38)
			declare @d2_fg16_occfcst numeric(38)
			declare @d3_fg16_occfcst numeric(38)
			declare @d4_fg16_occfcst numeric(38)
			declare @d5_fg16_occfcst numeric(38)
			declare @d6_fg16_occfcst numeric(38)
			declare @d7_fg16_occfcst numeric(38)
			declare @d8_fg16_occfcst numeric(38)
			declare @d1_fg17_sold int
			declare @d2_fg17_sold int
			declare @d3_fg17_sold int
			declare @d4_fg17_sold int
			declare @d5_fg17_sold int
			declare @d6_fg17_sold int
			declare @d7_fg17_sold int
			declare @d8_fg17_sold int
			declare @d1_fg17_occfcst numeric(38)
			declare @d2_fg17_occfcst numeric(38)
			declare @d3_fg17_occfcst numeric(38)
			declare @d4_fg17_occfcst numeric(38)
			declare @d5_fg17_occfcst numeric(38)
			declare @d6_fg17_occfcst numeric(38)
			declare @d7_fg17_occfcst numeric(38)
			declare @d8_fg17_occfcst numeric(38)
			declare @d1_fg18_sold int
			declare @d2_fg18_sold int
			declare @d3_fg18_sold int
			declare @d4_fg18_sold int
			declare @d5_fg18_sold int
			declare @d6_fg18_sold int
			declare @d7_fg18_sold int
			declare @d8_fg18_sold int
			declare @d1_fg18_occfcst numeric(38)
			declare @d2_fg18_occfcst numeric(38)
			declare @d3_fg18_occfcst numeric(38)
			declare @d4_fg18_occfcst numeric(38)
			declare @d5_fg18_occfcst numeric(38)
			declare @d6_fg18_occfcst numeric(38)
			declare @d7_fg18_occfcst numeric(38)
			declare @d8_fg18_occfcst numeric(38)
			declare @d1_fg19_sold int
			declare @d2_fg19_sold int
			declare @d3_fg19_sold int
			declare @d4_fg19_sold int
			declare @d5_fg19_sold int
			declare @d6_fg19_sold int
			declare @d7_fg19_sold int
			declare @d8_fg19_sold int
			declare @d1_fg19_occfcst numeric(38)
			declare @d2_fg19_occfcst numeric(38)
			declare @d3_fg19_occfcst numeric(38)
			declare @d4_fg19_occfcst numeric(38)
			declare @d5_fg19_occfcst numeric(38)
			declare @d6_fg19_occfcst numeric(38)
			declare @d7_fg19_occfcst numeric(38)
			declare @d8_fg19_occfcst numeric(38)
			declare @d1_fg20_sold int
			declare @d2_fg20_sold int
			declare @d3_fg20_sold int
			declare @d4_fg20_sold int
			declare @d5_fg20_sold int
			declare @d6_fg20_sold int
			declare @d7_fg20_sold int
			declare @d8_fg20_sold int
			declare @d1_fg20_occfcst numeric(38)
			declare @d2_fg20_occfcst numeric(38)
			declare @d3_fg20_occfcst numeric(38)
			declare @d4_fg20_occfcst numeric(38)
			declare @d5_fg20_occfcst numeric(38)
			declare @d6_fg20_occfcst numeric(38)
			declare @d7_fg20_occfcst numeric(38)
			declare @d8_fg20_occfcst numeric(38)
			declare @d1_fg21_sold int
			declare @d2_fg21_sold int
			declare @d3_fg21_sold int
			declare @d4_fg21_sold int
			declare @d5_fg21_sold int
			declare @d6_fg21_sold int
			declare @d7_fg21_sold int
			declare @d8_fg21_sold int
			declare @d1_fg21_occfcst numeric(38)
			declare @d2_fg21_occfcst numeric(38)
			declare @d3_fg21_occfcst numeric(38)
			declare @d4_fg21_occfcst numeric(38)
			declare @d5_fg21_occfcst numeric(38)
			declare @d6_fg21_occfcst numeric(38)
			declare @d7_fg21_occfcst numeric(38)
			declare @d8_fg21_occfcst numeric(38)
			declare @d1_fg22_sold int
			declare @d2_fg22_sold int
			declare @d3_fg22_sold int
			declare @d4_fg22_sold int
			declare @d5_fg22_sold int
			declare @d6_fg22_sold int
			declare @d7_fg22_sold int
			declare @d8_fg22_sold int
			declare @d1_fg22_occfcst numeric(38)
			declare @d2_fg22_occfcst numeric(38)
			declare @d3_fg22_occfcst numeric(38)
			declare @d4_fg22_occfcst numeric(38)
			declare @d5_fg22_occfcst numeric(38)
			declare @d6_fg22_occfcst numeric(38)
			declare @d7_fg22_occfcst numeric(38)
			declare @d8_fg22_occfcst numeric(38)
			declare @d1_fg23_sold int
			declare @d2_fg23_sold int
			declare @d3_fg23_sold int
			declare @d4_fg23_sold int
			declare @d5_fg23_sold int
			declare @d6_fg23_sold int
			declare @d7_fg23_sold int
			declare @d8_fg23_sold int
			declare @d1_fg23_occfcst numeric(38)
			declare @d2_fg23_occfcst numeric(38)
			declare @d3_fg23_occfcst numeric(38)
			declare @d4_fg23_occfcst numeric(38)
			declare @d5_fg23_occfcst numeric(38)
			declare @d6_fg23_occfcst numeric(38)
			declare @d7_fg23_occfcst numeric(38)
			declare @d8_fg23_occfcst numeric(38)
			declare @d1_fg24_sold int
			declare @d2_fg24_sold int
			declare @d3_fg24_sold int
			declare @d4_fg24_sold int
			declare @d5_fg24_sold int
			declare @d6_fg24_sold int
			declare @d7_fg24_sold int
			declare @d8_fg24_sold int
			declare @d1_fg24_occfcst numeric(38)
			declare @d2_fg24_occfcst numeric(38)
			declare @d3_fg24_occfcst numeric(38)
			declare @d4_fg24_occfcst numeric(38)
			declare @d5_fg24_occfcst numeric(38)
			declare @d6_fg24_occfcst numeric(38)
			declare @d7_fg24_occfcst numeric(38)
			declare @d8_fg24_occfcst numeric(38)
			declare @d1_fg25_sold int
			declare @d2_fg25_sold int
			declare @d3_fg25_sold int
			declare @d4_fg25_sold int
			declare @d5_fg25_sold int
			declare @d6_fg25_sold int
			declare @d7_fg25_sold int
			declare @d8_fg25_sold int
			declare @d1_fg25_occfcst numeric(38)
			declare @d2_fg25_occfcst numeric(38)
			declare @d3_fg25_occfcst numeric(38)
			declare @d4_fg25_occfcst numeric(38)
			declare @d5_fg25_occfcst numeric(38)
			declare @d6_fg25_occfcst numeric(38)
			declare @d7_fg25_occfcst numeric(38)
			declare @d8_fg25_occfcst numeric(38)
			declare @d1_bv1_sold int
			declare @d2_bv1_sold int
			declare @d3_bv1_sold int
			declare @d4_bv1_sold int
			declare @d5_bv1_sold int
			declare @d6_bv1_sold int
			declare @d7_bv1_sold int
			declare @d8_bv1_sold int
			declare @d1_bv1_occfcst numeric(38)
			declare @d2_bv1_occfcst numeric(38)
			declare @d3_bv1_occfcst numeric(38)
			declare @d4_bv1_occfcst numeric(38)
			declare @d5_bv1_occfcst numeric(38)
			declare @d6_bv1_occfcst numeric(38)
			declare @d7_bv1_occfcst numeric(38)
			declare @d8_bv1_occfcst numeric(38)
			declare @d1_bv2_sold int
			declare @d2_bv2_sold int
			declare @d3_bv2_sold int
			declare @d4_bv2_sold int
			declare @d5_bv2_sold int
			declare @d6_bv2_sold int
			declare @d7_bv2_sold int
			declare @d8_bv2_sold int
			declare @d1_bv2_occfcst numeric(38)
			declare @d2_bv2_occfcst numeric(38)
			declare @d3_bv2_occfcst numeric(38)
			declare @d4_bv2_occfcst numeric(38)
			declare @d5_bv2_occfcst numeric(38)
			declare @d6_bv2_occfcst numeric(38)
			declare @d7_bv2_occfcst numeric(38)
			declare @d8_bv2_occfcst numeric(38)
			declare @d1_bv3_sold int
			declare @d2_bv3_sold int
			declare @d3_bv3_sold int
			declare @d4_bv3_sold int
			declare @d5_bv3_sold int
			declare @d6_bv3_sold int
			declare @d7_bv3_sold int
			declare @d8_bv3_sold int
			declare @d1_bv3_occfcst numeric(38)
			declare @d2_bv3_occfcst numeric(38)
			declare @d3_bv3_occfcst numeric(38)
			declare @d4_bv3_occfcst numeric(38)
			declare @d5_bv3_occfcst numeric(38)
			declare @d6_bv3_occfcst numeric(38)
			declare @d7_bv3_occfcst numeric(38)
			declare @d8_bv3_occfcst numeric(38)
			declare @d1_bv4_sold int
			declare @d2_bv4_sold int
			declare @d3_bv4_sold int
			declare @d4_bv4_sold int
			declare @d5_bv4_sold int
			declare @d6_bv4_sold int
			declare @d7_bv4_sold int
			declare @d8_bv4_sold int
			declare @d1_bv4_occfcst numeric(38)
			declare @d2_bv4_occfcst numeric(38)
			declare @d3_bv4_occfcst numeric(38)
			declare @d4_bv4_occfcst numeric(38)
			declare @d5_bv4_occfcst numeric(38)
			declare @d6_bv4_occfcst numeric(38)
			declare @d7_bv4_occfcst numeric(38)
			declare @d8_bv4_occfcst numeric(38)
			declare @d1_bv5_sold int
			declare @d2_bv5_sold int
			declare @d3_bv5_sold int
			declare @d4_bv5_sold int
			declare @d5_bv5_sold int
			declare @d6_bv5_sold int
			declare @d7_bv5_sold int
			declare @d8_bv5_sold int
			declare @d1_bv5_occfcst numeric(38)
			declare @d2_bv5_occfcst numeric(38)
			declare @d3_bv5_occfcst numeric(38)
			declare @d4_bv5_occfcst numeric(38)
			declare @d5_bv5_occfcst numeric(38)
			declare @d6_bv5_occfcst numeric(38)
			declare @d7_bv5_occfcst numeric(38)
			declare @d8_bv5_occfcst numeric(38)
			declare @d1_bv6_sold int
			declare @d2_bv6_sold int
			declare @d3_bv6_sold int
			declare @d4_bv6_sold int
			declare @d5_bv6_sold int
			declare @d6_bv6_sold int
			declare @d7_bv6_sold int
			declare @d8_bv6_sold int
			declare @d1_bv6_occfcst numeric(38)
			declare @d2_bv6_occfcst numeric(38)
			declare @d3_bv6_occfcst numeric(38)
			declare @d4_bv6_occfcst numeric(38)
			declare @d5_bv6_occfcst numeric(38)
			declare @d6_bv6_occfcst numeric(38)
			declare @d7_bv6_occfcst numeric(38)
			declare @d8_bv6_occfcst numeric(38)
			declare @d1_bv7_sold int
			declare @d2_bv7_sold int
			declare @d3_bv7_sold int
			declare @d4_bv7_sold int
			declare @d5_bv7_sold int
			declare @d6_bv7_sold int
			declare @d7_bv7_sold int
			declare @d8_bv7_sold int
			declare @d1_bv7_occfcst numeric(38)
			declare @d2_bv7_occfcst numeric(38)
			declare @d3_bv7_occfcst numeric(38)
			declare @d4_bv7_occfcst numeric(38)
			declare @d5_bv7_occfcst numeric(38)
			declare @d6_bv7_occfcst numeric(38)
			declare @d7_bv7_occfcst numeric(38)
			declare @d8_bv7_occfcst numeric(38)
			declare @d1_bv8_sold int
			declare @d2_bv8_sold int
			declare @d3_bv8_sold int
			declare @d4_bv8_sold int
			declare @d5_bv8_sold int
			declare @d6_bv8_sold int
			declare @d7_bv8_sold int
			declare @d8_bv8_sold int
			declare @d1_bv8_occfcst numeric(38)
			declare @d2_bv8_occfcst numeric(38)
			declare @d3_bv8_occfcst numeric(38)
			declare @d4_bv8_occfcst numeric(38)
			declare @d5_bv8_occfcst numeric(38)
			declare @d6_bv8_occfcst numeric(38)
			declare @d7_bv8_occfcst numeric(38)
			declare @d8_bv8_occfcst numeric(38)
			declare @d1_bv9_sold int
			declare @d2_bv9_sold int
			declare @d3_bv9_sold int
			declare @d4_bv9_sold int
			declare @d5_bv9_sold int
			declare @d6_bv9_sold int
			declare @d7_bv9_sold int
			declare @d8_bv9_sold int
			declare @d1_bv9_occfcst numeric(38)
			declare @d2_bv9_occfcst numeric(38)
			declare @d3_bv9_occfcst numeric(38)
			declare @d4_bv9_occfcst numeric(38)
			declare @d5_bv9_occfcst numeric(38)
			declare @d6_bv9_occfcst numeric(38)
			declare @d7_bv9_occfcst numeric(38)
			declare @d8_bv9_occfcst numeric(38)
			declare @d1_bv10_sold int
			declare @d2_bv10_sold int
			declare @d3_bv10_sold int
			declare @d4_bv10_sold int
			declare @d5_bv10_sold int
			declare @d6_bv10_sold int
			declare @d7_bv10_sold int
			declare @d8_bv10_sold int
			declare @d1_bv10_occfcst numeric(38)
			declare @d2_bv10_occfcst numeric(38)
			declare @d3_bv10_occfcst numeric(38)
			declare @d4_bv10_occfcst numeric(38)
			declare @d5_bv10_occfcst numeric(38)
			declare @d6_bv10_occfcst numeric(38)
			declare @d7_bv10_occfcst numeric(38)
			declare @d8_bv10_occfcst numeric(38)
			declare @d1_bv11_sold int
			declare @d2_bv11_sold int
			declare @d3_bv11_sold int
			declare @d4_bv11_sold int
			declare @d5_bv11_sold int
			declare @d6_bv11_sold int
			declare @d7_bv11_sold int
			declare @d8_bv11_sold int
			declare @d1_bv11_occfcst numeric(38)
			declare @d2_bv11_occfcst numeric(38)
			declare @d3_bv11_occfcst numeric(38)
			declare @d4_bv11_occfcst numeric(38)
			declare @d5_bv11_occfcst numeric(38)
			declare @d6_bv11_occfcst numeric(38)
			declare @d7_bv11_occfcst numeric(38)
			declare @d8_bv11_occfcst numeric(38)
			declare @d1_bv12_sold int
			declare @d2_bv12_sold int
			declare @d3_bv12_sold int
			declare @d4_bv12_sold int
			declare @d5_bv12_sold int
			declare @d6_bv12_sold int
			declare @d7_bv12_sold int
			declare @d8_bv12_sold int
			declare @d1_bv12_occfcst numeric(38)
			declare @d2_bv12_occfcst numeric(38)
			declare @d3_bv12_occfcst numeric(38)
			declare @d4_bv12_occfcst numeric(38)
			declare @d5_bv12_occfcst numeric(38)
			declare @d6_bv12_occfcst numeric(38)
			declare @d7_bv12_occfcst numeric(38)
			declare @d8_bv12_occfcst numeric(38)
			declare @d1_bv13_sold int
			declare @d2_bv13_sold int
			declare @d3_bv13_sold int
			declare @d4_bv13_sold int
			declare @d5_bv13_sold int
			declare @d6_bv13_sold int
			declare @d7_bv13_sold int
			declare @d8_bv13_sold int
			declare @d1_bv13_occfcst numeric(38)
			declare @d2_bv13_occfcst numeric(38)
			declare @d3_bv13_occfcst numeric(38)
			declare @d4_bv13_occfcst numeric(38)
			declare @d5_bv13_occfcst numeric(38)
			declare @d6_bv13_occfcst numeric(38)
			declare @d7_bv13_occfcst numeric(38)
			declare @d8_bv13_occfcst numeric(38)
			declare @d1_bv14_sold int
			declare @d2_bv14_sold int
			declare @d3_bv14_sold int
			declare @d4_bv14_sold int
			declare @d5_bv14_sold int
			declare @d6_bv14_sold int
			declare @d7_bv14_sold int
			declare @d8_bv14_sold int
			declare @d1_bv14_occfcst numeric(38)
			declare @d2_bv14_occfcst numeric(38)
			declare @d3_bv14_occfcst numeric(38)
			declare @d4_bv14_occfcst numeric(38)
			declare @d5_bv14_occfcst numeric(38)
			declare @d6_bv14_occfcst numeric(38)
			declare @d7_bv14_occfcst numeric(38)
			declare @d8_bv14_occfcst numeric(38)
			declare @d1_bv15_sold int
			declare @d2_bv15_sold int
			declare @d3_bv15_sold int
			declare @d4_bv15_sold int
			declare @d5_bv15_sold int
			declare @d6_bv15_sold int
			declare @d7_bv15_sold int
			declare @d8_bv15_sold int
			declare @d1_bv15_occfcst numeric(38)
			declare @d2_bv15_occfcst numeric(38)
			declare @d3_bv15_occfcst numeric(38)
			declare @d4_bv15_occfcst numeric(38)
			declare @d5_bv15_occfcst numeric(38)
			declare @d6_bv15_occfcst numeric(38)
			declare @d7_bv15_occfcst numeric(38)
			declare @d8_bv15_occfcst numeric(38)
			declare @d1_bv16_sold int
			declare @d2_bv16_sold int
			declare @d3_bv16_sold int
			declare @d4_bv16_sold int
			declare @d5_bv16_sold int
			declare @d6_bv16_sold int
			declare @d7_bv16_sold int
			declare @d8_bv16_sold int
			declare @d1_bv16_occfcst numeric(38)
			declare @d2_bv16_occfcst numeric(38)
			declare @d3_bv16_occfcst numeric(38)
			declare @d4_bv16_occfcst numeric(38)
			declare @d5_bv16_occfcst numeric(38)
			declare @d6_bv16_occfcst numeric(38)
			declare @d7_bv16_occfcst numeric(38)
			declare @d8_bv16_occfcst numeric(38)
			declare @d1_bv17_sold int
			declare @d2_bv17_sold int
			declare @d3_bv17_sold int
			declare @d4_bv17_sold int
			declare @d5_bv17_sold int
			declare @d6_bv17_sold int
			declare @d7_bv17_sold int
			declare @d8_bv17_sold int
			declare @d1_bv17_occfcst numeric(38)
			declare @d2_bv17_occfcst numeric(38)
			declare @d3_bv17_occfcst numeric(38)
			declare @d4_bv17_occfcst numeric(38)
			declare @d5_bv17_occfcst numeric(38)
			declare @d6_bv17_occfcst numeric(38)
			declare @d7_bv17_occfcst numeric(38)
			declare @d8_bv17_occfcst numeric(38)
			declare @d1_bv18_sold int
			declare @d2_bv18_sold int
			declare @d3_bv18_sold int
			declare @d4_bv18_sold int
			declare @d5_bv18_sold int
			declare @d6_bv18_sold int
			declare @d7_bv18_sold int
			declare @d8_bv18_sold int
			declare @d1_bv18_occfcst numeric(38)
			declare @d2_bv18_occfcst numeric(38)
			declare @d3_bv18_occfcst numeric(38)
			declare @d4_bv18_occfcst numeric(38)
			declare @d5_bv18_occfcst numeric(38)
			declare @d6_bv18_occfcst numeric(38)
			declare @d7_bv18_occfcst numeric(38)
			declare @d8_bv18_occfcst numeric(38)
			declare @d1_bv19_sold int
			declare @d2_bv19_sold int
			declare @d3_bv19_sold int
			declare @d4_bv19_sold int
			declare @d5_bv19_sold int
			declare @d6_bv19_sold int
			declare @d7_bv19_sold int
			declare @d8_bv19_sold int
			declare @d1_bv19_occfcst numeric(38)
			declare @d2_bv19_occfcst numeric(38)
			declare @d3_bv19_occfcst numeric(38)
			declare @d4_bv19_occfcst numeric(38)
			declare @d5_bv19_occfcst numeric(38)
			declare @d6_bv19_occfcst numeric(38)
			declare @d7_bv19_occfcst numeric(38)
			declare @d8_bv19_occfcst numeric(38)
			declare @d1_bv20_sold int
			declare @d2_bv20_sold int
			declare @d3_bv20_sold int
			declare @d4_bv20_sold int
			declare @d5_bv20_sold int
			declare @d6_bv20_sold int
			declare @d7_bv20_sold int
			declare @d8_bv20_sold int
			declare @d1_bv20_occfcst numeric(38)
			declare @d2_bv20_occfcst numeric(38)
			declare @d3_bv20_occfcst numeric(38)
			declare @d4_bv20_occfcst numeric(38)
			declare @d5_bv20_occfcst numeric(38)
			declare @d6_bv20_occfcst numeric(38)
			declare @d7_bv20_occfcst numeric(38)
			declare @d8_bv20_occfcst numeric(38)
			declare @d1_bv21_sold int
			declare @d2_bv21_sold int
			declare @d3_bv21_sold int
			declare @d4_bv21_sold int
			declare @d5_bv21_sold int
			declare @d6_bv21_sold int
			declare @d7_bv21_sold int
			declare @d8_bv21_sold int
			declare @d1_bv21_occfcst numeric(38)
			declare @d2_bv21_occfcst numeric(38)
			declare @d3_bv21_occfcst numeric(38)
			declare @d4_bv21_occfcst numeric(38)
			declare @d5_bv21_occfcst numeric(38)
			declare @d6_bv21_occfcst numeric(38)
			declare @d7_bv21_occfcst numeric(38)
			declare @d8_bv21_occfcst numeric(38)
			declare @d1_bv22_sold int
			declare @d2_bv22_sold int
			declare @d3_bv22_sold int
			declare @d4_bv22_sold int
			declare @d5_bv22_sold int
			declare @d6_bv22_sold int
			declare @d7_bv22_sold int
			declare @d8_bv22_sold int
			declare @d1_bv22_occfcst numeric(38)
			declare @d2_bv22_occfcst numeric(38)
			declare @d3_bv22_occfcst numeric(38)
			declare @d4_bv22_occfcst numeric(38)
			declare @d5_bv22_occfcst numeric(38)
			declare @d6_bv22_occfcst numeric(38)
			declare @d7_bv22_occfcst numeric(38)
			declare @d8_bv22_occfcst numeric(38)
			declare @d1_bv23_sold int
			declare @d2_bv23_sold int
			declare @d3_bv23_sold int
			declare @d4_bv23_sold int
			declare @d5_bv23_sold int
			declare @d6_bv23_sold int
			declare @d7_bv23_sold int
			declare @d8_bv23_sold int
			declare @d1_bv23_occfcst numeric(38)
			declare @d2_bv23_occfcst numeric(38)
			declare @d3_bv23_occfcst numeric(38)
			declare @d4_bv23_occfcst numeric(38)
			declare @d5_bv23_occfcst numeric(38)
			declare @d6_bv23_occfcst numeric(38)
			declare @d7_bv23_occfcst numeric(38)
			declare @d8_bv23_occfcst numeric(38)
			declare @d1_bv24_sold int
			declare @d2_bv24_sold int
			declare @d3_bv24_sold int
			declare @d4_bv24_sold int
			declare @d5_bv24_sold int
			declare @d6_bv24_sold int
			declare @d7_bv24_sold int
			declare @d8_bv24_sold int
			declare @d1_bv24_occfcst numeric(38)
			declare @d2_bv24_occfcst numeric(38)
			declare @d3_bv24_occfcst numeric(38)
			declare @d4_bv24_occfcst numeric(38)
			declare @d5_bv24_occfcst numeric(38)
			declare @d6_bv24_occfcst numeric(38)
			declare @d7_bv24_occfcst numeric(38)
			declare @d8_bv24_occfcst numeric(38)
			declare @d1_bv25_sold int
			declare @d2_bv25_sold int
			declare @d3_bv25_sold int
			declare @d4_bv25_sold int
			declare @d5_bv25_sold int
			declare @d6_bv25_sold int
			declare @d7_bv25_sold int
			declare @d8_bv25_sold int
			declare @d1_bv25_occfcst numeric(38)
			declare @d2_bv25_occfcst numeric(38)
			declare @d3_bv25_occfcst numeric(38)
			declare @d4_bv25_occfcst numeric(38)
			declare @d5_bv25_occfcst numeric(38)
			declare @d6_bv25_occfcst numeric(38)
			declare @d7_bv25_occfcst numeric(38)
			declare @d8_bv25_occfcst numeric(38)
			declare @d1_rc1_sold int
			declare @d2_rc1_sold int
			declare @d3_rc1_sold int
			declare @d4_rc1_sold int
			declare @d5_rc1_sold int
			declare @d6_rc1_sold int
			declare @d7_rc1_sold int
			declare @d8_rc1_sold int
			declare @d1_rc1_occfcst numeric(38)
			declare @d2_rc1_occfcst numeric(38)
			declare @d3_rc1_occfcst numeric(38)
			declare @d4_rc1_occfcst numeric(38)
			declare @d5_rc1_occfcst numeric(38)
			declare @d6_rc1_occfcst numeric(38)
			declare @d7_rc1_occfcst numeric(38)
			declare @d8_rc1_occfcst numeric(38)
			declare @d1_rc1_lrv numeric(38)
			declare @d2_rc1_lrv numeric(38)
			declare @d3_rc1_lrv numeric(38)
			declare @d4_rc1_lrv numeric(38)
			declare @d5_rc1_lrv numeric(38)
			declare @d6_rc1_lrv numeric(38)
			declare @d7_rc1_lrv numeric(38)
			declare @d8_rc1_lrv numeric(38)
			declare @d1_rc2_sold int
			declare @d2_rc2_sold int
			declare @d3_rc2_sold int
			declare @d4_rc2_sold int
			declare @d5_rc2_sold int
			declare @d6_rc2_sold int
			declare @d7_rc2_sold int
			declare @d8_rc2_sold int
			declare @d1_rc2_occfcst numeric(38)
			declare @d2_rc2_occfcst numeric(38)
			declare @d3_rc2_occfcst numeric(38)
			declare @d4_rc2_occfcst numeric(38)
			declare @d5_rc2_occfcst numeric(38)
			declare @d6_rc2_occfcst numeric(38)
			declare @d7_rc2_occfcst numeric(38)
			declare @d8_rc2_occfcst numeric(38)
			declare @d1_rc2_lrv numeric(38)
			declare @d2_rc2_lrv numeric(38)
			declare @d3_rc2_lrv numeric(38)
			declare @d4_rc2_lrv numeric(38)
			declare @d5_rc2_lrv numeric(38)
			declare @d6_rc2_lrv numeric(38)
			declare @d7_rc2_lrv numeric(38)
			declare @d8_rc2_lrv numeric(38)
			declare @d1_rc3_sold int
			declare @d2_rc3_sold int
			declare @d3_rc3_sold int
			declare @d4_rc3_sold int
			declare @d5_rc3_sold int
			declare @d6_rc3_sold int
			declare @d7_rc3_sold int
			declare @d8_rc3_sold int
			declare @d1_rc3_occfcst numeric(38)
			declare @d2_rc3_occfcst numeric(38)
			declare @d3_rc3_occfcst numeric(38)
			declare @d4_rc3_occfcst numeric(38)
			declare @d5_rc3_occfcst numeric(38)
			declare @d6_rc3_occfcst numeric(38)
			declare @d7_rc3_occfcst numeric(38)
			declare @d8_rc3_occfcst numeric(38)
			declare @d1_rc3_lrv numeric(38)
			declare @d2_rc3_lrv numeric(38)
			declare @d3_rc3_lrv numeric(38)
			declare @d4_rc3_lrv numeric(38)
			declare @d5_rc3_lrv numeric(38)
			declare @d6_rc3_lrv numeric(38)
			declare @d7_rc3_lrv numeric(38)
			declare @d8_rc3_lrv numeric(38)
			declare @d1_rc4_sold int
			declare @d2_rc4_sold int
			declare @d3_rc4_sold int
			declare @d4_rc4_sold int
			declare @d5_rc4_sold int
			declare @d6_rc4_sold int
			declare @d7_rc4_sold int
			declare @d8_rc4_sold int
			declare @d1_rc4_occfcst numeric(38)
			declare @d2_rc4_occfcst numeric(38)
			declare @d3_rc4_occfcst numeric(38)
			declare @d4_rc4_occfcst numeric(38)
			declare @d5_rc4_occfcst numeric(38)
			declare @d6_rc4_occfcst numeric(38)
			declare @d7_rc4_occfcst numeric(38)
			declare @d8_rc4_occfcst numeric(38)
			declare @d1_rc4_lrv numeric(38)
			declare @d2_rc4_lrv numeric(38)
			declare @d3_rc4_lrv numeric(38)
			declare @d4_rc4_lrv numeric(38)
			declare @d5_rc4_lrv numeric(38)
			declare @d6_rc4_lrv numeric(38)
			declare @d7_rc4_lrv numeric(38)
			declare @d8_rc4_lrv numeric(38)
			declare @d1_rc5_sold int
			declare @d2_rc5_sold int
			declare @d3_rc5_sold int
			declare @d4_rc5_sold int
			declare @d5_rc5_sold int
			declare @d6_rc5_sold int
			declare @d7_rc5_sold int
			declare @d8_rc5_sold int
			declare @d1_rc5_occfcst numeric(38)
			declare @d2_rc5_occfcst numeric(38)
			declare @d3_rc5_occfcst numeric(38)
			declare @d4_rc5_occfcst numeric(38)
			declare @d5_rc5_occfcst numeric(38)
			declare @d6_rc5_occfcst numeric(38)
			declare @d7_rc5_occfcst numeric(38)
			declare @d8_rc5_occfcst numeric(38)
			declare @d1_rc5_lrv numeric(38)
			declare @d2_rc5_lrv numeric(38)
			declare @d3_rc5_lrv numeric(38)
			declare @d4_rc5_lrv numeric(38)
			declare @d5_rc5_lrv numeric(38)
			declare @d6_rc5_lrv numeric(38)
			declare @d7_rc5_lrv numeric(38)
			declare @d8_rc5_lrv numeric(38)
			declare @d1_rc6_sold int
			declare @d2_rc6_sold int
			declare @d3_rc6_sold int
			declare @d4_rc6_sold int
			declare @d5_rc6_sold int
			declare @d6_rc6_sold int
			declare @d7_rc6_sold int
			declare @d8_rc6_sold int
			declare @d1_rc6_occfcst numeric(38)
			declare @d2_rc6_occfcst numeric(38)
			declare @d3_rc6_occfcst numeric(38)
			declare @d4_rc6_occfcst numeric(38)
			declare @d5_rc6_occfcst numeric(38)
			declare @d6_rc6_occfcst numeric(38)
			declare @d7_rc6_occfcst numeric(38)
			declare @d8_rc6_occfcst numeric(38)
			declare @d1_rc6_lrv numeric(38)
			declare @d2_rc6_lrv numeric(38)
			declare @d3_rc6_lrv numeric(38)
			declare @d4_rc6_lrv numeric(38)
			declare @d5_rc6_lrv numeric(38)
			declare @d6_rc6_lrv numeric(38)
			declare @d7_rc6_lrv numeric(38)
			declare @d8_rc6_lrv numeric(38)
			declare @d1_rc7_sold int
			declare @d2_rc7_sold int
			declare @d3_rc7_sold int
			declare @d4_rc7_sold int
			declare @d5_rc7_sold int
			declare @d6_rc7_sold int
			declare @d7_rc7_sold int
			declare @d8_rc7_sold int
			declare @d1_rc7_occfcst numeric(38)
			declare @d2_rc7_occfcst numeric(38)
			declare @d3_rc7_occfcst numeric(38)
			declare @d4_rc7_occfcst numeric(38)
			declare @d5_rc7_occfcst numeric(38)
			declare @d6_rc7_occfcst numeric(38)
			declare @d7_rc7_occfcst numeric(38)
			declare @d8_rc7_occfcst numeric(38)
			declare @d1_rc7_lrv numeric(38)
			declare @d2_rc7_lrv numeric(38)
			declare @d3_rc7_lrv numeric(38)
			declare @d4_rc7_lrv numeric(38)
			declare @d5_rc7_lrv numeric(38)
			declare @d6_rc7_lrv numeric(38)
			declare @d7_rc7_lrv numeric(38)
			declare @d8_rc7_lrv numeric(38)
			declare @d1_rc8_sold int
			declare @d2_rc8_sold int
			declare @d3_rc8_sold int
			declare @d4_rc8_sold int
			declare @d5_rc8_sold int
			declare @d6_rc8_sold int
			declare @d7_rc8_sold int
			declare @d8_rc8_sold int
			declare @d1_rc8_occfcst numeric(38)
			declare @d2_rc8_occfcst numeric(38)
			declare @d3_rc8_occfcst numeric(38)
			declare @d4_rc8_occfcst numeric(38)
			declare @d5_rc8_occfcst numeric(38)
			declare @d6_rc8_occfcst numeric(38)
			declare @d7_rc8_occfcst numeric(38)
			declare @d8_rc8_occfcst numeric(38)
			declare @d1_rc8_lrv numeric(38)
			declare @d2_rc8_lrv numeric(38)
			declare @d3_rc8_lrv numeric(38)
			declare @d4_rc8_lrv numeric(38)
			declare @d5_rc8_lrv numeric(38)
			declare @d6_rc8_lrv numeric(38)
			declare @d7_rc8_lrv numeric(38)
			declare @d8_rc8_lrv numeric(38)
			declare @d1_rc9_sold int
			declare @d2_rc9_sold int
			declare @d3_rc9_sold int
			declare @d4_rc9_sold int
			declare @d5_rc9_sold int
			declare @d6_rc9_sold int
			declare @d7_rc9_sold int
			declare @d8_rc9_sold int
			declare @d1_rc9_occfcst numeric(38)
			declare @d2_rc9_occfcst numeric(38)
			declare @d3_rc9_occfcst numeric(38)
			declare @d4_rc9_occfcst numeric(38)
			declare @d5_rc9_occfcst numeric(38)
			declare @d6_rc9_occfcst numeric(38)
			declare @d7_rc9_occfcst numeric(38)
			declare @d8_rc9_occfcst numeric(38)
			declare @d1_rc9_lrv numeric(38)
			declare @d2_rc9_lrv numeric(38)
			declare @d3_rc9_lrv numeric(38)
			declare @d4_rc9_lrv numeric(38)
			declare @d5_rc9_lrv numeric(38)
			declare @d6_rc9_lrv numeric(38)
			declare @d7_rc9_lrv numeric(38)
			declare @d8_rc9_lrv numeric(38)
			declare @d1_rc10_sold int
			declare @d2_rc10_sold int
			declare @d3_rc10_sold int
			declare @d4_rc10_sold int
			declare @d5_rc10_sold int
			declare @d6_rc10_sold int
			declare @d7_rc10_sold int
			declare @d8_rc10_sold int
			declare @d1_rc10_occfcst numeric(38)
			declare @d2_rc10_occfcst numeric(38)
			declare @d3_rc10_occfcst numeric(38)
			declare @d4_rc10_occfcst numeric(38)
			declare @d5_rc10_occfcst numeric(38)
			declare @d6_rc10_occfcst numeric(38)
			declare @d7_rc10_occfcst numeric(38)
			declare @d8_rc10_occfcst numeric(38)
			declare @d1_rc10_lrv numeric(38)
			declare @d2_rc10_lrv numeric(38)
			declare @d3_rc10_lrv numeric(38)
			declare @d4_rc10_lrv numeric(38)
			declare @d5_rc10_lrv numeric(38)
			declare @d6_rc10_lrv numeric(38)
			declare @d7_rc10_lrv numeric(38)
			declare @d8_rc10_lrv numeric(38)
			declare @d1_rc11_sold int
			declare @d2_rc11_sold int
			declare @d3_rc11_sold int
			declare @d4_rc11_sold int
			declare @d5_rc11_sold int
			declare @d6_rc11_sold int
			declare @d7_rc11_sold int
			declare @d8_rc11_sold int
			declare @d1_rc11_occfcst numeric(38)
			declare @d2_rc11_occfcst numeric(38)
			declare @d3_rc11_occfcst numeric(38)
			declare @d4_rc11_occfcst numeric(38)
			declare @d5_rc11_occfcst numeric(38)
			declare @d6_rc11_occfcst numeric(38)
			declare @d7_rc11_occfcst numeric(38)
			declare @d8_rc11_occfcst numeric(38)
			declare @d1_rc11_lrv numeric(38)
			declare @d2_rc11_lrv numeric(38)
			declare @d3_rc11_lrv numeric(38)
			declare @d4_rc11_lrv numeric(38)
			declare @d5_rc11_lrv numeric(38)
			declare @d6_rc11_lrv numeric(38)
			declare @d7_rc11_lrv numeric(38)
			declare @d8_rc11_lrv numeric(38)
			declare @d1_rc12_sold int
			declare @d2_rc12_sold int
			declare @d3_rc12_sold int
			declare @d4_rc12_sold int
			declare @d5_rc12_sold int
			declare @d6_rc12_sold int
			declare @d7_rc12_sold int
			declare @d8_rc12_sold int
			declare @d1_rc12_occfcst numeric(38)
			declare @d2_rc12_occfcst numeric(38)
			declare @d3_rc12_occfcst numeric(38)
			declare @d4_rc12_occfcst numeric(38)
			declare @d5_rc12_occfcst numeric(38)
			declare @d6_rc12_occfcst numeric(38)
			declare @d7_rc12_occfcst numeric(38)
			declare @d8_rc12_occfcst numeric(38)
			declare @d1_rc12_lrv numeric(38)
			declare @d2_rc12_lrv numeric(38)
			declare @d3_rc12_lrv numeric(38)
			declare @d4_rc12_lrv numeric(38)
			declare @d5_rc12_lrv numeric(38)
			declare @d6_rc12_lrv numeric(38)
			declare @d7_rc12_lrv numeric(38)
			declare @d8_rc12_lrv numeric(38)
			declare @d1_rc13_sold int
			declare @d2_rc13_sold int
			declare @d3_rc13_sold int
			declare @d4_rc13_sold int
			declare @d5_rc13_sold int
			declare @d6_rc13_sold int
			declare @d7_rc13_sold int
			declare @d8_rc13_sold int
			declare @d1_rc13_occfcst numeric(38)
			declare @d2_rc13_occfcst numeric(38)
			declare @d3_rc13_occfcst numeric(38)
			declare @d4_rc13_occfcst numeric(38)
			declare @d5_rc13_occfcst numeric(38)
			declare @d6_rc13_occfcst numeric(38)
			declare @d7_rc13_occfcst numeric(38)
			declare @d8_rc13_occfcst numeric(38)
			declare @d1_rc13_lrv numeric(38)
			declare @d2_rc13_lrv numeric(38)
			declare @d3_rc13_lrv numeric(38)
			declare @d4_rc13_lrv numeric(38)
			declare @d5_rc13_lrv numeric(38)
			declare @d6_rc13_lrv numeric(38)
			declare @d7_rc13_lrv numeric(38)
			declare @d8_rc13_lrv numeric(38)
			declare @d1_rc14_sold int
			declare @d2_rc14_sold int
			declare @d3_rc14_sold int
			declare @d4_rc14_sold int
			declare @d5_rc14_sold int
			declare @d6_rc14_sold int
			declare @d7_rc14_sold int
			declare @d8_rc14_sold int
			declare @d1_rc14_occfcst numeric(38)
			declare @d2_rc14_occfcst numeric(38)
			declare @d3_rc14_occfcst numeric(38)
			declare @d4_rc14_occfcst numeric(38)
			declare @d5_rc14_occfcst numeric(38)
			declare @d6_rc14_occfcst numeric(38)
			declare @d7_rc14_occfcst numeric(38)
			declare @d8_rc14_occfcst numeric(38)
			declare @d1_rc14_lrv numeric(38)
			declare @d2_rc14_lrv numeric(38)
			declare @d3_rc14_lrv numeric(38)
			declare @d4_rc14_lrv numeric(38)
			declare @d5_rc14_lrv numeric(38)
			declare @d6_rc14_lrv numeric(38)
			declare @d7_rc14_lrv numeric(38)
			declare @d8_rc14_lrv numeric(38)
			declare @d1_rc15_sold int
			declare @d2_rc15_sold int
			declare @d3_rc15_sold int
			declare @d4_rc15_sold int
			declare @d5_rc15_sold int
			declare @d6_rc15_sold int
			declare @d7_rc15_sold int
			declare @d8_rc15_sold int
			declare @d1_rc15_occfcst numeric(38)
			declare @d2_rc15_occfcst numeric(38)
			declare @d3_rc15_occfcst numeric(38)
			declare @d4_rc15_occfcst numeric(38)
			declare @d5_rc15_occfcst numeric(38)
			declare @d6_rc15_occfcst numeric(38)
			declare @d7_rc15_occfcst numeric(38)
			declare @d8_rc15_occfcst numeric(38)
			declare @d1_rc15_lrv numeric(38)
			declare @d2_rc15_lrv numeric(38)
			declare @d3_rc15_lrv numeric(38)
			declare @d4_rc15_lrv numeric(38)
			declare @d5_rc15_lrv numeric(38)
			declare @d6_rc15_lrv numeric(38)
			declare @d7_rc15_lrv numeric(38)
			declare @d8_rc15_lrv numeric(38)
			declare @d1_rc16_sold int
			declare @d2_rc16_sold int
			declare @d3_rc16_sold int
			declare @d4_rc16_sold int
			declare @d5_rc16_sold int
			declare @d6_rc16_sold int
			declare @d7_rc16_sold int
			declare @d8_rc16_sold int
			declare @d1_rc16_occfcst numeric(38)
			declare @d2_rc16_occfcst numeric(38)
			declare @d3_rc16_occfcst numeric(38)
			declare @d4_rc16_occfcst numeric(38)
			declare @d5_rc16_occfcst numeric(38)
			declare @d6_rc16_occfcst numeric(38)
			declare @d7_rc16_occfcst numeric(38)
			declare @d8_rc16_occfcst numeric(38)
			declare @d1_rc16_lrv numeric(38)
			declare @d2_rc16_lrv numeric(38)
			declare @d3_rc16_lrv numeric(38)
			declare @d4_rc16_lrv numeric(38)
			declare @d5_rc16_lrv numeric(38)
			declare @d6_rc16_lrv numeric(38)
			declare @d7_rc16_lrv numeric(38)
			declare @d8_rc16_lrv numeric(38)
			declare @d1_rc17_sold int
			declare @d2_rc17_sold int
			declare @d3_rc17_sold int
			declare @d4_rc17_sold int
			declare @d5_rc17_sold int
			declare @d6_rc17_sold int
			declare @d7_rc17_sold int
			declare @d8_rc17_sold int
			declare @d1_rc17_occfcst numeric(38)
			declare @d2_rc17_occfcst numeric(38)
			declare @d3_rc17_occfcst numeric(38)
			declare @d4_rc17_occfcst numeric(38)
			declare @d5_rc17_occfcst numeric(38)
			declare @d6_rc17_occfcst numeric(38)
			declare @d7_rc17_occfcst numeric(38)
			declare @d8_rc17_occfcst numeric(38)
			declare @d1_rc17_lrv numeric(38)
			declare @d2_rc17_lrv numeric(38)
			declare @d3_rc17_lrv numeric(38)
			declare @d4_rc17_lrv numeric(38)
			declare @d5_rc17_lrv numeric(38)
			declare @d6_rc17_lrv numeric(38)
			declare @d7_rc17_lrv numeric(38)
			declare @d8_rc17_lrv numeric(38)
			declare @d1_rc18_sold int
			declare @d2_rc18_sold int
			declare @d3_rc18_sold int
			declare @d4_rc18_sold int
			declare @d5_rc18_sold int
			declare @d6_rc18_sold int
			declare @d7_rc18_sold int
			declare @d8_rc18_sold int
			declare @d1_rc18_occfcst numeric(38)
			declare @d2_rc18_occfcst numeric(38)
			declare @d3_rc18_occfcst numeric(38)
			declare @d4_rc18_occfcst numeric(38)
			declare @d5_rc18_occfcst numeric(38)
			declare @d6_rc18_occfcst numeric(38)
			declare @d7_rc18_occfcst numeric(38)
			declare @d8_rc18_occfcst numeric(38)
			declare @d1_rc18_lrv numeric(38)
			declare @d2_rc18_lrv numeric(38)
			declare @d3_rc18_lrv numeric(38)
			declare @d4_rc18_lrv numeric(38)
			declare @d5_rc18_lrv numeric(38)
			declare @d6_rc18_lrv numeric(38)
			declare @d7_rc18_lrv numeric(38)
			declare @d8_rc18_lrv numeric(38)
			declare @d1_rc19_sold int
			declare @d2_rc19_sold int
			declare @d3_rc19_sold int
			declare @d4_rc19_sold int
			declare @d5_rc19_sold int
			declare @d6_rc19_sold int
			declare @d7_rc19_sold int
			declare @d8_rc19_sold int
			declare @d1_rc19_occfcst numeric(38)
			declare @d2_rc19_occfcst numeric(38)
			declare @d3_rc19_occfcst numeric(38)
			declare @d4_rc19_occfcst numeric(38)
			declare @d5_rc19_occfcst numeric(38)
			declare @d6_rc19_occfcst numeric(38)
			declare @d7_rc19_occfcst numeric(38)
			declare @d8_rc19_occfcst numeric(38)
			declare @d1_rc19_lrv numeric(38)
			declare @d2_rc19_lrv numeric(38)
			declare @d3_rc19_lrv numeric(38)
			declare @d4_rc19_lrv numeric(38)
			declare @d5_rc19_lrv numeric(38)
			declare @d6_rc19_lrv numeric(38)
			declare @d7_rc19_lrv numeric(38)
			declare @d8_rc19_lrv numeric(38)
			declare @d1_rc20_sold int
			declare @d2_rc20_sold int
			declare @d3_rc20_sold int
			declare @d4_rc20_sold int
			declare @d5_rc20_sold int
			declare @d6_rc20_sold int
			declare @d7_rc20_sold int
			declare @d8_rc20_sold int
			declare @d1_rc20_occfcst numeric(38)
			declare @d2_rc20_occfcst numeric(38)
			declare @d3_rc20_occfcst numeric(38)
			declare @d4_rc20_occfcst numeric(38)
			declare @d5_rc20_occfcst numeric(38)
			declare @d6_rc20_occfcst numeric(38)
			declare @d7_rc20_occfcst numeric(38)
			declare @d8_rc20_occfcst numeric(38)
			declare @d1_rc20_lrv numeric(38)
			declare @d2_rc20_lrv numeric(38)
			declare @d3_rc20_lrv numeric(38)
			declare @d4_rc20_lrv numeric(38)
			declare @d5_rc20_lrv numeric(38)
			declare @d6_rc20_lrv numeric(38)
			declare @d7_rc20_lrv numeric(38)
			declare @d8_rc20_lrv numeric(38)
			declare @d1_rc21_sold int
			declare @d2_rc21_sold int
			declare @d3_rc21_sold int
			declare @d4_rc21_sold int
			declare @d5_rc21_sold int
			declare @d6_rc21_sold int
			declare @d7_rc21_sold int
			declare @d8_rc21_sold int
			declare @d1_rc21_occfcst numeric(38)
			declare @d2_rc21_occfcst numeric(38)
			declare @d3_rc21_occfcst numeric(38)
			declare @d4_rc21_occfcst numeric(38)
			declare @d5_rc21_occfcst numeric(38)
			declare @d6_rc21_occfcst numeric(38)
			declare @d7_rc21_occfcst numeric(38)
			declare @d8_rc21_occfcst numeric(38)
			declare @d1_rc21_lrv numeric(38)
			declare @d2_rc21_lrv numeric(38)
			declare @d3_rc21_lrv numeric(38)
			declare @d4_rc21_lrv numeric(38)
			declare @d5_rc21_lrv numeric(38)
			declare @d6_rc21_lrv numeric(38)
			declare @d7_rc21_lrv numeric(38)
			declare @d8_rc21_lrv numeric(38)
			declare @d1_rc22_sold int
			declare @d2_rc22_sold int
			declare @d3_rc22_sold int
			declare @d4_rc22_sold int
			declare @d5_rc22_sold int
			declare @d6_rc22_sold int
			declare @d7_rc22_sold int
			declare @d8_rc22_sold int
			declare @d1_rc22_occfcst numeric(38)
			declare @d2_rc22_occfcst numeric(38)
			declare @d3_rc22_occfcst numeric(38)
			declare @d4_rc22_occfcst numeric(38)
			declare @d5_rc22_occfcst numeric(38)
			declare @d6_rc22_occfcst numeric(38)
			declare @d7_rc22_occfcst numeric(38)
			declare @d8_rc22_occfcst numeric(38)
			declare @d1_rc22_lrv numeric(38)
			declare @d2_rc22_lrv numeric(38)
			declare @d3_rc22_lrv numeric(38)
			declare @d4_rc22_lrv numeric(38)
			declare @d5_rc22_lrv numeric(38)
			declare @d6_rc22_lrv numeric(38)
			declare @d7_rc22_lrv numeric(38)
			declare @d8_rc22_lrv numeric(38)
			declare @d1_rc23_sold int
			declare @d2_rc23_sold int
			declare @d3_rc23_sold int
			declare @d4_rc23_sold int
			declare @d5_rc23_sold int
			declare @d6_rc23_sold int
			declare @d7_rc23_sold int
			declare @d8_rc23_sold int
			declare @d1_rc23_occfcst numeric(38)
			declare @d2_rc23_occfcst numeric(38)
			declare @d3_rc23_occfcst numeric(38)
			declare @d4_rc23_occfcst numeric(38)
			declare @d5_rc23_occfcst numeric(38)
			declare @d6_rc23_occfcst numeric(38)
			declare @d7_rc23_occfcst numeric(38)
			declare @d8_rc23_occfcst numeric(38)
			declare @d1_rc23_lrv numeric(38)
			declare @d2_rc23_lrv numeric(38)
			declare @d3_rc23_lrv numeric(38)
			declare @d4_rc23_lrv numeric(38)
			declare @d5_rc23_lrv numeric(38)
			declare @d6_rc23_lrv numeric(38)
			declare @d7_rc23_lrv numeric(38)
			declare @d8_rc23_lrv numeric(38)
			declare @d1_rc24_sold int
			declare @d2_rc24_sold int
			declare @d3_rc24_sold int
			declare @d4_rc24_sold int
			declare @d5_rc24_sold int
			declare @d6_rc24_sold int
			declare @d7_rc24_sold int
			declare @d8_rc24_sold int
			declare @d1_rc24_occfcst numeric(38)
			declare @d2_rc24_occfcst numeric(38)
			declare @d3_rc24_occfcst numeric(38)
			declare @d4_rc24_occfcst numeric(38)
			declare @d5_rc24_occfcst numeric(38)
			declare @d6_rc24_occfcst numeric(38)
			declare @d7_rc24_occfcst numeric(38)
			declare @d8_rc24_occfcst numeric(38)
			declare @d1_rc24_lrv numeric(38)
			declare @d2_rc24_lrv numeric(38)
			declare @d3_rc24_lrv numeric(38)
			declare @d4_rc24_lrv numeric(38)
			declare @d5_rc24_lrv numeric(38)
			declare @d6_rc24_lrv numeric(38)
			declare @d7_rc24_lrv numeric(38)
			declare @d8_rc24_lrv numeric(38)
			declare @d1_rc25_sold int
			declare @d2_rc25_sold int
			declare @d3_rc25_sold int
			declare @d4_rc25_sold int
			declare @d5_rc25_sold int
			declare @d6_rc25_sold int
			declare @d7_rc25_sold int
			declare @d8_rc25_sold int
			declare @d1_rc25_occfcst numeric(38)
			declare @d2_rc25_occfcst numeric(38)
			declare @d3_rc25_occfcst numeric(38)
			declare @d4_rc25_occfcst numeric(38)
			declare @d5_rc25_occfcst numeric(38)
			declare @d6_rc25_occfcst numeric(38)
			declare @d7_rc25_occfcst numeric(38)
			declare @d8_rc25_occfcst numeric(38)
			declare @d1_rc25_lrv numeric(38)
			declare @d2_rc25_lrv numeric(38)
			declare @d3_rc25_lrv numeric(38)
			declare @d4_rc25_lrv numeric(38)
			declare @d5_rc25_lrv numeric(38)
			declare @d6_rc25_lrv numeric(38)
			declare @d7_rc25_lrv numeric(38)
			declare @d8_rc25_lrv numeric(38)


			SET NOCOUNT ON

create table #temp_pace_all (
                                Occupancy_DT date,
                                DaysToArrival int,
                                Occupancy_NBR numeric(8, 2),
                                Rooms_Sold int,
                                LRV numeric(19, 5),
                                FgId int,
                                BvId int,
                                RcId int
);

create table #temp_pace_bv(
                              daystoArrival int,
                              d1_sold int,
                              d2_sold int,
                              d3_sold int,
                              d4_sold int,
                              d5_sold int,
                              d6_sold int,
                              d7_sold int,
                              d8_sold int,
                              d1_occupancy_NBR numeric(8,2),
                              d2_occupancy_NBR numeric(8,2),
                              d3_occupancy_NBR numeric(8,2),
                              d4_occupancy_NBR numeric(8,2),
                              d5_occupancy_NBR numeric(8,2),
                              d6_occupancy_NBR numeric(8,2),
                              d7_occupancy_NBR numeric(8,2),
                              d8_occupancy_NBR numeric(8,2),
                              bvId int
);

create table #temp_pace_fg(
                              daystoArrival int,
                              d1_sold int,
                              d2_sold int,
                              d3_sold int,
                              d4_sold int,
                              d5_sold int,
                              d6_sold int,
                              d7_sold int,
                              d8_sold int,
                              d1_occupancy_NBR numeric(8,2),
                              d2_occupancy_NBR numeric(8,2),
                              d3_occupancy_NBR numeric(8,2),
                              d4_occupancy_NBR numeric(8,2),
                              d5_occupancy_NBR numeric(8,2),
                              d6_occupancy_NBR numeric(8,2),
                              d7_occupancy_NBR numeric(8,2),
                              d8_occupancy_NBR numeric(8,2),
                              fgId int
);

create table #temp_pace_rc(
                              daystoArrival int,
                              d1_sold int,
                              d2_sold int,
                              d3_sold int,
                              d4_sold int,
                              d5_sold int,
                              d6_sold int,
                              d7_sold int,
                              d8_sold int,
                              d1_occupancy_NBR numeric(8,2),
                              d2_occupancy_NBR numeric(8,2),
                              d3_occupancy_NBR numeric(8,2),
                              d4_occupancy_NBR numeric(8,2),
                              d5_occupancy_NBR numeric(8,2),
                              d6_occupancy_NBR numeric(8,2),
                              d7_occupancy_NBR numeric(8,2),
                              d8_occupancy_NBR numeric(8,2),
                              d1_lrv numeric(8,2),
                              d2_lrv numeric(8,2),
                              d3_lrv numeric(8,2),
                              d4_lrv numeric(8,2),
                              d5_lrv numeric(8,2),
                              d6_lrv numeric(8,2),
                              d7_lrv numeric(8,2),
                              d8_lrv numeric(8,2),
                              rcId int
);

declare	@transient_type varchar(10)
select @transient_type = case @isTransient when 1 then 'transient' else 'none' end

declare	@group_type varchar(10)
select @group_type = case @isGroup when 1 then 'group' else 'none' end

declare @fg_count int;
			declare @bv_count int;
			declare @rc_count int;


			declare @fg_loop table (fgId int);
begin
				insert @fg_loop values (@fg1),(@fg2),(@fg3),(@fg4),(@fg5),(@fg6),(@fg7),(@fg8),(@fg9),(@fg10),(@fg11),(@fg12),(@fg13),(@fg14),(@fg15),(@fg16),(@fg17),(@fg18),(@fg19),(@fg20),(@fg21),(@fg22),(@fg23),(@fg24),(@fg25);
end
			declare @bv_loop table (bvId int);
begin
				insert @bv_loop values (@bv1),(@bv2),(@bv3),(@bv4),(@bv5),(@bv6),(@bv7),(@bv8),(@bv9),(@bv10),(@bv11),(@bv12),(@bv13),(@bv14),(@bv15),(@bv16),(@bv17),(@bv18),(@bv19),(@bv20),(@bv21),(@bv22),(@bv23),(@bv24),(@bv25);
end
			declare @rc_loop table (rcId int);
begin
				insert @rc_loop values (@rc1),(@rc2),(@rc3),(@rc4),(@rc5),(@rc6),(@rc7),(@rc8),(@rc9),(@rc10),(@rc11),(@rc12),(@rc13),(@rc14),(@rc15),(@rc16),(@rc17),(@rc18),(@rc19),(@rc20),(@rc21),(@rc22),(@rc23),(@rc24),(@rc25);
end

			SET @fg_count = (select count(fgId) from @fg_loop where fgId > 0);
			SET @bv_count = (select count(bvId) from @bv_loop where bvId > 0);
			SET @rc_count = (select count(rcId) from @rc_loop where rcId > 0);

begin
				if(@isOcFcst=1 AND @fg_count > 0)
begin
insert into #temp_pace_all
select Occupancy_DT,daystoArrival,Occupancy_NBR,0,0,Fg.Forecast_Group_ID,0,0 from
                                                                                 (
                                                                                     select p.Occupancy_DT,p.Business_DT,p.Mkt_Seg_id,DATEDIFF(day,p.Business_DT,p.Occupancy_DT) as daystoArrival,
                                                                                            paof.Occupancy_NBR from (

                                                                                                                        select paof.property_id,paof.occupancy_dt,Business_DT,Mkt_Seg_id, MAX(paof.Decision_ID) as Decision_ID
                                                                                                                        from PACE_Mkt_Occupancy_FCST  paof
                                                                                                                                 join dbo.decision de on de.property_id=@property_id and de.decision_id=paof.decision_id
                                                                                                                        where paof.property_id=@property_id
                                                                                                                          and paof.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                          and de.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                          and de.Decision_Type_ID = 1
                                                                                                                        group by paof.Property_ID,Occupancy_DT,Business_DT,Mkt_Seg_id

                                                                                                                    ) p inner join
                                                                                                                    (
                                                                                                                        select POF.Property_ID,Occupancy_DT,Business_DT,Mkt_Seg_id, POF.Decision_ID, SUM(Occupancy_NBR)Occupancy_NBR
                                                                                                                        from PACE_Mkt_Occupancy_FCST POF
                                                                                                                                 inner join Decision DE
                                                                                                                                            on POF.Property_ID=DE.Property_ID
                                                                                                                                                and POF.Decision_ID=DE.Decision_ID
                                                                                                                        where POF.Property_ID=@property_id
                                                                                                                          and POF.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                          and DE.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                          and DE.Decision_Type_ID = 1
                                                                                                                        group by POF.Property_ID,Occupancy_DT,Business_DT,POF.Decision_ID,Mkt_Seg_id
                                                                                                                    ) paof
                                                                                                                    on p.property_id=paof.Property_ID
                                                                                                                        and p.occupancy_dt=paof.Occupancy_DT
                                                                                                                        and p.Business_DT=paof.Business_DT
                                                                                                                        and p.Mkt_Seg_id=paof.MKT_SEG_ID
                                                                                                                        and p.Decision_ID=paof.Decision_ID
                                                                                 ) fcst,
                                                                                 mkt_seg_forecast_group msfg,
                                                                                 Forecast_Group FG
where fcst.mkt_seg_id=msfg.mkt_seg_id
  and msfg.Forecast_Group_ID=FG.Forecast_Group_ID
  and FG.Forecast_Group_ID in (select fgId from @fg_loop where fgId > 0 )
  and msfg.Status_ID=1
order by Occupancy_DT,daystoArrival
end
end

begin
			if(@isSold=1 AND @fg_count > 0)
begin
insert into #temp_pace_all
select Occupancy_DT,daystoArrival,0,Rooms_Sold,0,fgId,0,0 from
    (
        select Occupancy_DT,Business_Day_End_DT,pma.Mkt_Seg_id, DATEDIFF(day,Business_Day_End_DT,Occupancy_DT) as daystoArrival,Rooms_Sold, FG.Forecast_Group_ID as fgId
        from PACE_Mkt_Activity pma
                 inner join mkt_seg_forecast_group msfg on pma.mkt_seg_id = msfg.mkt_seg_id and msfg.Status_ID=1
                 inner join Forecast_Group FG on msfg.Forecast_Group_ID=FG.Forecast_Group_ID
        where pma.Property_ID=@property_id
          and occupancy_dt in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
          and business_day_end_dt between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
          and FG.Forecast_Group_ID in (select fgId from @fg_loop where fgId > 0 )
    ) sold
order by Occupancy_DT,daystoArrival
end
end

begin
			if(@isOcFcst=1 AND @bv_count > 0)
begin
insert into #temp_pace_all
select Occupancy_DT,daystoArrival,Occupancy_NBR,0,0,0, msbg.bvId,0 from
                                                                       (
                                                                           select p.Occupancy_DT,p.Business_DT,p.Mkt_Seg_id,DATEDIFF(day,p.Business_DT,p.Occupancy_DT) as daystoArrival,
                                                                                  paof.Occupancy_NBR from (

                                                                                                              select paof.property_id,paof.occupancy_dt,Business_DT,Mkt_Seg_id, MAX(paof.Decision_ID) as Decision_ID
                                                                                                              from PACE_Mkt_Occupancy_FCST  paof
                                                                                                                       join dbo.decision de on de.property_id=@property_id and de.decision_id=paof.decision_id
                                                                                                              where paof.property_id=@property_id
                                                                                                                and paof.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                and de.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                and de.Decision_Type_ID = 1
                                                                                                              group by paof.Property_ID,Occupancy_DT,Business_DT,Mkt_Seg_id

                                                                                                          ) p inner join
                                                                                                          (
                                                                                                              select POF.Property_ID,Occupancy_DT,Business_DT,Mkt_Seg_id, POF.Decision_ID, SUM(Occupancy_NBR)Occupancy_NBR
                                                                                                              from PACE_Mkt_Occupancy_FCST POF
                                                                                                                       inner join Decision DE
                                                                                                                                  on POF.Property_ID=DE.Property_ID
                                                                                                                                      and POF.Decision_ID=DE.Decision_ID
                                                                                                              where POF.Property_ID=@property_id
                                                                                                                and POF.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                and DE.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                and DE.Decision_Type_ID = 1
                                                                                                              group by POF.Property_ID,Occupancy_DT,Business_DT,POF.Decision_ID,Mkt_Seg_id
                                                                                                          ) paof
                                                                                                          on p.property_id=paof.Property_ID
                                                                                                              and p.occupancy_dt=paof.Occupancy_DT
                                                                                                              and p.Business_DT=paof.Business_DT
                                                                                                              and p.Mkt_Seg_id=paof.MKT_SEG_ID
                                                                                                              and p.Decision_ID=paof.Decision_ID
                                                                       ) fcst,
                                                                       (
                                                                           select distinct MS.Property_ID,MS.Mkt_Seg_ID, MSBG.Business_Group_ID as bvId from Mkt_Seg_Business_Group MSBG
                                                                                                                                                                 inner join Mkt_Seg MS on MSBG.Mkt_Seg_ID = MS.Mkt_Seg_ID and MSBG.Business_Group_ID in(select bvId from @bv_loop where bvId > 0)
                                                                               and MS.Property_ID = @property_id and MS.Status_ID = 1
                                                                       ) msbg
where fcst.mkt_seg_id=msbg.mkt_seg_id
order by Occupancy_DT,daystoArrival
end
end

begin
			if(@isSold=1 AND @bv_count > 0)
begin
insert into #temp_pace_all
select Occupancy_DT,daystoArrival,0,Rooms_Sold,0,0, bvId,0 from
    (
        select Occupancy_DT,Business_Day_End_DT,pma.Mkt_Seg_id, DATEDIFF(day,Business_Day_End_DT,Occupancy_DT) as daystoArrival,Rooms_Sold,msbg.bvId
        from PACE_Mkt_Activity pma
                 inner join (

            select distinct MS.Property_ID,MS.Mkt_Seg_ID, MSBG.Business_Group_ID as bvId from Mkt_Seg_Business_Group MSBG
                                                                                                  inner join Mkt_Seg MS on MSBG.Mkt_Seg_ID = MS.Mkt_Seg_ID and MSBG.Business_Group_ID in(select bvId from @bv_loop where bvId > 0)
                and MS.Property_ID = @property_id and MS.Status_ID = 1

        ) msbg on pma.Mkt_Seg_ID=msbg.Mkt_Seg_ID
        where pma.Property_ID=@property_id
          and occupancy_dt in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
          and business_day_end_dt between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
          and msbg.bvId in (select bvId from @bv_loop where bvId > 0)
    ) sold
order by Occupancy_DT,daystoArrival
end
end

begin
				if(@isLrv=1)
begin
insert into #temp_pace_all
select Occupancy_DT,DATEDIFF(day,Business_DT,Occupancy_DT) as daystoArrival,0,0,LRV,0,0, Accom_Class_ID
from PACE_LRV PL
         inner join
     Decision DE
     on	PL.Decision_ID=DE.Decision_ID
         and PL.Property_ID=DE.Property_ID
where PL.Property_ID=@property_id
  and PL.Accom_Class_ID in (select rcId from @rc_loop where rcId > 0)
  and PL.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
  and DE.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
  and DE.Decision_Type_ID = 1
order by Occupancy_DT,daystoArrival
end
end

begin
			if(@isOcFcst=1 AND @rc_count >0)
begin
insert into #temp_pace_all
select fcst.Occupancy_DT,daystoArrival,fcst.Occupancy_NBR,0,0,0,0, aty.Accom_Class_ID from
                                                                                          (
                                                                                              select p.Occupancy_DT,p.Business_DT,p.Accom_Type_ID,DATEDIFF(day,p.Business_DT,p.Occupancy_DT) as daystoArrival,
                                                                                                     paof.Occupancy_NBR from (

                                                                                                                                 select paof.property_id,paof.occupancy_dt,Business_DT,Accom_Type_ID, MAX(paof.Decision_ID) as Decision_ID
                                                                                                                                 from PACE_Accom_Occupancy_FCST  paof
                                                                                                                                          join dbo.decision de on de.property_id=@property_id and de.decision_id=paof.decision_id
                                                                                                                                 where paof.property_id=@property_id
                                                                                                                                   and paof.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                                   and de.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                                   and de.Decision_Type_ID = 1
                                                                                                                                 group by paof.Property_ID,Occupancy_DT,Business_DT,Accom_Type_ID

                                                                                                                             ) p inner join
                                                                                                                             (
                                                                                                                                 select POF.Property_ID,Occupancy_DT,Business_DT,Accom_Type_ID, POF.Decision_ID, SUM(Occupancy_NBR)Occupancy_NBR
                                                                                                                                 from PACE_Accom_Occupancy_FCST POF
                                                                                                                                          inner join Decision DE
                                                                                                                                                     on POF.Property_ID=DE.Property_ID
                                                                                                                                                         and POF.Decision_ID=DE.Decision_ID
                                                                                                                                 where POF.Property_ID=@property_id
                                                                                                                                   and POF.Occupancy_DT in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
                                                                                                                                   and DE.Business_DT between DATEADD(DAY, -@pace_days,Occupancy_DT ) and Occupancy_DT
                                                                                                                                   and DE.Decision_Type_ID = 1
                                                                                                                                 group by POF.Property_ID,Occupancy_DT,Business_DT,POF.Decision_ID,Accom_Type_ID
                                                                                                                             ) paof
                                                                                                                             on p.property_id=paof.Property_ID
                                                                                                                                 and p.occupancy_dt=paof.Occupancy_DT
                                                                                                                                 and p.Business_DT=paof.Business_DT
                                                                                                                                 and p.Accom_Type_ID=paof.Accom_Type_ID
                                                                                                                                 and p.Decision_ID=paof.Decision_ID
                                                                                          ) fcst,	(
                                                                                              SELECT  Accom_Type_ID, Accom_Class_ID
                                                                                              FROM Accom_Type
                                                                                              WHERE Property_ID = @property_id
                                                                                                AND Status_ID = 1
                                                                                                AND Display_Status_ID IN (
                                                                                                  SELECT 1
                                                                                                  UNION
                                                                                                  SELECT CASE
                                                                                                             WHEN @includeInactiveRT = '1'
                                                                                                                 THEN 2
                                                                                                             ELSE 1
                                                                                                             END
                                                                                              )
                                                                                          ) aty
where aty.Accom_Type_ID=fcst.Accom_Type_ID
  and aty.Accom_Class_ID in (select rcId from @rc_loop where rcId > 0)
order by Occupancy_DT,daystoArrival
end
end


begin
			if(@isSold=1 AND @rc_count >0)
begin
insert into #temp_pace_all
select Occupancy_DT,daystoArrival,0,Rooms_Sold,0,0,0, Accom_Class_ID from
    (
        select paa.Occupancy_DT,Business_Day_End_DT,paa.Accom_Type_ID, DATEDIFF(day,Business_Day_End_DT,paa.Occupancy_DT) as daystoArrival,Rooms_Sold, act.Accom_Class_ID
        from PACE_Accom_Activity paa
                 inner join (
            SELECT  Accom_Type_ID, Accom_Class_ID
            FROM Accom_Type
            WHERE Property_ID = @property_id
              AND Status_ID = 1
              AND Display_Status_ID IN (
                SELECT 1
                UNION
                SELECT CASE
                           WHEN @includeInactiveRT = '1'
                               THEN 2
                           ELSE 1
                           END
            )

        ) act on act.Accom_Type_ID = paa.Accom_Type_ID
        where paa.Property_ID=@property_id
          and paa.occupancy_dt in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
          and business_day_end_dt between DATEADD(DAY, -@pace_days,paa.Occupancy_DT ) and paa.Occupancy_DT
          and act.Accom_Class_ID in (select rcId from @rc_loop where rcId > 0)
    ) sold
order by Occupancy_DT,daystoArrival
end
end


		Declare @fgID int
		Declare fg_cursor CURSOR FOR

select fgid from @fg_loop

    Open fg_cursor
		Fetch next from fg_cursor into @fgID

    while(@@FETCH_STATUS=0)
BEGIN
			IF(@fgID > 0)
begin
insert into #temp_pace_fg
select daystoArrival,
       isnull(SUM([d1_sold]),0.0) as d1_sold,
       isnull(SUM([d2_sold]),0.0) as d2_sold,
       isnull(SUM([d3_sold]),0.0) as d3_sold,
       isnull(SUM([d4_sold]),0.0) as d4_sold,
       isnull(SUM([d5_sold]),0.0) as d5_sold,
       isnull(SUM([d6_sold]),0.0) as d6_sold,
       isnull(SUM([d7_sold]),0.0) as d7_sold,
       isnull(SUM([d8_sold]),0.0) as d8_sold,
       isnull(MAX([d1_ocf]),0.0) as d1_ocf,
       isnull(MAX([d2_ocf]),0.0) as d2_ocf,
       isnull(MAX([d3_ocf]),0.0) as d3_ocf,
       isnull(MAX([d4_ocf]),0.0) as d4_ocf,
       isnull(MAX([d5_ocf]),0.0) as d5_ocf,
       isnull(MAX([d6_ocf]),0.0) as d6_ocf,
       isnull(MAX([d7_ocf]),0.0) as d7_ocf,
       isnull(MAX([d8_ocf]),0.0) as d8_ocf,
       @fgID
from
    (
        select base.daystoArrival,
               (case base.Occupancy_DT when @d1 then Rooms_Sold end) as [d1_sold],
					(case base.Occupancy_DT when @d2 then Rooms_Sold end) as [d2_sold],
					(case base.Occupancy_DT when @d3 then Rooms_Sold end) as [d3_sold],
					(case base.Occupancy_DT when @d4 then Rooms_Sold end) as [d4_sold],
					(case base.Occupancy_DT when @d5 then Rooms_Sold end) as [d5_sold],
					(case base.Occupancy_DT when @d6 then Rooms_Sold end) as [d6_sold],
					(case base.Occupancy_DT when @d7 then Rooms_Sold end) as [d7_sold],
					(case base.Occupancy_DT when @d8 then Rooms_Sold end) as [d8_sold],
					(case base.Occupancy_DT when @d1 then Occupancy_NBR end) as [d1_ocf],
					(case base.Occupancy_DT when @d2 then Occupancy_NBR end) as [d2_ocf],
					(case base.Occupancy_DT when @d3 then Occupancy_NBR end) as [d3_ocf],
					(case base.Occupancy_DT when @d4 then Occupancy_NBR end) as [d4_ocf],
					(case base.Occupancy_DT when @d5 then Occupancy_NBR end) as [d5_ocf],
					(case base.Occupancy_DT when @d6 then Occupancy_NBR end) as [d6_ocf],
					(case base.Occupancy_DT when @d7 then Occupancy_NBR end) as [d7_ocf],
					(case base.Occupancy_DT when @d8 then Occupancy_NBR end) as [d8_ocf]
        from
            (
            select cast(a.calendar_date as date)Occupancy_DT ,cast(b.calendar_date as date) Business_DT,
            DATEDIFF(day,b.calendar_date,a.calendar_date) as daystoArrival from calendar_dim a,calendar_dim b
            where b.calendar_date between DATEADD(DAY, -@pace_days,a.calendar_date ) and a.calendar_date
            and a.calendar_date in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
            ) base
            left join
            (
            select fgs.daystoArrival,fgs.occupancy_dt,sum(fgs.rooms_sold) as Rooms_Sold from #temp_pace_all  fgs where fgs.fgId = @fgID
            group by Occupancy_DT,daystoArrival,rooms_sold
            ) a on base.Occupancy_DT=a.Occupancy_DT
            and base.daystoArrival=a.daystoArrival
            left join
            (
            select fgf.occupancy_dt,fgf.daystoArrival,sum(fgf.Occupancy_NBR) as Occupancy_NBR from #temp_pace_all fgf where fgf.fgId = @fgID
            group by Occupancy_DT,daystoArrival
            ) b on base.Occupancy_DT=b.Occupancy_DT
            and base.daystoArrival=b.daystoArrival
    ) as x group by daystoArrival
order by daystoArrival
end

Fetch next from fg_cursor into @fgID
END

Close fg_cursor
    DEALLOCATE fg_cursor


Declare @bvID int
		Declare bv_cursor CURSOR FOR
select bvid from @bv_loop

    Open bv_cursor
		Fetch next from bv_cursor into @bvID

    while(@@FETCH_STATUS=0)
BEGIN
			IF(@bvID > 0)
begin
insert into #temp_pace_bv
select daystoArrival,
       isnull(SUM([d1_sold]),0.0) as d1_sold,
       isnull(SUM([d2_sold]),0.0) as d2_sold,
       isnull(SUM([d3_sold]),0.0) as d3_sold,
       isnull(SUM([d4_sold]),0.0) as d4_sold,
       isnull(SUM([d5_sold]),0.0) as d5_sold,
       isnull(SUM([d6_sold]),0.0) as d6_sold,
       isnull(SUM([d7_sold]),0.0) as d7_sold,
       isnull(SUM([d8_sold]),0.0) as d8_sold,
       isnull(MAX([d1_ocf]),0.0) as d1_ocf,
       isnull(MAX([d2_ocf]),0.0) as d2_ocf,
       isnull(MAX([d3_ocf]),0.0) as d3_ocf,
       isnull(MAX([d4_ocf]),0.0) as d4_ocf,
       isnull(MAX([d5_ocf]),0.0) as d5_ocf,
       isnull(MAX([d6_ocf]),0.0) as d6_ocf,
       isnull(MAX([d7_ocf]),0.0) as d7_ocf,
       isnull(MAX([d8_ocf]),0.0) as d8_ocf,
       @bvID
from
    (
        select base.daystoArrival,
               (case base.Occupancy_DT when @d1 then Rooms_Sold end) as [d1_sold],
					(case base.Occupancy_DT when @d2 then Rooms_Sold end) as [d2_sold],
					(case base.Occupancy_DT when @d3 then Rooms_Sold end) as [d3_sold],
					(case base.Occupancy_DT when @d4 then Rooms_Sold end) as [d4_sold],
					(case base.Occupancy_DT when @d5 then Rooms_Sold end) as [d5_sold],
					(case base.Occupancy_DT when @d6 then Rooms_Sold end) as [d6_sold],
					(case base.Occupancy_DT when @d7 then Rooms_Sold end) as [d7_sold],
					(case base.Occupancy_DT when @d8 then Rooms_Sold end) as [d8_sold],
					(case base.Occupancy_DT when @d1 then Occupancy_NBR end) as [d1_ocf],
					(case base.Occupancy_DT when @d2 then Occupancy_NBR end) as [d2_ocf],
					(case base.Occupancy_DT when @d3 then Occupancy_NBR end) as [d3_ocf],
					(case base.Occupancy_DT when @d4 then Occupancy_NBR end) as [d4_ocf],
					(case base.Occupancy_DT when @d5 then Occupancy_NBR end) as [d5_ocf],
					(case base.Occupancy_DT when @d6 then Occupancy_NBR end) as [d6_ocf],
					(case base.Occupancy_DT when @d7 then Occupancy_NBR end) as [d7_ocf],
					(case base.Occupancy_DT when @d8 then Occupancy_NBR end) as [d8_ocf]
        from
            (
            select cast(a.calendar_date as date)Occupancy_DT ,cast(b.calendar_date as date) Business_DT,
            DATEDIFF(day,b.calendar_date,a.calendar_date) as daystoArrival from calendar_dim a,calendar_dim b
            where b.calendar_date between DATEADD(DAY, -@pace_days,a.calendar_date ) and a.calendar_date
            and a.calendar_date in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
            ) base
            left join
            (
            select bvs.daystoArrival,bvs.occupancy_dt,sum(bvs.rooms_sold) as Rooms_Sold from #temp_pace_all  bvs where bvs.bvId = @bvID
            group by Occupancy_DT,daystoArrival,rooms_sold
            ) a on base.Occupancy_DT=a.Occupancy_DT
            and base.daystoArrival=a.daystoArrival
            left join
            (
            select bvf.occupancy_dt,bvf.daystoArrival,sum(bvf.Occupancy_NBR) as Occupancy_NBR from #temp_pace_all bvf where bvf.bvId = @bvID
            group by Occupancy_DT,daystoArrival
            ) b on base.Occupancy_DT=b.Occupancy_DT
            and base.daystoArrival=b.daystoArrival
    ) as x group by daystoArrival
order by daystoArrival
end

Fetch next from bv_cursor into @bvID
END

Close bv_cursor
    DEALLOCATE bv_cursor



Declare @rcID int
		Declare rc_cursor CURSOR FOR

select rcid from @rc_loop

    Open rc_cursor
		Fetch next from rc_cursor into @rcID

    while(@@FETCH_STATUS=0)
BEGIN
			IF(@rcID > 0)
begin
insert into #temp_pace_rc
select daystoArrival,
       isnull(SUM([d1_sold]),0.0) as d1_sold,
       isnull(SUM([d2_sold]),0.0) as d2_sold,
       isnull(SUM([d3_sold]),0.0) as d3_sold,
       isnull(SUM([d4_sold]),0.0) as d4_sold,
       isnull(SUM([d5_sold]),0.0) as d5_sold,
       isnull(SUM([d6_sold]),0.0) as d6_sold,
       isnull(SUM([d7_sold]),0.0) as d7_sold,
       isnull(SUM([d8_sold]),0.0) as d8_sold,
       isnull(MAX([d1_ocf]),0.0) as d1_ocf,
       isnull(MAX([d2_ocf]),0.0) as d2_ocf,
       isnull(MAX([d3_ocf]),0.0) as d3_ocf,
       isnull(MAX([d4_ocf]),0.0) as d4_ocf,
       isnull(MAX([d5_ocf]),0.0) as d5_ocf,
       isnull(MAX([d6_ocf]),0.0) as d6_ocf,
       isnull(MAX([d7_ocf]),0.0) as d7_ocf,
       isnull(MAX([d8_ocf]),0.0) as d8_ocf,
       isnull(MAX([d1_lrv]),0.0) as d1_lrv,
       isnull(MAX([d2_lrv]),0.0) as d2_lrv,
       isnull(MAX([d3_lrv]),0.0) as d3_lrv,
       isnull(MAX([d4_lrv]),0.0) as d4_lrv,
       isnull(MAX([d5_lrv]),0.0) as d5_lrv,
       isnull(MAX([d6_lrv]),0.0) as d6_lrv,
       isnull(MAX([d7_lrv]),0.0) as d7_lrv,
       isnull(MAX([d8_lrv]),0.0) as d8_lrv,
       @rcID
from
    (
        select base.daystoArrival,
               (case base.Occupancy_DT when @d1 then Rooms_Sold end) as [d1_sold],
				(case base.Occupancy_DT when @d2 then Rooms_Sold end) as [d2_sold],
				(case base.Occupancy_DT when @d3 then Rooms_Sold end) as [d3_sold],
				(case base.Occupancy_DT when @d4 then Rooms_Sold end) as [d4_sold],
				(case base.Occupancy_DT when @d5 then Rooms_Sold end) as [d5_sold],
				(case base.Occupancy_DT when @d6 then Rooms_Sold end) as [d6_sold],
				(case base.Occupancy_DT when @d7 then Rooms_Sold end) as [d7_sold],
				(case base.Occupancy_DT when @d8 then Rooms_Sold end) as [d8_sold],
				(case base.Occupancy_DT when @d1 then Occupancy_NBR end) as [d1_ocf],
				(case base.Occupancy_DT when @d2 then Occupancy_NBR end) as [d2_ocf],
				(case base.Occupancy_DT when @d3 then Occupancy_NBR end) as [d3_ocf],
				(case base.Occupancy_DT when @d4 then Occupancy_NBR end) as [d4_ocf],
				(case base.Occupancy_DT when @d5 then Occupancy_NBR end) as [d5_ocf],
				(case base.Occupancy_DT when @d6 then Occupancy_NBR end) as [d6_ocf],
				(case base.Occupancy_DT when @d7 then Occupancy_NBR end) as [d7_ocf],
				(case base.Occupancy_DT when @d8 then Occupancy_NBR end) as [d8_ocf],
				(case base.Occupancy_DT when @d1 then Lrv end) as [d1_lrv],
				(case base.Occupancy_DT when @d2 then Lrv end) as [d2_lrv],
				(case base.Occupancy_DT when @d3 then Lrv end) as [d3_lrv],
				(case base.Occupancy_DT when @d4 then Lrv end) as [d4_lrv],
				(case base.Occupancy_DT when @d5 then Lrv end) as [d5_lrv],
				(case base.Occupancy_DT when @d6 then Lrv end) as [d6_lrv],
				(case base.Occupancy_DT when @d7 then Lrv end) as [d7_lrv],
				(case base.Occupancy_DT when @d8 then Lrv end) as [d8_lrv]
        from
            (
            select cast(a.calendar_date as date)Occupancy_DT ,cast(b.calendar_date as date) Business_DT,
            DATEDIFF(day,b.calendar_date,a.calendar_date) as daystoArrival from calendar_dim a,calendar_dim b
            where b.calendar_date between DATEADD(DAY, -@pace_days,a.calendar_date ) and a.calendar_date
            and a.calendar_date in (@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8)
            ) base
            left join
            (
            select occupancy_dt,daystoArrival,sum(tpa.rooms_sold) as Rooms_Sold from #temp_pace_all  tpa where tpa.rcId = @rcID
            group by Occupancy_DT,daystoArrival,rooms_sold
            ) a on base.Occupancy_DT=a.Occupancy_DT
            and base.daystoArrival=a.daystoArrival
            left join
            (
            select occupancy_dt,daystoArrival,sum(tpa.Occupancy_NBR) as Occupancy_NBR from #temp_pace_all tpa where tpa.rcId = @rcID
            group by Occupancy_DT,daystoArrival
            ) b on base.Occupancy_DT=b.Occupancy_DT
            and base.daystoArrival=b.daystoArrival
            left join
            (
            select occupancy_dt,daystoArrival, sum(lrv) as lrv from #temp_pace_all tpa where tpa.rcId = @rcID
            group by Occupancy_DT,daystoArrival
            ) c on base.Occupancy_DT=c.Occupancy_DT
            and base.daystoArrival=c.daystoArrival
    ) as x group by daystoArrival
order by daystoArrival
end

Fetch next from rc_cursor into @rcID
END

Close rc_cursor
    DEALLOCATE rc_cursor

select
    base.daystoArrival,
    hotel.d1_sold d1_prop_sold,
    hotel.d2_sold d2_prop_sold,
    hotel.d3_sold d3_prop_sold,
    hotel.d4_sold d4_prop_sold,
    hotel.d5_sold d5_prop_sold,
    hotel.d6_sold d6_prop_sold,
    hotel.d7_sold d7_prop_sold,
    hotel.d8_sold d8_prop_sold,
    hotel.d1_lrv d1_prop_lrv,
    hotel.d2_lrv d2_prop_lrv,
    hotel.d3_lrv d3_prop_lrv,
    hotel.d4_lrv d4_prop_lrv,
    hotel.d5_lrv d5_prop_lrv,
    hotel.d6_lrv d6_prop_lrv,
    hotel.d7_lrv d7_prop_lrv,
    hotel.d8_lrv d8_prop_lrv,
    hotel.d1_occupancy_NBR d1_prop_occfcst,
    hotel.d2_occupancy_NBR d2_prop_occfcst,
    hotel.d3_occupancy_NBR d3_prop_occfcst,
    hotel.d4_occupancy_NBR d4_prop_occfcst,
    hotel.d5_occupancy_NBR d5_prop_occfcst,
    hotel.d6_occupancy_NBR d6_prop_occfcst,
    hotel.d7_occupancy_NBR d7_prop_occfcst,
    hotel.d8_occupancy_NBR d8_prop_occfcst,
    hotel.d1_occupancy_perc d1_prop_occfcst_perc,
    hotel.d2_occupancy_perc d2_prop_occfcst_perc,
    hotel.d3_occupancy_perc d3_prop_occfcst_perc,
    hotel.d4_occupancy_perc d4_prop_occfcst_perc,
    hotel.d5_occupancy_perc d5_prop_occfcst_perc,
    hotel.d6_occupancy_perc d6_prop_occfcst_perc,
    hotel.d7_occupancy_perc d7_prop_occfcst_perc,
    hotel.d8_occupancy_perc d8_prop_occfcst_perc,
    hotel.d1_occupancy_PhyCap_perc d1_prop_occfcst_PhyCap_perc,
    hotel.d2_occupancy_PhyCap_perc d2_prop_occfcst_PhyCap_perc,
    hotel.d3_occupancy_PhyCap_perc d3_prop_occfcst_PhyCap_perc,
    hotel.d4_occupancy_PhyCap_perc d4_prop_occfcst_PhyCap_perc,
    hotel.d5_occupancy_PhyCap_perc d5_prop_occfcst_PhyCap_perc,
    hotel.d6_occupancy_PhyCap_perc d6_prop_occfcst_PhyCap_perc,
    hotel.d7_occupancy_PhyCap_perc d7_prop_occfcst_PhyCap_perc,
    hotel.d8_occupancy_PhyCap_perc d8_prop_occfcst_PhyCap_perc,

    trans.d1_sold d1_trans_sold,
    trans.d2_sold d2_trans_sold,
    trans.d3_sold d3_trans_sold,
    trans.d4_sold d4_trans_sold,
    trans.d5_sold d5_trans_sold,
    trans.d6_sold d6_trans_sold,
    trans.d7_sold d7_trans_sold,
    trans.d8_sold d8_trans_sold,
    trans.d1_occupancy_NBR d1_trans_occfcst,
    trans.d2_occupancy_NBR d2_trans_occfcst,
    trans.d3_occupancy_NBR d3_trans_occfcst,
    trans.d4_occupancy_NBR d4_trans_occfcst,
    trans.d5_occupancy_NBR d5_trans_occfcst,
    trans.d6_occupancy_NBR d6_trans_occfcst,
    trans.d7_occupancy_NBR d7_trans_occfcst,
    trans.d8_occupancy_NBR d8_trans_occfcst,

    grp.d1_sold d1_grp_sold,
    grp.d2_sold d2_grp_sold,
    grp.d3_sold d3_grp_sold,
    grp.d4_sold d4_grp_sold,
    grp.d5_sold d5_grp_sold,
    grp.d6_sold d6_grp_sold,
    grp.d7_sold d7_grp_sold,
    grp.d8_sold d8_grp_sold,
    grp.d1_occupancy_NBR d1_grp_occfcst,
    grp.d2_occupancy_NBR d2_grp_occfcst,
    grp.d3_occupancy_NBR d3_grp_occfcst,
    grp.d4_occupancy_NBR d4_grp_occfcst,
    grp.d5_occupancy_NBR d5_grp_occfcst,
    grp.d6_occupancy_NBR d6_grp_occfcst,
    grp.d7_occupancy_NBR d7_grp_occfcst,
    grp.d8_occupancy_NBR d8_grp_occfcst,

    fg1.d1_sold d1_fg1_sold,
    fg1.d2_sold d2_fg1_sold,
    fg1.d3_sold d3_fg1_sold,
    fg1.d4_sold d4_fg1_sold,
    fg1.d5_sold d5_fg1_sold,
    fg1.d6_sold d6_fg1_sold,
    fg1.d7_sold d7_fg1_sold,
    fg1.d8_sold d8_fg1_sold,
    fg1.d1_occupancy_NBR d1_fg1_occfcst,
    fg1.d2_occupancy_NBR d2_fg1_occfcst,
    fg1.d3_occupancy_NBR d3_fg1_occfcst,
    fg1.d4_occupancy_NBR d4_fg1_occfcst,
    fg1.d5_occupancy_NBR d5_fg1_occfcst,
    fg1.d6_occupancy_NBR d6_fg1_occfcst,
    fg1.d7_occupancy_NBR d7_fg1_occfcst,
    fg1.d8_occupancy_NBR d8_fg1_occfcst,


    fg2.d1_sold d1_fg2_sold,
    fg2.d2_sold d2_fg2_sold,
    fg2.d3_sold d3_fg2_sold,
    fg2.d4_sold d4_fg2_sold,
    fg2.d5_sold d5_fg2_sold,
    fg2.d6_sold d6_fg2_sold,
    fg2.d7_sold d7_fg2_sold,
    fg2.d8_sold d8_fg2_sold,
    fg2.d1_occupancy_NBR d1_fg2_occfcst,
    fg2.d2_occupancy_NBR d2_fg2_occfcst,
    fg2.d3_occupancy_NBR d3_fg2_occfcst,
    fg2.d4_occupancy_NBR d4_fg2_occfcst,
    fg2.d5_occupancy_NBR d5_fg2_occfcst,
    fg2.d6_occupancy_NBR d6_fg2_occfcst,
    fg2.d7_occupancy_NBR d7_fg2_occfcst,
    fg2.d8_occupancy_NBR d8_fg2_occfcst,


    fg3.d1_sold d1_fg3_sold,
    fg3.d2_sold d2_fg3_sold,
    fg3.d3_sold d3_fg3_sold,
    fg3.d4_sold d4_fg3_sold,
    fg3.d5_sold d5_fg3_sold,
    fg3.d6_sold d6_fg3_sold,
    fg3.d7_sold d7_fg3_sold,
    fg3.d8_sold d8_fg3_sold,
    fg3.d1_occupancy_NBR d1_fg3_occfcst,
    fg3.d2_occupancy_NBR d2_fg3_occfcst,
    fg3.d3_occupancy_NBR d3_fg3_occfcst,
    fg3.d4_occupancy_NBR d4_fg3_occfcst,
    fg3.d5_occupancy_NBR d5_fg3_occfcst,
    fg3.d6_occupancy_NBR d6_fg3_occfcst,
    fg3.d7_occupancy_NBR d7_fg3_occfcst,
    fg3.d8_occupancy_NBR d8_fg3_occfcst,

    fg4.d1_sold d1_fg4_sold,
    fg4.d2_sold d2_fg4_sold,
    fg4.d3_sold d3_fg4_sold,
    fg4.d4_sold d4_fg4_sold,
    fg4.d5_sold d5_fg4_sold,
    fg4.d6_sold d6_fg4_sold,
    fg4.d7_sold d7_fg4_sold,
    fg4.d8_sold d8_fg4_sold,
    fg4.d1_occupancy_NBR d1_fg4_occfcst,
    fg4.d2_occupancy_NBR d2_fg4_occfcst,
    fg4.d3_occupancy_NBR d3_fg4_occfcst,
    fg4.d4_occupancy_NBR d4_fg4_occfcst,
    fg4.d5_occupancy_NBR d5_fg4_occfcst,
    fg4.d6_occupancy_NBR d6_fg4_occfcst,
    fg4.d7_occupancy_NBR d7_fg4_occfcst,
    fg4.d8_occupancy_NBR d8_fg4_occfcst,

    fg5.d1_sold d1_fg5_sold,
    fg5.d2_sold d2_fg5_sold,
    fg5.d3_sold d3_fg5_sold,
    fg5.d4_sold d4_fg5_sold,
    fg5.d5_sold d5_fg5_sold,
    fg5.d6_sold d6_fg5_sold,
    fg5.d7_sold d7_fg5_sold,
    fg5.d8_sold d8_fg5_sold,
    fg5.d1_occupancy_NBR d1_fg5_occfcst,
    fg5.d2_occupancy_NBR d2_fg5_occfcst,
    fg5.d3_occupancy_NBR d3_fg5_occfcst,
    fg5.d4_occupancy_NBR d4_fg5_occfcst,
    fg5.d5_occupancy_NBR d5_fg5_occfcst,
    fg5.d6_occupancy_NBR d6_fg5_occfcst,
    fg5.d7_occupancy_NBR d7_fg5_occfcst,
    fg5.d8_occupancy_NBR d8_fg5_occfcst,

    fg6.d1_sold d1_fg6_sold,
    fg6.d2_sold d2_fg6_sold,
    fg6.d3_sold d3_fg6_sold,
    fg6.d4_sold d4_fg6_sold,
    fg6.d5_sold d5_fg6_sold,
    fg6.d6_sold d6_fg6_sold,
    fg6.d7_sold d7_fg6_sold,
    fg6.d8_sold d8_fg6_sold,
    fg6.d1_occupancy_NBR d1_fg6_occfcst,
    fg6.d2_occupancy_NBR d2_fg6_occfcst,
    fg6.d3_occupancy_NBR d3_fg6_occfcst,
    fg6.d4_occupancy_NBR d4_fg6_occfcst,
    fg6.d5_occupancy_NBR d5_fg6_occfcst,
    fg6.d6_occupancy_NBR d6_fg6_occfcst,
    fg6.d7_occupancy_NBR d7_fg6_occfcst,
    fg6.d8_occupancy_NBR d8_fg6_occfcst,

    fg7.d1_sold d1_fg7_sold,
    fg7.d2_sold d2_fg7_sold,
    fg7.d3_sold d3_fg7_sold,
    fg7.d4_sold d4_fg7_sold,
    fg7.d5_sold d5_fg7_sold,
    fg7.d6_sold d6_fg7_sold,
    fg7.d7_sold d7_fg7_sold,
    fg7.d8_sold d8_fg7_sold,
    fg7.d1_occupancy_NBR d1_fg7_occfcst,
    fg7.d2_occupancy_NBR d2_fg7_occfcst,
    fg7.d3_occupancy_NBR d3_fg7_occfcst,
    fg7.d4_occupancy_NBR d4_fg7_occfcst,
    fg7.d5_occupancy_NBR d5_fg7_occfcst,
    fg7.d6_occupancy_NBR d6_fg7_occfcst,
    fg7.d7_occupancy_NBR d7_fg7_occfcst,
    fg7.d8_occupancy_NBR d8_fg7_occfcst,

    fg8.d1_sold d1_fg8_sold,
    fg8.d2_sold d2_fg8_sold,
    fg8.d3_sold d3_fg8_sold,
    fg8.d4_sold d4_fg8_sold,
    fg8.d5_sold d5_fg8_sold,
    fg8.d6_sold d6_fg8_sold,
    fg8.d7_sold d7_fg8_sold,
    fg8.d8_sold d8_fg8_sold,
    fg8.d1_occupancy_NBR d1_fg8_occfcst,
    fg8.d2_occupancy_NBR d2_fg8_occfcst,
    fg8.d3_occupancy_NBR d3_fg8_occfcst,
    fg8.d4_occupancy_NBR d4_fg8_occfcst,
    fg8.d5_occupancy_NBR d5_fg8_occfcst,
    fg8.d6_occupancy_NBR d6_fg8_occfcst,
    fg8.d7_occupancy_NBR d7_fg8_occfcst,
    fg8.d8_occupancy_NBR d8_fg8_occfcst,

    fg9.d1_sold d1_fg9_sold,
    fg9.d2_sold d2_fg9_sold,
    fg9.d3_sold d3_fg9_sold,
    fg9.d4_sold d4_fg9_sold,
    fg9.d5_sold d5_fg9_sold,
    fg9.d6_sold d6_fg9_sold,
    fg9.d7_sold d7_fg9_sold,
    fg9.d8_sold d8_fg9_sold,
    fg9.d1_occupancy_NBR d1_fg9_occfcst,
    fg9.d2_occupancy_NBR d2_fg9_occfcst,
    fg9.d3_occupancy_NBR d3_fg9_occfcst,
    fg9.d4_occupancy_NBR d4_fg9_occfcst,
    fg9.d5_occupancy_NBR d5_fg9_occfcst,
    fg9.d6_occupancy_NBR d6_fg9_occfcst,
    fg9.d7_occupancy_NBR d7_fg9_occfcst,
    fg9.d8_occupancy_NBR d8_fg9_occfcst,

    fg10.d1_sold d1_fg10_sold,
    fg10.d2_sold d2_fg10_sold,
    fg10.d3_sold d3_fg10_sold,
    fg10.d4_sold d4_fg10_sold,
    fg10.d5_sold d5_fg10_sold,
    fg10.d6_sold d6_fg10_sold,
    fg10.d7_sold d7_fg10_sold,
    fg10.d8_sold d8_fg10_sold,
    fg10.d1_occupancy_NBR d1_fg10_occfcst,
    fg10.d2_occupancy_NBR d2_fg10_occfcst,
    fg10.d3_occupancy_NBR d3_fg10_occfcst,
    fg10.d4_occupancy_NBR d4_fg10_occfcst,
    fg10.d5_occupancy_NBR d5_fg10_occfcst,
    fg10.d6_occupancy_NBR d6_fg10_occfcst,
    fg10.d7_occupancy_NBR d7_fg10_occfcst,
    fg10.d8_occupancy_NBR d8_fg10_occfcst,

    fg11.d1_sold d1_fg11_sold,
    fg11.d2_sold d2_fg11_sold,
    fg11.d3_sold d3_fg11_sold,
    fg11.d4_sold d4_fg11_sold,
    fg11.d5_sold d5_fg11_sold,
    fg11.d6_sold d6_fg11_sold,
    fg11.d7_sold d7_fg11_sold,
    fg11.d8_sold d8_fg11_sold,
    fg11.d1_occupancy_NBR d1_fg11_occfcst,
    fg11.d2_occupancy_NBR d2_fg11_occfcst,
    fg11.d3_occupancy_NBR d3_fg11_occfcst,
    fg11.d4_occupancy_NBR d4_fg11_occfcst,
    fg11.d5_occupancy_NBR d5_fg11_occfcst,
    fg11.d6_occupancy_NBR d6_fg11_occfcst,
    fg11.d7_occupancy_NBR d7_fg11_occfcst,
    fg11.d8_occupancy_NBR d8_fg11_occfcst,

    fg12.d1_sold d1_fg12_sold,
    fg12.d2_sold d2_fg12_sold,
    fg12.d3_sold d3_fg12_sold,
    fg12.d4_sold d4_fg12_sold,
    fg12.d5_sold d5_fg12_sold,
    fg12.d6_sold d6_fg12_sold,
    fg12.d7_sold d7_fg12_sold,
    fg12.d8_sold d8_fg12_sold,
    fg12.d1_occupancy_NBR d1_fg12_occfcst,
    fg12.d2_occupancy_NBR d2_fg12_occfcst,
    fg12.d3_occupancy_NBR d3_fg12_occfcst,
    fg12.d4_occupancy_NBR d4_fg12_occfcst,
    fg12.d5_occupancy_NBR d5_fg12_occfcst,
    fg12.d6_occupancy_NBR d6_fg12_occfcst,
    fg12.d7_occupancy_NBR d7_fg12_occfcst,
    fg12.d8_occupancy_NBR d8_fg12_occfcst,

    fg13.d1_sold d1_fg13_sold,
    fg13.d2_sold d2_fg13_sold,
    fg13.d3_sold d3_fg13_sold,
    fg13.d4_sold d4_fg13_sold,
    fg13.d5_sold d5_fg13_sold,
    fg13.d6_sold d6_fg13_sold,
    fg13.d7_sold d7_fg13_sold,
    fg13.d8_sold d8_fg13_sold,
    fg13.d1_occupancy_NBR d1_fg13_occfcst,
    fg13.d2_occupancy_NBR d2_fg13_occfcst,
    fg13.d3_occupancy_NBR d3_fg13_occfcst,
    fg13.d4_occupancy_NBR d4_fg13_occfcst,
    fg13.d5_occupancy_NBR d5_fg13_occfcst,
    fg13.d6_occupancy_NBR d6_fg13_occfcst,
    fg13.d7_occupancy_NBR d7_fg13_occfcst,
    fg13.d8_occupancy_NBR d8_fg13_occfcst,

    fg14.d1_sold d1_fg14_sold,
    fg14.d2_sold d2_fg14_sold,
    fg14.d3_sold d3_fg14_sold,
    fg14.d4_sold d4_fg14_sold,
    fg14.d5_sold d5_fg14_sold,
    fg14.d6_sold d6_fg14_sold,
    fg14.d7_sold d7_fg14_sold,
    fg14.d8_sold d8_fg14_sold,
    fg14.d1_occupancy_NBR d1_fg14_occfcst,
    fg14.d2_occupancy_NBR d2_fg14_occfcst,
    fg14.d3_occupancy_NBR d3_fg14_occfcst,
    fg14.d4_occupancy_NBR d4_fg14_occfcst,
    fg14.d5_occupancy_NBR d5_fg14_occfcst,
    fg14.d6_occupancy_NBR d6_fg14_occfcst,
    fg14.d7_occupancy_NBR d7_fg14_occfcst,
    fg14.d8_occupancy_NBR d8_fg14_occfcst,

    fg15.d1_sold d1_fg15_sold,
    fg15.d2_sold d2_fg15_sold,
    fg15.d3_sold d3_fg15_sold,
    fg15.d4_sold d4_fg15_sold,
    fg15.d5_sold d5_fg15_sold,
    fg15.d6_sold d6_fg15_sold,
    fg15.d7_sold d7_fg15_sold,
    fg15.d8_sold d8_fg15_sold,
    fg15.d1_occupancy_NBR d1_fg15_occfcst,
    fg15.d2_occupancy_NBR d2_fg15_occfcst,
    fg15.d3_occupancy_NBR d3_fg15_occfcst,
    fg15.d4_occupancy_NBR d4_fg15_occfcst,
    fg15.d5_occupancy_NBR d5_fg15_occfcst,
    fg15.d6_occupancy_NBR d6_fg15_occfcst,
    fg15.d7_occupancy_NBR d7_fg15_occfcst,
    fg15.d8_occupancy_NBR d8_fg15_occfcst,

    fg16.d1_sold d1_fg16_sold,
    fg16.d2_sold d2_fg16_sold,
    fg16.d3_sold d3_fg16_sold,
    fg16.d4_sold d4_fg16_sold,
    fg16.d5_sold d5_fg16_sold,
    fg16.d6_sold d6_fg16_sold,
    fg16.d7_sold d7_fg16_sold,
    fg16.d8_sold d8_fg16_sold,
    fg16.d1_occupancy_NBR d1_fg16_occfcst,
    fg16.d2_occupancy_NBR d2_fg16_occfcst,
    fg16.d3_occupancy_NBR d3_fg16_occfcst,
    fg16.d4_occupancy_NBR d4_fg16_occfcst,
    fg16.d5_occupancy_NBR d5_fg16_occfcst,
    fg16.d6_occupancy_NBR d6_fg16_occfcst,
    fg16.d7_occupancy_NBR d7_fg16_occfcst,
    fg16.d8_occupancy_NBR d8_fg16_occfcst,

    fg17.d1_sold d1_fg17_sold,
    fg17.d2_sold d2_fg17_sold,
    fg17.d3_sold d3_fg17_sold,
    fg17.d4_sold d4_fg17_sold,
    fg17.d5_sold d5_fg17_sold,
    fg17.d6_sold d6_fg17_sold,
    fg17.d7_sold d7_fg17_sold,
    fg17.d8_sold d8_fg17_sold,
    fg17.d1_occupancy_NBR d1_fg17_occfcst,
    fg17.d2_occupancy_NBR d2_fg17_occfcst,
    fg17.d3_occupancy_NBR d3_fg17_occfcst,
    fg17.d4_occupancy_NBR d4_fg17_occfcst,
    fg17.d5_occupancy_NBR d5_fg17_occfcst,
    fg17.d6_occupancy_NBR d6_fg17_occfcst,
    fg17.d7_occupancy_NBR d7_fg17_occfcst,
    fg17.d8_occupancy_NBR d8_fg17_occfcst,

    fg18.d1_sold d1_fg18_sold,
    fg18.d2_sold d2_fg18_sold,
    fg18.d3_sold d3_fg18_sold,
    fg18.d4_sold d4_fg18_sold,
    fg18.d5_sold d5_fg18_sold,
    fg18.d6_sold d6_fg18_sold,
    fg18.d7_sold d7_fg18_sold,
    fg18.d8_sold d8_fg18_sold,
    fg18.d1_occupancy_NBR d1_fg18_occfcst,
    fg18.d2_occupancy_NBR d2_fg18_occfcst,
    fg18.d3_occupancy_NBR d3_fg18_occfcst,
    fg18.d4_occupancy_NBR d4_fg18_occfcst,
    fg18.d5_occupancy_NBR d5_fg18_occfcst,
    fg18.d6_occupancy_NBR d6_fg18_occfcst,
    fg18.d7_occupancy_NBR d7_fg18_occfcst,
    fg18.d8_occupancy_NBR d8_fg18_occfcst,

    fg19.d1_sold d1_fg19_sold,
    fg19.d2_sold d2_fg19_sold,
    fg19.d3_sold d3_fg19_sold,
    fg19.d4_sold d4_fg19_sold,
    fg19.d5_sold d5_fg19_sold,
    fg19.d6_sold d6_fg19_sold,
    fg19.d7_sold d7_fg19_sold,
    fg19.d8_sold d8_fg19_sold,
    fg19.d1_occupancy_NBR d1_fg19_occfcst,
    fg19.d2_occupancy_NBR d2_fg19_occfcst,
    fg19.d3_occupancy_NBR d3_fg19_occfcst,
    fg19.d4_occupancy_NBR d4_fg19_occfcst,
    fg19.d5_occupancy_NBR d5_fg19_occfcst,
    fg19.d6_occupancy_NBR d6_fg19_occfcst,
    fg19.d7_occupancy_NBR d7_fg19_occfcst,
    fg19.d8_occupancy_NBR d8_fg19_occfcst,

    fg20.d1_sold d1_fg20_sold,
    fg20.d2_sold d2_fg20_sold,
    fg20.d3_sold d3_fg20_sold,
    fg20.d4_sold d4_fg20_sold,
    fg20.d5_sold d5_fg20_sold,
    fg20.d6_sold d6_fg20_sold,
    fg20.d7_sold d7_fg20_sold,
    fg20.d8_sold d8_fg20_sold,
    fg20.d1_occupancy_NBR d1_fg20_occfcst,
    fg20.d2_occupancy_NBR d2_fg20_occfcst,
    fg20.d3_occupancy_NBR d3_fg20_occfcst,
    fg20.d4_occupancy_NBR d4_fg20_occfcst,
    fg20.d5_occupancy_NBR d5_fg20_occfcst,
    fg20.d6_occupancy_NBR d6_fg20_occfcst,
    fg20.d7_occupancy_NBR d7_fg20_occfcst,
    fg20.d8_occupancy_NBR d8_fg20_occfcst,

    fg21.d1_sold d1_fg21_sold,
    fg21.d2_sold d2_fg21_sold,
    fg21.d3_sold d3_fg21_sold,
    fg21.d4_sold d4_fg21_sold,
    fg21.d5_sold d5_fg21_sold,
    fg21.d6_sold d6_fg21_sold,
    fg21.d7_sold d7_fg21_sold,
    fg21.d8_sold d8_fg21_sold,
    fg21.d1_occupancy_NBR d1_fg21_occfcst,
    fg21.d2_occupancy_NBR d2_fg21_occfcst,
    fg21.d3_occupancy_NBR d3_fg21_occfcst,
    fg21.d4_occupancy_NBR d4_fg21_occfcst,
    fg21.d5_occupancy_NBR d5_fg21_occfcst,
    fg21.d6_occupancy_NBR d6_fg21_occfcst,
    fg21.d7_occupancy_NBR d7_fg21_occfcst,
    fg21.d8_occupancy_NBR d8_fg21_occfcst,

    fg22.d1_sold d1_fg22_sold,
    fg22.d2_sold d2_fg22_sold,
    fg22.d3_sold d3_fg22_sold,
    fg22.d4_sold d4_fg22_sold,
    fg22.d5_sold d5_fg22_sold,
    fg22.d6_sold d6_fg22_sold,
    fg22.d7_sold d7_fg22_sold,
    fg22.d8_sold d8_fg22_sold,
    fg22.d1_occupancy_NBR d1_fg22_occfcst,
    fg22.d2_occupancy_NBR d2_fg22_occfcst,
    fg22.d3_occupancy_NBR d3_fg22_occfcst,
    fg22.d4_occupancy_NBR d4_fg22_occfcst,
    fg22.d5_occupancy_NBR d5_fg22_occfcst,
    fg22.d6_occupancy_NBR d6_fg22_occfcst,
    fg22.d7_occupancy_NBR d7_fg22_occfcst,
    fg22.d8_occupancy_NBR d8_fg22_occfcst,

    fg23.d1_sold d1_fg23_sold,
    fg23.d2_sold d2_fg23_sold,
    fg23.d3_sold d3_fg23_sold,
    fg23.d4_sold d4_fg23_sold,
    fg23.d5_sold d5_fg23_sold,
    fg23.d6_sold d6_fg23_sold,
    fg23.d7_sold d7_fg23_sold,
    fg23.d8_sold d8_fg23_sold,
    fg23.d1_occupancy_NBR d1_fg23_occfcst,
    fg23.d2_occupancy_NBR d2_fg23_occfcst,
    fg23.d3_occupancy_NBR d3_fg23_occfcst,
    fg23.d4_occupancy_NBR d4_fg23_occfcst,
    fg23.d5_occupancy_NBR d5_fg23_occfcst,
    fg23.d6_occupancy_NBR d6_fg23_occfcst,
    fg23.d7_occupancy_NBR d7_fg23_occfcst,
    fg23.d8_occupancy_NBR d8_fg23_occfcst,

    fg24.d1_sold d1_fg24_sold,
    fg24.d2_sold d2_fg24_sold,
    fg24.d3_sold d3_fg24_sold,
    fg24.d4_sold d4_fg24_sold,
    fg24.d5_sold d5_fg24_sold,
    fg24.d6_sold d6_fg24_sold,
    fg24.d7_sold d7_fg24_sold,
    fg24.d8_sold d8_fg24_sold,
    fg24.d1_occupancy_NBR d1_fg24_occfcst,
    fg24.d2_occupancy_NBR d2_fg24_occfcst,
    fg24.d3_occupancy_NBR d3_fg24_occfcst,
    fg24.d4_occupancy_NBR d4_fg24_occfcst,
    fg24.d5_occupancy_NBR d5_fg24_occfcst,
    fg24.d6_occupancy_NBR d6_fg24_occfcst,
    fg24.d7_occupancy_NBR d7_fg24_occfcst,
    fg24.d8_occupancy_NBR d8_fg24_occfcst,

    fg25.d1_sold d1_fg25_sold,
    fg25.d2_sold d2_fg25_sold,
    fg25.d3_sold d3_fg25_sold,
    fg25.d4_sold d4_fg25_sold,
    fg25.d5_sold d5_fg25_sold,
    fg25.d6_sold d6_fg25_sold,
    fg25.d7_sold d7_fg25_sold,
    fg25.d8_sold d8_fg25_sold,
    fg25.d1_occupancy_NBR d1_fg25_occfcst,
    fg25.d2_occupancy_NBR d2_fg25_occfcst,
    fg25.d3_occupancy_NBR d3_fg25_occfcst,
    fg25.d4_occupancy_NBR d4_fg25_occfcst,
    fg25.d5_occupancy_NBR d5_fg25_occfcst,
    fg25.d6_occupancy_NBR d6_fg25_occfcst,
    fg25.d7_occupancy_NBR d7_fg25_occfcst,
    fg25.d8_occupancy_NBR d8_fg25_occfcst,

    bv1.d1_sold d1_bv1_sold,
    bv1.d2_sold d2_bv1_sold,
    bv1.d3_sold d3_bv1_sold,
    bv1.d4_sold d4_bv1_sold,
    bv1.d5_sold d5_bv1_sold,
    bv1.d6_sold d6_bv1_sold,
    bv1.d7_sold d7_bv1_sold,
    bv1.d8_sold d8_bv1_sold,
    bv1.d1_occupancy_NBR d1_bv1_occfcst,
    bv1.d2_occupancy_NBR d2_bv1_occfcst,
    bv1.d3_occupancy_NBR d3_bv1_occfcst,
    bv1.d4_occupancy_NBR d4_bv1_occfcst,
    bv1.d5_occupancy_NBR d5_bv1_occfcst,
    bv1.d6_occupancy_NBR d6_bv1_occfcst,
    bv1.d7_occupancy_NBR d7_bv1_occfcst,
    bv1.d8_occupancy_NBR d8_bv1_occfcst,


    bv2.d1_sold d1_bv2_sold,
    bv2.d2_sold d2_bv2_sold,
    bv2.d3_sold d3_bv2_sold,
    bv2.d4_sold d4_bv2_sold,
    bv2.d5_sold d5_bv2_sold,
    bv2.d6_sold d6_bv2_sold,
    bv2.d7_sold d7_bv2_sold,
    bv2.d8_sold d8_bv2_sold,
    bv2.d1_occupancy_NBR d1_bv2_occfcst,
    bv2.d2_occupancy_NBR d2_bv2_occfcst,
    bv2.d3_occupancy_NBR d3_bv2_occfcst,
    bv2.d4_occupancy_NBR d4_bv2_occfcst,
    bv2.d5_occupancy_NBR d5_bv2_occfcst,
    bv2.d6_occupancy_NBR d6_bv2_occfcst,
    bv2.d7_occupancy_NBR d7_bv2_occfcst,
    bv2.d8_occupancy_NBR d8_bv2_occfcst,


    bv3.d1_sold d1_bv3_sold,
    bv3.d2_sold d2_bv3_sold,
    bv3.d3_sold d3_bv3_sold,
    bv3.d4_sold d4_bv3_sold,
    bv3.d5_sold d5_bv3_sold,
    bv3.d6_sold d6_bv3_sold,
    bv3.d7_sold d7_bv3_sold,
    bv3.d8_sold d8_bv3_sold,
    bv3.d1_occupancy_NBR d1_bv3_occfcst,
    bv3.d2_occupancy_NBR d2_bv3_occfcst,
    bv3.d3_occupancy_NBR d3_bv3_occfcst,
    bv3.d4_occupancy_NBR d4_bv3_occfcst,
    bv3.d5_occupancy_NBR d5_bv3_occfcst,
    bv3.d6_occupancy_NBR d6_bv3_occfcst,
    bv3.d7_occupancy_NBR d7_bv3_occfcst,
    bv3.d8_occupancy_NBR d8_bv3_occfcst,

    bv4.d1_sold d1_bv4_sold,
    bv4.d2_sold d2_bv4_sold,
    bv4.d3_sold d3_bv4_sold,
    bv4.d4_sold d4_bv4_sold,
    bv4.d5_sold d5_bv4_sold,
    bv4.d6_sold d6_bv4_sold,
    bv4.d7_sold d7_bv4_sold,
    bv4.d8_sold d8_bv4_sold,
    bv4.d1_occupancy_NBR d1_bv4_occfcst,
    bv4.d2_occupancy_NBR d2_bv4_occfcst,
    bv4.d3_occupancy_NBR d3_bv4_occfcst,
    bv4.d4_occupancy_NBR d4_bv4_occfcst,
    bv4.d5_occupancy_NBR d5_bv4_occfcst,
    bv4.d6_occupancy_NBR d6_bv4_occfcst,
    bv4.d7_occupancy_NBR d7_bv4_occfcst,
    bv4.d8_occupancy_NBR d8_bv4_occfcst,

    bv5.d1_sold d1_bv5_sold,
    bv5.d2_sold d2_bv5_sold,
    bv5.d3_sold d3_bv5_sold,
    bv5.d4_sold d4_bv5_sold,
    bv5.d5_sold d5_bv5_sold,
    bv5.d6_sold d6_bv5_sold,
    bv5.d7_sold d7_bv5_sold,
    bv5.d8_sold d8_bv5_sold,
    bv5.d1_occupancy_NBR d1_bv5_occfcst,
    bv5.d2_occupancy_NBR d2_bv5_occfcst,
    bv5.d3_occupancy_NBR d3_bv5_occfcst,
    bv5.d4_occupancy_NBR d4_bv5_occfcst,
    bv5.d5_occupancy_NBR d5_bv5_occfcst,
    bv5.d6_occupancy_NBR d6_bv5_occfcst,
    bv5.d7_occupancy_NBR d7_bv5_occfcst,
    bv5.d8_occupancy_NBR d8_bv5_occfcst,

    bv6.d1_sold d1_bv6_sold,
    bv6.d2_sold d2_bv6_sold,
    bv6.d3_sold d3_bv6_sold,
    bv6.d4_sold d4_bv6_sold,
    bv6.d5_sold d5_bv6_sold,
    bv6.d6_sold d6_bv6_sold,
    bv6.d7_sold d7_bv6_sold,
    bv6.d8_sold d8_bv6_sold,
    bv6.d1_occupancy_NBR d1_bv6_occfcst,
    bv6.d2_occupancy_NBR d2_bv6_occfcst,
    bv6.d3_occupancy_NBR d3_bv6_occfcst,
    bv6.d4_occupancy_NBR d4_bv6_occfcst,
    bv6.d5_occupancy_NBR d5_bv6_occfcst,
    bv6.d6_occupancy_NBR d6_bv6_occfcst,
    bv6.d7_occupancy_NBR d7_bv6_occfcst,
    bv6.d8_occupancy_NBR d8_bv6_occfcst,

    bv7.d1_sold d1_bv7_sold,
    bv7.d2_sold d2_bv7_sold,
    bv7.d3_sold d3_bv7_sold,
    bv7.d4_sold d4_bv7_sold,
    bv7.d5_sold d5_bv7_sold,
    bv7.d6_sold d6_bv7_sold,
    bv7.d7_sold d7_bv7_sold,
    bv7.d8_sold d8_bv7_sold,
    bv7.d1_occupancy_NBR d1_bv7_occfcst,
    bv7.d2_occupancy_NBR d2_bv7_occfcst,
    bv7.d3_occupancy_NBR d3_bv7_occfcst,
    bv7.d4_occupancy_NBR d4_bv7_occfcst,
    bv7.d5_occupancy_NBR d5_bv7_occfcst,
    bv7.d6_occupancy_NBR d6_bv7_occfcst,
    bv7.d7_occupancy_NBR d7_bv7_occfcst,
    bv7.d8_occupancy_NBR d8_bv7_occfcst,

    bv8.d1_sold d1_bv8_sold,
    bv8.d2_sold d2_bv8_sold,
    bv8.d3_sold d3_bv8_sold,
    bv8.d4_sold d4_bv8_sold,
    bv8.d5_sold d5_bv8_sold,
    bv8.d6_sold d6_bv8_sold,
    bv8.d7_sold d7_bv8_sold,
    bv8.d8_sold d8_bv8_sold,
    bv8.d1_occupancy_NBR d1_bv8_occfcst,
    bv8.d2_occupancy_NBR d2_bv8_occfcst,
    bv8.d3_occupancy_NBR d3_bv8_occfcst,
    bv8.d4_occupancy_NBR d4_bv8_occfcst,
    bv8.d5_occupancy_NBR d5_bv8_occfcst,
    bv8.d6_occupancy_NBR d6_bv8_occfcst,
    bv8.d7_occupancy_NBR d7_bv8_occfcst,
    bv8.d8_occupancy_NBR d8_bv8_occfcst,

    bv9.d1_sold d1_bv9_sold,
    bv9.d2_sold d2_bv9_sold,
    bv9.d3_sold d3_bv9_sold,
    bv9.d4_sold d4_bv9_sold,
    bv9.d5_sold d5_bv9_sold,
    bv9.d6_sold d6_bv9_sold,
    bv9.d7_sold d7_bv9_sold,
    bv9.d8_sold d8_bv9_sold,
    bv9.d1_occupancy_NBR d1_bv9_occfcst,
    bv9.d2_occupancy_NBR d2_bv9_occfcst,
    bv9.d3_occupancy_NBR d3_bv9_occfcst,
    bv9.d4_occupancy_NBR d4_bv9_occfcst,
    bv9.d5_occupancy_NBR d5_bv9_occfcst,
    bv9.d6_occupancy_NBR d6_bv9_occfcst,
    bv9.d7_occupancy_NBR d7_bv9_occfcst,
    bv9.d8_occupancy_NBR d8_bv9_occfcst,

    bv10.d1_sold d1_bv10_sold,
    bv10.d2_sold d2_bv10_sold,
    bv10.d3_sold d3_bv10_sold,
    bv10.d4_sold d4_bv10_sold,
    bv10.d5_sold d5_bv10_sold,
    bv10.d6_sold d6_bv10_sold,
    bv10.d7_sold d7_bv10_sold,
    bv10.d8_sold d8_bv10_sold,
    bv10.d1_occupancy_NBR d1_bv10_occfcst,
    bv10.d2_occupancy_NBR d2_bv10_occfcst,
    bv10.d3_occupancy_NBR d3_bv10_occfcst,
    bv10.d4_occupancy_NBR d4_bv10_occfcst,
    bv10.d5_occupancy_NBR d5_bv10_occfcst,
    bv10.d6_occupancy_NBR d6_bv10_occfcst,
    bv10.d7_occupancy_NBR d7_bv10_occfcst,
    bv10.d8_occupancy_NBR d8_bv10_occfcst,

    bv11.d1_sold d1_bv11_sold,
    bv11.d2_sold d2_bv11_sold,
    bv11.d3_sold d3_bv11_sold,
    bv11.d4_sold d4_bv11_sold,
    bv11.d5_sold d5_bv11_sold,
    bv11.d6_sold d6_bv11_sold,
    bv11.d7_sold d7_bv11_sold,
    bv11.d8_sold d8_bv11_sold,
    bv11.d1_occupancy_NBR d1_bv11_occfcst,
    bv11.d2_occupancy_NBR d2_bv11_occfcst,
    bv11.d3_occupancy_NBR d3_bv11_occfcst,
    bv11.d4_occupancy_NBR d4_bv11_occfcst,
    bv11.d5_occupancy_NBR d5_bv11_occfcst,
    bv11.d6_occupancy_NBR d6_bv11_occfcst,
    bv11.d7_occupancy_NBR d7_bv11_occfcst,
    bv11.d8_occupancy_NBR d8_bv11_occfcst,

    bv12.d1_sold d1_bv12_sold,
    bv12.d2_sold d2_bv12_sold,
    bv12.d3_sold d3_bv12_sold,
    bv12.d4_sold d4_bv12_sold,
    bv12.d5_sold d5_bv12_sold,
    bv12.d6_sold d6_bv12_sold,
    bv12.d7_sold d7_bv12_sold,
    bv12.d8_sold d8_bv12_sold,
    bv12.d1_occupancy_NBR d1_bv12_occfcst,
    bv12.d2_occupancy_NBR d2_bv12_occfcst,
    bv12.d3_occupancy_NBR d3_bv12_occfcst,
    bv12.d4_occupancy_NBR d4_bv12_occfcst,
    bv12.d5_occupancy_NBR d5_bv12_occfcst,
    bv12.d6_occupancy_NBR d6_bv12_occfcst,
    bv12.d7_occupancy_NBR d7_bv12_occfcst,
    bv12.d8_occupancy_NBR d8_bv12_occfcst,

    bv13.d1_sold d1_bv13_sold,
    bv13.d2_sold d2_bv13_sold,
    bv13.d3_sold d3_bv13_sold,
    bv13.d4_sold d4_bv13_sold,
    bv13.d5_sold d5_bv13_sold,
    bv13.d6_sold d6_bv13_sold,
    bv13.d7_sold d7_bv13_sold,
    bv13.d8_sold d8_bv13_sold,
    bv13.d1_occupancy_NBR d1_bv13_occfcst,
    bv13.d2_occupancy_NBR d2_bv13_occfcst,
    bv13.d3_occupancy_NBR d3_bv13_occfcst,
    bv13.d4_occupancy_NBR d4_bv13_occfcst,
    bv13.d5_occupancy_NBR d5_bv13_occfcst,
    bv13.d6_occupancy_NBR d6_bv13_occfcst,
    bv13.d7_occupancy_NBR d7_bv13_occfcst,
    bv13.d8_occupancy_NBR d8_bv13_occfcst,

    bv14.d1_sold d1_bv14_sold,
    bv14.d2_sold d2_bv14_sold,
    bv14.d3_sold d3_bv14_sold,
    bv14.d4_sold d4_bv14_sold,
    bv14.d5_sold d5_bv14_sold,
    bv14.d6_sold d6_bv14_sold,
    bv14.d7_sold d7_bv14_sold,
    bv14.d8_sold d8_bv14_sold,
    bv14.d1_occupancy_NBR d1_bv14_occfcst,
    bv14.d2_occupancy_NBR d2_bv14_occfcst,
    bv14.d3_occupancy_NBR d3_bv14_occfcst,
    bv14.d4_occupancy_NBR d4_bv14_occfcst,
    bv14.d5_occupancy_NBR d5_bv14_occfcst,
    bv14.d6_occupancy_NBR d6_bv14_occfcst,
    bv14.d7_occupancy_NBR d7_bv14_occfcst,
    bv14.d8_occupancy_NBR d8_bv14_occfcst,

    bv15.d1_sold d1_bv15_sold,
    bv15.d2_sold d2_bv15_sold,
    bv15.d3_sold d3_bv15_sold,
    bv15.d4_sold d4_bv15_sold,
    bv15.d5_sold d5_bv15_sold,
    bv15.d6_sold d6_bv15_sold,
    bv15.d7_sold d7_bv15_sold,
    bv15.d8_sold d8_bv15_sold,
    bv15.d1_occupancy_NBR d1_bv15_occfcst,
    bv15.d2_occupancy_NBR d2_bv15_occfcst,
    bv15.d3_occupancy_NBR d3_bv15_occfcst,
    bv15.d4_occupancy_NBR d4_bv15_occfcst,
    bv15.d5_occupancy_NBR d5_bv15_occfcst,
    bv15.d6_occupancy_NBR d6_bv15_occfcst,
    bv15.d7_occupancy_NBR d7_bv15_occfcst,
    bv15.d8_occupancy_NBR d8_bv15_occfcst,

    bv16.d1_sold d1_bv16_sold,
    bv16.d2_sold d2_bv16_sold,
    bv16.d3_sold d3_bv16_sold,
    bv16.d4_sold d4_bv16_sold,
    bv16.d5_sold d5_bv16_sold,
    bv16.d6_sold d6_bv16_sold,
    bv16.d7_sold d7_bv16_sold,
    bv16.d8_sold d8_bv16_sold,
    bv16.d1_occupancy_NBR d1_bv16_occfcst,
    bv16.d2_occupancy_NBR d2_bv16_occfcst,
    bv16.d3_occupancy_NBR d3_bv16_occfcst,
    bv16.d4_occupancy_NBR d4_bv16_occfcst,
    bv16.d5_occupancy_NBR d5_bv16_occfcst,
    bv16.d6_occupancy_NBR d6_bv16_occfcst,
    bv16.d7_occupancy_NBR d7_bv16_occfcst,
    bv16.d8_occupancy_NBR d8_bv16_occfcst,

    bv17.d1_sold d1_bv17_sold,
    bv17.d2_sold d2_bv17_sold,
    bv17.d3_sold d3_bv17_sold,
    bv17.d4_sold d4_bv17_sold,
    bv17.d5_sold d5_bv17_sold,
    bv17.d6_sold d6_bv17_sold,
    bv17.d7_sold d7_bv17_sold,
    bv17.d8_sold d8_bv17_sold,
    bv17.d1_occupancy_NBR d1_bv17_occfcst,
    bv17.d2_occupancy_NBR d2_bv17_occfcst,
    bv17.d3_occupancy_NBR d3_bv17_occfcst,
    bv17.d4_occupancy_NBR d4_bv17_occfcst,
    bv17.d5_occupancy_NBR d5_bv17_occfcst,
    bv17.d6_occupancy_NBR d6_bv17_occfcst,
    bv17.d7_occupancy_NBR d7_bv17_occfcst,
    bv17.d8_occupancy_NBR d8_bv17_occfcst,

    bv18.d1_sold d1_bv18_sold,
    bv18.d2_sold d2_bv18_sold,
    bv18.d3_sold d3_bv18_sold,
    bv18.d4_sold d4_bv18_sold,
    bv18.d5_sold d5_bv18_sold,
    bv18.d6_sold d6_bv18_sold,
    bv18.d7_sold d7_bv18_sold,
    bv18.d8_sold d8_bv18_sold,
    bv18.d1_occupancy_NBR d1_bv18_occfcst,
    bv18.d2_occupancy_NBR d2_bv18_occfcst,
    bv18.d3_occupancy_NBR d3_bv18_occfcst,
    bv18.d4_occupancy_NBR d4_bv18_occfcst,
    bv18.d5_occupancy_NBR d5_bv18_occfcst,
    bv18.d6_occupancy_NBR d6_bv18_occfcst,
    bv18.d7_occupancy_NBR d7_bv18_occfcst,
    bv18.d8_occupancy_NBR d8_bv18_occfcst,

    bv19.d1_sold d1_bv19_sold,
    bv19.d2_sold d2_bv19_sold,
    bv19.d3_sold d3_bv19_sold,
    bv19.d4_sold d4_bv19_sold,
    bv19.d5_sold d5_bv19_sold,
    bv19.d6_sold d6_bv19_sold,
    bv19.d7_sold d7_bv19_sold,
    bv19.d8_sold d8_bv19_sold,
    bv19.d1_occupancy_NBR d1_bv19_occfcst,
    bv19.d2_occupancy_NBR d2_bv19_occfcst,
    bv19.d3_occupancy_NBR d3_bv19_occfcst,
    bv19.d4_occupancy_NBR d4_bv19_occfcst,
    bv19.d5_occupancy_NBR d5_bv19_occfcst,
    bv19.d6_occupancy_NBR d6_bv19_occfcst,
    bv19.d7_occupancy_NBR d7_bv19_occfcst,
    bv19.d8_occupancy_NBR d8_bv19_occfcst,

    bv20.d1_sold d1_bv20_sold,
    bv20.d2_sold d2_bv20_sold,
    bv20.d3_sold d3_bv20_sold,
    bv20.d4_sold d4_bv20_sold,
    bv20.d5_sold d5_bv20_sold,
    bv20.d6_sold d6_bv20_sold,
    bv20.d7_sold d7_bv20_sold,
    bv20.d8_sold d8_bv20_sold,
    bv20.d1_occupancy_NBR d1_bv20_occfcst,
    bv20.d2_occupancy_NBR d2_bv20_occfcst,
    bv20.d3_occupancy_NBR d3_bv20_occfcst,
    bv20.d4_occupancy_NBR d4_bv20_occfcst,
    bv20.d5_occupancy_NBR d5_bv20_occfcst,
    bv20.d6_occupancy_NBR d6_bv20_occfcst,
    bv20.d7_occupancy_NBR d7_bv20_occfcst,
    bv20.d8_occupancy_NBR d8_bv20_occfcst,

    bv21.d1_sold d1_bv21_sold,
    bv21.d2_sold d2_bv21_sold,
    bv21.d3_sold d3_bv21_sold,
    bv21.d4_sold d4_bv21_sold,
    bv21.d5_sold d5_bv21_sold,
    bv21.d6_sold d6_bv21_sold,
    bv21.d7_sold d7_bv21_sold,
    bv21.d8_sold d8_bv21_sold,
    bv21.d1_occupancy_NBR d1_bv21_occfcst,
    bv21.d2_occupancy_NBR d2_bv21_occfcst,
    bv21.d3_occupancy_NBR d3_bv21_occfcst,
    bv21.d4_occupancy_NBR d4_bv21_occfcst,
    bv21.d5_occupancy_NBR d5_bv21_occfcst,
    bv21.d6_occupancy_NBR d6_bv21_occfcst,
    bv21.d7_occupancy_NBR d7_bv21_occfcst,
    bv21.d8_occupancy_NBR d8_bv21_occfcst,

    bv22.d1_sold d1_bv22_sold,
    bv22.d2_sold d2_bv22_sold,
    bv22.d3_sold d3_bv22_sold,
    bv22.d4_sold d4_bv22_sold,
    bv22.d5_sold d5_bv22_sold,
    bv22.d6_sold d6_bv22_sold,
    bv22.d7_sold d7_bv22_sold,
    bv22.d8_sold d8_bv22_sold,
    bv22.d1_occupancy_NBR d1_bv22_occfcst,
    bv22.d2_occupancy_NBR d2_bv22_occfcst,
    bv22.d3_occupancy_NBR d3_bv22_occfcst,
    bv22.d4_occupancy_NBR d4_bv22_occfcst,
    bv22.d5_occupancy_NBR d5_bv22_occfcst,
    bv22.d6_occupancy_NBR d6_bv22_occfcst,
    bv22.d7_occupancy_NBR d7_bv22_occfcst,
    bv22.d8_occupancy_NBR d8_bv22_occfcst,

    bv23.d1_sold d1_bv23_sold,
    bv23.d2_sold d2_bv23_sold,
    bv23.d3_sold d3_bv23_sold,
    bv23.d4_sold d4_bv23_sold,
    bv23.d5_sold d5_bv23_sold,
    bv23.d6_sold d6_bv23_sold,
    bv23.d7_sold d7_bv23_sold,
    bv23.d8_sold d8_bv23_sold,
    bv23.d1_occupancy_NBR d1_bv23_occfcst,
    bv23.d2_occupancy_NBR d2_bv23_occfcst,
    bv23.d3_occupancy_NBR d3_bv23_occfcst,
    bv23.d4_occupancy_NBR d4_bv23_occfcst,
    bv23.d5_occupancy_NBR d5_bv23_occfcst,
    bv23.d6_occupancy_NBR d6_bv23_occfcst,
    bv23.d7_occupancy_NBR d7_bv23_occfcst,

    bv24.d1_sold d1_bv24_sold,
    bv24.d2_sold d2_bv24_sold,
    bv24.d3_sold d3_bv24_sold,
    bv24.d4_sold d4_bv24_sold,
    bv24.d5_sold d5_bv24_sold,
    bv24.d6_sold d6_bv24_sold,
    bv24.d7_sold d7_bv24_sold,
    bv24.d8_sold d8_bv24_sold,
    bv24.d1_occupancy_NBR d1_bv24_occfcst,
    bv24.d2_occupancy_NBR d2_bv24_occfcst,
    bv24.d3_occupancy_NBR d3_bv24_occfcst,
    bv24.d4_occupancy_NBR d4_bv24_occfcst,
    bv24.d5_occupancy_NBR d5_bv24_occfcst,
    bv24.d6_occupancy_NBR d6_bv24_occfcst,
    bv24.d7_occupancy_NBR d7_bv24_occfcst,
    bv24.d8_occupancy_NBR d8_bv24_occfcst,

    bv25.d1_sold d1_bv25_sold,
    bv25.d2_sold d2_bv25_sold,
    bv25.d3_sold d3_bv25_sold,
    bv25.d4_sold d4_bv25_sold,
    bv25.d5_sold d5_bv25_sold,
    bv25.d6_sold d6_bv25_sold,
    bv25.d7_sold d7_bv25_sold,
    bv25.d8_sold d8_bv25_sold,
    bv25.d1_occupancy_NBR d1_bv25_occfcst,
    bv25.d2_occupancy_NBR d2_bv25_occfcst,
    bv25.d3_occupancy_NBR d3_bv25_occfcst,
    bv25.d4_occupancy_NBR d4_bv25_occfcst,
    bv25.d5_occupancy_NBR d5_bv25_occfcst,
    bv25.d6_occupancy_NBR d6_bv25_occfcst,
    bv25.d7_occupancy_NBR d7_bv25_occfcst,
    bv25.d8_occupancy_NBR d8_bv25_occfcst,
    bv23.d8_occupancy_NBR d8_bv23_occfcst,

    rc1.d1_sold d1_rc1_sold,
    rc1.d2_sold d2_rc1_sold,
    rc1.d3_sold d3_rc1_sold,
    rc1.d4_sold d4_rc1_sold,
    rc1.d5_sold d5_rc1_sold,
    rc1.d6_sold d6_rc1_sold,
    rc1.d7_sold d7_rc1_sold,
    rc1.d8_sold d8_rc1_sold,
    rc1.d1_occupancy_NBR d1_rc1_occfcst,
    rc1.d2_occupancy_NBR d2_rc1_occfcst,
    rc1.d3_occupancy_NBR d3_rc1_occfcst,
    rc1.d4_occupancy_NBR d4_rc1_occfcst,
    rc1.d5_occupancy_NBR d5_rc1_occfcst,
    rc1.d6_occupancy_NBR d6_rc1_occfcst,
    rc1.d7_occupancy_NBR d7_rc1_occfcst,
    rc1.d8_occupancy_NBR d8_rc1_occfcst,
    rc1.d1_lrv d1_rc1_lrv,
    rc1.d2_lrv d2_rc1_lrv,
    rc1.d3_lrv d3_rc1_lrv,
    rc1.d4_lrv d4_rc1_lrv,
    rc1.d5_lrv d5_rc1_lrv,
    rc1.d6_lrv d6_rc1_lrv,
    rc1.d7_lrv d7_rc1_lrv,
    rc1.d8_lrv d8_rc1_lrv,
    NULL d1_rc1_price,
    NULL d2_rc1_price,
    NULL d3_rc1_price,
    NULL d4_rc1_price,
    NULL d5_rc1_price,
    NULL d6_rc1_price,
    NULL d7_rc1_price,
    NULL d8_rc1_price,
    NULL d1_rc1_rate_code,
    NULL d2_rc1_rate_code,
    NULL d3_rc1_rate_code,
    NULL d4_rc1_rate_code,
    NULL d5_rc1_rate_code,
    NULL d6_rc1_rate_code,
    NULL d7_rc1_rate_code,
    NULL d8_rc1_rate_code,

    rc2.d1_sold d1_rc2_sold,
    rc2.d2_sold d2_rc2_sold,
    rc2.d3_sold d3_rc2_sold,
    rc2.d4_sold d4_rc2_sold,
    rc2.d5_sold d5_rc2_sold,
    rc2.d6_sold d6_rc2_sold,
    rc2.d7_sold d7_rc2_sold,
    rc2.d8_sold d8_rc2_sold,
    rc2.d1_occupancy_NBR d1_rc2_occfcst,
    rc2.d2_occupancy_NBR d2_rc2_occfcst,
    rc2.d3_occupancy_NBR d3_rc2_occfcst,
    rc2.d4_occupancy_NBR d4_rc2_occfcst,
    rc2.d5_occupancy_NBR d5_rc2_occfcst,
    rc2.d6_occupancy_NBR d6_rc2_occfcst,
    rc2.d7_occupancy_NBR d7_rc2_occfcst,
    rc2.d8_occupancy_NBR d8_rc2_occfcst,
    rc2.d1_lrv d1_rc2_lrv,
    rc2.d2_lrv d2_rc2_lrv,
    rc2.d3_lrv d3_rc2_lrv,
    rc2.d4_lrv d4_rc2_lrv,
    rc2.d5_lrv d5_rc2_lrv,
    rc2.d6_lrv d6_rc2_lrv,
    rc2.d7_lrv d7_rc2_lrv,
    rc2.d8_lrv d8_rc2_lrv,
    NULL d1_rc2_price,
    NULL d2_rc2_price,
    NULL d3_rc2_price,
    NULL d4_rc2_price,
    NULL d5_rc2_price,
    NULL d6_rc2_price,
    NULL d7_rc2_price,
    NULL d8_rc2_price,
    NULL d1_rc2_rate_code,
    NULL d2_rc2_rate_code,
    NULL d3_rc2_rate_code,
    NULL d4_rc2_rate_code,
    NULL d5_rc2_rate_code,
    NULL d6_rc2_rate_code,
    NULL d7_rc2_rate_code,
    NULL d8_rc2_rate_code,

    rc3.d1_sold d1_rc3_sold,
    rc3.d2_sold d2_rc3_sold,
    rc3.d3_sold d3_rc3_sold,
    rc3.d4_sold d4_rc3_sold,
    rc3.d5_sold d5_rc3_sold,
    rc3.d6_sold d6_rc3_sold,
    rc3.d7_sold d7_rc3_sold,
    rc3.d8_sold d8_rc3_sold,
    rc3.d1_occupancy_NBR d1_rc3_occfcst,
    rc3.d2_occupancy_NBR d2_rc3_occfcst,
    rc3.d3_occupancy_NBR d3_rc3_occfcst,
    rc3.d4_occupancy_NBR d4_rc3_occfcst,
    rc3.d5_occupancy_NBR d5_rc3_occfcst,
    rc3.d6_occupancy_NBR d6_rc3_occfcst,
    rc3.d7_occupancy_NBR d7_rc3_occfcst,
    rc3.d8_occupancy_NBR d8_rc3_occfcst,
    rc3.d1_lrv d1_rc3_lrv,
    rc3.d2_lrv d2_rc3_lrv,
    rc3.d3_lrv d3_rc3_lrv,
    rc3.d4_lrv d4_rc3_lrv,
    rc3.d5_lrv d5_rc3_lrv,
    rc3.d6_lrv d6_rc3_lrv,
    rc3.d7_lrv d7_rc3_lrv,
    rc3.d8_lrv d8_rc3_lrv,
    NULL d1_rc3_price,
    NULL d2_rc3_price,
    NULL d3_rc3_price,
    NULL d4_rc3_price,
    NULL d5_rc3_price,
    NULL d6_rc3_price,
    NULL d7_rc3_price,
    NULL d8_rc3_price,
    NULL d1_rc3_rate_code,
    NULL d2_rc3_rate_code,
    NULL d3_rc3_rate_code,
    NULL d4_rc3_rate_code,
    NULL d5_rc3_rate_code,
    NULL d6_rc3_rate_code,
    NULL d7_rc3_rate_code,
    NULL d8_rc3_rate_code,

    rc4.d1_sold d1_rc4_sold,
    rc4.d2_sold d2_rc4_sold,
    rc4.d3_sold d3_rc4_sold,
    rc4.d4_sold d4_rc4_sold,
    rc4.d5_sold d5_rc4_sold,
    rc4.d6_sold d6_rc4_sold,
    rc4.d7_sold d7_rc4_sold,
    rc4.d8_sold d8_rc4_sold,
    rc4.d1_occupancy_NBR d1_rc4_occfcst,
    rc4.d2_occupancy_NBR d2_rc4_occfcst,
    rc4.d3_occupancy_NBR d3_rc4_occfcst,
    rc4.d4_occupancy_NBR d4_rc4_occfcst,
    rc4.d5_occupancy_NBR d5_rc4_occfcst,
    rc4.d6_occupancy_NBR d6_rc4_occfcst,
    rc4.d7_occupancy_NBR d7_rc4_occfcst,
    rc4.d8_occupancy_NBR d8_rc4_occfcst,
    rc4.d1_lrv d1_rc4_lrv,
    rc4.d2_lrv d2_rc4_lrv,
    rc4.d3_lrv d3_rc4_lrv,
    rc4.d4_lrv d4_rc4_lrv,
    rc4.d5_lrv d5_rc4_lrv,
    rc4.d6_lrv d6_rc4_lrv,
    rc4.d7_lrv d7_rc4_lrv,
    rc4.d8_lrv d8_rc4_lrv,
    NULL d1_rc4_price,
    NULL d2_rc4_price,
    NULL d3_rc4_price,
    NULL d4_rc4_price,
    NULL d5_rc4_price,
    NULL d6_rc4_price,
    NULL d7_rc4_price,
    NULL d8_rc4_price,
    NULL d1_rc4_rate_code,
    NULL d2_rc4_rate_code,
    NULL d3_rc4_rate_code,
    NULL d4_rc4_rate_code,
    NULL d5_rc4_rate_code,
    NULL d6_rc4_rate_code,
    NULL d7_rc4_rate_code,
    NULL d8_rc4_rate_code,

    rc5.d1_sold d1_rc5_sold,
    rc5.d2_sold d2_rc5_sold,
    rc5.d3_sold d3_rc5_sold,
    rc5.d4_sold d4_rc5_sold,
    rc5.d5_sold d5_rc5_sold,
    rc5.d6_sold d6_rc5_sold,
    rc5.d7_sold d7_rc5_sold,
    rc5.d8_sold d8_rc5_sold,
    rc5.d1_occupancy_NBR d1_rc5_occfcst,
    rc5.d2_occupancy_NBR d2_rc5_occfcst,
    rc5.d3_occupancy_NBR d3_rc5_occfcst,
    rc5.d4_occupancy_NBR d4_rc5_occfcst,
    rc5.d5_occupancy_NBR d5_rc5_occfcst,
    rc5.d6_occupancy_NBR d6_rc5_occfcst,
    rc5.d7_occupancy_NBR d7_rc5_occfcst,
    rc5.d8_occupancy_NBR d8_rc5_occfcst,
    rc5.d1_lrv d1_rc5_lrv,
    rc5.d2_lrv d2_rc5_lrv,
    rc5.d3_lrv d3_rc5_lrv,
    rc5.d4_lrv d4_rc5_lrv,
    rc5.d5_lrv d5_rc5_lrv,
    rc5.d6_lrv d6_rc5_lrv,
    rc5.d7_lrv d7_rc5_lrv,
    rc5.d8_lrv d8_rc5_lrv,
    NULL d1_rc5_price,
    NULL d2_rc5_price,
    NULL d3_rc5_price,
    NULL d4_rc5_price,
    NULL d5_rc5_price,
    NULL d6_rc5_price,
    NULL d7_rc5_price,
    NULL d8_rc5_price,
    NULL d1_rc5_rate_code,
    NULL d2_rc5_rate_code,
    NULL d3_rc5_rate_code,
    NULL d4_rc5_rate_code,
    NULL d5_rc5_rate_code,
    NULL d6_rc5_rate_code,
    NULL d7_rc5_rate_code,
    NULL d8_rc5_rate_code,

    rc6.d1_sold d1_rc6_sold,
    rc6.d2_sold d2_rc6_sold,
    rc6.d3_sold d3_rc6_sold,
    rc6.d4_sold d4_rc6_sold,
    rc6.d5_sold d5_rc6_sold,
    rc6.d6_sold d6_rc6_sold,
    rc6.d7_sold d7_rc6_sold,
    rc6.d8_sold d8_rc6_sold,
    rc6.d1_occupancy_NBR d1_rc6_occfcst,
    rc6.d2_occupancy_NBR d2_rc6_occfcst,
    rc6.d3_occupancy_NBR d3_rc6_occfcst,
    rc6.d4_occupancy_NBR d4_rc6_occfcst,
    rc6.d5_occupancy_NBR d5_rc6_occfcst,
    rc6.d6_occupancy_NBR d6_rc6_occfcst,
    rc6.d7_occupancy_NBR d7_rc6_occfcst,
    rc6.d8_occupancy_NBR d8_rc6_occfcst,
    rc6.d1_lrv d1_rc6_lrv,
    rc6.d2_lrv d2_rc6_lrv,
    rc6.d3_lrv d3_rc6_lrv,
    rc6.d4_lrv d4_rc6_lrv,
    rc6.d5_lrv d5_rc6_lrv,
    rc6.d6_lrv d6_rc6_lrv,
    rc6.d7_lrv d7_rc6_lrv,
    rc6.d8_lrv d8_rc6_lrv,
    NULL d1_rc6_price,
    NULL d2_rc6_price,
    NULL d3_rc6_price,
    NULL d4_rc6_price,
    NULL d5_rc6_price,
    NULL d6_rc6_price,
    NULL d7_rc6_price,
    NULL d8_rc6_price,
    NULL d1_rc6_rate_code,
    NULL d2_rc6_rate_code,
    NULL d3_rc6_rate_code,
    NULL d4_rc6_rate_code,
    NULL d5_rc6_rate_code,
    NULL d6_rc6_rate_code,
    NULL d7_rc6_rate_code,
    NULL d8_rc6_rate_code,

    rc7.d1_sold d1_rc7_sold,
    rc7.d2_sold d2_rc7_sold,
    rc7.d3_sold d3_rc7_sold,
    rc7.d4_sold d4_rc7_sold,
    rc7.d5_sold d5_rc7_sold,
    rc7.d6_sold d6_rc7_sold,
    rc7.d7_sold d7_rc7_sold,
    rc7.d8_sold d8_rc7_sold,
    rc7.d1_occupancy_NBR d1_rc7_occfcst,
    rc7.d2_occupancy_NBR d2_rc7_occfcst,
    rc7.d3_occupancy_NBR d3_rc7_occfcst,
    rc7.d4_occupancy_NBR d4_rc7_occfcst,
    rc7.d5_occupancy_NBR d5_rc7_occfcst,
    rc7.d6_occupancy_NBR d6_rc7_occfcst,
    rc7.d7_occupancy_NBR d7_rc7_occfcst,
    rc7.d8_occupancy_NBR d8_rc7_occfcst,
    rc7.d1_lrv d1_rc7_lrv,
    rc7.d2_lrv d2_rc7_lrv,
    rc7.d3_lrv d3_rc7_lrv,
    rc7.d4_lrv d4_rc7_lrv,
    rc7.d5_lrv d5_rc7_lrv,
    rc7.d6_lrv d6_rc7_lrv,
    rc7.d7_lrv d7_rc7_lrv,
    rc7.d8_lrv d8_rc7_lrv,
    NULL d1_rc7_price,
    NULL d2_rc7_price,
    NULL d3_rc7_price,
    NULL d4_rc7_price,
    NULL d5_rc7_price,
    NULL d6_rc7_price,
    NULL d7_rc7_price,
    NULL d8_rc7_price,
    NULL d1_rc7_rate_code,
    NULL d2_rc7_rate_code,
    NULL d3_rc7_rate_code,
    NULL d4_rc7_rate_code,
    NULL d5_rc7_rate_code,
    NULL d6_rc7_rate_code,
    NULL d7_rc7_rate_code,
    NULL d8_rc7_rate_code,

    rc8.d1_sold d1_rc8_sold,
    rc8.d2_sold d2_rc8_sold,
    rc8.d3_sold d3_rc8_sold,
    rc8.d4_sold d4_rc8_sold,
    rc8.d5_sold d5_rc8_sold,
    rc8.d6_sold d6_rc8_sold,
    rc8.d7_sold d7_rc8_sold,
    rc8.d8_sold d8_rc8_sold,
    rc8.d1_occupancy_NBR d1_rc8_occfcst,
    rc8.d2_occupancy_NBR d2_rc8_occfcst,
    rc8.d3_occupancy_NBR d3_rc8_occfcst,
    rc8.d4_occupancy_NBR d4_rc8_occfcst,
    rc8.d5_occupancy_NBR d5_rc8_occfcst,
    rc8.d6_occupancy_NBR d6_rc8_occfcst,
    rc8.d7_occupancy_NBR d7_rc8_occfcst,
    rc8.d8_occupancy_NBR d8_rc8_occfcst,
    rc8.d1_lrv d1_rc8_lrv,
    rc8.d2_lrv d2_rc8_lrv,
    rc8.d3_lrv d3_rc8_lrv,
    rc8.d4_lrv d4_rc8_lrv,
    rc8.d5_lrv d5_rc8_lrv,
    rc8.d6_lrv d6_rc8_lrv,
    rc8.d7_lrv d7_rc8_lrv,
    rc8.d8_lrv d8_rc8_lrv,
    NULL d1_rc8_price,
    NULL d2_rc8_price,
    NULL d3_rc8_price,
    NULL d4_rc8_price,
    NULL d5_rc8_price,
    NULL d6_rc8_price,
    NULL d7_rc8_price,
    NULL d8_rc8_price,
    NULL d1_rc8_rate_code,
    NULL d2_rc8_rate_code,
    NULL d3_rc8_rate_code,
    NULL d4_rc8_rate_code,
    NULL d5_rc8_rate_code,
    NULL d6_rc8_rate_code,
    NULL d7_rc8_rate_code,
    NULL d8_rc8_rate_code,

    rc9.d1_sold d1_rc9_sold,
    rc9.d2_sold d2_rc9_sold,
    rc9.d3_sold d3_rc9_sold,
    rc9.d4_sold d4_rc9_sold,
    rc9.d5_sold d5_rc9_sold,
    rc9.d6_sold d6_rc9_sold,
    rc9.d7_sold d7_rc9_sold,
    rc9.d8_sold d8_rc9_sold,
    rc9.d1_occupancy_NBR d1_rc9_occfcst,
    rc9.d2_occupancy_NBR d2_rc9_occfcst,
    rc9.d3_occupancy_NBR d3_rc9_occfcst,
    rc9.d4_occupancy_NBR d4_rc9_occfcst,
    rc9.d5_occupancy_NBR d5_rc9_occfcst,
    rc9.d6_occupancy_NBR d6_rc9_occfcst,
    rc9.d7_occupancy_NBR d7_rc9_occfcst,
    rc9.d8_occupancy_NBR d8_rc9_occfcst,
    rc9.d1_lrv d1_rc9_lrv,
    rc9.d2_lrv d2_rc9_lrv,
    rc9.d3_lrv d3_rc9_lrv,
    rc9.d4_lrv d4_rc9_lrv,
    rc9.d5_lrv d5_rc9_lrv,
    rc9.d6_lrv d6_rc9_lrv,
    rc9.d7_lrv d7_rc9_lrv,
    rc9.d8_lrv d8_rc9_lrv,
    NULL d1_rc9_price,
    NULL d2_rc9_price,
    NULL d3_rc9_price,
    NULL d4_rc9_price,
    NULL d5_rc9_price,
    NULL d6_rc9_price,
    NULL d7_rc9_price,
    NULL d8_rc9_price,
    NULL d1_rc9_rate_code,
    NULL d2_rc9_rate_code,
    NULL d3_rc9_rate_code,
    NULL d4_rc9_rate_code,
    NULL d5_rc9_rate_code,
    NULL d6_rc9_rate_code,
    NULL d7_rc9_rate_code,
    NULL d8_rc9_rate_code,

    rc10.d1_sold d1_rc10_sold,
    rc10.d2_sold d2_rc10_sold,
    rc10.d3_sold d3_rc10_sold,
    rc10.d4_sold d4_rc10_sold,
    rc10.d5_sold d5_rc10_sold,
    rc10.d6_sold d6_rc10_sold,
    rc10.d7_sold d7_rc10_sold,
    rc10.d8_sold d8_rc10_sold,
    rc10.d1_occupancy_NBR d1_rc10_occfcst,
    rc10.d2_occupancy_NBR d2_rc10_occfcst,
    rc10.d3_occupancy_NBR d3_rc10_occfcst,
    rc10.d4_occupancy_NBR d4_rc10_occfcst,
    rc10.d5_occupancy_NBR d5_rc10_occfcst,
    rc10.d6_occupancy_NBR d6_rc10_occfcst,
    rc10.d7_occupancy_NBR d7_rc10_occfcst,
    rc10.d8_occupancy_NBR d8_rc10_occfcst,
    rc10.d1_lrv d1_rc10_lrv,
    rc10.d2_lrv d2_rc10_lrv,
    rc10.d3_lrv d3_rc10_lrv,
    rc10.d4_lrv d4_rc10_lrv,
    rc10.d5_lrv d5_rc10_lrv,
    rc10.d6_lrv d6_rc10_lrv,
    rc10.d7_lrv d7_rc10_lrv,
    rc10.d8_lrv d8_rc10_lrv,
    NULL d1_rc10_price,
    NULL d2_rc10_price,
    NULL d3_rc10_price,
    NULL d4_rc10_price,
    NULL d5_rc10_price,
    NULL d6_rc10_price,
    NULL d7_rc10_price,
    NULL d8_rc10_price,
    NULL d1_rc10_rate_code,
    NULL d2_rc10_rate_code,
    NULL d3_rc10_rate_code,
    NULL d4_rc10_rate_code,
    NULL d5_rc10_rate_code,
    NULL d6_rc10_rate_code,
    NULL d7_rc10_rate_code,
    NULL d8_rc10_rate_code,

    rc11.d1_sold d1_rc11_sold,
    rc11.d2_sold d2_rc11_sold,
    rc11.d3_sold d3_rc11_sold,
    rc11.d4_sold d4_rc11_sold,
    rc11.d5_sold d5_rc11_sold,
    rc11.d6_sold d6_rc11_sold,
    rc11.d7_sold d7_rc11_sold,
    rc11.d8_sold d8_rc11_sold,
    rc11.d1_occupancy_NBR d1_rc11_occfcst,
    rc11.d2_occupancy_NBR d2_rc11_occfcst,
    rc11.d3_occupancy_NBR d3_rc11_occfcst,
    rc11.d4_occupancy_NBR d4_rc11_occfcst,
    rc11.d5_occupancy_NBR d5_rc11_occfcst,
    rc11.d6_occupancy_NBR d6_rc11_occfcst,
    rc11.d7_occupancy_NBR d7_rc11_occfcst,
    rc11.d8_occupancy_NBR d8_rc11_occfcst,
    rc11.d1_lrv d1_rc11_lrv,
    rc11.d2_lrv d2_rc11_lrv,
    rc11.d3_lrv d3_rc11_lrv,
    rc11.d4_lrv d4_rc11_lrv,
    rc11.d5_lrv d5_rc11_lrv,
    rc11.d6_lrv d6_rc11_lrv,
    rc11.d7_lrv d7_rc11_lrv,
    rc11.d8_lrv d8_rc11_lrv,
    NULL d1_rc11_price,
    NULL d2_rc11_price,
    NULL d3_rc11_price,
    NULL d4_rc11_price,
    NULL d5_rc11_price,
    NULL d6_rc11_price,
    NULL d7_rc11_price,
    NULL d8_rc11_price,
    NULL d1_rc11_rate_code,
    NULL d2_rc11_rate_code,
    NULL d3_rc11_rate_code,
    NULL d4_rc11_rate_code,
    NULL d5_rc11_rate_code,
    NULL d6_rc11_rate_code,
    NULL d7_rc11_rate_code,
    NULL d8_rc11_rate_code,

    rc12.d1_sold d1_rc12_sold,
    rc12.d2_sold d2_rc12_sold,
    rc12.d3_sold d3_rc12_sold,
    rc12.d4_sold d4_rc12_sold,
    rc12.d5_sold d5_rc12_sold,
    rc12.d6_sold d6_rc12_sold,
    rc12.d7_sold d7_rc12_sold,
    rc12.d8_sold d8_rc12_sold,
    rc12.d1_occupancy_NBR d1_rc12_occfcst,
    rc12.d2_occupancy_NBR d2_rc12_occfcst,
    rc12.d3_occupancy_NBR d3_rc12_occfcst,
    rc12.d4_occupancy_NBR d4_rc12_occfcst,
    rc12.d5_occupancy_NBR d5_rc12_occfcst,
    rc12.d6_occupancy_NBR d6_rc12_occfcst,
    rc12.d7_occupancy_NBR d7_rc12_occfcst,
    rc12.d8_occupancy_NBR d8_rc12_occfcst,
    rc12.d1_lrv d1_rc12_lrv,
    rc12.d2_lrv d2_rc12_lrv,
    rc12.d3_lrv d3_rc12_lrv,
    rc12.d4_lrv d4_rc12_lrv,
    rc12.d5_lrv d5_rc12_lrv,
    rc12.d6_lrv d6_rc12_lrv,
    rc12.d7_lrv d7_rc12_lrv,
    rc12.d8_lrv d8_rc12_lrv,
    NULL d1_rc12_price,
    NULL d2_rc12_price,
    NULL d3_rc12_price,
    NULL d4_rc12_price,
    NULL d5_rc12_price,
    NULL d6_rc12_price,
    NULL d7_rc12_price,
    NULL d8_rc12_price,
    NULL d1_rc12_rate_code,
    NULL d2_rc12_rate_code,
    NULL d3_rc12_rate_code,
    NULL d4_rc12_rate_code,
    NULL d5_rc12_rate_code,
    NULL d6_rc12_rate_code,
    NULL d7_rc12_rate_code,
    NULL d8_rc12_rate_code,

    rc13.d1_sold d1_rc13_sold,
    rc13.d2_sold d2_rc13_sold,
    rc13.d3_sold d3_rc13_sold,
    rc13.d4_sold d4_rc13_sold,
    rc13.d5_sold d5_rc13_sold,
    rc13.d6_sold d6_rc13_sold,
    rc13.d7_sold d7_rc13_sold,
    rc13.d8_sold d8_rc13_sold,
    rc13.d1_occupancy_NBR d1_rc13_occfcst,
    rc13.d2_occupancy_NBR d2_rc13_occfcst,
    rc13.d3_occupancy_NBR d3_rc13_occfcst,
    rc13.d4_occupancy_NBR d4_rc13_occfcst,
    rc13.d5_occupancy_NBR d5_rc13_occfcst,
    rc13.d6_occupancy_NBR d6_rc13_occfcst,
    rc13.d7_occupancy_NBR d7_rc13_occfcst,
    rc13.d8_occupancy_NBR d8_rc13_occfcst,
    rc13.d1_lrv d1_rc13_lrv,
    rc13.d2_lrv d2_rc13_lrv,
    rc13.d3_lrv d3_rc13_lrv,
    rc13.d4_lrv d4_rc13_lrv,
    rc13.d5_lrv d5_rc13_lrv,
    rc13.d6_lrv d6_rc13_lrv,
    rc13.d7_lrv d7_rc13_lrv,
    rc13.d8_lrv d8_rc13_lrv,
    NULL d1_rc13_price,
    NULL d2_rc13_price,
    NULL d3_rc13_price,
    NULL d4_rc13_price,
    NULL d5_rc13_price,
    NULL d6_rc13_price,
    NULL d7_rc13_price,
    NULL d8_rc13_price,
    NULL d1_rc13_rate_code,
    NULL d2_rc13_rate_code,
    NULL d3_rc13_rate_code,
    NULL d4_rc13_rate_code,
    NULL d5_rc13_rate_code,
    NULL d6_rc13_rate_code,
    NULL d7_rc13_rate_code,
    NULL d8_rc13_rate_code,

    rc14.d1_sold d1_rc14_sold,
    rc14.d2_sold d2_rc14_sold,
    rc14.d3_sold d3_rc14_sold,
    rc14.d4_sold d4_rc14_sold,
    rc14.d5_sold d5_rc14_sold,
    rc14.d6_sold d6_rc14_sold,
    rc14.d7_sold d7_rc14_sold,
    rc14.d8_sold d8_rc14_sold,
    rc14.d1_occupancy_NBR d1_rc14_occfcst,
    rc14.d2_occupancy_NBR d2_rc14_occfcst,
    rc14.d3_occupancy_NBR d3_rc14_occfcst,
    rc14.d4_occupancy_NBR d4_rc14_occfcst,
    rc14.d5_occupancy_NBR d5_rc14_occfcst,
    rc14.d6_occupancy_NBR d6_rc14_occfcst,
    rc14.d7_occupancy_NBR d7_rc14_occfcst,
    rc14.d8_occupancy_NBR d8_rc14_occfcst,
    rc14.d1_lrv d1_rc14_lrv,
    rc14.d2_lrv d2_rc14_lrv,
    rc14.d3_lrv d3_rc14_lrv,
    rc14.d4_lrv d4_rc14_lrv,
    rc14.d5_lrv d5_rc14_lrv,
    rc14.d6_lrv d6_rc14_lrv,
    rc14.d7_lrv d7_rc14_lrv,
    rc14.d8_lrv d8_rc14_lrv,
    NULL d1_rc14_price,
    NULL d2_rc14_price,
    NULL d3_rc14_price,
    NULL d4_rc14_price,
    NULL d5_rc14_price,
    NULL d6_rc14_price,
    NULL d7_rc14_price,
    NULL d8_rc14_price,
    NULL d1_rc14_rate_code,
    NULL d2_rc14_rate_code,
    NULL d3_rc14_rate_code,
    NULL d4_rc14_rate_code,
    NULL d5_rc14_rate_code,
    NULL d6_rc14_rate_code,
    NULL d7_rc14_rate_code,
    NULL d8_rc14_rate_code,

    rc15.d1_sold d1_rc15_sold,
    rc15.d2_sold d2_rc15_sold,
    rc15.d3_sold d3_rc15_sold,
    rc15.d4_sold d4_rc15_sold,
    rc15.d5_sold d5_rc15_sold,
    rc15.d6_sold d6_rc15_sold,
    rc15.d7_sold d7_rc15_sold,
    rc15.d8_sold d8_rc15_sold,
    rc15.d1_occupancy_NBR d1_rc15_occfcst,
    rc15.d2_occupancy_NBR d2_rc15_occfcst,
    rc15.d3_occupancy_NBR d3_rc15_occfcst,
    rc15.d4_occupancy_NBR d4_rc15_occfcst,
    rc15.d5_occupancy_NBR d5_rc15_occfcst,
    rc15.d6_occupancy_NBR d6_rc15_occfcst,
    rc15.d7_occupancy_NBR d7_rc15_occfcst,
    rc15.d8_occupancy_NBR d8_rc15_occfcst,
    rc15.d1_lrv d1_rc15_lrv,
    rc15.d2_lrv d2_rc15_lrv,
    rc15.d3_lrv d3_rc15_lrv,
    rc15.d4_lrv d4_rc15_lrv,
    rc15.d5_lrv d5_rc15_lrv,
    rc15.d6_lrv d6_rc15_lrv,
    rc15.d7_lrv d7_rc15_lrv,
    rc15.d8_lrv d8_rc15_lrv,
    NULL d1_rc15_price,
    NULL d2_rc15_price,
    NULL d3_rc15_price,
    NULL d4_rc15_price,
    NULL d5_rc15_price,
    NULL d6_rc15_price,
    NULL d7_rc15_price,
    NULL d8_rc15_price,
    NULL d1_rc15_rate_code,
    NULL d2_rc15_rate_code,
    NULL d3_rc15_rate_code,
    NULL d4_rc15_rate_code,
    NULL d5_rc15_rate_code,
    NULL d6_rc15_rate_code,
    NULL d7_rc15_rate_code,
    NULL d8_rc15_rate_code,

    rc16.d1_sold d1_rc16_sold,
    rc16.d2_sold d2_rc16_sold,
    rc16.d3_sold d3_rc16_sold,
    rc16.d4_sold d4_rc16_sold,
    rc16.d5_sold d5_rc16_sold,
    rc16.d6_sold d6_rc16_sold,
    rc16.d7_sold d7_rc16_sold,
    rc16.d8_sold d8_rc16_sold,
    rc16.d1_occupancy_NBR d1_rc16_occfcst,
    rc16.d2_occupancy_NBR d2_rc16_occfcst,
    rc16.d3_occupancy_NBR d3_rc16_occfcst,
    rc16.d4_occupancy_NBR d4_rc16_occfcst,
    rc16.d5_occupancy_NBR d5_rc16_occfcst,
    rc16.d6_occupancy_NBR d6_rc16_occfcst,
    rc16.d7_occupancy_NBR d7_rc16_occfcst,
    rc16.d8_occupancy_NBR d8_rc16_occfcst,
    rc16.d1_lrv d1_rc16_lrv,
    rc16.d2_lrv d2_rc16_lrv,
    rc16.d3_lrv d3_rc16_lrv,
    rc16.d4_lrv d4_rc16_lrv,
    rc16.d5_lrv d5_rc16_lrv,
    rc16.d6_lrv d6_rc16_lrv,
    rc16.d7_lrv d7_rc16_lrv,
    rc16.d8_lrv d8_rc16_lrv,
    NULL d1_rc16_price,
    NULL d2_rc16_price,
    NULL d3_rc16_price,
    NULL d4_rc16_price,
    NULL d5_rc16_price,
    NULL d6_rc16_price,
    NULL d7_rc16_price,
    NULL d8_rc16_price,
    NULL d1_rc16_rate_code,
    NULL d2_rc16_rate_code,
    NULL d3_rc16_rate_code,
    NULL d4_rc16_rate_code,
    NULL d5_rc16_rate_code,
    NULL d6_rc16_rate_code,
    NULL d7_rc16_rate_code,
    NULL d8_rc16_rate_code,

    rc17.d1_sold d1_rc17_sold,
    rc17.d2_sold d2_rc17_sold,
    rc17.d3_sold d3_rc17_sold,
    rc17.d4_sold d4_rc17_sold,
    rc17.d5_sold d5_rc17_sold,
    rc17.d6_sold d6_rc17_sold,
    rc17.d7_sold d7_rc17_sold,
    rc17.d8_sold d8_rc17_sold,
    rc17.d1_occupancy_NBR d1_rc17_occfcst,
    rc17.d2_occupancy_NBR d2_rc17_occfcst,
    rc17.d3_occupancy_NBR d3_rc17_occfcst,
    rc17.d4_occupancy_NBR d4_rc17_occfcst,
    rc17.d5_occupancy_NBR d5_rc17_occfcst,
    rc17.d6_occupancy_NBR d6_rc17_occfcst,
    rc17.d7_occupancy_NBR d7_rc17_occfcst,
    rc17.d8_occupancy_NBR d8_rc17_occfcst,
    rc17.d1_lrv d1_rc17_lrv,
    rc17.d2_lrv d2_rc17_lrv,
    rc17.d3_lrv d3_rc17_lrv,
    rc17.d4_lrv d4_rc17_lrv,
    rc17.d5_lrv d5_rc17_lrv,
    rc17.d6_lrv d6_rc17_lrv,
    rc17.d7_lrv d7_rc17_lrv,
    rc17.d8_lrv d8_rc17_lrv,
    NULL d1_rc17_price,
    NULL d2_rc17_price,
    NULL d3_rc17_price,
    NULL d4_rc17_price,
    NULL d5_rc17_price,
    NULL d6_rc17_price,
    NULL d7_rc17_price,
    NULL d8_rc17_price,
    NULL d1_rc17_rate_code,
    NULL d2_rc17_rate_code,
    NULL d3_rc17_rate_code,
    NULL d4_rc17_rate_code,
    NULL d5_rc17_rate_code,
    NULL d6_rc17_rate_code,
    NULL d7_rc17_rate_code,
    NULL d8_rc17_rate_code,

    rc18.d1_sold d1_rc18_sold,
    rc18.d2_sold d2_rc18_sold,
    rc18.d3_sold d3_rc18_sold,
    rc18.d4_sold d4_rc18_sold,
    rc18.d5_sold d5_rc18_sold,
    rc18.d6_sold d6_rc18_sold,
    rc18.d7_sold d7_rc18_sold,
    rc18.d8_sold d8_rc18_sold,
    rc18.d1_occupancy_NBR d1_rc18_occfcst,
    rc18.d2_occupancy_NBR d2_rc18_occfcst,
    rc18.d3_occupancy_NBR d3_rc18_occfcst,
    rc18.d4_occupancy_NBR d4_rc18_occfcst,
    rc18.d5_occupancy_NBR d5_rc18_occfcst,
    rc18.d6_occupancy_NBR d6_rc18_occfcst,
    rc18.d7_occupancy_NBR d7_rc18_occfcst,
    rc18.d8_occupancy_NBR d8_rc18_occfcst,
    rc18.d1_lrv d1_rc18_lrv,
    rc18.d2_lrv d2_rc18_lrv,
    rc18.d3_lrv d3_rc18_lrv,
    rc18.d4_lrv d4_rc18_lrv,
    rc18.d5_lrv d5_rc18_lrv,
    rc18.d6_lrv d6_rc18_lrv,
    rc18.d7_lrv d7_rc18_lrv,
    rc18.d8_lrv d8_rc18_lrv,
    NULL d1_rc18_price,
    NULL d2_rc18_price,
    NULL d3_rc18_price,
    NULL d4_rc18_price,
    NULL d5_rc18_price,
    NULL d6_rc18_price,
    NULL d7_rc18_price,
    NULL d8_rc18_price,
    NULL d1_rc18_rate_code,
    NULL d2_rc18_rate_code,
    NULL d3_rc18_rate_code,
    NULL d4_rc18_rate_code,
    NULL d5_rc18_rate_code,
    NULL d6_rc18_rate_code,
    NULL d7_rc18_rate_code,
    NULL d8_rc18_rate_code,

    rc19.d1_sold d1_rc19_sold,
    rc19.d2_sold d2_rc19_sold,
    rc19.d3_sold d3_rc19_sold,
    rc19.d4_sold d4_rc19_sold,
    rc19.d5_sold d5_rc19_sold,
    rc19.d6_sold d6_rc19_sold,
    rc19.d7_sold d7_rc19_sold,
    rc19.d8_sold d8_rc19_sold,
    rc19.d1_occupancy_NBR d1_rc19_occfcst,
    rc19.d2_occupancy_NBR d2_rc19_occfcst,
    rc19.d3_occupancy_NBR d3_rc19_occfcst,
    rc19.d4_occupancy_NBR d4_rc19_occfcst,
    rc19.d5_occupancy_NBR d5_rc19_occfcst,
    rc19.d6_occupancy_NBR d6_rc19_occfcst,
    rc19.d7_occupancy_NBR d7_rc19_occfcst,
    rc19.d8_occupancy_NBR d8_rc19_occfcst,
    rc19.d1_lrv d1_rc19_lrv,
    rc19.d2_lrv d2_rc19_lrv,
    rc19.d3_lrv d3_rc19_lrv,
    rc19.d4_lrv d4_rc19_lrv,
    rc19.d5_lrv d5_rc19_lrv,
    rc19.d6_lrv d6_rc19_lrv,
    rc19.d7_lrv d7_rc19_lrv,
    rc19.d8_lrv d8_rc19_lrv,
    NULL d1_rc19_price,
    NULL d2_rc19_price,
    NULL d3_rc19_price,
    NULL d4_rc19_price,
    NULL d5_rc19_price,
    NULL d6_rc19_price,
    NULL d7_rc19_price,
    NULL d8_rc19_price,
    NULL d1_rc19_rate_code,
    NULL d2_rc19_rate_code,
    NULL d3_rc19_rate_code,
    NULL d4_rc19_rate_code,
    NULL d5_rc19_rate_code,
    NULL d6_rc19_rate_code,
    NULL d7_rc19_rate_code,
    NULL d8_rc19_rate_code,

    rc20.d1_sold d1_rc20_sold,
    rc20.d2_sold d2_rc20_sold,
    rc20.d3_sold d3_rc20_sold,
    rc20.d4_sold d4_rc20_sold,
    rc20.d5_sold d5_rc20_sold,
    rc20.d6_sold d6_rc20_sold,
    rc20.d7_sold d7_rc20_sold,
    rc20.d8_sold d8_rc20_sold,
    rc20.d1_occupancy_NBR d1_rc20_occfcst,
    rc20.d2_occupancy_NBR d2_rc20_occfcst,
    rc20.d3_occupancy_NBR d3_rc20_occfcst,
    rc20.d4_occupancy_NBR d4_rc20_occfcst,
    rc20.d5_occupancy_NBR d5_rc20_occfcst,
    rc20.d6_occupancy_NBR d6_rc20_occfcst,
    rc20.d7_occupancy_NBR d7_rc20_occfcst,
    rc20.d8_occupancy_NBR d8_rc20_occfcst,
    rc20.d1_lrv d1_rc20_lrv,
    rc20.d2_lrv d2_rc20_lrv,
    rc20.d3_lrv d3_rc20_lrv,
    rc20.d4_lrv d4_rc20_lrv,
    rc20.d5_lrv d5_rc20_lrv,
    rc20.d6_lrv d6_rc20_lrv,
    rc20.d7_lrv d7_rc20_lrv,
    rc20.d8_lrv d8_rc20_lrv,
    NULL d1_rc20_price,
    NULL d2_rc20_price,
    NULL d3_rc20_price,
    NULL d4_rc20_price,
    NULL d5_rc20_price,
    NULL d6_rc20_price,
    NULL d7_rc20_price,
    NULL d8_rc20_price,
    NULL d1_rc20_rate_code,
    NULL d2_rc20_rate_code,
    NULL d3_rc20_rate_code,
    NULL d4_rc20_rate_code,
    NULL d5_rc20_rate_code,
    NULL d6_rc20_rate_code,
    NULL d7_rc20_rate_code,
    NULL d8_rc20_rate_code,

    rc21.d1_sold d1_rc21_sold,
    rc21.d2_sold d2_rc21_sold,
    rc21.d3_sold d3_rc21_sold,
    rc21.d4_sold d4_rc21_sold,
    rc21.d5_sold d5_rc21_sold,
    rc21.d6_sold d6_rc21_sold,
    rc21.d7_sold d7_rc21_sold,
    rc21.d8_sold d8_rc21_sold,
    rc21.d1_occupancy_NBR d1_rc21_occfcst,
    rc21.d2_occupancy_NBR d2_rc21_occfcst,
    rc21.d3_occupancy_NBR d3_rc21_occfcst,
    rc21.d4_occupancy_NBR d4_rc21_occfcst,
    rc21.d5_occupancy_NBR d5_rc21_occfcst,
    rc21.d6_occupancy_NBR d6_rc21_occfcst,
    rc21.d7_occupancy_NBR d7_rc21_occfcst,
    rc21.d8_occupancy_NBR d8_rc21_occfcst,
    rc21.d1_lrv d1_rc21_lrv,
    rc21.d2_lrv d2_rc21_lrv,
    rc21.d3_lrv d3_rc21_lrv,
    rc21.d4_lrv d4_rc21_lrv,
    rc21.d5_lrv d5_rc21_lrv,
    rc21.d6_lrv d6_rc21_lrv,
    rc21.d7_lrv d7_rc21_lrv,
    rc21.d8_lrv d8_rc21_lrv,
    NULL d1_rc21_price,
    NULL d2_rc21_price,
    NULL d3_rc21_price,
    NULL d4_rc21_price,
    NULL d5_rc21_price,
    NULL d6_rc21_price,
    NULL d7_rc21_price,
    NULL d8_rc21_price,
    NULL d1_rc21_rate_code,
    NULL d2_rc21_rate_code,
    NULL d3_rc21_rate_code,
    NULL d4_rc21_rate_code,
    NULL d5_rc21_rate_code,
    NULL d6_rc21_rate_code,
    NULL d7_rc21_rate_code,
    NULL d8_rc21_rate_code,

    rc22.d1_sold d1_rc22_sold,
    rc22.d2_sold d2_rc22_sold,
    rc22.d3_sold d3_rc22_sold,
    rc22.d4_sold d4_rc22_sold,
    rc22.d5_sold d5_rc22_sold,
    rc22.d6_sold d6_rc22_sold,
    rc22.d7_sold d7_rc22_sold,
    rc22.d8_sold d8_rc22_sold,
    rc22.d1_occupancy_NBR d1_rc22_occfcst,
    rc22.d2_occupancy_NBR d2_rc22_occfcst,
    rc22.d3_occupancy_NBR d3_rc22_occfcst,
    rc22.d4_occupancy_NBR d4_rc22_occfcst,
    rc22.d5_occupancy_NBR d5_rc22_occfcst,
    rc22.d6_occupancy_NBR d6_rc22_occfcst,
    rc22.d7_occupancy_NBR d7_rc22_occfcst,
    rc22.d8_occupancy_NBR d8_rc22_occfcst,
    rc22.d1_lrv d1_rc22_lrv,
    rc22.d2_lrv d2_rc22_lrv,
    rc22.d3_lrv d3_rc22_lrv,
    rc22.d4_lrv d4_rc22_lrv,
    rc22.d5_lrv d5_rc22_lrv,
    rc22.d6_lrv d6_rc22_lrv,
    rc22.d7_lrv d7_rc22_lrv,
    rc22.d8_lrv d8_rc22_lrv,
    NULL d1_rc22_price,
    NULL d2_rc22_price,
    NULL d3_rc22_price,
    NULL d4_rc22_price,
    NULL d5_rc22_price,
    NULL d6_rc22_price,
    NULL d7_rc22_price,
    NULL d8_rc22_price,
    NULL d1_rc22_rate_code,
    NULL d2_rc22_rate_code,
    NULL d3_rc22_rate_code,
    NULL d4_rc22_rate_code,
    NULL d5_rc22_rate_code,
    NULL d6_rc22_rate_code,
    NULL d7_rc22_rate_code,
    NULL d8_rc22_rate_code,

    rc23.d1_sold d1_rc23_sold,
    rc23.d2_sold d2_rc23_sold,
    rc23.d3_sold d3_rc23_sold,
    rc23.d4_sold d4_rc23_sold,
    rc23.d5_sold d5_rc23_sold,
    rc23.d6_sold d6_rc23_sold,
    rc23.d7_sold d7_rc23_sold,
    rc23.d8_sold d8_rc23_sold,
    rc23.d1_occupancy_NBR d1_rc23_occfcst,
    rc23.d2_occupancy_NBR d2_rc23_occfcst,
    rc23.d3_occupancy_NBR d3_rc23_occfcst,
    rc23.d4_occupancy_NBR d4_rc23_occfcst,
    rc23.d5_occupancy_NBR d5_rc23_occfcst,
    rc23.d6_occupancy_NBR d6_rc23_occfcst,
    rc23.d7_occupancy_NBR d7_rc23_occfcst,
    rc23.d8_occupancy_NBR d8_rc23_occfcst,
    rc23.d1_lrv d1_rc23_lrv,
    rc23.d2_lrv d2_rc23_lrv,
    rc23.d3_lrv d3_rc23_lrv,
    rc23.d4_lrv d4_rc23_lrv,
    rc23.d5_lrv d5_rc23_lrv,
    rc23.d6_lrv d6_rc23_lrv,
    rc23.d7_lrv d7_rc23_lrv,
    rc23.d8_lrv d8_rc23_lrv,
    NULL d1_rc23_price,
    NULL d2_rc23_price,
    NULL d3_rc23_price,
    NULL d4_rc23_price,
    NULL d5_rc23_price,
    NULL d6_rc23_price,
    NULL d7_rc23_price,
    NULL d8_rc23_price,
    NULL d1_rc23_rate_code,
    NULL d2_rc23_rate_code,
    NULL d3_rc23_rate_code,
    NULL d4_rc23_rate_code,
    NULL d5_rc23_rate_code,
    NULL d6_rc23_rate_code,
    NULL d7_rc23_rate_code,
    NULL d8_rc23_rate_code,

    rc24.d1_sold d1_rc24_sold,
    rc24.d2_sold d2_rc24_sold,
    rc24.d3_sold d3_rc24_sold,
    rc24.d4_sold d4_rc24_sold,
    rc24.d5_sold d5_rc24_sold,
    rc24.d6_sold d6_rc24_sold,
    rc24.d7_sold d7_rc24_sold,
    rc24.d8_sold d8_rc24_sold,
    rc24.d1_occupancy_NBR d1_rc24_occfcst,
    rc24.d2_occupancy_NBR d2_rc24_occfcst,
    rc24.d3_occupancy_NBR d3_rc24_occfcst,
    rc24.d4_occupancy_NBR d4_rc24_occfcst,
    rc24.d5_occupancy_NBR d5_rc24_occfcst,
    rc24.d6_occupancy_NBR d6_rc24_occfcst,
    rc24.d7_occupancy_NBR d7_rc24_occfcst,
    rc24.d8_occupancy_NBR d8_rc24_occfcst,
    rc24.d1_lrv d1_rc24_lrv,
    rc24.d2_lrv d2_rc24_lrv,
    rc24.d3_lrv d3_rc24_lrv,
    rc24.d4_lrv d4_rc24_lrv,
    rc24.d5_lrv d5_rc24_lrv,
    rc24.d6_lrv d6_rc24_lrv,
    rc24.d7_lrv d7_rc24_lrv,
    rc24.d8_lrv d8_rc24_lrv,
    NULL d1_rc24_price,
    NULL d2_rc24_price,
    NULL d3_rc24_price,
    NULL d4_rc24_price,
    NULL d5_rc24_price,
    NULL d6_rc24_price,
    NULL d7_rc24_price,
    NULL d8_rc24_price,
    NULL d1_rc24_rate_code,
    NULL d2_rc24_rate_code,
    NULL d3_rc24_rate_code,
    NULL d4_rc24_rate_code,
    NULL d5_rc24_rate_code,
    NULL d6_rc24_rate_code,
    NULL d7_rc24_rate_code,
    NULL d8_rc24_rate_code,

    rc25.d1_sold d1_rc25_sold,
    rc25.d2_sold d2_rc25_sold,
    rc25.d3_sold d3_rc25_sold,
    rc25.d4_sold d4_rc25_sold,
    rc25.d5_sold d5_rc25_sold,
    rc25.d6_sold d6_rc25_sold,
    rc25.d7_sold d7_rc25_sold,
    rc25.d8_sold d8_rc25_sold,
    rc25.d1_occupancy_NBR d1_rc25_occfcst,
    rc25.d2_occupancy_NBR d2_rc25_occfcst,
    rc25.d3_occupancy_NBR d3_rc25_occfcst,
    rc25.d4_occupancy_NBR d4_rc25_occfcst,
    rc25.d5_occupancy_NBR d5_rc25_occfcst,
    rc25.d6_occupancy_NBR d6_rc25_occfcst,
    rc25.d7_occupancy_NBR d7_rc25_occfcst,
    rc25.d8_occupancy_NBR d8_rc25_occfcst,
    rc25.d1_lrv d1_rc25_lrv,
    rc25.d2_lrv d2_rc25_lrv,
    rc25.d3_lrv d3_rc25_lrv,
    rc25.d4_lrv d4_rc25_lrv,
    rc25.d5_lrv d5_rc25_lrv,
    rc25.d6_lrv d6_rc25_lrv,
    rc25.d7_lrv d7_rc25_lrv,
    rc25.d8_lrv d8_rc25_lrv,
    NULL d1_rc25_price,
    NULL d2_rc25_price,
    NULL d3_rc25_price,
    NULL d4_rc25_price,
    NULL d5_rc25_price,
    NULL d6_rc25_price,
    NULL d7_rc25_price,
    NULL d8_rc25_price,
    NULL d1_rc25_rate_code,
    NULL d2_rc25_rate_code,
    NULL d3_rc25_rate_code,
    NULL d4_rc25_rate_code,
    NULL d5_rc25_rate_code,
    NULL d6_rc25_rate_code,
    NULL d7_rc25_rate_code,
    NULL d8_rc25_rate_code

from
    (
        select distinct(0) as daystoArrival from Property
        union
        select cal_sk as daystoArrival from calendar_dim where cal_sk <=@pace_days
    ) base
        left join
    (
        select * from ufn_get_comparative_pace_property(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@pace_days,@isSold,@isLrv,@isOcFcst) where @isHotel>0
    ) hotel on base.daystoArrival = hotel.daystoArrival
        left join
    (
        select * from ufn_get_comparative_pace_bt(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@transient_type,@pace_days,@isSold,@isOcFcst) where @isTransient>0
    ) trans on base.daystoArrival=trans.daystoArrival
        left join
    (
        select * from ufn_get_comparative_pace_bt(@property_id,@d1,@d2,@d3,@d4,@d5,@d6,@d7,@d8,@group_type,@pace_days,@isSold,@isOcFcst) where @isGroup>0
    ) grp on base.daystoArrival=grp.daystoArrival
        LEFT JOIN
    #temp_pace_fg fg1 on base.daystoArrival=fg1.daystoArrival and fg1.fgId=@fg1
        LEFT JOIN
    #temp_pace_fg fg2 on base.daystoArrival=fg2.daystoArrival and fg2.fgId=@fg2
        LEFT JOIN
    #temp_pace_fg fg3 on base.daystoArrival=fg3.daystoArrival and fg3.fgId=@fg3
        LEFT JOIN
    #temp_pace_fg fg4 on base.daystoArrival=fg4.daystoArrival and fg4.fgId=@fg4
        LEFT JOIN
    #temp_pace_fg fg5 on base.daystoArrival=fg5.daystoArrival and fg5.fgId=@fg5
        LEFT JOIN
    #temp_pace_fg fg6 on base.daystoArrival=fg6.daystoArrival and fg6.fgId=@fg6
        LEFT JOIN
    #temp_pace_fg fg7 on base.daystoArrival=fg7.daystoArrival and fg7.fgId=@fg7
        LEFT JOIN
    #temp_pace_fg fg8 on base.daystoArrival=fg8.daystoArrival and fg8.fgId=@fg8
        LEFT JOIN
    #temp_pace_fg fg9 on base.daystoArrival=fg9.daystoArrival and fg9.fgId=@fg9
        LEFT JOIN
    #temp_pace_fg fg10 on base.daystoArrival=fg10.daystoArrival and fg10.fgId=@fg10
        LEFT JOIN
    #temp_pace_fg fg11 on base.daystoArrival=fg11.daystoArrival	 and fg11.fgId=@fg11
        LEFT JOIN
    #temp_pace_fg fg12 on base.daystoArrival=fg12.daystoArrival	 and fg12.fgId=@fg12
        LEFT JOIN
    #temp_pace_fg fg13 on base.daystoArrival=fg13.daystoArrival	 and fg13.fgId=@fg13
        LEFT JOIN
    #temp_pace_fg fg14 on base.daystoArrival=fg14.daystoArrival	 and fg14.fgId=@fg14
        LEFT JOIN
    #temp_pace_fg fg15 on base.daystoArrival=fg15.daystoArrival	 and fg15.fgId=@fg15
        LEFT JOIN
    #temp_pace_fg fg16 on base.daystoArrival=fg16.daystoArrival	 and fg16.fgId=@fg16
        LEFT JOIN
    #temp_pace_fg fg17 on base.daystoArrival=fg17.daystoArrival	 and fg17.fgId=@fg17
        LEFT JOIN
    #temp_pace_fg fg18 on base.daystoArrival=fg18.daystoArrival	 and fg18.fgId=@fg18
        LEFT JOIN
    #temp_pace_fg fg19 on base.daystoArrival=fg19.daystoArrival	 and fg19.fgId=@fg19
        LEFT JOIN
    #temp_pace_fg fg20 on base.daystoArrival=fg20.daystoArrival	 and fg20.fgId=@fg20
        LEFT JOIN
    #temp_pace_fg fg21 on base.daystoArrival=fg21.daystoArrival	 and fg21.fgId=@fg21
        LEFT JOIN
    #temp_pace_fg fg22 on base.daystoArrival=fg22.daystoArrival	 and fg22.fgId=@fg22
        LEFT JOIN
    #temp_pace_fg fg23 on base.daystoArrival=fg23.daystoArrival	 and fg23.fgId=@fg23
        LEFT JOIN
    #temp_pace_fg fg24 on base.daystoArrival=fg24.daystoArrival	 and fg24.fgId=@fg24
        LEFT JOIN
    #temp_pace_fg fg25 on base.daystoArrival=fg25.daystoArrival	 and fg25.fgId=@fg25
        LEFT JOIN
    #temp_pace_bv bv1 on base.daystoArrival=bv1.daystoArrival and bv1.bvId = @bv1
        LEFT JOIN
    #temp_pace_bv bv2 on base.daystoArrival=bv2.daystoArrival and bv2.bvId = @bv2
        LEFT JOIN
    #temp_pace_bv bv3 on base.daystoArrival=bv3.daystoArrival and bv3.bvId = @bv3
        LEFT JOIN
    #temp_pace_bv bv4 on base.daystoArrival=bv4.daystoArrival and bv4.bvId = @bv4
        LEFT JOIN
    #temp_pace_bv bv5 on base.daystoArrival=bv5.daystoArrival and bv5.bvId = @bv5
        LEFT JOIN
    #temp_pace_bv bv6 on base.daystoArrival=bv6.daystoArrival and bv6.bvId = @bv6
        LEFT JOIN
    #temp_pace_bv bv7 on base.daystoArrival=bv7.daystoArrival and bv7.bvId = @bv7
        LEFT JOIN
    #temp_pace_bv bv8 on base.daystoArrival=bv8.daystoArrival and bv8.bvId = @bv8
        LEFT JOIN
    #temp_pace_bv bv9 on base.daystoArrival=bv9.daystoArrival and bv9.bvId = @bv9
        LEFT JOIN
    #temp_pace_bv bv10 on base.daystoArrival=bv10.daystoArrival and bv10.bvId = @bv10
        LEFT JOIN
    #temp_pace_bv bv11 on base.daystoArrival=bv11.daystoArrival and bv11.bvId = @bv11
        LEFT JOIN
    #temp_pace_bv bv12 on base.daystoArrival=bv12.daystoArrival and bv12.bvId = @bv12
        LEFT JOIN
    #temp_pace_bv bv13 on base.daystoArrival=bv13.daystoArrival and bv13.bvId = @bv13
        LEFT JOIN
    #temp_pace_bv bv14 on base.daystoArrival=bv14.daystoArrival and bv14.bvId = @bv14
        LEFT JOIN
    #temp_pace_bv bv15 on base.daystoArrival=bv15.daystoArrival and bv15.bvId = @bv15
        LEFT JOIN
    #temp_pace_bv bv16 on base.daystoArrival=bv16.daystoArrival and bv16.bvId = @bv16
        LEFT JOIN
    #temp_pace_bv bv17 on base.daystoArrival=bv17.daystoArrival and bv17.bvId = @bv17
        LEFT JOIN
    #temp_pace_bv bv18 on base.daystoArrival=bv18.daystoArrival and bv18.bvId = @bv18
        LEFT JOIN
    #temp_pace_bv bv19 on base.daystoArrival=bv19.daystoArrival and bv19.bvId = @bv19
        LEFT JOIN
    #temp_pace_bv bv20 on base.daystoArrival=bv20.daystoArrival and bv20.bvId = @bv20
        LEFT JOIN
    #temp_pace_bv bv21 on base.daystoArrival=bv21.daystoArrival and bv21.bvId = @bv21
        LEFT JOIN
    #temp_pace_bv bv22 on base.daystoArrival=bv22.daystoArrival and bv22.bvId = @bv22
        LEFT JOIN
    #temp_pace_bv bv23 on base.daystoArrival=bv23.daystoArrival and bv23.bvId = @bv23
        LEFT JOIN
    #temp_pace_bv bv24 on base.daystoArrival=bv24.daystoArrival and bv24.bvId = @bv24
        LEFT JOIN
    #temp_pace_bv bv25 on base.daystoArrival=bv25.daystoArrival and bv25.bvId = @bv25
        LEFT JOIN
    #temp_pace_rc rc1 on base.daystoArrival=rc1.daystoArrival and rc1.rcId = @rc1
        LEFT JOIN
    #temp_pace_rc rc2 on base.daystoArrival=rc2.daystoArrival and rc2.rcId = @rc2
        LEFT JOIN
    #temp_pace_rc rc3 on base.daystoArrival=rc3.daystoArrival and rc3.rcId = @rc3
        LEFT JOIN
    #temp_pace_rc rc4 on base.daystoArrival=rc4.daystoArrival and rc4.rcId = @rc4
        LEFT JOIN
    #temp_pace_rc rc5 on base.daystoArrival=rc5.daystoArrival and rc5.rcId = @rc5
        LEFT JOIN
    #temp_pace_rc rc6 on base.daystoArrival=rc6.daystoArrival and rc6.rcId = @rc6
        LEFT JOIN
    #temp_pace_rc rc7 on base.daystoArrival=rc7.daystoArrival and rc7.rcId = @rc7
        LEFT JOIN
    #temp_pace_rc rc8 on base.daystoArrival=rc8.daystoArrival and rc8.rcId = @rc8
        LEFT JOIN
    #temp_pace_rc rc9 on base.daystoArrival=rc9.daystoArrival and rc9.rcId = @rc9
        LEFT JOIN
    #temp_pace_rc rc10 on base.daystoArrival=rc10.daystoArrival and rc10.rcId = @rc10
        LEFT JOIN
    #temp_pace_rc rc11 on base.daystoArrival=rc11.daystoArrival and rc11.rcId = @rc11
        LEFT JOIN
    #temp_pace_rc rc12 on base.daystoArrival=rc12.daystoArrival and rc12.rcId = @rc12
        LEFT JOIN
    #temp_pace_rc rc13 on base.daystoArrival=rc13.daystoArrival and rc13.rcId = @rc13
        LEFT JOIN
    #temp_pace_rc rc14 on base.daystoArrival=rc14.daystoArrival and rc14.rcId = @rc14
        LEFT JOIN
    #temp_pace_rc rc15 on base.daystoArrival=rc15.daystoArrival and rc15.rcId = @rc15
        LEFT JOIN
    #temp_pace_rc rc16 on base.daystoArrival=rc16.daystoArrival and rc16.rcId = @rc16
        LEFT JOIN
    #temp_pace_rc rc17 on base.daystoArrival=rc17.daystoArrival and rc17.rcId = @rc17
        LEFT JOIN
    #temp_pace_rc rc18 on base.daystoArrival=rc18.daystoArrival and rc18.rcId = @rc18
        LEFT JOIN
    #temp_pace_rc rc19 on base.daystoArrival=rc19.daystoArrival and rc19.rcId = @rc19
        LEFT JOIN
    #temp_pace_rc rc20 on base.daystoArrival=rc20.daystoArrival and rc20.rcId = @rc20
        LEFT JOIN
    #temp_pace_rc rc21 on base.daystoArrival=rc21.daystoArrival and rc21.rcId = @rc21
        LEFT JOIN
    #temp_pace_rc rc22 on base.daystoArrival=rc22.daystoArrival and rc22.rcId = @rc22
        LEFT JOIN
    #temp_pace_rc rc23 on base.daystoArrival=rc23.daystoArrival and rc23.rcId = @rc23
        LEFT JOIN
    #temp_pace_rc rc24 on base.daystoArrival=rc24.daystoArrival and rc24.rcId = @rc24
        LEFT JOIN
    #temp_pace_rc rc25 on base.daystoArrival=rc25.daystoArrival and rc25.rcId = @rc25
        order by base.daystoArrival

drop table #temp_pace_all
drop table #temp_pace_bv
drop table #temp_pace_fg
drop table #temp_pace_rc
    return
end