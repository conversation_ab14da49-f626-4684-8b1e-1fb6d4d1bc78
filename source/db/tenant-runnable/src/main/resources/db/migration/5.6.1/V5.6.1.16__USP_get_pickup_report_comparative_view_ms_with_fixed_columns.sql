IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_get_pickup_report_comparative_view_ms_with_fixed_columns]'))
DROP PROCEDURE [dbo].[usp_get_pickup_report_comparative_view_ms_with_fixed_columns]
GO

/*************************************************************************************

Procedure Name: usp_get_pickup_report_comparative_view_ms_with_fixed_columns

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@Market_segment_id --> comma seperated list of Market_segment_Ids (MAX 8 supported)
	@business_start_dt --> this is the derived version of caughtup_date(start_date)..for more details please check the data dictionary.
	@business_end_dt --> this is the derived version of caughtup_date(end_date)..for more details please check the data dictionary.
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_business_start_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_business_end_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_start_date  --> used when @isRollingDate is 1, else, this can be blank
	@rolling_end_date  --> used when @isRollingDate is 1, else, this can be blank
	
Ouput Parameter : NA

Execution: this is just an example
	Example : Pickup Report by Market segment in comparative view
	------------------------------------------------
	exec usp_get_pickup_report_comparative_view_ms_with_fixed_columns 10016,'109,110,111','2011-06-17','2011-06-30','2011-07-01','2011-07-02'


	
Purpose: The purpose of this procedure is to report Pickup report by Market segment in comparative view

Assumptions : Please make sure to pass Market_segment_Ids as a string ('30').
              When you are passing multiple Market segment ids, please enclose them in single quotes
              seperated by a comma - ('30,31,63'). Please refer to example
		 
***************************************************************************************/

CREATE procedure usp_get_pickup_report_comparative_view_ms_with_fixed_columns
(
	@property_id int,
	@Mkt_seg_id varchar(500),
	@business_start_dt date,
	@business_end_dt date,
	@start_date date,
	@end_date date,

	@isRollingDate int,
	@rolling_business_start_dt nvarchar(50),
	@rolling_business_end_dt nvarchar(50),		
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50)	
	
)	
as 
begin	
    
	set nocount on

		declare @caughtupdate date 
		set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property

		if(@isRollingDate=1)
		begin
			set @business_start_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_start_dt ,@caughtupdate))
			set @business_end_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_end_dt ,@caughtupdate))
			set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
			set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
		end

		--------------------------------------- activity_asof_businessdate_by_individual_ms ------------------------------------------------------
		create table #activity_asof_businessdate_by_individual_ms_1 
		(
			occupancy_dt date,
			property_id int,
			mkt_seg_id int,
			rooms_sold int, 
			room_revenue numeric(19,5),
			adr numeric(19,5) 
		)
		insert into #activity_asof_businessdate_by_individual_ms_1
		exec usp_get_activity_asof_businessdate_by_individual_ms @property_id,@mkt_seg_id,@business_end_dt,@start_date,@end_date

		create table #activity_asof_businessdate_by_individual_ms_2 
		(
			occupancy_dt date,
			property_id int,
			mkt_seg_id int,
			rooms_sold int, 
			room_revenue numeric(19,5),
			adr numeric(19,5) 
		)
		insert into #activity_asof_businessdate_by_individual_ms_2
		exec usp_get_activity_asof_businessdate_by_individual_ms @property_id,@mkt_seg_id,@business_start_dt,@start_date,@end_date
		
		create table #activity_asof_businessdate_by_individual_ms
		(
			property_id int,
			occupancy_dt date,
			mkt_seg_id int,
			end_date_rooms_sold int,
			start_date_rooms_sold int,
			end_date_room_revenue numeric(19,5),
			start_date_room_revenue numeric(19,5),
			end_date_adr numeric(19,5),
			start_date_adr numeric(19,5)
		)
		insert into #activity_asof_businessdate_by_individual_ms
		select 
			q2.property_id, q2.occupancy_dt, q2.mkt_seg_id, q1.rooms_sold as end_date_rooms_sold, q2.rooms_sold as start_date_rooms_sold,
			q1.room_revenue as end_date_room_revenue, q2.room_revenue as start_date_room_revenue, q1.adr as end_date_adr, q2.adr as start_date_adr
		from
			#activity_asof_businessdate_by_individual_ms_1 as q1
		right outer join
		  	#activity_asof_businessdate_by_individual_ms_2 as q2
		on q1.property_id=q2.property_id and q1.occupancy_dt=q2.occupancy_dt and q1.mkt_seg_id=q2.mkt_seg_id

		
		-------------------------------------------------------- occupancy_forecast_asof_businessdate_by_individual_ms -------------------------------------------------------

		create table #tempt1
		(
			occupancy_dt date,
			property_id	int,
			mkt_seg_id int,
			occupancy_nbr numeric(8,2),
			revenue numeric(19,5),
			adr numeric(19,5)
		)

		insert into #tempt1
		exec usp_get_occupancy_forecast_asof_businessdate_by_individual_ms @property_id,@mkt_seg_id,@business_end_dt,@start_date,@end_date

		create table #tempt2
		(
			occupancy_dt date,
			property_id	int,
			mkt_seg_id int,
			occupancy_nbr numeric(8,2),
			revenue numeric(19,5),
			adr numeric(19,5)
		)

		insert into #tempt2
		exec usp_get_occupancy_forecast_asof_businessdate_by_individual_ms @property_id,@mkt_seg_id,@business_start_dt,@start_date,@end_date

		create table #occupancy_forecast_asof_businessdate_by_individual_ms
		(
			property_id int,
			occupancy_dt date,
			mkt_seg_id int,
			end_date_occupancy_nbr numeric(19,5),
			start_date_occupancy_nbr numeric(19,5),
			end_date_revenue numeric(19,5),
			start_date_revenue numeric(19,5),
			end_date_adr numeric(19,5),
			start_date_adr numeric(19,5)
		)
		insert into #occupancy_forecast_asof_businessdate_by_individual_ms
		select 
			q2.property_id, q2.occupancy_dt, q2.mkt_seg_id,q1.occupancy_nbr as end_date_occupancy_nbr, q2.occupancy_nbr as start_date_occupancy_nbr,
			q1.revenue as end_date_revenue, q2.revenue as start_date_revenue, q1.adr as end_date_adr, q2.adr as start_date_adr
		from	
			#tempt1 as q1
		right outer join
			#tempt2 as q2 
		on q1.property_id=q2.property_id and q1.occupancy_dt=q2.occupancy_dt and q1.mkt_seg_id=q2.mkt_seg_id

		----------------------------------------------------------------------get_occupancy_forecast_by_individual_ms----------------------------------------------------------------

		create table #occupancy_forecast_by_individual_ms
		(
			occupancy_dt date,
			property_id int,
			mkt_seg_id int,
			occupancy_forecast_current numeric(8,2),
			fcsted_room_revenue_current numeric(19,5),
			estimated_adr_current numeric(19,5)
		)
		insert into #occupancy_forecast_by_individual_ms
		exec dbo.usp_get_occupancy_forecast_by_individual_ms @property_id,@mkt_seg_id,@start_date,@end_date


		-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------

		declare @ms1 int
		declare @ms2 int
		declare @ms3 int
		declare @ms4 int
		declare @ms5 int
		declare @ms6 int
		declare @ms7 int
		declare @ms8 int
		declare @ms9 int
		declare @ms10 int
		declare @ms11 int
		declare @ms12 int
		declare @ms13 int
		declare @ms14 int
		declare @ms15 int
		declare @ms16 int
		declare @ms17 int
		declare @ms18 int
		declare @ms19 int
		declare @ms20 int
		declare @ms21 int
		declare @ms22 int
		declare @ms23 int
		declare @ms24 int
		declare @ms25 int
		declare @ms26 int
		declare @ms27 int
		declare @ms28 int
		declare @ms29 int
		declare @ms30 int
		declare @ms31 int
		declare @ms32 int
		declare @ms33 int
		declare @ms34 int
		declare @ms35 int
		declare @ms36 int
		declare @ms37 int
		declare @ms38 int
		declare @ms39 int
		declare @ms40 int
		declare @ms41 int
		declare @ms42 int
		declare @ms43 int
		declare @ms44 int
		declare @ms45 int
		declare @ms46 int
		declare @ms47 int
		declare @ms48 int
		declare @ms49 int
		declare @ms50 int

		declare @ms1_name nvarchar(150)
		declare @ms2_name nvarchar(150)
		declare @ms3_name nvarchar(150)
		declare @ms4_name nvarchar(150)
		declare @ms5_name nvarchar(150)
		declare @ms6_name nvarchar(150)
		declare @ms7_name nvarchar(150)
		declare @ms8_name nvarchar(150)
		declare @ms9_name nvarchar(150)
		declare @ms10_name nvarchar(150)
		declare @ms11_name nvarchar(150)
		declare @ms12_name nvarchar(150)
		declare @ms13_name nvarchar(150)
		declare @ms14_name nvarchar(150)
		declare @ms15_name nvarchar(150)
		declare @ms16_name nvarchar(150)
		declare @ms17_name nvarchar(150)
		declare @ms18_name nvarchar(150)
		declare @ms19_name nvarchar(150)
		declare @ms20_name nvarchar(150)
		declare @ms21_name nvarchar(150)
		declare @ms22_name nvarchar(150)
		declare @ms23_name nvarchar(150)
		declare @ms24_name nvarchar(150)
		declare @ms25_name nvarchar(150)
		declare @ms26_name nvarchar(150)
		declare @ms27_name nvarchar(150)
		declare @ms28_name nvarchar(150)
		declare @ms29_name nvarchar(150)
		declare @ms30_name nvarchar(150)
		declare @ms31_name nvarchar(150)
		declare @ms32_name nvarchar(150)
		declare @ms33_name nvarchar(150)
		declare @ms34_name nvarchar(150)
		declare @ms35_name nvarchar(150)
		declare @ms36_name nvarchar(150)
		declare @ms37_name nvarchar(150)
		declare @ms38_name nvarchar(150)
		declare @ms39_name nvarchar(150)
		declare @ms40_name nvarchar(150)
		declare @ms41_name nvarchar(150)
		declare @ms42_name nvarchar(150)
		declare @ms43_name nvarchar(150)
		declare @ms44_name nvarchar(150)
		declare @ms45_name nvarchar(150)
		declare @ms46_name nvarchar(150)
		declare @ms47_name nvarchar(150)
		declare @ms48_name nvarchar(150)
		declare @ms49_name nvarchar(150)
		declare @ms50_name nvarchar(150)
	
		declare @tempMS table
		(
			number int,
			mkt_seg_id int,
			mkt_seg_name nvarchar(150)
		)
		insert into @tempMS
		select number = ROW_NUMBER() OVER (ORDER BY mkt_seg_id),mkt_seg_id, mkt_seg_name
			from mkt_seg where Property_ID=@property_id and mkt_seg_id in (SELECT Value FROM varcharToInt(@mkt_seg_id,','))
			and Status_ID=1
		
		set @ms1 = (Select mkt_seg_id from @tempMS where number=1)
		set @ms2 = (Select mkt_seg_id from @tempMS where number=2)
		set @ms3 = (Select mkt_seg_id from @tempMS where number=3)
		set @ms4 = (Select mkt_seg_id from @tempMS where number=4)
		set @ms5 = (Select mkt_seg_id from @tempMS where number=5)
		set @ms6 = (Select mkt_seg_id from @tempMS where number=6)
		set @ms7 = (Select mkt_seg_id from @tempMS where number=7)
		set @ms8 = (Select mkt_seg_id from @tempMS where number=8)
		set @ms9 = (Select mkt_seg_id from @tempMS where number=9)
		set @ms10 = (Select mkt_seg_id from @tempMS where number=10)
		set @ms11 = (Select mkt_seg_id from @tempMS where number=11)
		set @ms12 = (Select mkt_seg_id from @tempMS where number=12)
		set @ms13 = (Select mkt_seg_id from @tempMS where number=13)
		set @ms14 = (Select mkt_seg_id from @tempMS where number=14)
		set @ms15 = (Select mkt_seg_id from @tempMS where number=15)
		set @ms16 = (Select mkt_seg_id from @tempMS where number=16)
		set @ms17 = (Select mkt_seg_id from @tempMS where number=17)
		set @ms18 = (Select mkt_seg_id from @tempMS where number=18)
		set @ms19 = (Select mkt_seg_id from @tempMS where number=19)
		set @ms20 = (Select mkt_seg_id from @tempMS where number=20)
		set @ms21 = (Select mkt_seg_id from @tempMS where number=21)
		set @ms22 = (Select mkt_seg_id from @tempMS where number=22)
		set @ms23 = (Select mkt_seg_id from @tempMS where number=23)
		set @ms24 = (Select mkt_seg_id from @tempMS where number=24)
		set @ms25 = (Select mkt_seg_id from @tempMS where number=25)
		set @ms26 = (Select mkt_seg_id from @tempMS where number=26)
		set @ms27 = (Select mkt_seg_id from @tempMS where number=27)
		set @ms28 = (Select mkt_seg_id from @tempMS where number=28)
		set @ms29 = (Select mkt_seg_id from @tempMS where number=29)
		set @ms30 = (Select mkt_seg_id from @tempMS where number=30)
		set @ms31 = (Select mkt_seg_id from @tempMS where number=31)
		set @ms32 = (Select mkt_seg_id from @tempMS where number=32)
		set @ms33 = (Select mkt_seg_id from @tempMS where number=33)
		set @ms34 = (Select mkt_seg_id from @tempMS where number=34)
		set @ms35 = (Select mkt_seg_id from @tempMS where number=35)
		set @ms36 = (Select mkt_seg_id from @tempMS where number=36)
		set @ms37 = (Select mkt_seg_id from @tempMS where number=37)
		set @ms38 = (Select mkt_seg_id from @tempMS where number=38)
		set @ms39 = (Select mkt_seg_id from @tempMS where number=39)
		set @ms40 = (Select mkt_seg_id from @tempMS where number=40)
		set @ms41 = (Select mkt_seg_id from @tempMS where number=41)
		set @ms42 = (Select mkt_seg_id from @tempMS where number=42)
		set @ms43 = (Select mkt_seg_id from @tempMS where number=43)
		set @ms44 = (Select mkt_seg_id from @tempMS where number=44)
		set @ms45 = (Select mkt_seg_id from @tempMS where number=45)
		set @ms46 = (Select mkt_seg_id from @tempMS where number=46)
		set @ms47 = (Select mkt_seg_id from @tempMS where number=47)
		set @ms48 = (Select mkt_seg_id from @tempMS where number=48)
		set @ms49 = (Select mkt_seg_id from @tempMS where number=49)
		set @ms50 = (Select mkt_seg_id from @tempMS where number=50)

		set @ms1_name = (Select mkt_seg_name from @tempMS where number=1)
		set @ms2_name = (Select mkt_seg_name from @tempMS where number=2)
		set @ms3_name = (Select mkt_seg_name from @tempMS where number=3)
		set @ms4_name = (Select mkt_seg_name from @tempMS where number=4)
		set @ms5_name = (Select mkt_seg_name from @tempMS where number=5)
		set @ms6_name = (Select mkt_seg_name from @tempMS where number=6)
		set @ms7_name = (Select mkt_seg_name from @tempMS where number=7)
		set @ms8_name = (Select mkt_seg_name from @tempMS where number=8)
		set @ms9_name = (Select mkt_seg_name from @tempMS where number=9)
		set @ms10_name = (Select mkt_seg_name from @tempMS where number=10)
		set @ms11_name = (Select mkt_seg_name from @tempMS where number=11)
		set @ms12_name = (Select mkt_seg_name from @tempMS where number=12)
		set @ms13_name = (Select mkt_seg_name from @tempMS where number=13)
		set @ms14_name = (Select mkt_seg_name from @tempMS where number=14)
		set @ms15_name = (Select mkt_seg_name from @tempMS where number=15)
		set @ms16_name = (Select mkt_seg_name from @tempMS where number=16)
		set @ms17_name = (Select mkt_seg_name from @tempMS where number=17)
		set @ms18_name = (Select mkt_seg_name from @tempMS where number=18)
		set @ms19_name = (Select mkt_seg_name from @tempMS where number=19)
		set @ms20_name = (Select mkt_seg_name from @tempMS where number=20)
		set @ms21_name = (Select mkt_seg_name from @tempMS where number=21)
		set @ms22_name = (Select mkt_seg_name from @tempMS where number=22)
		set @ms23_name = (Select mkt_seg_name from @tempMS where number=23)
		set @ms24_name = (Select mkt_seg_name from @tempMS where number=24)
		set @ms25_name = (Select mkt_seg_name from @tempMS where number=25)
		set @ms26_name = (Select mkt_seg_name from @tempMS where number=26)
		set @ms27_name = (Select mkt_seg_name from @tempMS where number=27)
		set @ms28_name = (Select mkt_seg_name from @tempMS where number=28)
		set @ms29_name = (Select mkt_seg_name from @tempMS where number=29)
		set @ms30_name = (Select mkt_seg_name from @tempMS where number=30)
		set @ms31_name = (Select mkt_seg_name from @tempMS where number=31)
		set @ms32_name = (Select mkt_seg_name from @tempMS where number=32)
		set @ms33_name = (Select mkt_seg_name from @tempMS where number=33)
		set @ms34_name = (Select mkt_seg_name from @tempMS where number=34)
		set @ms35_name = (Select mkt_seg_name from @tempMS where number=35)
		set @ms36_name = (Select mkt_seg_name from @tempMS where number=36)
		set @ms37_name = (Select mkt_seg_name from @tempMS where number=37)
		set @ms38_name = (Select mkt_seg_name from @tempMS where number=38)
		set @ms39_name = (Select mkt_seg_name from @tempMS where number=39)
		set @ms40_name = (Select mkt_seg_name from @tempMS where number=40)
		set @ms41_name = (Select mkt_seg_name from @tempMS where number=41)
		set @ms42_name = (Select mkt_seg_name from @tempMS where number=42)
		set @ms43_name = (Select mkt_seg_name from @tempMS where number=43)
		set @ms44_name = (Select mkt_seg_name from @tempMS where number=44)
		set @ms45_name = (Select mkt_seg_name from @tempMS where number=45)
		set @ms46_name = (Select mkt_seg_name from @tempMS where number=46)
		set @ms47_name = (Select mkt_seg_name from @tempMS where number=47)
		set @ms48_name = (Select mkt_seg_name from @tempMS where number=48)
		set @ms49_name = (Select mkt_seg_name from @tempMS where number=49)
		set @ms50_name = (Select mkt_seg_name from @tempMS where number=50)

		--------------------------------Main query starts from here-------------------------------
		select 
		occupancy_dt,dow

		,@ms1_name as ms1_name
		,@ms2_name as ms2_name
		,@ms3_name as ms3_name
		,@ms4_name as ms4_name
		,@ms5_name as ms5_name
		,@ms6_name as ms6_name
		,@ms7_name as ms7_name
		,@ms8_name as ms8_name
		,@ms9_name as ms9_name
		,@ms10_name as ms10_name
		,@ms11_name as ms11_name
		,@ms12_name as ms12_name
		,@ms13_name as ms13_name
		,@ms14_name as ms14_name
		,@ms15_name as ms15_name
		,@ms16_name as ms16_name
		,@ms17_name as ms17_name
		,@ms18_name as ms18_name
		,@ms19_name as ms19_name
		,@ms20_name as ms20_name
		,@ms21_name as ms21_name
		,@ms22_name as ms22_name
		,@ms23_name as ms23_name
		,@ms24_name as ms24_name
		,@ms25_name as ms25_name
		,@ms26_name as ms26_name
		,@ms27_name as ms27_name
		,@ms28_name as ms28_name
		,@ms29_name as ms29_name
		,@ms30_name as ms30_name
		,@ms31_name as ms31_name
		,@ms32_name as ms32_name
		,@ms33_name as ms33_name
		,@ms34_name as ms34_name
		,@ms35_name as ms35_name
		,@ms36_name as ms36_name
		,@ms37_name as ms37_name
		,@ms38_name as ms38_name
		,@ms39_name as ms39_name
		,@ms40_name as ms40_name
		,@ms41_name as ms41_name
		,@ms42_name as ms42_name
		,@ms43_name as ms43_name
		,@ms44_name as ms44_name
		,@ms45_name as ms45_name
		,@ms46_name as ms46_name
		,@ms47_name as ms47_name
		,@ms48_name as ms48_name
		,@ms49_name as ms49_name
		,@ms50_name as ms50_name

		,isnull(MAX(ms1_roomsoldcurrent),0.0) as ms1_roomsoldcurrent,isnull(MAX(ms1_roomssoldpickup),0) as ms1_roomssoldpickup
		,isnull(MAX(ms2_roomsoldcurrent),0.0) as ms2_roomsoldcurrent,isnull(MAX(ms2_roomssoldpickup),0) as ms2_roomssoldpickup
		,isnull(MAX(ms3_roomsoldcurrent),0.0) as ms3_roomsoldcurrent,isnull(MAX(ms3_roomssoldpickup),0) as ms3_roomssoldpickup
		,isnull(MAX(ms4_roomsoldcurrent),0.0) as ms4_roomsoldcurrent,isnull(MAX(ms4_roomssoldpickup),0) as ms4_roomssoldpickup
		,isnull(MAX(ms5_roomsoldcurrent),0.0) as ms5_roomsoldcurrent,isnull(MAX(ms5_roomssoldpickup),0) as ms5_roomssoldpickup
		,isnull(MAX(ms6_roomsoldcurrent),0.0) as ms6_roomsoldcurrent,isnull(MAX(ms6_roomssoldpickup),0) as ms6_roomssoldpickup
		,isnull(MAX(ms7_roomsoldcurrent),0.0) as ms7_roomsoldcurrent,isnull(MAX(ms7_roomssoldpickup),0) as ms7_roomssoldpickup
		,isnull(MAX(ms8_roomsoldcurrent),0.0) as ms8_roomsoldcurrent,isnull(MAX(ms8_roomssoldpickup),0) as ms8_roomssoldpickup
		,isnull(MAX(ms9_roomsoldcurrent),0.0) as ms9_roomsoldcurrent,isnull(MAX(ms9_roomssoldpickup),0) as ms9_roomssoldpickup
		,isnull(MAX(ms10_roomsoldcurrent),0.0) as ms10_roomsoldcurrent,isnull(MAX(ms10_roomssoldpickup),0) as ms10_roomssoldpickup
		,isnull(MAX(ms11_roomsoldcurrent),0.0) as ms11_roomsoldcurrent,isnull(MAX(ms11_roomssoldpickup),0) as ms11_roomssoldpickup
		,isnull(MAX(ms12_roomsoldcurrent),0.0) as ms12_roomsoldcurrent,isnull(MAX(ms12_roomssoldpickup),0) as ms12_roomssoldpickup
		,isnull(MAX(ms13_roomsoldcurrent),0.0) as ms13_roomsoldcurrent,isnull(MAX(ms13_roomssoldpickup),0) as ms13_roomssoldpickup
		,isnull(MAX(ms14_roomsoldcurrent),0.0) as ms14_roomsoldcurrent,isnull(MAX(ms14_roomssoldpickup),0) as ms14_roomssoldpickup
		,isnull(MAX(ms15_roomsoldcurrent),0.0) as ms15_roomsoldcurrent,isnull(MAX(ms15_roomssoldpickup),0) as ms15_roomssoldpickup
		,isnull(MAX(ms16_roomsoldcurrent),0.0) as ms16_roomsoldcurrent,isnull(MAX(ms16_roomssoldpickup),0) as ms16_roomssoldpickup
		,isnull(MAX(ms17_roomsoldcurrent),0.0) as ms17_roomsoldcurrent,isnull(MAX(ms17_roomssoldpickup),0) as ms17_roomssoldpickup
		,isnull(MAX(ms18_roomsoldcurrent),0.0) as ms18_roomsoldcurrent,isnull(MAX(ms18_roomssoldpickup),0) as ms18_roomssoldpickup
		,isnull(MAX(ms19_roomsoldcurrent),0.0) as ms19_roomsoldcurrent,isnull(MAX(ms19_roomssoldpickup),0) as ms19_roomssoldpickup
		,isnull(MAX(ms20_roomsoldcurrent),0.0) as ms20_roomsoldcurrent,isnull(MAX(ms20_roomssoldpickup),0) as ms20_roomssoldpickup
		,isnull(MAX(ms21_roomsoldcurrent),0.0) as ms21_roomsoldcurrent,isnull(MAX(ms21_roomssoldpickup),0) as ms21_roomssoldpickup
		,isnull(MAX(ms22_roomsoldcurrent),0.0) as ms22_roomsoldcurrent,isnull(MAX(ms22_roomssoldpickup),0) as ms22_roomssoldpickup
		,isnull(MAX(ms23_roomsoldcurrent),0.0) as ms23_roomsoldcurrent,isnull(MAX(ms23_roomssoldpickup),0) as ms23_roomssoldpickup
		,isnull(MAX(ms24_roomsoldcurrent),0.0) as ms24_roomsoldcurrent,isnull(MAX(ms24_roomssoldpickup),0) as ms24_roomssoldpickup
		,isnull(MAX(ms25_roomsoldcurrent),0.0) as ms25_roomsoldcurrent,isnull(MAX(ms25_roomssoldpickup),0) as ms25_roomssoldpickup
		,isnull(MAX(ms26_roomsoldcurrent),0.0) as ms26_roomsoldcurrent,isnull(MAX(ms26_roomssoldpickup),0) as ms26_roomssoldpickup
		,isnull(MAX(ms27_roomsoldcurrent),0.0) as ms27_roomsoldcurrent,isnull(MAX(ms27_roomssoldpickup),0) as ms27_roomssoldpickup
		,isnull(MAX(ms28_roomsoldcurrent),0.0) as ms28_roomsoldcurrent,isnull(MAX(ms28_roomssoldpickup),0) as ms28_roomssoldpickup
		,isnull(MAX(ms29_roomsoldcurrent),0.0) as ms29_roomsoldcurrent,isnull(MAX(ms29_roomssoldpickup),0) as ms29_roomssoldpickup
		,isnull(MAX(ms30_roomsoldcurrent),0.0) as ms30_roomsoldcurrent,isnull(MAX(ms30_roomssoldpickup),0) as ms30_roomssoldpickup
		,isnull(MAX(ms31_roomsoldcurrent),0.0) as ms31_roomsoldcurrent,isnull(MAX(ms31_roomssoldpickup),0) as ms31_roomssoldpickup
		,isnull(MAX(ms32_roomsoldcurrent),0.0) as ms32_roomsoldcurrent,isnull(MAX(ms32_roomssoldpickup),0) as ms32_roomssoldpickup
		,isnull(MAX(ms33_roomsoldcurrent),0.0) as ms33_roomsoldcurrent,isnull(MAX(ms33_roomssoldpickup),0) as ms33_roomssoldpickup
		,isnull(MAX(ms34_roomsoldcurrent),0.0) as ms34_roomsoldcurrent,isnull(MAX(ms34_roomssoldpickup),0) as ms34_roomssoldpickup
		,isnull(MAX(ms35_roomsoldcurrent),0.0) as ms35_roomsoldcurrent,isnull(MAX(ms35_roomssoldpickup),0) as ms35_roomssoldpickup
		,isnull(MAX(ms36_roomsoldcurrent),0.0) as ms36_roomsoldcurrent,isnull(MAX(ms36_roomssoldpickup),0) as ms36_roomssoldpickup
		,isnull(MAX(ms37_roomsoldcurrent),0.0) as ms37_roomsoldcurrent,isnull(MAX(ms37_roomssoldpickup),0) as ms37_roomssoldpickup
		,isnull(MAX(ms38_roomsoldcurrent),0.0) as ms38_roomsoldcurrent,isnull(MAX(ms38_roomssoldpickup),0) as ms38_roomssoldpickup
		,isnull(MAX(ms39_roomsoldcurrent),0.0) as ms39_roomsoldcurrent,isnull(MAX(ms39_roomssoldpickup),0) as ms39_roomssoldpickup
		,isnull(MAX(ms40_roomsoldcurrent),0.0) as ms40_roomsoldcurrent,isnull(MAX(ms40_roomssoldpickup),0) as ms40_roomssoldpickup
		,isnull(MAX(ms41_roomsoldcurrent),0.0) as ms41_roomsoldcurrent,isnull(MAX(ms41_roomssoldpickup),0) as ms41_roomssoldpickup
		,isnull(MAX(ms42_roomsoldcurrent),0.0) as ms42_roomsoldcurrent,isnull(MAX(ms42_roomssoldpickup),0) as ms42_roomssoldpickup
		,isnull(MAX(ms43_roomsoldcurrent),0.0) as ms43_roomsoldcurrent,isnull(MAX(ms43_roomssoldpickup),0) as ms43_roomssoldpickup
		,isnull(MAX(ms44_roomsoldcurrent),0.0) as ms44_roomsoldcurrent,isnull(MAX(ms44_roomssoldpickup),0) as ms44_roomssoldpickup
		,isnull(MAX(ms45_roomsoldcurrent),0.0) as ms45_roomsoldcurrent,isnull(MAX(ms45_roomssoldpickup),0) as ms45_roomssoldpickup
		,isnull(MAX(ms46_roomsoldcurrent),0.0) as ms46_roomsoldcurrent,isnull(MAX(ms46_roomssoldpickup),0) as ms46_roomssoldpickup
		,isnull(MAX(ms47_roomsoldcurrent),0.0) as ms47_roomsoldcurrent,isnull(MAX(ms47_roomssoldpickup),0) as ms47_roomssoldpickup
		,isnull(MAX(ms48_roomsoldcurrent),0.0) as ms48_roomsoldcurrent,isnull(MAX(ms48_roomssoldpickup),0) as ms48_roomssoldpickup
		,isnull(MAX(ms49_roomsoldcurrent),0.0) as ms49_roomsoldcurrent,isnull(MAX(ms49_roomssoldpickup),0) as ms49_roomssoldpickup
		,isnull(MAX(ms50_roomsoldcurrent),0.0) as ms50_roomsoldcurrent,isnull(MAX(ms50_roomssoldpickup),0) as ms50_roomssoldpickup
		
		,isnull(MAX(ms1_occfcstcurrent),0.0) as ms1_occfcstcurrent,isnull(MAX(ms1_occfcstpickup),0.0) as ms1_occfcstpickup
		,isnull(MAX(ms2_occfcstcurrent),0.0) as ms2_occfcstcurrent,isnull(MAX(ms2_occfcstpickup),0.0) as ms2_occfcstpickup
		,isnull(MAX(ms3_occfcstcurrent),0.0) as ms3_occfcstcurrent,isnull(MAX(ms3_occfcstpickup),0.0) as ms3_occfcstpickup
		,isnull(MAX(ms4_occfcstcurrent),0.0) as ms4_occfcstcurrent,isnull(MAX(ms4_occfcstpickup),0.0) as ms4_occfcstpickup
		,isnull(MAX(ms5_occfcstcurrent),0.0) as ms5_occfcstcurrent,isnull(MAX(ms5_occfcstpickup),0.0) as ms5_occfcstpickup
		,isnull(MAX(ms6_occfcstcurrent),0.0) as ms6_occfcstcurrent,isnull(MAX(ms6_occfcstpickup),0.0) as ms6_occfcstpickup
		,isnull(MAX(ms7_occfcstcurrent),0.0) as ms7_occfcstcurrent,isnull(MAX(ms7_occfcstpickup),0.0) as ms7_occfcstpickup
		,isnull(MAX(ms8_occfcstcurrent),0.0) as ms8_occfcstcurrent,isnull(MAX(ms8_occfcstpickup),0.0) as ms8_occfcstpickup
		,isnull(MAX(ms9_occfcstcurrent),0.0) as ms9_occfcstcurrent,isnull(MAX(ms9_occfcstpickup),0.0) as ms9_occfcstpickup
		,isnull(MAX(ms10_occfcstcurrent),0.0) as ms10_occfcstcurrent,isnull(MAX(ms10_occfcstpickup),0.0) as ms10_occfcstpickup
		,isnull(MAX(ms11_occfcstcurrent),0.0) as ms11_occfcstcurrent,isnull(MAX(ms11_occfcstpickup),0.0) as ms11_occfcstpickup
		,isnull(MAX(ms12_occfcstcurrent),0.0) as ms12_occfcstcurrent,isnull(MAX(ms12_occfcstpickup),0.0) as ms12_occfcstpickup
		,isnull(MAX(ms13_occfcstcurrent),0.0) as ms13_occfcstcurrent,isnull(MAX(ms13_occfcstpickup),0.0) as ms13_occfcstpickup
		,isnull(MAX(ms14_occfcstcurrent),0.0) as ms14_occfcstcurrent,isnull(MAX(ms14_occfcstpickup),0.0) as ms14_occfcstpickup
		,isnull(MAX(ms15_occfcstcurrent),0.0) as ms15_occfcstcurrent,isnull(MAX(ms15_occfcstpickup),0.0) as ms15_occfcstpickup
		,isnull(MAX(ms16_occfcstcurrent),0.0) as ms16_occfcstcurrent,isnull(MAX(ms16_occfcstpickup),0.0) as ms16_occfcstpickup
		,isnull(MAX(ms17_occfcstcurrent),0.0) as ms17_occfcstcurrent,isnull(MAX(ms17_occfcstpickup),0.0) as ms17_occfcstpickup
		,isnull(MAX(ms18_occfcstcurrent),0.0) as ms18_occfcstcurrent,isnull(MAX(ms18_occfcstpickup),0.0) as ms18_occfcstpickup
		,isnull(MAX(ms19_occfcstcurrent),0.0) as ms19_occfcstcurrent,isnull(MAX(ms19_occfcstpickup),0.0) as ms19_occfcstpickup
		,isnull(MAX(ms20_occfcstcurrent),0.0) as ms20_occfcstcurrent,isnull(MAX(ms20_occfcstpickup),0.0) as ms20_occfcstpickup
		,isnull(MAX(ms21_occfcstcurrent),0.0) as ms21_occfcstcurrent,isnull(MAX(ms21_occfcstpickup),0.0) as ms21_occfcstpickup
		,isnull(MAX(ms22_occfcstcurrent),0.0) as ms22_occfcstcurrent,isnull(MAX(ms22_occfcstpickup),0.0) as ms22_occfcstpickup
		,isnull(MAX(ms23_occfcstcurrent),0.0) as ms23_occfcstcurrent,isnull(MAX(ms23_occfcstpickup),0.0) as ms23_occfcstpickup
		,isnull(MAX(ms24_occfcstcurrent),0.0) as ms24_occfcstcurrent,isnull(MAX(ms24_occfcstpickup),0.0) as ms24_occfcstpickup
		,isnull(MAX(ms25_occfcstcurrent),0.0) as ms25_occfcstcurrent,isnull(MAX(ms25_occfcstpickup),0.0) as ms25_occfcstpickup
		,isnull(MAX(ms26_occfcstcurrent),0.0) as ms26_occfcstcurrent,isnull(MAX(ms26_occfcstpickup),0.0) as ms26_occfcstpickup
		,isnull(MAX(ms27_occfcstcurrent),0.0) as ms27_occfcstcurrent,isnull(MAX(ms27_occfcstpickup),0.0) as ms27_occfcstpickup
		,isnull(MAX(ms28_occfcstcurrent),0.0) as ms28_occfcstcurrent,isnull(MAX(ms28_occfcstpickup),0.0) as ms28_occfcstpickup
		,isnull(MAX(ms29_occfcstcurrent),0.0) as ms29_occfcstcurrent,isnull(MAX(ms29_occfcstpickup),0.0) as ms29_occfcstpickup
		,isnull(MAX(ms30_occfcstcurrent),0.0) as ms30_occfcstcurrent,isnull(MAX(ms30_occfcstpickup),0.0) as ms30_occfcstpickup
		,isnull(MAX(ms31_occfcstcurrent),0.0) as ms31_occfcstcurrent,isnull(MAX(ms31_occfcstpickup),0.0) as ms31_occfcstpickup
		,isnull(MAX(ms32_occfcstcurrent),0.0) as ms32_occfcstcurrent,isnull(MAX(ms32_occfcstpickup),0.0) as ms32_occfcstpickup
		,isnull(MAX(ms33_occfcstcurrent),0.0) as ms33_occfcstcurrent,isnull(MAX(ms33_occfcstpickup),0.0) as ms33_occfcstpickup
		,isnull(MAX(ms34_occfcstcurrent),0.0) as ms34_occfcstcurrent,isnull(MAX(ms34_occfcstpickup),0.0) as ms34_occfcstpickup
		,isnull(MAX(ms35_occfcstcurrent),0.0) as ms35_occfcstcurrent,isnull(MAX(ms35_occfcstpickup),0.0) as ms35_occfcstpickup
		,isnull(MAX(ms36_occfcstcurrent),0.0) as ms36_occfcstcurrent,isnull(MAX(ms36_occfcstpickup),0.0) as ms36_occfcstpickup
		,isnull(MAX(ms37_occfcstcurrent),0.0) as ms37_occfcstcurrent,isnull(MAX(ms37_occfcstpickup),0.0) as ms37_occfcstpickup
		,isnull(MAX(ms38_occfcstcurrent),0.0) as ms38_occfcstcurrent,isnull(MAX(ms38_occfcstpickup),0.0) as ms38_occfcstpickup
		,isnull(MAX(ms39_occfcstcurrent),0.0) as ms39_occfcstcurrent,isnull(MAX(ms39_occfcstpickup),0.0) as ms39_occfcstpickup
		,isnull(MAX(ms40_occfcstcurrent),0.0) as ms40_occfcstcurrent,isnull(MAX(ms40_occfcstpickup),0.0) as ms40_occfcstpickup
		,isnull(MAX(ms41_occfcstcurrent),0.0) as ms41_occfcstcurrent,isnull(MAX(ms41_occfcstpickup),0.0) as ms41_occfcstpickup
		,isnull(MAX(ms42_occfcstcurrent),0.0) as ms42_occfcstcurrent,isnull(MAX(ms42_occfcstpickup),0.0) as ms42_occfcstpickup
		,isnull(MAX(ms43_occfcstcurrent),0.0) as ms43_occfcstcurrent,isnull(MAX(ms43_occfcstpickup),0.0) as ms43_occfcstpickup
		,isnull(MAX(ms44_occfcstcurrent),0.0) as ms44_occfcstcurrent,isnull(MAX(ms44_occfcstpickup),0.0) as ms44_occfcstpickup
		,isnull(MAX(ms45_occfcstcurrent),0.0) as ms45_occfcstcurrent,isnull(MAX(ms45_occfcstpickup),0.0) as ms45_occfcstpickup
		,isnull(MAX(ms46_occfcstcurrent),0.0) as ms46_occfcstcurrent,isnull(MAX(ms46_occfcstpickup),0.0) as ms46_occfcstpickup
		,isnull(MAX(ms47_occfcstcurrent),0.0) as ms47_occfcstcurrent,isnull(MAX(ms47_occfcstpickup),0.0) as ms47_occfcstpickup
		,isnull(MAX(ms48_occfcstcurrent),0.0) as ms48_occfcstcurrent,isnull(MAX(ms48_occfcstpickup),0.0) as ms48_occfcstpickup
		,isnull(MAX(ms49_occfcstcurrent),0.0) as ms49_occfcstcurrent,isnull(MAX(ms49_occfcstpickup),0.0) as ms49_occfcstpickup
		,isnull(MAX(ms50_occfcstcurrent),0.0) as ms50_occfcstcurrent,isnull(MAX(ms50_occfcstpickup),0.0) as ms50_occfcstpickup
		
		,isnull(MAX(ms1_bookedroomrevenuecurrent),0.0) as ms1_bookedroomrevenuecurrent,isnull(MAX(ms1_bookedroomrevenuepickup),0.0) as ms1_bookedroomrevenuepickup
		,isnull(MAX(ms2_bookedroomrevenuecurrent),0.0) as ms2_bookedroomrevenuecurrent,isnull(MAX(ms2_bookedroomrevenuepickup),0.0) as ms2_bookedroomrevenuepickup
		,isnull(MAX(ms3_bookedroomrevenuecurrent),0.0) as ms3_bookedroomrevenuecurrent,isnull(MAX(ms3_bookedroomrevenuepickup),0.0) as ms3_bookedroomrevenuepickup
		,isnull(MAX(ms4_bookedroomrevenuecurrent),0.0) as ms4_bookedroomrevenuecurrent,isnull(MAX(ms4_bookedroomrevenuepickup),0.0) as ms4_bookedroomrevenuepickup
		,isnull(MAX(ms5_bookedroomrevenuecurrent),0.0) as ms5_bookedroomrevenuecurrent,isnull(MAX(ms5_bookedroomrevenuepickup),0.0) as ms5_bookedroomrevenuepickup
		,isnull(MAX(ms6_bookedroomrevenuecurrent),0.0) as ms6_bookedroomrevenuecurrent,isnull(MAX(ms6_bookedroomrevenuepickup),0.0) as ms6_bookedroomrevenuepickup
		,isnull(MAX(ms7_bookedroomrevenuecurrent),0.0) as ms7_bookedroomrevenuecurrent,isnull(MAX(ms7_bookedroomrevenuepickup),0.0) as ms7_bookedroomrevenuepickup
		,isnull(MAX(ms8_bookedroomrevenuecurrent),0.0) as ms8_bookedroomrevenuecurrent,isnull(MAX(ms8_bookedroomrevenuepickup),0.0) as ms8_bookedroomrevenuepickup
		,isnull(MAX(ms9_bookedroomrevenuecurrent),0.0) as ms9_bookedroomrevenuecurrent,isnull(MAX(ms9_bookedroomrevenuepickup),0.0) as ms9_bookedroomrevenuepickup
		,isnull(MAX(ms10_bookedroomrevenuecurrent),0.0) as ms10_bookedroomrevenuecurrent,isnull(MAX(ms10_bookedroomrevenuepickup),0.0) as ms10_bookedroomrevenuepickup
		,isnull(MAX(ms11_bookedroomrevenuecurrent),0.0) as ms11_bookedroomrevenuecurrent,isnull(MAX(ms11_bookedroomrevenuepickup),0.0) as ms11_bookedroomrevenuepickup
		,isnull(MAX(ms12_bookedroomrevenuecurrent),0.0) as ms12_bookedroomrevenuecurrent,isnull(MAX(ms12_bookedroomrevenuepickup),0.0) as ms12_bookedroomrevenuepickup
		,isnull(MAX(ms13_bookedroomrevenuecurrent),0.0) as ms13_bookedroomrevenuecurrent,isnull(MAX(ms13_bookedroomrevenuepickup),0.0) as ms13_bookedroomrevenuepickup
		,isnull(MAX(ms14_bookedroomrevenuecurrent),0.0) as ms14_bookedroomrevenuecurrent,isnull(MAX(ms14_bookedroomrevenuepickup),0.0) as ms14_bookedroomrevenuepickup
		,isnull(MAX(ms15_bookedroomrevenuecurrent),0.0) as ms15_bookedroomrevenuecurrent,isnull(MAX(ms15_bookedroomrevenuepickup),0.0) as ms15_bookedroomrevenuepickup
		,isnull(MAX(ms16_bookedroomrevenuecurrent),0.0) as ms16_bookedroomrevenuecurrent,isnull(MAX(ms16_bookedroomrevenuepickup),0.0) as ms16_bookedroomrevenuepickup
		,isnull(MAX(ms17_bookedroomrevenuecurrent),0.0) as ms17_bookedroomrevenuecurrent,isnull(MAX(ms17_bookedroomrevenuepickup),0.0) as ms17_bookedroomrevenuepickup
		,isnull(MAX(ms18_bookedroomrevenuecurrent),0.0) as ms18_bookedroomrevenuecurrent,isnull(MAX(ms18_bookedroomrevenuepickup),0.0) as ms18_bookedroomrevenuepickup
		,isnull(MAX(ms19_bookedroomrevenuecurrent),0.0) as ms19_bookedroomrevenuecurrent,isnull(MAX(ms19_bookedroomrevenuepickup),0.0) as ms19_bookedroomrevenuepickup
		,isnull(MAX(ms20_bookedroomrevenuecurrent),0.0) as ms20_bookedroomrevenuecurrent,isnull(MAX(ms20_bookedroomrevenuepickup),0.0) as ms20_bookedroomrevenuepickup
		,isnull(MAX(ms21_bookedroomrevenuecurrent),0.0) as ms21_bookedroomrevenuecurrent,isnull(MAX(ms21_bookedroomrevenuepickup),0.0) as ms21_bookedroomrevenuepickup
		,isnull(MAX(ms22_bookedroomrevenuecurrent),0.0) as ms22_bookedroomrevenuecurrent,isnull(MAX(ms22_bookedroomrevenuepickup),0.0) as ms22_bookedroomrevenuepickup
		,isnull(MAX(ms23_bookedroomrevenuecurrent),0.0) as ms23_bookedroomrevenuecurrent,isnull(MAX(ms23_bookedroomrevenuepickup),0.0) as ms23_bookedroomrevenuepickup
		,isnull(MAX(ms24_bookedroomrevenuecurrent),0.0) as ms24_bookedroomrevenuecurrent,isnull(MAX(ms24_bookedroomrevenuepickup),0.0) as ms24_bookedroomrevenuepickup
		,isnull(MAX(ms25_bookedroomrevenuecurrent),0.0) as ms25_bookedroomrevenuecurrent,isnull(MAX(ms25_bookedroomrevenuepickup),0.0) as ms25_bookedroomrevenuepickup
		,isnull(MAX(ms26_bookedroomrevenuecurrent),0.0) as ms26_bookedroomrevenuecurrent,isnull(MAX(ms26_bookedroomrevenuepickup),0.0) as ms26_bookedroomrevenuepickup
		,isnull(MAX(ms27_bookedroomrevenuecurrent),0.0) as ms27_bookedroomrevenuecurrent,isnull(MAX(ms27_bookedroomrevenuepickup),0.0) as ms27_bookedroomrevenuepickup
		,isnull(MAX(ms28_bookedroomrevenuecurrent),0.0) as ms28_bookedroomrevenuecurrent,isnull(MAX(ms28_bookedroomrevenuepickup),0.0) as ms28_bookedroomrevenuepickup
		,isnull(MAX(ms29_bookedroomrevenuecurrent),0.0) as ms29_bookedroomrevenuecurrent,isnull(MAX(ms29_bookedroomrevenuepickup),0.0) as ms29_bookedroomrevenuepickup
		,isnull(MAX(ms30_bookedroomrevenuecurrent),0.0) as ms30_bookedroomrevenuecurrent,isnull(MAX(ms30_bookedroomrevenuepickup),0.0) as ms30_bookedroomrevenuepickup
		,isnull(MAX(ms31_bookedroomrevenuecurrent),0.0) as ms31_bookedroomrevenuecurrent,isnull(MAX(ms31_bookedroomrevenuepickup),0.0) as ms31_bookedroomrevenuepickup
		,isnull(MAX(ms32_bookedroomrevenuecurrent),0.0) as ms32_bookedroomrevenuecurrent,isnull(MAX(ms32_bookedroomrevenuepickup),0.0) as ms32_bookedroomrevenuepickup
		,isnull(MAX(ms33_bookedroomrevenuecurrent),0.0) as ms33_bookedroomrevenuecurrent,isnull(MAX(ms33_bookedroomrevenuepickup),0.0) as ms33_bookedroomrevenuepickup
		,isnull(MAX(ms34_bookedroomrevenuecurrent),0.0) as ms34_bookedroomrevenuecurrent,isnull(MAX(ms34_bookedroomrevenuepickup),0.0) as ms34_bookedroomrevenuepickup
		,isnull(MAX(ms35_bookedroomrevenuecurrent),0.0) as ms35_bookedroomrevenuecurrent,isnull(MAX(ms35_bookedroomrevenuepickup),0.0) as ms35_bookedroomrevenuepickup
		,isnull(MAX(ms36_bookedroomrevenuecurrent),0.0) as ms36_bookedroomrevenuecurrent,isnull(MAX(ms36_bookedroomrevenuepickup),0.0) as ms36_bookedroomrevenuepickup
		,isnull(MAX(ms37_bookedroomrevenuecurrent),0.0) as ms37_bookedroomrevenuecurrent,isnull(MAX(ms37_bookedroomrevenuepickup),0.0) as ms37_bookedroomrevenuepickup
		,isnull(MAX(ms38_bookedroomrevenuecurrent),0.0) as ms38_bookedroomrevenuecurrent,isnull(MAX(ms38_bookedroomrevenuepickup),0.0) as ms38_bookedroomrevenuepickup
		,isnull(MAX(ms39_bookedroomrevenuecurrent),0.0) as ms39_bookedroomrevenuecurrent,isnull(MAX(ms39_bookedroomrevenuepickup),0.0) as ms39_bookedroomrevenuepickup
		,isnull(MAX(ms40_bookedroomrevenuecurrent),0.0) as ms40_bookedroomrevenuecurrent,isnull(MAX(ms40_bookedroomrevenuepickup),0.0) as ms40_bookedroomrevenuepickup
		,isnull(MAX(ms41_bookedroomrevenuecurrent),0.0) as ms41_bookedroomrevenuecurrent,isnull(MAX(ms41_bookedroomrevenuepickup),0.0) as ms41_bookedroomrevenuepickup
		,isnull(MAX(ms42_bookedroomrevenuecurrent),0.0) as ms42_bookedroomrevenuecurrent,isnull(MAX(ms42_bookedroomrevenuepickup),0.0) as ms42_bookedroomrevenuepickup
		,isnull(MAX(ms43_bookedroomrevenuecurrent),0.0) as ms43_bookedroomrevenuecurrent,isnull(MAX(ms43_bookedroomrevenuepickup),0.0) as ms43_bookedroomrevenuepickup
		,isnull(MAX(ms44_bookedroomrevenuecurrent),0.0) as ms44_bookedroomrevenuecurrent,isnull(MAX(ms44_bookedroomrevenuepickup),0.0) as ms44_bookedroomrevenuepickup
		,isnull(MAX(ms45_bookedroomrevenuecurrent),0.0) as ms45_bookedroomrevenuecurrent,isnull(MAX(ms45_bookedroomrevenuepickup),0.0) as ms45_bookedroomrevenuepickup
		,isnull(MAX(ms46_bookedroomrevenuecurrent),0.0) as ms46_bookedroomrevenuecurrent,isnull(MAX(ms46_bookedroomrevenuepickup),0.0) as ms46_bookedroomrevenuepickup
		,isnull(MAX(ms47_bookedroomrevenuecurrent),0.0) as ms47_bookedroomrevenuecurrent,isnull(MAX(ms47_bookedroomrevenuepickup),0.0) as ms47_bookedroomrevenuepickup
		,isnull(MAX(ms48_bookedroomrevenuecurrent),0.0) as ms48_bookedroomrevenuecurrent,isnull(MAX(ms48_bookedroomrevenuepickup),0.0) as ms48_bookedroomrevenuepickup
		,isnull(MAX(ms49_bookedroomrevenuecurrent),0.0) as ms49_bookedroomrevenuecurrent,isnull(MAX(ms49_bookedroomrevenuepickup),0.0) as ms49_bookedroomrevenuepickup
		,isnull(MAX(ms50_bookedroomrevenuecurrent),0.0) as ms50_bookedroomrevenuecurrent,isnull(MAX(ms50_bookedroomrevenuepickup),0.0) as ms50_bookedroomrevenuepickup
		
		
		,isnull(MAX(ms1_fcstedroomrevenuecurrent),0.0) as ms1_fcstedroomrevenuecurrent,isnull(MAX(ms1_fcstedroomrevenuepickup),0.0) as ms1_fcstedroomrevenuepickup
		,isnull(MAX(ms2_fcstedroomrevenuecurrent),0.0) as ms2_fcstedroomrevenuecurrent,isnull(MAX(ms2_fcstedroomrevenuepickup),0.0) as ms2_fcstedroomrevenuepickup
		,isnull(MAX(ms3_fcstedroomrevenuecurrent),0.0) as ms3_fcstedroomrevenuecurrent,isnull(MAX(ms3_fcstedroomrevenuepickup),0.0) as ms3_fcstedroomrevenuepickup
		,isnull(MAX(ms4_fcstedroomrevenuecurrent),0.0) as ms4_fcstedroomrevenuecurrent,isnull(MAX(ms4_fcstedroomrevenuepickup),0.0) as ms4_fcstedroomrevenuepickup
		,isnull(MAX(ms5_fcstedroomrevenuecurrent),0.0) as ms5_fcstedroomrevenuecurrent,isnull(MAX(ms5_fcstedroomrevenuepickup),0.0) as ms5_fcstedroomrevenuepickup
		,isnull(MAX(ms6_fcstedroomrevenuecurrent),0.0) as ms6_fcstedroomrevenuecurrent,isnull(MAX(ms6_fcstedroomrevenuepickup),0.0) as ms6_fcstedroomrevenuepickup
		,isnull(MAX(ms7_fcstedroomrevenuecurrent),0.0) as ms7_fcstedroomrevenuecurrent,isnull(MAX(ms7_fcstedroomrevenuepickup),0.0) as ms7_fcstedroomrevenuepickup
		,isnull(MAX(ms8_fcstedroomrevenuecurrent),0.0) as ms8_fcstedroomrevenuecurrent,isnull(MAX(ms8_fcstedroomrevenuepickup),0.0) as ms8_fcstedroomrevenuepickup
		,isnull(MAX(ms9_fcstedroomrevenuecurrent),0.0) as ms9_fcstedroomrevenuecurrent,isnull(MAX(ms9_fcstedroomrevenuepickup),0.0) as ms9_fcstedroomrevenuepickup
		,isnull(MAX(ms10_fcstedroomrevenuecurrent),0.0) as ms10_fcstedroomrevenuecurrent,isnull(MAX(ms10_fcstedroomrevenuepickup),0.0) as ms10_fcstedroomrevenuepickup
		,isnull(MAX(ms11_fcstedroomrevenuecurrent),0.0) as ms11_fcstedroomrevenuecurrent,isnull(MAX(ms11_fcstedroomrevenuepickup),0.0) as ms11_fcstedroomrevenuepickup
		,isnull(MAX(ms12_fcstedroomrevenuecurrent),0.0) as ms12_fcstedroomrevenuecurrent,isnull(MAX(ms12_fcstedroomrevenuepickup),0.0) as ms12_fcstedroomrevenuepickup
		,isnull(MAX(ms13_fcstedroomrevenuecurrent),0.0) as ms13_fcstedroomrevenuecurrent,isnull(MAX(ms13_fcstedroomrevenuepickup),0.0) as ms13_fcstedroomrevenuepickup
		,isnull(MAX(ms14_fcstedroomrevenuecurrent),0.0) as ms14_fcstedroomrevenuecurrent,isnull(MAX(ms14_fcstedroomrevenuepickup),0.0) as ms14_fcstedroomrevenuepickup
		,isnull(MAX(ms15_fcstedroomrevenuecurrent),0.0) as ms15_fcstedroomrevenuecurrent,isnull(MAX(ms15_fcstedroomrevenuepickup),0.0) as ms15_fcstedroomrevenuepickup
		,isnull(MAX(ms16_fcstedroomrevenuecurrent),0.0) as ms16_fcstedroomrevenuecurrent,isnull(MAX(ms16_fcstedroomrevenuepickup),0.0) as ms16_fcstedroomrevenuepickup
		,isnull(MAX(ms17_fcstedroomrevenuecurrent),0.0) as ms17_fcstedroomrevenuecurrent,isnull(MAX(ms17_fcstedroomrevenuepickup),0.0) as ms17_fcstedroomrevenuepickup
		,isnull(MAX(ms18_fcstedroomrevenuecurrent),0.0) as ms18_fcstedroomrevenuecurrent,isnull(MAX(ms18_fcstedroomrevenuepickup),0.0) as ms18_fcstedroomrevenuepickup
		,isnull(MAX(ms19_fcstedroomrevenuecurrent),0.0) as ms19_fcstedroomrevenuecurrent,isnull(MAX(ms19_fcstedroomrevenuepickup),0.0) as ms19_fcstedroomrevenuepickup
		,isnull(MAX(ms20_fcstedroomrevenuecurrent),0.0) as ms20_fcstedroomrevenuecurrent,isnull(MAX(ms20_fcstedroomrevenuepickup),0.0) as ms20_fcstedroomrevenuepickup
		,isnull(MAX(ms21_fcstedroomrevenuecurrent),0.0) as ms21_fcstedroomrevenuecurrent,isnull(MAX(ms21_fcstedroomrevenuepickup),0.0) as ms21_fcstedroomrevenuepickup
		,isnull(MAX(ms22_fcstedroomrevenuecurrent),0.0) as ms22_fcstedroomrevenuecurrent,isnull(MAX(ms22_fcstedroomrevenuepickup),0.0) as ms22_fcstedroomrevenuepickup
		,isnull(MAX(ms23_fcstedroomrevenuecurrent),0.0) as ms23_fcstedroomrevenuecurrent,isnull(MAX(ms23_fcstedroomrevenuepickup),0.0) as ms23_fcstedroomrevenuepickup
		,isnull(MAX(ms24_fcstedroomrevenuecurrent),0.0) as ms24_fcstedroomrevenuecurrent,isnull(MAX(ms24_fcstedroomrevenuepickup),0.0) as ms24_fcstedroomrevenuepickup
		,isnull(MAX(ms25_fcstedroomrevenuecurrent),0.0) as ms25_fcstedroomrevenuecurrent,isnull(MAX(ms25_fcstedroomrevenuepickup),0.0) as ms25_fcstedroomrevenuepickup
		,isnull(MAX(ms26_fcstedroomrevenuecurrent),0.0) as ms26_fcstedroomrevenuecurrent,isnull(MAX(ms26_fcstedroomrevenuepickup),0.0) as ms26_fcstedroomrevenuepickup
		,isnull(MAX(ms27_fcstedroomrevenuecurrent),0.0) as ms27_fcstedroomrevenuecurrent,isnull(MAX(ms27_fcstedroomrevenuepickup),0.0) as ms27_fcstedroomrevenuepickup
		,isnull(MAX(ms28_fcstedroomrevenuecurrent),0.0) as ms28_fcstedroomrevenuecurrent,isnull(MAX(ms28_fcstedroomrevenuepickup),0.0) as ms28_fcstedroomrevenuepickup
		,isnull(MAX(ms29_fcstedroomrevenuecurrent),0.0) as ms29_fcstedroomrevenuecurrent,isnull(MAX(ms29_fcstedroomrevenuepickup),0.0) as ms29_fcstedroomrevenuepickup
		,isnull(MAX(ms30_fcstedroomrevenuecurrent),0.0) as ms30_fcstedroomrevenuecurrent,isnull(MAX(ms30_fcstedroomrevenuepickup),0.0) as ms30_fcstedroomrevenuepickup
		,isnull(MAX(ms31_fcstedroomrevenuecurrent),0.0) as ms31_fcstedroomrevenuecurrent,isnull(MAX(ms31_fcstedroomrevenuepickup),0.0) as ms31_fcstedroomrevenuepickup
		,isnull(MAX(ms32_fcstedroomrevenuecurrent),0.0) as ms32_fcstedroomrevenuecurrent,isnull(MAX(ms32_fcstedroomrevenuepickup),0.0) as ms32_fcstedroomrevenuepickup
		,isnull(MAX(ms33_fcstedroomrevenuecurrent),0.0) as ms33_fcstedroomrevenuecurrent,isnull(MAX(ms33_fcstedroomrevenuepickup),0.0) as ms33_fcstedroomrevenuepickup
		,isnull(MAX(ms34_fcstedroomrevenuecurrent),0.0) as ms34_fcstedroomrevenuecurrent,isnull(MAX(ms34_fcstedroomrevenuepickup),0.0) as ms34_fcstedroomrevenuepickup
		,isnull(MAX(ms35_fcstedroomrevenuecurrent),0.0) as ms35_fcstedroomrevenuecurrent,isnull(MAX(ms35_fcstedroomrevenuepickup),0.0) as ms35_fcstedroomrevenuepickup
		,isnull(MAX(ms36_fcstedroomrevenuecurrent),0.0) as ms36_fcstedroomrevenuecurrent,isnull(MAX(ms36_fcstedroomrevenuepickup),0.0) as ms36_fcstedroomrevenuepickup
		,isnull(MAX(ms37_fcstedroomrevenuecurrent),0.0) as ms37_fcstedroomrevenuecurrent,isnull(MAX(ms37_fcstedroomrevenuepickup),0.0) as ms37_fcstedroomrevenuepickup
		,isnull(MAX(ms38_fcstedroomrevenuecurrent),0.0) as ms38_fcstedroomrevenuecurrent,isnull(MAX(ms38_fcstedroomrevenuepickup),0.0) as ms38_fcstedroomrevenuepickup
		,isnull(MAX(ms39_fcstedroomrevenuecurrent),0.0) as ms39_fcstedroomrevenuecurrent,isnull(MAX(ms39_fcstedroomrevenuepickup),0.0) as ms39_fcstedroomrevenuepickup
		,isnull(MAX(ms40_fcstedroomrevenuecurrent),0.0) as ms40_fcstedroomrevenuecurrent,isnull(MAX(ms40_fcstedroomrevenuepickup),0.0) as ms40_fcstedroomrevenuepickup
		,isnull(MAX(ms41_fcstedroomrevenuecurrent),0.0) as ms41_fcstedroomrevenuecurrent,isnull(MAX(ms41_fcstedroomrevenuepickup),0.0) as ms41_fcstedroomrevenuepickup
		,isnull(MAX(ms42_fcstedroomrevenuecurrent),0.0) as ms42_fcstedroomrevenuecurrent,isnull(MAX(ms42_fcstedroomrevenuepickup),0.0) as ms42_fcstedroomrevenuepickup
		,isnull(MAX(ms43_fcstedroomrevenuecurrent),0.0) as ms43_fcstedroomrevenuecurrent,isnull(MAX(ms43_fcstedroomrevenuepickup),0.0) as ms43_fcstedroomrevenuepickup
		,isnull(MAX(ms44_fcstedroomrevenuecurrent),0.0) as ms44_fcstedroomrevenuecurrent,isnull(MAX(ms44_fcstedroomrevenuepickup),0.0) as ms44_fcstedroomrevenuepickup
		,isnull(MAX(ms45_fcstedroomrevenuecurrent),0.0) as ms45_fcstedroomrevenuecurrent,isnull(MAX(ms45_fcstedroomrevenuepickup),0.0) as ms45_fcstedroomrevenuepickup
		,isnull(MAX(ms46_fcstedroomrevenuecurrent),0.0) as ms46_fcstedroomrevenuecurrent,isnull(MAX(ms46_fcstedroomrevenuepickup),0.0) as ms46_fcstedroomrevenuepickup
		,isnull(MAX(ms47_fcstedroomrevenuecurrent),0.0) as ms47_fcstedroomrevenuecurrent,isnull(MAX(ms47_fcstedroomrevenuepickup),0.0) as ms47_fcstedroomrevenuepickup
		,isnull(MAX(ms48_fcstedroomrevenuecurrent),0.0) as ms48_fcstedroomrevenuecurrent,isnull(MAX(ms48_fcstedroomrevenuepickup),0.0) as ms48_fcstedroomrevenuepickup
		,isnull(MAX(ms49_fcstedroomrevenuecurrent),0.0) as ms49_fcstedroomrevenuecurrent,isnull(MAX(ms49_fcstedroomrevenuepickup),0.0) as ms49_fcstedroomrevenuepickup
		,isnull(MAX(ms50_fcstedroomrevenuecurrent),0.0) as ms50_fcstedroomrevenuecurrent,isnull(MAX(ms50_fcstedroomrevenuepickup),0.0) as ms50_fcstedroomrevenuepickup
		
		,isnull(MAX(ms1_bookedadrcurrent),0.0) as ms1_bookedadrcurrent,isnull(MAX(ms1_bookedadrpickup),0.0) as ms1_bookedadrpickup
		,isnull(MAX(ms2_bookedadrcurrent),0.0) as ms2_bookedadrcurrent,isnull(MAX(ms2_bookedadrpickup),0.0) as ms2_bookedadrpickup
		,isnull(MAX(ms3_bookedadrcurrent),0.0) as ms3_bookedadrcurrent,isnull(MAX(ms3_bookedadrpickup),0.0) as ms3_bookedadrpickup
		,isnull(MAX(ms4_bookedadrcurrent),0.0) as ms4_bookedadrcurrent,isnull(MAX(ms4_bookedadrpickup),0.0) as ms4_bookedadrpickup
		,isnull(MAX(ms5_bookedadrcurrent),0.0) as ms5_bookedadrcurrent,isnull(MAX(ms5_bookedadrpickup),0.0) as ms5_bookedadrpickup
		,isnull(MAX(ms6_bookedadrcurrent),0.0) as ms6_bookedadrcurrent,isnull(MAX(ms6_bookedadrpickup),0.0) as ms6_bookedadrpickup
		,isnull(MAX(ms7_bookedadrcurrent),0.0) as ms7_bookedadrcurrent,isnull(MAX(ms7_bookedadrpickup),0.0) as ms7_bookedadrpickup
		,isnull(MAX(ms8_bookedadrcurrent),0.0) as ms8_bookedadrcurrent,isnull(MAX(ms8_bookedadrpickup),0.0) as ms8_bookedadrpickup
		,isnull(MAX(ms9_bookedadrcurrent),0.0) as ms9_bookedadrcurrent,isnull(MAX(ms9_bookedadrpickup),0.0) as ms9_bookedadrpickup
		,isnull(MAX(ms10_bookedadrcurrent),0.0) as ms10_bookedadrcurrent,isnull(MAX(ms10_bookedadrpickup),0.0) as ms10_bookedadrpickup
		,isnull(MAX(ms11_bookedadrcurrent),0.0) as ms11_bookedadrcurrent,isnull(MAX(ms11_bookedadrpickup),0.0) as ms11_bookedadrpickup
		,isnull(MAX(ms12_bookedadrcurrent),0.0) as ms12_bookedadrcurrent,isnull(MAX(ms12_bookedadrpickup),0.0) as ms12_bookedadrpickup
		,isnull(MAX(ms13_bookedadrcurrent),0.0) as ms13_bookedadrcurrent,isnull(MAX(ms13_bookedadrpickup),0.0) as ms13_bookedadrpickup
		,isnull(MAX(ms14_bookedadrcurrent),0.0) as ms14_bookedadrcurrent,isnull(MAX(ms14_bookedadrpickup),0.0) as ms14_bookedadrpickup
		,isnull(MAX(ms15_bookedadrcurrent),0.0) as ms15_bookedadrcurrent,isnull(MAX(ms15_bookedadrpickup),0.0) as ms15_bookedadrpickup
		,isnull(MAX(ms16_bookedadrcurrent),0.0) as ms16_bookedadrcurrent,isnull(MAX(ms16_bookedadrpickup),0.0) as ms16_bookedadrpickup
		,isnull(MAX(ms17_bookedadrcurrent),0.0) as ms17_bookedadrcurrent,isnull(MAX(ms17_bookedadrpickup),0.0) as ms17_bookedadrpickup
		,isnull(MAX(ms18_bookedadrcurrent),0.0) as ms18_bookedadrcurrent,isnull(MAX(ms18_bookedadrpickup),0.0) as ms18_bookedadrpickup
		,isnull(MAX(ms19_bookedadrcurrent),0.0) as ms19_bookedadrcurrent,isnull(MAX(ms19_bookedadrpickup),0.0) as ms19_bookedadrpickup
		,isnull(MAX(ms20_bookedadrcurrent),0.0) as ms20_bookedadrcurrent,isnull(MAX(ms20_bookedadrpickup),0.0) as ms20_bookedadrpickup
		,isnull(MAX(ms21_bookedadrcurrent),0.0) as ms21_bookedadrcurrent,isnull(MAX(ms21_bookedadrpickup),0.0) as ms21_bookedadrpickup
		,isnull(MAX(ms22_bookedadrcurrent),0.0) as ms22_bookedadrcurrent,isnull(MAX(ms22_bookedadrpickup),0.0) as ms22_bookedadrpickup
		,isnull(MAX(ms23_bookedadrcurrent),0.0) as ms23_bookedadrcurrent,isnull(MAX(ms23_bookedadrpickup),0.0) as ms23_bookedadrpickup
		,isnull(MAX(ms24_bookedadrcurrent),0.0) as ms24_bookedadrcurrent,isnull(MAX(ms24_bookedadrpickup),0.0) as ms24_bookedadrpickup
		,isnull(MAX(ms25_bookedadrcurrent),0.0) as ms25_bookedadrcurrent,isnull(MAX(ms25_bookedadrpickup),0.0) as ms25_bookedadrpickup
		,isnull(MAX(ms26_bookedadrcurrent),0.0) as ms26_bookedadrcurrent,isnull(MAX(ms26_bookedadrpickup),0.0) as ms26_bookedadrpickup
		,isnull(MAX(ms27_bookedadrcurrent),0.0) as ms27_bookedadrcurrent,isnull(MAX(ms27_bookedadrpickup),0.0) as ms27_bookedadrpickup
		,isnull(MAX(ms28_bookedadrcurrent),0.0) as ms28_bookedadrcurrent,isnull(MAX(ms28_bookedadrpickup),0.0) as ms28_bookedadrpickup
		,isnull(MAX(ms29_bookedadrcurrent),0.0) as ms29_bookedadrcurrent,isnull(MAX(ms29_bookedadrpickup),0.0) as ms29_bookedadrpickup
		,isnull(MAX(ms30_bookedadrcurrent),0.0) as ms30_bookedadrcurrent,isnull(MAX(ms30_bookedadrpickup),0.0) as ms30_bookedadrpickup
		,isnull(MAX(ms31_bookedadrcurrent),0.0) as ms31_bookedadrcurrent,isnull(MAX(ms31_bookedadrpickup),0.0) as ms31_bookedadrpickup
		,isnull(MAX(ms32_bookedadrcurrent),0.0) as ms32_bookedadrcurrent,isnull(MAX(ms32_bookedadrpickup),0.0) as ms32_bookedadrpickup
		,isnull(MAX(ms33_bookedadrcurrent),0.0) as ms33_bookedadrcurrent,isnull(MAX(ms33_bookedadrpickup),0.0) as ms33_bookedadrpickup
		,isnull(MAX(ms34_bookedadrcurrent),0.0) as ms34_bookedadrcurrent,isnull(MAX(ms34_bookedadrpickup),0.0) as ms34_bookedadrpickup
		,isnull(MAX(ms35_bookedadrcurrent),0.0) as ms35_bookedadrcurrent,isnull(MAX(ms35_bookedadrpickup),0.0) as ms35_bookedadrpickup
		,isnull(MAX(ms36_bookedadrcurrent),0.0) as ms36_bookedadrcurrent,isnull(MAX(ms36_bookedadrpickup),0.0) as ms36_bookedadrpickup
		,isnull(MAX(ms37_bookedadrcurrent),0.0) as ms37_bookedadrcurrent,isnull(MAX(ms37_bookedadrpickup),0.0) as ms37_bookedadrpickup
		,isnull(MAX(ms38_bookedadrcurrent),0.0) as ms38_bookedadrcurrent,isnull(MAX(ms38_bookedadrpickup),0.0) as ms38_bookedadrpickup
		,isnull(MAX(ms39_bookedadrcurrent),0.0) as ms39_bookedadrcurrent,isnull(MAX(ms39_bookedadrpickup),0.0) as ms39_bookedadrpickup
		,isnull(MAX(ms40_bookedadrcurrent),0.0) as ms40_bookedadrcurrent,isnull(MAX(ms40_bookedadrpickup),0.0) as ms40_bookedadrpickup
		,isnull(MAX(ms41_bookedadrcurrent),0.0) as ms41_bookedadrcurrent,isnull(MAX(ms41_bookedadrpickup),0.0) as ms41_bookedadrpickup
		,isnull(MAX(ms42_bookedadrcurrent),0.0) as ms42_bookedadrcurrent,isnull(MAX(ms42_bookedadrpickup),0.0) as ms42_bookedadrpickup
		,isnull(MAX(ms43_bookedadrcurrent),0.0) as ms43_bookedadrcurrent,isnull(MAX(ms43_bookedadrpickup),0.0) as ms43_bookedadrpickup
		,isnull(MAX(ms44_bookedadrcurrent),0.0) as ms44_bookedadrcurrent,isnull(MAX(ms44_bookedadrpickup),0.0) as ms44_bookedadrpickup
		,isnull(MAX(ms45_bookedadrcurrent),0.0) as ms45_bookedadrcurrent,isnull(MAX(ms45_bookedadrpickup),0.0) as ms45_bookedadrpickup
		,isnull(MAX(ms46_bookedadrcurrent),0.0) as ms46_bookedadrcurrent,isnull(MAX(ms46_bookedadrpickup),0.0) as ms46_bookedadrpickup
		,isnull(MAX(ms47_bookedadrcurrent),0.0) as ms47_bookedadrcurrent,isnull(MAX(ms47_bookedadrpickup),0.0) as ms47_bookedadrpickup
		,isnull(MAX(ms48_bookedadrcurrent),0.0) as ms48_bookedadrcurrent,isnull(MAX(ms48_bookedadrpickup),0.0) as ms48_bookedadrpickup
		,isnull(MAX(ms49_bookedadrcurrent),0.0) as ms49_bookedadrcurrent,isnull(MAX(ms49_bookedadrpickup),0.0) as ms49_bookedadrpickup
		,isnull(MAX(ms50_bookedadrcurrent),0.0) as ms50_bookedadrcurrent,isnull(MAX(ms50_bookedadrpickup),0.0) as ms50_bookedadrpickup
		
		,isnull(MAX(ms1_fcstedadrcurrent),0.0) as ms1_fcstedadrcurrent,isnull(MAX(ms1_fcstedadrpickup),0.0) as ms1_fcstedadrpickup
		,isnull(MAX(ms2_fcstedadrcurrent),0.0) as ms2_fcstedadrcurrent,isnull(MAX(ms2_fcstedadrpickup),0.0) as ms2_fcstedadrpickup
		,isnull(MAX(ms3_fcstedadrcurrent),0.0) as ms3_fcstedadrcurrent,isnull(MAX(ms3_fcstedadrpickup),0.0) as ms3_fcstedadrpickup
		,isnull(MAX(ms4_fcstedadrcurrent),0.0) as ms4_fcstedadrcurrent,isnull(MAX(ms4_fcstedadrpickup),0.0) as ms4_fcstedadrpickup
		,isnull(MAX(ms5_fcstedadrcurrent),0.0) as ms5_fcstedadrcurrent,isnull(MAX(ms5_fcstedadrpickup),0.0) as ms5_fcstedadrpickup
		,isnull(MAX(ms6_fcstedadrcurrent),0.0) as ms6_fcstedadrcurrent,isnull(MAX(ms6_fcstedadrpickup),0.0) as ms6_fcstedadrpickup
		,isnull(MAX(ms7_fcstedadrcurrent),0.0) as ms7_fcstedadrcurrent,isnull(MAX(ms7_fcstedadrpickup),0.0) as ms7_fcstedadrpickup
		,isnull(MAX(ms8_fcstedadrcurrent),0.0) as ms8_fcstedadrcurrent,isnull(MAX(ms8_fcstedadrpickup),0.0) as ms8_fcstedadrpickup
		,isnull(MAX(ms9_fcstedadrcurrent),0.0) as ms9_fcstedadrcurrent,isnull(MAX(ms9_fcstedadrpickup),0.0) as ms9_fcstedadrpickup
		,isnull(MAX(ms10_fcstedadrcurrent),0.0) as ms10_fcstedadrcurrent,isnull(MAX(ms10_fcstedadrpickup),0.0) as ms10_fcstedadrpickup
		,isnull(MAX(ms11_fcstedadrcurrent),0.0) as ms11_fcstedadrcurrent,isnull(MAX(ms11_fcstedadrpickup),0.0) as ms11_fcstedadrpickup
		,isnull(MAX(ms12_fcstedadrcurrent),0.0) as ms12_fcstedadrcurrent,isnull(MAX(ms12_fcstedadrpickup),0.0) as ms12_fcstedadrpickup
		,isnull(MAX(ms13_fcstedadrcurrent),0.0) as ms13_fcstedadrcurrent,isnull(MAX(ms13_fcstedadrpickup),0.0) as ms13_fcstedadrpickup
		,isnull(MAX(ms14_fcstedadrcurrent),0.0) as ms14_fcstedadrcurrent,isnull(MAX(ms14_fcstedadrpickup),0.0) as ms14_fcstedadrpickup
		,isnull(MAX(ms15_fcstedadrcurrent),0.0) as ms15_fcstedadrcurrent,isnull(MAX(ms15_fcstedadrpickup),0.0) as ms15_fcstedadrpickup
		,isnull(MAX(ms16_fcstedadrcurrent),0.0) as ms16_fcstedadrcurrent,isnull(MAX(ms16_fcstedadrpickup),0.0) as ms16_fcstedadrpickup
		,isnull(MAX(ms17_fcstedadrcurrent),0.0) as ms17_fcstedadrcurrent,isnull(MAX(ms17_fcstedadrpickup),0.0) as ms17_fcstedadrpickup
		,isnull(MAX(ms18_fcstedadrcurrent),0.0) as ms18_fcstedadrcurrent,isnull(MAX(ms18_fcstedadrpickup),0.0) as ms18_fcstedadrpickup
		,isnull(MAX(ms19_fcstedadrcurrent),0.0) as ms19_fcstedadrcurrent,isnull(MAX(ms19_fcstedadrpickup),0.0) as ms19_fcstedadrpickup
		,isnull(MAX(ms20_fcstedadrcurrent),0.0) as ms20_fcstedadrcurrent,isnull(MAX(ms20_fcstedadrpickup),0.0) as ms20_fcstedadrpickup
		,isnull(MAX(ms21_fcstedadrcurrent),0.0) as ms21_fcstedadrcurrent,isnull(MAX(ms21_fcstedadrpickup),0.0) as ms21_fcstedadrpickup
		,isnull(MAX(ms22_fcstedadrcurrent),0.0) as ms22_fcstedadrcurrent,isnull(MAX(ms22_fcstedadrpickup),0.0) as ms22_fcstedadrpickup
		,isnull(MAX(ms23_fcstedadrcurrent),0.0) as ms23_fcstedadrcurrent,isnull(MAX(ms23_fcstedadrpickup),0.0) as ms23_fcstedadrpickup
		,isnull(MAX(ms24_fcstedadrcurrent),0.0) as ms24_fcstedadrcurrent,isnull(MAX(ms24_fcstedadrpickup),0.0) as ms24_fcstedadrpickup
		,isnull(MAX(ms25_fcstedadrcurrent),0.0) as ms25_fcstedadrcurrent,isnull(MAX(ms25_fcstedadrpickup),0.0) as ms25_fcstedadrpickup
		,isnull(MAX(ms26_fcstedadrcurrent),0.0) as ms26_fcstedadrcurrent,isnull(MAX(ms26_fcstedadrpickup),0.0) as ms26_fcstedadrpickup
		,isnull(MAX(ms27_fcstedadrcurrent),0.0) as ms27_fcstedadrcurrent,isnull(MAX(ms27_fcstedadrpickup),0.0) as ms27_fcstedadrpickup
		,isnull(MAX(ms28_fcstedadrcurrent),0.0) as ms28_fcstedadrcurrent,isnull(MAX(ms28_fcstedadrpickup),0.0) as ms28_fcstedadrpickup
		,isnull(MAX(ms29_fcstedadrcurrent),0.0) as ms29_fcstedadrcurrent,isnull(MAX(ms29_fcstedadrpickup),0.0) as ms29_fcstedadrpickup
		,isnull(MAX(ms30_fcstedadrcurrent),0.0) as ms30_fcstedadrcurrent,isnull(MAX(ms30_fcstedadrpickup),0.0) as ms30_fcstedadrpickup
		,isnull(MAX(ms31_fcstedadrcurrent),0.0) as ms31_fcstedadrcurrent,isnull(MAX(ms31_fcstedadrpickup),0.0) as ms31_fcstedadrpickup
		,isnull(MAX(ms32_fcstedadrcurrent),0.0) as ms32_fcstedadrcurrent,isnull(MAX(ms32_fcstedadrpickup),0.0) as ms32_fcstedadrpickup
		,isnull(MAX(ms33_fcstedadrcurrent),0.0) as ms33_fcstedadrcurrent,isnull(MAX(ms33_fcstedadrpickup),0.0) as ms33_fcstedadrpickup
		,isnull(MAX(ms34_fcstedadrcurrent),0.0) as ms34_fcstedadrcurrent,isnull(MAX(ms34_fcstedadrpickup),0.0) as ms34_fcstedadrpickup
		,isnull(MAX(ms35_fcstedadrcurrent),0.0) as ms35_fcstedadrcurrent,isnull(MAX(ms35_fcstedadrpickup),0.0) as ms35_fcstedadrpickup
		,isnull(MAX(ms36_fcstedadrcurrent),0.0) as ms36_fcstedadrcurrent,isnull(MAX(ms36_fcstedadrpickup),0.0) as ms36_fcstedadrpickup
		,isnull(MAX(ms37_fcstedadrcurrent),0.0) as ms37_fcstedadrcurrent,isnull(MAX(ms37_fcstedadrpickup),0.0) as ms37_fcstedadrpickup
		,isnull(MAX(ms38_fcstedadrcurrent),0.0) as ms38_fcstedadrcurrent,isnull(MAX(ms38_fcstedadrpickup),0.0) as ms38_fcstedadrpickup
		,isnull(MAX(ms39_fcstedadrcurrent),0.0) as ms39_fcstedadrcurrent,isnull(MAX(ms39_fcstedadrpickup),0.0) as ms39_fcstedadrpickup
		,isnull(MAX(ms40_fcstedadrcurrent),0.0) as ms40_fcstedadrcurrent,isnull(MAX(ms40_fcstedadrpickup),0.0) as ms40_fcstedadrpickup
		,isnull(MAX(ms41_fcstedadrcurrent),0.0) as ms41_fcstedadrcurrent,isnull(MAX(ms41_fcstedadrpickup),0.0) as ms41_fcstedadrpickup
		,isnull(MAX(ms42_fcstedadrcurrent),0.0) as ms42_fcstedadrcurrent,isnull(MAX(ms42_fcstedadrpickup),0.0) as ms42_fcstedadrpickup
		,isnull(MAX(ms43_fcstedadrcurrent),0.0) as ms43_fcstedadrcurrent,isnull(MAX(ms43_fcstedadrpickup),0.0) as ms43_fcstedadrpickup
		,isnull(MAX(ms44_fcstedadrcurrent),0.0) as ms44_fcstedadrcurrent,isnull(MAX(ms44_fcstedadrpickup),0.0) as ms44_fcstedadrpickup
		,isnull(MAX(ms45_fcstedadrcurrent),0.0) as ms45_fcstedadrcurrent,isnull(MAX(ms45_fcstedadrpickup),0.0) as ms45_fcstedadrpickup
		,isnull(MAX(ms46_fcstedadrcurrent),0.0) as ms46_fcstedadrcurrent,isnull(MAX(ms46_fcstedadrpickup),0.0) as ms46_fcstedadrpickup
		,isnull(MAX(ms47_fcstedadrcurrent),0.0) as ms47_fcstedadrcurrent,isnull(MAX(ms47_fcstedadrpickup),0.0) as ms47_fcstedadrpickup
		,isnull(MAX(ms48_fcstedadrcurrent),0.0) as ms48_fcstedadrcurrent,isnull(MAX(ms48_fcstedadrpickup),0.0) as ms48_fcstedadrpickup
		,isnull(MAX(ms49_fcstedadrcurrent),0.0) as ms49_fcstedadrcurrent,isnull(MAX(ms49_fcstedadrpickup),0.0) as ms49_fcstedadrpickup
		,isnull(MAX(ms50_fcstedadrcurrent),0.0) as ms50_fcstedadrcurrent,isnull(MAX(ms50_fcstedadrpickup),0.0) as ms50_fcstedadrpickup
		
		--Archana added for group block-, group pickup-Start
		,isnull(MAX(ms1_block),0) as ms1_block,isnull(MAX(ms1_block_available),0) as ms1_block_available,isnull(MAX(ms1_block_pickup),0) as ms1_block_pickup
		,isnull(MAX(ms2_block),0) as ms2_block,isnull(MAX(ms2_block_available),0) as ms2_block_available,isnull(MAX(ms2_block_pickup),0) as ms2_block_pickup
		,isnull(MAX(ms3_block),0) as ms3_block,isnull(MAX(ms3_block_available),0) as ms3_block_available,isnull(MAX(ms3_block_pickup),0) as ms3_block_pickup
		,isnull(MAX(ms4_block),0) as ms4_block,isnull(MAX(ms4_block_available),0) as ms4_block_available,isnull(MAX(ms4_block_pickup),0) as ms4_block_pickup
		,isnull(MAX(ms5_block),0) as ms5_block,isnull(MAX(ms5_block_available),0) as ms5_block_available,isnull(MAX(ms5_block_pickup),0) as ms5_block_pickup
		,isnull(MAX(ms6_block),0) as ms6_block,isnull(MAX(ms6_block_available),0) as ms6_block_available,isnull(MAX(ms6_block_pickup),0) as ms6_block_pickup
		,isnull(MAX(ms7_block),0) as ms7_block,isnull(MAX(ms7_block_available),0) as ms7_block_available,isnull(MAX(ms7_block_pickup),0) as ms7_block_pickup
		,isnull(MAX(ms8_block),0) as ms8_block,isnull(MAX(ms8_block_available),0) as ms8_block_available,isnull(MAX(ms8_block_pickup),0) as ms8_block_pickup
		,isnull(MAX(ms9_block),0) as ms9_block,isnull(MAX(ms9_block_available),0) as ms9_block_available,isnull(MAX(ms9_block_pickup),0) as ms9_block_pickup
		,isnull(MAX(ms10_block),0) as ms10_block,isnull(MAX(ms10_block_available),0) as ms10_block_available,isnull(MAX(ms10_block_pickup),0) as ms10_block_pickup
		,isnull(MAX(ms11_block),0) as ms11_block,isnull(MAX(ms11_block_available),0) as ms11_block_available,isnull(MAX(ms11_block_pickup),0) as ms11_block_pickup
		,isnull(MAX(ms12_block),0) as ms12_block,isnull(MAX(ms12_block_available),0) as ms12_block_available,isnull(MAX(ms12_block_pickup),0) as ms12_block_pickup
		,isnull(MAX(ms13_block),0) as ms13_block,isnull(MAX(ms13_block_available),0) as ms13_block_available,isnull(MAX(ms13_block_pickup),0) as ms13_block_pickup
		,isnull(MAX(ms14_block),0) as ms14_block,isnull(MAX(ms14_block_available),0) as ms14_block_available,isnull(MAX(ms14_block_pickup),0) as ms14_block_pickup
		,isnull(MAX(ms15_block),0) as ms15_block,isnull(MAX(ms15_block_available),0) as ms15_block_available,isnull(MAX(ms15_block_pickup),0) as ms15_block_pickup
		,isnull(MAX(ms16_block),0) as ms16_block,isnull(MAX(ms16_block_available),0) as ms16_block_available,isnull(MAX(ms16_block_pickup),0) as ms16_block_pickup
		,isnull(MAX(ms17_block),0) as ms17_block,isnull(MAX(ms17_block_available),0) as ms17_block_available,isnull(MAX(ms17_block_pickup),0) as ms17_block_pickup
		,isnull(MAX(ms18_block),0) as ms18_block,isnull(MAX(ms18_block_available),0) as ms18_block_available,isnull(MAX(ms18_block_pickup),0) as ms18_block_pickup
		,isnull(MAX(ms19_block),0) as ms19_block,isnull(MAX(ms19_block_available),0) as ms19_block_available,isnull(MAX(ms19_block_pickup),0) as ms19_block_pickup
		,isnull(MAX(ms20_block),0) as ms20_block,isnull(MAX(ms20_block_available),0) as ms20_block_available,isnull(MAX(ms20_block_pickup),0) as ms20_block_pickup
		,isnull(MAX(ms21_block),0) as ms21_block,isnull(MAX(ms21_block_available),0) as ms21_block_available,isnull(MAX(ms21_block_pickup),0) as ms21_block_pickup
		,isnull(MAX(ms22_block),0) as ms22_block,isnull(MAX(ms22_block_available),0) as ms22_block_available,isnull(MAX(ms22_block_pickup),0) as ms22_block_pickup
		,isnull(MAX(ms23_block),0) as ms23_block,isnull(MAX(ms23_block_available),0) as ms23_block_available,isnull(MAX(ms23_block_pickup),0) as ms23_block_pickup
		,isnull(MAX(ms24_block),0) as ms24_block,isnull(MAX(ms24_block_available),0) as ms24_block_available,isnull(MAX(ms24_block_pickup),0) as ms24_block_pickup
		,isnull(MAX(ms25_block),0) as ms25_block,isnull(MAX(ms25_block_available),0) as ms25_block_available,isnull(MAX(ms25_block_pickup),0) as ms25_block_pickup
		,isnull(MAX(ms26_block),0) as ms26_block,isnull(MAX(ms26_block_available),0) as ms26_block_available,isnull(MAX(ms26_block_pickup),0) as ms26_block_pickup
		,isnull(MAX(ms27_block),0) as ms27_block,isnull(MAX(ms27_block_available),0) as ms27_block_available,isnull(MAX(ms27_block_pickup),0) as ms27_block_pickup
		,isnull(MAX(ms28_block),0) as ms28_block,isnull(MAX(ms28_block_available),0) as ms28_block_available,isnull(MAX(ms28_block_pickup),0) as ms28_block_pickup
		,isnull(MAX(ms29_block),0) as ms29_block,isnull(MAX(ms29_block_available),0) as ms29_block_available,isnull(MAX(ms29_block_pickup),0) as ms29_block_pickup
		,isnull(MAX(ms30_block),0) as ms30_block,isnull(MAX(ms30_block_available),0) as ms30_block_available,isnull(MAX(ms30_block_pickup),0) as ms30_block_pickup
		,isnull(MAX(ms31_block),0) as ms31_block,isnull(MAX(ms31_block_available),0) as ms31_block_available,isnull(MAX(ms31_block_pickup),0) as ms31_block_pickup
		,isnull(MAX(ms32_block),0) as ms32_block,isnull(MAX(ms32_block_available),0) as ms32_block_available,isnull(MAX(ms32_block_pickup),0) as ms32_block_pickup
		,isnull(MAX(ms33_block),0) as ms33_block,isnull(MAX(ms33_block_available),0) as ms33_block_available,isnull(MAX(ms33_block_pickup),0) as ms33_block_pickup
		,isnull(MAX(ms34_block),0) as ms34_block,isnull(MAX(ms34_block_available),0) as ms34_block_available,isnull(MAX(ms34_block_pickup),0) as ms34_block_pickup
		,isnull(MAX(ms35_block),0) as ms35_block,isnull(MAX(ms35_block_available),0) as ms35_block_available,isnull(MAX(ms35_block_pickup),0) as ms35_block_pickup
		,isnull(MAX(ms36_block),0) as ms36_block,isnull(MAX(ms36_block_available),0) as ms36_block_available,isnull(MAX(ms36_block_pickup),0) as ms36_block_pickup
		,isnull(MAX(ms37_block),0) as ms37_block,isnull(MAX(ms37_block_available),0) as ms37_block_available,isnull(MAX(ms37_block_pickup),0) as ms37_block_pickup
		,isnull(MAX(ms38_block),0) as ms38_block,isnull(MAX(ms38_block_available),0) as ms38_block_available,isnull(MAX(ms38_block_pickup),0) as ms38_block_pickup
		,isnull(MAX(ms39_block),0) as ms39_block,isnull(MAX(ms39_block_available),0) as ms39_block_available,isnull(MAX(ms39_block_pickup),0) as ms39_block_pickup
		,isnull(MAX(ms40_block),0) as ms40_block,isnull(MAX(ms40_block_available),0) as ms40_block_available,isnull(MAX(ms40_block_pickup),0) as ms40_block_pickup
		,isnull(MAX(ms41_block),0) as ms41_block,isnull(MAX(ms41_block_available),0) as ms41_block_available,isnull(MAX(ms41_block_pickup),0) as ms41_block_pickup
		,isnull(MAX(ms42_block),0) as ms42_block,isnull(MAX(ms42_block_available),0) as ms42_block_available,isnull(MAX(ms42_block_pickup),0) as ms42_block_pickup
		,isnull(MAX(ms43_block),0) as ms43_block,isnull(MAX(ms43_block_available),0) as ms43_block_available,isnull(MAX(ms43_block_pickup),0) as ms43_block_pickup
		,isnull(MAX(ms44_block),0) as ms44_block,isnull(MAX(ms44_block_available),0) as ms44_block_available,isnull(MAX(ms44_block_pickup),0) as ms44_block_pickup
		,isnull(MAX(ms45_block),0) as ms45_block,isnull(MAX(ms45_block_available),0) as ms45_block_available,isnull(MAX(ms45_block_pickup),0) as ms45_block_pickup
		,isnull(MAX(ms46_block),0) as ms46_block,isnull(MAX(ms46_block_available),0) as ms46_block_available,isnull(MAX(ms46_block_pickup),0) as ms46_block_pickup
		,isnull(MAX(ms47_block),0) as ms47_block,isnull(MAX(ms47_block_available),0) as ms47_block_available,isnull(MAX(ms47_block_pickup),0) as ms47_block_pickup
		,isnull(MAX(ms48_block),0) as ms48_block,isnull(MAX(ms48_block_available),0) as ms48_block_available,isnull(MAX(ms48_block_pickup),0) as ms48_block_pickup
		,isnull(MAX(ms49_block),0) as ms49_block,isnull(MAX(ms49_block_available),0) as ms49_block_available,isnull(MAX(ms49_block_pickup),0) as ms49_block_pickup
		,isnull(MAX(ms50_block),0) as ms50_block,isnull(MAX(ms50_block_available),0) as ms50_block_available,isnull(MAX(ms50_block_pickup),0) as ms50_block_pickup
		--Archana added for group block-, group pickup-End
		
		from
		(
			select 
			occupancy_dt,dow
			,(case mkt_seg_id when @ms1 then roomsoldcurrent end) as ms1_roomsoldcurrent
			,(case mkt_seg_id when @ms1 then roomssoldpickup end) as ms1_roomssoldpickup
			,(case mkt_seg_id when @ms2 then roomsoldcurrent end) as ms2_roomsoldcurrent
			,(case mkt_seg_id when @ms2 then roomssoldpickup end) as ms2_roomssoldpickup
			,(case mkt_seg_id when @ms3 then roomsoldcurrent end) as ms3_roomsoldcurrent
			,(case mkt_seg_id when @ms3 then roomssoldpickup end) as ms3_roomssoldpickup
			,(case mkt_seg_id when @ms4 then roomsoldcurrent end) as ms4_roomsoldcurrent
			,(case mkt_seg_id when @ms4 then roomssoldpickup end) as ms4_roomssoldpickup
			,(case mkt_seg_id when @ms5 then roomsoldcurrent end) as ms5_roomsoldcurrent
			,(case mkt_seg_id when @ms5 then roomssoldpickup end) as ms5_roomssoldpickup
			,(case mkt_seg_id when @ms6 then roomsoldcurrent end) as ms6_roomsoldcurrent
			,(case mkt_seg_id when @ms6 then roomssoldpickup end) as ms6_roomssoldpickup
			,(case mkt_seg_id when @ms7 then roomsoldcurrent end) as ms7_roomsoldcurrent
			,(case mkt_seg_id when @ms7 then roomssoldpickup end) as ms7_roomssoldpickup
			,(case mkt_seg_id when @ms8 then roomsoldcurrent end) as ms8_roomsoldcurrent
			,(case mkt_seg_id when @ms8 then roomssoldpickup end) as ms8_roomssoldpickup
			,(case mkt_seg_id when @ms9 then roomsoldcurrent end) as ms9_roomsoldcurrent
			,(case mkt_seg_id when @ms9 then roomssoldpickup end) as ms9_roomssoldpickup
			,(case mkt_seg_id when @ms10 then roomsoldcurrent end) as ms10_roomsoldcurrent
			,(case mkt_seg_id when @ms10 then roomssoldpickup end) as ms10_roomssoldpickup
			,(case mkt_seg_id when @ms11 then roomsoldcurrent end) as ms11_roomsoldcurrent
			,(case mkt_seg_id when @ms11 then roomssoldpickup end) as ms11_roomssoldpickup
			,(case mkt_seg_id when @ms12 then roomsoldcurrent end) as ms12_roomsoldcurrent
			,(case mkt_seg_id when @ms12 then roomssoldpickup end) as ms12_roomssoldpickup
			,(case mkt_seg_id when @ms13 then roomsoldcurrent end) as ms13_roomsoldcurrent
			,(case mkt_seg_id when @ms13 then roomssoldpickup end) as ms13_roomssoldpickup
			,(case mkt_seg_id when @ms14 then roomsoldcurrent end) as ms14_roomsoldcurrent
			,(case mkt_seg_id when @ms14 then roomssoldpickup end) as ms14_roomssoldpickup
			,(case mkt_seg_id when @ms15 then roomsoldcurrent end) as ms15_roomsoldcurrent
			,(case mkt_seg_id when @ms15 then roomssoldpickup end) as ms15_roomssoldpickup
			,(case mkt_seg_id when @ms16 then roomsoldcurrent end) as ms16_roomsoldcurrent
			,(case mkt_seg_id when @ms16 then roomssoldpickup end) as ms16_roomssoldpickup
			,(case mkt_seg_id when @ms17 then roomsoldcurrent end) as ms17_roomsoldcurrent
			,(case mkt_seg_id when @ms17 then roomssoldpickup end) as ms17_roomssoldpickup
			,(case mkt_seg_id when @ms18 then roomsoldcurrent end) as ms18_roomsoldcurrent
			,(case mkt_seg_id when @ms18 then roomssoldpickup end) as ms18_roomssoldpickup
			,(case mkt_seg_id when @ms19 then roomsoldcurrent end) as ms19_roomsoldcurrent
			,(case mkt_seg_id when @ms19 then roomssoldpickup end) as ms19_roomssoldpickup
			,(case mkt_seg_id when @ms20 then roomsoldcurrent end) as ms20_roomsoldcurrent
			,(case mkt_seg_id when @ms20 then roomssoldpickup end) as ms20_roomssoldpickup
			,(case mkt_seg_id when @ms21 then roomsoldcurrent end) as ms21_roomsoldcurrent
			,(case mkt_seg_id when @ms21 then roomssoldpickup end) as ms21_roomssoldpickup
			,(case mkt_seg_id when @ms22 then roomsoldcurrent end) as ms22_roomsoldcurrent
			,(case mkt_seg_id when @ms22 then roomssoldpickup end) as ms22_roomssoldpickup
			,(case mkt_seg_id when @ms23 then roomsoldcurrent end) as ms23_roomsoldcurrent
			,(case mkt_seg_id when @ms23 then roomssoldpickup end) as ms23_roomssoldpickup
			,(case mkt_seg_id when @ms24 then roomsoldcurrent end) as ms24_roomsoldcurrent
			,(case mkt_seg_id when @ms24 then roomssoldpickup end) as ms24_roomssoldpickup
			,(case mkt_seg_id when @ms25 then roomsoldcurrent end) as ms25_roomsoldcurrent
			,(case mkt_seg_id when @ms25 then roomssoldpickup end) as ms25_roomssoldpickup
			,(case mkt_seg_id when @ms26 then roomsoldcurrent end) as ms26_roomsoldcurrent
			,(case mkt_seg_id when @ms26 then roomssoldpickup end) as ms26_roomssoldpickup
			,(case mkt_seg_id when @ms27 then roomsoldcurrent end) as ms27_roomsoldcurrent
			,(case mkt_seg_id when @ms27 then roomssoldpickup end) as ms27_roomssoldpickup
			,(case mkt_seg_id when @ms28 then roomsoldcurrent end) as ms28_roomsoldcurrent
			,(case mkt_seg_id when @ms28 then roomssoldpickup end) as ms28_roomssoldpickup
			,(case mkt_seg_id when @ms29 then roomsoldcurrent end) as ms29_roomsoldcurrent
			,(case mkt_seg_id when @ms29 then roomssoldpickup end) as ms29_roomssoldpickup
			,(case mkt_seg_id when @ms30 then roomsoldcurrent end) as ms30_roomsoldcurrent
			,(case mkt_seg_id when @ms30 then roomssoldpickup end) as ms30_roomssoldpickup
			,(case mkt_seg_id when @ms31 then roomsoldcurrent end) as ms31_roomsoldcurrent
			,(case mkt_seg_id when @ms31 then roomssoldpickup end) as ms31_roomssoldpickup
			,(case mkt_seg_id when @ms32 then roomsoldcurrent end) as ms32_roomsoldcurrent
			,(case mkt_seg_id when @ms32 then roomssoldpickup end) as ms32_roomssoldpickup
			,(case mkt_seg_id when @ms33 then roomsoldcurrent end) as ms33_roomsoldcurrent
			,(case mkt_seg_id when @ms33 then roomssoldpickup end) as ms33_roomssoldpickup
			,(case mkt_seg_id when @ms34 then roomsoldcurrent end) as ms34_roomsoldcurrent
			,(case mkt_seg_id when @ms34 then roomssoldpickup end) as ms34_roomssoldpickup
			,(case mkt_seg_id when @ms35 then roomsoldcurrent end) as ms35_roomsoldcurrent
			,(case mkt_seg_id when @ms35 then roomssoldpickup end) as ms35_roomssoldpickup
			,(case mkt_seg_id when @ms36 then roomsoldcurrent end) as ms36_roomsoldcurrent
			,(case mkt_seg_id when @ms36 then roomssoldpickup end) as ms36_roomssoldpickup
			,(case mkt_seg_id when @ms37 then roomsoldcurrent end) as ms37_roomsoldcurrent
			,(case mkt_seg_id when @ms37 then roomssoldpickup end) as ms37_roomssoldpickup
			,(case mkt_seg_id when @ms38 then roomsoldcurrent end) as ms38_roomsoldcurrent
			,(case mkt_seg_id when @ms38 then roomssoldpickup end) as ms38_roomssoldpickup
			,(case mkt_seg_id when @ms39 then roomsoldcurrent end) as ms39_roomsoldcurrent
			,(case mkt_seg_id when @ms39 then roomssoldpickup end) as ms39_roomssoldpickup
			,(case mkt_seg_id when @ms40 then roomsoldcurrent end) as ms40_roomsoldcurrent
			,(case mkt_seg_id when @ms40 then roomssoldpickup end) as ms40_roomssoldpickup
			,(case mkt_seg_id when @ms41 then roomsoldcurrent end) as ms41_roomsoldcurrent
			,(case mkt_seg_id when @ms41 then roomssoldpickup end) as ms41_roomssoldpickup
			,(case mkt_seg_id when @ms42 then roomsoldcurrent end) as ms42_roomsoldcurrent
			,(case mkt_seg_id when @ms42 then roomssoldpickup end) as ms42_roomssoldpickup
			,(case mkt_seg_id when @ms43 then roomsoldcurrent end) as ms43_roomsoldcurrent
			,(case mkt_seg_id when @ms43 then roomssoldpickup end) as ms43_roomssoldpickup
			,(case mkt_seg_id when @ms44 then roomsoldcurrent end) as ms44_roomsoldcurrent
			,(case mkt_seg_id when @ms44 then roomssoldpickup end) as ms44_roomssoldpickup
			,(case mkt_seg_id when @ms45 then roomsoldcurrent end) as ms45_roomsoldcurrent
			,(case mkt_seg_id when @ms45 then roomssoldpickup end) as ms45_roomssoldpickup
			,(case mkt_seg_id when @ms46 then roomsoldcurrent end) as ms46_roomsoldcurrent
			,(case mkt_seg_id when @ms46 then roomssoldpickup end) as ms46_roomssoldpickup
			,(case mkt_seg_id when @ms47 then roomsoldcurrent end) as ms47_roomsoldcurrent
			,(case mkt_seg_id when @ms47 then roomssoldpickup end) as ms47_roomssoldpickup
			,(case mkt_seg_id when @ms48 then roomsoldcurrent end) as ms48_roomsoldcurrent
			,(case mkt_seg_id when @ms48 then roomssoldpickup end) as ms48_roomssoldpickup
			,(case mkt_seg_id when @ms49 then roomsoldcurrent end) as ms49_roomsoldcurrent
			,(case mkt_seg_id when @ms49 then roomssoldpickup end) as ms49_roomssoldpickup
			,(case mkt_seg_id when @ms50 then roomsoldcurrent end) as ms50_roomsoldcurrent
			,(case mkt_seg_id when @ms50 then roomssoldpickup end) as ms50_roomssoldpickup
		
			,(case mkt_seg_id when @ms1 then occfcstcurrent end) as ms1_occfcstcurrent
			,(case mkt_seg_id when @ms1 then occfcstpickup end) as ms1_occfcstpickup
			,(case mkt_seg_id when @ms2 then occfcstcurrent end) as ms2_occfcstcurrent
			,(case mkt_seg_id when @ms2 then occfcstpickup end) as ms2_occfcstpickup
			,(case mkt_seg_id when @ms3 then occfcstcurrent end) as ms3_occfcstcurrent
			,(case mkt_seg_id when @ms3 then occfcstpickup end) as ms3_occfcstpickup
			,(case mkt_seg_id when @ms4 then occfcstcurrent end) as ms4_occfcstcurrent
			,(case mkt_seg_id when @ms4 then occfcstpickup end) as ms4_occfcstpickup
			,(case mkt_seg_id when @ms5 then occfcstcurrent end) as ms5_occfcstcurrent
			,(case mkt_seg_id when @ms5 then occfcstpickup end) as ms5_occfcstpickup
			,(case mkt_seg_id when @ms6 then occfcstcurrent end) as ms6_occfcstcurrent
			,(case mkt_seg_id when @ms6 then occfcstpickup end) as ms6_occfcstpickup
			,(case mkt_seg_id when @ms7 then occfcstcurrent end) as ms7_occfcstcurrent
			,(case mkt_seg_id when @ms7 then occfcstpickup end) as ms7_occfcstpickup
			,(case mkt_seg_id when @ms8 then occfcstcurrent end) as ms8_occfcstcurrent
			,(case mkt_seg_id when @ms8 then occfcstpickup end) as ms8_occfcstpickup
			,(case mkt_seg_id when @ms9 then occfcstcurrent end) as ms9_occfcstcurrent
			,(case mkt_seg_id when @ms9 then occfcstpickup end) as ms9_occfcstpickup
			,(case mkt_seg_id when @ms10 then occfcstcurrent end) as ms10_occfcstcurrent
			,(case mkt_seg_id when @ms10 then occfcstpickup end) as ms10_occfcstpickup
			,(case mkt_seg_id when @ms11 then occfcstcurrent end) as ms11_occfcstcurrent
			,(case mkt_seg_id when @ms11 then occfcstpickup end) as ms11_occfcstpickup
			,(case mkt_seg_id when @ms12 then occfcstcurrent end) as ms12_occfcstcurrent
			,(case mkt_seg_id when @ms12 then occfcstpickup end) as ms12_occfcstpickup
			,(case mkt_seg_id when @ms13 then occfcstcurrent end) as ms13_occfcstcurrent
			,(case mkt_seg_id when @ms13 then occfcstpickup end) as ms13_occfcstpickup
			,(case mkt_seg_id when @ms14 then occfcstcurrent end) as ms14_occfcstcurrent
			,(case mkt_seg_id when @ms14 then occfcstpickup end) as ms14_occfcstpickup
			,(case mkt_seg_id when @ms15 then occfcstcurrent end) as ms15_occfcstcurrent
			,(case mkt_seg_id when @ms15 then occfcstpickup end) as ms15_occfcstpickup
			,(case mkt_seg_id when @ms16 then occfcstcurrent end) as ms16_occfcstcurrent
			,(case mkt_seg_id when @ms16 then occfcstpickup end) as ms16_occfcstpickup
			,(case mkt_seg_id when @ms17 then occfcstcurrent end) as ms17_occfcstcurrent
			,(case mkt_seg_id when @ms17 then occfcstpickup end) as ms17_occfcstpickup
			,(case mkt_seg_id when @ms18 then occfcstcurrent end) as ms18_occfcstcurrent
			,(case mkt_seg_id when @ms18 then occfcstpickup end) as ms18_occfcstpickup
			,(case mkt_seg_id when @ms19 then occfcstcurrent end) as ms19_occfcstcurrent
			,(case mkt_seg_id when @ms19 then occfcstpickup end) as ms19_occfcstpickup
			,(case mkt_seg_id when @ms20 then occfcstcurrent end) as ms20_occfcstcurrent
			,(case mkt_seg_id when @ms20 then occfcstpickup end) as ms20_occfcstpickup
			,(case mkt_seg_id when @ms21 then occfcstcurrent end) as ms21_occfcstcurrent
			,(case mkt_seg_id when @ms21 then occfcstpickup end) as ms21_occfcstpickup
			,(case mkt_seg_id when @ms22 then occfcstcurrent end) as ms22_occfcstcurrent
			,(case mkt_seg_id when @ms22 then occfcstpickup end) as ms22_occfcstpickup
			,(case mkt_seg_id when @ms23 then occfcstcurrent end) as ms23_occfcstcurrent
			,(case mkt_seg_id when @ms23 then occfcstpickup end) as ms23_occfcstpickup
			,(case mkt_seg_id when @ms24 then occfcstcurrent end) as ms24_occfcstcurrent
			,(case mkt_seg_id when @ms24 then occfcstpickup end) as ms24_occfcstpickup
			,(case mkt_seg_id when @ms25 then occfcstcurrent end) as ms25_occfcstcurrent
			,(case mkt_seg_id when @ms25 then occfcstpickup end) as ms25_occfcstpickup
			,(case mkt_seg_id when @ms26 then occfcstcurrent end) as ms26_occfcstcurrent
			,(case mkt_seg_id when @ms26 then occfcstpickup end) as ms26_occfcstpickup
			,(case mkt_seg_id when @ms27 then occfcstcurrent end) as ms27_occfcstcurrent
			,(case mkt_seg_id when @ms27 then occfcstpickup end) as ms27_occfcstpickup
			,(case mkt_seg_id when @ms28 then occfcstcurrent end) as ms28_occfcstcurrent
			,(case mkt_seg_id when @ms28 then occfcstpickup end) as ms28_occfcstpickup
			,(case mkt_seg_id when @ms29 then occfcstcurrent end) as ms29_occfcstcurrent
			,(case mkt_seg_id when @ms29 then occfcstpickup end) as ms29_occfcstpickup
			,(case mkt_seg_id when @ms30 then occfcstcurrent end) as ms30_occfcstcurrent
			,(case mkt_seg_id when @ms30 then occfcstpickup end) as ms30_occfcstpickup
			,(case mkt_seg_id when @ms31 then occfcstcurrent end) as ms31_occfcstcurrent
			,(case mkt_seg_id when @ms31 then occfcstpickup end) as ms31_occfcstpickup
			,(case mkt_seg_id when @ms32 then occfcstcurrent end) as ms32_occfcstcurrent
			,(case mkt_seg_id when @ms32 then occfcstpickup end) as ms32_occfcstpickup
			,(case mkt_seg_id when @ms33 then occfcstcurrent end) as ms33_occfcstcurrent
			,(case mkt_seg_id when @ms33 then occfcstpickup end) as ms33_occfcstpickup
			,(case mkt_seg_id when @ms34 then occfcstcurrent end) as ms34_occfcstcurrent
			,(case mkt_seg_id when @ms34 then occfcstpickup end) as ms34_occfcstpickup
			,(case mkt_seg_id when @ms35 then occfcstcurrent end) as ms35_occfcstcurrent
			,(case mkt_seg_id when @ms35 then occfcstpickup end) as ms35_occfcstpickup
			,(case mkt_seg_id when @ms36 then occfcstcurrent end) as ms36_occfcstcurrent
			,(case mkt_seg_id when @ms36 then occfcstpickup end) as ms36_occfcstpickup
			,(case mkt_seg_id when @ms37 then occfcstcurrent end) as ms37_occfcstcurrent
			,(case mkt_seg_id when @ms37 then occfcstpickup end) as ms37_occfcstpickup
			,(case mkt_seg_id when @ms38 then occfcstcurrent end) as ms38_occfcstcurrent
			,(case mkt_seg_id when @ms38 then occfcstpickup end) as ms38_occfcstpickup
			,(case mkt_seg_id when @ms39 then occfcstcurrent end) as ms39_occfcstcurrent
			,(case mkt_seg_id when @ms39 then occfcstpickup end) as ms39_occfcstpickup
			,(case mkt_seg_id when @ms40 then occfcstcurrent end) as ms40_occfcstcurrent
			,(case mkt_seg_id when @ms40 then occfcstpickup end) as ms40_occfcstpickup
			,(case mkt_seg_id when @ms41 then occfcstcurrent end) as ms41_occfcstcurrent
			,(case mkt_seg_id when @ms41 then occfcstpickup end) as ms41_occfcstpickup
			,(case mkt_seg_id when @ms42 then occfcstcurrent end) as ms42_occfcstcurrent
			,(case mkt_seg_id when @ms42 then occfcstpickup end) as ms42_occfcstpickup
			,(case mkt_seg_id when @ms43 then occfcstcurrent end) as ms43_occfcstcurrent
			,(case mkt_seg_id when @ms43 then occfcstpickup end) as ms43_occfcstpickup
			,(case mkt_seg_id when @ms44 then occfcstcurrent end) as ms44_occfcstcurrent
			,(case mkt_seg_id when @ms44 then occfcstpickup end) as ms44_occfcstpickup
			,(case mkt_seg_id when @ms45 then occfcstcurrent end) as ms45_occfcstcurrent
			,(case mkt_seg_id when @ms45 then occfcstpickup end) as ms45_occfcstpickup
			,(case mkt_seg_id when @ms46 then occfcstcurrent end) as ms46_occfcstcurrent
			,(case mkt_seg_id when @ms46 then occfcstpickup end) as ms46_occfcstpickup
			,(case mkt_seg_id when @ms47 then occfcstcurrent end) as ms47_occfcstcurrent
			,(case mkt_seg_id when @ms47 then occfcstpickup end) as ms47_occfcstpickup
			,(case mkt_seg_id when @ms48 then occfcstcurrent end) as ms48_occfcstcurrent
			,(case mkt_seg_id when @ms48 then occfcstpickup end) as ms48_occfcstpickup
			,(case mkt_seg_id when @ms49 then occfcstcurrent end) as ms49_occfcstcurrent
			,(case mkt_seg_id when @ms49 then occfcstpickup end) as ms49_occfcstpickup
			,(case mkt_seg_id when @ms50 then occfcstcurrent end) as ms50_occfcstcurrent
			,(case mkt_seg_id when @ms50 then occfcstpickup end) as ms50_occfcstpickup

		
			,(case mkt_seg_id when @ms1 then bookedroomrevenuecurrent end) as ms1_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms1 then bookedroomrevenuepickup end) as ms1_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms2 then bookedroomrevenuecurrent end) as ms2_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms2 then bookedroomrevenuepickup end) as ms2_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms3 then bookedroomrevenuecurrent end) as ms3_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms3 then bookedroomrevenuepickup end) as ms3_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms4 then bookedroomrevenuecurrent end) as ms4_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms4 then bookedroomrevenuepickup end) as ms4_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms5 then bookedroomrevenuecurrent end) as ms5_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms5 then bookedroomrevenuepickup end) as ms5_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then bookedroomrevenuepickup end) as ms6_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then bookedroomrevenuecurrent end) as ms6_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then bookedroomrevenuecurrent end) as ms7_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then bookedroomrevenuepickup end) as ms7_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms8 then bookedroomrevenuecurrent end) as ms8_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms8 then bookedroomrevenuepickup end) as ms8_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms9 then bookedroomrevenuecurrent end) as ms9_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms9 then bookedroomrevenuepickup end) as ms9_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms10 then bookedroomrevenuecurrent end) as ms10_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms10 then bookedroomrevenuepickup end) as ms10_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms11 then bookedroomrevenuecurrent end) as ms11_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms11 then bookedroomrevenuepickup end) as ms11_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms12 then bookedroomrevenuecurrent end) as ms12_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms12 then bookedroomrevenuepickup end) as ms12_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms13 then bookedroomrevenuecurrent end) as ms13_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms13 then bookedroomrevenuepickup end) as ms13_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms14 then bookedroomrevenuepickup end) as ms14_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms14 then bookedroomrevenuecurrent end) as ms14_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms15 then bookedroomrevenuecurrent end) as ms15_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms15 then bookedroomrevenuepickup end) as ms15_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms16 then bookedroomrevenuecurrent end) as ms16_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms16 then bookedroomrevenuepickup end) as ms16_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms17 then bookedroomrevenuecurrent end) as ms17_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms17 then bookedroomrevenuepickup end) as ms17_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms18 then bookedroomrevenuepickup end) as ms18_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms18 then bookedroomrevenuecurrent end) as ms18_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms19 then bookedroomrevenuecurrent end) as ms19_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms19 then bookedroomrevenuepickup end) as ms19_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms20 then bookedroomrevenuecurrent end) as ms20_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms20 then bookedroomrevenuepickup end) as ms20_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms21 then bookedroomrevenuecurrent end) as ms21_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms21 then bookedroomrevenuepickup end) as ms21_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms22 then bookedroomrevenuecurrent end) as ms22_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms22 then bookedroomrevenuepickup end) as ms22_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms23 then bookedroomrevenuecurrent end) as ms23_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms23 then bookedroomrevenuepickup end) as ms23_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms24 then bookedroomrevenuepickup end) as ms24_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms24 then bookedroomrevenuecurrent end) as ms24_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms25 then bookedroomrevenuecurrent end) as ms25_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms25 then bookedroomrevenuepickup end) as ms25_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms26 then bookedroomrevenuecurrent end) as ms26_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms26 then bookedroomrevenuepickup end) as ms26_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms27 then bookedroomrevenuecurrent end) as ms27_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms27 then bookedroomrevenuepickup end) as ms27_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms28 then bookedroomrevenuepickup end) as ms28_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms28 then bookedroomrevenuecurrent end) as ms28_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms29 then bookedroomrevenuecurrent end) as ms29_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms29 then bookedroomrevenuepickup end) as ms29_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms30 then bookedroomrevenuecurrent end) as ms30_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms30 then bookedroomrevenuepickup end) as ms30_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms31 then bookedroomrevenuecurrent end) as ms31_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms31 then bookedroomrevenuepickup end) as ms31_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms32 then bookedroomrevenuecurrent end) as ms32_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms32 then bookedroomrevenuepickup end) as ms32_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms33 then bookedroomrevenuecurrent end) as ms33_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms33 then bookedroomrevenuepickup end) as ms33_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms34 then bookedroomrevenuepickup end) as ms34_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms34 then bookedroomrevenuecurrent end) as ms34_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms35 then bookedroomrevenuecurrent end) as ms35_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms35 then bookedroomrevenuepickup end) as ms35_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms36 then bookedroomrevenuecurrent end) as ms36_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms36 then bookedroomrevenuepickup end) as ms36_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms37 then bookedroomrevenuecurrent end) as ms37_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms37 then bookedroomrevenuepickup end) as ms37_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms38 then bookedroomrevenuepickup end) as ms38_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms38 then bookedroomrevenuecurrent end) as ms38_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms39 then bookedroomrevenuecurrent end) as ms39_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms39 then bookedroomrevenuepickup end) as ms39_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms40 then bookedroomrevenuecurrent end) as ms40_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms40 then bookedroomrevenuepickup end) as ms40_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms41 then bookedroomrevenuecurrent end) as ms41_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms41 then bookedroomrevenuepickup end) as ms41_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms42 then bookedroomrevenuecurrent end) as ms42_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms42 then bookedroomrevenuepickup end) as ms42_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms43 then bookedroomrevenuecurrent end) as ms43_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms43 then bookedroomrevenuepickup end) as ms43_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms44 then bookedroomrevenuepickup end) as ms44_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms44 then bookedroomrevenuecurrent end) as ms44_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms45 then bookedroomrevenuecurrent end) as ms45_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms45 then bookedroomrevenuepickup end) as ms45_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms46 then bookedroomrevenuecurrent end) as ms46_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms46 then bookedroomrevenuepickup end) as ms46_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms47 then bookedroomrevenuecurrent end) as ms47_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms47 then bookedroomrevenuepickup end) as ms47_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms48 then bookedroomrevenuepickup end) as ms48_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms48 then bookedroomrevenuecurrent end) as ms48_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms49 then bookedroomrevenuecurrent end) as ms49_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms49 then bookedroomrevenuepickup end) as ms49_bookedroomrevenuepickup
			,(case mkt_seg_id when @ms50 then bookedroomrevenuecurrent end) as ms50_bookedroomrevenuecurrent
			,(case mkt_seg_id when @ms50 then bookedroomrevenuepickup end) as ms50_bookedroomrevenuepickup
		
			,(case mkt_seg_id when @ms1 then fcstedroomrevenuecurrent end) as ms1_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms1 then fcstedroomrevenuepickup end) as ms1_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms2 then fcstedroomrevenuecurrent end) as ms2_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms2 then fcstedroomrevenuepickup end) as ms2_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms3 then fcstedroomrevenuecurrent end) as ms3_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms3 then fcstedroomrevenuepickup end) as ms3_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms4 then fcstedroomrevenuecurrent end) as ms4_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms4 then fcstedroomrevenuepickup end) as ms4_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms5 then fcstedroomrevenuecurrent end) as ms5_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms5 then fcstedroomrevenuepickup end) as ms5_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms6 then fcstedroomrevenuecurrent end) as ms6_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms6 then fcstedroomrevenuepickup end) as ms6_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms7 then fcstedroomrevenuecurrent end) as ms7_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms7 then fcstedroomrevenuepickup end) as ms7_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms8 then fcstedroomrevenuecurrent end) as ms8_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms8 then fcstedroomrevenuepickup end) as ms8_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms9 then fcstedroomrevenuecurrent end) as ms9_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms9 then fcstedroomrevenuepickup end) as ms9_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms10 then fcstedroomrevenuecurrent end) as ms10_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms10 then fcstedroomrevenuepickup end) as ms10_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms11 then fcstedroomrevenuecurrent end) as ms11_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms11 then fcstedroomrevenuepickup end) as ms11_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms12 then fcstedroomrevenuecurrent end) as ms12_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms12 then fcstedroomrevenuepickup end) as ms12_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms13 then fcstedroomrevenuecurrent end) as ms13_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms13 then fcstedroomrevenuepickup end) as ms13_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms14 then fcstedroomrevenuecurrent end) as ms14_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms14 then fcstedroomrevenuepickup end) as ms14_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms15 then fcstedroomrevenuecurrent end) as ms15_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms15 then fcstedroomrevenuepickup end) as ms15_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms16 then fcstedroomrevenuecurrent end) as ms16_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms16 then fcstedroomrevenuepickup end) as ms16_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms17 then fcstedroomrevenuecurrent end) as ms17_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms17 then fcstedroomrevenuepickup end) as ms17_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms18 then fcstedroomrevenuecurrent end) as ms18_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms18 then fcstedroomrevenuepickup end) as ms18_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms19 then fcstedroomrevenuecurrent end) as ms19_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms19 then fcstedroomrevenuepickup end) as ms19_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms20 then fcstedroomrevenuecurrent end) as ms20_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms20 then fcstedroomrevenuepickup end) as ms20_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms21 then fcstedroomrevenuecurrent end) as ms21_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms21 then fcstedroomrevenuepickup end) as ms21_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms22 then fcstedroomrevenuecurrent end) as ms22_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms22 then fcstedroomrevenuepickup end) as ms22_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms23 then fcstedroomrevenuecurrent end) as ms23_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms23 then fcstedroomrevenuepickup end) as ms23_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms24 then fcstedroomrevenuecurrent end) as ms24_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms24 then fcstedroomrevenuepickup end) as ms24_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms25 then fcstedroomrevenuecurrent end) as ms25_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms25 then fcstedroomrevenuepickup end) as ms25_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms26 then fcstedroomrevenuecurrent end) as ms26_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms26 then fcstedroomrevenuepickup end) as ms26_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms27 then fcstedroomrevenuecurrent end) as ms27_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms27 then fcstedroomrevenuepickup end) as ms27_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms28 then fcstedroomrevenuecurrent end) as ms28_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms28 then fcstedroomrevenuepickup end) as ms28_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms29 then fcstedroomrevenuecurrent end) as ms29_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms29 then fcstedroomrevenuepickup end) as ms29_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms30 then fcstedroomrevenuecurrent end) as ms30_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms30 then fcstedroomrevenuepickup end) as ms30_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms31 then fcstedroomrevenuecurrent end) as ms31_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms31 then fcstedroomrevenuepickup end) as ms31_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms32 then fcstedroomrevenuecurrent end) as ms32_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms32 then fcstedroomrevenuepickup end) as ms32_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms33 then fcstedroomrevenuecurrent end) as ms33_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms33 then fcstedroomrevenuepickup end) as ms33_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms34 then fcstedroomrevenuecurrent end) as ms34_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms34 then fcstedroomrevenuepickup end) as ms34_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms35 then fcstedroomrevenuecurrent end) as ms35_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms35 then fcstedroomrevenuepickup end) as ms35_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms36 then fcstedroomrevenuecurrent end) as ms36_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms36 then fcstedroomrevenuepickup end) as ms36_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms37 then fcstedroomrevenuecurrent end) as ms37_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms37 then fcstedroomrevenuepickup end) as ms37_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms38 then fcstedroomrevenuecurrent end) as ms38_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms38 then fcstedroomrevenuepickup end) as ms38_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms39 then fcstedroomrevenuecurrent end) as ms39_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms39 then fcstedroomrevenuepickup end) as ms39_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms40 then fcstedroomrevenuecurrent end) as ms40_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms40 then fcstedroomrevenuepickup end) as ms40_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms41 then fcstedroomrevenuecurrent end) as ms41_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms41 then fcstedroomrevenuepickup end) as ms41_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms42 then fcstedroomrevenuecurrent end) as ms42_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms42 then fcstedroomrevenuepickup end) as ms42_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms43 then fcstedroomrevenuecurrent end) as ms43_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms43 then fcstedroomrevenuepickup end) as ms43_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms44 then fcstedroomrevenuecurrent end) as ms44_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms44 then fcstedroomrevenuepickup end) as ms44_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms45 then fcstedroomrevenuecurrent end) as ms45_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms45 then fcstedroomrevenuepickup end) as ms45_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms46 then fcstedroomrevenuecurrent end) as ms46_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms46 then fcstedroomrevenuepickup end) as ms46_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms47 then fcstedroomrevenuecurrent end) as ms47_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms47 then fcstedroomrevenuepickup end) as ms47_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms48 then fcstedroomrevenuecurrent end) as ms48_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms48 then fcstedroomrevenuepickup end) as ms48_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms49 then fcstedroomrevenuecurrent end) as ms49_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms49 then fcstedroomrevenuepickup end) as ms49_fcstedroomrevenuepickup
			,(case mkt_seg_id when @ms50 then fcstedroomrevenuecurrent end) as ms50_fcstedroomrevenuecurrent
			,(case mkt_seg_id when @ms50 then fcstedroomrevenuepickup end) as ms50_fcstedroomrevenuepickup
		
			,(case mkt_seg_id when @ms1 then bookedadrcurrent end) as ms1_bookedadrcurrent
			,(case mkt_seg_id when @ms1 then bookedadrpickup end) as ms1_bookedadrpickup
			,(case mkt_seg_id when @ms2 then bookedadrcurrent end) as ms2_bookedadrcurrent
			,(case mkt_seg_id when @ms2 then bookedadrpickup end) as ms2_bookedadrpickup
			,(case mkt_seg_id when @ms3 then bookedadrcurrent end) as ms3_bookedadrcurrent
			,(case mkt_seg_id when @ms3 then bookedadrpickup end) as ms3_bookedadrpickup
			,(case mkt_seg_id when @ms4 then bookedadrcurrent end) as ms4_bookedadrcurrent
			,(case mkt_seg_id when @ms4 then bookedadrpickup end) as ms4_bookedadrpickup
			,(case mkt_seg_id when @ms5 then bookedadrcurrent end) as ms5_bookedadrcurrent
			,(case mkt_seg_id when @ms5 then bookedadrpickup end) as ms5_bookedadrpickup
			,(case mkt_seg_id when @ms6 then bookedadrcurrent end) as ms6_bookedadrcurrent
			,(case mkt_seg_id when @ms6 then bookedadrpickup end) as ms6_bookedadrpickup
			,(case mkt_seg_id when @ms7 then bookedadrcurrent end) as ms7_bookedadrcurrent
			,(case mkt_seg_id when @ms7 then bookedadrpickup end) as ms7_bookedadrpickup
			,(case mkt_seg_id when @ms8 then bookedadrcurrent end) as ms8_bookedadrcurrent
			,(case mkt_seg_id when @ms8 then bookedadrpickup end) as ms8_bookedadrpickup
			,(case mkt_seg_id when @ms9 then bookedadrcurrent end) as ms9_bookedadrcurrent
			,(case mkt_seg_id when @ms9 then bookedadrpickup end) as ms9_bookedadrpickup
			,(case mkt_seg_id when @ms10 then bookedadrcurrent end) as ms10_bookedadrcurrent
			,(case mkt_seg_id when @ms10 then bookedadrpickup end) as ms10_bookedadrpickup
			,(case mkt_seg_id when @ms11 then bookedadrcurrent end) as ms11_bookedadrcurrent
			,(case mkt_seg_id when @ms11 then bookedadrpickup end) as ms11_bookedadrpickup
			,(case mkt_seg_id when @ms12 then bookedadrcurrent end) as ms12_bookedadrcurrent
			,(case mkt_seg_id when @ms12 then bookedadrpickup end) as ms12_bookedadrpickup
			,(case mkt_seg_id when @ms13 then bookedadrcurrent end) as ms13_bookedadrcurrent
			,(case mkt_seg_id when @ms13 then bookedadrpickup end) as ms13_bookedadrpickup
			,(case mkt_seg_id when @ms14 then bookedadrcurrent end) as ms14_bookedadrcurrent
			,(case mkt_seg_id when @ms14 then bookedadrpickup end) as ms14_bookedadrpickup
			,(case mkt_seg_id when @ms15 then bookedadrcurrent end) as ms15_bookedadrcurrent
			,(case mkt_seg_id when @ms15 then bookedadrpickup end) as ms15_bookedadrpickup
			,(case mkt_seg_id when @ms16 then bookedadrcurrent end) as ms16_bookedadrcurrent
			,(case mkt_seg_id when @ms16 then bookedadrpickup end) as ms16_bookedadrpickup
			,(case mkt_seg_id when @ms17 then bookedadrcurrent end) as ms17_bookedadrcurrent
			,(case mkt_seg_id when @ms17 then bookedadrpickup end) as ms17_bookedadrpickup
			,(case mkt_seg_id when @ms18 then bookedadrcurrent end) as ms18_bookedadrcurrent
			,(case mkt_seg_id when @ms18 then bookedadrpickup end) as ms18_bookedadrpickup
			,(case mkt_seg_id when @ms19 then bookedadrcurrent end) as ms19_bookedadrcurrent
			,(case mkt_seg_id when @ms19 then bookedadrpickup end) as ms19_bookedadrpickup
			,(case mkt_seg_id when @ms20 then bookedadrcurrent end) as ms20_bookedadrcurrent
			,(case mkt_seg_id when @ms20 then bookedadrpickup end) as ms20_bookedadrpickup
			,(case mkt_seg_id when @ms21 then bookedadrcurrent end) as ms21_bookedadrcurrent
			,(case mkt_seg_id when @ms21 then bookedadrpickup end) as ms21_bookedadrpickup
			,(case mkt_seg_id when @ms22 then bookedadrcurrent end) as ms22_bookedadrcurrent
			,(case mkt_seg_id when @ms22 then bookedadrpickup end) as ms22_bookedadrpickup
			,(case mkt_seg_id when @ms23 then bookedadrcurrent end) as ms23_bookedadrcurrent
			,(case mkt_seg_id when @ms23 then bookedadrpickup end) as ms23_bookedadrpickup
			,(case mkt_seg_id when @ms24 then bookedadrcurrent end) as ms24_bookedadrcurrent
			,(case mkt_seg_id when @ms24 then bookedadrpickup end) as ms24_bookedadrpickup
			,(case mkt_seg_id when @ms25 then bookedadrcurrent end) as ms25_bookedadrcurrent
			,(case mkt_seg_id when @ms25 then bookedadrpickup end) as ms25_bookedadrpickup
			,(case mkt_seg_id when @ms26 then bookedadrcurrent end) as ms26_bookedadrcurrent
			,(case mkt_seg_id when @ms26 then bookedadrpickup end) as ms26_bookedadrpickup
			,(case mkt_seg_id when @ms27 then bookedadrcurrent end) as ms27_bookedadrcurrent
			,(case mkt_seg_id when @ms27 then bookedadrpickup end) as ms27_bookedadrpickup
			,(case mkt_seg_id when @ms28 then bookedadrcurrent end) as ms28_bookedadrcurrent
			,(case mkt_seg_id when @ms28 then bookedadrpickup end) as ms28_bookedadrpickup
			,(case mkt_seg_id when @ms29 then bookedadrcurrent end) as ms29_bookedadrcurrent
			,(case mkt_seg_id when @ms29 then bookedadrpickup end) as ms29_bookedadrpickup
			,(case mkt_seg_id when @ms30 then bookedadrcurrent end) as ms30_bookedadrcurrent
			,(case mkt_seg_id when @ms30 then bookedadrpickup end) as ms30_bookedadrpickup
			,(case mkt_seg_id when @ms31 then bookedadrcurrent end) as ms31_bookedadrcurrent
			,(case mkt_seg_id when @ms31 then bookedadrpickup end) as ms31_bookedadrpickup
			,(case mkt_seg_id when @ms32 then bookedadrcurrent end) as ms32_bookedadrcurrent
			,(case mkt_seg_id when @ms32 then bookedadrpickup end) as ms32_bookedadrpickup
			,(case mkt_seg_id when @ms33 then bookedadrcurrent end) as ms33_bookedadrcurrent
			,(case mkt_seg_id when @ms33 then bookedadrpickup end) as ms33_bookedadrpickup
			,(case mkt_seg_id when @ms34 then bookedadrcurrent end) as ms34_bookedadrcurrent
			,(case mkt_seg_id when @ms34 then bookedadrpickup end) as ms34_bookedadrpickup
			,(case mkt_seg_id when @ms35 then bookedadrcurrent end) as ms35_bookedadrcurrent
			,(case mkt_seg_id when @ms35 then bookedadrpickup end) as ms35_bookedadrpickup
			,(case mkt_seg_id when @ms36 then bookedadrcurrent end) as ms36_bookedadrcurrent
			,(case mkt_seg_id when @ms36 then bookedadrpickup end) as ms36_bookedadrpickup
			,(case mkt_seg_id when @ms37 then bookedadrcurrent end) as ms37_bookedadrcurrent
			,(case mkt_seg_id when @ms37 then bookedadrpickup end) as ms37_bookedadrpickup
			,(case mkt_seg_id when @ms38 then bookedadrcurrent end) as ms38_bookedadrcurrent
			,(case mkt_seg_id when @ms38 then bookedadrpickup end) as ms38_bookedadrpickup
			,(case mkt_seg_id when @ms39 then bookedadrcurrent end) as ms39_bookedadrcurrent
			,(case mkt_seg_id when @ms39 then bookedadrpickup end) as ms39_bookedadrpickup
			,(case mkt_seg_id when @ms40 then bookedadrcurrent end) as ms40_bookedadrcurrent
			,(case mkt_seg_id when @ms40 then bookedadrpickup end) as ms40_bookedadrpickup
			,(case mkt_seg_id when @ms41 then bookedadrcurrent end) as ms41_bookedadrcurrent
			,(case mkt_seg_id when @ms41 then bookedadrpickup end) as ms41_bookedadrpickup
			,(case mkt_seg_id when @ms42 then bookedadrcurrent end) as ms42_bookedadrcurrent
			,(case mkt_seg_id when @ms42 then bookedadrpickup end) as ms42_bookedadrpickup
			,(case mkt_seg_id when @ms43 then bookedadrcurrent end) as ms43_bookedadrcurrent
			,(case mkt_seg_id when @ms43 then bookedadrpickup end) as ms43_bookedadrpickup
			,(case mkt_seg_id when @ms44 then bookedadrcurrent end) as ms44_bookedadrcurrent
			,(case mkt_seg_id when @ms44 then bookedadrpickup end) as ms44_bookedadrpickup
			,(case mkt_seg_id when @ms45 then bookedadrcurrent end) as ms45_bookedadrcurrent
			,(case mkt_seg_id when @ms45 then bookedadrpickup end) as ms45_bookedadrpickup
			,(case mkt_seg_id when @ms46 then bookedadrcurrent end) as ms46_bookedadrcurrent
			,(case mkt_seg_id when @ms46 then bookedadrpickup end) as ms46_bookedadrpickup
			,(case mkt_seg_id when @ms47 then bookedadrcurrent end) as ms47_bookedadrcurrent
			,(case mkt_seg_id when @ms47 then bookedadrpickup end) as ms47_bookedadrpickup
			,(case mkt_seg_id when @ms48 then bookedadrcurrent end) as ms48_bookedadrcurrent
			,(case mkt_seg_id when @ms48 then bookedadrpickup end) as ms48_bookedadrpickup
			,(case mkt_seg_id when @ms49 then bookedadrcurrent end) as ms49_bookedadrcurrent
			,(case mkt_seg_id when @ms49 then bookedadrpickup end) as ms49_bookedadrpickup
			,(case mkt_seg_id when @ms50 then bookedadrcurrent end) as ms50_bookedadrcurrent
			,(case mkt_seg_id when @ms50 then bookedadrpickup end) as ms50_bookedadrpickup
		
			,(case mkt_seg_id when @ms1 then fcstedadrcurrent end) as ms1_fcstedadrcurrent
			,(case mkt_seg_id when @ms1 then fcstedadrpickup end) as ms1_fcstedadrpickup
			,(case mkt_seg_id when @ms2 then fcstedadrcurrent end) as ms2_fcstedadrcurrent
			,(case mkt_seg_id when @ms2 then fcstedadrpickup end) as ms2_fcstedadrpickup
			,(case mkt_seg_id when @ms3 then fcstedadrcurrent end) as ms3_fcstedadrcurrent
			,(case mkt_seg_id when @ms3 then fcstedadrpickup end) as ms3_fcstedadrpickup
			,(case mkt_seg_id when @ms4 then fcstedadrcurrent end) as ms4_fcstedadrcurrent
			,(case mkt_seg_id when @ms4 then fcstedadrpickup end) as ms4_fcstedadrpickup
			,(case mkt_seg_id when @ms5 then fcstedadrcurrent end) as ms5_fcstedadrcurrent
			,(case mkt_seg_id when @ms5 then fcstedadrpickup end) as ms5_fcstedadrpickup
			,(case mkt_seg_id when @ms6 then fcstedadrcurrent end) as ms6_fcstedadrcurrent
			,(case mkt_seg_id when @ms6 then fcstedadrpickup end) as ms6_fcstedadrpickup
			,(case mkt_seg_id when @ms7 then fcstedadrcurrent end) as ms7_fcstedadrcurrent
			,(case mkt_seg_id when @ms7 then fcstedadrpickup end) as ms7_fcstedadrpickup
			,(case mkt_seg_id when @ms8 then fcstedadrcurrent end) as ms8_fcstedadrcurrent
			,(case mkt_seg_id when @ms8 then fcstedadrpickup end) as ms8_fcstedadrpickup
			,(case mkt_seg_id when @ms9 then fcstedadrcurrent end) as ms9_fcstedadrcurrent
			,(case mkt_seg_id when @ms9 then fcstedadrpickup end) as ms9_fcstedadrpickup
			,(case mkt_seg_id when @ms10 then fcstedadrcurrent end) as ms10_fcstedadrcurrent
			,(case mkt_seg_id when @ms10 then fcstedadrpickup end) as ms10_fcstedadrpickup
			,(case mkt_seg_id when @ms11 then fcstedadrcurrent end) as ms11_fcstedadrcurrent
			,(case mkt_seg_id when @ms11 then fcstedadrpickup end) as ms11_fcstedadrpickup
			,(case mkt_seg_id when @ms12 then fcstedadrcurrent end) as ms12_fcstedadrcurrent
			,(case mkt_seg_id when @ms12 then fcstedadrpickup end) as ms12_fcstedadrpickup
			,(case mkt_seg_id when @ms13 then fcstedadrcurrent end) as ms13_fcstedadrcurrent
			,(case mkt_seg_id when @ms13 then fcstedadrpickup end) as ms13_fcstedadrpickup
			,(case mkt_seg_id when @ms14 then fcstedadrcurrent end) as ms14_fcstedadrcurrent
			,(case mkt_seg_id when @ms14 then fcstedadrpickup end) as ms14_fcstedadrpickup
			,(case mkt_seg_id when @ms15 then fcstedadrcurrent end) as ms15_fcstedadrcurrent
			,(case mkt_seg_id when @ms15 then fcstedadrpickup end) as ms15_fcstedadrpickup
			,(case mkt_seg_id when @ms16 then fcstedadrcurrent end) as ms16_fcstedadrcurrent
			,(case mkt_seg_id when @ms16 then fcstedadrpickup end) as ms16_fcstedadrpickup
			,(case mkt_seg_id when @ms17 then fcstedadrcurrent end) as ms17_fcstedadrcurrent
			,(case mkt_seg_id when @ms17 then fcstedadrpickup end) as ms17_fcstedadrpickup
			,(case mkt_seg_id when @ms18 then fcstedadrcurrent end) as ms18_fcstedadrcurrent
			,(case mkt_seg_id when @ms18 then fcstedadrpickup end) as ms18_fcstedadrpickup
			,(case mkt_seg_id when @ms19 then fcstedadrcurrent end) as ms19_fcstedadrcurrent
			,(case mkt_seg_id when @ms19 then fcstedadrpickup end) as ms19_fcstedadrpickup
			,(case mkt_seg_id when @ms20 then fcstedadrcurrent end) as ms20_fcstedadrcurrent
			,(case mkt_seg_id when @ms20 then fcstedadrpickup end) as ms20_fcstedadrpickup
			,(case mkt_seg_id when @ms21 then fcstedadrcurrent end) as ms21_fcstedadrcurrent
			,(case mkt_seg_id when @ms21 then fcstedadrpickup end) as ms21_fcstedadrpickup
			,(case mkt_seg_id when @ms22 then fcstedadrcurrent end) as ms22_fcstedadrcurrent
			,(case mkt_seg_id when @ms22 then fcstedadrpickup end) as ms22_fcstedadrpickup
			,(case mkt_seg_id when @ms23 then fcstedadrcurrent end) as ms23_fcstedadrcurrent
			,(case mkt_seg_id when @ms23 then fcstedadrpickup end) as ms23_fcstedadrpickup
			,(case mkt_seg_id when @ms24 then fcstedadrcurrent end) as ms24_fcstedadrcurrent
			,(case mkt_seg_id when @ms24 then fcstedadrpickup end) as ms24_fcstedadrpickup
			,(case mkt_seg_id when @ms25 then fcstedadrcurrent end) as ms25_fcstedadrcurrent
			,(case mkt_seg_id when @ms25 then fcstedadrpickup end) as ms25_fcstedadrpickup
			,(case mkt_seg_id when @ms26 then fcstedadrcurrent end) as ms26_fcstedadrcurrent
			,(case mkt_seg_id when @ms26 then fcstedadrpickup end) as ms26_fcstedadrpickup
			,(case mkt_seg_id when @ms27 then fcstedadrcurrent end) as ms27_fcstedadrcurrent
			,(case mkt_seg_id when @ms27 then fcstedadrpickup end) as ms27_fcstedadrpickup
			,(case mkt_seg_id when @ms28 then fcstedadrcurrent end) as ms28_fcstedadrcurrent
			,(case mkt_seg_id when @ms28 then fcstedadrpickup end) as ms28_fcstedadrpickup
			,(case mkt_seg_id when @ms29 then fcstedadrcurrent end) as ms29_fcstedadrcurrent
			,(case mkt_seg_id when @ms29 then fcstedadrpickup end) as ms29_fcstedadrpickup
			,(case mkt_seg_id when @ms30 then fcstedadrcurrent end) as ms30_fcstedadrcurrent
			,(case mkt_seg_id when @ms30 then fcstedadrpickup end) as ms30_fcstedadrpickup
			,(case mkt_seg_id when @ms31 then fcstedadrcurrent end) as ms31_fcstedadrcurrent
			,(case mkt_seg_id when @ms31 then fcstedadrpickup end) as ms31_fcstedadrpickup
			,(case mkt_seg_id when @ms32 then fcstedadrcurrent end) as ms32_fcstedadrcurrent
			,(case mkt_seg_id when @ms32 then fcstedadrpickup end) as ms32_fcstedadrpickup
			,(case mkt_seg_id when @ms33 then fcstedadrcurrent end) as ms33_fcstedadrcurrent
			,(case mkt_seg_id when @ms33 then fcstedadrpickup end) as ms33_fcstedadrpickup
			,(case mkt_seg_id when @ms34 then fcstedadrcurrent end) as ms34_fcstedadrcurrent
			,(case mkt_seg_id when @ms34 then fcstedadrpickup end) as ms34_fcstedadrpickup
			,(case mkt_seg_id when @ms35 then fcstedadrcurrent end) as ms35_fcstedadrcurrent
			,(case mkt_seg_id when @ms35 then fcstedadrpickup end) as ms35_fcstedadrpickup
			,(case mkt_seg_id when @ms36 then fcstedadrcurrent end) as ms36_fcstedadrcurrent
			,(case mkt_seg_id when @ms36 then fcstedadrpickup end) as ms36_fcstedadrpickup
			,(case mkt_seg_id when @ms37 then fcstedadrcurrent end) as ms37_fcstedadrcurrent
			,(case mkt_seg_id when @ms37 then fcstedadrpickup end) as ms37_fcstedadrpickup
			,(case mkt_seg_id when @ms38 then fcstedadrcurrent end) as ms38_fcstedadrcurrent
			,(case mkt_seg_id when @ms38 then fcstedadrpickup end) as ms38_fcstedadrpickup
			,(case mkt_seg_id when @ms39 then fcstedadrcurrent end) as ms39_fcstedadrcurrent
			,(case mkt_seg_id when @ms39 then fcstedadrpickup end) as ms39_fcstedadrpickup
			,(case mkt_seg_id when @ms40 then fcstedadrcurrent end) as ms40_fcstedadrcurrent
			,(case mkt_seg_id when @ms40 then fcstedadrpickup end) as ms40_fcstedadrpickup
			,(case mkt_seg_id when @ms41 then fcstedadrcurrent end) as ms41_fcstedadrcurrent
			,(case mkt_seg_id when @ms41 then fcstedadrpickup end) as ms41_fcstedadrpickup
			,(case mkt_seg_id when @ms42 then fcstedadrcurrent end) as ms42_fcstedadrcurrent
			,(case mkt_seg_id when @ms42 then fcstedadrpickup end) as ms42_fcstedadrpickup
			,(case mkt_seg_id when @ms43 then fcstedadrcurrent end) as ms43_fcstedadrcurrent
			,(case mkt_seg_id when @ms43 then fcstedadrpickup end) as ms43_fcstedadrpickup
			,(case mkt_seg_id when @ms44 then fcstedadrcurrent end) as ms44_fcstedadrcurrent
			,(case mkt_seg_id when @ms44 then fcstedadrpickup end) as ms44_fcstedadrpickup
			,(case mkt_seg_id when @ms45 then fcstedadrcurrent end) as ms45_fcstedadrcurrent
			,(case mkt_seg_id when @ms45 then fcstedadrpickup end) as ms45_fcstedadrpickup
			,(case mkt_seg_id when @ms46 then fcstedadrcurrent end) as ms46_fcstedadrcurrent
			,(case mkt_seg_id when @ms46 then fcstedadrpickup end) as ms46_fcstedadrpickup
			,(case mkt_seg_id when @ms47 then fcstedadrcurrent end) as ms47_fcstedadrcurrent
			,(case mkt_seg_id when @ms47 then fcstedadrpickup end) as ms47_fcstedadrpickup
			,(case mkt_seg_id when @ms48 then fcstedadrcurrent end) as ms48_fcstedadrcurrent
			,(case mkt_seg_id when @ms48 then fcstedadrpickup end) as ms48_fcstedadrpickup
			,(case mkt_seg_id when @ms49 then fcstedadrcurrent end) as ms49_fcstedadrcurrent
			,(case mkt_seg_id when @ms49 then fcstedadrpickup end) as ms49_fcstedadrpickup
			,(case mkt_seg_id when @ms50 then fcstedadrcurrent end) as ms50_fcstedadrcurrent
			,(case mkt_seg_id when @ms50 then fcstedadrpickup end) as ms50_fcstedadrpickup
			
			--Archana added for group block and group pickup-Start
			,(case mkt_seg_id when @ms1 then block end) as ms1_block,(case mkt_seg_id when @ms1 then block_pickup end) as ms1_block_pickup,(case mkt_seg_id when @ms1 then block_available end) as ms1_block_available
			,(case mkt_seg_id when @ms2 then block end) as ms2_block,(case mkt_seg_id when @ms2 then block_pickup end) as ms2_block_pickup,(case mkt_seg_id when @ms2 then block_available end) as ms2_block_available
			,(case mkt_seg_id when @ms3 then block end) as ms3_block,(case mkt_seg_id when @ms3 then block_pickup end) as ms3_block_pickup,(case mkt_seg_id when @ms3 then block_available end) as ms3_block_available
			,(case mkt_seg_id when @ms4 then block end) as ms4_block,(case mkt_seg_id when @ms4 then block_pickup end) as ms4_block_pickup,(case mkt_seg_id when @ms4 then block_available end) as ms4_block_available
			,(case mkt_seg_id when @ms5 then block end) as ms5_block,(case mkt_seg_id when @ms5 then block_pickup end) as ms5_block_pickup,(case mkt_seg_id when @ms5 then block_available end) as ms5_block_available
			,(case mkt_seg_id when @ms6 then block end) as ms6_block,(case mkt_seg_id when @ms6 then block_pickup end) as ms6_block_pickup,(case mkt_seg_id when @ms6 then block_available end) as ms6_block_available
			,(case mkt_seg_id when @ms7 then block end) as ms7_block,(case mkt_seg_id when @ms7 then block_pickup end) as ms7_block_pickup,(case mkt_seg_id when @ms7 then block_available end) as ms7_block_available
			,(case mkt_seg_id when @ms8 then block end) as ms8_block,(case mkt_seg_id when @ms8 then block_pickup end) as ms8_block_pickup,(case mkt_seg_id when @ms8 then block_available end) as ms8_block_available
			,(case mkt_seg_id when @ms9 then block end) as ms9_block,(case mkt_seg_id when @ms9 then block_pickup end) as ms9_block_pickup,(case mkt_seg_id when @ms9 then block_available end) as ms9_block_available
			,(case mkt_seg_id when @ms10 then block end) as ms10_block,(case mkt_seg_id when @ms10 then block_pickup end) as ms10_block_pickup,(case mkt_seg_id when @ms10 then block_available end) as ms10_block_available
			,(case mkt_seg_id when @ms11 then block end) as ms11_block,(case mkt_seg_id when @ms11 then block_pickup end) as ms11_block_pickup,(case mkt_seg_id when @ms11 then block_available end) as ms11_block_available
			,(case mkt_seg_id when @ms12 then block end) as ms12_block,(case mkt_seg_id when @ms12 then block_pickup end) as ms12_block_pickup,(case mkt_seg_id when @ms12 then block_available end) as ms12_block_available
			,(case mkt_seg_id when @ms13 then block end) as ms13_block,(case mkt_seg_id when @ms13 then block_pickup end) as ms13_block_pickup,(case mkt_seg_id when @ms13 then block_available end) as ms13_block_available
			,(case mkt_seg_id when @ms14 then block end) as ms14_block,(case mkt_seg_id when @ms14 then block_pickup end) as ms14_block_pickup,(case mkt_seg_id when @ms14 then block_available end) as ms14_block_available
			,(case mkt_seg_id when @ms15 then block end) as ms15_block,(case mkt_seg_id when @ms15 then block_pickup end) as ms15_block_pickup,(case mkt_seg_id when @ms15 then block_available end) as ms15_block_available
			,(case mkt_seg_id when @ms16 then block end) as ms16_block,(case mkt_seg_id when @ms16 then block_pickup end) as ms16_block_pickup,(case mkt_seg_id when @ms16 then block_available end) as ms16_block_available
			,(case mkt_seg_id when @ms17 then block end) as ms17_block,(case mkt_seg_id when @ms17 then block_pickup end) as ms17_block_pickup,(case mkt_seg_id when @ms17 then block_available end) as ms17_block_available
			,(case mkt_seg_id when @ms18 then block end) as ms18_block,(case mkt_seg_id when @ms18 then block_pickup end) as ms18_block_pickup,(case mkt_seg_id when @ms18 then block_available end) as ms18_block_available
			,(case mkt_seg_id when @ms19 then block end) as ms19_block,(case mkt_seg_id when @ms19 then block_pickup end) as ms19_block_pickup,(case mkt_seg_id when @ms19 then block_available end) as ms19_block_available
			,(case mkt_seg_id when @ms20 then block end) as ms20_block,(case mkt_seg_id when @ms20 then block_pickup end) as ms20_block_pickup,(case mkt_seg_id when @ms20 then block_available end) as ms20_block_available
			,(case mkt_seg_id when @ms21 then block end) as ms21_block,(case mkt_seg_id when @ms21 then block_pickup end) as ms21_block_pickup,(case mkt_seg_id when @ms21 then block_available end) as ms21_block_available
			,(case mkt_seg_id when @ms22 then block end) as ms22_block,(case mkt_seg_id when @ms22 then block_pickup end) as ms22_block_pickup,(case mkt_seg_id when @ms22 then block_available end) as ms22_block_available
			,(case mkt_seg_id when @ms23 then block end) as ms23_block,(case mkt_seg_id when @ms23 then block_pickup end) as ms23_block_pickup,(case mkt_seg_id when @ms23 then block_available end) as ms23_block_available
			,(case mkt_seg_id when @ms24 then block end) as ms24_block,(case mkt_seg_id when @ms24 then block_pickup end) as ms24_block_pickup,(case mkt_seg_id when @ms24 then block_available end) as ms24_block_available
			,(case mkt_seg_id when @ms25 then block end) as ms25_block,(case mkt_seg_id when @ms25 then block_pickup end) as ms25_block_pickup,(case mkt_seg_id when @ms25 then block_available end) as ms25_block_available
			,(case mkt_seg_id when @ms26 then block end) as ms26_block,(case mkt_seg_id when @ms26 then block_pickup end) as ms26_block_pickup,(case mkt_seg_id when @ms26 then block_available end) as ms26_block_available
			,(case mkt_seg_id when @ms27 then block end) as ms27_block,(case mkt_seg_id when @ms27 then block_pickup end) as ms27_block_pickup,(case mkt_seg_id when @ms27 then block_available end) as ms27_block_available
			,(case mkt_seg_id when @ms28 then block end) as ms28_block,(case mkt_seg_id when @ms28 then block_pickup end) as ms28_block_pickup,(case mkt_seg_id when @ms28 then block_available end) as ms28_block_available
			,(case mkt_seg_id when @ms29 then block end) as ms29_block,(case mkt_seg_id when @ms29 then block_pickup end) as ms29_block_pickup,(case mkt_seg_id when @ms29 then block_available end) as ms29_block_available
			,(case mkt_seg_id when @ms30 then block end) as ms30_block,(case mkt_seg_id when @ms30 then block_pickup end) as ms30_block_pickup,(case mkt_seg_id when @ms30 then block_available end) as ms30_block_available
			,(case mkt_seg_id when @ms31 then block end) as ms31_block,(case mkt_seg_id when @ms31 then block_pickup end) as ms31_block_pickup,(case mkt_seg_id when @ms31 then block_available end) as ms31_block_available
			,(case mkt_seg_id when @ms32 then block end) as ms32_block,(case mkt_seg_id when @ms32 then block_pickup end) as ms32_block_pickup,(case mkt_seg_id when @ms32 then block_available end) as ms32_block_available
			,(case mkt_seg_id when @ms33 then block end) as ms33_block,(case mkt_seg_id when @ms33 then block_pickup end) as ms33_block_pickup,(case mkt_seg_id when @ms33 then block_available end) as ms33_block_available
			,(case mkt_seg_id when @ms34 then block end) as ms34_block,(case mkt_seg_id when @ms34 then block_pickup end) as ms34_block_pickup,(case mkt_seg_id when @ms34 then block_available end) as ms34_block_available
			,(case mkt_seg_id when @ms35 then block end) as ms35_block,(case mkt_seg_id when @ms35 then block_pickup end) as ms35_block_pickup,(case mkt_seg_id when @ms35 then block_available end) as ms35_block_available
			,(case mkt_seg_id when @ms36 then block end) as ms36_block,(case mkt_seg_id when @ms36 then block_pickup end) as ms36_block_pickup,(case mkt_seg_id when @ms36 then block_available end) as ms36_block_available
			,(case mkt_seg_id when @ms37 then block end) as ms37_block,(case mkt_seg_id when @ms37 then block_pickup end) as ms37_block_pickup,(case mkt_seg_id when @ms37 then block_available end) as ms37_block_available
			,(case mkt_seg_id when @ms38 then block end) as ms38_block,(case mkt_seg_id when @ms38 then block_pickup end) as ms38_block_pickup,(case mkt_seg_id when @ms38 then block_available end) as ms38_block_available
			,(case mkt_seg_id when @ms39 then block end) as ms39_block,(case mkt_seg_id when @ms39 then block_pickup end) as ms39_block_pickup,(case mkt_seg_id when @ms39 then block_available end) as ms39_block_available
			,(case mkt_seg_id when @ms40 then block end) as ms40_block,(case mkt_seg_id when @ms40 then block_pickup end) as ms40_block_pickup,(case mkt_seg_id when @ms40 then block_available end) as ms40_block_available
			,(case mkt_seg_id when @ms41 then block end) as ms41_block,(case mkt_seg_id when @ms41 then block_pickup end) as ms41_block_pickup,(case mkt_seg_id when @ms41 then block_available end) as ms41_block_available
			,(case mkt_seg_id when @ms42 then block end) as ms42_block,(case mkt_seg_id when @ms42 then block_pickup end) as ms42_block_pickup,(case mkt_seg_id when @ms42 then block_available end) as ms42_block_available
			,(case mkt_seg_id when @ms43 then block end) as ms43_block,(case mkt_seg_id when @ms43 then block_pickup end) as ms43_block_pickup,(case mkt_seg_id when @ms43 then block_available end) as ms43_block_available
			,(case mkt_seg_id when @ms44 then block end) as ms44_block,(case mkt_seg_id when @ms44 then block_pickup end) as ms44_block_pickup,(case mkt_seg_id when @ms44 then block_available end) as ms44_block_available
			,(case mkt_seg_id when @ms45 then block end) as ms45_block,(case mkt_seg_id when @ms45 then block_pickup end) as ms45_block_pickup,(case mkt_seg_id when @ms45 then block_available end) as ms45_block_available
			,(case mkt_seg_id when @ms46 then block end) as ms46_block,(case mkt_seg_id when @ms46 then block_pickup end) as ms46_block_pickup,(case mkt_seg_id when @ms46 then block_available end) as ms46_block_available
			,(case mkt_seg_id when @ms47 then block end) as ms47_block,(case mkt_seg_id when @ms47 then block_pickup end) as ms47_block_pickup,(case mkt_seg_id when @ms47 then block_available end) as ms47_block_available
			,(case mkt_seg_id when @ms48 then block end) as ms48_block,(case mkt_seg_id when @ms48 then block_pickup end) as ms48_block_pickup,(case mkt_seg_id when @ms48 then block_available end) as ms48_block_available
			,(case mkt_seg_id when @ms49 then block end) as ms49_block,(case mkt_seg_id when @ms49 then block_pickup end) as ms49_block_pickup,(case mkt_seg_id when @ms49 then block_available end) as ms49_block_available
			,(case mkt_seg_id when @ms50 then block end) as ms50_block,(case mkt_seg_id when @ms50 then block_pickup end) as ms50_block_pickup,(case mkt_seg_id when @ms50 then block_available end) as ms50_block_available
			--Archana added for group block and group pickup-End
		
			from
			(
				select
					base.occupancy_dt,
					base.mkt_seg_id,
					datename(dw,base.occupancy_dt) as dow,
					isnull(a.rooms_sold,0.0) as roomsoldcurrent,
					isnull(a.room_revenue,0.0) as bookedroomrevenuecurrent,
					isnull(a.adr,0.0) as bookedadrcurrent,
					isnull(b.occupancy_forecast_current,0.0) as occfcstcurrent,
					isnull(b.fcsted_room_revenue_current,0.0) as fcstedroomrevenuecurrent,
					isnull(b.estimated_adr_current,0.0) as fcstedadrcurrent,
					cast(isnull(c.end_date_rooms_sold,isnull(a.rooms_sold,0.0)) - ISNULL(c.start_date_rooms_sold, isnull(a.rooms_sold,0.0)) as int) roomssoldpickup,
					cast(isnull(d.end_date_occupancy_nbr,isnull(b.occupancy_forecast_current,0.0)) - ISNULL(d.start_date_occupancy_nbr, isnull(b.occupancy_forecast_current,0.0)) as numeric (19,2)) occfcstpickup,
					cast(isnull(c.end_date_room_revenue,isnull(a.room_revenue,0.0)) - ISNULL(c.start_date_room_revenue, isnull(a.room_revenue,0.0)) as numeric (19,2)) bookedroomrevenuepickup,
					cast(isnull(d.end_date_revenue,isnull(b.fcsted_room_revenue_current,0.0)) - ISNULL(d.start_date_revenue, isnull(b.fcsted_room_revenue_current,0.0)) as numeric (19,2)) fcstedroomrevenuepickup,
					cast(isnull(c.end_date_adr,isnull(a.adr,0.0)) - ISNULL(c.start_date_adr, isnull(a.adr,0.0)) as numeric (19,2)) bookedadrpickup,
					cast(isnull(d.end_date_adr,isnull(b.estimated_adr_current,0.0)) - ISNULL(d.start_date_adr, isnull(b.estimated_adr_current,0.0)) as numeric (19,2)) fcstedadrpickup,
					groupBlock.block,
					groupBlock.block_pickup,
					groupBlock.block_available
				from
				(
					select @property_id property_id,CAST(calendar_date as date) Occupancy_DT,mkt_seg_id  from calendar_dim left join @tempMS on mkt_seg_id is not null
					 where calendar_date between @start_date and @end_date
				)base left join
				(
					select * from ufn_get_activity_by_individual_ms (@property_id,@mkt_seg_id,@start_date,@end_date)
				) as a on base.property_id=a.property_id and base.occupancy_dt=a.occupancy_dt and base.mkt_seg_id=a.mkt_seg_id
				left join
					#occupancy_forecast_by_individual_ms as b 
					on base.property_id=b.property_id and base.occupancy_dt=b.occupancy_dt and base.mkt_seg_id=b.mkt_seg_id
				left join
					#activity_asof_businessdate_by_individual_ms as c
					on base.property_id=c.property_id and base.occupancy_dt=c.occupancy_dt and base.mkt_seg_id=c.mkt_seg_id
				left join
					#occupancy_forecast_asof_businessdate_by_individual_ms as d
					on base.property_id=d.property_id and base.occupancy_dt=d.occupancy_dt and base.mkt_seg_id=d.mkt_seg_id
				--Archana added group block-Aggregated-START
				left join
				(
					select * from ufn_get_groupBlock_groupPickup_by_MarketSegment (@property_id,@mkt_seg_id,@start_date, @end_date,0)
				) as groupBlock on
				a.property_id=groupBlock.property_id and a.occupancy_dt=groupBlock.occupancy_dt and a.mkt_seg_id=groupBlock.marketSegment_id 
				--Archana added group block-Aggregated-END
			) data
		)data2	
		group by occupancy_dt,dow
		order by occupancy_dt
	
end
GO





