DROP PROCEDURE IF EXISTS [opera].[USP_History_Transaction_Insert]
GO
CREATE PROCEDURE [opera].USP_History_Transaction_Insert
       @History_Transaction_Batch [opera].History_Transaction_Batch READONLY
AS
BEGIN
    INSERT INTO [opera].[History_Transaction] ([Confirmation_Number],[Reservation_Status],[Is_Shared],[Sharers],[Transaction_DT],[Arrival_DT],[Departure_DT],[Checkout_DT],[Cancellation_DT],[Booking_DT],[Rate_Code],[Rate_Amount],
       [Market_Code],[Room],[Room_Revenue] ,[Food_Beverage_Revenue],[Other_Revenue],[Total_Revenue],[Room_Type],[Source_Code],[Channel],[Booked_Room_Type],[Nationality],[Reservation_Type],[Number_Children],[Number_Adults],[Reservation_Name_ID],
       [Data_Load_Metadata_ID],[Update_DTTM],[Rate_Category],[Booking_TM])
    SELECT [Confirmation_Number],[Reservation_Status],[Is_Shared],[Sharers],[Transaction_DT],[Arrival_DT],[Departure_DT],[Checkout_DT],[Cancellation_DT],[Booking_DT],[Rate_Code],[Rate_Amount],[Market_Code],[Room],[Room_Revenue],
[Food_Beverage_Revenue],[Other_Revenue],[Total_Revenue],[Room_Type],[Source_Code],[Channel],[Booked_Room_Type],[Nationality],[Reservation_Type],[Number_Children],[Number_Adults],[Reservation_Name_ID],[Data_Load_Metadata_ID],
       [Update_DTTM],[Rate_Category],[Booking_TM] FROM @History_Transaction_Batch;
END
GO