IF EXISTS(SELECT *
          FROM sys.objects
          WHERE object_id = OBJECT_ID(N'[dbo].[usp_dataextraction_report_fg_st19]')
            AND type IN (N'P', N'PC'))
    DROP PROCEDURE [dbo].[usp_dataextraction_report_fg_st19]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


CREATE PROCEDURE [dbo].[usp_dataextraction_report_fg_st19](
    @property_id INT,
    @ly_start_date DATE,
    @ly_end_date DATE,
    @ly_businessdate DATE,
    @day_diff INT,
    @MktExcludeCompFlag VARCHAR(5)
)
AS
BEGIN

    SELECT Occupancy_DT,
           Forecast_Group_Name,
           SUM(rooms_sold)   AS rooms_sold,
           SUM(room_revenue) AS room_revenue,
           sum(total_profit) AS profit
    FROM (
             SELECT pace.property_Id,
                    fg.Forecast_Group_ID,
                    fg.Forecast_Group_Name,
                    DateAdd(DAY, @day_diff, pace.Occupancy_DT) AS Occupancy_DT,
                    pace.rooms_sold,
                    pace.room_revenue,
                    pace.Total_Profit
             FROM PACE_Mkt_Activity pace
                      JOIN Mkt_Seg mkt
                           ON mkt.Property_ID = pace.property_Id
                               AND mkt.Mkt_Seg_ID = pace.Mkt_Seg_ID
                               AND mkt.Exclude_CompHouse_Data_Display IN
                                   (SELECT value FROM VARCHARTOINT(@MktExcludeCompFlag, ','))
                      JOIN Mkt_Seg_Forecast_Group fgmkt
                           ON fgmkt.Mkt_Seg_ID = mkt.Mkt_Seg_ID
                               AND fgmkt.status_id = 1
                      JOIN Forecast_Group fg
                           ON fg.property_Id = pace.property_Id
                               AND fg.forecast_group_id = fgmkt.forecast_group_id
                               AND fg.status_id = 1
             WHERE pace.Occupancy_DT BETWEEN @ly_start_date AND @ly_end_date
               AND pace.Property_ID = @property_id
               AND pace.Business_Day_End_DT = @ly_businessdate
         ) AS fgstly
    GROUP BY Occupancy_DT, Forecast_Group_Name
    ORDER BY Occupancy_DT, Forecast_Group_Name

END
GO
