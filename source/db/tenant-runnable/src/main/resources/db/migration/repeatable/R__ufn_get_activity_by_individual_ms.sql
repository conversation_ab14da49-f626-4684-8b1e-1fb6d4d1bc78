if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_activity_by_individual_ms]'))
    drop function [ufn_get_activity_by_individual_ms]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Function Name: ufn_get_activity_by_individual_ms

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@mkt_seg_id --> list of comma seperated MSs
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	
Ouput Parameter : NA

Execution: this is just an example
	Example 1: Report at market segment level 
	-----------------------------------------
	select * from dbo.ufn_get_activity_by_individual_ms (18,'84','2011-07-01','2011-07-31')
	
	select * from dbo.ufn_get_activity_by_individual_ms (18,'81,84','2011-07-01','2011-07-31')
	

	
Purpose: The purpose of this function is to report 'rooms_sold','adr' and 'room_revenue' values by 
		 market segment for a given occupancy date range. 

Assumptions : Please enclose the market segment ids (@mkt_seg_id) in single quotes.
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
06/07/2012		Atul				Shendye					Initial Version
12/05/2022		Vikas 			    Shivankar				FEYNMAN-1348 :Increase the size of MS input parameter to 1500 characters
***************************************************************************************/

create function [dbo].[ufn_get_activity_by_individual_ms]

(
		@property_id int,
		@mkt_seg_id varchar(1500),
		@start_date date,
		@end_date date
)		
returns  @adr_room_rev table
	(	
		occupancy_dt	date,
		property_id	int,
		mkt_seg_id int,
		rooms_sold	numeric(18,0),
		room_revenue	numeric(19,5),
		adr	numeric(19,5)
	)
as
begin
		insert into @adr_room_rev
		select 
			
			occupancy_dt,
			mac.property_id,
			mkt_seg_id,
			sum(rooms_sold) as rooms_sold, 
			sum(room_revenue) as room_revenue,
			adr =
				case (sum(rooms_sold))
					when 0 then 0
				else
					sum(room_revenue)/sum(rooms_sold)
				end
		from mkt_accom_activity mac inner join Accom_Type at on mac.Accom_Type_Id = at.Accom_Type_ID and at.isComponentRoom = 'N'
		where mac.Property_ID=@property_id and occupancy_dt between @start_date and @end_date
		and mkt_seg_id in (
							select value from varchartoint(@mkt_seg_id,',')
						  )
		group by mac.property_id,occupancy_dt,mkt_seg_id
		order by occupancy_dt,mkt_seg_id
	return
end