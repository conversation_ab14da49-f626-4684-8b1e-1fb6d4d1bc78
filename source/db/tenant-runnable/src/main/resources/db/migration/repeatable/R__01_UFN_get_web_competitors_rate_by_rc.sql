if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_web_competitors_rate_by_rc]'))
drop function [ufn_get_web_competitors_rate_by_rc]

GO
/****** Object:  UserDefinedFunction [dbo].[ufn_get_web_competitors_rate_by_rc]    Script Date: 11/27/2019 2:26:10 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
 
/*************************************************************************************

Function Name: ufn_get_web_competitors_rate_by_rc

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@webrate_comp_id --> id associated with a webrate competitor
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@RoomClasses --> Room classes for which we need the competitor information.
	
Ouput Parameter : NA

Execution: this is just an example
	select * from dbo.ufn_get_web_competitors_rate_by_rc (18,132,'2011-07-01', '2011-07-31','64,61') --> to get comp info for room class 64 and 61
	select * from dbo.ufn_get_web_competitors_rate_by_rc (18,132,'2011-07-01', '2011-07-31','64') --> to get comp info for room class 64
	
Purpose: The purpose of this function is to extract competitors webrates for a given property and competitor id. OR for given room class and competitor id

Assumptions : NA
		 
Author: Atul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
03/23/2012	Atul				Shendye					Initial Version
3/26/2020     Kyle       Vierkant       updated to return webrate_ratevalue with and without tax
4/3/2020      Kyle       Vierkant       updated to add @isAdjustRSSForTaxEnabled parameter
8/4/2020      Kyle          Vierkant      updated to use webrate display value; leaving @isAdjustRSSForTaxEnabled parameter for now
8/20/2020      Kyle          Vierkant      Removed @isAdjustRSSForTaxEnabled parameter
08/11/2023	Shrey				Vegda					Adding rate shopping rate type condition
***************************************************************************************/

CREATE function [dbo].[ufn_get_web_competitors_rate_by_rc]
(
		@property_id int,
		@webrate_comp_id int,
		@start_date date,
		@end_date date,
		@RoomClasses nvarchar(200)
)		
returns  @web_rate_comp table
	(	
		property_id	int,
		accom_Class_id	int,
		occupancy_dt date,
		webrate_currency nvarchar(50),
		webrate_ratevalue numeric(19,5),
		webrate_competitors_name nvarchar(150)
	)
as
begin
		declare @webrateTypeID int;
		set @webrateTypeID = (select top 1 wtp.Webrate_Type_ID from Webrate_Type_Product wtp where wtp.Product_ID=1);

		insert into @web_rate_comp
		select 
		name.property_id,
		name.accom_Class_id, 
		name.occupancy_dt, 
		webrate_currency,
		CAST(webrate_ratevalue_display as NUMERIC(19,2)),
		name.webrate_competitors_name 
		from 
		( 
			select a.property_id,a.Webrate_Competitors_Name,b.Occupancy_DT,Accom_Class_id,a.Webrate_Competitors_ID from
			( 
				select @property_id as property_id,webrate_competitors_name,Accom_Class_id,Webrate_Competitors_ID from Webrate_Competitors
				inner join
				(select Accom_Class_id from Accom_Class where Status_ID=1 and System_Default=0 and Accom_Class_id in (SELECT Value FROM varcharToInt(@RoomClasses,',')) )acc
				on property_id = @property_id
				where Webrate_Competitors_ID=@webrate_comp_id and status_id in (1,2,3)
			) a left join 
			( 
				select Property_ID,Occupancy_DT from Total_Activity where Property_ID=@property_id and Occupancy_DT between @start_date and @end_date 
			) b on a.property_id=b.Property_ID 
		) name left join 
		( 
			select * 
			from dbo.vw_webrate_channel 
			where occupancy_dt between @start_date and @end_date and property_id=@property_id 
		) channel on name.property_id=channel.property_id and name.Occupancy_DT=channel.Occupancy_DT 
		left join 
		( 
			SELECT * FROM (
					select webrate_competitors_id,webrate_channel_id,occupancy_dt,webrate_currency
					,Accom_Class_ID
					,webrate_ratevalue_display
					,ROW_NUMBER() over (Partition by webrate_competitors_id,webrate_channel_id,occupancy_dt,Accom_Class_ID 
					order by webrate_competitors_id,webrate_channel_id,occupancy_dt,Accom_Class_ID,Webrate_GenerationDate desc,
					webrate_ratevalue_display asc) as rowNUm
				from dbo.webrate vweb 
				left join ACCom_CLass acc on
				acc.Accom_Class_ID in (
						select Accom_Class_id from Accom_Class where Status_ID=1 and System_Default=0 
						and Accom_Class_id in (SELECT Value FROM varcharToInt(@RoomClasses,',')) 
					)
					where webrate_competitors_id=@webrate_comp_id and occupancy_dt between @start_date and @end_date and LOS=1 
					and Webrate_status = 'A'
					and Webrate_Accom_Type_ID in 
					( select webT.webrate_Accom_Type_ID from webrate_accom_type webT inner join Webrate_Accom_Class_Mapping webM
													on webT.Webrate_Accom_Type_ID = webM.Webrate_Accom_Type_ID and webM.Accom_Class_ID = acc.Accom_Class_id and  webT.property_ID = @property_id)
					AND (@webrateTypeID is null or Webrate_Type_ID = @webrateTypeID)								
			)AS der_webRate
			WHERE rowNUm = 1
		) webrate_info 
		on channel.occupancy_dt=webrate_info.occupancy_dt and channel.channel_id=webrate_info.webrate_channel_id and name.Accom_Class_ID = webrate_info.Accom_Class_ID
		order by name.property_id,name.occupancy_dt
	return
end