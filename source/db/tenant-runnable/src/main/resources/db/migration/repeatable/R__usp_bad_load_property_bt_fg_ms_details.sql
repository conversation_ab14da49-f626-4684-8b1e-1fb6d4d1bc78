if exists (select * from sys.objects where object_id = object_id(N'[dbo].[usp_bad_load_property_bt_fg_ms_details]'))
drop procedure [dbo].[usp_bad_load_property_bt_fg_ms_details]
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Stored Procedure Name: usp_bad_load_property_bt_fg_ms_details

Input Parameters : 
	@property_id --> property id associated with a property (e.g.,'XNAES' id FROM the property table is 10027)
	@forecast_group_id --> forecast group associated with market segment
	@business_type_id --> forecast group id associated with business type id
	@start_date --> start date from which we need data ('2016-03-23')
	@end_date --> end date till which we need data ('2016-03-31')
	@business_date --> business run date for the selected property
	@ms_status_ids --> status id for market segment
	@comp_rooms_filter_flag --> comp filter is associated with market segment

Ouput Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_bad_load_property_bt_fg_ms_details 010027, 1, 1,'2016-03-23', '2016-03-31','2016-03-22','1,2,3,4,5',0

Purpose: The purpose of this procedure is to load all Business Type/Forecast Group details metrics of inventory group required for reference data dashboard details tab
		 
Author: Rahul Chavan

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
25/8/2017		Rahul				Chavan					Initial Version
---------------------------------------------------------------------------------------
05/11/2021		Adarsh				Vishwakarma					KALAM-4121
---------------------------------------------------------------------------------------
06/10/2021		Anil				Borgude					     7.3.3
***************************************************************************************/
CREATE procedure [dbo].[usp_bad_load_property_bt_fg_ms_details]
(
		@property_id int,
		@forecast_group_id int,
		@business_type_id int,
		@start_date date,
		@end_date date,
		@business_date date,
		@ms_status_ids varchar(16),
		@comp_rooms_filter_flag int
)
as
begin
	declare @caughtupdate date 

	-- extract caughtup date for a property 
	set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) 
	
	declare @exclude_comp_room varchar(5) = '0,1';
		if(@comp_rooms_filter_flag = 1)
			set @exclude_comp_room = '0';

		IF OBJECT_ID('tempdb..#temp_mkt_seg') IS NOT NULL
		BEGIN
		DROP TABLE #temp_mkt_seg
		END
		CREATE table #temp_mkt_seg
		(
			Property_ID int,
			Mkt_Seg_ID int,
			Mkt_Seg_Name nvarchar(100)
		)
		insert into #temp_mkt_seg
			select Property_ID, Mkt_Seg_ID, Mkt_Seg_Name
			from Mkt_Seg where Property_ID=@property_id and Status_ID in (select items from Split(@ms_status_ids , ',')) and
			 Exclude_CompHouse_Data_Display in (select value from varcharToInt(@exclude_comp_room, ','))
		
	--- Sum Market Segment data to the Business_Type_ID and join with Business Type to get name
	select ms.Mkt_Seg_ID, ms.Mkt_Seg_Name, sum(Rooms_Sold) Rooms_Sold, 
	cast(sum(Occupancy_Forecast) as numeric(8,1)) Occupancy_Forecast
	,ADR_OnBooks = cast(case(sum(Rooms_Sold)) when 0 then 0 else SUM(Revenue) / sum(Rooms_Sold) end as numeric(19,2))
	,ADR_Forecast = cast(case(sum(Occupancy_Forecast)) when 0 then 0 else sum(Revenue_Forecast) / sum(Occupancy_Forecast) end as numeric(19,2))
	,cast(sum(Revenue) as numeric(19,2)) Revenue,cast(sum(Revenue_Forecast) as numeric(19,2)) Revenue_Forecast,
	(SUM(Rooms_sold)- ISNULL(Rooms_Sold_buisness_dt, 0)) as Rooms_Sold_Pickup,
	(SUM(Revenue)- ISNULL(Rooms_Revenue_business_dt, 0)) as Rooms_Revenue_Pickup,
	cast((CASE WHEN ISNULL(SUM(Rooms_Sold),0) > 0 THEN  sum(Revenue)/SUM(Rooms_Sold) ELSE 0 END) 
		 - 
        (CASE WHEN ISNULL(Rooms_Sold_buisness_dt,0) > 0 THEN  ISNULL(Rooms_Revenue_business_dt, 0)/Rooms_Sold_buisness_dt ELSE 0 END)
		  as numeric(19,2)) as Adr_Pickup
	
	from (
		-- Get Market Segment Data 
		select activity.Mkt_Seg_ID,Rooms_Sold,Occupancy_Forecast,Revenue,Revenue_Forecast
		from (
			-- Market Segment Activity data
			select 
				Mkt_Seg_ID,
				Rooms_Sold,
				Room_Revenue Revenue
			from
			(
				select 
					maa.Mkt_Seg_ID,
					sum(maa.Rooms_Sold) Rooms_Sold,
					sum(maa.Room_Revenue) Room_Revenue
				from Mkt_Accom_Activity maa
				inner join Accom_Type at on maa.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
				inner join Mkt_Seg_Forecast_Group msfg on maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID
				and msfg.Forecast_Group_ID = @forecast_group_id
				inner join Mkt_Seg_Details msd on msd.Mkt_Seg_ID = maa.Mkt_Seg_ID
				inner join #temp_mkt_seg ms ON ms.Mkt_Seg_ID = maa.Mkt_Seg_ID
				and msd.Business_Type_ID = @business_type_id
				where maa.Property_Id=@property_id 
				and msfg.Status_ID = 1
				and maa.Occupancy_DT between @start_date and @end_date
				group by maa.Mkt_Seg_ID
			) activity_type
		) as activity
		left join
		(
			-- Get the Market Segment Occupancy Forecast
			select
				 fnATMS.Mkt_Seg_ID,
				 sum(Occupancy_NBR) as Occupancy_Forecast,
				 sum(Revenue) as Revenue_Forecast
				 from FN_AT_MS_Occupancy(@caughtupdate, 1) fnATMS
				 inner join Accom_Type at on fnATMS.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
				 inner join Mkt_Seg_Details msd on msd.Mkt_Seg_ID = fnATMS.Mkt_Seg_ID
				 and msd.Business_Type_ID = @business_type_id
				 where fnATMS.Property_ID=@property_id and Occupancy_DT between @start_date and @end_date
				 group by fnATMS.Mkt_Seg_ID
		) as occ_forecast on occ_forecast.Mkt_Seg_ID = activity.Mkt_Seg_ID
	) mkt_seg_totals 
	inner join #temp_mkt_seg ms on mkt_seg_totals.Mkt_Seg_ID = ms.Mkt_Seg_ID
	left join 
	(
		select
			Mkt_Seg_ID,
			sum(Rooms_Sold_buisness_dt) Rooms_Sold_buisness_dt,
			sum(Rooms_Revenue_business_dt) Rooms_Revenue_business_dt
		from (
				select
				pma.Mkt_Seg_ID,
				sum(pma.Rooms_Sold) Rooms_Sold_buisness_dt,
				sum(pma.Room_Revenue) Rooms_Revenue_business_dt
				from 
				PACE_Mkt_Activity pma 
				inner join Mkt_Seg_Forecast_Group msfg on pma.Mkt_Seg_ID = msfg.Mkt_Seg_ID and msfg.Status_ID = 1
				and msfg.Forecast_Group_ID = @forecast_group_id
				inner join Mkt_Seg_Details msd on msd.Mkt_Seg_ID = pma.Mkt_Seg_ID
				inner join #temp_mkt_seg ms ON ms.Mkt_Seg_ID = pma.Mkt_Seg_ID
				and msd.Business_Type_ID = @business_type_id
				where pma.Property_Id=@property_id 
				and pma.Business_Day_End_DT = @business_date
				and pma.Occupancy_DT between @start_date and @end_date
				and Occupancy_DT > @business_date
				group by pma.Mkt_Seg_ID	
				union all 
				select 
					maa.Mkt_Seg_ID,
					sum(Rooms_Sold)Rooms_Sold_buisness_dt,
					sum(Room_Revenue) Rooms_Revenue_business_dt
				from Mkt_Accom_Activity maa
				inner join Accom_Type at on maa.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
				inner join Mkt_Seg_Forecast_Group msfg on maa.Mkt_Seg_ID = msfg.Mkt_Seg_ID and msfg.Status_ID = 1
				inner join #temp_mkt_seg ms ON ms.Mkt_Seg_ID = maa.Mkt_Seg_ID
				and msfg.Forecast_Group_ID = @forecast_group_id
				inner join Mkt_Seg_Details msd on msd.Mkt_Seg_ID = maa.Mkt_Seg_ID
				and msd.Business_Type_ID = @business_type_id
				where maa.Property_Id=@property_id 
					and maa.Occupancy_DT between @start_date and @end_date
					and Occupancy_DT <= @business_date
				group by maa.Mkt_Seg_ID
		) temp group by Mkt_Seg_ID		
	) pace_activity on mkt_seg_totals.Mkt_Seg_ID = pace_activity.Mkt_Seg_ID
	group by ms.Mkt_Seg_ID, ms.Mkt_Seg_Name,Rooms_Sold_buisness_dt,Rooms_Revenue_business_dt
	order by ms.Mkt_Seg_Name
	
	DROP TABLE #temp_mkt_seg
end
;