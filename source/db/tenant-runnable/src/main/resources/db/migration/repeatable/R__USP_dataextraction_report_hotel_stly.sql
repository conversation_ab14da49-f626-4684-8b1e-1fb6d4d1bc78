if exists (select * from sys.objects where object_id = object_id(N'[usp_dataextraction_report_hotel_stly]'))
drop procedure [usp_dataextraction_report_hotel_stly]
GO
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE procedure [dbo].[usp_dataextraction_report_hotel_stly]
(
    @property_id int,
	@ly_start_date date,
	@ly_end_date date,
	@ly_businessdate date,
	@is_stly_or_2y int,
    @isExcludeCompRoom int,
	@includeZeroCapacityRT int,
	@includePseudoRT int
)
as
begin

    declare @ExcludeCompRoomFlag varchar(5) = '0,1';
    if (@isExcludeCompRoom = 1)
        set @ExcludeCompRoomFlag = '0';

	select
		ta.Occupancy_DT,
		ta.rooms_sold,
		Group_RoomSold,
		Tran_RoomSold,
		ta.room_revenue as onBooks_Revenue,
		ta.Total_Profit as profit
		from
		(
			select
                (case when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT) else DateAdd(WEEK, 104, pace.Occupancy_DT) end) as Occupancy_DT,
                (
					pace.rooms_sold - iif(@isExcludeCompRoom = 1, isNull(pma.Rooms_Sold, 0), 0)
					- iif(@includeZeroCapacityRT = 0 OR @includePseudoRT = 0, isNull(paa.Rooms_Sold, 0), 0)
				) as rooms_sold,
                (
					pace.room_revenue - iif(@isExcludeCompRoom = 1, isNull(pma.room_revenue, 0), 0)
					- iif(@includeZeroCapacityRT = 0 OR @includePseudoRT = 0, isNull(paa.room_revenue, 0), 0)
				) as room_revenue,
                (
					pace.Total_Profit - iif(@isExcludeCompRoom = 1, isNull(pma.Total_Profit, 0), 0)
					- iif(@includeZeroCapacityRT = 0 OR @includePseudoRT = 0, isNull(paa.Total_Profit, 0), 0)
				) as Total_Profit
			from PACE_Total_Activity pace
            left join (
                select
                    pmact.Occupancy_DT,
                    sum(pmact.Rooms_Sold) as Rooms_Sold,
                    sum(pmact.Room_Revenue) as Room_Revenue,
                    sum(pmact.Total_Profit) as Total_Profit
                from PACE_Mkt_Activity pmact
                         inner join Mkt_Seg ms ON ms.Mkt_Seg_ID = pmact.Mkt_Seg_ID and Exclude_CompHouse_Data_Display = 1
                where pmact.Occupancy_DT between @ly_start_date and @ly_end_date
                  and pmact.Property_ID=@property_id
                  and pmact.Business_Day_End_DT = @ly_businessdate
                group by pmact.Occupancy_DT
            ) AS pma on pma.Occupancy_DT = pace.Occupancy_DT
            left join (
                select
                    paact.Occupancy_DT,
                    sum(paact.Rooms_Sold) as Rooms_Sold,
                    sum(paact.Room_Revenue) as Room_Revenue,
                    sum(paact.Total_Profit) as Total_Profit
                from PACE_Accom_Activity paact
				inner join Accom_Type at on at.Accom_Type_ID = paact.Accom_Type_ID 
					AND at.status_id IN (SELECT 1 UNION SELECT CASE WHEN @includePseudoRT = 0 THEN 6 ELSE 0 END)
					AND at.display_status_id IN (SELECT 0 UNION SELECT CASE WHEN @includeZeroCapacityRT = 0 THEN 2 ELSE 0 END UNION 
																SELECT CASE WHEN @includePseudoRT = 0 THEN 4 ELSE 0 END)
                where paact.Occupancy_DT between @ly_start_date and @ly_end_date
                  and paact.Property_ID=@property_id
                  and paact.Business_Day_End_DT = @ly_businessdate
                group by paact.Occupancy_DT
            ) AS paa on paa.Occupancy_DT = pace.Occupancy_DT
			where
				  pace.Occupancy_DT between @ly_start_date and @ly_end_date
				  and pace.Property_ID=@property_id
				  and pace.Business_Day_End_DT = @ly_businessdate
		) ta left join
		(
			select
				Occupancy_DT,
				SUM(Group_RoomSold) as Group_RoomSold,
				SUM(Tran_RoomSold) as Tran_RoomSold
			from
			(
				select
                    (case when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT) else DateAdd(WEEK, 104, pace.Occupancy_DT) end) as Occupancy_DT,
					(case Business_Type_ID when  1 then  rooms_sold else 0  end) as Group_RoomSold,
					(case Business_Type_ID when  2 then  rooms_sold else 0  end) as Tran_RoomSold,
					room_revenue
				from PACE_Mkt_Activity pace
                    inner join Mkt_Seg ms on ms.Mkt_Seg_ID = pace.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varcharToInt(@ExcludeCompRoomFlag, ','))
				    join Mkt_Seg_Details mktd
						on mktd.Mkt_Seg_ID = pace.Mkt_Seg_ID
						and mktd.status_id = 1
				where
					  pace.Occupancy_DT between @ly_start_date and @ly_end_date
					  and pace.Property_ID=@property_id
					  and pace.Business_Day_End_DT = @ly_businessdate
			)as stlyh group by Occupancy_DT
	   ) ma
	   on ma.Occupancy_DT = ta.Occupancy_DT
	order by ta.Occupancy_DT

end
GO