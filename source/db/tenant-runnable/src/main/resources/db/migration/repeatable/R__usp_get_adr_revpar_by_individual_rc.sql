IF EXISTS
(
    SELECT * FROM sys.objects WHERE object_id = object_id(N'[usp_get_adr_revpar_by_individual_rc]')
)
    DROP PROCEDURE [dbo].[usp_get_adr_revpar_by_individual_rc]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_get_adr_revpar_by_individual_rc]
(
    @property_id INT,
    @roomclass_id VARCHAR(500),
    @start_date DATE,
    @end_date DATE,
    @use_physical_capacity INT,
    @includeZeroCapacityRT INT
)
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @TempAdrRevpar TABLE
    (
        occupancy_dt DATE,
        property_id INT,
        accom_class_id INT,
        rooms_sold NUMERIC(18, 0),
        accom_capacity NUMERIC(18, 0),
        outoforder NUMERIC(18, 0),
        room_revenue NUMERIC(19, 5),
        adr NUMERIC(19, 5),
        revpar NUMERIC(19, 5)
    );

    IF (@roomclass_id = '')
    BEGIN
        INSERT INTO @TempAdrRevpar
        SELECT ta.occupancy_dt,
               ta.property_id,
               NULL AS accom_class_id,
               ta.rooms_sold,
               ta.total_accom_capacity,
               (ta.rooms_not_avail_maint + ta.rooms_not_avail_other) AS outoforder,
               ta.room_revenue,
               CASE
                   WHEN ta.rooms_sold = 0 THEN
                       0
                   ELSE
                       ROUND(ta.room_revenue / ta.rooms_sold, 2)
               END AS adr,
               (
                   SELECT revpar
                   FROM dbo.ufn_calculate_revpar(
                                                    ta.room_revenue,
                                                    ta.total_accom_capacity,
                                                    (ta.rooms_not_avail_maint + ta.rooms_not_avail_other),
                                                    @use_physical_capacity
                                                )
               ) AS revpar
        FROM total_activity ta
        WHERE ta.property_id = @property_id
              AND ta.occupancy_dt
              BETWEEN @start_date AND @end_date
        ORDER BY ta.occupancy_dt;
    END
    ELSE
    BEGIN
        INSERT INTO @TempAdrRevpar
        SELECT aa.occupancy_dt,
               aa.property_id,
               ac.accom_class_id,
               SUM(aa.rooms_sold) AS rooms_sold,
               SUM(aa.accom_capacity) AS accom_capacity,
               SUM((aa.rooms_not_avail_maint + aa.rooms_not_avail_other)) AS outoforder,
               SUM(aa.room_revenue) AS room_revenue,
               CASE
                   WHEN SUM(aa.rooms_sold) = 0 THEN
                       0
                   ELSE
                       ROUND(SUM(aa.room_revenue) / SUM(aa.rooms_sold), 2)
               END AS adr,
               (
                   SELECT revpar
                   FROM dbo.ufn_calculate_revpar(
                                                    SUM(aa.room_revenue),
                                                    SUM(aa.accom_capacity),
                                                    SUM(aa.rooms_not_avail_maint + aa.rooms_not_avail_other),
                                                    @use_physical_capacity
                                                )
               ) AS revpar
        FROM accom_activity aa
            JOIN accom_type at
                ON aa.accom_type_id = at.accom_type_id
                   AND aa.property_id = at.property_id
            JOIN accom_class ac
                ON ac.accom_class_id = at.accom_class_id
                   AND ac.property_id = at.property_id
        WHERE aa.property_id = @property_id
              AND aa.occupancy_dt
              BETWEEN @start_date AND @end_date
              AND ac.accom_class_id IN (
                                           SELECT value FROM dbo.varcharToInt(@roomclass_id, ',')
                                       )
              AND at.Display_Status_ID IN (
                                              SELECT 1
                                              UNION
                                              SELECT CASE
                                                         WHEN @includeZeroCapacityRT = 1 THEN
                                                             2
                                                         ELSE
                                                             1
                                                     END
                                          )
        GROUP BY aa.occupancy_dt,
                 aa.property_id,
                 ac.accom_class_id
        ORDER BY aa.occupancy_dt,
                 aa.property_id,
                 ac.accom_class_id;
    END
    SELECT * FROM @TempAdrRevpar order by occupancy_dt;
END;
