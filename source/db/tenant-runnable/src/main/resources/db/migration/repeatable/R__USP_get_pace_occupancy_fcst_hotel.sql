IF EXISTS (
		SELECT *
		FROM sys.objects
		WHERE object_id = OBJECT_ID(N'[dbo].[usp_get_pace_occupancy_fcst_hotel]')
		)
	DROP PROCEDURE [dbo].[usp_get_pace_occupancy_fcst_hotel]
GO
/****** Object:  StoredProcedure [dbo].[usp_get_pace_occupancy_fcst_hotel]    Script Date: 3/24/2021 11:36:28 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Procedure Name: usp_get_pace_occupancy_fcst_hotel

Input Parameters :
	@property_id --> property Id associated with a property (e.g.,'XNAES' id from the property table is 10027)
	@start_date --> start date from which you need pace ('2016-02-01')
	@end_date --> end date till which you need pace ('2016-02-29')
	@past_start_date --> maximum past date till which we want pace from
    @excludeCompRooms --> weather or not to exclude comp room data ('0' = excluded / '0,1' = all data included)

Output Parameter : NA

Execution: this is just an example
	exec dbo.usp_get_pace_occupancy_fcst_hotel 10027,'2016-02-01', '2016-02-29','2015-12-03', '0,1'

Purpose: The purpose of this procedure is to extract pace of forecast for a given date range.

Assumptions : NA

Author: Nikhil Tyagi

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
3/24/2021		Nikhil				Tyagi					Initial Version
06/30/2021      Shilpa              Shaha                   Fix for BHASK-1594 BAD Screen : Pace Data tab | 0 Diet
11/26/2021		Rajratna			Awale					7.4.1 - Adds comp room data exclusion support.
***************************************************************************************/
create PROCEDURE [dbo].[usp_get_pace_occupancy_fcst_hotel]( @property_id INT
	,@start_date DATE
	,@end_date DATE
	,@past_start_date DATE
	,@excludeCompRooms VARCHAR(5)
	)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @Businessdate DATE

	SET @Businessdate = DATEADD(DAY, - 1, cast((
					SELECT dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13)
					) AS DATE)) --> extract caughtup date for a property

	DECLARE @temp_end_date AS DATE = @end_date

	IF (@Businessdate > @end_date)
BEGIN
		SET @temp_end_date = @Businessdate
END

CREATE TABLE #temp_decision (
                                Business_DT DATE
    ,Decision_ID INT
    ,UNIQUE CLUSTERED (
                                    Decision_ID
                                    )
)

    INSERT INTO #temp_decision
SELECT Business_DT
     ,Decision_ID
FROM Decision
WHERE Business_DT BETWEEN @past_start_date
    AND @Businessdate
  AND Decision_Type_ID = 1

CREATE TABLE #temp_Pace_occupancy_fcst (
                                           Occupancy_DT DATE
    ,Decision_ID INT
    ,Occupancy_NBR [numeric](8, 2)
    ,UNIQUE CLUSTERED (
                                               Occupancy_DT
                                               ,Decision_ID
                                               )
)

    INSERT INTO #temp_Pace_occupancy_fcst
SELECT Occupancy_DT
     ,Decision_ID
     ,SUM(Occupancy_NBR) Occupancy_NBR
FROM PACE_Mkt_Occupancy_FCST occ
         INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = occ.MKT_SEG_ID AND ms.Exclude_CompHouse_Data_Display IN (SELECT value FROM varcharToInt(@excludeCompRooms, ','))
WHERE Occupancy_DT BETWEEN @start_date
          AND @end_date
GROUP BY Occupancy_DT
       ,Decision_ID

CREATE TABLE #temp_occupancy_fcst (
                                      Occupancy_DT DATE
    ,Occupancy_NBR [numeric](8, 2)
    ,UNIQUE CLUSTERED (
                                          Occupancy_DT
                                          )
)

    INSERT INTO #temp_occupancy_fcst

select cast(calendar_date as date), Occupancy_NBR from calendar_dim cal
                                                           left join (

    SELECT Occupancy_DT
         ,SUM(Occupancy_NBR) Occupancy_NBR
    FROM Occupancy_FCST occ
             INNER JOIN Mkt_Seg ms ON ms.Mkt_Seg_ID = occ.MKT_SEG_ID and ms.Exclude_CompHouse_Data_Display IN (SELECT value from varcharToInt(@excludeCompRooms, ','))
             INNER JOIN Accom_Type at ON occ.Accom_Type_ID = at.Accom_Type_ID
        AND at.isComponentRoom = 'N'
    WHERE Occupancy_DT BETWEEN @start_date
      AND @temp_end_date
    GROUP BY Occupancy_DT
) core  on cast(cal.calendar_date as date)  = core.Occupancy_DT  where calendar_date between @start_date and @end_date

CREATE TABLE #pace_occupancy_fcst_hotel (
                                            Business_Day_End_DT DATE
    ,Occupancy_NBR NUMERIC(8, 2)
)

    INSERT INTO #pace_occupancy_fcst_hotel
SELECT Business_Day_End_DT
     ,SUM(Occupancy_NBR)
FROM (
         SELECT a.Business_Day_End_DT
              ,b.Occupancy_NBR
         FROM (
                  SELECT occ.Occupancy_DT
                       ,Business_DT AS Business_Day_End_DT
                       ,MAX(occ.Decision_id) Decision_id
                  FROM #temp_Pace_occupancy_fcst occ
                           INNER JOIN #temp_decision de ON occ.Decision_ID = de.Decision_ID
                  GROUP BY occ.Occupancy_DT
                         ,Business_DT
              ) a
                  INNER JOIN (
             SELECT occ.Occupancy_DT
                  ,Business_DT AS Business_Day_End_DT
                  ,occ.Decision_ID
                  ,Occupancy_NBR
             FROM #temp_Pace_occupancy_fcst occ
                      INNER JOIN #temp_decision de ON
                     occ.Decision_ID = de.Decision_ID
         ) b ON a.Business_Day_End_DT = b.Business_Day_End_DT
             AND a.Decision_id = b.Decision_ID
             AND a.Occupancy_DT = b.Occupancy_DT

         UNION ALL

         SELECT tof2.Occupancy_DT as Business_Day_End_DT
              ,tof.Occupancy_NBR
         FROM #temp_occupancy_fcst tof join #temp_occupancy_fcst tof2 on
                 tof.occupancy_dt <= tof2.occupancy_dt
         WHERE tof.Occupancy_DT BETWEEN @start_date and @end_date
           AND tof2.occupancy_dt between @start_date AND @Businessdate
     ) AS data
GROUP BY Business_Day_End_DT
ORDER BY Business_Day_End_DT

SELECT isnull(DATEADD(day, - 1, base.SnapShot_DT), pofm.Business_Day_End_DT) Business_Day_End_DT
     ,pofm.Occupancy_NBR  AS Occupancy_NBR
FROM (
         SELECT CAST(SnapShot_DT AS DATE) SnapShot_DT, max(File_Metadata_ID) as File_Metadata_ID
         FROM File_Metadata
         WHERE IsBDE = 1
           AND Record_Type_ID = 3
           AND Process_Status_ID = 13
           AND SnapShot_DT > @past_start_date
         group by SnapShot_DT
     ) base
         FULL OUTER JOIN #pace_occupancy_fcst_hotel pofm ON pofm.Business_Day_End_DT = DATEADD(day, - 1, base.SnapShot_DT)
order by Business_Day_End_DT asc

drop table #temp_decision
drop table #temp_Pace_occupancy_fcst
drop table #temp_occupancy_fcst
drop table #pace_occupancy_fcst_hotel
END
GO
