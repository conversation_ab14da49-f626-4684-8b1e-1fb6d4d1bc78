IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[usp_bad_load_property_bt_details]'))
DROP PROCEDURE [dbo].[usp_bad_load_property_bt_details]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*************************************************************************************

Procedure Name: usp_bad_load_property_bt_details

Input Parameters : 
	@property_id --> property id associated with a property (e.g.,'XNAES' id FROM the property table is 10027)
	@start_date --> start date from which we need data ('2016-03-23')
	@end_date --> end date till which we need data ('2016-03-31')
	@business_date --> business run date for the selected property
	@ms_status_ids --> status id for market segment
	@comp_rooms_filter_flag --> comp filter is associated with market segment

Ouput Parameter : NA

Execution: this is just an example
	EXECUTE dbo.usp_bad_load_property_bt_details 010027, '2016-03-23', '2016-03-31','2016-03-22','1,2,3,4,5',1

Purpose: The purpose of this procedure is to load all Business Type details metrics required for reference data dashboard details tab
		 
Author: Atul and Paul

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
05/18/2012		Paul				Samargia					Initial Version
---------------------------------------------------------------------------------------
06/10/2021		Anil				Borgude					     7.3.3
---------------------------------------------------------------------------------------
1/3/2025		Akshaykumar		    Kore					     9.7.1
***************************************************************************************/
CREATE procedure [dbo].[usp_bad_load_property_bt_details]
(
		@property_id int,
		@start_date date,
		@end_date date,
		@business_date date,
		@ms_status_ids varchar(16),
		@comp_rooms_filter_flag int
)

as

begin
	declare @caughtupdate date 

	-- extract caughtup date for a property 
	set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) 
	
	declare @exclude_comp_room varchar(5) = '0,1';
		if(@comp_rooms_filter_flag = 1)
			set @exclude_comp_room = '0';
		
		IF OBJECT_ID('tempdb..#temp_mkt_seg') IS NOT NULL
		BEGIN
		DROP TABLE #temp_mkt_seg
		END
		CREATE table #temp_mkt_seg
		(
			Property_ID int,
			Mkt_Seg_ID int,
			Mkt_Seg_Name nvarchar(100)
		)
		insert into #temp_mkt_seg
			select Property_ID, Mkt_Seg_ID, Mkt_Seg_Name
			from Mkt_Seg where Property_ID=@property_id and Status_ID in (select items from Split(@ms_status_ids , ',')) and
			 Exclude_CompHouse_Data_Display in (select value from varcharToInt(@exclude_comp_room, ','))

	-- Join Activity and Forecast data with the demand data
	select 
		activity_and_forecast.Business_Type_ID, 
		Business_Type_Name,
		activity_and_forecast.Rooms_Sold, 
		cast(System_Demand as numeric(19,2)) System_Demand, 
		cast(User_Remaining_Demand as numeric(19,2)) User_Remaining_Demand, 
		cast(Occupancy_Forecast as numeric(8,1)) Occupancy_Forecast, 
		cast(ADR_OnBooks as numeric(19,2)) ADR_OnBooks, 
		cast(ADR_Forecast as numeric(19,2)) ADR_Forecast, 
		cast(Revenue as numeric(19,2)) Revenue, 
		cast(Revenue_Forecast as numeric(19,2)) Revenue_Forecast,
        (activity_and_forecast.Rooms_Sold - ISNULL(pace_activity.rooms_sold, 0)) as Rooms_Sold_Pickup,
		CAST(budget.Budget_ADR as numeric(19,2)) as Budget_ADR,
		CAST(budget.Budget_Revenue as numeric(19,2)) as Budget_Revenue,
		CAST(budget.Rooms as numeric(19,2)) as Budget_Rooms,
        (Revenue - ISNULL(pace_activity.room_revenue, 0)) as Rooms_Revenue_Pickup,
        cast((CASE WHEN ISNULL(activity_and_forecast.Rooms_Sold,0) > 0 THEN  Revenue/activity_and_forecast.Rooms_Sold ELSE 0 END)
            -
             (CASE WHEN ISNULL(pace_activity.rooms_sold, 0) > 0 THEN  ISNULL(pace_activity.room_revenue, 0)/pace_activity.rooms_sold ELSE 0 END)
            as numeric(19,2)) as Adr_Pickup,
        CAST(User_Forecast.Rooms as numeric(19,2)) as User_Forecast_Rooms,
        CAST(User_Forecast.Room_Revenue as numeric(19,2)) as User_Forecast_Revenue,
        CAST(User_Forecast.ADR as numeric(19,2)) as User_Forecast_ADR
		
	from (	
		--- Sum by Business Type and join with Business Type to get name
		select bt.Business_Type_ID, bt.Business_Type_Name, sum(Rooms_Sold) Rooms_Sold,
			sum(Occupancy_Forecast) Occupancy_Forecast,ADR_OnBooks = case(sum(Rooms_Sold)) when 0 then 0 else sum(Revenue) / sum(Rooms_Sold) end,
			ADR_Forecast = case(SUM(Occupancy_Forecast)) when 0 then 0 else SUM(Revenue_Forecast) / SUM(Occupancy_Forecast) end,
			sum(Revenue) Revenue,sum(Revenue_Forecast) Revenue_Forecast
		from (
			-- Get Market Segment Data 
			select activity.Mkt_Seg_ID,Rooms_Sold,Occupancy_Forecast,Revenue,Revenue_Forecast
			from (
				-- Market Segment Activity data
				select 
					Mkt_Seg_ID,
					Rooms_Sold,
					Room_Revenue Revenue
				from
				(
					select 
						maa.Mkt_Seg_ID,
						sum(maa.Rooms_Sold) Rooms_Sold,
						sum(maa.Room_Revenue) Room_Revenue
					from Mkt_Accom_Activity maa
					inner join Mkt_Seg_Details msd on maa.Mkt_Seg_ID = msd.Mkt_Seg_ID
					inner join Accom_Type at on maa.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
					inner join #temp_mkt_seg ms on ms.Mkt_Seg_ID = maa.Mkt_Seg_ID
					where maa.Property_Id=@property_id
					and maa.Occupancy_DT between @start_date and @end_date
					group by maa.Mkt_Seg_ID, msd.Business_Type_ID
				) activity_type
			) as activity			
			left join
			(
				-- Get the Market Segment Occupancy Forecast
        select
         fnatms.Mkt_Seg_ID,
				 sum(Occupancy_NBR) as Occupancy_Forecast,
				 sum(Revenue) as Revenue_Forecast
				 from FN_AT_MS_Occupancy_with_dates(@caughtupdate, 1, @start_date, @end_date) fnATMS
				 inner join Accom_Type at on fnATMS.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
				 where fnATMS.Property_ID=@property_id and Occupancy_DT between @start_date and @end_date
				 group by fnatms.Mkt_Seg_ID
			) as occ_forecast on occ_forecast.Mkt_Seg_ID = activity.Mkt_Seg_ID
		) mkt_seg_totals inner join Mkt_Seg_Details msd on mkt_seg_totals.Mkt_Seg_ID = msd.Mkt_Seg_ID 
		inner join Business_Type bt on msd.Business_Type_ID = bt.Business_Type_ID
		inner join #temp_mkt_seg ms on ms.Mkt_Seg_ID = mkt_seg_totals.Mkt_Seg_ID
		  group by bt.Business_Type_ID, bt.Business_Type_Name 
		) activity_and_forecast 
		left join (
			-- Get the Demand at the Business Type
			select Business_Type_ID, sum(User_Remaining_Demand) User_Remaining_Demand, sum(System_Demand) System_Demand from (
				select 
					odf.Forecast_Group_ID, 
					(select top 1 Business_Type_ID from Mkt_Seg_Details msd inner join Mkt_Seg_Forecast_Group msfg on msd.Mkt_Seg_ID = msfg.Mkt_Seg_ID where Forecast_Group_ID = odf.Forecast_Group_ID and msfg.Status_ID=1) Business_Type_ID,
					User_Remaining_Demand = SUM( 
						case 
							when Occupancy_DT >= @caughtupdate then User_Remaining_Demand else 0
						end),
					System_Demand = SUM( 
					case 
						when Occupancy_DT >= @caughtupdate then Remaining_Demand else 0
					end)
				from Occupancy_Demand_FCST odf
				where Property_ID = @property_id and Occupancy_DT between @start_date and @end_date
				group by odf.Forecast_Group_ID
		) demand
		group by Business_Type_ID
	) business_type_demand on activity_and_forecast.Business_Type_ID = business_type_demand.Business_Type_ID
	left join 
	(
		select
		Business_Type_ID,
		sum(rooms_sold) rooms_sold,
		sum(Room_Revenue) Room_Revenue
		from
		(
			select
				bt.Business_Type_ID,
				sum(rooms_sold) rooms_sold,
				sum(Room_Revenue) Room_Revenue
			from pace_mkt_activity pma
			inner join mkt_seg_details msd 
			on pma.Mkt_Seg_ID = msd.Mkt_Seg_ID			
			inner join business_type bt on msd.Business_Type_ID = bt.Business_Type_ID
			inner join #temp_mkt_seg ms on ms.Mkt_Seg_ID = pma.Mkt_Seg_ID
			where
			pma.property_id=@property_id
			and occupancy_dt between @start_date and @end_date
			and business_day_end_dt = @business_date
			and Occupancy_DT > @business_date
			group by bt.Business_Type_ID
			union all 
			select 
				bt.Business_Type_ID,
				sum(Rooms_Sold)Rooms_Sold,
				sum(Room_Revenue) Room_Revenue
			from Mkt_Accom_Activity maa
			inner join Accom_Type at on maa.Accom_Type_ID = at.Accom_Type_ID and at.isComponentRoom = 'N'
			inner join Mkt_Seg_Details msd 
			on maa.Mkt_Seg_ID = msd.Mkt_Seg_ID	
			inner join Business_Type bt on msd.Business_Type_ID = bt.Business_Type_ID
			inner join #temp_mkt_seg ms on ms.Mkt_Seg_ID = maa.Mkt_Seg_ID
			where maa.Property_Id=@property_id 
				and maa.Occupancy_DT between @start_date and @end_date
				and Occupancy_DT <= @business_date
			group by bt.Business_Type_ID
		) temp group by Business_Type_ID
	)as pace_activity on activity_and_forecast.Business_Type_ID = pace_activity.Business_Type_ID
	left join
	(
		select
		bt.Business_Type_ID,
		SUM(bd.Rooms_Sold) as Rooms, SUM(bd.Room_Revenue) as Budget_Revenue,
		case(SUM(bd.Rooms_Sold)) when 0 then 0 else SUM(bd.Room_Revenue) / SUM(bd.Rooms_Sold) end as Budget_ADR,
		@property_id as property_id
		 from 
		Budget_Data as bd
		inner join Business_Type as bt 
		on bt.Business_Type_ID = bd.Segment_ID
		where Occupancy_Date between @start_date and @end_date and (select bl.Budget_Level_ID from Budget_Config bc inner join Budget_Level bl on bc.Budget_Level_ID = bl.Budget_Level_ID where bc.Module_Name = 'client.budget') = (select Budget_Level_ID from budget_level where Budget_Level = 'Business Type')
		group by bt.Business_Type_ID	 
	) as budget
	on budget.Business_Type_ID = activity_and_forecast.Business_Type_ID
    left join
     (
         select
             bt.Business_Type_ID,
             SUM(bd.Rooms_Sold) as Rooms, SUM(bd.Room_Revenue) as Room_Revenue,
             case(SUM(bd.Rooms_Sold)) when 0 then 0 else SUM(bd.Room_Revenue) / SUM(bd.Rooms_Sold) end as ADR,
             @property_id as property_id
         from
             User_Forecast_Data as bd
                 inner join Business_Type as bt
                            on bt.Business_Type_ID = bd.Business_Group_ID
         where Occupancy_Date between @start_date and @end_date and (select bl.Budget_Level_ID from Budget_Config bc inner join Budget_Level bl on bc.Budget_Level_ID = bl.Budget_Level_ID where bc.Module_Name = 'client.user.forecast') = (select Budget_Level_ID from budget_level where Budget_Level = 'Business Type')
         group by bt.Business_Type_ID
     ) as User_Forecast
     on User_Forecast.Business_Type_ID = activity_and_forecast.Business_Type_ID
	order by Business_Type_Name desc
	
	DROP TABLE #temp_mkt_seg
end
Go
