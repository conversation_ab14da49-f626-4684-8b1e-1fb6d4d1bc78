if exists (select * from sys.objects where object_id = object_id(N'[usp_get_mpp_inventory_history_report]'))
    drop procedure [usp_get_mpp_inventory_history_report]
GO

/*************************************************************************************

Procedure Name: usp_get_mpp_inventory_history_report

Input Parameters :
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@start_date --> start date for the report period ('2024-01-01')
	@end_date --> end date for the report period ('2024-01-31')
	@decision_type --> type of decision to filter by (e.g., 'ACCEPT', 'REJECT')
	@meeting_room_list --> comma-separated list of meeting room IDs or 'all' for all rooms
	@meeting_package_list --> comma-separated list of meeting package IDs or 'all' for all packages
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_start_date --> used when @isRollingDate is 1, else, this can be blank
	@rolling_end_date --> used when @isRollingDate is 1, else, this can be blank
	@includeSuccessfulControls --> 1 to include successful control records (Error_Code = 0), 0 to exclude
	@includeFailures --> 1 to include failure records (Error_Code <> 0), 0 to exclude
	@includePace --> 1 to include all pace records, 0 to include only latest record per occupancy date/product/room
	@systemMode --> system mode filter (e.g., 'TwoWay', 'OneWay')
	@twoWayModeDate --> date threshold for two-way mode filtering

Output Parameter : Result set with MPP inventory history data

Execution: this is just an example
	Example 1: usp_get_mpp_inventory_history_report
	-----------------------------------------
	exec dbo.usp_get_mpp_inventory_history_report
		@property_id = 10016,
		@start_date = '2024-01-01',
		@end_date = '2024-01-31',
		@decision_type = 'MeetingPackagePricing',
		@meeting_room_list = 'all',
		@meeting_package_list = 'all',
		@isRollingDate = 0,
		@rolling_start_date = '',
		@rolling_end_date = '',
		@includeSuccessfulControls = 1,
		@includeFailures = 1,
		@includePace = 1,
		@systemMode = 'TwoWay',
		@twoWayModeDate = '2024-01-01'

Purpose: The purpose of this procedure is to generate MPP (Meeting Package Pricing) inventory history report
         with flexible filtering options for meeting rooms, packages, decision types, and acknowledgment status.
         Supports both fixed and rolling date ranges, and provides options to include/exclude different types
         of records based on success/failure status and pace requirements.

Author: [Aarti Goyal]

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
[2025-06-18]			[Aarti]		[Goyal]				Initial Version
***************************************************************************************/


GO

CREATE PROCEDURE [dbo].[usp_get_mpp_inventory_history_report]
    @property_id int,
    @start_date date,
    @end_date date,
    @decision_type nvarchar(60),
    @meeting_room_list nvarchar(max),
    @meeting_package_list nvarchar(max),
    @isRollingDate int,
    @rolling_start_date nvarchar(50),
    @rolling_end_date nvarchar(50),
    @includeSuccessfulControls int,
    @includeFailures int,
    @includePace int,
    @systemMode nvarchar(50),
    @twoWayModeDate date
AS
BEGIN
    SET NOCOUNT ON;


    DECLARE @property_Name nvarchar(150)
SELECT @property_Name = Property_Name FROM Property WHERE Property_ID = @property_id


    IF (@isRollingDate = 1)
BEGIN

        DECLARE @caughtupdate date = (SELECT dbo.ufn_get_caughtup_date_by_property(@property_id, 3, 13))
        SET @start_date = (SELECT absolute_date FROM ufn_get_absolute_dates_from_rolling_dates(@rolling_start_date, @caughtupdate))
        SET @end_date = (SELECT absolute_date FROM ufn_get_absolute_dates_from_rolling_dates(@rolling_end_date, @caughtupdate))
END


    IF (@meeting_room_list = '-1' OR @meeting_package_list = '-1' OR
        @systemMode <> 'TwoWay' OR
        (@includeSuccessfulControls = 0 AND @includeFailures = 0))
BEGIN

SELECT
    CAST(NULL AS nvarchar(150)) AS Property_Name,
    CAST(NULL AS nvarchar(50)) AS Decision_Type,
    CAST(NULL AS nvarchar(100)) AS Product_Name,
    CAST(NULL AS nvarchar(50)) AS Package_Id,
    CAST(NULL AS nvarchar(100)) AS Meeting_Room,
    CAST(NULL AS date) AS Occupancy_Date,
    CAST(NULL AS datetime) AS Decision_Date_Time,
    CAST(NULL AS decimal(18,2)) AS Decision,
    CAST(NULL AS datetime) AS Acknowledgement_Date_Time,
    CAST(NULL AS nvarchar(50)) AS Acknowledgement_Status,
    CAST(NULL AS nvarchar(500)) AS Error_Reason
    WHERE 1 = 0
        RETURN
END


    DECLARE @selected_rooms TABLE (RoomID int)
    DECLARE @selected_packages TABLE (PackageID int)


    IF (@meeting_room_list = 'all')
BEGIN

INSERT INTO @selected_rooms (RoomID)
SELECT FS_Cfg_Func_Room_ID
FROM FS_Cfg_Func_Room
WHERE Property_ID = @property_id AND Is_Included_For_Pricing = 1
END
ELSE
BEGIN

INSERT INTO @selected_rooms (RoomID)
SELECT CAST(LTRIM(RTRIM(value)) AS int)
FROM STRING_SPLIT(@meeting_room_list, ',')
WHERE LTRIM(RTRIM(value)) <> ''
  AND ISNUMERIC(LTRIM(RTRIM(value))) = 1
  AND CAST(LTRIM(RTRIM(value)) AS int) > 0
END


    IF (@meeting_package_list = 'all')
BEGIN

INSERT INTO @selected_packages (PackageID)
SELECT MP_Product_ID
FROM MP_Product
WHERE Status_ID = 1
END
ELSE
BEGIN

INSERT INTO @selected_packages (PackageID)
SELECT CAST(LTRIM(RTRIM(value)) AS int)
FROM STRING_SPLIT(@meeting_package_list, ',')
WHERE LTRIM(RTRIM(value)) <> ''
  AND ISNUMERIC(LTRIM(RTRIM(value))) = 1
  AND CAST(LTRIM(RTRIM(value)) AS int) > 0
END


    IF NOT EXISTS (SELECT 1 FROM @selected_rooms) OR NOT EXISTS (SELECT 1 FROM @selected_packages)
BEGIN

SELECT
    CAST(NULL AS nvarchar(150)) AS Property_Name,
    CAST(NULL AS nvarchar(50)) AS Decision_Type,
    CAST(NULL AS nvarchar(100)) AS Product_Name,
    CAST(NULL AS nvarchar(50)) AS Package_Id,
    CAST(NULL AS nvarchar(100)) AS Meeting_Room,
    CAST(NULL AS date) AS Occupancy_Date,
    CAST(NULL AS datetime) AS Decision_Date_Time,
    CAST(NULL AS decimal(18,2)) AS Decision,
    CAST(NULL AS datetime) AS Acknowledgement_Date_Time,
    CAST(NULL AS nvarchar(50)) AS Acknowledgement_Status,
    CAST(NULL AS nvarchar(500)) AS Error_Reason
    WHERE 1 = 0
        RETURN
END

CREATE TABLE #FilteredData (
                               MP_Product_ID int,
                               FS_Cfg_Func_Room_ID int,
                               Occupancy_Date date,
                               Decision_Date_Time datetime,
                               Decision decimal(18,2),
                               Acknowledgement_Date_Time datetime,
                               Error_Code int,
                               Error_Description nvarchar(500),
                               Decision_Type nvarchar(60),
                               row_num int
)

    INSERT INTO #FilteredData (
        MP_Product_ID, FS_Cfg_Func_Room_ID, Occupancy_Date, Decision_Date_Time,
        Decision, Acknowledgement_Date_Time, Error_Code, Error_Description, Decision_Type, row_num
    )
SELECT
    das.MP_Product_ID,
    das.FS_Cfg_Func_Room_ID,
    das.Occupancy_Date,
    das.Decision_Date_Time,
    das.Decision,
    das.Acknowledgement_Date_Time,
    das.Error_Code,
    das.Error_Description,
    das.Decision_Type,
    CASE
        WHEN @includePace = 0 THEN
            ROW_NUMBER() OVER (
                    PARTITION BY das.Occupancy_Date, das.MP_Product_ID, das.FS_Cfg_Func_Room_ID
                    ORDER BY das.Decision_Date_Time DESC
                )
        ELSE 1
        END AS row_num
FROM MP_Decision_Ack_Status das
WHERE das.Occupancy_Date BETWEEN @start_date AND @end_date
  AND das.Decision_Type = @decision_type
  AND (
        (@includeSuccessfulControls = 1 AND das.Error_Code = 0) OR
        (@includeFailures = 1 AND das.Error_Code <> 0)
    )
  AND das.FS_Cfg_Func_Room_ID IN (SELECT RoomID FROM @selected_rooms)
  AND das.MP_Product_ID IN (SELECT PackageID FROM @selected_packages)
  AND (
            @systemMode = 'TwoWay'
        AND das.Acknowledgement_Date_Time >= @twoWayModeDate
    )

SELECT
    @property_Name AS Property_Name,
    fd.Decision_Type AS Decision_Type,
    mp.Name AS Product_Name,
    mp.Package_ID AS Package_Id,
    fr.Name AS Meeting_Room,
    fd.Occupancy_Date,
    fd.Decision_Date_Time,
    fd.Decision,
    fd.Acknowledgement_Date_Time,
    CASE fd.Error_Code
        WHEN -1 THEN 'not received'
        WHEN 0 THEN 'success'
        ELSE 'failure'
        END AS Acknowledgement_Status,
    ISNULL(fd.Error_Description, '') AS Error_Reason
FROM #FilteredData fd
         INNER JOIN MP_Product mp ON fd.MP_Product_ID = mp.MP_Product_ID
         INNER JOIN FS_Cfg_Func_Room fr ON fd.FS_Cfg_Func_Room_ID = fr.FS_Cfg_Func_Room_ID
WHERE (@includePace = 1 OR fd.row_num = 1)
ORDER BY fd.Occupancy_Date ASC, mp.Name ASC, fr.Name ASC, fd.Decision_Date_Time DESC

DROP TABLE #FilteredData

END
GO