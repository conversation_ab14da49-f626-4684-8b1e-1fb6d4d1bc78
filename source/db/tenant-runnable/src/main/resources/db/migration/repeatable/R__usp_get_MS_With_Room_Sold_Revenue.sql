GO
/****** Object@  StoredProcedure [dbo].[USP_Get_MS_With_Room_Sold_Revenue]    Script Date@ 8/1/2024******/
SET ANSI_NULLS ON
 GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT *  FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[USP_Get_MS_With_Room_Sold_Revenue]'))
BEGIN
		DROP PROCEDURE [dbo].[USP_Get_MS_With_Room_Sold_Revenue]
END
GO

CREATE PROCEDURE [dbo].[USP_Get_MS_With_Room_Sold_Revenue]
(
		@amsEnabled INT,
		@startDate DATE,
		@endDate DATE
)
AS
BEGIN

	IF @amsEnabled = 1
SELECT ms.Property_ID,  ms.Mkt_Seg_Code, ISNULL(ams.Market_Code,ms.Mkt_Seg_Code) originalMS,
       Business_Type_ID, Yield_Type_Id, Qualified, Fenced, Package, Priced_By_BAR, Link, Booking_Block_Pc, MONTH(data.Projection_Month),
    Mon_Occupancy, Tue_Occupancy, Wed_Occupancy, Thu_Occupancy,
    Fri_Occupancy, Sat_Occupancy, Sun_Occupancy, revenue
FROM Mkt_Seg ms
    INNER JOIN Mkt_Seg_Details msd ON ms.Mkt_Seg_ID=msd.Mkt_Seg_ID
    INNER JOIN (SELECT distinct Market_Code, Mapped_Market_Code FROM Analytical_Mkt_Seg) ams ON ams.Mapped_Market_Code=ms.Mkt_Seg_Code
    LEFT JOIN (select
    Mkt_Seg_id,Mkt_Seg_Code,Projection_Month,
    ISNULL(sum([1]),0) AS Mon_Occupancy,ISNULL(sum([2]),0) AS Tue_Occupancy, ISNULL(sum([3]),0) AS Wed_Occupancy,
    ISNULL(sum([4]),0) AS Thu_Occupancy,ISNULL(sum([5]),0) AS Fri_Occupancy,ISNULL(sum([6]),0) AS Sat_Occupancy,ISNULL(sum([7]),0) AS Sun_Occupancy,
    (ISNULL(sum([8]),0) + ISNULL(sum([16]),0)+ISNULL(sum([24]),0) + ISNULL(sum([32]),0)+ISNULL(sum([40]),0) + ISNULL(sum([48]),0)+ISNULL(sum([56]),0)) as revenue
    from
    (SELECT mkt_seg_Id, Mkt_Seg_Code, dow,dow1,Projection_Month, SUM(Rooms_Sold) AS occupancy, SUM(Room_Revenue) AS revenue
    FROM (SELECT ms.mkt_seg_Id, ms.Mkt_Seg_Code, maa.occupancy_dt, (case when maa.Room_Revenue<0 then 0 else maa.Room_Revenue end ) as Room_Revenue,
    maa.Rooms_Sold, maa.Accom_Type_ID,DATEPART(dw, occupancy_dt) as dow,DATEPART(dw, occupancy_dt)*8 as dow1,DATEFROMPARTS(YEAR(occupancy_dt),MONTH(occupancy_dt),1) AS Projection_Month
    FROM Mkt_Accom_Activity maa INNER JOIN Mkt_Seg ms ON maa.Mkt_Seg_ID = ms.Mkt_Seg_ID) AS tempMAA
    WHERE Occupancy_DT BETWEEN @startDate AND @endDate
    GROUP BY tempMAA.dow,tempMAA.dow1,tempMAA.Projection_Month, Mkt_Seg_Code, mkt_Seg_Id ) as t
    pivot (sum(occupancy) for dow in ([1],[2],[3],[4],[5],[6],[7])) as pt
    pivot (sum(revenue) for dow1 in ([8],[16],[24],[32],[40],[48],[56])) as kt
    group by Mkt_Seg_id,Mkt_Seg_Code,Projection_Month) data on data.Mkt_Seg_ID=ms.Mkt_Seg_ID ORDER BY data.Projection_Month, ms.Mkt_Seg_Code

    Else
SELECT ms.Property_ID,  ms.Mkt_Seg_Code, ms.Mkt_Seg_Code  originalMS, Business_Type_ID,
       Yield_Type_Id, Qualified, Fenced, Package, Priced_By_BAR, Link, Booking_Block_Pc, MONTH(data.Projection_Month),
    Mon_Occupancy, Tue_Occupancy, Wed_Occupancy, Thu_Occupancy,
    Fri_Occupancy, Sat_Occupancy, Sun_Occupancy, revenue
FROM Mkt_Seg ms
    INNER JOIN Mkt_Seg_Details msd ON ms.Mkt_Seg_ID=msd.Mkt_Seg_ID
    LEFT JOIN (select
    Mkt_Seg_id,Mkt_Seg_Code,Projection_Month,
    ISNULL(sum([1]),0) AS Mon_Occupancy,ISNULL(sum([2]),0) AS Tue_Occupancy, ISNULL(sum([3]),0) AS Wed_Occupancy,
    ISNULL(sum([4]),0) AS Thu_Occupancy,ISNULL(sum([5]),0) AS Fri_Occupancy,ISNULL(sum([6]),0) AS Sat_Occupancy,ISNULL(sum([7]),0) AS Sun_Occupancy,
    (ISNULL(sum([8]),0) + ISNULL(sum([16]),0)+ISNULL(sum([24]),0) + ISNULL(sum([32]),0)+ISNULL(sum([40]),0) + ISNULL(sum([48]),0)+ISNULL(sum([56]),0)) as revenue
    from
    (SELECT mkt_seg_Id, Mkt_Seg_Code, dow,dow1,Projection_Month, SUM(Rooms_Sold) AS occupancy, SUM(Room_Revenue) AS revenue
    FROM (SELECT ms.mkt_seg_Id, ms.Mkt_Seg_Code, maa.occupancy_dt, (case when maa.Room_Revenue<0 then 0 else maa.Room_Revenue end ) as Room_Revenue,
    maa.Rooms_Sold, maa.Accom_Type_ID,DATEPART(dw, occupancy_dt) as dow,DATEPART(dw, occupancy_dt)*8 as dow1,DATEFROMPARTS(YEAR(occupancy_dt),MONTH(occupancy_dt),1) AS Projection_Month
    FROM Mkt_Accom_Activity maa INNER JOIN Mkt_Seg ms ON maa.Mkt_Seg_ID = ms.Mkt_Seg_ID) AS tempMAA
    WHERE Occupancy_DT BETWEEN @startDate AND @endDate
    GROUP BY tempMAA.dow,tempMAA.dow1,tempMAA.Projection_Month, Mkt_Seg_Code, mkt_Seg_Id ) as t
    pivot (sum(occupancy) for dow in ([1],[2],[3],[4],[5],[6],[7])) as pt
    pivot (sum(revenue) for dow1 in ([8],[16],[24],[32],[40],[48],[56])) as kt
    group by Mkt_Seg_id,Mkt_Seg_Code,Projection_Month) data ON data.Mkt_Seg_ID=ms.Mkt_Seg_ID ORDER BY data.Projection_Month, ms.Mkt_Seg_Code
END