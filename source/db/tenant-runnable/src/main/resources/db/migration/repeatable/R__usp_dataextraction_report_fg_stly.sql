IF EXISTS(SELECT *
          FROM sys.objects
          WHERE object_id = OBJECT_ID(N'[dbo].[usp_dataextraction_report_fg_stly]')
            AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[usp_dataextraction_report_fg_stly]
go

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


create procedure [dbo].[usp_dataextraction_report_fg_stly]
(
    @property_id int,
    @ly_start_date date,
    @ly_end_date date,
    @ly_businessdate date,
    @is_stly_or_2y int,
    @MktExcludeCompFlag varchar(5)
)
as
begin

        select
            Occupancy_DT,
            Forecast_Group_Name,
            SUM(rooms_sold) as rooms_sold,
            SUM(room_revenue) as room_revenue,
            sum(total_profit) as profit
        from
        (
            select
                pace.property_Id,
                fg.Forecast_Group_ID,
                fg.Forecast_Group_Name,
                (case when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT) else DateAdd(WEEK, 104, pace.Occupancy_DT) end) as Occupancy_DT,
                rooms_sold,
                room_revenue,
                Total_Profit
            from PACE_Mkt_Activity pace
                join Mkt_Seg mkt
                    on mkt.Property_ID = pace.property_Id
                        and mkt.Mkt_Seg_ID = pace.Mkt_Seg_ID
                        and mkt.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag, ','))
                join Mkt_Seg_Forecast_Group fgmkt
                    on fgmkt.Mkt_Seg_ID = mkt.Mkt_Seg_ID
                        and fgmkt.status_id = 1
                join Forecast_Group fg
                    on fg.property_Id = pace.property_Id
                        and fg.forecast_group_id = fgmkt.forecast_group_id
                        and fg.status_id = 1
            where
                  pace.Occupancy_DT between @ly_start_date and @ly_end_date
                  and pace.Property_ID=@property_id
                  and pace.Business_Day_End_DT = @ly_businessdate

        )as fgstly
    group by Occupancy_DT, Forecast_Group_Name
    order by Occupancy_DT, Forecast_Group_Name

end
GO
