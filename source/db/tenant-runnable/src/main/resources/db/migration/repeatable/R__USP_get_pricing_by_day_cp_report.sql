drop procedure if exists [dbo].[usp_get_pricing_by_day_cp_report]
GO
/*************************************************************************************
	*** Object:  StoredProcedure [dbo].[usp_get_pricing_by_day_cp_report]    Script Date: 5/08/2020 2:45:29 PM ***

Input Parameters :
        @property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
        @start_date --> occupancy_start_date ('2011-07-01')
        @end_date --> occupancy_end_date ('2011-07-31')
        @comp_id_1 --> competitor id of 1st competitor
        @comp_id_2 --> competitor id of 2nd competitor
        @comp_id_3 --> competitor id of 3rd competitor
        @comp_id_4 --> competitor id of 4th competitor
        @comp_id_5 --> competitor id of 5th competitor
        @comp_id_6 --> competitor id of 6st competitor
        @comp_id_7 --> competitor id of 7nd competitor
        @comp_id_8 --> competitor id of 8rd competitor
        @comp_id_9 --> competitor id of 9th competitor
        @comp_id_10 --> competitor id of 10th competitor
        @comp_id_11--> competitor id of 11st competitor
        @comp_id_12 --> competitor id of 12nd competitor
        @comp_id_13 --> competitor id of 13rd competitor
        @comp_id_14 --> competitor id of 14th competitor
        @comp_id_15 --> competitor id of 15th competitor
        @RoomTypes --> comma-separated list of RoomTypes you want to get the report for
        @isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
        @rolling_start_date --> used when @isRollingDate is 1, else, this can be blank
        @rolling_end_date --> used when @isRollingDate is 1, else, this can be blank
        @products --> comma- separated list of products
        @filterDtsWhenMatchesGFO --> filter dts
        @includeIndependentProductsInReport --> toggle value of includeIndependentProductsInReport
Ouput Parameter : NA

Execution: this is just an example
       Example 1: usp_get_pricing_by_day_cp_report
       -----------------------------------------
       exec dbo.usp_get_pricing_by_day_cp_report 990003,'1970-01-01','1970-01-01',-1, -1, -1,-1, -1,-1, -1, -1, -1, -1, -1, -1, -1, -1, -1,'-1',1,'TODAY-365','TODAY','16,1,17,18,19,20,5,21,15',0,0

Purpose: The purpose of this stored procedure is to get pricing_by_day_cp_report

Author: Vikas Shivankar

***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_get_pricing_by_day_cp_report]
(
       @property_id int,
       @start_date date,
       @end_date date,
       @comp_id_1 int,
       @comp_id_2 int,
       @comp_id_3 int,
       @comp_id_4 int,
       @comp_id_5 int,
       @comp_id_6 int,
       @comp_id_7 int,
       @comp_id_8 int,
       @comp_id_9 int,
       @comp_id_10 int,
       @comp_id_11 int,
       @comp_id_12 int,
       @comp_id_13 int,
       @comp_id_14 int,
       @comp_id_15 int,
       @RoomTypes nvarchar(200),
       @isRollingDate int,
       @rolling_start_date nvarchar(50),
       @rolling_end_date nvarchar(50),
       @products nvarchar(500),    
	   @filterDtsWhenMatchesGFO int,
       @includeIndependentProductsInReport int,
       @isRdlEnabled int
)             
as
begin
			  SET NOCOUNT ON

              declare @caughtupdate date 
              declare @RoomClasses nvarchar(200)
             IF OBJECT_ID('tempdb..#tempAccomClassId') IS NOT NULL
				BEGIN
					DROP TABLE #tempAccomClassId
				END
					CREATE table #tempAccomClassId
				(
					ID int, 
					Accom_Class_ID int
				)
              set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property
              
              insert into #tempAccomClassId select 1, Accom_Class_ID as Accom_Class_ID 
              FROM (SELECT value Accom_Type_ID FROM varcharToInt(@RoomTypes,',')
              )a inner join Accom_Type b on b.Accom_Type_ID = a.Accom_Type_ID and b.property_id = @property_id group by Accom_Class_ID
              SELECT @RoomClasses = STUFF((SELECT ', ' + CAST(Accom_Class_ID AS VARCHAR(10)) [text()]
                                         FROM #tempAccomClassId 
                                          WHERE ID = t.ID
                                         FOR XML PATH(''), TYPE)
                                         .value('.','NVARCHAR(MAX)'),1,2,' ') 
              FROM #tempAccomClassId t group by ID
              
              if(@RoomTypes = '-1')
              begin
                     SELECT @RoomClasses = STUFF((SELECT ', ' + CAST(Accom_Class_ID AS VARCHAR(10)) [text()]
                                         FROM Accom_Class 
                                          WHERE property_id = t.property_id
                                         FOR XML PATH(''), TYPE)
                                         .value('.','NVARCHAR(MAX)'),1,2,' ') 
                           FROM Accom_Class t where property_id = @property_id group by property_id
              end
                           
              if(@isRollingDate=1)
              begin
                     set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
                     set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
              end

              ------------------------------create temp table to keep room types------------------------------
              
             IF OBJECT_ID('tempdb..#selectedRoomTypesTable') IS NOT NULL
				BEGIN
					DROP TABLE #selectedRoomTypesTable
				END
			CREATE table #selectedRoomTypesTable
			(
				Accom_Type_ID int
			)

              if(@RoomTypes = '-1')
                     insert into #selectedRoomTypesTable select Accom_Type_ID from Accom_Type where Status_ID = 1
              else 
                     insert into #selectedRoomTypesTable select value as Accom_Type_ID from varcharToInt(@RoomTypes,',')
       
              ------------------------------create temp table to keep product, room types and date combination------------------------------

             IF OBJECT_ID('tempdb..#selectedProductsTable') IS NOT NULL
				BEGIN
					DROP TABLE #selectedProductsTable
				END
			CREATE table #selectedProductsTable
			(
			Property_ID int, 
			Product_ID int, 
			Product_Name nvarchar(100), 
			Arrival_DT date, 
			Accom_Type_ID int,
		    Offset_Method int,
            Product_Code nvarchar(50),
            display_order int
			)
       
              insert into #selectedProductsTable  
              select p.Property_ID, p.Product_ID, p.Name as Product_Name, selectedDate.date as Arrival_DT, rt.Accom_Type_ID, p.Offset_Method, p.Product_Code,p.display_order from
              (
                     SELECT Value as Product_ID, p.Name, @property_id as Property_ID, Offset_Method, p.Code as Product_Code, p.display_order
                     FROM 
                     varcharToInt(@products,',')
                     inner join 
                     Product as p
                     on p.Product_ID = value
              ) as p
              cross join 
              (
                     select Accom_Type_ID from #selectedRoomTypesTable
              
              ) as rt
              cross join
              (
                     select cast(calendar_date as date) date, @property_id as Property_ID from calendar_dim where calendar_date BETWEEN @start_date AND @end_date
              ) as selectedDate
              
            -----------------------------------------------create temp table webCompetitorsRateByRC and populating --------------------------------
			
			declare @webrate_comp_ids nvarchar(200)
			
			set @webrate_comp_ids = CONCAT_WS(',', @comp_id_1, @comp_id_2, @comp_id_3, @comp_id_4, @comp_id_5, @comp_id_6, @comp_id_7, @comp_id_8, @comp_id_9, @comp_id_10, @comp_id_11, @comp_id_12, @comp_id_13, @comp_id_14, @comp_id_15)
			
			IF OBJECT_ID('tempdb..#webCompetitorsRateByRC') IS NOT NULL
				BEGIN
					DROP TABLE #webCompetitorsRateByRC
				END
			CREATE table #webCompetitorsRateByRC
			(
				property_id	int,
				accom_Class_id	int,
				occupancy_dt date,
				webrate_currency nvarchar(50),
				webrate_ratevalue numeric(19,5),
				webrate_competitors_name nvarchar(150),
				webrate_competitors_ID int,
				Product_Id int
			)

			if(@isRdlEnabled=1)
                begin
                    INSERT INTO #webCompetitorsRateByRC
                    SELECT
                    property_id,
                    accom_Class_id,
                    occupancy_dt,
                    webrate_currency,
                    webrate_ratevalue,
                    webrate_competitors_name,
                    webrate_competitors_ID,
                    Product_Id
                    FROM ufn_get_web_competitors_rate_by_rc_for_pricing_report_independent_products_v2(@property_id,@webrate_comp_ids,@start_date,@end_date,@RoomClasses, @products)
                end
            else
                begin
                    INSERT INTO #webCompetitorsRateByRC
                    SELECT
                        property_id,
                        accom_Class_id,
                        occupancy_dt,
                        webrate_currency,
                        webrate_ratevalue,
                        webrate_competitors_name,
                        webrate_competitors_ID,
                        Product_Id
                    FROM ufn_get_web_competitors_rate_by_rc_for_pricing_report_independent_products(@property_id,@webrate_comp_ids,@start_date,@end_date,@RoomClasses, @products)
                end

			---------------------------------Create Temp Table for Accom _class_id, accom_type_id,arrival_dt and close_lv0_los combination

			  Select Accom_Class_ID,Accom_Type_ID,Arrival_DT,STUFF((SELECT ', ' + CAST(LOS AS VARCHAR(10)) [text()]
                                         FROM Decision_Restrict_Highest_Bar
                                          WHERE Accom_Class_ID = t.Accom_Class_ID and Accom_Type_ID= t.Accom_Type_ID and Arrival_DT= t.Arrival_DT
                                         FOR XML PATH(''), TYPE)
                                         .value('.','NVARCHAR(MAX)'),1,2,' ') LOS, (Select NAME from Product where System_Default = 1 )  as product_name ,u.User_ID, u.User_Name , t.CreateDate_DTTM as CreateDate_DTTM
              INTO #closeLV0Table FROM Decision_Restrict_Highest_Bar t INNER JOIN Users u on t.user_id = u.user_id WHERE Arrival_DT between @start_date and @end_date and property_id = @property_id
			   and t.CreateDate_DTTM = (Select MAX(CreateDate_DTTM) FROM Decision_Restrict_Highest_Bar
			WHERE Accom_Class_ID = t.Accom_Class_ID and Accom_Type_ID= t.Accom_Type_ID and Arrival_DT= t.Arrival_DT)   group by Accom_Class_ID,Accom_Type_ID,Arrival_DT,u.User_ID,u.User_Name,t.CreateDate_DTTM

			-----------------------------------------------Main query starts from here-------------------------------------------------------------
	          
			select Arrival_DT,
			Override,
			DOW,
			CAST(Rooms_Sold AS numeric(18,0)) AS Rooms_Sold,
			CAST(Out_of_Order AS numeric(18,0)) AS Out_of_Order,
			CAST(Occupancy_FCST AS numeric(18,2)) AS Occupancy_FCST,
			CAST(Occupancy_FCST_Per AS numeric(18,2)) AS Occupancy_FCST_Per,
			CAST(Property_Occupancy_NBR AS numeric(18,2)) AS Property_Occupancy_NBR,
			CAST(Property_Occupancy_Percent AS numeric(18,2)) AS Property_Occupancy_Percent,
			Rate_Code_Name,
			CAST(BarRate AS numeric(19,5)) AS BarRate,
			Accom_Class_Name,
			Notes,
			CAST(comp1_rate AS numeric(19,5)) AS comp1_rate,comp1_name,
			CAST(comp2_rate AS numeric(19,5)) AS comp2_rate,comp2_name,
			CAST(comp3_rate AS numeric(19,5)) AS comp3_rate,comp3_name,
			CAST(comp4_rate AS numeric(19,5)) AS comp4_rate,comp4_name,
			CAST(comp5_rate AS numeric(19,5)) AS comp5_rate,comp5_name,
			CAST(comp6_rate AS numeric(19,5)) AS comp6_rate,comp6_name,
			CAST(comp7_rate AS numeric(19,5)) AS comp7_rate,comp7_name,
			CAST(comp8_rate AS numeric(19,5)) AS comp8_rate,comp8_name,
			CAST(comp9_rate AS numeric(19,5)) AS comp9_rate,comp9_name,
			CAST(comp10_rate AS numeric(19,5)) AS comp10_rate,comp10_name,
			CAST(comp11_rate AS numeric(19,5)) AS comp11_rate,comp11_name,
			CAST(comp12_rate AS numeric(19,5)) AS comp12_rate,comp12_name,
			CAST(comp13_rate AS numeric(19,5)) AS comp13_rate,comp13_name,
			CAST(comp14_rate AS numeric(19,5)) AS comp14_rate,comp14_name,
			CAST(comp15_rate AS numeric(19,5)) AS comp15_rate,comp15_name,
			CAST(lrv AS numeric(19,5)) AS LRV,
			CAST(Total_Property_Rooms AS numeric(8,0)) AS Total_Property_Rooms,
			User_ID,
			CreateDate_DTTM,
			Username,
			CAST(Occupancy_FCST_PhyCap_Per AS numeric(18,2)) AS Occupancy_FCST_PhyCap_Per,
			CAST(Property_Occupancy_PhyCap_Percent AS numeric(8,2)) AS Property_Occupancy_PhyCap_Percent,
			Accom_Type_Name,
			Product_Name,
			Close_LV0_LOS,
			adjustment,
            Product_Code,
            display_order
			from (
			SELECT   main.Occupancy_DT AS Arrival_DT,
                     bar.Override as Override,
                     DATENAME(dw,main.Occupancy_DT) AS DOW,
                     accomClassActivity.Room_Type_Rooms_Sold AS Rooms_Sold,
                     accomClassActivity.Room_Type_Outoforder AS Out_of_Order,
                     accomClassOccupancyForecast.OccupancyForecast AS Occupancy_FCST,
                     Occupancy_FCST_Per =
                             CASE (accomClassActivity.Room_Type_Capacity-accomClassActivity.Room_Type_Outoforder)
                                         WHEN 0 THEN 0
                             ELSE
                                         (accomClassOccupancyForecast.OccupancyForecast / (accomClassActivity.Room_Type_Capacity-accomClassActivity.Room_Type_Outoforder)) * 100
                             END,
                     propertyOccupancyForecast.Property_Occupancy_NBR,
                     propertyOccupancyForecast.Property_Occupancy_Percent,
                     NULL AS Rate_Code_Name,
                     bar.Final_BAR AS BarRate,
                     main.Accom_Class_Name,
                     notes.Notes,
                     comp1.webrate_ratevalue AS comp1_rate,
                     case when comp1.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_1)
                          else comp1.webrate_competitors_name end AS comp1_name,
                     comp2.webrate_ratevalue AS comp2_rate,
                     case when comp2.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_2)
                          else comp2.webrate_competitors_name end AS comp2_name,
                     comp3.webrate_ratevalue AS comp3_rate,
                     case when comp3.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_3)
                          else comp3.webrate_competitors_name end AS comp3_name,
                     comp4.webrate_ratevalue AS comp4_rate,
                     case when comp4.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_4)
                          else comp4.webrate_competitors_name end AS comp4_name,
                     comp5.webrate_ratevalue AS comp5_rate,
                     case when comp5.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_5)
                          else comp5.webrate_competitors_name end AS comp5_name,
                     comp6.webrate_ratevalue AS comp6_rate,
                     case when comp6.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_6)
                          else comp6.webrate_competitors_name end AS comp6_name,
                     comp7.webrate_ratevalue AS comp7_rate,
                     case when comp7.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_7)
                          else comp7.webrate_competitors_name end AS comp7_name,
                     comp8.webrate_ratevalue AS comp8_rate,
                     case when comp8.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_8)
                          else comp8.webrate_competitors_name end AS comp8_name,
                     comp9.webrate_ratevalue AS comp9_rate,
                     case when comp9.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_9)
                          else comp9.webrate_competitors_name end AS comp9_name,
                     comp10.webrate_ratevalue AS comp10_rate,
                     case when comp10.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_10)
                          else comp10.webrate_competitors_name end AS comp10_name,
                     comp11.webrate_ratevalue AS comp11_rate,
                     case when comp11.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_11)
                          else comp11.webrate_competitors_name end AS comp11_name,
                     comp12.webrate_ratevalue AS comp12_rate,
                     case when comp12.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_12)
                          else comp12.webrate_competitors_name end AS comp12_name,
                     comp13.webrate_ratevalue AS comp13_rate,
                     case when comp13.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_13)
                          else comp13.webrate_competitors_name end AS comp13_name,
                     comp14.webrate_ratevalue AS comp14_rate,
                     case when comp14.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_14)
                          else comp14.webrate_competitors_name end AS comp14_name,
                     comp15.webrate_ratevalue AS comp15_rate,
                     case when comp15.webrate_competitors_name is null then (select Webrate_Competitors_Name from Webrate_Competitors where Webrate_Competitors_Id = @comp_id_15)
                          else comp15.webrate_competitors_name end AS comp15_name,
                     lrv.LRV AS lrv,
                     totalActivity.Property_Rooms_Sold AS Total_Property_Rooms,
                     IsNull(bar.user_id,closeLv0.user_id) as USER_ID,
                     IsNull(bar.CreateDate_DTTM,closeLv0.CreateDate_DTTM) as CreateDate_DTTM,
                     IsNull(bar.User_Name,closeLv0.User_Name) AS Username,
                     Occupancy_FCST_PhyCap_Per =
                           CASE (accomClassActivity.Room_Type_Capacity)
                          WHEN 0 THEN 0
                           ELSE
                          (accomClassOccupancyForecast.OccupancyForecast / (accomClassActivity.Room_Type_Capacity)) * 100
                           END,
                     Property_Occupancy_PhyCap_Percent =
                           CASE (propertyOccupancyForecast.Total_Accom_Capacity)
                          WHEN 0 THEN 0
                           ELSE
                          (propertyOccupancyForecast.property_Occupancy_NBR / (propertyOccupancyForecast.Total_Accom_Capacity)) * 100
                           END,
                     main.Accom_Type_Name AS Accom_Type_Name,
                     bar.Product_Name,
                     closelv0.LOS AS Close_LV0_LOS,
					 concat(cast(bar.Adjustment_Value as numeric(19,2)), bar.adjustment_type) AS adjustment, bar.Product_Code,display_order
              FROM
              (
                     SELECT aa.Property_ID, p.Property_Name, aa.Occupancy_DT, at.Accom_Class_ID, ac.Accom_Class_Name, at.Accom_Type_ID, at.Accom_Type_Name
                     FROM Accom_Activity aa INNER JOIN Property p ON aa.Property_ID = p.Property_ID INNER JOIN Accom_Type at ON aa.Accom_Type_ID = at.Accom_Type_ID INNER JOIN Accom_Class ac on at.Accom_Class_ID = ac.Accom_Class_ID
                     WHERE aa.Property_ID = @property_id AND aa.Occupancy_DT BETWEEN @start_date AND @end_date
                     AND at.Status_ID = 1 AND at.System_Default = 0 AND ((@RoomTypes = '-1' and at.Display_Status_ID = 1) OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
              ) main
              LEFT JOIN (
                     SELECT 
                           Property_ID,
                           Occupancy_DT,
                           Rooms_Sold as Property_Rooms_Sold,
                           Total_Accom_Capacity as Property_capacity,
                           (Rooms_Not_Avail_Maint+Rooms_Not_Avail_Other) Property_Outoforder
                     FROM Total_Activity Total_Activity
                     WHERE Total_Activity.Property_ID=@property_id AND Occupancy_DT BETWEEN @start_date AND @end_date
              ) AS totalActivity on main.Property_ID = totalActivity.Property_ID AND main.Occupancy_DT = totalActivity.Occupancy_DT
              LEFT JOIN
              (
                     SELECT
                           AA.Property_ID,
                           AA.Occupancy_DT,
                           AT.Accom_Class_ID,
                           SUM(Rooms_Sold) AS Room_Type_Rooms_Sold,
                           SUM(AA.Accom_capacity) AS Room_Type_Capacity,
                           (SUM(AA.Rooms_Not_Avail_Maint) + SUM(AA.Rooms_Not_Avail_Other)) AS Room_Type_Outoforder
                     FROM Accom_Activity aa INNER JOIN Accom_Type at on aa.Accom_Type_ID = at.Accom_Type_ID
                     WHERE aa.Property_ID = @property_id AND aa.Occupancy_DT BETWEEN @start_date AND @end_date
                     AND at.Status_ID = 1 AND at.System_Default = 0 AND ((@RoomTypes = '-1' and at.Display_Status_ID = 1) OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                     GROUP BY AA.Property_ID,AA.Occupancy_DT,AT.Accom_Class_ID
              ) AS accomClassActivity ON main.Property_ID=accomClassActivity.Property_ID AND main.Occupancy_DT=accomClassActivity.Occupancy_DT AND main.Accom_Class_ID = accomClassActivity.Accom_Class_ID
              LEFT JOIN
              (
                    SELECT DL.Property_ID, DL.Occupancy_DT, DL.Accom_Class_ID, DL.LRV 
                    FROM Decision_LRV DL
                       WHERE DL.Property_ID=@property_id AND DL.Occupancy_DT BETWEEN @start_date AND @end_date
                       AND DL.Accom_Class_ID IN (SELECT DISTINCT at.Accom_Class_ID FROM Accom_Type at WHERE at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))))
              ) AS lrv ON main.Property_ID=lrv.Property_ID AND main.Occupancy_DT=lrv.Occupancy_DT AND main.Accom_Class_ID=lrv.Accom_Class_ID
              LEFT JOIN
              (
                    SELECT Property_ID, Occupancy_DT, Occupancy_Percent AS Property_Occupancy_Percent, Occupancy_NBR AS Property_Occupancy_NBR, Total_Accom_Capacity 
                    FROM dbo.FN_Occupancy_Forecast(@caughtupdate, 0)
                    WHERE Property_ID=@property_id AND Occupancy_DT BETWEEN @start_date AND @end_date
              ) AS propertyOccupancyForecast ON main.Property_ID=propertyOccupancyForecast.Property_ID AND main.Occupancy_DT=propertyOccupancyForecast.Occupancy_DT
              LEFT JOIN
              (
                    SELECT Property_ID, Occupancy_DT, Accom_class_ID, SUM(Occupancy_NBR) as OccupancyForecast
                       FROM dbo.FN_AT_MS_Occupancy(@caughtupdate, 0)
                    WHERE Property_ID=@property_id and Occupancy_DT between @start_date AND @end_date
                    AND Accom_Class_ID IN (SELECT DISTINCT at.Accom_Class_ID FROM Accom_Type at WHERE at.Status_ID = 1 AND at.System_Default = 0 AND (@RoomTypes = '-1' OR at.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,','))))
                       GROUP BY Occupancy_DT, Property_ID, Accom_Class_ID
              ) AS accomClassOccupancyForecast ON main.Occupancy_DT=accomClassOccupancyForecast.Occupancy_DT AND main.Property_ID=accomClassOccupancyForecast.Property_ID AND main.Accom_Class_ID=accomClassOccupancyForecast.Accom_Class_ID
              LEFT JOIN
              (
                       SELECT * FROM ufn_get_notes_by_multiple_module (@property_id,@start_date,@end_date,'Overbooking','Continuous Pricing','Demand And Wash','Pricing')
              ) AS notes ON main.Occupancy_DT = notes.Arrival_DT and main.Property_ID=notes.Property_ID
              LEFT JOIN
              (
                     SELECT 
                           bar_output.Property_ID, 
                           bar_output.Arrival_DT,
                           bar_output.Accom_Type_ID,
                           bar_output.LOS,
                           bar_output.Final_BAR,
                           bar_output.Override,
                           bar_output_ovr.User_ID,
                           bar_output_ovr.User_Name,
                           bar_output_ovr.CreateDate_DTTM,
                           bar_output.Product_Name as Product_Name,
						   bar_output.adjustment_value,
						   bar_output.adjustment_type,
           bar_output.Product_Code,
		   bar_output.Product_ID,
           bar_output.display_order
                     FROM
                     (
                           select pr.Property_ID, pr.Arrival_DT, pr.Accom_Type_ID, rate.LOS, rate.Final_BAR, rate.Override, pr.Product_Name, pr.Product_ID, rate.Adjustment_Value, 
						   CASE
								WHEN pr.Offset_Method = 1 and rate.adjustment_value is not null THEN ' %'
								ELSE ''
								END AS adjustment_type,
           Product_Code,display_order
						   from 
                           (
                                  SELECT * from #selectedProductsTable
                           ) as pr
                           left join 
                           (
                                  SELECT cdbo.Property_ID, cdbo.Arrival_DT, cdbo.Accom_Type_ID, cdbo.LOS, cdbo.Final_BAR, cdbo.Override, cdbo.Product_ID, cdbo.Adjustment_Value--, p.Name, p.Product_ID
                                  FROM 
                                  CP_Decision_Bar_Output cdbo
                                  WHERE cdbo.Property_ID = @property_id AND cdbo.Arrival_DT BETWEEN @start_date AND @end_date
								  AND cdbo.LOS = -1
                                  AND (@RoomTypes = '-1' OR cdbo.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                           ) as rate
                           on pr.Product_ID = rate.Product_ID and pr.Accom_Type_ID = rate.Accom_Type_ID and pr.Arrival_DT = rate.Arrival_DT and pr.Property_ID = rate.Property_ID

                     ) AS bar_output LEFT JOIN
                     (
                           SELECT cdboo.Property_ID, cdboo.Arrival_DT, cdboo.Accom_Type_ID, u.User_ID, u.User_Name, max(cdboo.CreateDate) as CreateDate_DTTM
                           FROM CP_Decision_Bar_Output_OVR cdboo
                           INNER JOIN Users U ON cdboo.User_ID=U.User_ID
                           LEFT JOIN Product as p
                           ON p.Product_ID = cdboo.Product_ID
                           WHERE cdboo.Property_ID=@property_id AND Arrival_DT BETWEEN @start_date AND @end_date 
						   AND LOS=-1
                           AND (@RoomTypes = '-1' OR cdboo.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
                           AND cdboo.Product_ID IN (SELECT Value FROM varcharToInt(@products,','))
						   AND cdboo.CreateDate IN	(
														SELECT MAX(CreateDate) FROM CP_Decision_Bar_Output_OVR cdboo
														WHERE cdboo.Property_ID=@property_id AND Arrival_DT BETWEEN @start_date AND @end_date 
														AND LOS=-1
														AND (@RoomTypes = '-1' OR cdboo.Accom_Type_ID IN (SELECT Value FROM varcharToInt(@RoomTypes,',')))
														AND cdboo.Product_ID IN (SELECT Value FROM varcharToInt(@products,','))
														GROUP BY Property_ID, Arrival_DT, Accom_Type_ID
													)
                           GROUP BY Property_ID, Arrival_DT, Accom_Type_ID, u.User_ID ,User_Name

                     ) AS bar_output_ovr ON bar_output.Property_ID = bar_output_ovr.Property_ID AND bar_output.Arrival_DT=bar_output_ovr.Arrival_DT AND bar_output.Accom_Type_ID=bar_output_ovr.Accom_Type_ID
              ) AS bar ON main.Property_ID=bar.Property_ID AND main.Occupancy_DT=bar.Arrival_DT AND main.Accom_Type_ID=bar.Accom_Type_ID
              LEFT JOIN
			  #closeLV0Table closelv0 on closelv0.Accom_Class_ID = main.Accom_Class_ID AND closelv0.Accom_Type_ID=main.Accom_Type_ID and closelv0.Arrival_DT = main.Occupancy_DT and bar.Product_Name = closelv0.product_name
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp1 on  @comp_id_1>0 and @comp_id_1 = comp1.webrate_competitors_ID and main.Occupancy_DT=comp1.Occupancy_DT and main.Property_ID=comp1.Property_ID and main.Accom_Class_ID = comp1.accom_Class_id and (comp1.Product_Id IS NULL OR comp1.Product_Id = bar.Product_ID )
			  LEFT JOIN 
			  #webCompetitorsRateByRC as comp2 on  @comp_id_2>0 and @comp_id_2 = comp2.webrate_competitors_ID and main.Occupancy_DT=comp2.Occupancy_DT and main.Property_ID=comp2.Property_ID and main.Accom_Class_ID = comp2.accom_Class_id and (comp2.Product_Id IS NULL OR comp2.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp3 on  @comp_id_3>0 and @comp_id_3 = comp3.webrate_competitors_ID and main.Occupancy_DT=comp3.Occupancy_DT and main.Property_ID=comp3.Property_ID and main.Accom_Class_ID = comp3.accom_Class_id and (comp3.Product_Id IS NULL OR comp3.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp4 on  @comp_id_4>0 and @comp_id_4 = comp4.webrate_competitors_ID and main.Occupancy_DT=comp4.Occupancy_DT and main.Property_ID=comp4.Property_ID and main.Accom_Class_ID = comp4.accom_Class_id and (comp4.Product_Id IS NULL OR comp4.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp5 on  @comp_id_5>0 and @comp_id_5 = comp5.webrate_competitors_ID and main.Occupancy_DT=comp5.Occupancy_DT and main.Property_ID=comp5.Property_ID and main.Accom_Class_ID = comp5.accom_Class_id and (comp5.Product_Id IS NULL OR comp5.Product_Id = bar.Product_ID)
			  LEFT JOIN 
			  #webCompetitorsRateByRC as comp6 on  @comp_id_6>0 and @comp_id_6 = comp6.webrate_competitors_ID and main.Occupancy_DT=comp6.Occupancy_DT and main.Property_ID=comp6.Property_ID and main.Accom_Class_ID = comp6.accom_Class_id and (comp6.Product_Id IS NULL OR comp6.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp7 on  @comp_id_7>0 and @comp_id_7 = comp7.webrate_competitors_ID and main.Occupancy_DT=comp7.Occupancy_DT and main.Property_ID=comp7.Property_ID and main.Accom_Class_ID = comp7.accom_Class_id and (comp7.Product_Id IS NULL OR comp7.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp8 on  @comp_id_8>0 and @comp_id_8 = comp8.webrate_competitors_ID and main.Occupancy_DT=comp8.Occupancy_DT and main.Property_ID=comp8.Property_ID and main.Accom_Class_ID = comp8.accom_Class_id and (comp8.Product_Id IS NULL OR comp8.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp9 on  @comp_id_9>0 and @comp_id_9 = comp9.webrate_competitors_ID and main.Occupancy_DT=comp9.Occupancy_DT and main.Property_ID=comp9.Property_ID and main.Accom_Class_ID = comp9.accom_Class_id and (comp9.Product_Id IS NULL OR comp9.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp10 on  @comp_id_10>0 and @comp_id_10 = comp10.webrate_competitors_ID and main.Occupancy_DT=comp10.Occupancy_DT and main.Property_ID=comp10.Property_ID and main.Accom_Class_ID = comp10.accom_Class_id and (comp10.Product_Id IS NULL OR comp10.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp11 on  @comp_id_11>0 and @comp_id_11 = comp11.webrate_competitors_ID and main.Occupancy_DT=comp11.Occupancy_DT and main.Property_ID=comp11.Property_ID and main.Accom_Class_ID = comp11.accom_Class_id and (comp11.Product_Id IS NULL OR comp11.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp12 on  @comp_id_12>0 and @comp_id_12 = comp12.webrate_competitors_ID and main.Occupancy_DT=comp12.Occupancy_DT and main.Property_ID=comp12.Property_ID and main.Accom_Class_ID = comp12.accom_Class_id and (comp12.Product_Id IS NULL OR comp12.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp13 on  @comp_id_13>0 and @comp_id_13 = comp13.webrate_competitors_ID and main.Occupancy_DT=comp13.Occupancy_DT and main.Property_ID=comp13.Property_ID and main.Accom_Class_ID = comp13.accom_Class_id and (comp13.Product_Id IS NULL OR comp13.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp14 on  @comp_id_14>0 and @comp_id_14 = comp14.webrate_competitors_ID and main.Occupancy_DT=comp14.Occupancy_DT and main.Property_ID=comp14.Property_ID and main.Accom_Class_ID = comp14.accom_Class_id and (comp14.Product_Id IS NULL OR comp14.Product_Id = bar.Product_ID)
              LEFT JOIN 
			  #webCompetitorsRateByRC as comp15 on  @comp_id_15>0 and @comp_id_15 = comp15.webrate_competitors_ID and main.Occupancy_DT=comp15.Occupancy_DT and main.Property_ID=comp15.Property_ID and main.Accom_Class_ID = comp15.accom_Class_id and (comp15.Product_Id IS NULL OR comp15.Product_Id = bar.Product_ID)
			  LEFT JOIN
			  Group_Floor_OVR  AS GFO
			  on bar.Accom_Type_ID = GFO.Base_Accom_Type_ID and bar.Arrival_DT = GFO.Occupancy_DT
			  where @filterDtsWhenMatchesGFO = 0 or (GFO.IsGFOApplied=1 AND GFO.Pretty_Group_Rate = bar.Final_BAR)
	
               ) oq ORDER BY oq.Arrival_DT, oq.Accom_Class_Name, oq.Accom_Type_Name,
                        CASE @includeIndependentProductsInReport WHEN 1 THEN oq.display_order END,
		        		CASE @includeIndependentProductsInReport WHEN 0 THEN oq.Product_Name END
end
