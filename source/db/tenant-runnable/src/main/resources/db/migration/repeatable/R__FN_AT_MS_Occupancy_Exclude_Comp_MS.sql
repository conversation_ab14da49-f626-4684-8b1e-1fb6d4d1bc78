if exists (select * from sys.objects where object_id = object_id(N'[dbo].[FN_AT_MS_Occupancy_Exclude_Comp_MS]'))
drop function [dbo].[FN_AT_MS_Occupancy_Exclude_Comp_MS]

GO
/****** Object:  UserDefinedFunction [dbo].[FN_AT_MS_Occupancy_Exclude_Comp_MS]    Script Date: 10/18/2021 1:50:07 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/****

Function Name: FN_AT_MS_Occupancy_Exclude_Comp_MS

Input Parameters : 
	@caughtUpDate --> System today's date of property(Today's date)
	@includeZeroCapacityRT --> If this is 1 then it will also include Display_Status_ID=2's records,in short inactive room types records
	which otherwise we don't include.
    @MktExcludeCompFlag --> This flag's value is passed based on the received param value for @isExcludeCompData in the main procedure.

	

Execution: this is just an example
	Example : Data extration at Business View level.
	------------------------------------------------------
	select * from FN_AT_MS_Occupancy_Exclude_Comp_MS('2020-96-11',0,0);
	
	
Purpose: The purpose of this function is to get forecast and room solds from occupancy_fcst and mkt_accom_activity tables
past data which now is fixed and won't change will get fetched from mkt_accom_activity and future data will be fetched
from occupancy_fcst table.
		 

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
01-Jan-2013		Unknown				Unknown					Existing function											

11-Jun-2020     Archana             Mundaye                 Replaced UNION with UNION all to improve performance as this won't give duplicate results
															looking at criteria and table's unique key and also ran count(*) check on unique key	
															on 2 dbs which are downloaded from production.
12/10/2021      Shrey            	Vegda                   Whether or not to exclude comp room data in report.
****/
CREATE FUNCTION [dbo].[FN_AT_MS_Occupancy_Exclude_Comp_MS] (@caughtUpDate Date, @includeZeroCapacityRT int, @MktExcludeCompFlag varchar(5))
RETURNS TABLE
AS 
RETURN
SELECT occ.Occupancy_FCST_ID, occ.Occupancy_DT, occ.Property_ID, 
	'Forecast' as Forecast_Actual,
	msfg.Forecast_Group_ID, msfg.Mkt_Seg_ID,
	at.Accom_Class_ID, at.Accom_Type_ID,
	maa.Cancellations as accom_type_cancellations,
	maa.No_Shows as accom_type_no_shows,
	occ.Occupancy_NBR,
	occ.revenue,
	ADR =
		CASE (occ.occupancy_nbr)
			WHEN 0 THEN 0
			ELSE ROUND(occ.revenue / occ.occupancy_nbr, 2) 
		END
FROM Occupancy_FCST AS occ 
	INNER JOIN Accom_Type AS at ON occ.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID=1 and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end) and at.System_Default=0
	INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON occ.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID=1
	INNER JOIN Mkt_Accom_Activity AS maa ON occ.Property_ID = maa.Property_ID
    INNER JOIN Mkt_Seg ms on ms.Mkt_Seg_ID = maa.Mkt_Seg_ID
		AND occ.Occupancy_DT = maa.Occupancy_DT 
		AND maa.Accom_Type_ID = at.Accom_Type_ID 
		AND ms.Mkt_Seg_ID = msfg.Mkt_Seg_ID
	    AND ms.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag,','))
WHERE maa.Occupancy_DT >= @caughtUpDate		
UNION ALL
SELECT maa.Mkt_Accom_Activity_ID, maa.Occupancy_DT, maa.Property_ID, 
	'Actual' as Forecast_Actual,
	msfg.Forecast_Group_ID, msfg.Mkt_Seg_ID,
	at.Accom_Class_ID, at.Accom_Type_ID,
	maa.Cancellations  as accom_type_cancellations,
	maa.No_Shows as accom_type_no_shows,
	maa.Rooms_Sold as Occupancy_NBR,
	maa.Room_Revenue as revenue,
	ADR =
		CASE (maa.rooms_sold)
			WHEN 0 THEN 0
			ELSE ROUND(maa.room_revenue / maa.Rooms_Sold, 2) 
		END
FROM Mkt_Accom_Activity AS maa
	INNER JOIN Accom_Type AS at ON maa.Accom_Type_ID = at.Accom_Type_ID and at.Status_ID=1 and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end) and at.System_Default=0
	INNER JOIN Accom_Class AS ac ON ac.Accom_Class_ID = at.Accom_Class_ID and ac.Status_ID=1 and ac.System_Default=0
	INNER JOIN Mkt_Seg AS ms ON maa.Mkt_Seg_ID = ms.Mkt_Seg_ID and ms.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag,','))
	INNER JOIN Mkt_Seg_Forecast_Group AS msfg ON ms.MKT_SEG_ID = msfg.Mkt_Seg_ID and msfg.Status_ID=1
WHERE maa.Occupancy_DT < @caughtUpDate



