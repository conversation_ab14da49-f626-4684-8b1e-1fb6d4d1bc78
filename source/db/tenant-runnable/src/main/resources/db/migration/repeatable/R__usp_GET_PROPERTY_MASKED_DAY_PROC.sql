GO
/****** Object@  StoredProcedure [dbo].[GET_PROPERTY_MASKED_DAY_PROC]    Script Date@ 8/7/2024******/
SET ANSI_NULLS ON
 GO
SET QUOTED_IDENTIFIER ON
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GET_PROPERTY_MASKED_DAY_PROC]'))
BEGIN
		DROP PROCEDURE [dbo].[GET_PROPERTY_MASKED_DAY_PROC]
END
GO
/****************************************************************************
Procedure@  GET_PROPERTY_MASKED_DAY_PROC
Author@     Suraj Kulkarni
Date@       8/7/2024

*****************************************************************************/

CREATE PROCEDURE [dbo].[GET_PROPERTY_MASKED_DAY_PROC] @startDate Date, @endDate Date, @maskDaysThreshold Int
AS
BEGIN
    SET NOCOUNT ON
SELECT Property_ID,
       count(DISTINCT CalendarDay) AS masked_days
FROM (SELECT Property_ID, CalendarDay
      FROM (SELECT Property_ID,
                   Start_Date,
                   End_date,
                   data_type_name mask_type
            FROM IP_Cfg_Mark_Property_Date A
                     INNER JOIN ip_cfg_mark_date_data_type B
                                ON A.IP_CFG_DATA_TYPE_ID = B.ip_cfg_data_type_id AND Status_ID = 1 AND data_type_name IN ('BOTH','SUMMARY')
           ) AS IP_Cfg_Mark_Property_Date_TEMP
          CROSS APPLY (SELECT TOP (DATEDIFF(DAY, start_Date, End_Date) + 1) DATEADD(DAY,
                                                                                               ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) -1,
                                                                                               start_Date) AS CalendarDay
                                  FROM master.dbo.spt_values) AS DateRange) AS CalendarDaysTable
where CalendarDay between @startDate and @endDate
group by Property_ID
having count(DISTINCT CalendarDay) > @maskDaysThreshold
END