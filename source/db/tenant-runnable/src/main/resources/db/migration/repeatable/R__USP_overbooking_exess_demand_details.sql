IF
EXISTS(SELECT *
          FROM sys.objects
          WHERE object_id = object_id(N'[usp_overbooking_excess_demand_details]'))
    DROP PROCEDURE
[usp_overbooking_excess_demand_details]
GO
/*******************************************************************************************************************************************
Store Procedure Name: usp_overbooking_excess_demand_details
Output :  Reasons and details of excess demand
Execution: this is just an example
	Example 1: usp_overbooking_excess_demand_details
	-----------------------------------------
	exec dbo.usp_overbooking_excess_demand_details
Release Update:
Release_Dt		First_Name			 Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
12/10/2024	     Vasudev 				Raut					Initial Version
***************************************************************************************/
CREATE PROCEDURE [dbo].[usp_overbooking_excess_demand_details]
    @occupancyDate DATE,
    @propertyId INT
AS
BEGIN

    SET
NOCOUNT ON;


CREATE TABLE #TempResults
(
    Property_Name          nvarchar(300),
    dow                    varchar(10),
    Occupancy_DT           date,
    Accom_Class_Name       nvarchar(100),
    Accom_Capacity         int,
    Rooms_Sold             int,
    onBooks_Revenue        numeric(19, 5),
    onBooks_ADR            numeric(19, 5),
    onBooks_REVPAR         numeric(19, 5),
    Arrivals               int,
    Departures             int,
    Rooms_Not_Avail_Maint  int,
    Rooms_Not_Avail_Other  int,
    Cancellations          int,
    No_Shows               int,
    Room_Revenue           numeric(19, 5),
    Food_Revenue           numeric(19, 5),
    Total_Revenue          numeric(19, 5),
    Occupancy_NBR          numeric(19, 2),
    REVPAR                 numeric(19, 5),
    ADR                    numeric(19, 5),
    Demand_Rooms_Sold      int,
    User_Demand_Rooms_Sold int,
    LRV                    numeric(19, 5),
    price                  numeric(19, 5),
    LOS                    nvarchar(50),
    price1                 numeric(19, 5),
    LOS1                   nvarchar(50),
    price2                 numeric(19, 5),
    LOS2                   nvarchar(50),
    price3                 numeric(19, 5),
    LOS3                   nvarchar(50),
    price4                 numeric(19, 5),
    LOS4                   nvarchar(50),
    price5                 numeric(19, 5),
    LOS5                   nvarchar(50),
    price6                 numeric(19, 5),
    LOS6                   nvarchar(50),
    price7                 numeric(19, 5),
    LOS7                   nvarchar(50),
    price8                 numeric(19, 5),
    LOS8                   nvarchar(50),
    Remaining_Capacity     int,
    onBooks_Profit         numeric(19, 5),
    Profit                 numeric(19, 5),
    onBooks_ProPOR         numeric(19, 5),
    onBooks_ProPAR         numeric(19, 5),
    ProPOR                 numeric(19, 5),
    ProPAR                 numeric(19, 5),
    Projected_Rooms_Sold   numeric(19, 5),
    Projected_Room_Revenue numeric(19, 5),
    Projected_ADR          numeric(19, 5)
);

INSERT INTO #TempResults
    EXEC dbo.usp_dataextraction_report_rc
    @property_id = @propertyId,
    @record_type_id = 3,
    @process_status_id = 5,
    @start_date = @occupancyDate,
    @end_date = @occupancyDate,
    @isRollingDate = 0,
    @rolling_start_date = NULL,
    @rolling_end_date = NULL,
    @use_physical_capacity = 0,
    @includeZeroCapacityRT = 0,
    @marketSegment_ids = '-1',
    @includePseudoRT = 1,
    @isExcludeCompRoom = 1;

SELECT
       Accom_Class_Name                          as Room_Class,
       Accom_Capacity                            as Capacity_This_Year,
       Rooms_Sold                                as Occupancy_On_Books_This_Year,
       Occupancy_NBR                             as Occupancy_Forecast_This_Year,
       User_Demand_Rooms_Sold                    as User_Demand_This_Year,
       CASE
           WHEN (User_Demand_Rooms_Sold - Accom_Capacity) < 0 THEN NULL
           ELSE (User_Demand_Rooms_Sold - Accom_Capacity)
           END AS Excess_Demand,
       CASE
           WHEN (Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other) < 0 THEN NULL
           ELSE (Accom_Capacity - Rooms_Not_Avail_Maint - Rooms_Not_Avail_Other)
           END AS Effective_Capacity
FROM #TempResults;

DROP TABLE #TempResults;

END;