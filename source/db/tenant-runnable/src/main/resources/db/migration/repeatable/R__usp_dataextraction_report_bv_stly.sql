if exists(select *
          from sys.objects
          where object_id = object_id(N'[dbo].[usp_dataextraction_report_bv_stly]'))
    drop procedure [dbo].[usp_dataextraction_report_bv_stly]

GO

/****** Object:  StoredProcedure [dbo].[usp_dataextraction_report_rt_stly]    Script Date: 7/27/2020 12:53:48 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

create procedure [dbo].[usp_dataextraction_report_bv_stly](@property_id int,
                                                           @ly_start_date date,
                                                           @ly_end_date date,
                                                           @ly_businessdate date,
                                                           @is_stly_or_2y int,
                                                           @MktExcludeCompFlag varchar(5))
as
begin

    select Occupancy_DT,
           Business_Group_Name,
           SUM(rooms_sold)   as rooms_sold,
           SUM(room_revenue) as room_revenue
    from (
             select pace.property_Id,
                    bv.Business_Group_Name,
                    (case
                         when @is_stly_or_2y = 1 then DateAdd(WEEK, 52, pace.Occupancy_DT)
                         else DateAdd(WEEK, 104, pace.Occupancy_DT) end
                    ) as Occupancy_DT,
                    rooms_sold,
                    room_revenue
             from PACE_Mkt_Activity pace
                      join Mkt_Seg mkt
                           on mkt.Property_ID = pace.property_Id
                               and mkt.Mkt_Seg_ID = pace.Mkt_Seg_ID
                               and mkt.Exclude_CompHouse_Data_Display IN (select value from varchartoint(@MktExcludeCompFlag, ','))
                      join Mkt_Seg_Business_Group bvmkt
                           on bvmkt.Mkt_Seg_ID = mkt.Mkt_Seg_ID
                      join Business_Group bv
                           on bv.property_Id = pace.property_Id
                               and bv.Business_Group_ID = bvmkt.Business_Group_ID
                               and bv.status_id = 1
             where pace.Occupancy_DT between @ly_start_date and @ly_end_date
               and pace.Property_ID = @property_id
               and pace.Business_Day_End_DT = @ly_businessdate
         ) as bv_stly
    group by Occupancy_DT, Business_Group_Name
    order by Occupancy_DT, Business_Group_Name

end
GO
