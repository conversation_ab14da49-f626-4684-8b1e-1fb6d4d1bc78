DROP PROCEDURE IF EXISTS [opera].[usp_yield_category_rule_batch_load]
    GO
DROP TYPE IF EXISTS [opera].[Yield_category_rule_table_batch]
    GO

    IF NOT EXISTS (SELECT * FROM sys.types WHERE is_table_type = 1 AND schema_id=(select schema_id from sys.schemas where name ='opera') AND name = 'Yield_category_rule_table_batch')
BEGIN
CREATE TYPE [opera].[Yield_category_rule_table_batch] AS TABLE
    (
    [Market_Code] [nvarchar](50) NOT NULL,
    [Rate_Code] [nvarchar](255) NULL,
    [Analytical_Market_Code] [nvarchar](50) NOT NULL,
    [Booking_Start_DT] [date] NULL,
    [Booking_End_DT] [date] NULL,
    [Rank] [int] NOT NULL
    )
END
GO

CREATE PROCEDURE [opera].[usp_yield_category_rule_batch_load]
    @Yield_category_rule_table_batch Yield_category_rule_table_batch READONLY
AS
BEGIN
INSERT INTO [opera].[yield_category_rule] ([Market_Code], [Rate_Code], [Analytical_Market_Code], [Booking_Start_DT], [Booking_End_DT], [Rank])
SELECT [Market_Code], [Rate_Code], [Analytical_Market_Code], [Booking_Start_DT], [Booking_End_DT], [Rank] FROM @Yield_category_rule_table_batch;
END
