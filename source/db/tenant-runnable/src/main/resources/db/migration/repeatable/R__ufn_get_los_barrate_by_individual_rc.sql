if exists (select * from sys.objects where object_id = object_id(N'[ufn_get_los_barrate_by_individual_rc]'))
drop function [ufn_get_los_barrate_by_individual_rc]

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

create function [dbo].[ufn_get_los_barrate_by_individual_rc]
(
		@property_id int,
		@roomclass_id varchar(500),
		@start_date date,
		@end_date date,
		@includeZeroCapacityRT int
)		
returns  @los_barrate table
	(	
		
		property_id	int,
		arrival_dt date,
		accom_class_id int,
		bar_los1 float,
		bar_los2 float,
		bar_los3 float,
		bar_los4 float,
		bar_los5 float,
		bar_los6 float,
		bar_los7 float,
		bar_los8 float,
		bar_by_day float
	)
as
begin
		insert into @los_barrate
		select 
			property_id,
			arrival_dt,
			accom_class_id,
			isnull([1],0.0) as [1],
			isnull([2],0.0) as [2],
			isnull([3],0.0) as [3],
			isnull([4],0.0) as [4],
			isnull([5],0.0) as [5],
			isnull([6],0.0) as [6],
			isnull([7],0.0) as [7],
			isnull([8],0.0) as [8],
			isnull([-1],0.0) as [-1]
		from
		  (
				select property_id,arrival_dt,accom_class_id,los,min(rate)as barrate
				from
				(
					select dbo.property_id,arrival_dt,ac.accom_class_id,rate_code_name ,los,
						rate=
						case (datename(dw,arrival_dt))
							when 'monday' then rud.monday
							when 'tuesday' then rud.tuesday
							when 'wednesday' then rud.wednesday
							when 'thursday' then rud.thursday
							when 'friday' then rud.friday
							when 'saturday' then rud.saturday
							when 'sunday' then rud.sunday
						end
					from decision_bar_output dbo
							inner join accom_class ac on dbo.accom_class_id = ac.accom_class_id and ac.accom_class_id in (select value from dbo.varcharToInt(@roomclass_id,','))
							inner join rate_unqualified ru on dbo.rate_unqualified_id = ru.rate_unqualified_id
									--and dbo.arrival_dt between ru.start_date_dt and ru.end_date_dt
							inner join rate_unqualified_details rud on ru.rate_unqualified_id=rud.rate_unqualified_id
							inner join accom_type at on rud.accom_type_id=at.accom_type_id and at.Status_ID=1 and at.System_Default=0 and at.accom_class_id in (select value from dbo.varcharToInt(@roomclass_id,','))
							where dbo.property_id = @property_id and dbo.arrival_dt between rud.start_date_dt and rud.end_date_dt
							and dbo.arrival_dt between @start_date and @end_date
							and at.Display_Status_ID in (select 1 union select case when @includeZeroCapacityRT = 1 then 2 else 1 end)
				) bar group by property_id,arrival_dt,accom_class_id,rate_code_name,los
			) as src
		pivot
			(
				avg(barrate)
				for los in ([1],[2],[3],[4],[5],[6],[7],[8],[-1])
			) as pivottable2
		order by arrival_dt
return
end

