
IF EXISTS (SELECT *
           FROM   sys.objects
           WHERE  object_id = Object_id(N'[dbo].[usp_automated_rc_rt_config_evaluation]')
                  AND type IN ( N'P', N'PC' ))
  DROP PROCEDURE [dbo].[usp_automated_rc_rt_config_evaluation];

SET ansi_nulls ON

go

SET quoted_identifier ON

go

CREATE PROCEDURE [dbo].[usp_automated_rc_rt_config_evaluation]
AS
BEGIN

set nocount on

declare @rcrt_mapping_changed tinyint
declare @master_class_changed tinyint
declare @rc_ranking_changed tinyint
declare @new_rc_added tinyint
declare @obk_changed tinyint
declare @rcs_with_changed_mappings nvarchar(max)
declare @new_rcs nvarchar(max)
declare @ats_with_changed_rc nvarchar(max)


select @rcrt_mapping_changed = max(iif(old.accom_class_code=new.accom_class_code, 0, 1))
from
    (select accom_type_code, ac.Accom_Class_Code
     from accom_type at join accom_class ac on at.Accom_Class_ID=ac.Accom_Class_ID
    ) old
        join
    (select accom_type_code, Accom_Class_Code
     from rc_rt_mapping
    ) new on old.accom_type_code = new.accom_type_code



select
        @master_class_changed =
        iif(
                    (select top 1 Accom_Class_Code
                     from accom_class
                     where master_class = 1
                    )=
                    (select top 1 Accom_Class_Code
                     from rc_rt_mapping
                     where master_class = 1
                    ),
                    0,1
            )



select @rc_ranking_changed = max(iif(old.accom_class_code=new.accom_class_code, 0, 1))
from
    (select ac.Accom_Class_Code, ac.Rank_Order
     from accom_class ac
     where Status_ID=1 and System_Default=0
    ) old
        full outer join
    (select distinct Accom_Class_Code, rank_order
     from rc_rt_mapping
    ) new on old.rank_order = new.rank_order


-- codes of new room-classes with changed rt-rc mappings
declare @new_rc_codes List_To_CSV_Input
insert into @new_rc_codes
select new.Accom_Class_Code
from
    (select distinct Accom_Class_Code, rank_order
     from rc_rt_mapping
    ) new
        left join
    Accom_Class old
    on old.Accom_Class_Code= new.accom_class_code
where old.Accom_Class_Code is null

set @new_rcs = dbo.list_to_csv(@new_rc_codes)

select @new_rc_added = iif(count(*)=0, 0, 1)
from @new_rc_codes



select @obk_changed=iif(count(*)=0,0,1)
from
    (select accom_type_code,
            iif(obk.Sunday_Overbooking_Type_ID=1,1,0) + iif(obk.Monday_Overbooking_Type_ID=1,1,0)
                + iif(obk.Tuesday_Overbooking_Type_ID=1,1,0) + iif(obk.Wednesday_Overbooking_Type_ID=1,1,0)
                + iif(obk.Thursday_Overbooking_Type_ID=1,1,0) + iif(obk.Friday_Overbooking_Type_ID=1,1,0)
                + iif(obk.Saturday_Overbooking_Type_ID=1,1,0)
                dow_obk_status
     from accom_type at join overbooking_accom obk on at.accom_type_id=obk.accom_type_id
    ) old
        join
    (select accom_type_code, iif(overbooking_allowed=1, 7, 0) dow_obk_status
     from rc_rt_mapping
    ) new on old.accom_type_code = new.accom_type_code
where old.dow_obk_status <> new.dow_obk_status


-- ids of room-classes with changed rt-rc mappings
declare @changed_rc_ids List_To_CSV_Input

insert into @changed_rc_ids
select distinct convert(nvarchar, old.Accom_Class_ID)
from
    (select accom_type_code, ac.Accom_Class_Code, ac.Accom_Class_ID
     from accom_type at join accom_class ac on at.Accom_Class_ID=ac.Accom_Class_ID
    ) old
        join
    (select accom_type_code, Accom_Class_Code
     from rc_rt_mapping
    ) new on old.accom_type_code = new.accom_type_code
where old.accom_class_code <> new.accom_class_code

set @rcs_with_changed_mappings = dbo.list_to_csv(@changed_rc_ids)



-- ids of room-types with changed rt-rc mappings
declare @changed_at_ids List_To_CSV_Input

insert into @changed_at_ids
select distinct convert(nvarchar, old.accom_type_id)
from
    (select accom_type_code, ac.Accom_Class_Code, accom_type_id
     from accom_type at join accom_class ac on at.Accom_Class_ID=ac.Accom_Class_ID
    ) old
        join
    (select accom_type_code, Accom_Class_Code
     from rc_rt_mapping
    ) new on old.accom_type_code = new.accom_type_code
where old.accom_class_code <> new.accom_class_code

set @ats_with_changed_rc = dbo.list_to_csv(@changed_at_ids)


-- return evaluated flags
select @rcrt_mapping_changed rcrt_mapping_changed, @master_class_changed master_class_changed,
       @rc_ranking_changed rc_ranking_changed, @new_rc_added new_rc_added, @obk_changed obk_changed,
       @rcs_with_changed_mappings rcs_with_changed_mappings, @new_rcs new_rcs, @ats_with_changed_rc ats_with_changed_rc


end
go 