DROP PROCEDURE IF EXISTS usp_hotel_pickup_report_property
GO

/*************************************************************************************

Procedure Name: usp_hotel_pickup_report_property

Input Parameters : 
	@property_id --> property Id associated with a property (e.g.,'BOSCO' id from the property table is 12)
	@record_type_id --> Type of file_metadata record (3,9,11..etc.)
	@process_status_id --> Associated with different states of file_metdata record (5,12,13...)
	@business_start_dt --> this is the derived version of caughtup_date(start_date)..for more details please check the data dictionary.
	@business_end_dt --> this is the derived version of caughtup_date(end_date)..for more details please check the data dictionary.
	@start_date --> occupancy_start_date ('2011-07-01')
	@end_date --> occupancy_end_date ('2011-07-31')
	@isRollingDate --> 1 if dates passed are rolling dates and need to be converted into actual dates on the fly
	@rolling_business_start_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_business_end_dt --> used when @isRollingDate is 1, else, this can be blank
	@rolling_start_date --> used when @isRollingDate is 1, else, this can be blank
	@rolling_end_date --> used when @isRollingDate is 1, else, this can be blank
	@use_physical_capacity --> whether or not to use physical capacity in revpar calculations
	
Ouput Parameter : NA

Execution: this is just an example
	Example 1: Hotel Pickup Report
	--------------------------------------
	exec dbo.usp_hotel_pickup_report_property 18,3,13,'2011-06-18','2011-06-30','2011-07-01','2011-07-31',1
	

	
Purpose: The purpose of this procedure is to report ----- please elaborate here

Assumptions : NA
		 
Author: Atul and Manohar

Release Update:
Release_Dt		First_Name			Last_Name				Release Comments
----------	----------------	-------------------		-------------------------------
09/15/2011	Atul				Shendye					Initial Version
09/15/2011	Manohar				Sunkum					Refactored
03/10/2017    Sam         Naden           Adding in param use_physical_capacity
11/16/2021 		Shilpa 				Shaha					BHASK-1974 : Changed logic not to substract actual data from actual data in case no data present in pace.This will applicable while calculating either pickup/change
12/09/2021 		Shilpa 				Shaha					FEYNMAN-877 Pickup/Change Report: Data Discrepancy for past dates
09/11/2023		Shrey				Vegda					KANADA-822 : Correct the Business logic for pickup  report if  Activity start date has zero solds
***************************************************************************************/

CREATE procedure [dbo].[usp_hotel_pickup_report_property]
(
	@property_id int,
	@record_type_id int,
	@process_status_id int,
	@business_start_dt date,
	@business_end_dt date,
	@start_date date,
	@end_date date,
	@isRollingDate int,
	@rolling_business_start_dt nvarchar(50),
	@rolling_business_end_dt nvarchar(50),		
	@rolling_start_date nvarchar(50),
	@rolling_end_date nvarchar(50),
	@use_physical_capacity int
)		

as

begin	
	declare @caughtupdate date 
	set @caughtupdate = (select  dbo.ufn_get_caughtup_date_by_property(@property_id,3,13)) --> extract caughtup date for a property
	if(@isRollingDate=1)
	begin
		set @business_start_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_start_dt ,@caughtupdate))
		set @business_end_dt = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_business_end_dt ,@caughtupdate))
		set @start_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_start_date ,@caughtupdate))
		set @end_date = (select absolute_date from ufn_get_absolute_dates_from_rolling_dates (@rolling_end_date ,@caughtupdate))
	end
		
	select
		a.occupancy_dt,
		datename(dw,a.occupancy_dt) as dow,
		a.property_id,
		isnull(a.rooms_sold,0) as rooms_sold,
		g.special_event_name as special_event,
		isnull(a.outoforder,0) as outoforder,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(d.end_date_rooms_sold,isnull(a.rooms_sold,0)) - ISNULL(d.start_date_rooms_sold, IIF(a.occupancy_dt > @business_start_dt,0,isnull(a.rooms_sold,0))) as numeric (19,2))
		else
			cast(isnull(d.end_date_rooms_sold,0.0) - ISNULL(d.start_date_rooms_sold,0.0) as numeric (19,2)) 
		end as rooms_solds_pickup,
		cast(isnull(e.occupancy_nbr,0) as numeric (19,2))    as occupancy_nbr,
		cast(isnull(e.occupancy_percent,0.0 ) as numeric (19,2))   as occupancy_percent,
		cast(isnull(e.occupancy_percent_phyCap,0.0 ) as numeric (19,2))   as occupancy_percent_phyCap,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_occupancy_nbr,isnull(e.occupancy_nbr,0)) - ISNULL(f.start_date_occupancy_nbr, IIF(a.occupancy_dt > @business_start_dt,0,isnull(e.occupancy_nbr,0))) as numeric (19,2))
		else
			cast(isnull(f.end_date_occupancy_nbr,0.0) - ISNULL(f.start_date_occupancy_nbr,0.0) as numeric (19,2))    
		end as occupancy_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_occupancy_perc, isnull(e.occupancy_percent,0.0 )) - ISNULL(f.start_date_occupancy_perc, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(e.occupancy_percent,0.0 ))) as numeric (19,2))
		else
			cast(isnull(f.end_date_occupancy_perc, 0.0) - ISNULL(f.start_date_occupancy_perc,0.0) as numeric (19,2))   
		end as occupancy_perc_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_occupancy_perc_PhyCap,isnull(e.occupancy_percent_phyCap,0.0 )) - ISNULL(f.start_date_occupancy_perc_PhyCap, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(e.occupancy_percent_phyCap,0.0 ))) as numeric (19,2))
		else
			cast(isnull(f.end_date_occupancy_perc_PhyCap,0.0) - ISNULL(f.start_date_occupancy_perc_PhyCap, 0.0) as numeric (19,2)) 
		end as occupancy_perc_pickup_phyCap,
		cast(isnull(e.revenue,0.0) as numeric (19,2))    as revenue,
		cast(isnull(e.adr,0.0) as numeric (19,2))    as adr,
		cast(isnull(e.revpar,0.0) as numeric (19,2))    as revpar, --RevPAR Forecast() Current
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_revpar, isnull(e.revpar,0.0)) - ISNULL(f.start_date_revpar, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(e.revpar,0.0))) as numeric (19,2))
		else
			cast(isnull(f.end_date_revpar, 0.0) - ISNULL(f.start_date_revpar, 0.0) as numeric (19,2)) 
		end as revpar_pickup, --RevPAR Forecast() Activity Range Pick Up
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_revenue,isnull(e.revenue,0.0)) - ISNULL(f.start_date_revenue, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(e.revenue,0.0))) as numeric (19,2))
		else
			cast(isnull(f.end_date_revenue,0.0) - ISNULL(f.start_date_revenue, 0.0) as numeric (19,2)) 
		end as revenue_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(f.end_date_adr,isnull(e.adr,0.0)) - ISNULL(f.start_date_adr, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(e.adr,0.0))) as numeric (19,2))
		else
			cast(isnull(f.end_date_adr,0.0) - ISNULL(f.start_date_adr, 0.0) as numeric (19,2))    
		end as adr_pickup,
		cast(isnull(a.room_revenue,0.0) as numeric (19,2))    as booked_revenue,
		cast(isnull(a.adr,0.0) as numeric (19,2))    as booked_adr,
		cast(isnull(a.revpar,0.0) as numeric (19,2))    as booked_revpar, --RevPAR On Books() Current
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(d.end_date_adr,isnull(a.adr,0.0)) - ISNULL(d.start_date_adr, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(a.adr,0.0))) as numeric (19,2))
		else
			cast(isnull(d.end_date_adr,0.0) - ISNULL(d.start_date_adr, 0.0) as numeric (19,2)) 
		end as booked_adr_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(d.end_date_revpar,isnull(a.revpar,0.0)) - ISNULL(d.start_date_revpar, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(a.revpar,0.0))) as numeric (19,2))
		else
			cast(isnull(d.end_date_revpar,0.0) - ISNULL(d.start_date_revpar, 0.0) as numeric (19,2)) 
		end as booked_revpar_pickup, --RevPAR On Books() Activity Range Pick Up
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(d.end_date_room_revenue,isnull(a.room_revenue,0.0)) - ISNULL(d.start_date_room_revenue, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(a.room_revenue,0.0))) as numeric (19,2))
		else
			cast(isnull(d.end_date_room_revenue,0.0) - ISNULL(d.start_date_room_revenue, 0.0) as numeric (19,2)) 
		end as booked_revenue_pickup,
		-----------Profit Columns--------
		cast(isnull(proffcst.profit,0.0) as numeric (19,2))    as profit,
		cast(isnull(proffcst.propor,0.0) as numeric (19,2))    as propor,
		cast(isnull(proffcst.propar,0.0) as numeric (19,2))    as propar, --ProPAR Forecast() Current
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(h.end_date_profit,isnull(proffcst.profit,0.0)) - ISNULL(h.start_date_profit, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(proffcst.profit,0.0))) as numeric (19,2))
		else
			cast(isnull(h.end_date_profit,0.0) - ISNULL(h.start_date_profit, 0.0) as numeric (19,2)) 
		end as profit_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(h.end_date_propor,isnull(proffcst.propor,0.0)) - ISNULL(h.start_date_propor, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(proffcst.propor,0.0))) as numeric (19,2))
		else
			cast(isnull(h.end_date_propor,0.0) - ISNULL(h.start_date_propor, 0.0) as numeric (19,2))   
		end as propor_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(h.end_date_propar, isnull(proffcst.ProPAR,0.0)) - ISNULL(h.start_date_propar, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(proffcst.ProPAR,0.0))) as numeric (19,2))
		else
			cast(isnull(h.end_date_propar, 0.0) - ISNULL(h.start_date_propar, 0.0) as numeric (19,2)) 
		end as propar_pickup, --proPAR Forecast() Activity Range Pick Up
		cast(isnull(profcurr.profit,0.0) as numeric (19,2))    as booked_profit,
		cast(isnull(profcurr.propor,0.0) as numeric (19,2))    as booked_propor,
		cast(isnull(profcurr.propar,0.0) as numeric (19,2))    as booked_propar, --proPAR On Books() Current
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(bookedProfit.end_date_room_profit,isnull(profcurr.profit,0.0)) - ISNULL(bookedProfit.start_date_room_profit, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(profcurr.profit,0.0))) as numeric (19,2))
		else
			cast(isnull(bookedProfit.end_date_room_profit,0.0) - ISNULL(bookedProfit.start_date_room_profit, 0.0) as numeric (19,2)) 
		end as booked_profit_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(bookedProfit.end_date_propor,isnull(profcurr.propor,0.0)) - ISNULL(bookedProfit.start_date_propor, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(profcurr.propor,0.0))) as numeric (19,2))
		else
			cast(isnull(bookedProfit.end_date_propor,0.0) - ISNULL(bookedProfit.start_date_propor, 0.0) as numeric (19,2)) 
		end as booked_propor_pickup,
		case when a.occupancy_dt <= @business_end_dt then
			cast(isnull(bookedProfit.end_date_propar,isnull(profcurr.propar,0.0)) - ISNULL(bookedProfit.start_date_propar, IIF(a.occupancy_dt > @business_start_dt,0.0,isnull(profcurr.propar,0.0))) as numeric (19,2))
		else
			cast(isnull(bookedProfit.end_date_propar,0.0) - ISNULL(bookedProfit.start_date_propar, 0.0) as numeric (19,2)) 
		end as booked_propar_pickup --proPAR On Books() Activity Range Pick Up
		
	from
	(
		select * from dbo.ufn_get_adr_revpar_by_rc (@property_id,'',@start_date,@end_date,@use_physical_capacity,1)
	) as a  
	left outer join
	( 
		 select c.property_id, c.occupancy_dt,
		b.rooms_sold as end_date_rooms_sold, c.rooms_sold as start_date_rooms_sold, b.adr as end_date_adr, c.adr as start_date_adr, 
		b.revpar as end_date_revpar, c.revpar as start_date_revpar, b.room_revenue as end_date_room_revenue, c.room_revenue as start_date_room_revenue
		 from 
		(
			select * from dbo.ufn_get_adr_revpar_asof_businessdate_by_property (@property_id,@business_end_dt,@start_date,@end_date,@use_physical_capacity)
		) as b ---> comments here
		 right outer join 
		(
			select * from dbo.ufn_get_adr_revpar_asof_businessdate_by_property (@property_id,@business_start_dt,@start_date,@end_date,@use_physical_capacity)
		) as c on ---> comments here
		b.occupancy_dt=c.occupancy_dt and b.property_id=c.property_id
	

	) as d on a.occupancy_dt=d.occupancy_dt and a.property_id=d.property_id
	left outer join(
	select * from dbo.ufn_get_propor_propar_by_rc (@property_id,'',@start_date,@end_date,@use_physical_capacity,1)
	) as profcurr on a.occupancy_dt=profcurr.occupancy_dt and a.property_id=profcurr.property_id
	
	left outer join
	( 
		select bookedProfitR.property_id, bookedProfitR.occupancy_dt,
		bookedProfitL.rooms_sold as end_date_rooms_sold, bookedProfitR.rooms_sold as start_date_rooms_sold, bookedProfitL.propor as end_date_propor, bookedProfitR.propor as start_date_propor, 
		bookedProfitL.propar as end_date_propar, bookedProfitR.propar as start_date_propar, bookedProfitL.profit as end_date_room_profit, bookedProfitR.profit as start_date_room_profit
		 from 
		(
			select * from dbo.ufn_get_propor_propar_asof_businessdate_by_property (@property_id,@business_end_dt,@start_date,@end_date,@use_physical_capacity)
		) as bookedProfitL
		 right outer join 
		(
			select * from dbo.ufn_get_propor_propar_asof_businessdate_by_property (@property_id,@business_start_dt,@start_date,@end_date,@use_physical_capacity)
		) as bookedProfitR on
		bookedProfitL.occupancy_dt=bookedProfitR.occupancy_dt and bookedProfitL.property_id=bookedProfitR.property_id
	
	) as bookedProfit on a.occupancy_dt=bookedProfit.occupancy_dt and a.property_id=bookedProfit.property_id

	left outer join
	(
		select property_id,occupancy_dt,occupancy_percent,
		 case Total_Accom_Capacity 
			when 0 then 0
			else (Occupancy_NBR / Total_Accom_Capacity)*100
		 end as occupancy_percent_phyCap,
		revpar,occupancy_nbr,revenue,adr
		from dbo.fn_occupancy_forecast(
			(
				select  dbo.ufn_get_caughtup_date_by_property(@property_id,@record_type_id,@process_status_id) --> extract caughtup date for a property
			), @use_physical_capacity
		)
		
		where property_id=@property_id and occupancy_dt between @start_date and @end_date
	) as e on e.occupancy_dt=a.occupancy_dt and e.property_id=a.property_id
	left outer join
	(
		select property_id,occupancy_dt,
		ProPAR,profit,propor
		from dbo.ufn_get_ProPAR_ProPOR(
			(
				select  dbo.ufn_get_caughtup_date_by_property(@property_id,@record_type_id,@process_status_id) --> extract caughtup date for a property
			), @use_physical_capacity
		)
		
		where property_id=@property_id and occupancy_dt between @start_date and @end_date
	) as proffcst on proffcst.occupancy_dt=a.occupancy_dt and proffcst.property_id=a.property_id
	
  	left outer join
	(
		 select subquery2.property_id,subquery2.occupancy_dt,
			subquery1.occupancy_nbr as end_date_occupancy_nbr, subquery2.occupancy_nbr as start_date_occupancy_nbr,
			subquery1.occupancy_perc as end_date_occupancy_perc, subquery2.occupancy_perc as start_date_occupancy_perc,
			subquery1.occupancy_perc_PhyCap as end_date_occupancy_perc_PhyCap, subquery2.occupancy_perc_PhyCap as start_date_occupancy_perc_PhyCap,
			subquery1.revenue as end_date_revenue, subquery2.revenue as start_date_revenue, subquery1.adr as end_date_adr,
			subquery2.adr as start_date_adr, subquery1.revpar as end_date_revpar, subquery2.revpar as start_date_revpar
		from
		(
			select * from dbo.ufn_get_occupancy_forecast_asof_businessdate_by_property (@property_id,@business_end_dt,@start_date,@end_date,@use_physical_capacity)
		) subquery1 
		right outer join
		(	
			select * from dbo.ufn_get_occupancy_forecast_asof_businessdate_by_property (@property_id,@business_start_dt,@start_date,@end_date,@use_physical_capacity)
		) subquery2 on subquery1.property_id=subquery2.property_id and subquery1.occupancy_dt=subquery2.occupancy_dt
	) as f on f.occupancy_dt=a.occupancy_dt and f.property_id=a.property_id
	left outer join
	(
			select subquery2.property_id,subquery2.occupancy_dt,
			subquery1.profit as end_date_profit, subquery2.profit as start_date_profit , subquery1.propor as end_date_propor,
			subquery2.propor as start_date_propor, subquery1.propar as end_date_propar, subquery2.propar as start_date_propar
		from
		(
			select * from dbo.ufn_get_occupancy_forecast_profit_asof_businessdate_by_property (@property_id,@business_end_dt,@start_date,@end_date,@use_physical_capacity)
		) subquery1 
		right outer join
		(	
			select * from dbo.ufn_get_occupancy_forecast_profit_asof_businessdate_by_property (@property_id,@business_start_dt,@start_date,@end_date,@use_physical_capacity)
		) subquery2 on subquery1.property_id=subquery2.property_id and subquery1.occupancy_dt=subquery2.occupancy_dt
	) as h on h.occupancy_dt=a.occupancy_dt and h.property_id=a.property_id
	left outer join
	(
	  	select * from ufn_get_special_events_including_information_only_events_by_property (@property_id,@start_date, @end_date)
	) as g on a.occupancy_dt = g.event_cal_date and a.property_id=g.property_id
	order by a.occupancy_dt
end

