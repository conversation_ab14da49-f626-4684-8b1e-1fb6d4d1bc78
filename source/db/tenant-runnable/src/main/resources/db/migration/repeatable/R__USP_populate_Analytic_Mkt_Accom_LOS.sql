drop procedure if exists [dbo].[usp_populate_Analytic_Mkt_Accom_Los]
GO
create procedure [dbo].[usp_populate_Analytic_Mkt_Accom_Los]
(
            @minArrivalDate date,
            @totalRateEnabled bit
)
as
begin

IF Object_id('tempdb..#Reservation_night') IS NOT NULL
DROP TABLE #reservation_night
SELECT rn.Reservation_identifier ,
       rn.Accom_type_id ,
       Mkt_seg_id,
       rn.Arrival_dt ,
       Departure_dt ,
       coalesce(rs.short_status,'SS')  AS Individual_Status,
	   case when rn.Individual_Status in ('XX', 'NS') then 0
       else
            case when @totalRateEnabled = 1 then Rate_value
            else Room_revenue end
       end as Room_Revenue,
       case when rn.Individual_Status in ('XX', 'NS') then 0
	   else Total_revenue end as Total_Revenue
	   INTO #reservation_night
FROM Dbo.Reservation_night rn WITH(nolock)
inner join (
	select Reservation_identifier, min(arrival_DT) as arrival_DT from reservation_night
	group by reservation_identifier
) rn1 on rn.reservation_identifier = rn1.reservation_identifier
left join reservation_status rs on rn.Individual_Status = rs.expanded_status
inner join Accom_Type at on rn.Accom_Type_ID = at.Accom_Type_ID
and rn1.Arrival_dt >= @minArrivalDate and rn.arrival_DT <> rn.Departure_dt
and at.Status_ID <> 6


  INSERT INTO  dbo.Analytic_Mkt_Accom_Los_Inv (Analytic_Mkt_Accom_Los_Inv_ID, Arrival_Dt, LOS, Mkt_Seg_Id, Accom_Type_Id, Total_Revenue, Room_Revenue, Cancellations, No_Shows, Arrivals)
  SELECT 'ID_' + Cast(Arrival_dt AS Nvarchar(10)) + '_' + Cast(Los AS Nvarchar(10)) + '_' + Cast(Mkt_seg_id AS Nvarchar(10)) + '_' + Cast(Accom_type_id AS Nvarchar(10)) AS Analytic_mkt_accom_los_inv_id,
         Arrival_dt,
         Los,
         Mkt_seg_id,
         Accom_type_id,
         Sum(Total_revenue) AS Total_revenue,
         Sum(Room_revenue) AS Room_revenue,
         Sum(Cancellations) AS Cancellations,
         Sum(No_shows) AS No_shows,
         Sum(Arrivals) AS Arrivals
  FROM
    ( SELECT Arrival_dt,
             Los,
             Mkt_seg_id,
             Accom_type_id,
             Individual_status,
             Number_of_reservations,
             Total_revenue,
             Room_revenue,
             CASE
                 WHEN M.Individual_status = 'XX' THEN M.Number_of_reservations
                 ELSE 0
             END AS Cancellations,
             CASE
                 WHEN M.Individual_status = 'NS' THEN M.Number_of_reservations
                 ELSE 0
             END AS No_shows,
             CASE
                 WHEN M.Individual_status = 'CO' THEN M.Number_of_reservations
                 ELSE 0
             END AS Arrivals
     FROM
       ( SELECT A.Arrival_dt,
                Datediff(DAY, A.Arrival_dt, A.Departure_dt) AS Los,
                B.Mkt_seg_id,
                B.Accom_type_id,
                CASE
                    WHEN B.Individual_status NOT IN ('XX',
                                                     'NS') THEN 'CO'
                    ELSE B.Individual_status
                END AS Individual_status,
                Count(*) AS Number_of_reservations,
                Sum(A.Total_revenue) AS Total_revenue,
                Sum(A.Room_revenue) AS Room_revenue
        FROM
          (SELECT Reservation_identifier ,
                  Min(Arrival_dt) AS Arrival_dt ,
                  Max(Departure_dt) AS Departure_dt ,
                  Sum(Room_revenue) AS Room_revenue ,
                  Sum(Total_revenue) AS Total_revenue
           FROM #reservation_night
           GROUP BY Reservation_identifier) AS A
        INNER JOIN
          ( SELECT DISTINCT Reservation_identifier,
                            Arrival_dt,
                            Accom_type_id,
                            Mkt_seg_id,
                            individual_status
           FROM #reservation_night ) AS B ON A.Reservation_identifier = B.Reservation_identifier
        AND A.Arrival_dt = B.Arrival_dt
        GROUP BY A.Arrival_dt,
                 Datediff(DAY, A.Arrival_dt, A.Departure_dt),
                 B.Mkt_seg_id,
                 B.Accom_type_id,
                 CASE
                     WHEN B.Individual_status NOT IN ('XX',
                                                      'NS') THEN 'CO'
                     ELSE B.Individual_status
                 END ) M) AS Sorted
GROUP BY Mkt_seg_id,
         Arrival_dt,
         Accom_type_id,
         Los
ORDER BY Mkt_seg_id,
         Arrival_dt,
         Accom_type_id,
         Los

end
GO


