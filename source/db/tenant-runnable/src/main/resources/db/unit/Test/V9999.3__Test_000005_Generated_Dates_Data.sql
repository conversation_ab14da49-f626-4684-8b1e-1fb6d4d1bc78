IF (DB_NAME() = '000005')
BEGIN
---------Property 5-----------------

------------------------------------------------------------------------
DECLARE @Property_ID int
set @Property_ID = 5

Declare @total_accom_Capacity numeric
set @total_accom_Capacity = 448

declare @total_accom_type numeric
set @total_accom_type = (select COUNT(accom_type.accom_type_Id) from accom_type  where accom_type.property_id=@property_ID)

declare @total_mkt_seg numeric
set @total_mkt_seg = (select COUNT(mkt_seg_Id) from mkt_seg  where mkt_seg.property_id=@property_ID) 

------------------------Create dates from SP-------------------------------------------
-- activity data for current year window
declare @enddate date
set @enddate = GETDATE()+120

declare @startdate date
set @startdate = GETDATE()-61

DELETE FROM date_list;

exec  generatedates @startDate=@startdate, @endDate=@enddate;

-- activity data for window from last year
-- used for rooms sold last year and last year to date
declare @lastyear_enddate date
set @lastyear_enddate = GETDATE()-305
declare @lastyear_startdate date
set @lastyear_startdate = GETDATE()-426
exec  generatedates @startDate=@lastyear_startdate, @endDate=@lastyear_enddate;

------------------------Create dates from SP-------------------------------------------


-------------------------------------------------------Insert Mkt_Accom_Activity--------------------------------

insert into Mkt_Accom_Activity(Property_ID, Occupancy_DT, SnapShot_DTTM, 
Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue,
 Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM)
select @Property_ID,today,today,Mkt_Seg_ID, Accom_type_ID,((DAY(today)+accom_type_ID+Mkt_seg_ID)/3),((DAY(today)+accom_type_ID+Mkt_seg_ID)/4),
((DAY(today)+accom_type_ID+Mkt_seg_ID)/5),((DAY(today)+accom_type_ID+Mkt_seg_ID)/40),((DAY(today)+accom_type_ID+Mkt_seg_ID)/43),(((DAY(today)+accom_type_ID+Mkt_seg_ID)/5)*90),
(((DAY(today)+accom_type_ID+Mkt_seg_ID)/5)*5), (((DAY(today)+accom_type_ID+Mkt_seg_ID)/5)*95),'1', @enddate
from Date_list cross join accom_type cross join mkt_seg
where accom_type.Property_ID =@property_id
and 
Mkt_Seg.Property_ID =@Property_ID 
and mkt_seg_id < 20 and mkt_seg_id !=7

INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 50), CONVERT(smalldatetime, GETDATE() + 50), 7, 4, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 50), CONVERT(smalldatetime, GETDATE() + 50), 7, 5, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 50), CONVERT(smalldatetime, GETDATE() + 50), 7, 6, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 50), CONVERT(smalldatetime, GETDATE() + 50), 7, 7, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 51), CONVERT(smalldatetime, GETDATE() + 51), 7, 4, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 51), CONVERT(smalldatetime, GETDATE() + 51), 7, 5, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 51), CONVERT(smalldatetime, GETDATE() + 51), 7, 6, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());
INSERT INTO Mkt_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Mkt_Seg_ID, Accom_Type_ID, Rooms_Sold, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM, CreateDate)
    VALUES (5, CONVERT(date, GETDATE() + 51), CONVERT(smalldatetime, GETDATE() + 51), 7, 7, CAST(20 AS NUMERIC(18, 0)), CAST(15 AS NUMERIC(18, 0)), CAST(12 AS NUMERIC(18, 0)), CAST(3 AS NUMERIC(18, 0)), CAST(1 AS NUMERIC(18, 0)), CAST(356.15 AS NUMERIC(19, 5)), CAST(52.25 AS NUMERIC(19, 5)), CAST(408.40 AS NUMERIC(19, 5)), 1, GETDATE(), GETDATE());

-------------------------------------------------------Insert Mkt_Accom_Activity--------------------------------


-------------------------------------------------------Insert Accom_Activity--------------------------------


insert into Accom_activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Accom_Type_ID, 
Accom_Capacity, Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Arrivals, Departures,
 Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID,
 Last_Updated_DTTM)
select @Property_ID,today, today,accom_type_ID,2,1,(DAY(today)/15),(DAY(today)/17),1,2,0,0,90.00,5.00,95.00,1,'1/1/1900'
from date_list cross join accom_type
where accom_type.Property_ID =@property_id

-------------------------------------------------------Insert Accom_Activity--------------------------------


-------------------------------------------------------update Accom_activity------------------------------
update Accom_Activity
set Accom_Capacity = k
from (select (sum(rooms_sold)+1)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set Rooms_Sold = k
from (select (sum(rooms_sold))as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT


update Accom_Activity
set Arrivals = (k -((Rooms_Not_Avail_Maint)+Rooms_Not_Avail_Other))
from (select (sum(rooms_sold)-1)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT


update Accom_Activity
set Departures = k
from (select (sum(rooms_sold)-2)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set Departures =0
where Rooms_Sold =0

update Accom_Activity
set arrivals =0
where Rooms_Sold =0

update Accom_Activity
set Room_Revenue = k 
from (select (sum(rooms_sold)*90.00)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set Food_Revenue = k 
from (select (sum(rooms_sold)*5.00)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set Total_Revenue = k 
from (select (sum(rooms_sold)*95.00)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set Cancellations = k 
from (select sum(Cancellations)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT

update Accom_Activity
set No_Shows = k 
from (select sum(No_Shows)as k,Accom_Type_id, Occupancy_DT from Mkt_Accom_Activity
group by occupancy_dt, accom_type_id)d, Accom_Activity l
where d.Accom_Type_ID = l.accom_type_ID
and d.Occupancy_DT = l.Occupancy_DT


-------------------------------------------------------Update Accom_Activity-------------------------------


------------------------------------------------------Insert Total_Activity--------------------------------

insert into Total_activity (Property_ID, Occupancy_DT, SnapShot_DTTM, Total_Accom_Capacity, 
Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Arrivals, Departures, Cancellations,
No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, Last_Updated_DTTM)
select @Property_ID, today, today,@total_accom_Capacity,(select SUM(rooms_sold)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT),(select SUM(Rooms_Not_Avail_Maint)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select SUM(Rooms_Not_Avail_Other)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select SUM(Arrivals)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select SUM(Departures)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select SUM(Cancellations)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select SUM(No_Shows)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select (SUM(rooms_sold)*90.00)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID  group by Occupancy_DT ),(select (SUM(rooms_sold)*5.00)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),(select (SUM(rooms_sold)*95.00)from accom_activity
where Occupancy_DT =today and property_ID = @property_ID group by Occupancy_DT ),1,'1/1/1900'
from Date_list

------------------------------------------------------Insert Total_Activity--------------------------------


-------------------------------------------------------Insert PACE_Total_Activity--------------------------------

Insert into PACE_Total_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, 
Business_Day_End_DT, Total_Accom_Capacity, Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, 
Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, 
Month_ID, Year_ID, Last_Updated_DTTM)
select @Property_ID, Occupancy_DT, today,today, Total_Accom_Capacity, Rooms_Sold, 
Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, 
Food_Revenue, Total_Revenue, File_Metadata_ID, 1,1,Last_Updated_DTTM
from dbo.Total_Activity cross join date_list
where total_activity.property_ID =@property_ID
and occupancy_dt >= today


-------------------------------------------------------Insert PACE_Total_Activity--------------------------------


-------------------------------------------------------Insert PACE_Accom_Activity--------------------------------


insert into PACE_Accom_Activity (Property_ID, Occupancy_DT, SnapShot_DTTM, 
Business_Day_End_DT, Accom_Type_ID, Accom_Capacity, Rooms_Sold, Rooms_Not_Avail_Maint, 
Rooms_Not_Avail_Other, Arrivals, Departures, Cancellations, No_Shows, Room_Revenue, 
Food_Revenue, Total_Revenue, File_Metadata_ID, Month_ID, Year_ID, Last_Updated_DTTM)
select @Property_ID, Occupancy_DT, today,today, Accom_Type_ID, Accom_Capacity, 
Rooms_Sold, Rooms_Not_Avail_Maint, Rooms_Not_Avail_Other, Arrivals, Departures, Cancellations, 
No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID,1,1,Last_Updated_DTTM
from dbo.Accom_Activity cross join date_list
where accom_activity.property_ID =@property_ID
and occupancy_dt >= today

-------------------------------------------------------Insert PACE_Accom_Activity--------------------------------


-------------------------------------------------------Insert Pace_Mkt_Activity---------------------------


insert into Pace_Mkt_Activity(Property_ID, Occupancy_DT, SnapShot_DTTM,
 Business_Day_End_DT, Mkt_Seg_ID, Rooms_Sold, Arrivals, Departures, Cancellations,
  No_Shows, Room_Revenue, Food_Revenue, Total_Revenue, File_Metadata_ID, 
  Month_ID, Year_ID)
select 5,Occupancy_DT, today, today, Mkt_Seg_ID,sum(Rooms_Sold),SUM(Arrivals),
SUM(Departures),SUM(Cancellations),SUM(No_Shows),SUM(Room_Revenue),SUM(Food_Revenue),
SUM(Total_Revenue),1,1,1
from dbo.Mkt_Accom_Activity cross join Date_List
where dbo.Mkt_Accom_Activity.Property_ID =5
and occupancy_dt >= today
group by Occupancy_DT,today,today,Mkt_seg_ID;

-------------------------------------------------------Insert Pace_Mkt_Activity---------------------------

  
---------Property 5-----------------

------------------------------------------------------------------------
set @Property_ID = 5

------------------------Create dates from SP-------------------------------------------
delete from date_list

set @enddate = GETDATE()+120

set @startdate = GETDATE()-61

exec  generatedates @startDate=@startdate, @endDate=@enddate;

------------------------Create dates from SP-------------------------------------------

---------Generate decisions ---------------
insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)

select @Property_ID,
cast(date_list.today as varchar(11))+ ' 05:00:00' ,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 05:00:00',
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+ ' 05:00:00',
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+ ' 05:00:00',
Decision_Type_ID,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 10:20:00',
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 10:30:00' as End_DTTM,
2
from date_list, Decision_Type where Decision_Type_ID =1
and date_List.today < GETDATE()

union

select @Property_ID,
cast(date_list.today as varchar(11))+' '+ cast(time_DTTM as varchar(8))  ,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
Decision_Type_ID,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+' '+ cast(DATEADD(hour,5,time_DTTM) as varchar(8)),
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' '+cast(DATEADD(hour,6,time_DTTM) as varchar(8)) as End_DTTM,
2
from date_list, Decision_Type,time where Decision_Type_ID =2
and date_List.today < GETDATE()

union

select @Property_ID,
cast(date_list.today as varchar(11))+ ' 05:00:00' ,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 05:00:00',
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+ ' 05:00:00',
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+ ' 05:00:00',
Decision_Type_ID,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 11:20:00',
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' 11:30:00' as End_DTTM,
2
from date_list, Decision_Type where Decision_Type_ID in (3,4)
and date_List.today < GETDATE()
and
datepart(day,today)%4=0

union

select @Property_ID,
cast(date_list.today as varchar(11))+' '+ cast(time_DTTM as varchar(8))  ,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
cast(DATEADD ( day , -1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
cast(DATEADD ( day, -1, date_list.today )  as varchar(11))+' '+ cast(time_DTTM as varchar(8)),
Decision_Type_ID,
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+' '+ cast(DATEADD(minute,20,DATEADD(hour,5,time_DTTM)) as varchar(8)),
cast(DATEADD ( day , 1, date_list.today )  as varchar(11))+ ' '+cast(DATEADD(minute,20,DATEADD(hour,6,time_DTTM)) as varchar(8)) as End_DTTM,
2
from date_list, Decision_Type,time where Decision_Type_ID in (3,4)
and date_List.today < GETDATE()
and
datepart(day,today)%8=0


order by End_DTTM


---------------------------update unqualified Demand FCST Price------------------------------------------------------

Declare @2maxDecision_ID bigint
set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID) 


insert into Unqualified_Demand_FCST_Price (Decision_ID,Property_ID,Arrival_DT,Accom_Class_ID,Rate_Unqualified_ID,LOS,Remaining_Demand)
select @2maxDecision_ID,@Property_ID,today,Accom_Class_ID,Rate_Unqualified_ID,los_values.los,Rate_Unqualified_ID*Accom_Class_ID+day(today)+100/ABS(los_values.los)
from date_list cross join Rate_Unqualified cross join Accom_Class cross join los_values
where
Rate_Unqualified.Property_ID=@Property_ID
and 
Rate_Unqualified.System_Default =0
and
Accom_Class.Property_ID=@Property_ID
and
Accom_Class.Accom_Class_Name<>'Unassigned'
and
los=-1

--------------------------------------Rate_Unqualified_Defaults---------------
insert into Rate_Unqualified_Defaults(Rate_Unqualified_Accom_Class_ID, 
User_Override_Only, Sunday_Min_Los, Monday_Min_Los, Tuesday_Min_Los, 
Wednesday_Min_Los, Thursday_Min_Los, Friday_Min_Los, Saturday_Min_Los,
 Sunday_Available, Monday_Available, Tuesday_Available, Wednesday_Available,
  Thursday_Available, Friday_Available, Saturday_Available, Sunday_Max_Los,
   Monday_Max_Los, Tuesday_Max_Los, Wednesday_Max_Los, Thursday_max_Los,
    Friday_Max_Los, Saturday_Max_Los)
Select Rate_Unqualified_Accom_Class_ID,0,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1 from dbo.Rate_Unqualified_Accom_Class  
where accom_class_ID in (2,3,4)

--------------------------------------Rate_Unqualified_Defaults---------------


--------------------------------------------Declare Var----------------------------------------
DECLARE  @MaxUnq int
DECLARE  @MinUnq int

select * from Rate_Unqualified where Property_ID=@Property_ID and Rate_Unqualified.System_Default =0;

select @MaxUnq = MAX(Rate_Unqualified_ID),@MinUnq = MIN(Rate_Unqualified_ID) from Rate_Unqualified where Property_ID=@Property_ID and Rate_Unqualified.System_Default =0

------------------------------------------------Insert Decision_Bar_output-------------

insert into Decision_bar_output (Decision_Id,Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,LOS)
select @2maxDecision_ID,@Property_ID,Accom_Class_ID,today, round(cast('0.'+cast((Accom_Class_ID*(abs(los_values.los))*DAY(today)) as nchar ) as float) * ((@MaxUnq)-@MinUnq)+@MinUnq,0),los_values.los
from date_list cross join Accom_Class cross join los_values
where
Accom_Class.Property_ID=@Property_ID
and
Accom_Class.Accom_Class_Name<>'Unassigned'
and
los=-1
---------------------------------------------Insert Decision_Bar_output --------------

----------------------------------------------Insert LRV-------------------------------
insert into dbo.Decision_LRV (Decision_ID, Property_ID, Accom_Class_ID,
 Occupancy_DT, LRV)
 select @2maxDecision_ID,@Property_ID,Accom_Class_ID,today,(SUM(accom_class_ID* property_ID+DAY(today)+19)) 
 from date_list cross join Accom_Class
 where 
Accom_Class.Property_ID=@Property_ID 
 and
Accom_Class_Name<>'Unassigned'
group by today, accom_class_ID

----------------------------------------------Insert LRV-------------------------------


-----------------------------------------------Update Decision_Bar_output------
declare @firstdayofcurrentmonth date
set @firstdayofcurrentmonth = GETDATE()

set @firstdayofcurrentmonth = CAST(CAST(YEAR(@firstdayofcurrentmonth) AS VARCHAR(4)) + '/' + 
                CAST(MONTH(@firstdayofcurrentmonth) AS VARCHAR(2)) + '/01' AS DATETIME)


update Decision_Bar_Output set Override='User' where Arrival_DT between dateadd(day,5,@firstdayofcurrentmonth) and dateadd(day,6,@firstdayofcurrentmonth) and Property_ID=@Property_ID

update Decision_Bar_Output set Override='Floor',Floor_Rate_Unqualified_ID=Rate_Unqualified_ID where Arrival_DT 
between dateadd(day,11,@firstdayofcurrentmonth) and dateadd(day,13,@firstdayofcurrentmonth) and Property_ID=@Property_ID


update Decision_Bar_Output set Override='Floor',Floor_Rate_Unqualified_ID=@MinUnq+2 where Arrival_DT 
between dateadd(day,21,@firstdayofcurrentmonth) and dateadd(day,21,@firstdayofcurrentmonth) and Property_ID=@Property_ID 


update Decision_Bar_Output set Override='User' 
where Arrival_DT between dateadd(day,28,@firstdayofcurrentmonth) and dateadd(day,29,@firstdayofcurrentmonth) and Property_ID=@Property_ID



------------------------------------------------Update Decision_Bar_output -------------


------------------------------------------------Declare user------------------------------

Declare @user_ID int
set @user_ID = (SELECT [User_ID]
  FROM [dbo].[Users] where Screen_Name = '<EMAIL>')

------------------------------------------------Declare user------------------------------



------------------------------------------------Insert Notes-------------

Insert into dbo.Notes (Property_ID, Module, Arrival_DT, Notes, User_ID)  

Select Property_ID,'BAR',Arrival_DT, 'Decided this day needed a floor',@user_ID
from dbo.Decision_Bar_Output where Override ='Floor' and Property_ID=@property_ID
union
Select Property_ID,'BAR',Arrival_DT, 'Decided this rate needed to be overwritten ',@user_ID
from dbo.Decision_Bar_Output where Override ='User' and Property_ID=@property_ID

------------------------------------------------Insert Notes-------------

------------------------------------------------Insert Decision_Bar_Output_OVR---------------

insert into dbo.Decision_Bar_Output_OVR(Decision_ID, Property_ID, Accom_Class_ID, 
Arrival_DT, User_ID, Old_Rate_Unqualified_ID, LOS, Old_Override, Old_Floor_Rate_Unqualified_ID, 
New_Override, New_Rate_Unqualified_ID, New_Floor_Rate_Unqualified_ID)

Select @2maxDecision_ID -1,Property_ID, Accom_Class_ID, Arrival_DT,@user_ID, 
Rate_Unqualified_ID -1 , LOS,'None',NULL,'FLoor',Rate_Unqualified_ID,Floor_Rate_Unqualified_ID
from dbo.Decision_Bar_Output where Override ='Floor' and Property_ID =@Property_ID
union
Select @2maxDecision_ID -1,Property_ID, Accom_Class_ID, Arrival_DT,@user_ID, 
Rate_Unqualified_ID -1, LOS,'None',NULL,'User',Rate_Unqualified_ID,Floor_Rate_Unqualified_ID
from dbo.Decision_Bar_Output where Override ='User' and Property_ID =@Property_ID


------------------------------------------------Insert Decision_Bar_Output_OVR---------------


------------------------------------------------Declare Webrate_source_Property--------------

DECLARE @Webrate_Source_Property_ID int
set @Webrate_Source_Property_ID = (Select Webrate_Source_Property_ID from Webrate_Source_Property 
where Property_ID=@property_ID)

------------------------------------------------Declare Webrate_source_Property--------------



------------------------------------------------Insert Pace_Bar_Output---------------------------

insert into PACE_Bar_Output(Decision_ID,Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,Derived_Unqualified_Value,LOS,Override,Floor_Rate_Unqualified_ID,Month_ID,Year_ID,CreateDate)
select Decision.Decision_ID,Decision_Bar_Output.Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,((accom_class_ID+rate_unqualified_id)+80),LOS,Override,Floor_Rate_Unqualified_ID,1,1,Decision.End_DTTM
from
Decision_Bar_Output inner join Decision on Decision_Bar_Output.Property_ID=Decision.Property_ID
where Decision.Property_ID=@Property_ID
and Decision_Type_ID=1
and Arrival_DT>Business_DT  
and DATEDIFF ( DAY , Business_DT, Arrival_DT )<366

union
select Decision.Decision_ID,Decision_Bar_Output.Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,((accom_class_ID+rate_unqualified_id)+80),LOS,Override,Floor_Rate_Unqualified_ID,1,1,Decision.End_DTTM
from
Decision_Bar_Output inner join Decision on Decision_Bar_Output.Property_ID=Decision.Property_ID
where Decision.Property_ID=@Property_ID
and Decision_Type_ID=2
and Decision_Bar_Output.decision_id%DATEPART(day,Arrival_DT)=0
and Arrival_DT>Business_DT
and DATEDIFF ( DAY , Business_DT, Arrival_DT )<366

union
select Decision.Decision_ID,Decision_Bar_Output.Property_ID,Accom_Class_ID,Arrival_DT,Rate_Unqualified_ID,((accom_class_ID+rate_unqualified_id)+80),LOS,Override,Floor_Rate_Unqualified_ID,1,1,Decision.End_DTTM
from
Decision_Bar_Output inner join Decision on Decision_Bar_Output.Property_ID=Decision.Property_ID
where Decision.Property_ID=@Property_ID
and Decision_Type_ID in (3,4 )
and DATEPART(day,Arrival_DT)%10=0
and Arrival_DT>Business_DT
and DATEDIFF ( DAY , Business_DT, Arrival_DT )<366

order by End_DTTM asc

------------------------------------------------Insert Pace_Bar_Output Property 5---------------------------

------------------------------------------------Update Pace_Bar_OutPut ---------------------------

Update PACE_Bar_Output
set Rate_Unqualified_ID = (Rate_Unqualified_id+1)
where Decision_ID in (Select decision_ID from decision where decision_id between(@2maxDecision_ID-50) and(@2maxDecision_ID-30))
and Arrival_DT >DATEADD (day,10,@firstdayofcurrentmonth)
and Property_ID =@property_ID
and Rate_Unqualified_id<>12
and Rate_Unqualified_ID <>4


Update PACE_Bar_Output
set Rate_Unqualified_ID = (Rate_Unqualified_id-2)
where Decision_ID in (Select decision_ID from decision where decision_id between(@2maxDecision_ID-30) and(@2maxDecision_ID-20))
and Arrival_DT >DATEADD (day,10,@firstdayofcurrentmonth)
and Property_ID =@property_ID
and Rate_Unqualified_id<>12
and Rate_Unqualified_ID <>6
and Rate_Unqualified_ID <>5
and Rate_Unqualified_ID <>4

Update PACE_Bar_Output
set Rate_Unqualified_ID = (Rate_Unqualified_id+1)
where Decision_ID in (Select decision_ID from decision where decision_id between(@2maxDecision_ID-20) and(@2maxDecision_ID-5))
and Arrival_DT >DATEADD (day,10,@firstdayofcurrentmonth)
and Property_ID =@property_ID
and Rate_Unqualified_id<>12
and Rate_Unqualified_ID <>4


------------------------------------------------Update Pace_Bar_OutPut ---------------------------




------------------------------------------------Insert Occupancy Demand FCST property 2------------------------

insert into dbo.Occupancy_Demand_FCST (Decision_ID, Property_ID, Forecast_Group_ID, Accom_Class_ID, 
Occupancy_DT, Peak_Demand, Remaining_Demand,Deviation,User_Remaining_Demand) 
Select @2maxDecision_ID, @Property_ID,Forecast_Group_ID,Accom_Class_ID,Today,((Accom_class_ID/2)+2),
((Accom_class_ID /3)),(DAY(today)/50.03),((Accom_class_ID /3))
from Date_list cross join forecast_group cross join Accom_class 
where 
Forecast_group.Property_ID =@Property_ID
and Forecast_Group.Forecast_Type_ID in (1,2,4,5,6)
and 
Accom_Class.Property_ID=@Property_ID
and 
Accom_Class.Accom_Class_Name<>'Unassigned'
and [Forecast_Group_ID] < 20

------------------------------------------------Insert Occupancy Demand FCST property 5------------------------



------------------------------------------------Insert Occupancy FCST property--------------------

insert into dbo.Occupancy_FCST (Decision_ID, Property_ID, MKT_SEG_ID, Accom_Type_ID, 
Occupancy_DT, Occupancy_NBR, Revenue, Month_ID, Year_ID)
Select @2maxDecision_ID, @property_ID,MKT_SEG_ID,Accom_type_ID, occupancy_dt,(rooms_sold+2),
(rooms_sold +2 *90.00),'4','5'
from Mkt_Accom_Activity
where dbo.Mkt_Accom_Activity.property_ID = @property_ID


------------------------------------------------Insert Occupancy FCST property--------------------

------------------------------------------------Arrival_Demand_FCST-------------------------

insert into dbo.Arrival_Demand_FCST(Decision_ID, Property_ID, 
Forecast_Group_ID, Accom_Class_ID, Arrival_DT, LOS, Peak_Demand, Remaining_Demand, 
Deviation,User_Remaining_Demand)
Select @2maxDecision_ID, @property_ID, Forecast_group_ID,accom_class_ID,today,los,(forecast_group_id*accom_class_ID+ DAY(today)) 
,(forecast_group_id*accom_class_ID),((forecast_group_id *accom_class_ID+ DAY(today))-(forecast_group_id*accom_class_ID)),(forecast_group_id*accom_class_ID)
from date_list cross join forecast_group cross join accom_class cross join los_values
where forecast_group.property_ID =@Property_ID
and accom_class.property_ID = @Property_ID
and Accom_Class.Accom_Class_Name<>'Unassigned'
and Forecast_Group_ID < 20
and 
los=1
and Forecast_Group.Forecast_Type_ID in (3,5,6)

insert into dbo.Arrival_Demand_FCST(Decision_ID, Property_ID, 
Forecast_Group_ID, Accom_Class_ID, Arrival_DT, LOS, Peak_Demand, Remaining_Demand, 
Deviation,User_Remaining_Demand)
Select @2maxDecision_ID, @property_ID, Forecast_group_ID,accom_class_ID,today,los,(forecast_group_id*accom_class_ID+ DAY(today)) 
,(forecast_group_id*accom_class_ID),((forecast_group_id *accom_class_ID+ DAY(today))-(forecast_group_id*accom_class_ID)),(forecast_group_id*accom_class_ID)
from date_list cross join forecast_group cross join accom_class cross join los_values
where forecast_group.property_ID =@Property_ID
and accom_class.property_ID = @Property_ID
and Accom_Class.Accom_Class_Name<>'Unassigned'
and 
los<>-1
and Forecast_Group.Forecast_Type_ID in (1,2,4)


------------------------------------------------Arrival_Demand_FCST-------------------------


------------------------------------------------Decision_Ovrbk_Property---------------------

insert into dbo.Decision_Ovrbk_Property (Decision_ID, 
Property_ID, Occupancy_DT, Overbooking_Decision, Expected_Walks) 
select @2maxDecision_ID, @property_ID,today,DAY(today)-@property_ID,
DAY(today)-@property_ID/DAY(today)*.07
from date_list

update dbo.Decision_Ovrbk_Property
set Overbooking_Decision =0
where Overbooking_Decision <0
and property_ID =@property_ID
------------------------------------------------Decision_Ovrbk_Property---------------------


------------------------------------------------Decision_Ovrbk_Accom---------------------

insert into dbo.Decision_Ovrbk_Accom(Decision_ID, Property_ID, 
Occupancy_DT, Accom_Type_ID, Overbooking_Decision)
select @2maxDecision_ID, @property_ID, today, Accom_type_ID,DAY(today)-@property_ID/accom_type_id
from date_list cross join Accom_Type
where accom_type.Property_ID =@property_ID

update Decision_Ovrbk_Accom
set Overbooking_Decision =0
where Overbooking_Decision <0
and property_ID =@property_ID



------------------------------------------------Decision_Ovrbk_Accom---------------------


------------------------------------------------Pace_Decision_Ovrbk_Property---------------------

insert into Pace_Ovrbk_Property (Decision_ID, 
Property_ID, Occupancy_DT, Overbooking_Decision, Expected_Walks) 
select Decision_ID, @property_ID,today,DAY(today)-@property_ID,
DAY(today)-@property_ID/DAY(today)*.07
from Decision a cross join date_list 
where a.Property_ID = @Property_ID
and  a.Decision_Type_ID =1
and a.Decision_ID <> @2maxDecision_ID
and Property_ID =@Property_ID

Update dbo.PACE_Ovrbk_Property
set Overbooking_Decision = 4
where Overbooking_Decision < 1
and Property_ID = @property_ID
------------------------------------------------Pace_Decision_Ovrbk_Property---------------------


------------------------------------------------Pace_Decision_Ovrbk_Accom---------------------

insert into Pace_Ovrbk_Accom(Decision_ID, Property_ID, 
Occupancy_DT, Accom_Type_ID, Overbooking_Decision)
select a.Decision_ID, @property_ID, today, Accom_type_ID,DAY(today)-@property_ID/accom_type_id
from decision a cross join date_list cross join Accom_Type
where accom_type.Property_ID =@property_ID
and  a.Decision_Type_ID =1
and a.Decision_ID <> @2maxDecision_ID
and a.Property_ID =@Property_ID

Update dbo.PACE_Ovrbk_Accom
set Overbooking_Decision = 4
where Overbooking_Decision < 1
and Property_ID = @property_ID

------------------------------------------------Pace_Decision_Ovrbk_Accom---------------------


------------------------------------------------Wash_FCST------------------------------------

insert into dbo.Wash_FCST(Decision_ID, Property_ID, Forecast_Group_ID, 
Accom_Class_ID, Occupancy_DT, System_Wash, User_Wash)
Select @2maxDecision_ID, @property_ID,Forecast_Group_ID,Accom_Class_ID,today,(accom_class_ID -Forecast_group_ID+DAY(today)/11),
(accom_class_ID -Forecast_group_ID+DAY(today)/11) from date_list cross join forecast_group cross join accom_class
where forecast_group.Property_ID = @Property_ID and 
accom_class.property_ID =@Property_ID
and Accom_Class.Accom_Class_Name<>'Unassigned'
and Forecast_Group_ID < 20

update Wash_FCST
set System_Wash = 1
where System_Wash <1
and Property_ID =@Property_ID

update Wash_FCST
set User_Wash = 1
where User_Wash <1
and Property_ID =@Property_ID


------------------------------------------------Wash_FCST------------------------------------


------------------------------------------------Wash_Forecast_Group_FCST---------------------------

insert into dbo.Wash_Forecast_Group_FCST(Decision_ID, Property_ID,
 Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash)
Select Decision_ID, Property_ID, Forecast_Group_ID, 
Occupancy_DT, Sum(System_Wash),SUM(User_Wash)From Wash_FCST
where property_ID=@property_ID
group by decision_Id, Property_ID,Forecast_Group_Id, Occupancy_dt


------------------------------------------------Wash_Forecast_Group_FCST---------------------------


------------------------------------------------Wash_Property_FCST---------------------------

Insert into dbo.Wash_Property_FCST(Decision_ID, Property_ID, 
Occupancy_DT, System_Wash, User_Wash)
Select Decision_ID, Property_ID,Occupancy_DT, Sum(System_Wash), Sum(User_Wash) 
from Wash_Forecast_Group_FCST
where property_ID=@property_ID
group by  Decision_ID, Property_ID,occupancy_dt


----------------------------------------------Wash_Property_FCST---------------------------




------------------------------------------------Arrival_Demand_FCST_OVR-----------------------

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,6,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID) 

insert into dbo.Arrival_Demand_FCST_OVR(Decision_ID, Property_ID,
 Forecast_Group_ID, Accom_Class_ID, Arrival_DT, LOS, Remaining_Demand,
  User_Demand_Override_Value,Rate_Unqualified_ID, Created_By_User_ID)
Select  @2maxDecision_ID,@Property_ID,b.Forecast_Group_ID,b.Accom_Class_ID,
b.Arrival_DT, b.LOS,b.Remaining_Demand,(DAY(b.Arrival_DT)+b.Remaining_Demand),4,16547 from dbo.Arrival_Demand_FCST b
where b.Arrival_DT = cast(GETDATE()+30 as DATE)
and b.forecast_group_ID = 3 and Accom_Class_ID =3
and b.Property_ID =@Property_ID


update Arrival_Demand_FCST set User_Remaining_Demand= (DAY(Arrival_DT)+Remaining_Demand) 
where Arrival_DT = cast(GETDATE()+30 as DATE)
and forecast_group_ID = 3 and Accom_Class_ID =3
and Property_ID =@Property_ID

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,6,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID) 

insert into dbo.Arrival_Demand_FCST_OVR(Decision_ID, Property_ID,
 Forecast_Group_ID, Accom_Class_ID, Arrival_DT, LOS, Remaining_Demand,
  User_Demand_Override_Value,Rate_Unqualified_ID)
Select  @2maxDecision_ID,@Property_ID,b.Forecast_Group_ID,b.Accom_Class_ID,
b.Arrival_DT, b.LOS,b.Remaining_Demand,(DAY(b.Arrival_DT)+b.Remaining_Demand),5 from dbo.Arrival_Demand_FCST b
where b.Arrival_DT = cast(GETDATE()+50 as DATE)
and b.forecast_group_ID = 3 and Accom_Class_ID =4
and b.Property_ID =@Property_ID

update Arrival_Demand_FCST set User_Remaining_Demand= (DAY(Arrival_DT)+Remaining_Demand) 
where Arrival_DT = cast(GETDATE()+50 as DATE)
and forecast_group_ID = 3 and Accom_Class_ID =4
and Property_ID =@Property_ID

------------------------------------------------Arrival_Demand_FCST_OVR-----------------------

---------------------------------------Occupancy_Demand_FCST_OVR-------------------------------------

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,6,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID) 

insert into dbo.Occupancy_Demand_FCST_OVR (Decision_ID, Property_ID, 
Forecast_Group_ID, Accom_Class_ID, Occupancy_DT, Remaining_Demand, User_Demand_Override_Value,
 Rate_Unqualified_ID,Rooms_Sold)
select @2maxDecision_ID,@Property_ID,b.Forecast_Group_ID, b.Accom_Class_ID,b.Occupancy_DT, 
b.Remaining_Demand,(DAY(b.Occupancy_dt)+b.Remaining_Demand),5,20 from Occupancy_Demand_FCST b
where b.Occupancy_DT = cast(GETDATE()+60 as DATE)
and b.forecast_group_ID = 3 and Accom_Class_ID =3
and b.Property_ID =@Property_ID

---------------------------------------Occupancy_Demand_FCST_OVR-------------------------------------


----------------------------------------Decision_Ovrbk_Property_OVR----------------------------

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,7,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into Decision_Ovrbk_Property_OVR (Decision_ID, Property_ID, 
Occupancy_DT, Overbooking_Decision, OVR_Overbooking_Type_ID, Overbooking_OVR, Created_by_User_ID)
select @2maxDecision_ID, @Property_ID,Occupancy_DT,Overbooking_Decision,2,Overbooking_Decision+1,
@user_ID from dbo.Decision_Ovrbk_Property
where Occupancy_DT = cast(GETDATE()+20 as DATE)
and Property_ID =@Property_ID

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,7,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into Decision_Ovrbk_Property_OVR (Decision_ID, Property_ID, 
Occupancy_DT, Overbooking_Decision, OVR_Overbooking_Type_ID, Overbooking_OVR, Created_by_User_ID)
select @2maxDecision_ID, @Property_ID,Occupancy_DT,Overbooking_Decision,2,Overbooking_Decision+1,
@user_ID from dbo.Decision_Ovrbk_Property
where Occupancy_DT = cast(GETDATE()+38 as DATE)
and Property_ID =@Property_ID

----------------------------------------Decision_Ovrbk_Property_OVR----------------------------


---------------------------------------Decision_Ovrbk_Accom_OVR--------------------------------

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,7,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into Decision_Ovrbk_Accom_OVR(Decision_ID, Property_ID,
Occupancy_DT, Accom_Type_ID, Overbooking_Decision, OVR_Overbooking_type_ID, Overbooking_OVR)
Select @2maxDecision_ID, @Property_ID,Occupancy_DT,Accom_Type_ID,Overbooking_Decision,2,Overbooking_Decision+1 from Decision_Ovrbk_Accom 
where accom_type_ID = 4
and Occupancy_DT = cast(GETDATE()+28 as DATE)
and Property_ID =@Property_ID

Insert into Decision_Ovrbk_Accom_OVR(Decision_ID, Property_ID,
Occupancy_DT, Accom_Type_ID, Overbooking_Decision, OVR_Overbooking_type_ID, Overbooking_OVR)
Select @2maxDecision_ID, @Property_ID,Occupancy_DT,Accom_Type_ID,Overbooking_Decision,2,Overbooking_Decision+1 from Decision_Ovrbk_Accom  
where accom_type_ID = 5
and Occupancy_DT = cast(GETDATE()+29 as DATE)
and Property_ID =@Property_ID

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,7,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into Decision_Ovrbk_Accom_OVR(Decision_ID, Property_ID,
Occupancy_DT, Accom_Type_ID, Overbooking_Decision, OVR_Overbooking_type_ID, Overbooking_OVR)
Select @2maxDecision_ID, @Property_ID,Occupancy_DT,Accom_Type_ID,Overbooking_Decision,2,Overbooking_Decision+1 from Decision_Ovrbk_Accom 
where accom_type_ID = 4
and Occupancy_DT = cast(GETDATE()+40 as DATE)
and Property_ID =@Property_ID

---------------------------------------Decision_Ovrbk_Accom_OVR--------------------------------


---------------------------------------Wash_Forecast_Group_FCST_OVR----------------------------

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,8,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into dbo.Wash_Forecast_Group_FCST_OVR(Decision_ID,
Property_ID, Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT, 
Status_ID)
Select @2maxDecision_ID, @Property_ID,Forecast_Group_ID,Occupancy_DT,System_Wash,system_Wash+1,
dateadd (day,-10,Occupancy_DT) ,1 from dbo.Wash_Forecast_Group_FCST
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+15 as DATE)
and Forecast_Group_ID >3 and Forecast_Group_ID < 20


Insert into dbo.Wash_Forecast_Group_FCST_OVR(Decision_ID,
Property_ID, Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT, 
Status_ID)
Select @2maxDecision_ID, @Property_ID,Forecast_Group_ID,Occupancy_DT,System_Wash,system_Wash+1,
dateadd (day,-10,Occupancy_DT),1 from dbo.Wash_Forecast_Group_FCST
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+17 as DATE)
and Forecast_Group_ID <3 and Forecast_Group_ID < 20


Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,8,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into dbo.Wash_Forecast_Group_FCST_OVR(Decision_ID,
Property_ID, Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT, 
Status_ID)
Select @2maxDecision_ID, @Property_ID,Forecast_Group_ID,Occupancy_DT,System_Wash,system_Wash+1,
dateadd (day,-10,Occupancy_DT),1 from dbo.Wash_Forecast_Group_FCST
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+20 as DATE)
and Forecast_Group_ID >2 and forecast_group_id < 20

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,8,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

Insert into dbo.Wash_Forecast_Group_FCST_OVR(Decision_ID,
Property_ID, Forecast_Group_ID, Occupancy_DT, System_Wash, User_Wash_OVR, Expiration_DT, 
Status_ID)
Select @2maxDecision_ID, @Property_ID,Forecast_Group_ID,Occupancy_DT,System_Wash,system_Wash+1,
Occupancy_DT,1 from dbo.Wash_Forecast_Group_FCST
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+30 as DATE)
and Forecast_Group_ID >1 and forecast_group_id < 20

update dbo.Wash_FCST
set User_Wash = User_Wash_OVR
from dbo.Wash_Forecast_Group_FCST_OVR
where  dbo.Wash_FCST.Property_ID = dbo.Wash_Forecast_Group_FCST_OVR.property_ID
and  dbo.Wash_FCST.forecast_Group_ID = dbo.Wash_Forecast_Group_FCST_OVR.Forecast_Group_ID
and  dbo.Wash_FCST.Occupancy_DT =dbo.Wash_Forecast_Group_FCST_OVR.Occupancy_DT

update dbo.Wash_Forecast_Group_FCST
set User_Wash = User_Wash_OVR
from dbo.Wash_Forecast_Group_FCST_OVR
where  dbo.Wash_Forecast_Group_FCST.Property_ID = dbo.Wash_Forecast_Group_FCST_OVR.property_ID
and  dbo.Wash_Forecast_Group_FCST.forecast_Group_ID = dbo.Wash_Forecast_Group_FCST_OVR.Forecast_Group_ID
and  dbo.Wash_Forecast_Group_FCST.Occupancy_DT =dbo.Wash_Forecast_Group_FCST_OVR.Occupancy_DT

update dbo.Wash_Property_FCST
set User_Wash = User_Wash_OVR
from dbo.Wash_Forecast_Group_FCST_OVR
where  dbo.Wash_Property_FCST.Property_ID = dbo.Wash_Forecast_Group_FCST_OVR.property_ID
and  dbo.Wash_Property_FCST.Occupancy_DT =dbo.Wash_Forecast_Group_FCST_OVR.Occupancy_DT

--------------------------------------------Wash_Forecast_Group_FCST_OVR-----------


--------------------------------------------Decision_COW_Value_OVR------------------


Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,9,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

insert into Decision_COW_Value_OVR(Decision_ID,Property_ID,Occupancy_DT,
 Accom_Type_ID,CostofWalk_Value,CostofWalk_Value_OVR, User_ID)
select @2maxDecision_ID, @Property_ID,occupancy_dt,accom_type_ID,'300','325',@user_ID
from Decision_Ovrbk_Accom
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+15 as DATE)
and Accom_Type_ID =5 

insert into Decision_COW_Value_OVR(Decision_ID,Property_ID,Occupancy_DT,
 Accom_Type_ID,CostofWalk_Value,CostofWalk_Value_OVR, User_ID)
select @2maxDecision_ID, @Property_ID,occupancy_dt,accom_type_ID,'300','345',@user_ID
from Decision_Ovrbk_Accom
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+20 as DATE)
and Accom_Type_ID =4 

Insert into Decision(Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,Decision_Type_ID,Start_DTTM,End_DTTM,Process_status_ID)
select @Property_ID,Business_DT,Caught_up_DTTM,Rate_Unqualified_DTTM,
WebRate_DTTM,9,Start_DTTM,End_DTTM,1
from Decision
where Decision_ID = @2maxDecision_ID
and Property_ID =@property_ID

set @2maxDecision_ID  = (Select MAX(decision_Id)from decision where property_ID =@Property_ID)

insert into Decision_COW_Value_OVR(Decision_ID,Property_ID,Occupancy_DT,
Accom_Type_ID,CostofWalk_Value,CostofWalk_Value_OVR, User_ID)
select @2maxDecision_ID, @Property_ID,occupancy_dt,accom_type_ID,'300','337',@user_ID
from Decision_Ovrbk_Accom
where Property_ID =@Property_ID
and Occupancy_DT = cast(GETDATE()+23 as DATE)
and Accom_Type_ID =5 ;

--------------------------------------------Decision_COW_Value_OVR------------------

INSERT INTO [Decision]([Property_ID],[Business_DT],[Caught_up_DTTM],[Rate_Unqualified_DTTM],[WebRate_DTTM]
           ,[Decision_Type_ID],[Start_DTTM],[End_DTTM],[Process_Status_ID],[CreateDate_DTTM])
     VALUES
           (5,'2010-01-01','2010-01-01 00:00:00','2010-01-01 00:00:00',
           '2010-01-01 00:00:00',1,'2010-01-01 00:00:00','2010-01-01 00:00:00'
           ,13 ,'2010-01-01 00:00:00')



INSERT INTO [Decision]([Property_ID],[Business_DT],[Caught_up_DTTM],[Rate_Unqualified_DTTM],[WebRate_DTTM]
           ,[Decision_Type_ID],[Start_DTTM],[End_DTTM],[Process_Status_ID],[CreateDate_DTTM])
     VALUES
           (5,'2010-01-02','2010-01-01 00:00:00','2010-01-01 00:00:00',
           '2010-01-01 00:00:00',1,'2010-01-01 00:00:00','2010-01-01 00:00:00'
           ,13 ,'2010-01-01 00:00:00');

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
           ,[Month_ID],[Year_ID],[CreateDate])
     select decision_ID,5,2,'2010-11-01',8,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;
     
INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-01',8,200,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-02',8,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-02',8,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-01',8,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-01',8,200,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-02',8,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-11-02',8,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
           ,[Month_ID],[Year_ID],[CreateDate])
     select decision_ID,5,2,'2010-12-01',12,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;
     
INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-01',8,200,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-02',8,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-02',8,100,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-01',8,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-01',12,200,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-02',12,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
      ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
      ,[Month_ID],[Year_ID],[CreateDate])
select decision_ID,5,2,'2010-12-02',12,100,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from Decision where property_ID = 5 and Business_DT = '2010-01-02' and Process_Status_ID = 13 and Decision_Type_ID = 1;

INSERT INTO [Decision]
           ([Property_ID]
           ,[Business_DT]
           ,[Caught_up_DTTM]
           ,[Rate_Unqualified_DTTM]
           ,[WebRate_DTTM]
           ,[Decision_Type_ID]
           ,[Start_DTTM]
           ,[End_DTTM]
           ,[Process_Status_ID]
           ,[CreateDate_DTTM])
           select [Property_ID]
           ,[Business_DT]
           ,[Caught_up_DTTM]
           ,[Rate_Unqualified_DTTM]
           ,[WebRate_DTTM]
           ,[Decision_Type_ID]
           ,[Start_DTTM]
           ,[End_DTTM]
           ,[Process_Status_ID]
           ,[CreateDate_DTTM] from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1;    

INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
           ,[Month_ID],[Year_ID],[CreateDate])
     select latestDecision.maxDecisionID,5,2,'2010-11-01',4,50,1,'None',Null,1,1,1,'2010-01-01 00:00:00' from (select MAX(decision_id) as maxDecisionID from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1) latestDecision;
     
     INSERT INTO [PACE_Bar_Output]([Decision_ID],[Property_ID],[Accom_Class_ID],[Arrival_DT],[Rate_Unqualified_ID]
           ,[Derived_Unqualified_Value],[LOS],[Override],[Floor_Rate_Unqualified_ID],[Decision_Reason_Type_ID]
           ,[Month_ID],[Year_ID],[CreateDate])
     select latestDecision.maxDecisionID,5,2,'2010-11-01',12,50,-1,'None',Null,1,1,1,'2010-01-01 00:00:00' from (select MAX(decision_id) as maxDecisionID from Decision where property_ID = 5 and Business_DT = '2010-01-01' and Process_Status_ID = 13 and Decision_Type_ID = 1) latestDecision;

set @Property_ID = 5

set @firstdayofcurrentmonth = GETDATE()

set @firstdayofcurrentmonth = CAST(CAST(YEAR(@firstdayofcurrentmonth) AS VARCHAR(4)) + '/' + 
                CAST(MONTH(@firstdayofcurrentmonth) AS VARCHAR(2)) + '/01' AS DATETIME)
 
set @Webrate_Source_Property_ID = (Select Webrate_Source_Property_ID from Webrate_Source_Property 
where Property_ID=@property_ID)

insert into Webrate (Webrate_Source_Property_ID,Webrate_GenerationDate,Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID,Webrate_Type_ID,
Occupancy_DT,LOS,Webrate_Remark,Webrate_Status,Webrate_Currency,Webrate_RateValue,Webrate_Page_Number,Webrate_Rank,Webrate_Rating,CreateDate, Webrate_RateValue_Display)
  select @Webrate_Source_Property_ID,GETDATE(),Webrate_Competitors_ID,Webrate_Channel_ID,Webrate_Accom_Type_ID,1,today,1,'Test data','A','USD',
round(cast('0.'+cast((Webrate_Competitors_ID*Webrate_Channel_ID*Webrate_Accom_Type_ID*DAY(today)) as nchar ) as float)  * ((200.0+0)-100.0)+100.0,0),
1,1,1,'2010-12-02',
round(cast('0.'+cast((Webrate_Competitors_ID*Webrate_Channel_ID*Webrate_Accom_Type_ID*DAY(today)) as nchar ) as float)  * ((200.0+0)-100.0)+100.0,0)
from Webrate_Channel cross join Webrate_Competitors cross join Webrate_Accom_Type cross join date_list
where 
Webrate_Channel.Property_ID=@Property_ID
and
Webrate_Competitors.Property_ID=@Property_ID
and
Webrate_Accom_Type.Property_ID=@Property_ID
and
today<=dateadd(day,100,@firstdayofcurrentmonth);

delete from date_list;

---------Property 5-----------------
set @Property_ID = 5

------------------Start Insert STR_Monthly --------------------------------
insert into STR_Monthly (Property_ID, Chain_ID, Mgmt_ID, Owner_ID, Hotel_Name, Date, Property_Avail, Property_Sold, Property_Rev, Comp_Set_Avail, Comp_Set_Sold, Comp_Set_Rev, Created_DTTM, Last_Updated_DTTM)
values(@Property_ID, 100, 200, 300, 'Hilton - Pune', DATEADD(mm, DATEDIFF(mm, 0, GETDATE()) -1, 0), 200, 150, 15000.00, 225, 175, 17500.00, GETDATE(), GETDATE())

insert into STR_Monthly (Property_ID, Chain_ID, Mgmt_ID, Owner_ID, Hotel_Name, Date, Property_Avail, Property_Sold, Property_Rev, Comp_Set_Avail, Comp_Set_Sold, Comp_Set_Rev, Created_DTTM, Last_Updated_DTTM)
values(@Property_ID, 100, 200, 300, 'Hilton - Pune', DATEADD(mm, DATEDIFF(mm, 0, GETDATE()) - 2, 0), 200, 161, 17225.00, 225, 198, 20638.00, GETDATE(), GETDATE())

insert into STR_Monthly (Property_ID, Chain_ID, Mgmt_ID, Owner_ID, Hotel_Name, Date, Property_Avail, Property_Sold, Property_Rev, Comp_Set_Avail, Comp_Set_Sold, Comp_Set_Rev, Created_DTTM, Last_Updated_DTTM)
values(@Property_ID, 100, 200, 300, 'Hilton - Pune', DATEADD(mm, DATEDIFF(mm, 0, GETDATE()) - 3, 0), 200, 185, 19944.00, 225, 211, 22487.00, GETDATE(), GETDATE())

------------------Start Insert STR_Daily --------------------------------
set @startdate = GETDATE()- 720

set @enddate = GETDATE()

exec  generatedates @startDate=@startdate, @endDate=@enddate;

insert into STR_Daily (Property_ID, Chain_ID, Mgmt_ID, Owner_ID, Hotel_Name, Occupancy_DT, Property_Avail, Property_Sold, Property_Rev, Comp_Set_Avail, Comp_Set_Sold, Comp_Set_Rev, Created_DTTM, Last_Updated_DTTM)
select @Property_ID, 100, 200, 300, 'Hilton - Pune', today, 200, ABS(Checksum(NewID()) % 35) + 160, 30100.00, 250, ABS(Checksum(NewID()) % 30) + 219, 40140.00, GETDATE(), GETDATE()
from Date_list;

DELETE FROM date_list;

---------Property 5-----------------
SET @Property_ID = 5

------------------Start Insert Operations Report Data --------------------------------
SET @startdate = GETDATE()

SET @enddate = GETDATE() + 21

EXEC  generatedates @startDate=@startdate, @endDate=@enddate;

INSERT INTO Arr_Dep_FCST SELECT 1, @Property_ID, today, ABS(CHECKSUM(NewId())) % 100, ABS(CHECKSUM(NewId())) % 50, ABS(CHECKSUM(NewId())) % 25, ABS(CHECKSUM(NewId())) % 150, ABS(CHECKSUM(NewId())) % 100, ABS(CHECKSUM(NewId())) % 40, MONTH(today), RIGHT(YEAR(today), 2), GETDATE()
FROM date_list;

DELETE from date_list;

-- Current Year
set @startdate = GETDATE()-61

set @enddate = GETDATE()+120

exec  generatedates @startDate=@startdate, @endDate=@enddate;

-- Last Year
set @lastyear_startdate = GETDATE()-426

set @lastyear_enddate = GETDATE()-305

exec  generatedates @startDate=@lastyear_startdate, @endDate=@lastyear_enddate;

insert into dbo.CP_Decision_Bar_Output
	(Property_ID, Decision_ID, Product_ID, Decision_Reason_Type_ID, Accom_Type_ID, Arrival_DT, LOS, Optimal_BAR, Pretty_BAR, Override, Floor_Rate, Ceil_Rate)
select Property_ID, (select max(decision_id) from Decision), 1, 1, Accom_Type_ID, today, -1, cast((Accom_Type_ID * 30) + cast('0.'+cast((Accom_Type_ID*DAY(today)) as nchar ) as float) - MONTH(today) AS decimal (6,2)), cast((Accom_Type_ID * 30) + cast('0.'+cast((Accom_Type_ID*DAY(today)) as nchar ) as float) - MONTH(today) AS decimal (6,0)), 'NONE', null, null
from date_list cross join Accom_Type
where
Accom_Type.Property_ID=5
and
Accom_Type.Accom_Class_ID in (select Accom_Class_ID from Accom_Class where Accom_Class_Name<>'Unassigned');


-- Load Function Space Forecast Data --

------------------Start Insert FS_Fcst--------------------------------
insert into FS_Fcst (Property_ID, FS_Cfg_Day_Part_ID, Occupancy_DT, System_Demand_Fcst, Fcst_Variance)
select @Property_ID, FS_Cfg_Day_Part_ID, today, ((DAY(today)+FS_Cfg_Day_Part_ID)) *.01, ((DAY(today)+FS_Cfg_Day_Part_ID)/2) *.00001
from Date_list cross join FS_Cfg_Day_Part
where FS_Cfg_Day_Part.Property_ID = @Property_ID
and FS_Cfg_Day_Part.Is_Included = 1
------------------End Insert FS_Fcst----------------------------------

------------------Start Insert FS_Fcst_Eval_Override------------------
-- Overriding Monday Morning to be Open
insert into FS_Fcst_Eval_Override (Property_ID,FS_Cfg_Day_Part_ID,Occupancy_DT,Evaluation_Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)
select @Property_ID,FS_Cfg_Day_Part_ID, today, 1, 16557, DATEADD(DAY,-3, today), 16557, DATEADD(DAY,-3, today)
from Date_list cross join FS_Cfg_Day_Part
where datename(dw,today) = 'Monday' 
and FS_Cfg_Day_Part.Property_ID = @Property_ID
and FS_Cfg_Day_Part.Name='Morning'
and FS_Cfg_Day_Part.Is_Included = 1

-- Overriding Thursday Evening to be Closed
insert into FS_Fcst_Eval_Override (Property_ID,FS_Cfg_Day_Part_ID,Occupancy_DT,Evaluation_Status_ID,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)
select @Property_ID,FS_Cfg_Day_Part_ID, today, 2, 16557, DATEADD(DAY,-3, today), 16557, DATEADD(DAY,-3, today)
from Date_list cross join FS_Cfg_Day_Part
where datename(dw,today) = 'Thursday' 
and FS_Cfg_Day_Part.Property_ID = @Property_ID
and FS_Cfg_Day_Part.Name='Evening'
and FS_Cfg_Day_Part.Is_Included = 1

------------------END Insert FS_Fcst_Eval_Override---------------------

------------------Start Insert FS_OTB----------------------------------
insert into FS_OTB (Property_ID,FS_Cfg_Day_Part_ID,Occupancy_DT,Utilization,Utilization_With_Prospects,Capacity)
select @Property_ID,FS_Cfg_Day_Part_ID,today, ((DAY(today)+FS_Cfg_Day_Part_ID)) *.01, ((DAY(today)+FS_Cfg_Day_Part_ID)) *.02, 1
from Date_list cross join FS_Cfg_Day_Part
where FS_Cfg_Day_Part.Property_ID = @Property_ID
and FS_Cfg_Day_Part.Is_Included = 1
------------------END Insert FS_OTB------------------------------------

------------------Start Insert FS_KPI----------------------------------
insert into FS_KPI (Property_ID, Occupancy_DT, Pro_Post, Pro_Past, Rev_Post, Rev_Past)
select @Property_ID, today, (DAY(today)/3) * .01, (DAY(today)/4) * .01, (DAY(today)) * .01, (DAY(today)/2) * .01
from Date_list
------------------END Insert FS_KPI------------------------------------

------------------Start Insert FS_Fcst_Override------------------------

--Overriding Tuesday Afternoon Utilization values

------------------Start Insert Fct_Spc_Fcst_Override------------------------

--Overriding Tuesday Afternoon Utilization values ----
insert into FS_Fcst_Override (Property_ID, FS_Cfg_Day_Part_ID, Occupancy_DT, User_Utilization, Data_Sync_On,Created_By_User_ID,Created_DTTM,Last_Updated_By_User_ID,Last_Updated_DTTM)
select @Property_ID, FS_Cfg_Day_Part_ID, today, (DAY(today)) * .02, DATEADD(DAY,-3, today), 16557, DATEADD(DAY,-3, today), 16557, DATEADD(DAY,-3, today)
from Date_list cross join FS_Cfg_Day_Part
where datename(dw,today) = 'Tuesday' 
and FS_Cfg_Day_Part.Property_ID = @Property_ID
and FS_Cfg_Day_Part.Name='Afternoon'
and FS_Cfg_Day_Part.Is_Included = 1;

-- Load Function Space Sales and Catering Identifier --

update dbo.FS_Cfg_Func_Room set Sales_Catering_Identifier = '1' where FS_Cfg_Func_Room_ID = 1;
update dbo.FS_Cfg_Func_Room set Sales_Catering_Identifier = '2' where FS_Cfg_Func_Room_ID = 2;
update dbo.FS_Cfg_Func_Room set Sales_Catering_Identifier = '3' where FS_Cfg_Func_Room_ID = 3;

delete from date_list;

-- Load Function Space Override Data --
set @Property_ID = 5

------------------------Set Dates------------------------------------------
-- Current Year
set @startdate = GETDATE()-61

set @enddate = GETDATE()+120

exec  generatedates @startDate=@startdate, @endDate=@enddate;

-- Last Year
set @lastyear_startdate = GETDATE()-426

set @lastyear_enddate = GETDATE()-305

exec  generatedates @startDate=@lastyear_startdate, @endDate=@lastyear_enddate;

------------------END Insert FS_Fcst_Override--------------------------

--  Load Function Space Booking and Event Data --

SET IDENTITY_INSERT dbo.FS_Booking ON;

insert into [dbo].[FS_Booking] 
([FS_Booking_ID], [Property_ID], [FS_Cfg_Booking_Type_ID], [FS_Cfg_Mkt_Seg_ID], [FS_Cfg_Status_ID], [Sales_Catering_Identifier], [Acct_Name_ID], [Arrival_DT], [Attendees], [Cutoff_DT], [Cutoff_Days], [Block_Code], [Block_Name],[Booking_Category], [Decision_DT], [Departure_DT], [SC_Created_DTTM], [SC_Last_Updated_DTTM], [Created_DTTM], [Last_Updated_DTTM])
 values  (1, 5, NULL, 1, 1, 12, 55, GETDATE(),  100, GETDATE(), 5, 'DEF', 'DHL', 1, GETDATE(), GETDATE(), GETDATE(), GETDATE(), GETDATE(), GETDATE());

SET IDENTITY_INSERT dbo.FS_Booking OFF


-- Start Insert FS_Event--------------------------------

DELETE from dbo.FS_Event;

SET IDENTITY_INSERT dbo.FS_Event ON;

DECLARE @Room_Start_Time datetime
DECLARE @Room_End_Time datetime
set @Room_Start_Time = CONVERT(DATETIME, CONVERT(DATE, CURRENT_TIMESTAMP)) + '14:00'
set @Room_End_Time = CONVERT(DATETIME, CONVERT(DATE, CURRENT_TIMESTAMP)) + '16:00'

insert into [dbo].[FS_Event] 
([FS_Event_ID], [Property_ID], [FS_Booking_ID], [FS_Cfg_Func_Room_ID], [FS_Cfg_Event_Type_ID], [FS_Cfg_Func_Room_Setup_ID], [FS_Cfg_Status_ID], [Sales_Catering_Identifier], [Block_Start_DTTM], [Block_End_DTTM], [Start_Date_DTTM], [End_Date_DTTM], [Setup_Time_Min], [Teardown_Time_Min], [Forecast_Attendees], [Actual_Attendees], [Is_Moveable], [Created_DTTM], [Last_Updated_DTTM])
 values (1, 5, 1, 2, 4, NULL, 1, 12, @Room_Start_Time, @Room_End_Time, CONVERT(DATE, GETDATE()), CONVERT(DATE, GETDATE()), 15, 15, 100, 95, 0, GETDATE(), GETDATE());


SET IDENTITY_INSERT dbo.FS_Event OFF;

------------------End Insert FS_Event----------------------------------

SET IDENTITY_INSERT [dbo].[Wash_Ind_Group_FCST_OVR] ON;
INSERT [dbo].[Wash_Ind_Group_FCST_OVR] ([Wash_Ind_Group_Fcst_OVR_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash_OVR], [Expiration_DT], [Status_ID])
    VALUES (10664, 11, Convert(date, GETDATE() + 50), 4,  259, 5, 7, CAST(12.66 AS Numeric(8, 2)), CAST(12.65 AS Numeric(8, 2)), Convert(date, GETDATE() + 48), 1);
INSERT [dbo].[Wash_Ind_Group_FCST_OVR] ([Wash_Ind_Group_Fcst_OVR_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash_OVR], [Expiration_DT], [Status_ID])
    VALUES (10665, 11, Convert(date, GETDATE() + 50), 4,  247, 5, 7, CAST(12.99 AS Numeric(8, 2)), CAST(12.99 AS Numeric(8, 2)), Convert(date, GETDATE() + 49), 2);
INSERT [dbo].[Wash_Ind_Group_FCST_OVR] ([Wash_Ind_Group_Fcst_OVR_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash_OVR], [Expiration_DT], [Status_ID])
    VALUES (10666, 11, CONVERT(date, GETDATE() + 50), 5, 243, 5, 7, CAST(15.50 AS Numeric(8, 2)), CAST(15.50 AS Numeric(8, 2)), Convert(date, GETDATE() + 47), 1);
INSERT [dbo].[Wash_Ind_Group_FCST_OVR] ([Wash_Ind_Group_Fcst_OVR_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash_OVR], [Expiration_DT], [Status_ID])
    VALUES (10667, 11, CONVERT(date, GETDATE() + 50), 7, 239, 5, 7, CAST(20.55 AS Numeric(8, 2)), CAST(20.55 AS Numeric(8, 2)), Convert(date, GETDATE() + 46), 1);
SET IDENTITY_INSERT [dbo].[Wash_Ind_Group_FCST_OVR] OFF;

SET IDENTITY_INSERT [dbo].[Wash_Ind_Group_FCST] ON;
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10665, 11, Convert(date, GETDATE() + 50), 4, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10666, 11, CONVERT(date, GETDATE() + 50), 5, 309, 5, 7, CAST(5.90 AS Numeric(8, 2)), CAST(5.91 AS Numeric(8, 2)), CAST(0.00 AS Numeric(8, 2)), CAST(0.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10667, 11, CONVERT(date, GETDATE() + 50), 7, 309, 5, 7, CAST(5.95 AS Numeric(8, 2)), CAST(5.96 AS Numeric(8, 2)), CAST(0.00 AS Numeric(8, 2)), CAST(0.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10668, 11, CONVERT(date, GETDATE() + 51), 4, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10669, 11, CONVERT(date, GETDATE() + 51), 5, 309, 5, 7, CAST(5.86 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10670, 11, CONVERT(date, GETDATE() + 51), 7, 309, 5, 7, CAST(5.87 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10671, 11, CONVERT(date, GETDATE() + 52), 4, 309, 5, 7, CAST(5.90 AS Numeric(8, 2)), CAST(5.90 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10672, 11, CONVERT(date, GETDATE() + 52), 5, 309, 5, 7, CAST(5.90 AS Numeric(8, 2)), CAST(5.90 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10673, 11, CONVERT(date, GETDATE() + 52), 7, 309, 5, 7, CAST(5.90 AS Numeric(8, 2)), CAST(5.90 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10674, 11, CONVERT(date, GETDATE() + 53), 4, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10675, 11, CONVERT(date, GETDATE() + 53), 5, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10676, 11, CONVERT(date, GETDATE() + 53), 7, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10677, 11, CONVERT(date, GETDATE() + 54), 4, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10678, 11, CONVERT(date, GETDATE() + 54), 5, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (10679, 11, CONVERT(date, GETDATE() + 54), 7, 309, 5, 7, CAST(5.85 AS Numeric(8, 2)), CAST(5.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15267, 18, CONVERT(date, GETDATE() + 28), 4, 305, 5, 7, CAST(21.85 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15268, 18, CONVERT(date, GETDATE() + 28), 7, 305, 5, 20, CAST(21.85 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15269, 18, CONVERT(date, GETDATE() + 29), 4, 305, 5, 20, CAST(21.80 AS Numeric(8, 2)), CAST(21.80 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15270, 18, CONVERT(date, GETDATE() + 29), 7, 305, 5, 20, CAST(21.81 AS Numeric(8, 2)), CAST(21.81 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15288, 18, CONVERT(date, GETDATE() + 28), 5, 305, 5, 20, CAST(21.85 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15289, 18, CONVERT(date, GETDATE() + 28), 6, 305, 5, 20, CAST(21.95 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15290, 18, CONVERT(date, GETDATE() + 29), 5, 305, 5, 20, CAST(21.91 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15291, 18, CONVERT(date, GETDATE() + 29), 6, 305, 5, 20, CAST(21.92 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23946, 49, CONVERT(date, GETDATE() + 17), 4, 301, 5, 20, CAST(0.92 AS Numeric(8, 2)), CAST(0.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23947, 49, CONVERT(date, GETDATE() + 17), 5, 301, 5, 20, CAST(0.92 AS Numeric(8, 2)), CAST(0.92 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23948, 49, CONVERT(date, GETDATE() + 17), 6, 301, 5, 20, CAST(0.72 AS Numeric(8, 2)), CAST(0.72 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23949, 49, CONVERT(date, GETDATE() + 17), 7, 301, 5, 20, CAST(0.77 AS Numeric(8, 2)), CAST(0.77 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23950, 49, CONVERT(date, GETDATE() + 18), 4, 301, 5, 20, CAST(0.78 AS Numeric(8, 2)), CAST(0.79 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (23951, 49, CONVERT(date, GETDATE() + 18), 5, 301, 5, 20, CAST(0.75 AS Numeric(8, 2)), CAST(0.75 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15292, 18, CONVERT(date, GETDATE() - 29), 5, 305, 5, 20, CAST(21.91 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15293, 18, CONVERT(date, GETDATE() - 29), 6, 305, 5, 20, CAST(21.92 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15294, 49, CONVERT(date, GETDATE() - 29), 4, 305, 5, 20, CAST(21.91 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15295, 49, CONVERT(date, GETDATE() - 29), 5, 305, 5, 20, CAST(21.92 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15296, 49, CONVERT(date, GETDATE() - 29), 6, 305, 5, 20, CAST(21.91 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
INSERT [dbo].[Wash_Ind_Group_FCST] ([Wash_Ind_Group_Fcst_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Decision_ID], [Property_ID],[Mkt_Seg_ID], [System_Wash], [User_Wash], [Noshow_Value], [disagg_val], [CreateDate_DTTM])
    VALUES (15297, 49, CONVERT(date, GETDATE() - 29), 7, 305, 5, 20, CAST(21.92 AS Numeric(8, 2)), CAST(21.86 AS Numeric(8, 2)), CAST(0.01 AS Numeric(8, 2)), CAST(1.00 AS Numeric(8, 2)), GETDATE());
SET IDENTITY_INSERT [dbo].[Wash_Ind_Group_FCST] OFF;


SET IDENTITY_INSERT [dbo].[Wash_Forecast_Group_FCST_OVR] ON;
INSERT [dbo].[Wash_Forecast_Group_FCST_OVR] ([Wash_Forecast_Group_FCST_OVR_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash_OVR], [Expiration_DT], [Status_ID]) VALUES (21, 193, 5, 22, CONVERT(date, GETDATE() + 37), CAST(90.00 AS Numeric(18, 2)), CAST(25.00 AS Numeric(18, 2)), CONVERT(date, GETDATE() + 37), 2);
SET IDENTITY_INSERT [dbo].[Wash_Forecast_Group_FCST_OVR] OFF;

SET IDENTITY_INSERT [dbo].[Wash_Forecast_Group_FCST] ON;
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (68993, 87, 5, 20, CONVERT(date, GETDATE() + 1), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C400F9E45E AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (72278, 89, 5, 20, CONVERT(date, GETDATE() + 2), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C5009050F7 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (82133, 100, 5, 20, CONVERT(date, GETDATE() + 3), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C601279090 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (85418, 102, 5, 20, CONVERT(date, GETDATE() + 4), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C7006F6E54 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (88703, 105, 5, 20, CONVERT(date, GETDATE() + 5), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C80042754B AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (91988, 109, 5, 20, CONVERT(date, GETDATE() + 6), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C9002E0DA6 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (98558, 119, 5, 20, CONVERT(date, GETDATE() + 7), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CA00FFC63F AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (101843, 122, 5, 20, CONVERT(date, GETDATE() + 8), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CB008C955C AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (105128, 125, 5, 20, CONVERT(date, GETDATE() + 9), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CC0073C6EC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (108413, 131, 5, 20, CONVERT(date, GETDATE() + 10), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CD002FB17A AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (111698, 134, 5, 20, CONVERT(date, GETDATE() + 11), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CE0074E47C AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (114983, 137, 5, 20, CONVERT(date, GETDATE() + 12), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CF001E6A20 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (121553, 161, 5, 20, CONVERT(date, GETDATE() + 13), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D001118CAB AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (124838, 164, 5, 20, CONVERT(date, GETDATE() + 14), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D1001F901C AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (128123, 177, 5, 22, CONVERT(date, GETDATE() + 15), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D300D43E50 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (141263, 188, 5, 22, CONVERT(date, GETDATE() + 19), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D600868075 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (144548, 197, 5, 22, CONVERT(date, GETDATE() + 20), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D700798086 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (147833, 200, 5, 22, CONVERT(date, GETDATE() + 21), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D800A6136D AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (154403, 208, 5, 22, CONVERT(date, GETDATE() + 22), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0D901743DC7 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (157688, 210, 5, 22, CONVERT(date, GETDATE() + 23), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DA00204D1D AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (160973, 217, 5, 22, CONVERT(date, GETDATE() + 24), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DB001C2917 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (164258, 220, 5, 22, CONVERT(date, GETDATE() + 25), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DC00666696 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (167543, 223, 5, 22, CONVERT(date, GETDATE() + 26), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DD00248027 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (170828, 240, 5, 22, CONVERT(date, GETDATE() + 27), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DF007F8168 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (174113, 245, 5, 22, CONVERT(date, GETDATE() + 28), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DF0099F930 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (177398, 250, 5, 22, CONVERT(date, GETDATE() + 29), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0E000609C6C AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (180683, 254, 5, 22, CONVERT(date, GETDATE() + 30), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E1003D8984 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (183968, 257, 5, 22, CONVERT(date, GETDATE() + 31), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E301827534 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (187253, 260, 5, 22, CONVERT(date, GETDATE() + 32), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E301839900 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (190538, 263, 5, 22, CONVERT(date, GETDATE() + 33), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E3018AA5B0 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (193823, 268, 5, 22, CONVERT(date, GETDATE() + 34), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E500652CC8 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (197108, 271, 5, 22, CONVERT(date, GETDATE() + 35), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E60097B774 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (200393, 274, 5, 22, CONVERT(date, GETDATE() + 36), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E700D0A034 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (203678, 277, 5, 22, CONVERT(date, GETDATE() + 37), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E800678018 AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (206963, 280, 5, 22, CONVERT(date, GETDATE() + 38), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E90006FB1C AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (210248, 283, 5, 22, CONVERT(date, GETDATE() + 4), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0EA00410FDC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213533, 286, 5, 22, CONVERT(date, GETDATE() + 39), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213542, 286, 5, 22, CONVERT(date, GETDATE() + 40), CAST(50.00 AS Numeric(18, 2)), CAST(50.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213551, 286, 5, 22, CONVERT(date, GETDATE() + 41), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213560, 286, 5, 22, CONVERT(date, GETDATE() + 42), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213569, 286, 5, 22, CONVERT(date, GETDATE() + 43), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213578, 286, 5, 22, CONVERT(date, GETDATE() + 44), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213587, 286, 5, 22, CONVERT(date, GETDATE() + 45), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213596, 286, 5, 22, CONVERT(date, GETDATE() + 46), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213605, 286, 5, 22, CONVERT(date, GETDATE() + 47), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213614, 286, 5, 22, CONVERT(date, GETDATE() + 48), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213623, 286, 5, 22, CONVERT(date, GETDATE() + 50), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213632, 286, 5, 22, CONVERT(date, GETDATE() + 16), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213641, 286, 5, 22, CONVERT(date, GETDATE() + 17), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213650, 286, 5, 22, CONVERT(date, GETDATE() + 18), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213659, 286, 5, 22, CONVERT(date, GETDATE() + 51), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213668, 286, 5, 22, CONVERT(date, GETDATE() + 52), CAST(88.05 AS Numeric(18, 2)), CAST(88.05 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213677, 286, 5, 22, CONVERT(date, GETDATE() + 53), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213686, 286, 5, 22, CONVERT(date, GETDATE() + 54), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213695, 286, 5, 22, CONVERT(date, GETDATE() + 55), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213704, 286, 5, 22, CONVERT(date, GETDATE() + 56), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213713, 286, 5, 22, CONVERT(date, GETDATE() + 57), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213722, 286, 5, 22, CONVERT(date, GETDATE() + 58), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213731, 286, 5, 22, CONVERT(date, GETDATE() + 59), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
INSERT [dbo].[Wash_Forecast_Group_FCST] ([Wash_Forecast_Group_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (213740, 286, 5, 22, CONVERT(date, GETDATE() + 60), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A98CC AS DateTime));
SET IDENTITY_INSERT [dbo].[Wash_Forecast_Group_FCST] OFF;

SET IDENTITY_INSERT [dbo].[Wash_FCST] ON;
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (76658, 87, 5, 20, 2, CONVERT(date, GETDATE() + 60), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C400F9E3E9 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (94908, 102, 5, 20, 2, CONVERT(date, GETDATE() + 4), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C7006F6DB9 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (98558, 105, 5, 20, 2, CONVERT(date, GETDATE() + 5), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C8004274B1 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (102208, 109, 5, 20, 2, CONVERT(date, GETDATE() + 6), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0C9002E0D07 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (109508, 119, 5, 20, 2, CONVERT(date, GETDATE() + 7), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CA00FFC5A5 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (113158, 122, 5, 20, 2, CONVERT(date, GETDATE() + 8), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0CB008C94C1 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (116808, 125, 5, 20, 2, CONVERT(date, GETDATE() + 9), CAST(0.78 AS Numeric(18, 2)), CAST(0.78 AS Numeric(18, 2)), CAST(0x0000A0CC0073C652 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (120458, 131, 5, 20, 2, CONVERT(date, GETDATE() + 10), CAST(0.78 AS Numeric(18, 2)), CAST(0.78 AS Numeric(18, 2)), CAST(0x0000A0CD002FB0DB AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (124108, 134, 5, 20, 2, CONVERT(date, GETDATE() + 11), CAST(0.78 AS Numeric(18, 2)), CAST(0.78 AS Numeric(18, 2)), CAST(0x0000A0CE0074E3E2 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (127758, 137, 5, 20, 2, CONVERT(date, GETDATE() + 12), CAST(0.78 AS Numeric(18, 2)), CAST(0.78 AS Numeric(18, 2)), CAST(0x0000A0CF001E6981 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (135058, 161, 5, 20, 2, CONVERT(date, GETDATE() + 13), CAST(0.77 AS Numeric(18, 2)), CAST(0.77 AS Numeric(18, 2)), CAST(0x0000A0D001118BF4 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (138708, 164, 5, 20, 2, CONVERT(date, GETDATE() + 14), CAST(0.77 AS Numeric(18, 2)), CAST(0.77 AS Numeric(18, 2)), CAST(0x0000A0D1001F8F61 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (142359, 177, 5, 22, 2, CONVERT(date, GETDATE() + 15), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D300D43D99 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (156959, 188, 5, 22, 2, CONVERT(date, GETDATE() + 19), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D600867FBA AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (160609, 197, 5, 22, 2, CONVERT(date, GETDATE() + 20), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D700797FD0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (164259, 200, 5, 22, 2, CONVERT(date, GETDATE() + 21), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0D800A612B2 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (171559, 208, 5, 22, 2, CONVERT(date, GETDATE() + 22), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0D901743CD8 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (175209, 210, 5, 22, 2, CONVERT(date, GETDATE() + 23), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DA00204C62 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (178859, 217, 5, 22, 2, CONVERT(date, GETDATE() + 24), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DB001C285B AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (182509, 220, 5, 22, 2, CONVERT(date, GETDATE() + 25), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DC006665DF AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (186159, 223, 5, 22, 2, CONVERT(date, GETDATE() + 26), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DD00247F6C AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (189809, 240, 5, 22, 2, CONVERT(date, GETDATE() + 27), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DF007F8168 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (193459, 245, 5, 22, 2, CONVERT(date, GETDATE() + 28), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0DF0099F930 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (197109, 250, 5, 22, 2, CONVERT(date, GETDATE() + 29), CAST(23.07 AS Numeric(18, 2)), CAST(23.07 AS Numeric(18, 2)), CAST(0x0000A0E000609C6C AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (200759, 254, 5, 22, 2, CONVERT(date, GETDATE() + 30), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E1003D8984 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (204409, 257, 5, 22, 2, CONVERT(date, GETDATE() + 31), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E301827408 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (208059, 260, 5, 22, 2, CONVERT(date, GETDATE() + 32), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E3018397D4 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (211709, 263, 5, 22, 2, CONVERT(date, GETDATE() + 33), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E3018AA484 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (215359, 268, 5, 22, 2, CONVERT(date, GETDATE() + 34), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E500652B9C AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (219009, 271, 5, 22, 2, CONVERT(date, GETDATE() + 35), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E60097B774 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (222659, 274, 5, 22, 2, CONVERT(date, GETDATE() + 36), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E700D09F08 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (226309, 277, 5, 22, 2, CONVERT(date, GETDATE() + 37), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E800678018 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (229959, 280, 5, 22, 2, CONVERT(date, GETDATE() + 38), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0E90006F9F0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (233609, 283, 5, 22, 2, CONVERT(date, GETDATE() + 4), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0EA00410EB0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237259, 286, 5, 22, 2, CONVERT(date, GETDATE() + 39), CAST(0.00 AS Numeric(18, 2)), CAST(0.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237269, 286, 5, 22, 2, CONVERT(date, GETDATE() + 40), CAST(50.00 AS Numeric(18, 2)), CAST(50.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237279, 286, 5, 22, 2, CONVERT(date, GETDATE() + 41), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237289, 286, 5, 22, 2, CONVERT(date, GETDATE() + 42), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237299, 286, 5, 22, 2, CONVERT(date, GETDATE() + 43), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237309, 286, 5, 22, 2, CONVERT(date, GETDATE() + 44), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237319, 286, 5, 22, 2, CONVERT(date, GETDATE() + 45), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237329, 286, 5, 22, 2, CONVERT(date, GETDATE() + 46), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237339, 286, 5, 22, 2, CONVERT(date, GETDATE() + 47), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237349, 286, 5, 22, 2, CONVERT(date, GETDATE() + 48), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237359, 286, 5, 22, 2, CONVERT(date, GETDATE() + 50), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237369, 286, 5, 22, 2, CONVERT(date, GETDATE() + 16), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237379, 286, 5, 22, 2, CONVERT(date, GETDATE() + 17), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237389, 286, 5, 22, 2, CONVERT(date, GETDATE() + 18), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237399, 286, 5, 22, 2, CONVERT(date, GETDATE() + 51), CAST(60.00 AS Numeric(18, 2)), CAST(60.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237409, 286, 5, 22, 2, CONVERT(date, GETDATE() + 52), CAST(88.05 AS Numeric(18, 2)), CAST(88.05 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237419, 286, 5, 22, 2, CONVERT(date, GETDATE() + 53), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237429, 286, 5, 22, 2, CONVERT(date, GETDATE() + 54), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237439, 286, 5, 22, 2, CONVERT(date, GETDATE() + 55), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237449, 286, 5, 22, 2, CONVERT(date, GETDATE() + 56), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237459, 286, 5, 22, 2, CONVERT(date, GETDATE() + 57), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237469, 286, 5, 22, 2, CONVERT(date, GETDATE() + 58), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237479, 286, 5, 22, 2, CONVERT(date, GETDATE() + 59), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
INSERT [dbo].[Wash_FCST] ([Wash_FCST_ID], [Decision_ID], [Property_ID], [Forecast_Group_ID], [Accom_Class_ID], [Occupancy_DT], [System_Wash], [User_Wash], [CreateDate_DTTM]) VALUES (237489, 286, 5, 22, 2, CONVERT(date, GETDATE() + 60), CAST(90.00 AS Numeric(18, 2)), CAST(90.00 AS Numeric(18, 2)), CAST(0x0000A0EA018A97A0 AS DateTime));
SET IDENTITY_INSERT [dbo].[Wash_FCST] OFF;

UPDATE Group_Master SET Start_DT = DateAdd(month, -3, Start_DT) where Group_ID = 49;

UPDATE Group_Master SET Mkt_Seg_ID = 11 WHERE Group_ID = 18;
UPDATE Wash_Ind_Group_Fcst SET Mkt_Seg_ID = 11 WHERE Group_ID = 18;

UPDATE Group_Master set Pickup_Type_Code = 'INDV' where Group_ID = 11;
UPDATE Group_Master set Pickup_Type_Code = 'Rooming List' where Group_ID = 18;
UPDATE Group_Master SET Start_DT = DateAdd(month, -6, Start_DT) where Group_ID = 18;
UPDATE Group_Master SET End_DT = DateAdd(day, 1, End_DT) where Group_ID = 49;

SET IDENTITY_INSERT [dbo].[Group_Block] ON;
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33958, 49, CONVERT(date, GETDATE() - 29), 4, CAST(24 AS Numeric(18, 0)), CAST(4 AS Numeric(18, 0)), CAST(14 AS Numeric(18, 0)), CAST(44.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33959, 49, CONVERT(date, GETDATE() - 29), 5, CAST(25 AS Numeric(18, 0)), CAST(5 AS Numeric(18, 0)), CAST(15 AS Numeric(18, 0)), CAST(55.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33960, 49, CONVERT(date, GETDATE() - 29), 6, CAST(26 AS Numeric(18, 0)), CAST(6 AS Numeric(18, 0)), CAST(16 AS Numeric(18, 0)), CAST(66.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33961, 49, CONVERT(date, GETDATE() - 29), 7, CAST(27 AS Numeric(18, 0)), CAST(7 AS Numeric(18, 0)), CAST(17 AS Numeric(18, 0)), CAST(77.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (25292, 18, CONVERT(date, GETDATE() - 29), 5, CAST(19 AS Numeric(18, 0)), CAST(0 AS Numeric(18, 0)), CAST(19 AS Numeric(18, 0)), CAST(90.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (25293, 18, CONVERT(date, GETDATE() - 29), 6, CAST(17 AS Numeric(18, 0)), CAST(0 AS Numeric(18, 0)), CAST(17 AS Numeric(18, 0)), CAST(80.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33954, 49, CONVERT(date, GETDATE() + 19), 4, CAST(24 AS Numeric(18, 0)), CAST(4 AS Numeric(18, 0)), CAST(14 AS Numeric(18, 0)), CAST(44.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33955, 49, CONVERT(date, GETDATE() + 19), 5, CAST(25 AS Numeric(18, 0)), CAST(5 AS Numeric(18, 0)), CAST(15 AS Numeric(18, 0)), CAST(55.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33956, 49, CONVERT(date, GETDATE() + 19), 6, CAST(26 AS Numeric(18, 0)), CAST(6 AS Numeric(18, 0)), CAST(16 AS Numeric(18, 0)), CAST(66.00000 AS Numeric(19, 5)));
INSERT [dbo].[Group_Block] ([Group_Block_ID], [Group_ID], [Occupancy_DT], [Accom_Type_ID], [Blocks], [Pickup], [Original_Blocks], [rate]) VALUES (33957, 49, CONVERT(date, GETDATE() + 19), 7, CAST(27 AS Numeric(18, 0)), CAST(7 AS Numeric(18, 0)), CAST(17 AS Numeric(18, 0)), CAST(77.00000 AS Numeric(19, 5)));
SET IDENTITY_INSERT [dbo].[Group_Block] OFF;

UPDATE Group_Master SET Mkt_Seg_ID = 4 WHERE Group_ID = 18;

INSERT INTO Wash_Forecast_Group_FCST_OVR
           (Decision_ID
           ,Property_ID
           ,Forecast_Group_ID
           ,Occupancy_DT
           ,System_Wash
           ,User_Wash_OVR
           ,Expiration_DT
           ,Status_ID)
     (select top 1 decision_id, property_id, 22
           ,DATEADD(day, 1, occupancy_dt)
           ,8
           ,9
           ,expiration_dt
           ,1
           FROM Wash_Ind_Group_Fcst_OVR);
END;