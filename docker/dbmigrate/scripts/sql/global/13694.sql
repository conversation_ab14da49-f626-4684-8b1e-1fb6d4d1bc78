use global;

BEGIN
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.futureDays', 'pacman.SandBox.YYZBO', '365', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.pastDays', 'pacman.SandBox.YYZBO', '365', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.processRoomRates', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.yieldCurrencyCode', 'pacman.SandBox.YYZBO', 'CAD','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.barDecision', 'pacman.SandBox.YYZBO', 'BARByLOS','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.maxLOS', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webRateShoppingEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.barOvrdDisplayCompetitor', 'pacman.SandBox.YYZBO', 'medianPrice'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.barOvrdAbsoluteCompetitor', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webRateAlias', 'pacman.SandBox.YYZBO', '1190660', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webRateStalenessThreshold', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.newRmCostRaw', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.population.clientDataStorageValue', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.forecasting.forecastWindowBDE', 'pacman.SandBox.YYZBO', '358', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.forecasting.forecastWindowCDP', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.optimization.optimizationWindowBDE', 'pacman.SandBox.YYZBO', '358', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.optimization.optimizationWindowCDP', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.AllowAvailableForArrival', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.EnableSingleBarDecision', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.TypeThreeData.error.maxErrors', 'pacman.SandBox.YYZBO', '103', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.TypeTwoData.error.maxErrors', 'pacman.SandBox.YYZBO', '102', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.TypeThreeData.error.percentNumber', 'pacman.SandBox.YYZBO', '10', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.receivingsystems', 'pacman.SandBox.YYZBO', 'pcrs', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.decisiontypes', 'pacman.SandBox.YYZBO', 'barfplosbyrank,fplos,hoteloverbooking,roomtypeoverbooking', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.decisiontypes', 'pacman.SandBox.YYZBO', 'barfplosbyrank,fplos,hoteloverbooking,roomtypeoverbooking', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.barfplosbyrank.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.barfplosbyrank.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.fplos.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.fplos.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.hoteloverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.hoteloverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.roomtypeoverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.roomtypeoverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'differential', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.decisionfilepath', 'pacman.SandBox.YYZBO', 'C:\G3\Data/decisions', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.propertyTimeZone', 'pacman.SandBox.YYZBO', 'America/Toronto', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.generateRateExtract', 'pacman.SandBox.YYZBO', 'Rates', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.defaultMarketSegment', 'pacman.SandBox.YYZBO', 'BAR', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.alertsEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.qualifiedFplos', 'pacman.SandBox.YYZBO', 'allsrps', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.externalSystem', 'pacman.SandBox.YYZBO', 'PCRS', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.userExceptionsEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.useSubMcatMapping', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.scheduledTwoWayDate', 'pacman.SandBox.YYZBO', 'Apr 27 2016 12:00AM', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.scheduledExtractDate', 'pacman.SandBox.YYZBO', '03/18/2016', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.allowedSkipDays', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.InfoMgrEvalService.EnableInfoMgrLastGoodRunCheck', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.DisplayName.DisplayNameEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.srpFplosAtTotalLevel', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.configurationComplete', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.crsAckLocalFilePath', 'pacman.SandBox.YYZBO', 'C:\G3\Data\crsack', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.applyVariableDecisionWindow', 'pacman.SandBox.YYZBO', 'false', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.completeDecisionUploadDaysOfWeek', 'pacman.SandBox.YYZBO', 'Sunday', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.variableDecisionWindowDays', 'pacman.SandBox.YYZBO', '180', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.fgCreationMode', 'pacman.SandBox.YYZBO', 'Normal', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.setIupdFrmFieldToNull', 'pacman.SandBox.YYZBO', 'false', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.srpAttributionSubmittedDate', 'pacman.SandBox.YYZBO', '03/18/2016', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.applydeVatRatelevels', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.MaxPropertyPerPropertyGroup', 'pacman.SandBox.YYZBO', '50', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GroupWashByGroupEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.displayPropertyCodeOrName', 'pacman.SandBox.YYZBO', 'Name', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webrateStalenessAlertsEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.RatePlanConfigurationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.user.UserAuthOneTimeSyncComplete', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pseudoRoomTypeCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.reservationStatusesToFilter', 'pacman.SandBox.YYZBO', 'WAITLIST,PROSPECT', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.InventoryHistoryReportEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.ForecastValidationReportEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.OOOThresholdHotelCapacityInPercent', 'pacman.SandBox.YYZBO', '20', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.OOOMinimumDaysExceedingThreshold', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.ScheduleReportEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-10:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-10:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-10:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-10:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-10:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-9:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-9:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-9:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-9:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-9:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-8:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-8:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-8:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-8:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-8:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-7:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-7:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-7:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-7:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-7:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-6:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-6:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-6:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-6:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-6:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-5:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-5:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-5:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-5:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-5:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS141', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-4:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-4:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-4:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-4:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-4:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-3:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-3:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-3:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-3:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC-3:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC1:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC1:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC1:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC1:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC1:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC2:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC2:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC2:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC2:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC2:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC5:30.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC5:30.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC5:30.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC5:30.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC5:30.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.isRankingEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.pastDays', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.crsTimeZone', 'pacman.SandBox.YYZBO', 'America/Chicago', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.MinConsecutiveDaysThreshold', 'pacman.SandBox.YYZBO', '14', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.CreateFGTranscactionValidationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.operaResort', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.AllSRPReportEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.dailybarPMSRateCode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.MinimumLengthOfStayByRateCode.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.noOfDecisionsToCommit', 'pacman.SandBox.YYZBO', '25', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.LRAEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.dailyBAR.useBaseRateAsSingleRate', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.dailyBAR.useSingleRateAsDoubleRate', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.dailyBAR.useExtraAdultConfig', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.dailyBAR.useExtraChildConfig', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.DecisionConfigurationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.InstallationStatusEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.miscAdjustment', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.cdp.dailymax', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.WhatIfEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.RDS.BarByLOSatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.RDS.BarByLOSatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.QualifiedRatePlanConfigurationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.QualifiedFPLOSMaxLOS', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnablePhysicalCapacityConsideration', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.ShowSystemHealthForExternalUser', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.MinimumLengthOfStayByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.YieldCurrencyCode', 'pacman.SandBox.YYZBO', 'CAD', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.historyPastDays', 'pacman.SandBox.YYZBO', '720', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.propertycode', 'pacman.SandBox.YYZBO', 'HFSCFFS', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.propertycode', 'pacman.SandBox.YYZBO', 'MBLTST', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.dailybarRateCode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.propertycode', 'pacman.SandBox.YYZBO', '60737', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.dailybarRateCode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.dailybarRateCode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.HistoricalDataAvailabilityDays', 'pacman.SandBox.YYZBO', '365', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomType.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomType.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomType.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomClass.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomClass.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomClass.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomClass.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomClass.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomClass.roundingPrecision', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.miscAdjustment', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.miscAdjustment', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.SeedExcludedDatesEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'

    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.excludedDatesPre', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.excludedDatesPost', 'pacman.SandBox.YYZBO', '28', 'int'

    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.HotelOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.HotelOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.HotelOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.RoomTypeOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.RoomTypeOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.RoomTypeOverbooking.valueType', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.RateUnqualifiedRankingEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.BdeArrivedOverdue', 'pacman.SandBox.YYZBO', '300', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.CdpArrivedOverdue', 'pacman.SandBox.YYZBO', '60', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.StepOverdue', 'pacman.SandBox.YYZBO', '60', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.BlockedOverdue', 'pacman.SandBox.YYZBO', '180', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.DecisionDeliveryOverdue', 'pacman.SandBox.YYZBO', '60', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.DisplayOptimizationSettingsTab', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.BdeCompletedOverdue', 'pacman.SandBox.YYZBO', '240', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.CdpCompletedOverdue', 'pacman.SandBox.YYZBO', '60', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.Rezview.MinMaxLosByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.Rezview.MinLosDefaultForSubOptimalScenarios', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.PasswordActiveDuration', 'pacman.SandBox.YYZBO', '90', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.MaxFailedAuthenticationRetriesAllowed', 'pacman.SandBox.YYZBO', '5', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.PasswordExpiryAlertDays', 'pacman.SandBox.YYZBO', '15', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.HighestLowestBarEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezview.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.missingRoomTypeValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.rates.baseOccupancyNumber', 'pacman.SandBox.YYZBO', 'single', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.timeToCheckAuditDate', 'pacman.SandBox.YYZBO', '0100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.feedRequestDelay', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.LastRoomValuebyRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarFplosByHierarchy.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.rates.mondayToThursday', 'pacman.SandBox.YYZBO', 'use base occupancy','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.rates.fridayToSunday', 'pacman.SandBox.YYZBO', 'use base occupancy','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezview.miscAdjustment', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.functionSpace.sqfeet', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.populationChunkSize', 'pacman.SandBox.YYZBO', '180', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.readChunkSize', 'pacman.SandBox.YYZBO', '10000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.taxAdjustmentValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webRateSource', 'pacman.SandBox.YYZBO', 'Rubicon', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opmsAgentInstallationType', 'pacman.SandBox.YYZBO', 'Shared','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.orsAgentInstallationType', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.miscAdjustment', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.miscAdjustment', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.BarFplosByHotel.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.BarFplosByHotel.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.FplosByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.FplosByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.FplosByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.FplosByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.FplosByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.FplosByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.FplosByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.FplosByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.MinimumLengthOfStayByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.MinimumLengthOfStayByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.MinimumLengthOfStayByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.MinimumLengthOfStayByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.MinimumLengthOfStayByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.MinimumLengthOfStayByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isPricingManagementEnabled', 'pacman.SandBox.YYZBO', 'false', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableDataExtractionReportSchedule', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableManualBARUpload', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.PaceDataAvailabilityDays', 'pacman.SandBox.YYZBO', '110', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.FunctionSpaceCompletedOverdue', 'pacman.SandBox.YYZBO', '480', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.InternalHistoryDaysToRetain', 'pacman.SandBox.YYZBO', '45', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isDatafeedFileEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.DailyProcessingMonitoringEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.MinimumLengthOfStayByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarFplosByHotel.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.FplosByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.FplosByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.FplosByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.FplosByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.MinimumLengthOfStayByRateCodeByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.MinimumLengthOfStayByRateCategoryByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.MinimumLengthOfStayByRateCategory.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.DBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.NetRemainingDemandForGroupEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.CostlyOutOfOrderThreshold', 'pacman.SandBox.YYZBO', '0.0', 'double'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.RDS.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratchet.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.enableForceFullDecisions', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.DisplayCancelRebookPercentageOption', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.upload.DecisionUploadWindowBDEDays', 'pacman.SandBox.YYZBO', '358', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isDerivedQualifiedRatePlanEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.validateDataFeed', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.EsDailyBarDecisionSync', 'pacman.SandBox.YYZBO', 'RateOverrideSync', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForDataExtractionReport', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForBookingSituationReport', 'pacman.SandBox.YYZBO', '8', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForOutputOverrideReport', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForPerformanceComparisonReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForPricingOverrideHistoryReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForPricingReport', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForRatePlanProductionReport', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.PurgeDataJob.Location', 'pacman.SandBox.YYZBO', 'systemHealthJob', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.PurgeDataJob.NumberOfDaysBeforeToPurge', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForForecastValidationReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForInputOverrideReport', 'pacman.SandBox.YYZBO', '3', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForMarketSegmentMappingReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForPickupAndChangeReport', 'pacman.SandBox.YYZBO', '8', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.highestBarRestrictedEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.optimization.MaxPaceOutputLength', 'pacman.SandBox.YYZBO', '365', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.groupPastDays', 'pacman.SandBox.YYZBO', '45', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.monitorProcessJobClientsBatchSize', 'pacman.SandBox.YYZBO', '175', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.tenantId', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.rollback.emailRecipients', 'pacman.SandBox.YYZBO', '','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.useTKOnDemand', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.GroupPricingMinProfitEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.inboundVendor', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.webrateCleanUpEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.LowestBarFplosChecks', 'pacman.SandBox.YYZBO', 'SkipAlways','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.HighestBarFplosChecks', 'pacman.SandBox.YYZBO', 'SkipNever','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.externalSystem.subSystem', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.maxCriteriaColumnsInDataExtractionReportSchedule', 'pacman.SandBox.YYZBO', '6', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.lowestOptimizationDecisionCheck', 'pacman.SandBox.YYZBO', 'SkipNever', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.lowVolumeMarketSegment', 'pacman.SandBox.YYZBO', '5', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.lowVolumeRateCode', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForSpecialEventReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.TARS.MinMaxLosByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.TARS.DailyBARByRateCode.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.ams.isGroupOptionEnabledForRateCodeAttribution', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.internalAlertsEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.SSOEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GroupPricingWindowDisplacement', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.extractNotReceived.EmailRecipients', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.extractNotReceived.HourOfDayThreshold', 'pacman.SandBox.YYZBO', '4', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.tars.toEmailRecipients', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.tars.ccEmailRecipients', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.TrimZeroFilledSummary', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC0:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC0:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC0:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC0:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC0:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.sso.IDPEntityID', 'pacman.SandBox.YYZBO', 'pfSandBoxprod.SandBox.com:saml2','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRVatRoomClass.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRVatRoomType.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.RoomTypeOverbooking.uploadType', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.maxPacePointsAllowedForReports', 'pacman.SandBox.YYZBO', '90', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isPerformanceFixForSharerDisjointEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableStaticDatesForScheduledReports', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.extractNotReceived.EmailRecipientsCC', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.automatedEmailStandardEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.useForecastingWebRateStaleness', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isScheduledTwoWayDateEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ngi.room.stay.batch.size', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.asyncWebrateCleanUpEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.StatisticalMonitoringEnabledFor', 'pacman.SandBox.YYZBO', 'BDE','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.MobilePricingManagementEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.BarFplosByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.BarFplosByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarFplosByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.OCC_TO_ARR_BUFFER', 'pacman.SandBox.YYZBO', '14', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.OPT_DISTRIBUTION', 'pacman.SandBox.YYZBO', 'normal', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.BarFplosByHierarchyByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.BarFplosByHierarchyByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.BarFplosByHierarchyByRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.displayOccupancyForecastInWhatIf', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC3:00.instance', 'pacman.SandBox.YYZBO', 'TETRIS_DEV01','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC3:00.jndiName', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC3:00.jndiNameReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC3:00.port', 'pacman.SandBox.YYZBO', '1433', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.database.timezone.UTC3:00.server', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.property.nonYieldableRoomTypes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.earlierPMSName', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.dateByWhichNewPMSWouldHaveAllReservations', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.is365DaysHistoryDataPresent', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.roomTypesChangesInLastTwoYears', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.isRoomTypesMatchesWithInboundAndSellingSystems', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.isRateCodesMatchesWithInboundAndSellingSystems', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.deductibleGroupBlockStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.nonDeductibleGroupBlockStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cancelledGroupStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.lostGroupStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.deniedGroupStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.regretGroupStatusCodes', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.firstExtractDate', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.PMSVersion', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.rateplan.displayPriceStrategyConfigTabs', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MassRestrictionConfigurationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ihotelier.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ihotelier.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.Suite8.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.Suite8.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.roomRevenuePackages', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.additionalExternalIntegrationSystems', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.OperaCorrectMissingTransactionNumber', 'pacman.SandBox.YYZBO', '50', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.agilerates.MaxActiveAgileRates', 'pacman.SandBox.YYZBO', '6', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.agilerates.MaxUploadedAgileRates', 'pacman.SandBox.YYZBO', '5', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.webrateLoadChunkingEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.ReCreateDecisionsAsOfDate', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.enableAdditionalInformationalDatafeedFields', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.StatisticalOutlierDataPopulationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.RemoteAgentDaysToRetain', 'pacman.SandBox.YYZBO', '10', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.enableMaxDateRangeValidationForSchedulingDataExtractionReport', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GroupFinalFcstOvrdEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.MaxAllowedSchedulesForOperationsReport', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.async', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.dailybarRoundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.useDeltaForDifferential', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'DeltaMinimum','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomType.includeCeilingDeltaMaxSold', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomType.roundingPrecision', 'pacman.SandBox.YYZBO', '2', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.AllMessagesSentAsDelta', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.DoNotMonitorFromDate', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.DoNotMonitorToDate', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.tars.acknowledgement.timeout.toEmailRecipients', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.tars.acknowledgement.timeout.ccEmailRecipients', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.ResolveSandBoxRawRateOverlap', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableCommonPropertyGroupAccess', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.specialEventUploadEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.enableClientProcessingDashboard', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.demand360.NGIPurgeEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.demand360.NGIPostPurgeWeeksToRetain', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.bar.webRateFileNamePattern', 'pacman.SandBox.YYZBO', 'SandBox-YYZBO', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.ratchet.differentialTransPopulation', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isPropertyReadyForExternalUser', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.clientProcessingCompleteCutOffTime', 'pacman.SandBox.YYZBO', '0800', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.futureDaysCDP', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.RMPO_LOG_LEVEL', 'pacman.SandBox.YYZBO', '5', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.ResolveSandBoxRawRateOverlapChunkSize', 'pacman.SandBox.YYZBO', '700', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.uploadExtraChild', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.timeToStartBDE', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.skipStayedSasPopulation', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.TRAN_BLK_WASH_PACE_THRESHOLD', 'pacman.SandBox.YYZBO', '111', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.GRP_WASH_PACE_THRESHOLD', 'pacman.SandBox.YYZBO', '111', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.CRMDashboardHotelPerformanceByMonthEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.UseBCPWrite', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.loadMSRTChunkSize', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.LDBProjectionsBuilderEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.SnoozeCostlyOOOExceptionUntilThresholdCondition', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.SASPropertyNode', 'pacman.SandBox.YYZBO', '${sandboxSASHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.DBPropertyNode', 'pacman.SandBox.YYZBO', '${sandboxDBHost}', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.pricingManagement.pricingOverrideRatePlansSortingStrategy', 'pacman.SandBox.YYZBO', 'SUM_OF_ROOM_RATES', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.groupFloorOverride.minDifferenceValue', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.groupFloorOverride.minDifferenceType', 'pacman.SandBox.YYZBO', 'Percent', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.enablePriceDropRestriction', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.GroupWashIndividualGroupsAlternateExcelExport', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.groupFloorOverride.shoulderNightPercentThreshold', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.useCompactWebratePace', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.DerivedRatesFPLOSCorrection', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.showSingleSearchResult', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.SandBoxStreamingDecisionEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.UsePaceWebrateDifferentialTable', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.PaceWebRateMigrationToDifferentialEnable', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableInventoryGroupOnDataExtractionReport', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.operaAverageGroupRateCalculationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.DisplayCompMSAttribute', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.UseGeneralRollbackJobForSandBox', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.enableEmailFeatureForRemoteAgent', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.NF_NEW_BBLOS_MASTER_RC', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.DAILY_BAR_PRICING_RULE_TYPE', 'pacman.SandBox.YYZBO', '3', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.FCST_DB_UPGRADE', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.ldbProjectionDatafeedEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.outOfOrderOverridesEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.isProjectHeartbeatEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.DefaultJNDIForReports', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/ReportsPacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.core.DefaultJNDI', 'pacman.SandBox.YYZBO', 'java:jboss/datasources/PacmanTenantDS1','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.DisplayLRVandOVBKforDecisionPaceReport', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'WalkMeEnabled', 'pacman.SandBox.YYZBO', 'false', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.IsGroupPricingEvaluationDataFeedFileEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.UseAsyncSpecialEventUpload', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.missingMarketCodeValue', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.resTypesToInclude', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.isSpecialCareFunctionalityEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.improvePerformanceForAuthorizationGroup', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.AgileConfigurationDataFeedFiles', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableRoomTypeforDecisionPaceNonCPReport', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.BarByLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.BarFplosByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.DailyBAR.newDailyBARFormat', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.Fplos.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.HotelOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRVatRoomClass.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRVatRoomClass.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRVatRoomType.consolidateCeilingDelta', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRVatRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.RoomTypeOverbooking.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.useSoapChunks', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.soapChunkSize', 'pacman.SandBox.YYZBO', '1000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MinimumLengthOfStaybyRateCodebyRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.BARByLOSByRoomType.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.useYieldCurrencyForDailyBar', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.DeferredDecisionDeliverySeconds', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRAControlFPLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.LRAControlMinLOS.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.DailyBAR.FetchAllDailyBARDecisionsIfAnyRoomTypeDecisionIsChanged', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.AgileRates.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ack.includeSoapBody', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.property.isActionableSpecialCare', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.CDPCPOptimalPriceToBARStep.chunkSize', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.CPOptimalPriceToBARStep.chunkSize', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GenerateLinkedSRPRates', 'pacman.SandBox.YYZBO', 'Manual File', ''
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.PCRS.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.HILSTAR.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.enableSyncAndAutoUpload', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.displayBookedVsStayInImportantInformation', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.EnableSFTPDeliveryForReports', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.property.specialCareStartDate', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.property.specialCareEndDate', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.showIDPProcessing', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.showProcessingDashboardExtraFilters', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.SandBoxReusableGroupSrps', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pmsMigrationNewVendorId', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.enableSpecialCareScheduling', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.fixFilterPosition', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preproduction.enableDNMDaysOfWeek', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.improveFPLOSQualifiedDecisionTranslationPerformance', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iupdDecisionACKStatusChunkSize', 'pacman.SandBox.YYZBO', '100000', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.allowTransferEncodingChunked', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.async.timeout', 'pacman.SandBox.YYZBO', '-1', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.clientcode', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.includeHTNGAsyncHeaders', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.UploadChildAgeBuckets', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.useHttpBasicAuth', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.monitor.DoNotMonitorDaysOfWeek', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.groupFloorOverride.minimumBlockSize', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.enableFPLOSQualifiedRecommendationLoopStep', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.fplosNoOfRatesToProcessInLoop', 'pacman.SandBox.YYZBO', '100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.linkedSRPSeasonResolutionStep.chunkSize', 'pacman.SandBox.YYZBO', '100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.enableCustomizedHeatMap', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.FPLOSQualifiedRecommendationSrpChunkStep.chunkSize', 'pacman.SandBox.YYZBO', '45', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.FPLOSQualifiedCdpRecommendationSrpChunkStep.chunkSize', 'pacman.SandBox.YYZBO', '45', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.analytics.ipconfig.BAR_REF_PRICE_SCALE', 'pacman.SandBox.YYZBO', '1.014','char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.decisionAnomalyDeviationThreshold', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.linkedSrpOffsetCalculationStep.chunkSize', 'pacman.SandBox.YYZBO', '100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.linkedSrpCumulativeOffsetsCalculationStep.chunkSize', 'pacman.SandBox.YYZBO', '100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.linkedSrpYieldingSRPsOutputGenerationStep.chunkSize', 'pacman.SandBox.YYZBO', '100', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.OnlineUIReportUsePDF', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.PricingMultiProductDisplayEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.NGIChunkFplosDecisionsChunkSize', 'pacman.SandBox.YYZBO', '0', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isAgileQualifiedFPLOSGenerationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.processingDashboardIDPCompletionCutoffTimeDuration', 'pacman.SandBox.YYZBO', '4', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.useGlobalDBForBookkeepingData', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.Alerts.InconsistentBARRatesThresholdMultiplier', 'pacman.SandBox.YYZBO', '3', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.allowExternalIDPConfiguration', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.ZeroCapacityRoomTypesToInclude', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.DataFeedIncludeBookedStatus', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.agilerates.MaxAgileRatesProductGroups', 'pacman.SandBox.YYZBO', '1', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.syncSalesAndCateringConfigurationMapping', 'pacman.SandBox.YYZBO', 'None', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.MYFIDELIO.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ORS.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.EnableGFFDataFeedFile', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.opera.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.LinkedSRPFlatFileDownloaderEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.groupThroughClientAPI', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.anyhtng.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.curtisc.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hbsi.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.infor.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protel.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rvng.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.siteminder.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.suite8.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis1.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis2.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis3.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis4.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis5.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis6.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis7.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis8.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis9.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis10.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.synxis11.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.traveltripper.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.iHotelier.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.yourvoyager.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.winnerpms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.windsurfercrs.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.webrezpropms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.vaillms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.staah.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.smarthotel.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rmspms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rezlynx.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ratetiger.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.rategain.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.protelioair.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.newbookpms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.leanpms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelspider.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hotelnetsolution.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hermeshotels.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.groupmax.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.cubilis.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.connecterevmax.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookingexpert.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.bookassist.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.blastness.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.avvio.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.availpro.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.advantagereserve.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.maestropms.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.ManualRestrictions.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.amsRebuild.lastDayIncreaseThreshold', 'pacman.SandBox.YYZBO', '30', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GroupPricingEvaluationMaxDailyRateChange', 'pacman.SandBox.YYZBO', '3', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.enableCPFloorCeilingPreviewUI', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.ManualRestrictionsDTABasedAccessDays', 'pacman.SandBox.YYZBO', '7', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.GroupEvaluationInflationFactorForPreviousStayRate', 'pacman.SandBox.YYZBO', '0.00', 'double'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isIDeaShareEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.PopulateNotificationStepOptimizationEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.hilstar.uploadExtraChild', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.pcrs.uploadExtraChild', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.roomTypeMarketSegmentProvidedActivityThroughClientAPI', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.roomTypeHotelMarketSegmentActivityThroughClientAPI', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.v2GenerationFlowEnabledNotifications', 'pacman.SandBox.YYZBO', '', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.PreProduction.EnablePropertySpecificFileNewColumns', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.integration.apaleo.DailyBAR.uploadtype', 'pacman.SandBox.YYZBO', 'none', 'char'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.allowedMaxDaysOnlineReports', 'pacman.SandBox.YYZBO', '365', 'int'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.IncludeTaxInFunctionSpaceEvaluationResults', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.isContinuousPricingEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.EnableFullDecisionsForHiltonPostCPMigration', 'pacman.SandBox.YYZBO', 'false', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.feature.HiltonStreamingDecisionEnabled', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    exec [global].[dbo].usp_add_update_parameter_value 'pacman.preProduction.skipDecisionAckSuccessStatusSRPFPLOS', 'pacman.SandBox.YYZBO', 'true', 'boolean'
    update [global].[dbo].[Property] set stage = 'TWO_WAY' where Property_ID =13694
end