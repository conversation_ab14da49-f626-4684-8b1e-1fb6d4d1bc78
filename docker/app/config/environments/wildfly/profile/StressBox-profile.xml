<profile>
    <subsystem xmlns="urn:jboss:domain:logging:7.0">
        <console-handler name="CONSOLE">
            <level name="ALL"/>
            <formatter>
                <named-formatter name="JSON"/>
            </formatter>
        </console-handler>
        <size-rotating-file-handler name="FILE" autoflush="true">
            <level name="ALL"/>
            <formatter>
                <named-formatter name="PATTERN"/>
            </formatter>
            <file relative-to="jboss.server.log.dir" path="server.log"/>
            <rotate-size value="200m"/>
            <max-backup-index value="20"/>
            <append value="true"/>
        </size-rotating-file-handler>
        <size-rotating-file-handler name="SQL" autoflush="true">
            <level name="ALL"/>
            <formatter>
                <named-formatter name="PATTERN"/>
            </formatter>
            <file relative-to="jboss.server.log.dir" path="sql-log.log"/>
            <rotate-size value="200m"/>
            <max-backup-index value="20"/>
            <append value="true"/>
        </size-rotating-file-handler>
        <logger category="com.ideas" use-parent-handlers="true">
            <level name="INFO"/>
        </logger>
        <logger category="io.netty">
            <level name="INFO"/>
        </logger>
        <logger category="io.undertow">
            <level name="INFO"/>
        </logger>
        <logger category="org.quartz">
            <level name="INFO"/>
        </logger>
        <logger category="com.microsoft.sqlserver">
            <level name="INFO"/>
        </logger>
        <logger category="org.apache">
            <level name="INFO"/>
        </logger>
        <logger category="javax">
            <level name="INFO"/>
        </logger>
        <logger category="org.redisson">
            <level name="INFO"/>
        </logger>
        <logger category="org.infinispan">
            <level name="INFO"/>
        </logger>
        <logger category="com.vaadin">
            <level name="INFO"/>
        </logger>
        <logger category="com.amazonaws">
            <level name="INFO"/>
        </logger>
        <logger category="org.jboss">
            <level name="INFO"/>
        </logger>
        <logger category="org.jboss.as.server.deployment">
            <level name="INFO"/>
        </logger>
        <logger category="org.jboss.weld.deployer">
            <level name="INFO"/>
        </logger>
        <logger category="org.springframework">
            <level name="INFO"/>
        </logger>
        <logger category="_org.springframework">
            <level name="INFO"/>
        </logger>
        <logger category="org.springframework.beans">
            <level name="INFO"/>
        </logger>
        <!-- OOTB -->
        <logger category="org.jgroups.protocols">
            <level name="ERROR"/>
        </logger>
        <logger category="com.arjuna">
            <level name="INFO"/>
        </logger>
        <logger category="org.apache.tomcat.util.modeler">
            <level name="WARN"/>
        </logger>
        <logger category="sun.rmi">
            <level name="WARN"/>
        </logger>
        <logger category="org.hornetq" use-parent-handlers="true">
            <level name="WARN"/>
        </logger>
        <logger category="org.jboss.resteasy" use-parent-handlers="true">
            <level name="INFO"/>
        </logger>
        <logger category="com.sas" use-parent-handlers="true">
            <level name="INFO"/>
        </logger>
        <logger category="com.sas.services.session.SessionContext" use-parent-handlers="true">
            <level name="WARN"/>
        </logger>
        <logger category="com.sas.tkclient.TKMessageRequest" use-parent-handlers="true">
            <level name="WARN"/>
        </logger>
        <logger category="com.sas.tkclient.ls.TKMessageRequest" use-parent-handlers="true">
            <level name="WARN"/>
        </logger>
        <logger category="org.hibernate" use-parent-handlers="true">
            <level name="INFO"/>
        </logger>
        <logger category="org.hibernate.SQL" use-parent-handlers="false">
            <level name="INFO"/>
            <handlers>
                <handler name="SQL"/>
            </handlers>
        </logger>
        <logger category="org.hibernate.engine.internal.StatisticalLoggingSessionEventListener">
            <level name="WARN"/>
        </logger>
        <logger category="org.hibernate.orm.deprecation">
            <level name="ERROR"/>
        </logger>
        <logger category="org.springframework.batch.core.job.SimpleStepHandler">
            <level name="WARN"/>
        </logger>
        <logger category="org.springframework.batch.core.step.AbstractStep">
            <level name="WARN"/>
        </logger>
        <logger category="stderr" use-parent-handlers="true">
            <level name="FATAL"/>
        </logger>
        <root-logger>
            <level name="INFO"/>
            <handlers>
                <handler name="CONSOLE"/>
                <handler name="FILE"/>
            </handlers>
        </root-logger>
        <formatter name="PATTERN">
            <pattern-formatter pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"/>
        </formatter>
        <formatter name="COLOR-PATTERN">
            <pattern-formatter pattern="%K{level}%d{HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"/>
        </formatter>
        <formatter name="JSON">
            <json-formatter/>
        </formatter>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:batch-jberet:2.0">
        <default-job-repository name="in-memory"/>
        <default-thread-pool name="batch"/>
        <job-repository name="in-memory">
            <in-memory/>
        </job-repository>
        <thread-pool name="batch">
            <max-threads count="10"/>
            <keepalive-time time="30" unit="seconds"/>
        </thread-pool>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:bean-validation:1.0"/>
    <subsystem xmlns="urn:jboss:domain:core-management:1.0"/>
    <subsystem xmlns="urn:jboss:domain:datasources:5.0">
        <datasources>
            <datasource jndi-name="java:jboss/datasources/ExampleDS" pool-name="ExampleDS" enabled="true" use-java-context="true">
                <connection-url>jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE</connection-url>
                <driver>h2</driver>
                <security>
                    <user-name>sa</user-name>
                    <password>sa</password>
                </security>
            </datasource>
            <datasource jndi-name="java:jboss/datasources/global" pool-name="global" enabled="true" statistics-enabled="true">
                <connection-url>*********************************************************************;</connection-url>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <pool>
                    <min-pool-size>2</min-pool-size>
                    <max-pool-size>500</max-pool-size>
                    <prefill>true</prefill>
                </pool>
                <security>
                    <user-name>g3services</user-name>
                    <password>IDeaS123</password>
                </security>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </datasource>
            <datasource jndi-name="java:jboss/datasources/TetrisRatchetDS" pool-name="TetrisRatchetDS" enabled="true" statistics-enabled="true">
                <connection-url>**********************************************************************</connection-url>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <pool>
                    <min-pool-size>2</min-pool-size>
                    <max-pool-size>200</max-pool-size>
                    <prefill>true</prefill>
                </pool>
                <security>
                    <user-name>g3services</user-name>
                    <password>IDeaS123</password>
                </security>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </datasource>
            <xa-datasource jndi-name="java:jboss/datasources/TetrisGlobalDS" pool-name="TetrisGlobalDS" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    Global
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl101.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanGlobalDS" pool-name="PacmanGlobalDS" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    Global
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl101.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>2</min-pool-size>
                    <max-pool-size>200</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/JobDS" pool-name="JobDS" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    Job
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl101.ideasstg.int
                </xa-datasource-property>
                <xa-datasource-property name="sendStringParametersAsUnicode">
                    false
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/QuartzDS" pool-name="QuartzDS" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    Quartz
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl101.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>2</min-pool-size>
                    <max-pool-size>200</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanRatchetDS" pool-name="PacmanRatchetDS" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    Ratchet
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl101.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>2</min-pool-size>
                    <max-pool-size>200</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS1" pool-name="PacmanTenantDS1" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl102.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS102" pool-name="PacmanTenantDS102" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl102.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS103" pool-name="PacmanTenantDS103" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl103.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS104" pool-name="PacmanTenantDS104" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl104.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS105" pool-name="PacmanTenantDS105" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl105.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <xa-datasource jndi-name="java:jboss/datasources/PacmanTenantDS106" pool-name="PacmanTenantDS106" enabled="true" statistics-enabled="true">
                <xa-datasource-property name="DatabaseName">
                    tempdb
                </xa-datasource-property>
                <xa-datasource-property name="User">
                    g3services
                </xa-datasource-property>
                <xa-datasource-property name="Password">
                    IDeaS123
                </xa-datasource-property>
                <xa-datasource-property name="PortNumber">
                    1433
                </xa-datasource-property>
                <xa-datasource-property name="ServerName">
                    mn4sg3xdbsl106.ideasstg.int
                </xa-datasource-property>
                <driver>mssql</driver>
                <new-connection-sql>select 1</new-connection-sql>
                <xa-pool>
                    <min-pool-size>100</min-pool-size>
                    <max-pool-size>1500</max-pool-size>
                    <prefill>true</prefill>
                </xa-pool>
                <validation>
                    <check-valid-connection-sql>select 1</check-valid-connection-sql>
                </validation>
                <statement>
                    <track-statements>false</track-statements>
                </statement>
            </xa-datasource>
            <drivers>
                <driver name="mssql" module="com.microsoft.sqlserver">
                    <driver-class>com.microsoft.sqlserver.jdbc.SQLServerDriver</driver-class>
                    <xa-datasource-class>com.microsoft.sqlserver.jdbc.SQLServerXADataSource</xa-datasource-class>
                </driver>
                <driver name="h2" module="com.h2database.h2">
                    <xa-datasource-class>org.h2.jdbcx.JdbcDataSource</xa-datasource-class>
                </driver>
            </drivers>
        </datasources>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:deployment-scanner:2.0">
        <deployment-scanner path="deployments"
                            relative-to="jboss.server.base.dir"
                            scan-interval="5000"
                            runtime-failure-causes-rollback="${jboss.deployment.scanner.rollback.on.failure:false}"
                            auto-deploy-exploded="false"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:discovery:1.0"/>
    <subsystem xmlns="urn:jboss:domain:ee:4.0">
        <spec-descriptor-property-replacement>false</spec-descriptor-property-replacement>
        <concurrent>
            <context-services>
                <context-service name="default" jndi-name="java:jboss/ee/concurrency/context/default" use-transaction-setup-provider="true"/>
            </context-services>
            <managed-thread-factories>
                <managed-thread-factory name="default" jndi-name="java:jboss/ee/concurrency/factory/default" context-service="default"/>
            </managed-thread-factories>
            <managed-executor-services>
                <managed-executor-service name="default" jndi-name="java:jboss/ee/concurrency/executor/default" context-service="default" hung-task-threshold="60000" core-threads="5" max-threads="25" keepalive-time="5000"/>
            </managed-executor-services>
            <managed-scheduled-executor-services>
                <managed-scheduled-executor-service name="default" jndi-name="java:jboss/ee/concurrency/scheduler/default" context-service="default" hung-task-threshold="60000" core-threads="2" keepalive-time="3000"/>
            </managed-scheduled-executor-services>
        </concurrent>
        <default-bindings context-service="java:jboss/ee/concurrency/context/default" datasource="java:jboss/datasources/ExampleDS" jms-connection-factory="java:jboss/DefaultJMSConnectionFactory" managed-executor-service="java:jboss/ee/concurrency/executor/default" managed-scheduled-executor-service="java:jboss/ee/concurrency/scheduler/default" managed-thread-factory="java:jboss/ee/concurrency/factory/default"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:ee-security:1.0"/>
    <subsystem xmlns="urn:jboss:domain:ejb3:5.0">
        <session-bean>
            <stateless>
                <bean-instance-pool-ref pool-name="slsb-strict-max-pool"/>
            </stateless>
            <stateful default-access-timeout="5000" cache-ref="distributable" passivation-disabled-cache-ref="simple"/>
            <singleton default-access-timeout="5000"/>
        </session-bean>
        <mdb>
            <resource-adapter-ref resource-adapter-name="${ejb.resource-adapter-name:hornetq-ra.rar}"/>
            <bean-instance-pool-ref pool-name="mdb-strict-max-pool"/>
        </mdb>
        <pools>
            <bean-instance-pools>
                <strict-max-pool name="slsb-strict-max-pool" max-pool-size="300" instance-acquisition-timeout="5" instance-acquisition-timeout-unit="MINUTES"/>
                <strict-max-pool name="mdb-strict-max-pool" max-pool-size="200" instance-acquisition-timeout="5" instance-acquisition-timeout-unit="MINUTES"/>
            </bean-instance-pools>
        </pools>
        <caches>
            <cache name="simple"/>
            <cache name="distributable" passivation-store-ref="infinispan" aliases="passivating clustered"/>
        </caches>
        <passivation-stores>
            <passivation-store name="infinispan" cache-container="ejb" max-size="10000"/>
        </passivation-stores>
        <async thread-pool-name="default"/>
        <timer-service thread-pool-name="default" default-data-store="default-file-store">
            <data-stores>
                <file-data-store name="default-file-store" path="timer-service-data" relative-to="jboss.server.data.dir"/>
            </data-stores>
        </timer-service>
        <remote connector-ref="http-remoting-connector" thread-pool-name="default" execute-in-worker="false"/>
        <thread-pools>
            <thread-pool name="default">
                <max-threads count="100"/>
                <keepalive-time time="100" unit="milliseconds"/>
            </thread-pool>
        </thread-pools>
        <iiop enable-by-default="false" use-qualified-name="false"/>
        <default-security-domain value="other"/>
        <default-missing-method-permissions-deny-access value="true"/>
        <statistics enabled="true"/>
        <log-system-exceptions value="true"/>
    </subsystem>
    <subsystem xmlns="urn:wildfly:elytron:7.0" final-providers="combined-providers" disallowed-providers="OracleUcrypto">
        <providers>
            <aggregate-providers name="combined-providers">
                <providers name="elytron"/>
                <providers name="openssl"/>
            </aggregate-providers>
            <provider-loader name="elytron" module="org.wildfly.security.elytron"/>
            <provider-loader name="openssl" module="org.wildfly.openssl"/>
        </providers>
        <audit-logging>
            <file-audit-log name="local-audit" path="audit.log" relative-to="jboss.server.log.dir" format="JSON"/>
        </audit-logging>
        <security-domains>
            <security-domain name="ApplicationDomain" default-realm="ApplicationRealm" permission-mapper="default-permission-mapper">
                <realm name="ApplicationRealm" role-decoder="groups-to-roles"/>
                <realm name="local"/>
            </security-domain>
            <security-domain name="ManagementDomain" default-realm="ManagementRealm" permission-mapper="default-permission-mapper">
                <realm name="ManagementRealm" role-decoder="groups-to-roles"/>
                <realm name="local" role-mapper="super-user-mapper"/>
            </security-domain>
        </security-domains>
        <security-realms>
            <identity-realm name="local" identity="$local"/>
            <properties-realm name="ApplicationRealm">
                <users-properties path="application-users.properties" relative-to="jboss.server.config.dir" digest-realm-name="ApplicationRealm"/>
                <groups-properties path="application-roles.properties" relative-to="jboss.server.config.dir"/>
            </properties-realm>
            <properties-realm name="ManagementRealm">
                <users-properties path="mgmt-users.properties" relative-to="jboss.server.config.dir" digest-realm-name="ManagementRealm"/>
                <groups-properties path="mgmt-groups.properties" relative-to="jboss.server.config.dir"/>
            </properties-realm>
        </security-realms>
        <mappers>
            <simple-permission-mapper name="default-permission-mapper" mapping-mode="first">
                <permission-mapping>
                    <principal name="anonymous"/>
                    <permission-set name="default-permissions"/>
                </permission-mapping>
                <permission-mapping match-all="true">
                    <permission-set name="login-permission"/>
                    <permission-set name="default-permissions"/>
                </permission-mapping>
            </simple-permission-mapper>
            <constant-realm-mapper name="local" realm-name="local"/>
            <simple-role-decoder name="groups-to-roles" attribute="groups"/>
            <constant-role-mapper name="super-user-mapper">
                <role name="SuperUser"/>
            </constant-role-mapper>
        </mappers>
        <permission-sets>
            <permission-set name="login-permission">
                <permission class-name="org.wildfly.security.auth.permission.LoginPermission"/>
            </permission-set>
            <permission-set name="default-permissions">
                <permission class-name="org.wildfly.extension.batch.jberet.deployment.BatchPermission" module="org.wildfly.extension.batch.jberet" target-name="*"/>
                <permission class-name="org.wildfly.transaction.client.RemoteTransactionPermission" module="org.wildfly.transaction.client"/>
                <permission class-name="org.jboss.ejb.client.RemoteEJBPermission" module="org.jboss.ejb-client"/>
            </permission-set>
        </permission-sets>
        <http>
            <http-authentication-factory name="management-http-authentication" security-domain="ManagementDomain" http-server-mechanism-factory="global">
                <mechanism-configuration>
                    <mechanism mechanism-name="DIGEST">
                        <mechanism-realm realm-name="ManagementRealm"/>
                    </mechanism>
                </mechanism-configuration>
            </http-authentication-factory>
            <http-authentication-factory name="application-http-authentication" security-domain="ApplicationDomain" http-server-mechanism-factory="global">
                <mechanism-configuration>
                    <mechanism mechanism-name="BASIC">
                        <mechanism-realm realm-name="Application Realm"/>
                    </mechanism>
                    <mechanism mechanism-name="FORM"/>
                </mechanism-configuration>
            </http-authentication-factory>
            <provider-http-server-mechanism-factory name="global"/>
        </http>
        <sasl>
            <sasl-authentication-factory name="management-sasl-authentication" sasl-server-factory="configured" security-domain="ManagementDomain">
                <mechanism-configuration>
                    <mechanism mechanism-name="JBOSS-LOCAL-USER" realm-mapper="local"/>
                    <mechanism mechanism-name="DIGEST-MD5">
                        <mechanism-realm realm-name="ManagementRealm"/>
                    </mechanism>
                </mechanism-configuration>
            </sasl-authentication-factory>
            <sasl-authentication-factory name="application-sasl-authentication" sasl-server-factory="configured" security-domain="ApplicationDomain">
                <mechanism-configuration>
                    <mechanism mechanism-name="JBOSS-LOCAL-USER" realm-mapper="local"/>
                    <mechanism mechanism-name="DIGEST-MD5">
                        <mechanism-realm realm-name="ApplicationRealm"/>
                    </mechanism>
                </mechanism-configuration>
            </sasl-authentication-factory>
            <configurable-sasl-server-factory name="configured" sasl-server-factory="elytron">
                <properties>
                    <property name="wildfly.sasl.local-user.default-user" value=""/>
                </properties>
            </configurable-sasl-server-factory>
            <mechanism-provider-filtering-sasl-server-factory name="elytron" sasl-server-factory="global">
                <filters>
                    <filter provider-name="WildFlyElytron"/>
                </filters>
            </mechanism-provider-filtering-sasl-server-factory>
            <provider-sasl-server-factory name="global"/>
        </sasl>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:infinispan:8.0">
        <cache-container name="server" default-cache="default" module="org.wildfly.clustering.server">
            <local-cache name="default">
                <transaction mode="BATCH"/>
            </local-cache>
        </cache-container>
        <cache-container name="web" default-cache="passivation" module="org.wildfly.clustering.web.infinispan">
            <local-cache name="passivation">
                <locking isolation="REPEATABLE_READ"/>
                <transaction mode="BATCH"/>
                <file-store passivation="true" purge="false"/>
            </local-cache>
            <local-cache name="persistent">
                <locking isolation="REPEATABLE_READ"/>
                <transaction mode="BATCH"/>
                <file-store passivation="false" purge="false"/>
            </local-cache>
            <local-cache name="concurrent">
                <file-store passivation="true" purge="false"/>
            </local-cache>
        </cache-container>
        <cache-container name="ejb" aliases="sfsb" default-cache="passivation" module="org.wildfly.clustering.ejb.infinispan">
            <local-cache name="passivation">
                <locking isolation="REPEATABLE_READ"/>
                <transaction mode="BATCH"/>
                <file-store passivation="true" purge="false"/>
            </local-cache>
            <local-cache name="persistent">
                <locking isolation="REPEATABLE_READ"/>
                <transaction mode="BATCH"/>
                <file-store passivation="false" purge="false"/>
            </local-cache>
        </cache-container>
        <cache-container name="hibernate" default-cache="local-query" module="org.infinispan.hibernate-cache">
            <local-cache name="local-query">
                <object-memory size="10000"/>
                <expiration max-idle="100000"/>
            </local-cache>
            <local-cache name="entity">
                <transaction mode="NON_XA"/>
                <object-memory size="10000"/>
                <expiration max-idle="100000"/>
            </local-cache>
            <local-cache name="timestamps"/>
        </cache-container>
     </subsystem>
    <subsystem xmlns="urn:jboss:domain:io:3.0">
        <worker name="default"/>
        <buffer-pool name="default"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:jaxrs:1.0"/>
    <subsystem xmlns="urn:jboss:domain:jca:5.0">
        <archive-validation enabled="true" fail-on-error="true" fail-on-warn="false"/>
        <bean-validation enabled="true"/>
        <default-workmanager>
            <short-running-threads>
                <core-threads count="50"/>
                <queue-length count="50"/>
                <max-threads count="50"/>
                <keepalive-time time="10" unit="seconds"/>
            </short-running-threads>
            <long-running-threads>
                <core-threads count="50"/>
                <queue-length count="50"/>
                <max-threads count="50"/>
                <keepalive-time time="10" unit="seconds"/>
            </long-running-threads>
        </default-workmanager>
        <cached-connection-manager/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:jdr:1.0"/>
    <subsystem xmlns="urn:jboss:domain:jmx:1.3">
        <expose-resolved-model/>
        <expose-expression-model/>
        <remoting-connector/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:jpa:1.1">
        <jpa default-datasource="" default-extended-persistence-inheritance="DEEP"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:jsf:1.1"/>
    <subsystem xmlns="urn:jboss:domain:jsr77:1.0"/>
    <subsystem xmlns="urn:jboss:domain:mail:3.0">
        <mail-session name="default" jndi-name="java:jboss/mail/Default">
            <smtp-server outbound-socket-binding-ref="mail-smtp"/>
        </mail-session>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:messaging-activemq:7.0">
        <server name="default">
            <journal file-size="102400"/>
            <shared-store-master/>
            <security-setting name="#">
                <role name="guest" send="true" consume="true" create-durable-queue="false" delete-durable-queue="false" create-non-durable-queue="true" delete-non-durable-queue="true" manage="false"/>
            </security-setting>
            <address-setting name="#" dead-letter-address="jms.queue.DLQ" expiry-address="jms.queue.ExpiryQueue" max-size-bytes="10485760" page-size-bytes="2097152" message-counter-history-day-limit="10"/>
            <http-connector name="http-connector" socket-binding="http" endpoint="http-acceptor"/>
            <http-connector name="http-connector-throughput" socket-binding="http" endpoint="http-acceptor-throughput">
                <param name="batch-delay" value="50"/>
            </http-connector>
            <in-vm-connector name="in-vm" server-id="0"/>
            <http-acceptor name="http-acceptor" http-listener="default"/>
            <http-acceptor name="http-acceptor-throughput" http-listener="default">
                <param name="batch-delay" value="50"/>
                <param name="direct-deliver" value="false"/>
            </http-acceptor>
            <in-vm-acceptor name="in-vm" server-id="0"/>
            <jms-queue name="ExpiryQueue" entries="java:/jms/queue/ExpiryQueue"/>
            <jms-queue name="DLQ" entries="java:/jms/queue/DLQ"/>
            <jms-queue name="BPMSQueue" entries="queue/BPMSQueue java:jboss/exported/jms/queue/BPMSQueue"/>
            <connection-factory name="InVmConnectionFactory" entries="java:/ConnectionFactory" connectors="in-vm"/>
            <connection-factory name="RemoteConnectionFactory" entries="java:jboss/exported/jms/RemoteConnectionFactory" connectors="http-connector"/>
            <pooled-connection-factory name="hornetq-ra" entries="java:/JmsXA java:jboss/DefaultJMSConnectionFactory" connectors="in-vm" transaction="xa">
                <outbound-config allow-local-transactions="true"/>
            </pooled-connection-factory>
        </server>
    </subsystem>
    <subsystem xmlns="urn:wildfly:microprofile-config-smallrye:1.0"/>
    <subsystem xmlns="urn:wildfly:microprofile-health-smallrye:1.0" security-enabled="false"/>
    <subsystem xmlns="urn:wildfly:microprofile-metrics-smallrye:2.0" security-enabled="false" exposed-subsystems="*"/>
    <subsystem xmlns="urn:wildfly:microprofile-opentracing-smallrye:1.0"/>
    <subsystem xmlns="urn:jboss:domain:naming:2.0">
        <remote-naming/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:pojo:1.0"/>
    <subsystem xmlns="urn:jboss:domain:remoting:4.0">
        <endpoint worker="default"/>
        <http-connector name="http-remoting-connector" connector-ref="default" security-realm="ApplicationRealm"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:request-controller:1.0"/>
    <subsystem xmlns="urn:jboss:domain:resource-adapters:5.0"/>
    <subsystem xmlns="urn:jboss:domain:sar:1.0"/>
    <subsystem xmlns="urn:jboss:domain:security:2.0">
        <security-domains>
            <security-domain name="other" cache-type="default">
                <authentication>
                    <login-module code="Remoting" flag="optional">
                        <module-option name="password-stacking" value="useFirstPass"/>
                    </login-module>
                    <login-module code="RealmDirect" flag="required">
                        <module-option name="password-stacking" value="useFirstPass"/>
                    </login-module>
                </authentication>
            </security-domain>
            <security-domain name="jboss-web-policy" cache-type="default">
                <authorization>
                    <policy-module code="Delegating" flag="required"/>
                </authorization>
            </security-domain>
            <security-domain name="jboss-ejb-policy" cache-type="default">
                <authorization>
                    <policy-module code="Delegating" flag="required"/>
                </authorization>
            </security-domain>
            <security-domain name="tetris" cache-type="default">
                <authentication>
                    <login-module code="com.ideas.infra.tetris.security.jaas.TetrisLoginModule" flag="required"/>
                </authentication>
            </security-domain>
        </security-domains>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:security-manager:1.0">
        <deployment-permissions>
            <maximum-set>
                <permission class="java.security.AllPermission"/>
            </maximum-set>
        </deployment-permissions>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:singleton:1.0">
        <singleton-policies default="default">
            <singleton-policy name="default" cache-container="server">
                <simple-election-policy/>
            </singleton-policy>
        </singleton-policies>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:transactions:5.0">
        <core-environment>
            <process-id>
                <uuid/>
            </process-id>
        </core-environment>
        <recovery-environment socket-binding="txn-recovery-environment" status-socket-binding="txn-status-manager"/>
        <coordinator-environment statistics-enabled="true" enable-tsm-status="false"/>
        <object-store relative-to="jboss.server.data.dir"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:undertow:9.0">
        <buffer-cache name="default"/>
        <server name="default-server">
            <ajp-listener name="ajp" socket-binding="ajp"/>
            <http-listener name="default" socket-binding="http" max-post-size="100971520" redirect-socket="https" enable-http2="true"/>
            <https-listener name="https" socket-binding="https" security-realm="ApplicationRealm" enable-http2="true"/>
            <host name="default-host" alias="localhost">
                <location name="/" handler="welcome-content"/>
                <access-log pattern="common" use-server-log="true"/>
                <filter-ref name="limit-connections"/>
                <http-invoker security-realm="ApplicationRealm"/>
            </host>
        </server>
        <servlet-container name="default">
            <jsp-config/>
            <session-cookie http-only="true"/>
            <websockets/>
        </servlet-container>
        <handlers>
            <file name="welcome-content" path="${jboss.home.dir}/welcome-content"/>
        </handlers>
        <filters>
            <request-limit name="limit-connections" max-concurrent-requests="2048" queue-size="100"/>
        </filters>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:webservices:2.0">
        <wsdl-host>${jboss.bind.address:127.0.0.1}</wsdl-host>
        <endpoint-config name="Standard-Endpoint-Config"/>
        <endpoint-config name="Recording-Endpoint-Config">
            <pre-handler-chain name="recording-handlers" protocol-bindings="##SOAP11_HTTP ##SOAP11_HTTP_MTOM ##SOAP12_HTTP ##SOAP12_HTTP_MTOM">
                <handler name="RecordingHandler" class="org.jboss.ws.common.invocation.RecordingServerHandler"/>
            </pre-handler-chain>
        </endpoint-config>
        <client-config name="Standard-Client-Config"/>
    </subsystem>
    <subsystem xmlns="urn:jboss:domain:weld:4.0"/>
</profile>
